env JAVA_HOME=/usr/java/jdk-11 mvn clean
env JAVA_HOME=/usr/java/jdk-11 mvn -U package

rsync -av --progress --delete --delete-excluded --rsh='ssh -p7109' \
  --include=classes \
  --include=lib \
  --include=classes/** \
  --include=lib/* \
  --exclude=* \
  target/ root@dc:/root/tmp_deploy/iportal-service/

ssh root@dc -p 7109 'cp -Rp /opt/iportal-service/classes/ /opt/tmp_backup/iportal-service-$(date +%Y%m%d_%H%M%S)/'

ssh root@dc -p 7109 -X 'meld /root/tmp_deploy/iportal-service/ /opt/iportal-service/ && /opt/iportal-service/setper'
