{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "sync prod",
            "type": "shell",
            "command": "./sync_prod",
            "group": "build"
        },
        {
            "label": "package",
            "type": "shell",
            "command": "env JAVA_HOME=/usr/java/jdk-11 mvn -U package",
            "group": "build",
            "problemMatcher": [
                "$msCompile"
            ]
        },
        {
            "label": "clean",
            "type": "shell",
            "command": "env JAVA_HOME=/usr/java/jdk-11 mvn clean",
            "group": "build",
            "problemMatcher": [
                "$msCompile"
            ]
        }
    ]
}