package vn.onepay.portal.resources.debtClearance.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.debtClearance.dto.DebtClearanceAprovalDto;
import vn.onepay.portal.resources.debtClearance.dto.DebtClearanceServiceData;
import vn.onepay.portal.resources.pay_collect.dto.*;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DebtClearanceDao extends Db implements IConstants {
    private static final String GET_SERVICE_DATA = "{call pkg_onebill_debt_clearance.get_debit_clear_service(?,?,?,?) }";
    private static final String UPDATE_DEBT_CLEARING = "{call pkg_onebill_debt_clearance.update_debit_clear_status(?,?,?,?,?,?,?,?) }";
    private static final String SEARCH_DEBT_CLEARANCE= "{call pkg_onebill_debt_clearance.get_debt_clear_search(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final Logger LOGGER = Logger.getLogger(DebtClearanceDao.class.getName());

    public static void close(AutoCloseable[] objs) {
        for (AutoCloseable obj : objs)
            try {
                if (obj != null) obj.close();
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "", e);
            }
    }

    public static List<DebtClearanceServiceData> getSericeData(String type) throws Exception {
        Exception exception = null;
        List<DebtClearanceServiceData> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_SERVICE_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_SERVICE_DATA: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindServiceData(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
           
        return result;
    }

    public static boolean updateState(String id, String status, String billingStatus,String updateBy,String reasonFailed) throws Exception {
        Exception exception = null;
        List<DebtClearanceServiceData> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_DEBT_CLEARING);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.setString(5, status);
            cs.setString(6, billingStatus);
            cs.setString(7, updateBy);
            cs.setString(8, reasonFailed);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB UPDATE_DEBT_CLEARING: " + error);
            } else {
                return true;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static List<DebtClearanceAprovalDto> getDebtClearanceSearch(Map mIn, QueryMethod method) throws Exception {
        Exception exception = null;
        List<DebtClearanceAprovalDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(SEARCH_DEBT_CLEARANCE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, method.toString());
            cs.setString(5, mIn.get("dateType").toString());
            cs.setString(6, mIn.get("fromDate").toString());
            cs.setString(7, mIn.get("toDate").toString());
            cs.setString(8, mIn.get("billCode").toString());
            cs.setString(9, mIn.get("serviceType").toString());
            cs.setString(10, mIn.get("service").toString());
            cs.setString(11, mIn.get("requestStatus").toString());
            cs.setString(12, mIn.get("debtClearanceStatus").toString());
            cs.setInt(13, Integer.parseInt(mIn.get("pageSize").toString()));
            cs.setInt(14, Integer.parseInt(mIn.get("page").toString()));
            cs.setString(15, mIn.get("transactionId").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB SEARCH_DEBT_CLEARANCE: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindDebtClearingData(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return result;
    }



    public static Integer getTotalDebtClearance(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(SEARCH_DEBT_CLEARANCE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.get("dateType").toString());
            cs.setString(6, mIn.get("fromDate").toString());
            cs.setString(7, mIn.get("toDate").toString());
            cs.setString(8, mIn.get("billCode").toString());
            cs.setString(9, mIn.get("serviceType").toString());
            cs.setString(10, mIn.get("service").toString());
            cs.setString(11, mIn.get("requestStatus").toString());
            cs.setString(12, mIn.get("debtClearanceStatus").toString());
            cs.setInt(13, Integer.parseInt(mIn.get("pageSize").toString()));
            cs.setInt(14, Integer.parseInt(mIn.get("page").toString()));
            cs.setString(15, mIn.get("transactionId").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB SEARCH_DEBT_CLEARANCE: " + error);
            } else {
                    while (rs != null && rs.next()) {
                        result = rs.getInt("N_TOTAL");
                    }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return result;
    }

    private static DebtClearanceServiceData bindServiceData(ResultSet rs) throws SQLException {
        DebtClearanceServiceData model = new DebtClearanceServiceData();
        model.setName(rs.getString("S_NAME"));
        model.setValue(rs.getString("S_NAME"));
        return model;
    }

    private static DebtClearanceAprovalDto bindDebtClearingData(ResultSet rs) throws SQLException {
        DebtClearanceAprovalDto model = new DebtClearanceAprovalDto();
        Timestamp createDate = rs.getString("D_CREATE") == null ? null : Timestamp.valueOf(rs.getString("D_CREATE"));
        Timestamp updateDate = rs.getString("D_UPDATE") == null ? null : Timestamp.valueOf(rs.getString("D_UPDATE"));
        model.setId(rs.getString("S_ID"));
        model.setBillCode(rs.getString("S_BILL_CODE"));
        model.setService(rs.getString("S_SERVICE_ID"));
        model.setClient(rs.getString("S_CLIENT_ID"));
        model.setAmount(rs.getDouble("N_AMOUNT"));
        model.setCreateDate(createDate);
        model.setUpdateDate(updateDate);
        model.setCreateBy(rs.getString("S_CREATE_BY"));
        model.setUpdateBy(rs.getString("S_UPDATE_BY"));
        model.setDebtClearanceStatus(rs.getString("S_BILLING_STATUS"));
        model.setStatus(rs.getString("S_STATUS"));
        model.setTransactionId(rs.getString("S_TRANSACTION_ID"));
        model.setResponce(rs.getString("S_REASON_FAILED"));
        model.setServiceBilling(rs.getString("S_SERVICE_BILLING"));
        return model;
    }
    
}
