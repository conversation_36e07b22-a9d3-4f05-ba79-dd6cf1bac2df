package vn.onepay.portal.resources.debtClearance.handler;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.OneClient.OneClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.debtClearance.dao.DebtClearanceDao;
import vn.onepay.portal.resources.lead_provider.dao.LeadProviderDao;
import vn.onepay.portal.resources.pay_collect.dao.PayCollectDao;
import vn.onepay.portal.resources.pay_collect.dto.MerchantPaycollectDto;
import vn.onepay.portal.resources.pay_collect.dto.PartnerPaycollectDto;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.UserProfileDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static vn.onepay.portal.Util.sendResponse;

public class DebtClearanceHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(DebtClearanceHandler.class.getName());

    private static final Gson gson = new Gson();

    private static final String WEB_PAY_CHANNEL = "6014";

    public static void getDebtClearanceService(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HashMap mOut = new HashMap();
                mOut.put("service", DebtClearanceDao.getSericeData("SERVICE"));
                mOut.put("provider", DebtClearanceDao.getSericeData("PROVIDER"));
                mOut.put("client", DebtClearanceDao.getSericeData("CLIENT"));
                sendResponse(ctx, 200, mOut);
            } catch (Exception e) {
                logger.log(Level.WARNING, "getDebtClearanceService: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDebtClearanceSearch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                HashMap mIn = new HashMap();
                HashMap mOut = new HashMap();
                mIn.put("dateType",request.getParam("dateType"));
                mIn.put("fromDate",request.getParam("fromDate"));
                mIn.put("toDate",request.getParam("toDate"));
                mIn.put("billCode",request.getParam("billCode") == null ? "":request.getParam("billCode"));
                mIn.put("transactionId",request.getParam("transactionId") == null ? "":request.getParam("transactionId"));
                mIn.put("serviceType",request.getParam("serviceType"));
                mIn.put("service",request.getParam("service") == null ? "":request.getParam("service"));
                mIn.put("requestStatus",request.getParam("requestStatus") == null ? "":request.getParam("requestStatus"));
                mIn.put("debtClearanceStatus",request.getParam("debtClearanceStatus") == null ? "":request.getParam("debtClearanceStatus"));
                mIn.put("pageSize",request.getParam("pageSize"));
                mIn.put("page",request.getParam("page"));
                mOut.put("list",DebtClearanceDao.getDebtClearanceSearch(mIn, QueryMethod.SELECT));
                mOut.put("totalItems", DebtClearanceDao.getTotalDebtClearance(mIn));
                sendResponse(ctx, 200, mOut);
            } catch (Exception e) {
                logger.log(Level.WARNING, "getDebtClearanceSearch: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void reject(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(X_USER_ID);
                UserProfileDto userProfileDto = UserDao.getProfileInfo(userId);
                String name = userProfileDto.getEmail();
                JsonObject data =ctx.getBodyAsJson();
                JsonArray jsonArray = data.getJsonArray("listData");
                Map mOut = new HashMap();
                Integer cSuccess = 0;
                Integer cFail = 0;
                for(Object object : jsonArray){
                    JsonObject jsonObject = (JsonObject)object;
                    boolean checkUpdate = DebtClearanceDao.updateState(jsonObject.getString("id"),"REJECTED","CREATED", name, "Rejected");
                    if(checkUpdate){
                        cSuccess ++;
                    }else{
                        cFail ++;
                    }
                }
                mOut.put("fail",cFail);
                mOut.put("success",cSuccess);
                sendResponse(ctx, 200, mOut);
            } catch (Exception e) {
                logger.log(Level.WARNING, "getDebtClearanceSearch: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void aproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map mOut = new HashMap();
                Integer userId = ctx.get(X_USER_ID);
                UserProfileDto userProfileDto = UserDao.getProfileInfo(userId);
                String name = userProfileDto.getEmail();
                String topupHost = Config.getString("onebill.topup.host", "************");
                Integer topupPort = Config.getInteger("onebill.topup.port", 10239);
                JsonObject data =ctx.getBodyAsJson();
                JsonArray jsonArray = data.getJsonArray("listData");
                logger.info("onebill.topup.host : " + topupHost);
                logger.info("onebill.topup.port : " + topupPort);
                String src = data.getString("otp");
                int countSuccess = 0;
                int countFail = 0;
                int countPending = 0;
                if ("********".equals(src)) {
                    for(Object object : jsonArray){
                        JsonObject jsonObject = (JsonObject)object;
                        OneClient os = new OneClient(topupHost, topupPort);
                        logger.info("ma hoa don : " + jsonObject.getString("billCode"));
                        logger.info("dich vu : " + jsonObject.getString("serviceBilling"));
                        logger.info("cho ngan hang : " + jsonObject.getString("client"));
                        Map mO = os.settlement(jsonObject.getString("billCode"),
                                jsonObject.getInteger("amount"),
                                jsonObject.getString("serviceBilling"), WEB_PAY_CHANNEL, jsonObject.getString("client"), 60000);
                        logger.info("Map: "+ mO);

                        if (mO != null && Integer.parseInt(mO.get("state").toString()) == 800) {
                            countSuccess++;
                            DebtClearanceDao.updateState(jsonObject.getString("id"),"APPROVED","SUCCESSED", name,null);
                        } else {

                            if(mO != null && mO.get("description").toString().contains("timeout")){
                                countPending++;
                                DebtClearanceDao.updateState(jsonObject.getString("id"),"APPROVED","PENDING", name, mO.get("description").toString());
                            }else{
                                countFail++;
                                DebtClearanceDao.updateState(jsonObject.getString("id"),"APPROVED","FAILED", name,mO.get("description").toString());
                            }
                        }
                        mOut.put("code",200);
                        mOut.put("message","Send request (" + countSuccess + " succeed, " + countFail + " failed, "+countPending+" pending).");
                    }
                } else {
                    mOut.put("code",500);
                    mOut.put("message","invalid OTP");
                }
                sendResponse(ctx, 200, mOut);
            } catch (Exception e) {
                logger.log(Level.WARNING, "getDebtClearanceSearch: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    public static void downloadReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("fromDate", Util.handleJsonObject(bodyJson, "fromDate", ""));
                mIn.put("toDate", Util.handleJsonObject(bodyJson, "toDate", ""));
                mIn.put("dateType", Util.handleJsonObject(bodyJson, "dateType", ""));
                mIn.put("billCode", Util.handleJsonObject(bodyJson, "billCode", ""));
                mIn.put("transactionId", Util.handleJsonObject(bodyJson, "transactionId", ""));
                mIn.put("serviceType", Util.handleJsonObject(bodyJson, "serviceType", ""));
                mIn.put("service", Util.handleJsonObject(bodyJson, "service", ""));
                mIn.put("requestStatus", Util.handleJsonObject(bodyJson, "requestStatus", ""));
                mIn.put("debtClearanceStatus", Util.handleJsonObject(bodyJson, "debtClearanceStatus", ""));
                mIn.put("pageSize",Util.handleJsonObject(bodyJson, "pageSize", ""));
                mIn.put("page",Util.handleJsonObject(bodyJson, "page", ""));
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                HttpServerRequest request = ctx.request();
                requestData.put(X_USER_ID, Util.handleHeaderHttpRequest(request, X_USER_ID, ""));
                requestData.put(X_REQUEST_ID, Util.handleHeaderHttpRequest(request, X_REQUEST_ID, ""));
                requestData.put(X_REAL_IP, Util.handleHeaderHttpRequest(request, X_REAL_IP, ""));

                int totalRows = DebtClearanceDao.getTotalDebtClearance(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = dateFormat.format(new Date());
                String fileName = "debt_clearance_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("debt_clearance_");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                }else{
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "REPORT DOWNLOAD ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
