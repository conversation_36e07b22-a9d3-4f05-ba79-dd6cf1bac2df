package vn.onepay.portal.resources.debtClearance.dto;

import java.sql.Time;
import java.sql.Timestamp;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 6/7/2021
 * Time: 11:53 AM
 * To change this IPORTAL_SERVICE.
 */

public class DebtClearanceAprovalDto {
    private String id;
    private String transactionId;
    private String billCode;
    private String service;
    private String client;
    private double amount;
    private Timestamp createDate;
    private Timestamp updateDate;
    private String createBy;
    private String updateBy;
    private String status;
    private String debtClearanceStatus;
    private String responce;
    private String serviceBilling;

    public String getServiceBilling() {
        return serviceBilling;
    }

    public void setServiceBilling(String serviceBilling) {
        this.serviceBilling = serviceBilling;
    }

    public String getResponce() {
        return responce;
    }

    public void setResponce(String responce) {
        this.responce = responce;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDebtClearanceStatus() {
        return debtClearanceStatus;
    }

    public void setDebtClearanceStatus(String debtClearanceStatus) {
        this.debtClearanceStatus = debtClearanceStatus;
    }
}
