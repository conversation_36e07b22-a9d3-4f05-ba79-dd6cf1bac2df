package vn.onepay.portal.resources.merchant_config_view.qr_paygate.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchant_config_view.qr_paygate.dao.QrPaygateDao;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/*
 * Author: Tiennv
 * Date: 15/04/2021
 */
public class QrPaygateHandler implements IConstants {

    private QrPaygateHandler() {

    }
    private static Logger logger = Logger.getLogger(QrPaygateHandler.class.getName());

    public static void getQrPaygate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(TYPE, Util.handleHttpRequest(request, TYPE, "SEARCH").toString());
                mIn.put(KEYWORD, Util.handleHttpRequest(request, KEYWORD, "").toString());
                mIn.put(PAGE, Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer.parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "20").toString()));
                
                Map<String,Object> data = QrPaygateDao.searchQrPaygate(mIn);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE MERCHANT DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantPaygate ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadQrPaygate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(TYPE,"SEARCH");
                mIn.put(KEYWORD, Util.handleJsonObject(bodyJson, KEYWORD, ""));
                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                HttpServerRequest request = ctx.request();
                requestData.put(X_USER_ID, Util.handleHeaderHttpRequest(request, X_USER_ID, ""));
                requestData.put(X_REQUEST_ID, Util.handleHeaderHttpRequest(request, X_REQUEST_ID, ""));
                requestData.put(X_REAL_IP, Util.handleHeaderHttpRequest(request, X_REAL_IP, ""));

                Map<String,Object> dataMerchant = QrPaygateDao.searchQrPaygate(mIn);
                int totalRows = Integer.parseInt(dataMerchant.get("total") + "");
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = dateFormat.format(new Date());
                String fileName = "Qr_Paygate_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("qr_paygate");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                }else{
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "REPORT DOWNLOAD ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}
