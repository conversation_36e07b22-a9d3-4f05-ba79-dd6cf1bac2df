package vn.onepay.portal.resources.merchant_config_view.qr_paygate.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.*;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;

/*
 * Author: Tiennv
 * Date: 12/10/2021
 */
public class QrPaygateDao extends Db implements IConstants {
    private static final Logger logger = Logger.getLogger(QrPaygateDao.class.getName());

    private static final String GET_QR_PAYGATE = "{call MERCHANT_CONFIG_VIEW.GET_QR_PAYGATE(?,?,?,?,?,?,?,?) }";
    private static final String GET_BANK_CONFIG = "{call MSP.MERCHANT_CONFIG_VIEW.GET_BANK_CONFIG(?,?,?,?) }";
    private static final String GET_QR_APP_ACTIVE = "{call MSP.MERCHANT_CONFIG_VIEW.GET_QR_APP_ACTIVE(?,?,?,?,?) }";
    private static final String GET_LIST_APP = "{call MSP.MERCHANT_CONFIG_VIEW.GET_LIST_APP(?,?,?,?,?) }";
    private static final String GET_LIST_APP_V2 = "{call MSP.MERCHANT_CONFIG_VIEW.GET_LIST_APP_V2(?,?,?,?,?) }";
    private static final String GET_QR_APP_HISTORY = "{call MSP.MERCHANT_CONFIG_VIEW.GET_QR_APP_HISTORY(?,?,?,?,?) }";

    public static Map<String, Object> searchQrPaygate(Map<String, Object> mIn) throws Exception {
        Map<String, Object> data = getListQrPaygate(mIn);
        List<Map<String, Object>> list = (List<Map<String, Object>>) data.get("list");

        if ("DETAIL".equals(mIn.get(TYPE))) {
            Map<String, Object> dataQrHistory = getListQrAppHistory(mIn.get(KEYWORD) + "");
            List<Map<String, Object>> listQrHistory = (List<Map<String, Object>>) dataQrHistory.get("list");

            Map<String, Object> dataApp = getListApp(mIn.get(KEYWORD) + "");
            List<Map<String, Object>> listApp = (List<Map<String, Object>>) dataApp.get("list");

            list.get(0).put("listBankCongfig", listApp);
            list.get(0).put("listQrHistory", listQrHistory);
        } else if ("DOWNLOAD".equals(mIn.get(TYPE))) {
            int total = 0;
            for (int i = 0; i < list.size(); i++) {
                int countActive = 0;
                Map<String, Object> dataApp = getListApp(list.get(i).get("merchantId") + "");
                List<Map<String, Object>> listApp = (List<Map<String, Object>>) dataApp.get("list");
                total = (int) dataApp.get("total");
                for (int j = 0; j < listApp.size(); j++) {
                    if ("ACTIVE".equals((listApp.get(j).get("appActive") + "")))
                        countActive = countActive + 1;
                }
                list.get(i).put("appActive", countActive + "/" + total);
            }
        }

        return data;
    }

    public static Map<String, Object> getListQrPaygate(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_QR_PAYGATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, mIn.get(TYPE).toString());
            cs.setString(6, mIn.get(KEYWORD).toString());
            cs.setInt(7, Integer.valueOf(mIn.get(PAGE).toString()));
            cs.setInt(8, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));

            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {

                while (rsList != null && rsList.next()) {
                    Map<String, Object> merchant = new HashMap<>();
                    merchant.put("merchantId", rsList.getString("MERCHANT_ID"));
                    merchant.put("merchantName", rsList.getString("MERCHANT_NAME"));
                    merchant.put("merchantState", rsList.getString("MERCHANT_STATE"));
                    merchant.put("partnerName", rsList.getString("S_PARTNER_NAME"));
                    merchant.put("mcc", rsList.getString("S_CATEGORY_CODE"));
                    merchant.put("city", rsList.getString("S_TINH"));
                    merchant.put("category", rsList.getString("S_TEN_NGANH_NGHE"));
                    list.add(merchant);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", list);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getListBankConfig() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_BANK_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);

            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rsList != null && rsList.next()) {
                    Map<String, Object> bankConfig = new HashMap<>();
                    bankConfig.put("id", rsList.getString("N_ID"));
                    bankConfig.put("ids", rsList.getString("S_ID"));
                    bankConfig.put("shortName", rsList.getString("S_SHORT_NAME"));
                    bankConfig.put("fullName", rsList.getString("S_FULL_NAME"));
                    bankConfig.put("payChannel", rsList.getString("S_PAYCHANNEL"));
                    bankConfig.put("state", rsList.getString("S_ACTIVE"));
                    bankConfig.put("description", rsList.getString("S_DESC"));
                    bankConfig.put("dateCreate", rsList.getTimestamp("D_CREATE"));
                    bankConfig.put("dateCreateFomat", rsList.getString("DATE_CREATE_FOMAT"));
                    bankConfig.put("dateUpdate", rsList.getTimestamp("D_UPDATE"));
                    bankConfig.put("dateUpdateFomat", rsList.getString("DATE_UPDATE_FOMAT"));
                    bankConfig.put("accessCode", rsList.getString("S_ACCESS_CODE"));

                    list.add(bankConfig);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", list);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getListQrAppActive(String merchantId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_QR_APP_ACTIVE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, merchantId);

            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rsList != null && rsList.next()) {
                    Map<String, Object> appQrActive = new HashMap<>();
                    appQrActive.put("id", rsList.getString("N_ID"));
                    appQrActive.put("merchantId", rsList.getString("S_MERCHANT_ID"));
                    appQrActive.put("bank", rsList.getString("S_BANK"));
                    appQrActive.put("dateCreate", rsList.getTimestamp("D_CREATE"));
                    appQrActive.put("dateCreateFomat", rsList.getString("DATE_CREATE_FOMAT"));
                    appQrActive.put("userCreate", rsList.getString("S_CREATE"));
                    appQrActive.put("payChannel", rsList.getString("S_PAY_CHANNEL"));
                    list.add(appQrActive);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", list);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getListApp(String merchantId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_APP_V2);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, merchantId);

            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rsList != null && rsList.next()) {
                    Map<String, Object> appQrActive = new HashMap<>();
                    appQrActive.put("bank", rsList.getString("S_SHORT_NAME"));
                    appQrActive.put("dateCreate", rsList.getString("D_CREATE"));
                    appQrActive.put("dateCreateFomat", rsList.getString("DATE_CREATE_FOMAT"));
                    appQrActive.put("dateUpdate", rsList.getTimestamp("D_UPDATE"));
                    appQrActive.put("dateUpdateFomat", rsList.getString("DATE_UPDATE_FOMAT"));
                    appQrActive.put("payChannel", rsList.getString("S_PAYCHANNEL"));
                    appQrActive.put("appActive", rsList.getString("S_STATUS"));
                    JsonObject jClientData = new JsonObject(rsList.getString("S_CLIENT_DATA") != null ? rsList.getString("S_CLIENT_DATA") : "{}");
                    if (jClientData.containsKey("bank_terminal_id") && jClientData.containsKey("bank_merchant_id")) {
                        appQrActive.put("bankTerminalId", jClientData.getString("bank_terminal_id"));
                        appQrActive.put("bankMerchantId", jClientData.getString("bank_merchant_id"));
                    }
                    list.add(appQrActive);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", list);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getListQrAppHistory(String merchantId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_QR_APP_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, merchantId);

            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rsList != null && rsList.next()) {
                    Map<String, Object> merchant = new HashMap<>();
                    merchant.put("id", rsList.getString("N_ID"));
                    merchant.put("merchantId", rsList.getString("S_MERCHANT_ID"));
                    merchant.put("bank", rsList.getString("S_BANK"));
                    String state = rsList.getString("S_ACTIVE");
                    merchant.put("state", state != null && !"".equals(state) ? state.toUpperCase() : "");
                    merchant.put("before", rsList.getString("S_DATA_BEFORE"));
                    merchant.put("after", rsList.getString("S_DATA_AFTER"));
                    merchant.put("dateUpdate", rsList.getTimestamp("D_UPDATE"));
                    merchant.put("dateUpdateFomat", rsList.getString("DATE_UPDATE_FOMAT"));
                    merchant.put("userUpdate", rsList.getString("S_UPDATE"));
                    list.add(merchant);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", list);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

}
