package vn.onepay.portal.resources.onepay_registed_email.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.onepay_registed_email.dto.RegisteredEmailsDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class HomepageRegisteredEmailDAO extends Db implements IConstants {
    private static final Logger logger = Logger.getLogger(HomepageRegisteredEmailDAO.class.getName());
    private static final SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm a");

    private static final String GET_ALL_REGISTERED_EMAILS = "{call LDP.GET_ALL_REGISTERED_EMAILS(?,?,?)}";


    public static List<RegisteredEmailsDto> getAllRegisteredEmails() throws Exception {
        RegisteredEmailsDto registeredEmailsDto = null;
        List<RegisteredEmailsDto> lstResult = new ArrayList<>();
        Exception ex = null;
        Connection conn = null;
        ResultSet rs = null;
        CallableStatement cs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(GET_ALL_REGISTERED_EMAILS);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.execute();

            int nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200) {
                throw new Exception("DB GET_ALL_REGISTERED_EMAILS error: " + sResult);
            }
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next()) {
                registeredEmailsDto = new RegisteredEmailsDto();
                registeredEmailsDto.setEmail(Util.getColumnString(rs, "S_EMAIL"));
                registeredEmailsDto.setRegisteredDate(sdf.format(Util.getColumnDate(rs, "D_CREATE")));
                lstResult.add(registeredEmailsDto);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "getAllRegisteredEmails error: " + e.getMessage());
            ex = e;
        }

        if (ex != null) {
            throw ex;
        }

        return lstResult;
    }
}
