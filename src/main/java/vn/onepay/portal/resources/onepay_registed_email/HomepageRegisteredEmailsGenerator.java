package vn.onepay.portal.resources.onepay_registed_email;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceBigMerchantDsGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.onepay_registed_email.dao.HomepageRegisteredEmailDAO;
import vn.onepay.portal.resources.onepay_registed_email.dto.RegisteredEmailsDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class HomepageRegisteredEmailsGenerator implements BaseGenerator<Map> {

    private static final Logger LOGGER = Logger.getLogger(AdvanceBigMerchantDsGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<RegisteredEmailsDto> lstRegisteredEmails = (List<RegisteredEmailsDto>) HomepageRegisteredEmailDAO.getAllRegisteredEmails();
            int i = 1;
            Map map;
            for (RegisteredEmailsDto item : lstRegisteredEmails) {
                map = new HashMap();
                map.put("NO", i++);
                map.put("S_EMAIL", item.getEmail());
                map.put("S_REGISTERED_DATE", item.getRegisteredDate());

                listMap.add(map);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Registered Emails error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }
}
