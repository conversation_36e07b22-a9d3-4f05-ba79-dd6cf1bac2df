package vn.onepay.portal.resources.onepay_registed_email;

import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.on_off_banks.OnOffBankDao;
import vn.onepay.portal.resources.onepay_registed_email.dao.HomepageRegisteredEmailDAO;
import vn.onepay.portal.resources.onepay_registed_email.dto.RegisteredEmailsDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.IConstants.*;
import static vn.onepay.portal.Util.sendResponse;

public class HomepageRegisteredEmailHandler {

    public static final Logger logger = Logger.getLogger(HomepageRegisteredEmailHandler.class.getName());

    public static void downloadRegisteredEmails(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                long date = new java.util.Date().getTime();
                String fileName = "Website_OnePay_Registered_Emails_" + date;
                String fileHashName = "";
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("website_onepay_registered_emails");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                List<RegisteredEmailsDto> lstRegisteredEmails = HomepageRegisteredEmailDAO.getAllRegisteredEmails();
                int totalRows = lstRegisteredEmails.size();
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(
                            new Message(new HashMap<>(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(
                            new Message(new HashMap<>(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "downloadRegisteredEmails error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
