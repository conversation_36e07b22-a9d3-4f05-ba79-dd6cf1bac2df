package vn.onepay.portal.resources.approval_request;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.approval_request.dto.ApprovalDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/11/2020
 * Time: 9:19 AM
 * To change this iportal-service.
 */

public class ApprovalRequestDAO extends Db implements IConstants {

    // public static void insertMspMerchantInsertApproval(ApprovalDto dto,)

    public static Integer approve(ApprovalDto dto) throws Exception {
        return changeStatus(dto, "approved");
    }

    public static Integer reject(ApprovalDto dto) throws Exception {
        return changeStatus(dto, "rejected");
    }


    private static Integer changeStatus(ApprovalDto dto, String status) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.update_status(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, dto.getId());
            cs.setString(4, status);
            cs.setString(5, dto.getUserConfirm());
            cs.execute();
            result = cs.getInt(1);
            String sError = cs.getString(2);
            if (result != 200) {
                logger.severe("get_approval_by_id " + " error: " + result + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    public static Integer insertApproval(ApprovalDto dto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection114();
            Clob oldValue = conn.createClob();
            oldValue.setString(1, dto.getOldValue() == null ? "" : dto.getOldValue());
            Clob newValue = conn.createClob();
            newValue.setString(1, dto.getNewValue() == null ? "" : dto.getNewValue());
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.insert_approval(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, dto.getName());
            cs.setString(5, dto.getSchema());
            cs.setString(6, dto.getTable());
            cs.setString(7, dto.getField());
            cs.setString(8, "pending");
            cs.setString(9, dto.getCondition());
            cs.setString(10, dto.getUserRequest());
            cs.setString(11, dto.getType());
            cs.setString(12, dto.getDescription());
            cs.setClob(13, oldValue);
            cs.setClob(14, newValue);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("INSERT APPROVAL  error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                result = cs.getInt(1);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }


    public static String checkApproval(String key, String value) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVAL.CHECK_APPROVAL(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, value);
            cs.setString(5, key);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("CHECK_APPROVAL " + key + " error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            result = cs.getString(1);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    public static BaseList<ApprovalDto> getMerchantConfigApproval(Map<String, String> min) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        BaseList<ApprovalDto> dtoBaseList = new BaseList<>();
        List<ApprovalDto> arrayList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.GET_MER_CONF_APPROVAL2(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setObject(5, min.getOrDefault(KEYWORD, ""));
            cs.setObject(6, min.getOrDefault(PAGE, "0"));
            cs.setObject(7, min.getOrDefault(PAGE_SIZE, "100"));
            cs.setObject(8, min.getOrDefault(MERCHANT_ID, ""));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(2);
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("CHECK_MERCHANT_APPROVAL " + " error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                while (rs != null && rs.next()) {
                    arrayList.add(bindApproval(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        dtoBaseList.setList(arrayList);
        dtoBaseList.setTotalItems(total);
        return dtoBaseList;
    }

    public static BaseList<ApprovalDto> getPaycollectMerchantConfigApproval(Map<String, String> min) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        BaseList<ApprovalDto> dtoBaseList = new BaseList<>();
        List<ApprovalDto> arrayList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.get_pc_mer_conf_approval(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setObject(5, min.getOrDefault(KEYWORD, ""));
            cs.setObject(6, min.getOrDefault(PAGE, "0"));
            cs.setObject(7, min.getOrDefault(PAGE_SIZE, "100"));
            cs.setObject(8, min.getOrDefault(MERCHANT_ID, ""));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(2);
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("CHECK_PAYCOLLECT_MERCHANT_APPROVAL " + " error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                while (rs != null && rs.next()) {
                    arrayList.add(bindApproval(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        dtoBaseList.setList(arrayList);
        dtoBaseList.setTotalItems(total);
        return dtoBaseList;
    }

    public static BaseList<ApprovalDto> getIpnConfigApproval(Map<String, String> min) throws Exception {
        return getApproval("{ call ONEPORTAL.PKG_APPROVE.GET_ALL_IPN_APPROVAL(?,?,?,?,?,?,?)}", min);
    }

    public static BaseList<ApprovalDto> getMerchantProfileApproval(Map<String, String> min) throws Exception {
        return getApproval("{ call ONEPORTAL.PKG_APPROVE.get_merchant_profile(?,?,?,?,?,?,?)}", min);
    }

    /**
     * Merchant Approval Request
     * 
     * @param
     * @throws Exception
     */
    private static BaseList<ApprovalDto> getApproval(String sql, Map<String, String> min) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        BaseList<ApprovalDto> dtoBaseList = new BaseList<>();
        List<ApprovalDto> arrayList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs = conn.prepareCall(sql);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setObject(5, min.getOrDefault(KEYWORD, ""));
            cs.setObject(6, min.getOrDefault(PAGE, "0"));
            cs.setObject(7, min.getOrDefault(PAGE_SIZE, "100"));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(2);
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("CHECK_MERCHANT_APPROVAL " + " error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            } else {
                while (rs != null && rs.next()) {
                    arrayList.add(bindApproval(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        dtoBaseList.setList(arrayList);
        dtoBaseList.setTotalItems(total);
        return dtoBaseList;
    }

    public static String countApproval(String key) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.get_count_approval(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setObject(4, key);
            cs.execute();
            total = cs.getInt(1);
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("countApproval " + " error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return total > 0 ? "Y" : "N";
    }

    public static ApprovalDto getApprovalById(Integer id) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ApprovalDto approvalDto = new ApprovalDto();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.get_approval_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("get_approval_by_id " + " error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    approvalDto = bindApproval(rs);
                }

            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return approvalDto;
    }


    private static ApprovalDto bindApproval(ResultSet rs) throws Exception {
        ApprovalDto approvalDto = new ApprovalDto();
        approvalDto.setCreatedDate(rs.getTimestamp("D_CREATE"));
        approvalDto.setId(rs.getInt("N_ID"));
        approvalDto.setNewValue(rs.getString("S_NEW_VALUE"));
        approvalDto.setOldValue(rs.getString("S_OLD_VALUE"));
        approvalDto.setUserRequest(rs.getString("S_USER_NAME"));
        approvalDto.setType(rs.getString("S_TYPE"));
        approvalDto.setCondition(rs.getString("S_CONDITIONS"));
        return approvalDto;
    }

    public static BaseList<ApprovalDto> getShopifyApproval(Map<String, String> min) throws Exception {
        return getApproval("{ call ONEPORTAL.PKG_APPROVE.get_all_shopify_approval(?,?,?,?,?,?,?)}", min);
    }


    public static void overwriteQrAcceptInstrument(String acceptInstrument, String merchantId) {
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{call ONEPORTAL.PKG_MSP_MERCHANT_CONFIG.overwrite_qr_accept_instrument(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, acceptInstrument);
            cs.setString(4, merchantId);
            cs.execute();

            int nResult = cs.getInt(1);
            if (nResult != 200) {
                logger.log(Level.SEVERE, "Error: ", cs.getString(2));
            }
            logger.info("Overwrite QR Accept Instrument Successful! - merchant_id: " + merchantId);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
    }

    public static BaseList<ApprovalDto> getBankAccPaycollectApproval(Map<String, String> min) throws Exception {
        return getApproval("{ call ONEPORTAL.PKG_APPROVE.get_pc_bank_acc_approval(?,?,?,?,?,?,?)}", min);
    }
}
