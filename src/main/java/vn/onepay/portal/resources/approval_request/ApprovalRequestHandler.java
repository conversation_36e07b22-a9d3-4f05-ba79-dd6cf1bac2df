package vn.onepay.portal.resources.approval_request;
import io.vertx.ext.web.RoutingContext;

import java.beans.Transient;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/11/2020
 * Time: 9:18 AM
 * To change this iportal-service.
 */

public class ApprovalRequestHandler {
    private static Logger logger = Logger.getLogger(ApprovalRequestHandler.class.getName());
    @Transient
    public static void list(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {

//                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
            logger.log(Level.WARNING, "APPROVAL REQUEST LIST ERROR: ", e);
            ctx.fail(e);
            }
        }, false, null);

    }
    @Transient
    public static void insertUpdateAproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
//                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "APPROVAL REQUEST INSERT UPDATE ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    @Transient
    public static void aprovalReject(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
//                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "APPROVAL REQUEST APPROVE REJECT ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
