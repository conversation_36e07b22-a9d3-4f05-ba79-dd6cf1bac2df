package vn.onepay.portal.resources.approval_request.dto;

import io.vertx.core.json.JsonObject;

import java.sql.Timestamp;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/18/2020
 * Time: 9:23 AM
 * To change this iportal-service.
 */

public class ApprovalConvertDto {

    Integer id;
    String name;
    String schema;
    String table;
    String field;
    String status;
    Map condition;
    String type;
    String description;
    String userRequest;
    String userConfirm;
    Map newValue;
    Map oldValue;
    Timestamp createdDate;
    Timestamp updatedDate;
    String updatedDateString;
    int isPaymentLink;

    public ApprovalConvertDto() {
    }

    public ApprovalConvertDto(ApprovalDto dto) {
        this.id = dto.id;
        this.name = dto.name;
        this.schema = dto.schema;
        this.table = dto.table;
        this.field = dto.field;
        this.status = dto.status;
        this.type = dto.type;
        this.description = dto.description;
        this.userRequest = dto.userRequest;
        this.userConfirm = dto.userConfirm;
        this.createdDate = dto.createdDate;
        this.updatedDate = dto.updatedDate;
        this.condition = StringUtils.isEmpty(dto.getCondition()) ? null : new JsonObject(dto.getCondition()).getMap();
        this.newValue = StringUtils.isEmpty(dto.getNewValue()) ? null : new JsonObject(dto.getNewValue()).getMap();
        this.oldValue = StringUtils.isEmpty(dto.getOldValue()) ? null : new JsonObject(dto.getOldValue()).getMap();
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setUserRequest(String userRequest) {
        this.userRequest = userRequest;
    }

    public String getUserConfirm() {
        return userConfirm;
    }

    public void setUserConfirm(String userConfirm) {
        this.userConfirm = userConfirm;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserRequest() {
        return userRequest;
    }

    public Map getCondition() {
        return condition;
    }

    public void setCondition(Map condition) {
        this.condition = condition;
    }

    public Map getNewValue() {
        return newValue;
    }

    public void setNewValue(Map newValue) {
        this.newValue = newValue;
    }

    public Map getOldValue() {
        return oldValue;
    }

    public void setOldValue(Map oldValue) {
        this.oldValue = oldValue;
    }

    public int getIsPaymentLink() {
        return isPaymentLink;
    }

    public void setIsPaymentLink(int isPaymentLink) {
        this.isPaymentLink = isPaymentLink;
    }

    public String getUpdatedDateString() {
        return this.updatedDateString;
    }

    public void setUpdatedDateString(String updatedDateString) {
        this.updatedDateString = updatedDateString;
    }

    public ApprovalConvertDto  ConvertAppove(ApprovalDto dto) {
         this.id = dto.id;
         this.name = dto.name;
         this.schema = dto.schema;
         this.table = dto.table;
         this.field = dto.field;
         this.status = dto.status;
         this.type = dto.type;
         this.description = dto.description;
         this.userRequest = dto.userRequest;
         this.userConfirm = dto.userConfirm;
         this.createdDate = dto.createdDate;
         this.updatedDate = dto.updatedDate;
         this.updatedDateString =  dto.getUpdatedDateString();
         JsonObject json = new JsonObject();
         json.put("merchantId", dto.getCondition());
         this.condition = dto.getCondition() == null ? null : json.getMap();
         this.newValue = dto.getNewValue() == null ? null : new JsonObject(dto.getNewValue()).getMap();
         this.oldValue = dto.getOldValue() == null ? null : new JsonObject(dto.getOldValue()).getMap();
         return this;
    }
}
