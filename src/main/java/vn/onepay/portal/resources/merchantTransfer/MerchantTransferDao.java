package vn.onepay.portal.resources.merchantTransfer;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantDto;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantTransferDto;
import vn.onepay.portal.resources.refundApproval.dto.RefundApprovalDto;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MerchantTransferDao extends Db implements IConstants {

    public static BaseList<MerchantTransferDto> search(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantTransferDto> result = new BaseList<>();
        List<MerchantTransferDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_transfer_list(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(FROM_DATE) + "");
            cs.setString(5, mIn.get(TO_DATE) + "");
            cs.setInt(6, Convert.parseInt(mIn.get(PAGE) + "", 0));
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE_SIZE) + "", Integer.MAX_VALUE));
            cs.setString(8, mIn.get(MERCHANT_ID) + "");
            cs.setString(9, mIn.get(MERCHANT_ACCOUNT) + "");
            cs.setString(10, mIn.get(PAY_CHANNEL) + "");
            cs.setString(11, mIn.get(ONEPAY_ACCOUNT) + "");
            cs.setString(12, mIn.get(REFERENCE) + "");
            cs.setString(13, mIn.get(STATE) + "");
            cs.setString(14, mIn.get(TYPE) + "");
            cs.setString(15, mIn.get(APPROVE) + "");
            cs.setInt(16, Convert.parseInt(mIn.get(ADVANCE) + "", 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    MerchantTransferDto dto = new MerchantTransferDto();
                    dto.setId(rs.getLong("N_ID"));
                    dto.setMerchantAccount(rs.getString("S_MERCHANT_ACCOUNT"));
                    dto.setMerchantId(rs.getString("S_MERCHANT_ID"));
                    dto.setAmount(rs.getDouble("N_AMOUNT"));
                    dto.setExpectedAmount(rs.getDouble("N_EXPECTED_AMOUNT"));
                    dto.setReference(rs.getString("S_REFERENCE"));
                    dto.setDesc(rs.getString("S_DESC"));
                    dto.setState(rs.getString("S_STATE"));
                    dto.setApprove(rs.getString("S_APPROVE"));
                    dto.setNote(rs.getString("S_NOTE"));
                    dto.setType(rs.getString("S_TYPE"));
                    dto.setDate(rs.getTimestamp("D_DATE"));
                    dto.setPayChannel(rs.getString("S_PAY_CHANNEL"));
                    dto.setOnepayAccount(rs.getString("S_ONEPAY_ACCOUNT"));
                    dto.setOnepayBank(rs.getString("S_ONEPAY_BANK"));
                    dto.setSourceType(rs.getString("S_SOURCE_TYPE"));
                    dto.setAdvanceId(rs.getLong("N_ID_ADVANCE"));
                    dto.setPartnerName(rs.getString("S_PARTNER_NAME"));
                    dto.setBusinessRegName(rs.getString("S_BUSINESS_REG_NAME"));
                    list.add(dto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static Integer total(Map mIn) throws Exception {
        Exception exception = null;
        Integer total = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_transfer_total(?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(FROM_DATE) + "");
            cs.setString(5, mIn.get(TO_DATE) + "");
            cs.setString(6, mIn.get(MERCHANT_ID) + "");
            cs.setString(7, mIn.get(MERCHANT_ACCOUNT) + "");
            cs.setString(8, mIn.get(PAY_CHANNEL) + "");
            cs.setString(9, mIn.get(ONEPAY_ACCOUNT) + "");
            cs.setString(10, mIn.get(REFERENCE) + "");
            cs.setString(11, mIn.get(STATE) + "");
            cs.setString(12, mIn.get(TYPE) + "");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            total = cs.getInt(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static Integer insert(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_transfer_insert(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.get(MERCHANT_ACCOUNT) + "");
            cs.setString(4, mIn.get(MERCHANT_ID) + "");
            cs.setString(5, mIn.get(AMOUNT) + "");
            cs.setString(6, mIn.get(EXPECTED_AMOUNT) + "");
            cs.setString(7, mIn.get(REFERENCE) + "");
            cs.setString(8, mIn.get(STATE) + "");
            cs.setString(9, mIn.get(TYPE) + "");
            cs.setString(10, mIn.get(DESC) + "");
            cs.setString(11, mIn.get(NOTE) + "");
            cs.setString(12, mIn.get(DATE) + "");
            cs.setString(13, mIn.get(PAY_CHANNEL) + "");
            cs.setString(14, mIn.get(ONEPAY_ACCOUNT) + "");
            cs.setString(15, mIn.get(ONEPAY_BANK) + "");
            cs.setString(16, mIn.get(SOURCE_TYPE) + "");
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer update(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_transfer_update(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.get(ID) + "");
            cs.setString(4, mIn.get(MERCHANT_ACCOUNT) + "");
            cs.setString(5, mIn.get(MERCHANT_ID) + "");
            cs.setString(6, mIn.get(AMOUNT) + "");
            cs.setString(7, mIn.get(EXPECTED_AMOUNT) + "");
            cs.setString(8, mIn.get(REFERENCE) + "");
            cs.setString(9, mIn.get(STATE) + "");
            cs.setString(10, mIn.get(TYPE) + "");
            cs.setString(11, mIn.get(DESC) + "");
            cs.setString(12, mIn.get(NOTE) + "");
            cs.setString(13, mIn.get(PAY_CHANNEL) + "");
            cs.setString(14, mIn.get(ONEPAY_ACCOUNT) + "");
            cs.setString(15, mIn.get(ONEPAY_BANK) + "");
            cs.setString(16, mIn.get(SOURCE_TYPE) + "");
            cs.setString(17, mIn.get(DATE) + "");
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer updateState(Long id, String state) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.mt_update_status(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.setString(4, state);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<MerchantDto> searchMerchant(String keyword) throws Exception {
        Exception exception = null;
        BaseList<MerchantDto> baseList = new BaseList<>();
        List<MerchantDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_list(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, keyword);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    MerchantDto dto = new MerchantDto();
                    dto.setId(rs.getString("S_MERCHANT_ID"));
                    dto.setAccount(rs.getString("S_ADVANCE_ACCOUNT"));
                    dto.setPayChannel(rs.getString("S_PAY_CHANNEL"));
                    list.add(dto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        baseList.setList(list);
        return baseList;
    }

    public static BaseList<MerchantDto> searchMerchantId(String account, String payChannel) throws Exception {
        Exception exception = null;
        BaseList<MerchantDto> baseList = new BaseList<>();
        List<MerchantDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.merchant_id_list(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, account);
            cs.setString(5, payChannel);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    MerchantDto dto = new MerchantDto();
                    dto.setId(rs.getString("S_MERCHANT_ID"));
                    dto.setPayChannel(rs.getString("S_PAY_CHANNEL"));
                    list.add(dto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        baseList.setList(list);
        return baseList;
    }

    public static Integer adjustMerchantTransfer(String advanceAccount, Double adjustAmount, RefundApprovalDto dto) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.mt_adjust_refund(?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, advanceAccount);
            cs.setDouble(4, adjustAmount);
            cs.setString(5, dto.getMerchantId());
            cs.setString(6, dto.getPayChannel());
            cs.setString(7, "refund");
            cs.setString(8, dto.getId());
            cs.setDouble(9, dto.getAmount());
            cs.setString(10, dto.getState());
            cs.setString(11, dto.getCurrency());
            cs.execute();

            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
            result = 1;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer approve(Long id, String state) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.mt_approve(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.setString(4, state);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer reject(Long id) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_MERCHANT_TRANSFER.mt_delete(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
