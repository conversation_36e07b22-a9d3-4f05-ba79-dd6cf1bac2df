package vn.onepay.portal.resources.merchantTransfer.dto;

import java.sql.Timestamp;

public class MerchantTransferDto {
    private Long id;
    private String merchantAccount;
    private String merchantId;
    private Double amount;
    private Double expectedAmount;
    private String reference;
    private String desc;
    private String state;
    private String approve;
    private Long advanceId;
    private String note;
    private String type;
    private Timestamp date;
    private String payChannel;
    private String onepayAccount;
    private String onepayBank;
    private String sourceType;

    // Extra
    private String partnerName;
    private String businessRegName;

    public Long getId() {
        return id;
    }

    public void setAdvanceId(Long advanceId) {
        this.advanceId = advanceId;
    }

    public Long getAdvanceId() {
        return advanceId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApprove() {
        return approve;
    }

    public void setApprove(String approve) {
        this.approve = approve;
    }

    public String getMerchantAccount() {
        return merchantAccount;
    }

    public void setMerchantAccount(String merchantAccount) {
        this.merchantAccount = merchantAccount;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getExpectedAmount() {
        return expectedAmount;
    }

    public void setExpectedAmount(Double expectedAmount) {
        this.expectedAmount = expectedAmount;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getOnepayAccount() {
        return onepayAccount;
    }

    public void setOnepayAccount(String onepayAccount) {
        this.onepayAccount = onepayAccount;
    }

    public String getOnepayBank() {
        return onepayBank;
    }

    public void setOnepayBank(String onepayBank) {
        this.onepayBank = onepayBank;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getBusinessRegName() {
        return businessRegName;
    }

    public void setBusinessRegName(String businessRegName) {
        this.businessRegName = businessRegName;
    }
}
