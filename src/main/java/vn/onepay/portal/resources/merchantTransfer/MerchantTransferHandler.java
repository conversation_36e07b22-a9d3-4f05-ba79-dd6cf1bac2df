package vn.onepay.portal.resources.merchantTransfer;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.refund.DomesticRefundDao;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantDto;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantTransferDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantTransferHandler implements IConstants {

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String fromDate = request.getParam(FROM_DATE) == null ? BLANK : request.getParam(FROM_DATE);
                String toDate = request.getParam(TO_DATE) == null ? BLANK : request.getParam(TO_DATE);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID) == null ? BLANK : request.getParam(MERCHANT_ID));
                mIn.put(MERCHANT_ACCOUNT, request.getParam(MERCHANT_ACCOUNT) == null ? BLANK : request.getParam(MERCHANT_ACCOUNT));
                mIn.put(PAY_CHANNEL, request.getParam(PAY_CHANNEL) == null ? BLANK : request.getParam(PAY_CHANNEL));
                mIn.put(ONEPAY_ACCOUNT, request.getParam(ONEPAY_ACCOUNT) == null ? BLANK : request.getParam(ONEPAY_ACCOUNT));
                mIn.put(REFERENCE, request.getParam(REFERENCE) == null ? BLANK : request.getParam(REFERENCE));
                mIn.put(STATE, request.getParam(STATE) == null ? BLANK : request.getParam(STATE));
                mIn.put(TYPE, request.getParam(TYPE) == null ? BLANK : request.getParam(TYPE));
                mIn.put(APPROVE, request.getParam(APPROVE) == null ? BLANK : request.getParam(APPROVE));
                mIn.put(ADVANCE, request.getParam(ADVANCE) == null || request.getParam(ADVANCE).equals("") ? 0 : request.getParam(ADVANCE));
                BaseList<MerchantTransferDto> baseList = MerchantTransferDao.search(mIn);
                baseList.setTotalItems(MerchantTransferDao.total(mIn));
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insert(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();

                String date = body.getString(DATE) == null ? BLANK : body.getString(DATE);
                
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MERCHANT_ACCOUNT, body.getString(MERCHANT_ACCOUNT) == null ? BLANK : body.getString(MERCHANT_ACCOUNT));
                mIn.put(MERCHANT_ID, body.getString(MERCHANT_ID) == null ? BLANK : body.getString(MERCHANT_ID));
                mIn.put(AMOUNT, body.getString(AMOUNT) == null ? 0 : Double.parseDouble(body.getString(AMOUNT)));
                mIn.put(EXPECTED_AMOUNT, body.getString(EXPECTED_AMOUNT) == null ? 0 : Double.parseDouble(body.getString(EXPECTED_AMOUNT)));
                mIn.put(REFERENCE, body.getString(REFERENCE) == null ? BLANK : body.getString(REFERENCE));
                mIn.put(STATE, body.getString(STATE) == null ? BLANK : body.getString(STATE));
                mIn.put(TYPE, body.getString(TYPE) == null ? BLANK : body.getString(TYPE));
                mIn.put(DESC, body.getString(DESC) == null ? BLANK : body.getString(DESC));
                mIn.put(NOTE, body.getString(NOTE) == null ? BLANK : body.getString(NOTE));
                mIn.put(DATE, date);
                mIn.put(PAY_CHANNEL, body.getString(PAY_CHANNEL) == null ? BLANK : body.getString(PAY_CHANNEL));
                mIn.put(ONEPAY_ACCOUNT, body.getString(ONEPAY_ACCOUNT) == null ? BLANK : body.getString(ONEPAY_ACCOUNT));
                mIn.put(ONEPAY_BANK, body.getString(ONEPAY_BANK) == null ? BLANK : body.getString(ONEPAY_BANK).toUpperCase());
                mIn.put(SOURCE_TYPE, body.getString(SOURCE_TYPE) == null ? BLANK : body.getString(SOURCE_TYPE));

                MerchantTransferDao.insert(mIn);
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void update(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();

                String date = body.getString(DATE) == null ? BLANK : body.getString(DATE);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? 0L : Long.parseLong(request.getParam(ID)));
                mIn.put(MERCHANT_ACCOUNT, body.getString(MERCHANT_ACCOUNT) == null ? BLANK : body.getString(MERCHANT_ACCOUNT));
                mIn.put(MERCHANT_ID, body.getString(MERCHANT_ID) == null ? BLANK : body.getString(MERCHANT_ID));
                mIn.put(AMOUNT, body.getString(AMOUNT) == null ? 0 : Double.parseDouble(body.getString(AMOUNT)));
                mIn.put(EXPECTED_AMOUNT, body.getString(EXPECTED_AMOUNT) == null ? 0 : Double.parseDouble(body.getString(EXPECTED_AMOUNT)));
                mIn.put(REFERENCE, body.getString(REFERENCE) == null ? BLANK : body.getString(REFERENCE));
                mIn.put(STATE, body.getString(STATE) == null ? BLANK : body.getString(STATE));
                mIn.put(TYPE, body.getString(TYPE) == null ? BLANK : body.getString(TYPE));
                mIn.put(DESC, body.getString(DESC) == null ? BLANK : body.getString(DESC));
                mIn.put(NOTE, body.getString(NOTE) == null ? BLANK : body.getString(NOTE));
                mIn.put(DATE, date);
                mIn.put(PAY_CHANNEL, body.getString(PAY_CHANNEL) == null ? BLANK : body.getString(PAY_CHANNEL));
                mIn.put(ONEPAY_ACCOUNT, body.getString(ONEPAY_ACCOUNT) == null ? BLANK : body.getString(ONEPAY_ACCOUNT));
                mIn.put(ONEPAY_BANK, body.getString(ONEPAY_BANK) == null ? BLANK : body.getString(ONEPAY_BANK).toUpperCase());
                mIn.put(SOURCE_TYPE, body.getString(SOURCE_TYPE) == null ? BLANK : body.getString(SOURCE_TYPE));


                MerchantTransferDao.update(mIn);
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                e.printStackTrace();
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                Long id = body.getValue(ID) == null ? 0L : Long.parseLong(body.getValue(ID).toString());
                String state = body.getString(STATE) == null ? BLANK : body.getString(STATE);
                MerchantTransferDao.updateState(id, state);
                sendResponse(ctx, 200, new HashMap<>());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = ctx.getBodyAsJson();
                String fromDate = mIn.getString(FROM_DATE);
                String toDate = mIn.getString(TO_DATE);

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                int totalRows = MerchantTransferDao.total(mIn.getMap());
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "merchant_transfer_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn.getMap());
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("merchant_transfer");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(mIn.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                }else{
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    //fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    //fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING,ctx.get(REQUEST_UUID) + ": "+ "DOWNLOAD MERCHANT TRANSFER Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String keyword = request.getParam(KEYWORD) == null ? BLANK : request.getParam(KEYWORD);

                BaseList<MerchantDto> baseList = MerchantTransferDao.searchMerchant(keyword);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchMerchantId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                String account = body.getString(ACCOUNT) == null ? BLANK : body.getString(ACCOUNT);
                String payChannel = body.getString(PAYCHANNEL) == null ? BLANK : body.getString(PAYCHANNEL);
                BaseList<MerchantDto> baseList = MerchantTransferDao.searchMerchantId(account, payChannel);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveMerchantTransfer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Long id = request.getParam(ID) == null ? 0L : Long.parseLong(request.getParam(ID));

                MerchantTransferDao.approve(id, "approved");
                sendResponse(ctx, 200, new HashMap<>());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void rejectMerchantTransfer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Long id = request.getParam(ID) == null ? 0L : Long.parseLong(request.getParam(ID));

                MerchantTransferDao.reject(id);
                sendResponse(ctx, 200, new HashMap<>());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    final private static Logger logger = Logger.getLogger(MerchantTransferHandler.class.getName());

}
