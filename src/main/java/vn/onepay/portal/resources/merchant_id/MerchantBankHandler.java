package vn.onepay.portal.resources.merchant_id;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.merchant_id.dto.MerchantBankDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantBankHandler {
    public static void getConfigMerchantND(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String acquirerId =  request.getParam("acq_id");
            String contractType = request.getParam("contract_type");
            Map<String,Object> temp = new HashMap<>();
            try {
                List<MerchantBankDto> config = MerchantBankDao.getMerchantBankByAcq(acquirerId,contractType,"ND");
                temp.put("list",config);
                sendResponse(ctx, 200,temp);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
