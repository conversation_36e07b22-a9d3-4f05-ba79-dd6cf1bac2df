package vn.onepay.portal.resources.merchant_id;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.merchant_id.dto.MerchantPaypalDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantPaypalHandler {
    public static void getConfigMerchantPaypal(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int partnerId = Integer.parseInt(request.getParam("partner_id"));
            Map<String,Object> temp = new HashMap<>();
            try {
                List<MerchantPaypalDto> config = MerchantPaypalDao.getMerchantPaypalByPartner(partnerId);
                temp.put("list",config);
                sendResponse(ctx, 200,temp);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
