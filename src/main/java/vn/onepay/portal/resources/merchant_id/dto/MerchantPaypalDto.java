package vn.onepay.portal.resources.merchant_id.dto;

import java.util.Date;

public class MerchantPaypalDto {

    private int Id;
    private int partnerId;
    private String paypalMerchantId;
    private String statePaypal;
    private String merchantId;
    private Date createDate;
    private Date updateDate;
    private String desc;
    private String emailConfirm;
    private String paymentsReceivable;
    private String type;

    public MerchantPaypalDto() {
    }

    public MerchantPaypalDto(int id, int partnerId, String paypalMerchantId, String statePaypal, String merchantId, Date createDate, Date updateDate, String desc, String emailConfirm, String paymentsReceivable, String type) {
        Id = id;
        this.partnerId = partnerId;
        this.paypalMerchantId = paypalMerchantId;
        this.statePaypal = statePaypal;
        this.merchantId = merchantId;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.desc = desc;
        this.emailConfirm = emailConfirm;
        this.paymentsReceivable = paymentsReceivable;
        this.type = type;
    }

    public int getId() {
        return Id;
    }

    public MerchantPaypalDto setId(int id) {
        Id = id;
        return this;
    }

    public int getPartnerId() {
        return partnerId;
    }

    public MerchantPaypalDto setPartnerId(int partnerId) {
        this.partnerId = partnerId;
        return this;
    }

    public String getPaypalMerchantId() {
        return paypalMerchantId;
    }

    public MerchantPaypalDto setPaypalMerchantId(String paypalMerchantId) {
        this.paypalMerchantId = paypalMerchantId;
        return this;
    }

    public String getStatePaypal() {
        return statePaypal;
    }

    public MerchantPaypalDto setStatePaypal(String statePaypal) {
        this.statePaypal = statePaypal;
        return this;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public MerchantPaypalDto setMerchantId(String merchantId) {
        this.merchantId = merchantId;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public MerchantPaypalDto setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public MerchantPaypalDto setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public MerchantPaypalDto setDesc(String desc) {
        this.desc = desc;
        return this;
    }

    public String getEmailConfirm() {
        return emailConfirm;
    }

    public MerchantPaypalDto setEmailConfirm(String emailConfirm) {
        this.emailConfirm = emailConfirm;
        return this;
    }

    public String getPaymentsReceivable() {
        return paymentsReceivable;
    }

    public MerchantPaypalDto setPaymentsReceivable(String paymentsReceivable) {
        this.paymentsReceivable = paymentsReceivable;
        return this;
    }

    public String getType() {
        return type;
    }

    public MerchantPaypalDto setType(String type) {
        this.type = type;
        return this;
    }
}
