package vn.onepay.portal.resources.merchant_id;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchant_id.dto.MerchantBankDto;
import vn.onepay.portal.resources.merchantconfig.MerchantConfigHandler;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantBankDao  extends Db implements IConstants {
    private static Logger logger = Logger.getLogger(MerchantBankDao.class.getName());
    public static List<MerchantBankDto> getMerchantBankByAcq(String acquirerId,String contractType,String payChannel) throws Exception {
        logger.info("DB get MerchantBankDao by Acq" + acquirerId + "-" + contractType);
        Exception exception = null;
        List<MerchantBankDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.get_list_merchant_bank_by_acq(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, acquirerId);
            cs.setString(5, contractType);
            cs.setString(6, payChannel);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get MerchantBankDao by Acq error: " + error);
            } else {
                while (rs.next()) {
                    MerchantBankDto merchantBankDto = new MerchantBankDto();
                    merchantBankDto.setBankMerchantId(rs.getString("S_BANK_MERCHANT_ID"))
                            .setBankMerchantIdShow(rs.getString("S_BANK_MERCHANT_ID_SHOW"))
                            .setAcquirer(rs.getString("S_ACQUIRER"))
                            .setnAcquirer(rs.getInt("N_ACQUIRER_ID"))
                            .setContractType(rs.getString("S_CONTRACT_TYPE"));
                    result.add(merchantBankDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
