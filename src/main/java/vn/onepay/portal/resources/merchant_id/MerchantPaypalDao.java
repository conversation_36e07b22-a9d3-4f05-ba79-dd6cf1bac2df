package vn.onepay.portal.resources.merchant_id;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchant_id.dto.MerchantPaypalDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class MerchantPaypalDao extends Db implements IConstants {
    private static Logger logger = Logger.getLogger(MerchantPaypalDao.class.getName());
    public static List<MerchantPaypalDto> getMerchantPaypalByPartner(int partnerId) throws Exception {
        logger.info("DB get MerchantPaypalDao by Partner" + partnerId);
        Exception exception = null;
        List<MerchantPaypalDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall("{call ONEPORTAL.PKG_ONECREDIT.search_paypal_by_partner_id(?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.setInt(2, partnerId);
            cs.executeQuery();
            rs = (ResultSet) cs.getObject(1);
            while (rs.next()) {
                MerchantPaypalDto merchantPaypalDto = new MerchantPaypalDto();
                merchantPaypalDto.setId(rs.getInt("N_ID"))
                        .setPartnerId(rs.getInt("N_PARTNER_ID"))
                        .setPaypalMerchantId(rs.getString("S_PAYPAL_MERCHANT_ID"))
                        .setStatePaypal(rs.getString("S_STATE_PAYPAL"))
                        .setMerchantId(rs.getString("S_MERCHANT_ID"))
                        .setCreateDate(rs.getDate("D_CREATE"))
                        .setUpdateDate(rs.getDate("D_UPDATE"))
                        .setDesc(rs.getString("S_DESC"))
                        .setEmailConfirm(rs.getString("S_EMAIL_CONFIRM"))
                        .setPaymentsReceivable(rs.getString("S_PAYMENTS_RECEIVABLE"))
                        .setType(rs.getString("S_TYPE"));
                result.add(merchantPaypalDto);
            }
        } catch (Exception e) {
            logger.severe("DB get MerchantPaypalDao by Partner error: " + e.getMessage());
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
