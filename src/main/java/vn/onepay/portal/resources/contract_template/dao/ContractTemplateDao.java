package vn.onepay.portal.resources.contract_template.dao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import java.io.*;
import java.sql.*;

public class ContractTemplateDao extends Db{
    /* function search contract template */
    public static Map<String, Object> searchContractTemplate(JsonObject mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.search_contract_template(?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, mIn.getString("S_KEYWORD"));
            cs.setString(6, mIn.getString("S_TYPE"));
            cs.setString(7, mIn.getString("S_APPOVE"));
            cs.setString(8, mIn.getString("S_STATE"));
            cs.setInt(9, mIn.getInteger(PAGE));
            cs.setInt(10, mIn.getInteger(PAGE_SIZE));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB ONEPARTNER.PKG_CONTRACT_TEMPLATE.search_contract_template error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>(); // Parameterize the Map object
                    m.put("N_ID_VERSION", Util.getColumnInteger(rs, "N_ID"));
                    m.put("N_ID_TEMPLATE", Util.getColumnInteger(rs, "N_ID_TEMPLATE"));
                    m.put("S_VERSION", Util.getColumnString(rs, "S_VERSION"));
                    m.put("S_NAME", Util.getColumnString(rs, "S_NAME"));
                    m.put("S_CONTRACT_TYPE", Util.getColumnString(rs, "S_CONTRACT_TYPE"));
                    m.put("S_ACTIVE", Util.getColumnString(rs, "S_ACTIVE"));
                    m.put("S_APPROVE", Util.getColumnString(rs, "S_APPROVE"));
                    m.put("D_CREATE", Util.getColumnString(rs, "D_CREATE"));
                    m.put("D_UPDATE", Util.getColumnString(rs, "D_UPDATE"));
                    m.put("S_CREATE", Util.getColumnString(rs, "S_CREATE"));
                    m.put("S_UPDATE", Util.getColumnString(rs, "S_UPDATE"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }
    /* update contract template status by id version */
    public static Map<String, Object> updateContractTemplateApprove(int idVersion, String status) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_tmpl_approve(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idVersion);
            cs.setString(4, status);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_tmpl_approve error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error_state", error);
        result.put("nerror", nerror);
        return result;
    }
    /* get contract template by id version */
    public static Map<String, Object> getContractTemplateByIdVersion(int idVersion) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.get_contract_tmpl_by_id_ver(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, idVersion);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.get_contract_tmpl_by_id_ver error: {0}", error);
            } else {
                if (rs != null && rs.next()) {
                    result.put("N_ID_VERSION", Util.getColumnInteger(rs, "N_ID"));
                    result.put("N_ID_TEMPLATE", Util.getColumnInteger(rs, "N_ID_TEMPLATE"));
                    result.put("S_VERSION", Util.getColumnString(rs, "S_VERSION"));
                    result.put("S_NAME", Util.getColumnString(rs, "S_NAME"));
                    result.put("S_CONTRACT_TYPE", Util.getColumnString(rs, "S_CONTRACT_TYPE"));
                    result.put("S_ACTIVE", Util.getColumnString(rs, "S_ACTIVE"));
                    result.put("S_APPROVE", Util.getColumnString(rs, "S_APPROVE"));
                    result.put("D_CREATE", Util.getColumnString(rs, "D_CREATE"));
                    result.put("D_UPDATE", Util.getColumnString(rs, "D_UPDATE"));
                    result.put("S_CREATE", Util.getColumnString(rs, "S_CREATE"));
                    result.put("S_UPDATE", Util.getColumnString(rs, "S_UPDATE"));
                    result.put("S_FILE_VN", Util.getColumnString(rs, "S_FILE_VN"));
                    result.put("S_FILE_BILANGUAL", Util.getColumnString(rs, "S_FILE_BILANGUAL"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    /* get contract template history by id template */
    public static Map<String, Object> getContractTemplateHistoryByIdTemplate(int idTemplate,int page,int pageSize) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.get_contract_tmpl_his_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, idTemplate);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.get_contract_tmpl_his_by_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("N_ID_VERSION", Util.getColumnInteger(rs, "N_ID"));
                    m.put("N_ID_TEMPLATE", Util.getColumnInteger(rs, "N_ID_TEMPLATE"));
                    m.put("S_VERSION", Util.getColumnString(rs, "S_VERSION"));
                    m.put("S_NAME", Util.getColumnString(rs, "S_NAME"));
                    m.put("S_CONTRACT_TYPE", Util.getColumnString(rs, "S_CONTRACT_TYPE"));
                    m.put("S_ACTIVE", Util.getColumnString(rs, "S_ACTIVE"));
                    m.put("S_APPROVE", Util.getColumnString(rs, "S_APPROVE"));
                    m.put("D_CREATE", Util.getColumnString(rs, "D_CREATE"));
                    m.put("D_UPDATE", Util.getColumnString(rs, "D_UPDATE"));
                    m.put("S_CREATE", Util.getColumnString(rs, "S_CREATE"));
                    m.put("S_UPDATE", Util.getColumnString(rs, "S_UPDATE"));
                    m.put("D_APPROVE", Util.getColumnString(rs, "D_APPROVE"));
                    m.put("S_APPROVE_USER", Util.getColumnString(rs, "S_APPROVE_USER"));
                    m.put("S_FILE_VN", Util.getColumnString(rs, "S_FILE_VN"));
                    m.put("S_FILE_BILANGUAL", Util.getColumnString(rs, "S_FILE_BILANGUAL"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        return result;
    }

    /* get the file in B_TEMPLATE_FILE blob from DB by id version and write to file */
    public static Map<String, Object> getTemplateFileVNByIdVersion(int idVersion,String fileName,String type) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            if(type.equals("VN")){
                cs = con.prepareCall("select B_TEMPLATE_FILE from onepartner.TBL_CONTRACT_TMPL_VERSION where N_ID=?");
            }else{
                cs = con.prepareCall("select B_TEMPLATE_BILINGUAL from onepartner.TBL_CONTRACT_TMPL_VERSION where N_ID=?");
            }
            cs.setInt(1, idVersion);
            rs = cs.executeQuery();
            Blob blob = null;
            while (rs.next()) {
                if(type.equals("VN")){
                    blob = rs.getBlob("B_TEMPLATE_FILE");
                }else{
                    blob = rs.getBlob("B_TEMPLATE_BILINGUAL");
                }
            }
            logger.info("lob=" + blob);
            InputStream in = blob.getBinaryStream();
            // Define output file
            File file = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName);
            OutputStream outputStream = new FileOutputStream(new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName));

            result.put("filePath",file.getAbsolutePath());
            logger.info("filePath=" + file.getAbsolutePath());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int bytesRead;
            byte[] buffer = new byte[4096];
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.writeTo(outputStream);

            in.close();
            out.close();
            outputStream.close();
            error="OK";
            nerror=200;
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error=e.getMessage();
            nerror=300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* function update filed BLOB file templateVN and templateBilingual on version template*/
    public static Map<String, Object> updateTemplateFile(int idVersion,int idTemplate,String version,String email,byte[] bArrayVN,byte[] bArrayBilingual) {
        logger.log(Level.INFO, "updateTemplateFile idVersion="+idVersion+"|idTemplate="+idTemplate+"|version="+version);
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            InputStream inputStreamVN = null;
            if(bArrayVN!=null) inputStreamVN = new ByteArrayInputStream(bArrayVN);
            InputStream inputStreamBilingual = null;
            if(bArrayBilingual!=null) inputStreamBilingual = new ByteArrayInputStream(bArrayBilingual);
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_template_blob(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idVersion);
            cs.setInt(4, idTemplate);
            cs.setString(5, version);
            cs.setBinaryStream(6, inputStreamVN, bArrayVN!=null?bArrayVN.length:0);
            cs.setBinaryStream(7, inputStreamBilingual, bArrayBilingual!=null?bArrayBilingual.length:0);
            cs.setString(8, email);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            logger.log(Level.INFO, "updateTemplateFile error="+error+"|nerror="+nerror);
            if (nerror != 200 && nerror != 201) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_template_blob error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* update template active by id version*/
    public static Map<String, Object> updateTemplateActive(int idVersion,String status,String email) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_tmpl_active(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idVersion);
            cs.setString(4, status);
            cs.setString(5, email);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.update_contract_tmpl_active error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }



    /* for contract management */
    /* load list contract template by type */
    public static Map<String, Object> loadListContractTemplateByType(String type,String contractCode) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.load_contract_template_by_type(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type);
            cs.setString(5, contractCode);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.load_contract_template_by_type error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("N_ID_TEMPLATE", Util.getColumnInteger(rs, "N_ID"));
                    m.put("N_ID_VERSION", Util.getColumnInteger(rs, "N_ID_VERSION"));
                    m.put("S_NAME", Util.getColumnString(rs, "S_NAME"));
                    m.put("S_VERSION", Util.getColumnString(rs, "S_VERSION"));
                    m.put("S_CONTRACT_TYPE", Util.getColumnString(rs, "S_CONTRACT_TYPE"));
                    m.put("S_ACTIVE", Util.getColumnString(rs, "S_ACTIVE"));
                    m.put("S_APPROVE", Util.getColumnString(rs, "S_APPROVE"));
                    m.put("D_CREATE", Util.getColumnString(rs, "D_CREATE"));
                    m.put("D_UPDATE", Util.getColumnString(rs, "D_UPDATE"));
                    m.put("S_CREATE", Util.getColumnString(rs, "S_CREATE"));
                    m.put("S_UPDATE", Util.getColumnString(rs, "S_UPDATE"));
                    m.put("S_CONTRACT_CODE", Util.getColumnString(rs, "S_CONTRACT_CODE"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        return result;
    }

    /* save file location and url contract */
    public static Map<String, Object> saveFileLocationAndUrl(int idPartner,int idContract,int idContractVersion,String fileLocation,String fileUrl) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT_TEMPLATE.insert_contract_file_location(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idPartner);
            cs.setInt(4, idContract);
            cs.setInt(5, idContractVersion);
            cs.setString(6, fileLocation);
            cs.setString(7, fileUrl);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT_TEMPLATE.insert_contract_file_location error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
}
