package vn.onepay.portal.resources.contract_template;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.commons.util.Convert;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.contract_template.dao.ContractTemplateDao;
import vn.onepay.portal.resources.notification.dao.NotificationSystemDao;
import vn.onepay.portal.utils.ParamsPool;
import static vn.onepay.portal.Util.sendResponse;
import static java.nio.charset.StandardCharsets.UTF_8;

public class ContractTemplateHandler implements IConstants{
private static final Logger _LOGGER = Logger.getLogger(ContractTemplateHandler.class.getName());
     public static void searchContractTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                JsonObject mIn = new JsonObject();
                mIn.put("S_KEYWORD", request.getParam("S_KEYWORD") == null ? BLANK : request.getParam("S_KEYWORD"));
                mIn.put("S_TYPE", request.getParam("S_TYPE") == null ? BLANK : request.getParam("S_TYPE"));
                mIn.put("S_APPOVE", request.getParam("S_APPOVE") == null ? BLANK : request.getParam("S_APPOVE"));
                mIn.put("S_STATE", request.getParam("S_STATE") == null ? BLANK : request.getParam("S_STATE"));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> notiResult = ContractTemplateDao.searchContractTemplate(mIn);
                sendResponse(ctx, 200, notiResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* update group email state by id */
    public static void updateTemplateApprove(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idVersion = body.getValue("N_ID_VERSION") == null ? 0 : Convert.parseInt(body.getValue("N_ID_VERSION")+"", 0) ;
                String state = body.getString(S_STATE) == null ? "" : body.getString(S_STATE);
                if (idVersion!=0 && !state.equals("")) {
                    Map<String, Object> result = ContractTemplateDao.updateContractTemplateApprove(idVersion, state);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idGroup or state is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* get contract template by id version */
    public static void getContractTemplateByIdVersion(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idVersion = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idVersion!=0) {
                    Map<String, Object> result = ContractTemplateDao.getContractTemplateByIdVersion(idVersion);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idVersion is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* get contract template history by id template */
    public static void getContractTemplateHistoryByIdTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idTemplate = request.getParam("N_ID_TEMPLATE") == null ? 0 : Convert.parseInt(request.getParam("N_ID_TEMPLATE"),0);
                int page = request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0);
                int pageSize = request.getParam(PAGE_SIZE) == null ? 30 : Convert.parseInt(request.getParam(PAGE_SIZE),0);
                if (idTemplate!=0) {
                    Map<String, Object> result = ContractTemplateDao.getContractTemplateHistoryByIdTemplate(idTemplate,page,pageSize);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idTemplate is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadContractTemplateVN(RoutingContext ctx) {
        downloadContractTemplate(ctx,"VN");
    }
    public static void downloadContractTemplateBilingual(RoutingContext ctx) {
        downloadContractTemplate(ctx,"BILINGUAL");
    }
    /* download file b_lob from db by idVersion */
    public static void downloadContractTemplate(RoutingContext ctx,String type) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idVersion = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                String fileNameFull = request.getParam(S_FILE_NAME) == null ? BLANK : request.getParam(S_FILE_NAME);
                if (idVersion!=0) {
                    Map<String, Object> resultLoadFile = ContractTemplateDao.getTemplateFileVNByIdVersion(idVersion,fileNameFull,type);
                    if (resultLoadFile.get("nerror").equals(200)) {
                        String[] arr = fileNameFull.split("\\.");
                        String fileName = arr[0];
                        String fileExt = arr[1];

                        String filePath = resultLoadFile.get("filePath") == null ? BLANK : resultLoadFile.get("filePath").toString();
                        Path requestPath = FileSystems.getDefault().getPath(filePath).normalize();
                        FileSystem fs = ctx.vertx().fileSystem();
                        fs.exists(requestPath.toString(), ar -> {
                            if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                                Map<String, Object> data = new HashMap<>();
                                data.put(ParamsPool.PATH_FILE, requestPath.toString());
                                data.put(ParamsPool.FILE_NAME, fileName);
                                data.put(ParamsPool.FILE_EXT, fileExt);
                                try {
                                    data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                                } catch (IOException e) {
                                    ctx.fail(e);
                                }
                                ctx.response().setChunked(true);

                                String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                                String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                                String contentType = "application/octet-stream";
                                if (extFile.equals("zip")) {
                                    contentType = "application/zip";
                                }
                                ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                                ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                                ctx.response().sendFile(requestPath.toString(), result -> {
                                        if (result.succeeded()) {
                                            _LOGGER.log(Level.INFO,"Download Success");
                                        } else {
                                            _LOGGER.log(Level.INFO,"Download Fail");
                                        }
                                    });
                            } else {
                                ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* function update file BLOB file templateVN and templateBilingual on version template*/
    public static void uploadFileTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                String idVersion = request.getParam(ID) == null ? BLANK : request.getParam(ID);
                int idVersionInt = Convert.parseInt(idVersion, 0);
                int idTemplate = request.getParam("N_ID_TEMPLATE") == null ? 0 : Convert.parseInt(request.getParam("N_ID_TEMPLATE"),0);
                String version = request.getParam("S_VERSION") == null ? BLANK : request.getParam("S_VERSION");
                // version = generateRandomString();//sử dụng random string tạo ra version mới khi file template thay đổi trên version cũ (và không update trên version chờ duyệt)
                String email = request.getParam("S_EMAIL") == null ? BLANK : request.getParam("S_EMAIL");
                System.out.println("idVersion: "+idVersion+" version: "+version);
                byte[] bArray = null;
                byte[] bArrayBilingual = null;
                for (FileUpload fileUpload : fileUploadSet) {
                    String contentDisposition = fileUpload.name();
                    // String fileName = URLDecoder.decode(fileUpload.fileName(), UTF_8.toString());
                    if(contentDisposition.contains("VN")){
                        Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                        bArray = uploadedFile.getBytes();
                    }else if(contentDisposition.contains("BILINGUAL")){
                        Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                        bArrayBilingual = uploadedFile.getBytes();
                    }
                }
                if(bArray != null || bArrayBilingual != null){
                    Map<String, Object> result = ContractTemplateDao.updateTemplateFile(idVersionInt,idTemplate,version,email,bArray, bArrayBilingual);
                    sendResponse(ctx, 200, result);
                }else{
                    sendResponse(ctx, 400, "File is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final int LENGTH = 8;
    public static String generateRandomString() {
        long currentTimeMillis = System.currentTimeMillis();
        Random random = new Random(currentTimeMillis);
        StringBuilder sb = new StringBuilder(LENGTH);

        for (int i = 0; i < LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }

        return sb.toString();
    }

    /* update template active by id version*/
    public static void updateTemplateActive(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idVersion = body.getValue("N_ID_VERSION") == null ? 0 : body.getInteger("N_ID_VERSION");
                String state = body.getString("S_ACTIVE") == null ? "" : body.getString("S_ACTIVE");
                String email = body.getString("S_EMAIL") == null ? "" : body.getString("S_EMAIL");
                if (idVersion!=0 && !state.equals("")) {
                    Map<String, Object> result = ContractTemplateDao.updateTemplateActive(idVersion, state,email);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idGroup or state is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* for contract management */
    /* load list contract template by type */
    public static void loadListContractTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String type = request.getParam("S_TYPE") == null ? BLANK : request.getParam("S_TYPE");
                String contractCode = request.getParam("S_CONTRACT_CODE") == null ? BLANK : request.getParam("S_CONTRACT_CODE");
                Map<String, Object> result = ContractTemplateDao.loadListContractTemplateByType(type,contractCode);
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* save file location and url contract */
    public static void saveFileLocationContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                String contractUrl = body.getString("S_CONTRACT_URL") == null ? BLANK : body.getString("S_CONTRACT_URL");
                String contractLocation = body.getString("S_CONTRACT_LOCATION") == null ? BLANK : body.getString("S_CONTRACT_LOCATION");
                
                if(contractUrl.equals(BLANK) || contractLocation.equals(BLANK)){
                    sendResponse(ctx, 201, "contractUrl or contractLocation is null");
                    return;
                }
                Map<String, String> params = extractParamsFromUrl(contractUrl);

                Map<String, Object> result = ContractTemplateDao.saveFileLocationAndUrl(Convert.parseInt(params.get("idPartner"), 0),Convert.parseInt(params.get("contractId"), 0)
                    ,Convert.parseInt(params.get("v"), 0),contractLocation,contractUrl);
                if(!result.get("nerror").equals(200)){
                    sendResponse(ctx, 202, result);
                    return;
                }
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static Map<String, String> extractParamsFromUrl(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String path = uri.getPath();
        String[] pathSegments = path.split("/");
        String idPartner = pathSegments[pathSegments.length - 1];

        String query = uri.getQuery();
        String[] queryParams = query.split("&");

        Map<String, String> params = new HashMap<>();
        params.put("idPartner", idPartner);

        for (String param : queryParams) {
            String[] keyValue = param.split("=");
            if (keyValue.length == 2) {
                params.put(keyValue[0], keyValue[1]);
            }
        }

        return params;
    }

    private static final String PATH_FILE_PDF = "/root/Projects/java/one-projects/iportal-service/src/test/java/HD13_TH32.pdf";
    /* download contract pdf */
    public static void downloadContractPdf(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                
                HttpServerRequest request = ctx.request();
                int idVersion = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                String fileNameFull = request.getParam(S_FILE_NAME) == null ? BLANK : request.getParam(S_FILE_NAME);
                // if (idVersion != 0) {
                    // Đọc file PDF từ đường dẫn cụ thể
                    Path pdfPath = Paths.get(PATH_FILE_PDF);
                    byte[] fileContent = Files.readAllBytes(pdfPath);
                    String fileName = pdfPath.getFileName().toString();

                    // Thiết lập phản hồi HTTP
                    ctx.response()
                            .putHeader(HttpHeaders.CONTENT_TYPE, "application/pdf")
                            .putHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                            .putHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileContent.length))
                            .write(Buffer.buffer(fileContent))
                            .end();
                // } else {
                //     sendResponse(ctx, 400, "idVersion is null or invalid");
                // }



                // String filePath = PATH_FILE_PDF;
                //         Path requestPath = FileSystems.getDefault().getPath(filePath).normalize();
                //         FileSystem fs = ctx.vertx().fileSystem();
                //         fs.exists(requestPath.toString(), ar -> {
                //             if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                //                 Map<String, Object> data = new HashMap<>();
                //                 data.put(ParamsPool.PATH_FILE, requestPath.toString());
                //                 data.put(ParamsPool.FILE_NAME, fileName);
                //                 data.put(ParamsPool.FILE_EXT, ".pdf");
                //                 try {
                //                     data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                //                 } catch (IOException e) {
                //                     ctx.fail(e);
                //                 }
                //                 ctx.response().setChunked(true);

                //                 String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                //                 String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                //                 String contentType = "application/pdf";
                //                 if (extFile.equals("zip")) {
                //                     contentType = "application/zip";
                //                 }
                //                 ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                //                 ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                //                 ctx.response().sendFile(requestPath.toString(), result -> {
                //                         if (result.succeeded()) {
                //                             _LOGGER.log(Level.INFO,"Download Success");
                //                         } else {
                //                             _LOGGER.log(Level.INFO,"Download Fail");
                //                         }
                //                     });
                //             } else {
                //                 ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                //             }
                //         });
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
