package vn.onepay.portal.resources.dispute.dto;

import java.sql.Timestamp;

public class DisputeDto {
    private int id;
    private String disputeStatus;
    private Timestamp disputeDate;
    private Timestamp sentToMerchantDate;
    private Timestamp dueDate;
    private Timestamp lastResponse;
    private String partnerName;
    private String merchantId;
    private String merchantName;
    private String mcc;
    private String transactionId;
    private String orderReference;
    private String merchantTransactionReference;
    private String channel;
    private String acquirer;
    private String cardType;
    private String cardNumber;
    private String authorisationCode;
    private String transactionType;
    private Double transactionAmount;
    private Timestamp transactionDate;
    private Double disputeAmount;
    private String outcome;
    private String cardIssure;
    private String binCountry;
    private String ipAddress;
    private String ipCountry;
    private String fraud;
    private String cscResponse;
    private String authenticationState;
    private String responseCode;
    private String transactionStatus;
    private int parentId;
    private String disputeSender;
    private String onepayPic;
    private String sendDisputeTo;
    private String merchantRespond;
    private String businessCategory;
    private String merchantGroup;
    private String disputeStage;
    private String disputeReason;
    private String disputeCode;
    private String disputeCurrency;
    private String refNumber;
    private String note;
    private String operator;
    private String paygate;
    private String disputeDateStr;
    private String dueDateStr;
    private String nFileID;

    public DisputeDto() {

    }

    public DisputeDto(int id, String disputeStatus, Timestamp disputeDate, Timestamp sentToMerchantDate,
                      Timestamp dueDate, Timestamp lastResponse, String partnerName, String merchantId, String merchantName,
                      String mcc, String transactionId, String orderReference, String merchantTransactionReference,
                      String channel, String acquirer, String cardType, String cardNumber, String authorisationCode,
                      String transactionType, Double transactionAmount, Timestamp transactionDate, Double disputeAmount,
                      String outcome, String cardIssure, String binCountry, String ipAddress, String ipCountry, String fraud,
                      String cscResponse, String authenticationState, String responseCode, String transactionStatus, int parentId,
                      String disputeSender, String onepayPic, String sendDisputeTo, String merchantRespond,
                      String businessCategory, String merchantGroup, String disputeStage, String disputeReason,
                      String disputeCode, String disputeCurrency, String refNumber, String note, String operator, String nFileID) {
        this.id = id;
        this.disputeStatus = disputeStatus;
        this.disputeDate = disputeDate;
        this.sentToMerchantDate = sentToMerchantDate;
        this.dueDate = dueDate;
        this.lastResponse = lastResponse;
        this.partnerName = partnerName;
        this.merchantId = merchantId;
        this.merchantName = merchantName;
        this.mcc = mcc;
        this.transactionId = transactionId;
        this.orderReference = orderReference;
        this.merchantTransactionReference = merchantTransactionReference;
        this.channel = channel;
        this.acquirer = acquirer;
        this.cardType = cardType;
        this.cardNumber = cardNumber;
        this.authorisationCode = authorisationCode;
        this.transactionType = transactionType;
        this.transactionAmount = transactionAmount;
        this.transactionDate = transactionDate;
        this.disputeAmount = disputeAmount;
        this.outcome = outcome;
        this.cardIssure = cardIssure;
        this.binCountry = binCountry;
        this.ipAddress = ipAddress;
        this.ipCountry = ipCountry;
        this.fraud = fraud;
        this.cscResponse = cscResponse;
        this.authenticationState = authenticationState;
        this.responseCode = responseCode;
        this.transactionStatus = transactionStatus;
        this.parentId = parentId;
        this.disputeSender = disputeSender;
        this.onepayPic = onepayPic;
        this.sendDisputeTo = sendDisputeTo;
        this.merchantRespond = merchantRespond;
        this.businessCategory = businessCategory;
        this.merchantGroup = merchantGroup;
        this.disputeStage = disputeStage;
        this.disputeReason = disputeReason;
        this.disputeCode = disputeCode;
        this.disputeCurrency = disputeCurrency;
        this.refNumber = refNumber;
        this.note = note;
        this.operator = operator;
        this.nFileID = nFileID;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDisputeStatus() {
        return disputeStatus;
    }

    public void setDisputeStatus(String disputeStatus) {
        this.disputeStatus = disputeStatus;
    }

    public Timestamp getDisputeDate() {
        return disputeDate;
    }

    public void setDisputeDate(Timestamp disputeDate) {
        this.disputeDate = disputeDate;
    }

    public Timestamp getSentToMerchantDate() {
        return sentToMerchantDate;
    }

    public void setSentToMerchantDate(Timestamp sentToMerchantDate) {
        this.sentToMerchantDate = sentToMerchantDate;
    }

    public Timestamp getDueDate() {
        return dueDate;
    }

    public void setDueDate(Timestamp dueDate) {
        this.dueDate = dueDate;
    }

    public Timestamp getLastResponse() {
        return lastResponse;
    }

    public void setLastResponse(Timestamp lastResponse) {
        this.lastResponse = lastResponse;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOrderReference() {
        return orderReference;
    }

    public void setOrderReference(String orderReference) {
        this.orderReference = orderReference;
    }

    public String getMerchantTransactionReference() {
        return merchantTransactionReference;
    }

    public void setMerchantTransactionReference(String merchantTransactionReference) {
        this.merchantTransactionReference = merchantTransactionReference;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getAuthorisationCode() {
        return authorisationCode;
    }

    public void setAuthorisationCode(String authorisationCode) {
        this.authorisationCode = authorisationCode;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public Timestamp getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Timestamp transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Double getDisputeAmount() {
        return disputeAmount;
    }

    public void setDisputeAmount(Double disputeAmount) {
        this.disputeAmount = disputeAmount;
    }

    public String getOutcome() {
        return outcome;
    }

    public void setOutcome(String outcome) {
        this.outcome = outcome;
    }

    public String getCardIssure() {
        return cardIssure;
    }

    public void setCardIssure(String cardIssure) {
        this.cardIssure = cardIssure;
    }

    public String getBinCountry() {
        return binCountry;
    }

    public void setBinCountry(String binCountry) {
        this.binCountry = binCountry;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getIpCountry() {
        return ipCountry;
    }

    public void setIpCountry(String ipCountry) {
        this.ipCountry = ipCountry;
    }

    public String getFraud() {
        return fraud;
    }

    public void setFraud(String fraud) {
        this.fraud = fraud;
    }

    public String getCscResponse() {
        return cscResponse;
    }

    public void setCscResponse(String cscResponse) {
        this.cscResponse = cscResponse;
    }

    public String getAuthenticationState() {
        return authenticationState;
    }

    public void setAuthenticationState(String authenticationState) {
        this.authenticationState = authenticationState;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(String transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public String getDisputeSender() {
        return disputeSender;
    }

    public void setDisputeSender(String disputeSender) {
        this.disputeSender = disputeSender;
    }

    public String getOnepayPic() {
        return onepayPic;
    }

    public void setOnepayPic(String onepayPic) {
        this.onepayPic = onepayPic;
    }

    public String getSendDisputeTo() {
        return sendDisputeTo;
    }

    public void setSendDisputeTo(String sendDisputeTo) {
        this.sendDisputeTo = sendDisputeTo;
    }

    public String getMerchantRespond() {
        return merchantRespond;
    }

    public void setMerchantRespond(String merchantRespond) {
        this.merchantRespond = merchantRespond;
    }

    public String getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    public String getMerchantGroup() {
        return merchantGroup;
    }

    public void setMerchantGroup(String merchantGroup) {
        this.merchantGroup = merchantGroup;
    }

    public String getDisputeStage() {
        return disputeStage;
    }

    public void setDisputeStage(String disputeStage) {
        this.disputeStage = disputeStage;
    }

    public String getDisputeReason() {
        return disputeReason;
    }

    public void setDisputeReason(String disputeReason) {
        this.disputeReason = disputeReason;
    }

    public String getDisputeCode() {
        return disputeCode;
    }

    public void setDisputeCode(String disputeCode) {
        this.disputeCode = disputeCode;
    }

    public String getDisputeCurrency() {
        return disputeCurrency;
    }

    public void setDisputeCurrency(String disputeCurrency) {
        this.disputeCurrency = disputeCurrency;
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getDisputeDateStr() {
        return disputeDateStr;
    }

    public void setDisputeDateStr(String disputeDateStr) {
        this.disputeDateStr = disputeDateStr;
    }

    public String getDueDateStr() {
        return dueDateStr;
    }

    public void setDueDateStr(String dueDateStr) {
        this.dueDateStr = dueDateStr;
    }

    public String getnFileID() {
        return nFileID;
    }

    public void setnFileID(String nFileID) {
        this.nFileID = nFileID;
    }
}
