package vn.onepay.portal.resources.dispute.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.client.DisputeClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.dispute.dao.DisputeDao;
import vn.onepay.portal.resources.dispute.dto.DisputeParam;
import vn.onepay.portal.resources.dispute.dto.TransactionList;
import vn.onepay.portal.resources.dispute.job.Sftp;
import vn.onepay.portal.resources.reconciliation.cdrFileManagement.CDRFileManageDao;
import vn.onepay.portal.resources.reconciliation.cdrFileManagement.dto.CDRFileDto;
import vn.onepay.portal.resources.service_suport.trans_management.dao.SSTransManagementDao;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.UserProfileDto;
import java.io.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.io.FileUtils;
import java.nio.file.Files;
import io.vertx.core.Future;
import io.vertx.core.MultiMap;
import io.vertx.core.Promise;
import com.google.gson.Gson;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.file.FileSystem;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.Objects;
import vn.onepay.portal.Util;

import static vn.onepay.portal.Util.sendResponse;

public class DisputeHandler implements IConstants {
    private static final Logger logger = Logger.getLogger(DisputeHandler.class.getName());
    private static final Gson gson = new Gson();

    public static void createDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject jRequest = ctx.getBodyAsJson();
            // String body = ctx.getBodyAsString();
            // String keys = ctx.request().getParam(KEYS);
            JsonObject operator = jRequest.getJsonObject("operator");
            JsonArray listDisputeData = jRequest.getJsonArray("listDisputeData");
            String department = jRequest.getString("department");

            Integer operatorId = Integer.valueOf(operator.getString("operatorId"));
            String operatorName = operator.getString("operatorName");
            try {
                Integer result = 0;
                JsonObject res = new JsonObject();
                String statusMessage = null;
                List<TransactionList> transactionList = new ArrayList<>();

                for (int i = 0; i < listDisputeData.getList().size(); i++) {
                    JsonObject disputeData = listDisputeData.getJsonObject(i);
                    String key = disputeData.getString("key");
                    if (key.equals(null))
                        key = "";
                    String disputeCurrency = disputeData.getString("disputeCurrency");
                    Double disputeAmount = Double.parseDouble(disputeData.getString("disputeAmount"));

                    List<TransactionList> getByKey = SSTransManagementDao.getGeneralByKeysV2(operatorId, operatorName, key).getTransactionList();
                    if (getByKey == null) {
                        throw IErrors.VALIDATION_ERROR;
                    }

                    for (TransactionList item : getByKey) {
                        item.setDisputeCurrency(disputeCurrency);
                        item.setDisputeAmount(disputeAmount);
                        item.setDepartment(department);
                    }

                    transactionList.addAll(getByKey);
                }

                DisputeParam disputeParam = new DisputeParam();
                disputeParam.setTransactionList(transactionList);
                result = DisputeClient.createDispute(disputeParam) > 0 ? 200 : 500;
                statusMessage = result > 0 ? "Successful" : "Failed";
                res.put("status", statusMessage);
                sendResponse(ctx, result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map mIn = new HashMap();
                if (request.getParam(FROM_DATE) == null || request.getParam(FROM_DATE).trim().isEmpty()
                        || request.getParam(TO_DATE) == null || request.getParam(TO_DATE).trim().isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                Integer userID = ctx.get(X_USER_ID);
                UserProfileDto userProfile = UserDao.getProfileInfo(userID);
                String outsource = userProfile.getFunctions().stream().filter(s -> {return s.equals(SS_OUTSOURCE_USER);}).findAny().orElse(null);
                if(null != outsource && !outsource.isEmpty()){
                    logger.info(() -> String.format("userId=%d name=%s is %s", userID, userProfile.getName(), SS_OUTSOURCE_USER));
                    mIn.put(USER_ID, userID);
                }
                String roleName = Config.getString("role_merchant.role_id", "");
                if (userProfile.getRoleList().contains(roleName)) {
                    String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                    String merchantFE = request.getParam(MERCHANT_ID);
                    StringBuilder merchants = new StringBuilder("");
                    if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                        String[] merchantFEs = merchantFE.split(",");
                        String[] configuredMerchants = merchantsConf.split(",");
                        List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                        if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                            merchants.append("abc");
                        } else {
                            merchants.append(String.join(",", merchantSearchs));
                        }

                    } else if (!merchantsConf.isBlank() && 
                        (("contains".equalsIgnoreCase(request.getParam(FILTER_TYPE)) && !request.getParam(MERCHANT_ID).isBlank() || "equals".equalsIgnoreCase(request.getParam(FILTER_TYPE))))) {
                        merchants.append(merchantsConf);
                    } else {
                        merchants.append(merchantFE);
                    }
                    mIn.put(MERCHANT_ID, merchants);
                    mIn.put(MERCHANT_ROLE, merchantsConf);
                } else {
                    mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID));
                }
                mIn.put(FROM_DATE, request.getParam(FROM_DATE).trim());
                mIn.put(TO_DATE, request.getParam(TO_DATE).trim());
                mIn.put(DISPUTE_STATUS, request.getParam(DISPUTE_STATUS));
                mIn.put(TRANSACTION_ID, request.getParam(TRANSACTION_ID));
                mIn.put(ORDER_REF, request.getParam(ORDER_REF));
                mIn.put(MERCHANT_TRANSACTION_REF, request.getParam(MERCHANT_TRANSACTION_REF));
                mIn.put(CHANNEL, request.getParam(CHANNEL));
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID));
                mIn.put(ONEPAY_PIC, request.getParam(ONEPAY_PIC));
                mIn.put(CARD_TYPE, Util.appendSearchOldName(request.getParam(CARD_TYPE)));
                mIn.put(CARD_NUMBER, request.getParam(CARD_NUMBER));
                mIn.put(AUTHORISATION_CODE, request.getParam(AUTHORISATION_CODE));
                mIn.put(DISPUTE_TRANS_CURRENCY, request.getParam(DISPUTE_TRANS_CURRENCY));
                mIn.put(DISPUTE_CURRENCY, request.getParam(DISPUTE_CURRENCY));
                mIn.put(DISPUTE_REASON, request.getParam(DISPUTE_REASON));
                mIn.put(DISPUTE_CODE, request.getParam(DISPUTE_CODE));
                mIn.put(DISPUTE_STAGE, request.getParam(DISPUTE_STAGE));
                mIn.put(OUTCOME, request.getParam(OUTCOME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(PAGE_SIZE,
                        request.getParam(PAGE_SIZE) == null ? 100 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put(FILTER_TYPE, request.getParam(FILTER_TYPE));
                mIn.put(DEPARTMENT, request.getParam(DEPARTMENT));
                mIn.put(MERCHANT_CHANNEL, request.getParam(MERCHANT_CHANNEL));
                mIn.put(PARTNER_NAME, request.getParam(PARTNER_NAME));
                mIn.put(TRANSACTION_STATE, request.getParam(TRANSACTION_STATE));
                mIn.put(TRANSACTION_TYPE, request.getParam(TRANSACTION_TYPE));
                mIn.put(FRAUD_INVES, request.getParam(FRAUD_INVES));

                // synchronize data search dispute
                synchronizeDataDispute();

                sendResponse(ctx, 200, DisputeClient.searchDispute(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DISPUTE: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static void synchronizeDataDispute() {
        try {
            JsonObject jsonDispute = OneSchedClient.synchronize(Config.getString("onesched-service.sync_data_search_dispute", ""), "0m");
            logger.log(Level.INFO, "SEARCH DISPUTE SYNC: " + jsonDispute.encode());
        } catch (Exception e) {
            logger.log(Level.WARNING, "SEARCH DISPUTE SYNC DATA ERROR: ", e);
        } 
    }

    public static void searchDisputeCase(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map mIn = new HashMap();
                if (request.getParam(FROM_DATE) == null || request.getParam(FROM_DATE).trim().isEmpty()
                        || request.getParam(TO_DATE) == null || request.getParam(TO_DATE).trim().isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                mIn.put(FROM_DATE, request.getParam(FROM_DATE).trim());
                mIn.put(TO_DATE, request.getParam(TO_DATE).trim());
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID));
                mIn.put(USER_ID, request.getParam(USER_ID));
                mIn.put(FILE_NAME, request.getParam(FILE_NAME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(PAGE_SIZE,
                        request.getParam(PAGE_SIZE) == null ? 100 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                sendResponse(ctx, 200, DisputeClient.searchDisputeCase(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DISPUTE: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchDisputeRefundAmount(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map mIn = new HashMap();
                if (request.getParam(FROM_DATE) == null || request.getParam(FROM_DATE).trim().isEmpty()
                        || request.getParam(TO_DATE) == null || request.getParam(TO_DATE).trim().isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                mIn.put(FROM_DATE, request.getParam(FROM_DATE).trim());
                mIn.put(TO_DATE, request.getParam(TO_DATE).trim());
                mIn.put(DISPUTE_STATUS, request.getParam(DISPUTE_STATUS));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID));
                mIn.put(TRANSACTION_ID, request.getParam(TRANSACTION_ID));
                mIn.put(ORDER_REF, request.getParam(ORDER_REF));
                mIn.put(MERCHANT_TRANSACTION_REF, request.getParam(MERCHANT_TRANSACTION_REF));
                mIn.put(CHANNEL, request.getParam(CHANNEL));
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID));
                mIn.put(CARD_TYPE, request.getParam(CARD_TYPE));
                mIn.put(CARD_NUMBER, request.getParam(CARD_NUMBER));
                mIn.put(AUTHORISATION_CODE, request.getParam(AUTHORISATION_CODE));
                mIn.put(TRANS_AMOUNT, request.getParam(TRANS_AMOUNT));
                mIn.put(DISPUTE_AMOUNT, request.getParam(DISPUTE_AMOUNT));
                mIn.put(DISPUTE_REASON, request.getParam(DISPUTE_REASON));
                mIn.put(DISPUTE_CODE, request.getParam(DISPUTE_CODE));
                mIn.put(DISPUTE_STAGE, request.getParam(DISPUTE_STAGE));
                mIn.put(OUTCOME, request.getParam(OUTCOME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(PAGE_SIZE,
                        request.getParam(PAGE_SIZE) == null ? 100 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put(FILTER_TYPE, request.getParam(FILTER_TYPE));
                Integer userID = ctx.get(X_USER_ID);
                UserProfileDto userProfile = UserDao.getProfileInfo(userID);
                String outsource = userProfile.getFunctions().stream().filter(s -> {return s.equals(SS_OUTSOURCE_USER);}).findAny().orElse(null);
                if(null != outsource && !outsource.isEmpty()){
                    logger.info(() -> String.format("userId=%d name=%s is %s", userID, userProfile.getName(), SS_OUTSOURCE_USER));
                    mIn.put(USER_ID, userID);
                }
                sendResponse(ctx, 200, DisputeClient.searchDisputeRefundAmount(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DISPUTE REFUND AMOUNT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchDisputeRefundByTrans(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String transIds = ctx.request().getParam(TRANSACTIONIDS);
                String paygate = ctx.request().getParam(PAYGATE);

                // synchronize data tb_dispute_refund
                logger.log(Level.INFO, () -> String.format("SYNCHRONIZE REFUND AMOUNT, transId [%s], paygate [%s]", transIds, paygate));
                synchronizeDataDispute();

                sendResponse(ctx, 200, DisputeClient.searchDisputeRefundByTrans(transIds, paygate));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET REFUND AMOUNT", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    // public static void searchDisputeRefundByRef(RoutingContext ctx) {
    // ctx.vertx().executeBlocking(future -> {
    // try {
    // HttpServerRequest request = ctx.request();
    // String reference = ctx.request().getParam("refs");
    // String type = ctx.request().getParam("type");
    // sendResponse(ctx, 200, DisputeClient.searchDisputeRefundByRef(reference,type));
    // } catch (Exception e) {
    // logger.log(Level.WARNING, "GET REFUND AMOUNT", e);
    // ctx.fail(e);
    // }
    // }, false, null);
    // }

    public static void searchHistoryByTransIds(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String transIds = ctx.request().getParam(TRANSACTIONIDS);
                sendResponse(ctx, 200, DisputeClient.searchHistoryByTransIds(transIds));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DISPUTE: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            Integer result = 0;
            int userId = ctx.get(X_USER_ID);
            body.put("operatorId", Integer.toString(userId));
            try {
                result = DisputeClient.updateDispute(body);
                if (result == 1) {
                    JsonObject res = new JsonObject();
                    int statusCode = 200;
                    String statusMessage = "Successful";
                    res.put("code", statusCode);
                    res.put("status", statusMessage);
                    sendResponse(ctx, statusCode, res);
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.VALIDATION_ERROR;
            }
        }, false, null);
    }

    public static void deleteDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            Integer result = 0;
            try {
                result = DisputeClient.deleteDispute(body);
                if (result == 1) {
                    JsonObject res = new JsonObject();
                    int statusCode = 200;
                    String statusMessage = "Successful";
                    res.put("code", statusCode);
                    res.put("status", statusMessage);
                    sendResponse(ctx, statusCode, res);
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.VALIDATION_ERROR;
            }
        }, false, null);
    }

    public static void sendDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            Integer result = 0;
            int userId = ctx.get(X_USER_ID);
            body.put("operatorId", Integer.toString(userId));
            try {
                result = DisputeClient.sendDispute(body);
                if (result == 1) {
                    JsonObject res = new JsonObject();
                    int statusCode = 200;
                    String statusMessage = "Successful";
                    res.put("code", statusCode);
                    res.put("status", statusMessage);
                    sendResponse(ctx, statusCode, res);
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.VALIDATION_ERROR;
            }
        }, false, null);
    }

    public static void getOperator(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String transIds = ctx.request().getParam(TRANSACTIONIDS);
                sendResponse(ctx, 200, DisputeClient.getOperator());
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST BUSINESS CATEGORY: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAllOnePayPics(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String role = request.getParam(ROLE);
                if (role == null || role.isEmpty()) {
                    throw IErrors.VALIDATION_ERROR;
                }
                sendResponse(ctx, 200, DisputeClient.getAllOnePayPics(role));
            } catch (Exception e) {
                logger.log(Level.WARNING, "getAllOnePayPics: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getOnePartnerEmailList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String result = "";
            String channel = request.getParam(CHANNEL) == null ? BLANK : request.getParam(CHANNEL);
            int partnerId = request.getParam(PARTNERID) == null ? 0 : Integer.parseInt(request.getParam(PARTNERID));
            try {
                result = DisputeDao.getOnePartnerEmailList(partnerId, channel);
                // if (result != "") {
                JsonObject res = new JsonObject();
                int statusCode = 200;
                String statusMessage = "Successful";
                res.put("code", statusCode);
                res.put("status", statusMessage);
                res.put("emailList", result);
                sendResponse(ctx, statusCode, res);
                // } else {
                // throw IErrors.VALIDATION_ERROR;
                // }
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }

    // on off email
    // public static void sendOnDisputeEmail(RoutingContext ctx) {
    //     ctx.vertx().executeBlocking(future -> {
    //         try {
    //             JsonObject body = ctx.getBodyAsJson();
    //             DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
    //             HttpServerRequest request = ctx.request();
    //             String merchantId = body.getString("merchantId") == null ? "" : body.getString("merchantId");
    //             String sendDisputeTo = body.getString("sendDisputeTo") == null ? "" : body.getString("sendDisputeTo");
    //             String merchantName = body.getString("merchantName") == null ? "" : body.getString("merchantName");
    //             String orderReference = body.getString("orderReference") == null ? ""
    //                     : body.getString("orderReference");
    //             String authorisationCode = body.getString("authorisationCode") == null ? ""
    //                     : body.getString("authorisationCode");
    //             String channel = body.getString("channel") == null ? "" : body.getString("channel");
    //             String dueDate = body.getString("dueDate") == null ? "" : body.getString("dueDate");
    //             String merchantTxnRef = body.getString("merchantTxnRef") == null ? ""
    //                     : body.getString("merchantTxnRef");
    //             String cardNumber = body.getString("cardNumber") == null ? "" : body.getString("cardNumber");
    //             String transDate = body.getString("transDate") == null ? "" : body.getString("transDate");
    //             String transAmount = body.getString("transAmount") == null ? "" : body.getString("transAmount");
    //             String disputeAmount = body.getString("disputeAmount") == null ? "" : body.getString("disputeAmount");
    //             String disputeCode = body.getString("disputeCode") == null ? "" : body.getString("disputeCode");
    //             String disputeStage = body.getString("disputeStage") == null ? "" : body.getString("disputeStage");
    //             String disputeCurrency = body.getString("disputeCurrency") == null ? ""
    //                     : body.getString("disputeCurrency");
    //             int partnerId = request.getParam("partnerId") == null ? 0
    //                     : Integer.parseInt(request.getParam("partnerId"));
    //             String note = body.getString("note") == null ? "" : body.getString("note");
    //             String type = "";
    //             if (body.getString("type") == null) {
    //                 if (body.getString("disputeStatus") != null
    //                         && "dispute_reminded".equals(body.getString("disputeStatus"))) {
    //                     type = "remindDisputes";
    //                 } else if (body.getString("disputeStatus") != null
    //                         && "need_merchant_response".equals(body.getString("disputeStatus"))) {
    //                     type = "sendDisputes";
    //                 }
    //             } else {
    //                 type = body.getString("type");
    //             }
    //             // if User choose Arbitration for DisputeStagee
    //             String Subject = "";
    //             String contentUser = "";
    //             if (true) {
    //                 String bcc = "";
    //                 // String tittleUser = "OnePay - Thông báo Ngân hàng " + bank_name + " đã hoạt
    //                 // động bình thường / " + bank_name + " system is back on";
    //                 if ("sendDispute".equals(type) || "sendDisputes".equals(type)) {
    //                     if ("sendDispute".equals(type)) {
    //                         Subject = generateSubject("sendDispute", channel, disputeStage, merchantName,
    //                                 orderReference,
    //                                 authorisationCode);
    //                         sendDisputeTo = generateSendDisputeTo("sendDispute", sendDisputeTo);
    //                         contentUser = generateContentUser("sendDispute", channel, merchantName, dueDate, merchantId,
    //                                 orderReference,
    //                                 merchantTxnRef, cardNumber, transDate, authorisationCode, transAmount,
    //                                 disputeCurrency, disputeAmount, disputeCode, disputeStage, note);
    //                     } else {
    //                         JsonObject disputeByTransactionIds = DisputeClient
    //                                 .searchDisputeByTransIds(body.getString("transactionIds"));
    //                         JsonArray list = new JsonArray();
    //                         list = disputeByTransactionIds.getJsonArray("list");
    //                         for (int i = 0; i < list.size(); i++) {
    //                             JsonObject jsonObject = list.getJsonObject(i);
    //                             Subject = generateSubject("sendDisputes", jsonObject.getString("channel"),
    //                                     jsonObject.getString("disputeStage"), jsonObject.getString("merchantName"),
    //                                     jsonObject.getString("orderReference"),
    //                                     jsonObject.getString("authorisationCode"));
    //                             sendDisputeTo = generateSendDisputeTo("sendDisputes",
    //                                     jsonObject.getString("sendDisputeTo"));
    //                             contentUser = generateContentUser("sendDisputes", channel,
    //                                     jsonObject.getString("merchantName"),
    //                                     jsonObject.getString("dueDate"), jsonObject.getString("merchantId"),
    //                                     jsonObject.getString("orderReference"),
    //                                     jsonObject.getString("merchantTxnRef"), jsonObject.getString("cardNumber"),
    //                                     jsonObject.getString("transDate"), jsonObject.getString("authorisationCode"),
    //                                     jsonObject.getString("transAmount"),
    //                                     jsonObject.getString("disputeCurrency"), jsonObject.getString("disputeAmount"),
    //                                     jsonObject.getString("disputeCode"), jsonObject.getString("disputeStage"),
    //                                     jsonObject.getString("note"));
    //                         }
    //                     }
    //                 } else if ("remindDisputes".equals(type)) {
    //                     JsonObject disputeByTransactionIds = DisputeClient
    //                             .searchDisputeByTransIds(body.getString("transactionIds"));
    //                     JsonArray list = new JsonArray();
    //                     list = disputeByTransactionIds.getJsonArray("list");
    //                     for (int i = 0; i < list.size(); i++) {
    //                         JsonObject jsonObject = list.getJsonObject(i);
    //                         Subject = generateSubject("remindDisputes", jsonObject.getString("channel"),
    //                                 jsonObject.getString("disputeStage"), jsonObject.getString("merchantName"),
    //                                 jsonObject.getString("orderReference"),
    //                                 jsonObject.getString("authorisationCode"));
    //                         sendDisputeTo = generateSendDisputeTo("remindDisputes",
    //                                 jsonObject.getString("sendDisputeTo"));
    //                         contentUser = generateContentUser("remindDisputes", channel,
    //                                 jsonObject.getString("merchantName"),
    //                                 jsonObject.getString("dueDate"), jsonObject.getString("merchantId"),
    //                                 jsonObject.getString("orderReference"),
    //                                 jsonObject.getString("merchantTxnRef"), jsonObject.getString("cardNumber"),
    //                                 jsonObject.getString("transDate"), jsonObject.getString("authorisationCode"),
    //                                 jsonObject.getString("transAmount"),
    //                                 jsonObject.getString("disputeCurrency"), jsonObject.getString("disputeAmount"),
    //                                 jsonObject.getString("disputeCode"), jsonObject.getString("disputeStage"),
    //                                 jsonObject.getString("note"));
    //                     }
    //                 }

    //                 // Email 4: Advise Dispute - Thông báo tư vấn gửi đến merchant
    //                 MailUtil.sendMailWithBCC(sendDisputeTo, "", "", Subject, contentUser, "html");

    //                 // if User choose PreArbitration for DisputeStage
    //             } else {
    //                 logger.info(ctx.get(REQUEST_UUID) + ": " + "No email user found");
    //             }
    //             JsonObject res = new JsonObject();
    //             int statusCode = 200;
    //             String statusMessage = "Successful";
    //             res.put("code", statusCode);
    //             res.put("status", statusMessage);
    //             sendResponse(ctx, statusCode, res);
    //         } catch (Exception e) {
    //             logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "UPDATE ONSITE ERROR: ", e);
    //             ctx.fail(e);
    //         }

    //     }, false, null);
    // }

    public static String generateSendDisputeTo(String type, String sendDisputeTo) {
        return sendDisputeTo;
    }

    public static String generateSubject(String type, String channel, String disputeStage, String merchantName,
            String orderReference, String authorisationCode) {
        String Subject = "";
        if (type.equals("sendDispute") || type.equals("sendDisputes")) {
            if (disputeStage.equals("Pre-Arbitration")) {
                Subject = merchantName + " / " + orderReference + " / " + authorisationCode + " / "
                        + "Pre-Arbitration – Notification";
            } else {
                Subject = merchantName + " / " + orderReference + " / " + authorisationCode + " / "
                        + "Dispute Step Notification";
            }
        } else {
            Subject = merchantName + " / " + orderReference + " / " + authorisationCode + " / "
                    + "Pre-Arbitration – Notification";
        }

        return Subject;
    }

    public static String generateContentUser(String type, String channel, String merchantName, String dueDate,
            String merchantId,
            String orderReference, String merchantTxnRef, String cardNumber, String transDate, String authorisationCode,
            String transAmount, String disputeCurrency, String disputeAmount, String disputeCode, String disputeStage,
            String note) {
        String contentUser = "";
        if (channel.equals("INT") && disputeStage.equals("Pre-Arbitration")) {
            // EMAIL_1: Email 1: Send Dispute - Gửi thông tin tra soát đến Merchant
            contentUser = "Dear " + merchantName + ", <br/><br/>"
                    + "OnePay would like to inform you that the below payment has been filed by the cardholder though the banking system.<br/><br/>"
                    + "<b>Your best practices with this dispute case:</b><br/>"
                    + "- Contact the customer to figure out the issue & reach a mutual agreement.<br/>"
                    + "- Decide the best solution for the dispute case & respond OnePay before " + dueDate
                    + " .<br/>"
                    + "      1.  Accept Pre-Arbitration by refunding the dispute amount through OnePay system<br/>"
                    + "      2.  Ask Arbitration to decide by providing evidence to fight for the dispute.<br/><br/>"
                    + "<b>How to respond to a dispute case:</b><br/><br/>"
                    + "Please click here <a href='https://ma.onepay.vn/accounts/'> <u>here<u> </a>, or log in to your OnePay MA account and go to the Dispute Resolution Center.<br/>"
                    + "Click open this case and follow the instructions.<br/>"
                    + "<b><center style='color:blue; font-size: 20px'>Dispute Details</center></b><br/>"
                    + "<table style='border=0.5px'> <tr style='background-gcolor:#fce4d6'><th>Merchant ID</th><th>Order Ref.</th><th>MerchantTrans Ref.</th><th>Card Number</th><th>Transaction Date.</th><th>AuthCode</th><th>Transaction Amount</th><th>Dispute Amount</th><th>DisputeCode</th><th>DisputeStage</th></tr><tr><td>"
                    + merchantId + "</td><td>" + orderReference + "</td><td> " + merchantTxnRef
                    + "</td><td>" + cardNumber
                    + "</td><td>" + transDate + "</td><td>" + authorisationCode + "</td><td>"
                    + transAmount + "</td><td>" + disputeCurrency + disputeAmount + "</td><td>"
                    + disputeCode + "</td><td>" + disputeStage + "</td></tr></table><br/><br/>"
                    // + "Remarks from OnePay: Note OP User<br/>"
                    + note + "<br/>"
                    + "If you need further assistance, please contact us at <a href='<EMAIL>'><EMAIL></a><br/><br/>"
                    + "<b>Note:</b><br/>"
                    + "<i style='font=size: 10px'>-  All documents must be in English.</i><br/>"
                    + "<i style='font=size: 10px'>-  The Arbitration will give final ruling the financial liability party mostly based on your supporting evidence submitted in the previous dipsute stage. However, if you can add any evidence for your argument, don’t miss this last chance to protect yourself by supply the additional documents to us.</i><br/><br/>"
                    + "<b>Best Regards,</b><br/>"
                    + "OnePay Risk Management Dept.,<br/>"
                    + "<a style ='color:blue'> t </a>:    +84 24 3936 6668 <br/>"
                    + "<a style ='color:blue'> e </a>:   <a href='<EMAIL>'><EMAIL></a> <br/>"
                    + "<a style ='color:blue'> w </a>:   <a href='www.onepay.vn'>www.onepay.vn</a><br/><br/>"
                    + "<a style ='color:blue'> a </a>:   6th Floor, BIDV Tower, 194 Tran Quang Khai Street, Hoan Kiem Dist.,Hanoi, Vietnam<br>";
        } else if (true) {
            // EMAIL_2: Dispute Reminder - Nhắc nhở merchant phản hồi tra soát
            contentUser = "Dear" + merchantName + ", <br/><br/>"
                    + "We emailed you earlier to let you know one of your payments has been filed by the cardholder though the banking system.<br/><br/>"
                    + "Your participation is essential to the dispute resolution process. If we don’t receive a response from you before "
                    + dueDate + ", weunderstand you agree to refund the dispute amount to the cardholder"
                    + "Thanks for your attention to this matter."
                    + "<b><center style='color:blue; font-size: 20px'>DisputeDetails</center></b><br/>"
                    + "<table> </table><br/><br/>"
                    // + "Remarks from OnePay: (import Note OP User đã nhập ở bước 6)"
                    + "Remarks from OnePay: " + note + ""
                    + "<b>Your best practices with this dispute case:</b><br/>"
                    + "- Contact the customer to figure out the issue & reach a mutualagreement.<br/>"
                    + "- Decide the best solution for the dispute case & respond OnePay .<br/>"
                    + " 1. Refund the dispute amount through OnePay system<br/>"
                    + " 2. OR Provide compelling evidences to fight for the dispute.<br/><br/>"
                    + "<b>How to respond to a dispute case:</b><br/><br/>"
                    + "Please click here <a href='https://ma.onepay.vn/accounts/'> <u>here</u>> </a>, or log in to your OnePay MA account and go to the Dispute Resolution Center.<br/>"
                    + "Click open this case and follow the instructions.<br/>"
                    + "If you need further assistance, please contact us at <a href='<EMAIL>'><EMAIL></a><br/><br/>"
                    + "<b>Best Regards, </b><br/>"
                    + "OnePay Risk Management Dept.,<br/>"
                    + "<a style ='color:blue'> t </a>: +84 24 3936 6668 <br/>"
                    + "<a style ='color:blue'> e </a>: <a href='<EMAIL>'><EMAIL></a> <br/>"
                    + "<a style ='color:blue'> w </a>: <a href='www.onepay.vn'>www.onepay.vn</a><br/><br/>"
                    + "<a style ='color:blue'> a </a>: 6th Floor, BIDV Tower, 194 Tran Quang Khai Street, Hoan Kiem Dist.,Hanoi, Vietnam<br>";
        } else {
            // EMAIL_3: Dispute Closed - Thông báo merchant đã phản hồi tra soát
            contentUser = "Dear " + merchantName + ", <br/><br/>"
                    + "We acknowledge your decision for this case as Submit Evidence (import Outcome)<br/><br/>"
                    + "The dispute outcome is transmitted to banks and this case has been closed. Thanks for working with your customer to resolve this case."
                    + "<b>Best Regards, </b><br/>"
                    + "OnePay Risk Management Dept.,<br/>"
                    + "<a style ='color:blue'> t </a>: +84 24 3936 6668 <br/>"
                    + "<a style ='color:blue'> e </a>: <a href='<EMAIL>'><EMAIL></a> <br/>"
                    + "<a style ='color:blue'> w </a>: <a href='www.onepay.vn'>www.onepay.vn</a><br/><br/>"
                    + "<a style ='color:blue'> a </a>: 6th Floor, BIDV Tower, 194 Tran Quang Khai Street, Hoan Kiem Dist.,Hanoi, Vietnam<br>";
        }

        return contentUser;
    }

    public static void updateDisputeStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            Integer result = 0;
            int userId = ctx.get(X_USER_ID);
            body.put("operatorId", Integer.toString(userId));
            try {
                result = DisputeClient.updateDisputeStatus(body);
                if (result == 1) {
                    // sendOnDisputeEmail(ctx);
                    JsonObject res = new JsonObject();
                    int statusCode = 200;
                    String statusMessage = "Successful";
                    res.put("code", statusCode);
                    res.put("status", statusMessage);
                    sendResponse(ctx, statusCode, res);
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.VALIDATION_ERROR;
            }
        }, false, null);
    }

    public static void getDisputeCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, DisputeClient.getDisputeCode());
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DISPUTE CODE: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBusinessCategory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String transIds = ctx.request().getParam(TRANSACTIONIDS);
                sendResponse(ctx, 200, DisputeClient.getBusinessCategory());
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST BUSINESS CATEGORY: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();

                mIn.put(FROM_DATE, body.getString(FROM_DATE).trim());
                mIn.put(TO_DATE, body.getString(TO_DATE).trim());
                mIn.put(DISPUTE_STATUS, body.getString(DISPUTE_STATUS));
                mIn.put(TRANSACTION_ID, body.getString(TRANSACTION_ID));
                mIn.put(ORDER_REF, body.getString(ORDER_REF));
                mIn.put(MERCHANT_TRANSACTION_REF, body.getString(MERCHANT_TRANSACTION_REF));
                mIn.put(CHANNEL, body.getString(CHANNEL));
                mIn.put(ACQUIRER_ID, body.getString(ACQUIRER_ID));
                mIn.put(CARD_TYPE, Util.appendSearchOldName(body.getString(CARD_TYPE)));
                mIn.put(CARD_NUMBER, body.getString(CARD_NUMBER));
                mIn.put(AUTHORISATION_CODE, body.getString(AUTHORISATION_CODE));
                mIn.put(TRANS_AMOUNT, body.getString(TRANS_AMOUNT));
                mIn.put(DISPUTE_AMOUNT, body.getString(DISPUTE_AMOUNT));
                mIn.put(DISPUTE_REASON, body.getString(DISPUTE_REASON));
                mIn.put(DISPUTE_CODE, body.getString(DISPUTE_CODE));
                mIn.put(DISPUTE_STAGE, body.getString(DISPUTE_STAGE));
                mIn.put(OUTCOME, body.getString(OUTCOME));
                mIn.put(FILTER_TYPE, body.getString(FILTER_TYPE));
                mIn.put(COLUMN_LIST, body.getString(COLUMN_LIST));
                mIn.put(COLUMN_ACTIVE, body.getString(COLUMN_ACTIVE));
                mIn.put(FILE_TYPE, body.getString(FILE_TYPE));
                Integer userID = ctx.get(X_USER_ID);
                UserProfileDto userProfile = UserDao.getProfileInfo(userID);
                String outsource = userProfile.getFunctions().stream().filter(s -> {return s.equals(SS_OUTSOURCE_USER);}).findAny().orElse(null);
                if(null != outsource && !outsource.isEmpty()){
                    logger.info(() -> String.format("userId=%d name=%s is %s", userID, userProfile.getName(), SS_OUTSOURCE_USER));
                    mIn.put(USER_ID, userID);
                }
                mIn.put(ONEPAY_PIC, body.getString(ONEPAY_PIC));
                mIn.put(DEPARTMENT, body.getString(DEPARTMENT));
                mIn.put(MERCHANT_CHANNEL, body.getString(MERCHANT_CHANNEL));
                mIn.put(PARTNER_NAME, body.getString(PARTNER_NAME));
                mIn.put(TRANSACTION_STATE, body.getString(TRANSACTION_STATE));
                mIn.put(TRANSACTION_TYPE, body.getString(TRANSACTION_TYPE));
                mIn.put(FRAUD_INVES, body.getString(FRAUD_INVES));
                mIn.put(DISPUTE_IDS, body.getString(DISPUTE_IDS));
                String roleName = Config.getString("role_merchant.role_id", "");
                if (userProfile.getRoleList().contains(roleName)) {
                    String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                    String merchantFE = body.getString(MERCHANT_ID);
                    StringBuilder merchants = new StringBuilder("");
                    if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                        String[] merchantFEs = merchantFE.split(",");
                        String[] configuredMerchants = merchantsConf.split(",");
                        List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                        if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                            merchants.append("abc");
                        } else {
                            merchants.append(String.join(",", merchantSearchs));
                        }

                    } else if (!merchantsConf.isBlank() && 
                        (("contains".equalsIgnoreCase(body.getString(FILTER_TYPE)) && !body.getString(MERCHANT_ID).isBlank() || "equals".equalsIgnoreCase(body.getString(FILTER_TYPE))))) {
                        merchants.append(merchantsConf);
                    } else {
                        merchants.append(merchantFE);
                    }
                    mIn.put(MERCHANT_ID, merchants);
                    mIn.put(MERCHANT_ROLE, merchantsConf);
                } else {
                    mIn.put(MERCHANT_ID, body.getString(MERCHANT_ID));
                }

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                // Initial file information
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String date = sdf.format(new Date());
                String fileName = "";

                if (DISPUTE_SERVICE_SUPPORT.equalsIgnoreCase(body.getString(FILE_TYPE))) fileName += DISPUTE_SERVICE_SUPPORT_FILE_NAME;
                else if (DISPUTE_RISK_DOM.equalsIgnoreCase(body.getString(FILE_TYPE))) fileName += DISPUTE_RISK_DOM_FILE_NAME;
                else if (DISPUTE_RISK_INT.equalsIgnoreCase(body.getString(FILE_TYPE))) fileName += DISPUTE_RISK_INT_FILE_NAME;
                fileName += date;
                String fileHashName = "";
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("dispute_management");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);

                JsonObject disputeResp = DisputeClient.getTotalDispute(mIn);
                int totalRows = disputeResp == null ? 0 : disputeResp.getInteger("totalItems");

                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);

    }

    public static void emailDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                Map mIn = new HashMap();
                mIn.put(MERCHANT_NAME, body.getString(MERCHANT_NAME));
                mIn.put(TRANSACTION_ID, body.getString(TRANSACTION_ID));
                mIn.put(DUE_DATE, body.getString(DUE_DATE));
                mIn.put(DISPUTE_STAGE, body.getString(DISPUTE_STAGE));
                mIn.put(DISPUTE_CODE, body.getString(DISPUTE_CODE));
                mIn.put(ORDER_REF, body.getString(ORDER_REF));
                mIn.put(AUTHORISATION_CODE, body.getString(AUTHORISATION_CODE));
                mIn.put(PAYGATE, body.getString(PAYGATE));
                mIn.put(MERCHANT_ID, body.getString(MERCHANT_ID));
                mIn.put(DISPUTE_DATE, body.getString(DISPUTE_DATE));
                mIn.put(DISPUTE_TRANS_AMOUNT, body.getInteger(DISPUTE_TRANS_AMOUNT));
                mIn.put(DISPUTE_AMOUNT, body.getInteger(DISPUTE_AMOUNT));
                mIn.put(MERCHANT_TRANSACTION_REF, body.getString(MERCHANT_TRANSACTION_REF));
                mIn.put(DISPUTE_TRANS_DATE, body.getString(DISPUTE_TRANS_DATE));
                mIn.put(CARD_NUMBER, body.getString(CARD_NUMBER));
                mIn.put(DISPUTE_IDS, body.getString(DISPUTE_IDS));

                sendResponse(ctx, 200, DisputeClient.getEmailContent(mIn));
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }

    public static void sendEmailToMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                Map mOut = new HashMap();
                String emailContent = body.getString("email_content");
                String emailCc = body.getString("email_cc");
                String emailSubject = body.getString("email_subject");
                String emailTo = body.getString("email_to");

                MailUtil.sendMailWithBCC(emailTo, emailCc, "", emailSubject, emailContent, "html");
                mOut.put("nStatus", 200);
                sendResponse(ctx, 200, mOut);
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }

    public static void getAcquirerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mOut = DisputeClient.getAcqList();
                sendResponse(ctx, 200, mOut);
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }

    public static String uploadFileDispute(RoutingContext ctx, String path) {
        String s_result = "";
        try {
            for (FileUpload f : ctx.fileUploads()) {
                // FileUploadDto fu = new FileUploadDto();
                String fileName = f.fileName();
                s_result = fileName;
                File uploadedFile = new File(f.uploadedFileName());
                Files.move(uploadedFile.toPath(), new File(path, f.fileName()).toPath());
            }

        } catch (Exception e) {
            logger.log(Level.WARNING, "Upload FILE Dispute ERROR: ", e);
        }
        return s_result;
    }
    private static Future<Boolean> upFile(FileSystem fs, Path srcPath, Path desPath){
        Promise p = Promise.promise();
        fs.exists(srcPath.toString(), (isExist) -> {
            if (isExist.result()) {
                try {
                    Files.copy(srcPath, desPath);
                    p.complete();
                } catch (IOException e) {
                    logger.log(Level.SEVERE, "error on move file dispute from "+srcPath.toString() + " to " +desPath.toString(), e);
                    p.fail(e.getMessage());;
                }
            } else {
                logger.log(Level.SEVERE, "file dispute srcPath not found: " + srcPath.toString());
                p.fail("file dispute srcPath not found: " + srcPath.toString());
            }
        });
        return p.future();
    }
    
    public static void sendToSftp(RoutingContext ctx) {
        try {
            // JsonObject body = ctx.getBodyAsJson();
            JsonObject body = ctx.getBodyAsJson();
            // TO DO
            // body bao gom ********
            // call sang api dispute detail => Lay dc ta

            String fileFormat = "zip";
            // config iportal
            String host = Config.getString("dispute-service.host", "");
            int port = Config.getInteger("dispute-service.port", 0);
            String user = Config.getString("dispute-service.user", "");
            String password = Config.getString("dispute-service.pass", "");
            String privateKey = Config.getString("dispute-service.host", "");
            String folderTo = Config.getString("dispute-service.folder", "");
            String backup = Config.getString("backup", "");
            String folderBackup = Config.getString("folder_backup", "");
            File folder = new File("filename");
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Service Dispute Upload");
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                InputStream fileStream = new FileInputStream(file);
                                Sftp.pushSftp(host, port, user, password, privateKey, folderTo, backup, file.getName(),
                                        fileStream, null);
                                fileStream.close();
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    OutputStream outStream = new FileOutputStream(fileBackup);
                                    file.delete();
                                    try {
                                        if (outStream != null)
                                            outStream.close();
                                    } catch (IOException e) {
                                        logger.log(Level.SEVERE, "", e);
                                    }
                                }
                                try {
                                    if (fileStream != null)
                                        fileStream.close();
                                } catch (IOException e) {
                                    logger.log(Level.SEVERE, "", e);
                                }
                            } catch (Exception e) {// Catch exception if any
                                logger.log(Level.SEVERE, "", e);
                            }
                        } else {
                            logger.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("folderScan: " + "folderFrom" + " is not a directory");
            }
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
        }
    }

    public static void searchDisputeByKeys(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                Map mIn = new HashMap();
                mIn.put("keys", body.getString("keys"));

                sendResponse(ctx, 200, DisputeClient.getDisputesByKeys(mIn));
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }

    public static void downloadFromSftp(RoutingContext ctx) {
        logger.info("begin downloadFromSftp:");
        JsonObject res = new JsonObject();
        ctx.vertx().executeBlocking(future -> {
            try {
                String URL = Config.getString("dispute-service.path_upload_file", "") + "download/";;
                // logger.info("URL: " + URL);
                HttpServerRequest jReq = ctx.request();
                String fileId = jReq.getParam("fileId");
                String fileType = jReq.getParam("fileType");
                CDRFileDto file = CDRFileManageDao.getCDRFile(fileId);
                String serviceId = file.getService_name();
                String fileName = file.getFile_name();
                // logger.info("serviceId: " + serviceId);
                Map<String, Object> merchantConfig = DisputeDao.getConfigSendToSftp(serviceId, fileType);
                // for (String key : merchantConfig.keySet()) {
                // logger.info(key + ": " + String.valueOf(merchantConfig.get(key)));
                // }
                String host = merchantConfig.get("host") != null ? String.valueOf(merchantConfig.get("host")) : null;
                int port = merchantConfig.get("port") != null ? Integer.parseInt(String.valueOf(merchantConfig.get("port"))) : null;
                String user = merchantConfig.get("user") != null ? String.valueOf(merchantConfig.get("user")) : null;
                String password = merchantConfig.get("password") != null ? String.valueOf(merchantConfig.get("password")) : null;
                String privateKey = merchantConfig.get("privateKey") != null ? String.valueOf(merchantConfig.get("privateKey")) : null;
                String folderBackup = merchantConfig.get("backup") != null ? String.valueOf(merchantConfig.get("backup")) : null;
                File folderDir = new File(URL);
                JSch jsch = new JSch();
                if (privateKey != null && !"".equals(privateKey)) {
                    jsch.addIdentity("", privateKey.getBytes(), null, null);
                }
                Session session = null;
                ChannelSftp sftpChannel = null;
                InputStream fileInputStrm = null;
                try {
                    session = jsch.getSession(user, host, port);
                    session.setConfig("StrictHostKeyChecking", "no");
                    if (password != null && !"".equals(password)) {
                        session.setPassword(password);
                    }
                    session.connect();
                    Channel channel = session.openChannel("sftp");
                    channel.connect();
                    sftpChannel = (ChannelSftp) channel;
                    sftpChannel.cd(getFullPath(folderBackup, ""));
                    //phần này không để trong Sftp.java bởi vì inputstream lấy từ sftp mà bị close() 
                    //thì sẽ không tải được file, phải trả response file download trước rồi mới đóng connect
                    fileInputStrm = sftpChannel.get(fileName);
                    byte[] bytes = fileInputStrm.readAllBytes();
                    Buffer buf = Buffer.buffer(bytes);
                    ctx.response().setStatusMessage(fileName).sendFile(URL + fileName, (result) -> {
                        if (result.succeeded()) {
                            try {
                                FileUtils.cleanDirectory(folderDir);
                            } catch (IOException e) {
                                logger.log(Level.SEVERE, "downloadFromSftp error1", e);
                            }
                        }
                    }).setChunked(true)
                    .write(buf).end();
                } catch (Exception e) {
                    // logger.log(Level.SEVERE, "downloadFromSftp error2", e);
                    throw e;
                } finally {
                    logger.info("done downloadFromSftp");
                    if (sftpChannel!=null){
                        sftpChannel.disconnect();
                    }
                    if (session!=null){
                        session.disconnect();
                    }
                    if(fileInputStrm!=null){
                        fileInputStrm.close();
                    }
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "downloadFromSftp error2", e);
                res.put("error", e.getLocalizedMessage());
                sendResponse(ctx, 500, res);
            }
        }, false, null);
    }

    private static String getFullPath(String path, String fileName) {
        String startSeparator = path.startsWith("/") ? "" : "/";
        String endSeparator = path.endsWith("/") ? "" : "/";
        return startSeparator + path + endSeparator + fileName;
    }

    public static void SendToBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject res = new JsonObject();
            try {
                Map merchantConfig = DisputeDao.getConfigSendToSftp("KBANK-DISPUTE", "onepay");
                SendListToSftp(ctx, merchantConfig);
                //insert db
                MultiMap formRequest = ctx.request().formAttributes();
                JsonArray listDispute = new JsonArray(formRequest.get("listDispute"));
                for(int i = 0; i < listDispute.size(); i++){
                    if(listDispute.getJsonObject(i).getString("fileName") != null 
                        && !listDispute.getJsonObject(i).getString("fileName").isEmpty()){

                        int disputeID = listDispute.getJsonObject(i).getInteger("id");
                        String userName = ctx.request().getParam("userName");
                        String transID = listDispute.getJsonObject(i).getString("transactionId");
                        String acquirer = listDispute.getJsonObject(i).getString("acquirer");
                        String caseID = listDispute.getJsonObject(i).getString("sCaseID");
                        String noteDisputeCase = "";
                        if(listDispute.getJsonObject(i).containsKey("noteDisputeCase") && listDispute.getJsonObject(i).getString("noteDisputeCase") != null){
                            noteDisputeCase = listDispute.getJsonObject(i).getString("noteDisputeCase");
                        }
                        
                        Integer n_id = DisputeDao.InsertTbCdrFile(listDispute.getJsonObject(i).getString("fileName"), merchantConfig.get("serviceID").toString());
                        if(n_id != 0){
                            DisputeDao.InsertTbDisputeCase(disputeID, transID, caseID, userName, n_id, noteDisputeCase, acquirer);
                        }
                    }
                }
                res.put("status", "success");
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEND TO BANK: ", e);
                res.put("status", "failed");
                ctx.fail(e);
            } finally{
                sendResponse(ctx, 200, res);
            }
        }, false, null);
    }

    public static String SendListToSftp(RoutingContext ctx, Map merchantConfig) {
        JsonObject res = new JsonObject();
        Integer result = 0;
        String s_result = "";
        try {
            // TO DO
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime now = LocalDateTime.now();
            String fileName = dtf.format(now) + "_" + "KBANK-DISPUTE";

            Path source = Paths.get(Config.getString("dispute-service.path_upload_file", "") + fileName);
            if (!Files.isDirectory(source)) {
                Files.createDirectories(source);
            }
            //upload file từ frontend lên đây (iportal-service)
            s_result = uploadFileDispute(ctx, Config.getString("dispute-service.path_upload_file", "") + fileName);
            try {
                //upload file sftp lên con nào tùy theo config merchant lấy từ onecdr14.tb_exp_partner_file
                int checkup = SendToSftpHandler.uploadFileToMerchantConfig(Config.getString("dispute-service.path_upload_file", "") + fileName, merchantConfig);
                // int checkup = 1;
                if (checkup == 1) {
                    File removeFile = new File(Config.getString("dispute-service.path_upload_file", "") + fileName);
                    //xóa folder temp mà upload lên iportal-service
                    FileUtils.cleanDirectory(removeFile);
                    //
                    File directory = new File(Config.getString("dispute-service.path_upload_file", "") + fileName);
                    FileUtils.cleanDirectory(directory);
                    Files.delete(source);
                }
            } catch (IOException e) {
                res.put("status", "failed");
                result = 500;
                s_result = "";
                e.printStackTrace();
            }

            System.out.println("Done");
            res.put("status", "success");
            result = 200;
        } catch (Exception e) {
            logger.log(Level.WARNING, "Send to sftp Error: ", e);
            res.put("status", "failed");
            result = 500;
            s_result = "";
            ctx.fail(e);
        } finally {
            return s_result;
        }
    }

    public static void getCdrAccquirerRequest(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map mIn = new HashMap();
                if (request.getParam(FROM_DATE) == null || request.getParam(FROM_DATE).trim().isEmpty()
                    || request.getParam(TO_DATE) == null || request.getParam(TO_DATE).trim().isEmpty()){
                        throw IErrors.VALIDATION_ERROR;
                    }
                mIn.put(FROM_DATE, request.getParam(FROM_DATE).trim());
                mIn.put(TO_DATE, request.getParam(TO_DATE).trim());
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID));
                mIn.put(USER_ID, request.getParam(USER_ID));
                mIn.put(FILE_NAME, request.getParam(FILE_NAME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 100 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put("serviceId", request.getParam("serviceId"));
                mIn.put("type", request.getParam("type"));

                sendResponse(ctx, 200, DisputeDao.getCdrAccquirerRequest(mIn));
            } catch (Exception ex) {
                ctx.fail(ex);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }, false, null);
    }
    public static void searchDisputeById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String disputeId = ctx.request().getParam(ID);
                String transId;
                String paygate;
                logger.info(() -> "Search dispute by id=" + disputeId);
                JsonObject jsonReturn = new JsonObject();

                JsonObject jsonReturn112 = DisputeClient.searchDisputeById(disputeId);
                JsonObject disputeDetail = jsonReturn112.getJsonArray("list").getJsonObject(0);
                transId = disputeDetail.getString("transactionId");
                paygate = disputeDetail.getString("paygate");
                logger.info(() -> "disputeId=" + disputeId + " transId=" + transId + " paygate=" + paygate);

                JsonArray disputeHistory = jsonReturn112.getJsonArray("list");
                jsonReturn.put("list", disputeHistory);
                jsonReturn.put("totalItems", disputeHistory.size());

                JsonObject jsonReturnTransHistory = DisputeClient.searchHistoryByTransIdsV2(transId, paygate);
                JsonArray transHistory = jsonReturnTransHistory.getJsonArray("list");
                jsonReturn.put("transactionHistory", transHistory);

                //file history
                List<JsonObject> filesHis = DisputeDao.getFilesHistory(disputeId);
                jsonReturn.put("fileHistory", filesHis);
                //

                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "searchDisputeById: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchDisputeByIds(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jBody = ctx.getBodyAsJson();
                if (!jBody.containsKey(DISPUTE_IDS)) {
                    throw IErrors.VALIDATION_ERROR;
                }
                logger.info(() -> "Search dispute by ids=" + jBody.encode());
                JsonObject jsonReturn = DisputeClient.searchDisputeByIds(jBody);
                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.warning(() -> "Search dispute by ids: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateByBatch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                logger.info(() -> "Update disputes by batch");

                JsonObject jsonReturn = DisputeClient.updateDisputeByBatch(body);

                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.warning(() -> "Update disputes by batch: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantsCanCreateCSVFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> "GetMerchantsCanCreateCSVFile");

                JsonObject jsonReturn = DisputeClient.getAppleDisputeMerchants();

                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.warning(() -> "GetMerchantsCanCreateCSVFile: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getCurrentCaseNoValue(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> "GetCurrentCaseNoValue");

                JsonObject jsonReturn = DisputeClient.getCurrentCaseNo();

                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.warning(() -> "GetCurrentCaseNoValue: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createCSVFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> "createCSVFile");
                JsonObject body = ctx.getBodyAsJson();
                if (!body.containsKey(DATA) || body.getJsonArray(DATA).size() == 0 || !body.containsKey(OPERATOR)) {
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject jsonReturn = DisputeClient.createCSVFile(body);

                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.warning(() -> "GetCurrentCaseNoValue: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getCardList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject acquirers = new JsonObject();
                acquirers.put("acquirers", DisputeDao.getCardLists());
                Util.sendResponse(ctx, 200, acquirers);
            } catch (Exception ex) {
                logger.log(Level.INFO, "get card list error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadMultipleFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonArray bodyJsonArray = ctx.getBodyAsJsonArray();

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                Map<String, Object> mIn = new HashMap<>();
                List<Map<String, Object>> listFileDto = new ArrayList<>();
                
                for (Object jsObj : bodyJsonArray) {
                    JsonObject object = (JsonObject) jsObj;
                    Map<String, Object> mFile = new HashMap<>();
                    mFile.put(CDR_FILE_ID, object.getString(CDR_FILE_ID) == null ? "" : object.getString(CDR_FILE_ID));
                    boolean download_data_only = Objects.requireNonNullElse(object.getBoolean(DOWNLOAD_DATA_ONLY), false);
                    mFile.put(DOWNLOAD_DATA_ONLY, download_data_only);
                
                    CDRFileDto cdrFileDto = CDRFileManageDao.getCDRFile2(mFile.get(CDR_FILE_ID).toString());
                    mFile.put(CDR_FILE_HEADER, cdrFileDto.header);
                    mFile.put(SOURCE_NAME, cdrFileDto.source_name);
                    mFile.put(SERVICE_NAME, cdrFileDto.service_name);
                    mFile.put(CDR_FILE_NAME, cdrFileDto.file_name);

                    mFile.put(CDR_FILE_D_CREATE, cdrFileDto.getCreateDateString());
                    mFile.put(CDR_FILE_D_IMPORT, cdrFileDto.getImportDateString());
                    mFile.put(CDR_FILE_D_CDR, cdrFileDto.getCDRDateString());

                    listFileDto.add(mFile);
                }

                mIn.put("listFileDto", listFileDto);

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "";
                String fileHashName = "";
                fileName = "data_cdr_file_" + date;

                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("cdr_file");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                
                fileDownloadDto.setConditions(gson.toJson(mIn));
                fileDownloadDto.setExt("zip");

                FileDownloadDao.insert(fileDownloadDto);

                QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), ctx.request().path(), requestData));

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD DISPUTE FILE Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
