package vn.onepay.portal.resources.dispute.dto;

public class TransactionList {
    private int id;
    private int partnerId;
    private String generalKey;
    private String partnerName;
    private String merchantId;
    private String merchantName;
    private String mcc;
    private String transactionId;
    private String disputeTxnId;
    private String merchantTxnRef;
    private String orderInfo;
    private String channel;
    private String acquirer;
    private String cardType;
    private String cardNumber;
    private String tranType;
    private Double tranAmount;
    private String currency;
    private String tranDate;
    private String authCode;
    private String bankName;
    private String binCountry;
    private String cscResponse;
    private String authState;
    private String responseCode;
    private String tranStatus;
    private Integer operatorId;
    private String operatorName;
    private String paygate;
    private String qrId;
    private String transCurrency;
    private String issuer;
    private String orderRef;
    private String disputeCurrency;
    private Double disputeAmount;
    private String department;
    private String merchantChannel;
    private String networkTransId;

    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public int getPartnerId() {
        return partnerId;
    }
    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }
    public String getPartnerName() {
        return partnerName;
    }
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
    public String getMerchantId() {
        return merchantId;
    }
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
    public String getMerchantName() {
        return merchantName;
    }
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getMcc() {
        return mcc;
    }
    public void setMcc(String mcc) {
        this.mcc = mcc;
    }
    public String getTransactionId() {
        return transactionId;
    }
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    public String getMerchantTxnRef() {
        return merchantTxnRef;
    }
    public void setMerchantTxnRef(String merchantTxnRef) {
        this.merchantTxnRef = merchantTxnRef;
    }
    public String getOrderInfo() {
        return orderInfo;
    }
    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }
    public String getChannel() {
        return channel;
    }
    public void setChannel(String channel) {
        this.channel = channel;
    }
    public String getAcquirer() {
        return acquirer;
    }
    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }
    public String getCardType() {
        return cardType;
    }
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }
    public String getCardNumber() {
        return cardNumber;
    }
    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }
    public String getTranType() {
        return tranType;
    }
    public void setTranType(String tranType) {
        this.tranType = tranType;
    }
    public Double getTranAmount() {
        return tranAmount;
    }
    public void setTranAmount(Double tranAmount) {
        this.tranAmount = tranAmount;
    }
    public String getCurrency() {
        return currency;
    }
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    public String getTranDate() {
        return tranDate;
    }
    public void setTranDate(String tranDate) {
        this.tranDate = tranDate;
    }
    public String getAuthCode() {
        return authCode;
    }
    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }
    public String getBankName() {
        return bankName;
    }
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    public String getBinCountry() {
        return binCountry;
    }
    public void setBinCountry(String binCountry) {
        this.binCountry = binCountry;
    }
    public String getCscResponse() {
        return cscResponse;
    }
    public void setCscResponse(String cscResponse) {
        this.cscResponse = cscResponse;
    }
    public String getAuthState() {
        return authState;
    }
    public void setAuthState(String authState) {
        this.authState = authState;
    }
    public String getResponseCode() {
        return responseCode;
    }
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
    public String getTranStatus() {
        return tranStatus;
    }
    public void setTranStatus(String tranStatus) {
        this.tranStatus = tranStatus;
    }

    public Integer getOperatorId() {
        return operatorId;
    }
    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }
    public String getOperatorName() {
        return operatorName;
    }
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }
    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getQrId() {
        return qrId;
    }

    public void setQrId(String qrId) {
        this.qrId = qrId;
    }

    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getOrderRef() {
        return orderRef;
    }

    public void setOrderRef(String orderRef) {
        this.orderRef = orderRef;
    }
    
    public String getDisputeCurrency() {
        return disputeCurrency;
    }
    public void setDisputeCurrency(String disputeCurrency) {
        this.disputeCurrency = disputeCurrency;
    }
    public Double getDisputeAmount() {
        return disputeAmount;
    }
    public void setDisputeAmount(Double disputeAmount) {
        this.disputeAmount = disputeAmount;
    }
    public String getDepartment() {
        return department;
    }
    public void setDepartment(String department) {
        this.department = department;
    }
    public String getMerchantChannel() {
        return merchantChannel;
    }
    public void setMerchantChannel(String merchantChannel) {
        this.merchantChannel = merchantChannel;
    }
    public String getDisputeTxnId() {
        return disputeTxnId;
    }
    public void setDisputeTxnId(String disputeTxnId) {
        this.disputeTxnId = disputeTxnId;
    }
    public String getGeneralKey() {
        return generalKey;
    }
    public void setGeneralKey(String generalKey) {
        this.generalKey = generalKey;
    }
    
    public String getNetworkTransId() {
        return this.networkTransId;
    }
    public void setNetworkTransId(String networkTransId) {
        this.networkTransId = networkTransId;
    }
}
