package vn.onepay.portal.resources.dispute.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.dispute.dto.CardListDto;
import vn.onepay.portal.resources.dispute.dto.MerchantData;
import vn.onepay.portal.resources.quicklink.dto.MerchantProfileDto;
import vn.onepay.portal.resources.quicklink.dto.ModelDto;
import vn.onepay.portal.resources.quicklink.dto.PartnerDto;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import io.vertx.core.json.JsonObject;

public class DisputeDao extends Db {
    private static final String GET_ALL_MERCHANT_NAME = "{call PKG_QUICKLINK.GET_ALL_MERCHANT_NAME(?,?,?) }";
    private static final String GET_ALL_MERCHANT_ID = "{call PKG_QUICKLINK.GET_ALL_MERCHANT_ID(?,?,?) }";
    private static final String GET_ALL_PARTNER = "{call PKG_QUICKLINK.GET_ALL_PARTNER(?,?,?) }";
    private static final String GET_LIST_MERCHANT_PROFILE = "{call PKG_QUICKLINK.get_list_merchant_profile(?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String GET_PARTNER_LIST = "{call PKG_QUICKLINK.GET_PARTNER_LIST(?,?,?,?,?,?,?) }";
    private static final String GET_TOTAL_PARTNER_LIST = "{call PKG_QUICKLINK.GET_TOTAL_PARTNER_LIST(?,?,?,?,?) }";
    private static final String GET_PAYNOW_BY_ID = "{call PKG_QUICKLINK.get_paynow_by_id(?,?,?,?) }";
    private static final String GET_INSTALLMENT_BY_ID = "{call PKG_QUICKLINK.get_installment_by_id(?,?,?,?) }";
    private static final String GET_INSTALLMENT_MERCHANT_IDS = "{call MSP.P_PORTAL.installment_merchant_ids(?,?,?,?)}";
    private static final String CHECK_HAS_PAYMENT_LINK = "{call PKG_QUICKLINK.check_payment_link(?,?,?,?)";
    private static final String GET_ACQUIRER_LIST = "{call PKG_DISPUTE.get_acquirer_list(?,?,?)}";
    private static final String GET_MERCHANT_MSP_DATA = "{call GET_MERCHANT_MSP_DATA(?,?,?,?)}";
    private static final String GET_PATH_EXPORT_FILE = "{? = call PKG_CDR.get_exp_file(?,?)}";
    private static final String GET_PATH_IMPORT_FILE = "{? = call PKG_CDR.get_imp_file2(?,?)}";
    private static final String INSERT_TB_CDR_FILE = "{? = call INSERT_TB_CDR_FILE(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String INSERT_TB_DISPUTE_CASE = "{? = call INSERT_TB_DISPUTE_CASE_2(?,?,?,?,?,?,?)}";
    // private static final String GET_DISPUTE_CASE_BY_TRANS_ID = "{call P_DISPUTED.get_dispute_case_by_transId(?,?,?)}";
    private static final String GET_FILE_HISTORY = "{call PKG_DISPUTE.get_file_history(?,?,?,?)}";
    private static final String GET_CDR_FILES_BY_IDS = "{call PKG_CDR.get_cdr_files_by_ids(?,?,?)}";
    private static final String GET_CDR_FILE_ACCQUIRER_REQUEST = "{call PKG_CDR.get_cdr_file_accquirer_request(?,?,?,?,?,?,?,?)}";


    private static final Logger LOGGER = Logger.getLogger(DisputeDao.class.getName());

    public static Map<String, Object> getAllMerchantName() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_MERCHANT_NAME);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_MERCHANT_NAME: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_NAME")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getAllMerchantId() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_MERCHANT_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_MERCHANT_ID: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getAllPartner() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<PartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_PARTNER: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(new PartnerDto(rs.getInt("N_PARTNER_ID"), rs.getString("S_PARTNER_NAME")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static List<MerchantProfileDto> getListMerchantProfile(Map mIn) throws Exception {
        Exception exception = null;
        List<MerchantProfileDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_MERCHANT_PROFILE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.get("name").toString());
            cs.setString(6, mIn.get("merchantName").toString());
            cs.setString(7, mIn.get("merchantId").toString());
            cs.setString(8, mIn.get("partnerId").toString());
            cs.setString(9, mIn.get("paymentMethod").toString());
            cs.setString(10, mIn.get("state").toString());
            cs.setString(11, mIn.get("type").toString());
            cs.setString(12, mIn.get("page").toString());
            cs.setString(13, mIn.get("page_size").toString());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_LIST_MERCHANT_PROFILE: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindMerchantProfileData(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalMerchantProfile(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_MERCHANT_PROFILE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.get("name").toString());
            cs.setString(6, mIn.get("merchantName").toString());
            cs.setString(7, mIn.get("merchantId").toString());
            cs.setString(8, mIn.get("partnerId").toString());
            cs.setString(9, mIn.get("paymentMethod").toString());
            cs.setString(10, mIn.get("state").toString());
            cs.setString(11, mIn.get("type").toString());
            cs.setString(12, mIn.get("page").toString());
            cs.setString(13, mIn.get("page_size").toString());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_LIST_MERCHANT_PROFILE: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getPartnerLists(Map mIn) throws Exception {
        Map<String, Object> dataList = new HashMap<>();
        dataList.put("list", getPartnerList(mIn));
        dataList.put("total", getTotalPartnerList(mIn));
        return dataList;
    }

    public static List<PartnerDto> getPartnerList(Map mIn) throws Exception {
        Exception exception = null;
        List<PartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(KEYWORD).toString());
            cs.setString(5, mIn.get(CONTRACT_STATUS).toString());
            cs.setString(6, mIn.get(PAGE).toString());
            cs.setString(7, mIn.get(PAGE_SIZE).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_PARTNER_LIST: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindPartner(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalPartnerList(Map mIn) throws Exception {
        Exception exception = null;
        Integer total = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_TOTAL_PARTNER_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(KEYWORD).toString());
            cs.setString(5, mIn.get(CONTRACT_STATUS).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_TOTAL_PARTNER_LIST: " + error);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt("TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static Map<String, Object> getPaynowMerchantByPartnerId(Integer partnerId) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PAYNOW_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_PAYNOW: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }

            if (result.size() > 0) {
                String merchantIds = result.stream().map(item -> item.getModelName()).collect(Collectors.joining(","));
                List<String> merchantIdsInstallment = merchantIdsInstallment112(merchantIds);
                if (merchantIdsInstallment != null && merchantIdsInstallment.size() > 0) {
                    Iterator<ModelDto> iterator = result.iterator();
                    while (iterator.hasNext()) {
                        ModelDto modelDto = iterator.next();
                        if (merchantIdsInstallment.contains(modelDto.getModelName()))
                            iterator.remove();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getInstallmentMerchantByPartnerId(Integer partnerId) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_INSTALLMENT_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_INSTALLMENT_BY_ID: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }
            if (result.size() > 0) {
                String merchantIds = result.stream().map(item -> item.getModelName()).collect(Collectors.joining(","));
                List<String> merchantIdsInstallment = merchantIdsInstallment112(merchantIds);
                if (merchantIdsInstallment != null && merchantIdsInstallment.size() > 0) {
                    Iterator<ModelDto> iterator = result.iterator();
                    while (iterator.hasNext()) {
                        ModelDto modelDto = iterator.next();
                        if (!merchantIdsInstallment.contains(modelDto.getModelName()))
                            iterator.remove();
                    }
                } else
                    //không tồn tại merchant id nào trong bảng msp112.tb_installment_merchant
                    result = new ArrayList<>();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getListMerchantProfiles(Map mIn) throws Exception {
        Map<String, Object> dataList = new HashMap<>();
        dataList.put("list", getListMerchantProfile(mIn));
        dataList.put("total", getTotalMerchantProfile(mIn));
        return dataList;
    }

    public static String getOnePartnerEmailList(int partnerId, String channel) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result = "";
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.dispute_partner_email_list(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.setString(5, channel);
            cs.execute();

            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("DB get registerd email error: " + sResult);
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                if (Util.getColumnString(rs, "S_EMAIL") != null) {
                    if (result == "") {
                        result = Util.getColumnString(rs, "S_EMAIL");
                    } else {
                        result = result + "," + Util.getColumnString(rs, "S_EMAIL");
                    }
                }

            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static MerchantProfileDto bindMerchantProfileData(ResultSet rs) throws SQLException {
        MerchantProfileDto model = new MerchantProfileDto();
        model.setId(rs.getInt("N_ID"));
        model.setName(rs.getString("S_NAME"));
        model.setLogo(rs.getString("S_LOGO"));
        model.setMerchantName(rs.getString("S_MERCHANT_NAME"));
        model.setAddress(rs.getString("S_ADDRESS"));
        model.setEmail(rs.getString("S_EMAIL"));
        model.setWebsite(rs.getString("S_WEBSITE"));
        List<String> fields = new ArrayList<String>(Arrays.asList(rs.getString("S_FIELDS").split(",")));
        model.setFields(fields);
        model.setCreateDate(rs.getTimestamp("D_CREATE"));
        model.setUpdateDate(rs.getTimestamp("D_UPDATE"));
        model.setCreate(rs.getString("S_CREATE"));
        model.setUpdate(rs.getString("S_UPDATE"));
        model.setPaymentMethod(rs.getString("S_PAY_METHOD"));
        model.setState(rs.getString("S_STATE"));
        model.setType(rs.getString("S_TYPE"));
        return model;
    }

    private static PartnerDto bindPartner(ResultSet rs) throws SQLException {
        PartnerDto model = new PartnerDto();
        model.setPartnerId(rs.getInt("N_ID"));
        model.setShortName(rs.getString("S_SHORT_NAME"));
        model.setPartnerName(rs.getString("S_PARTNER_NAME"));
        model.setBusinessName(rs.getString("S_BUSINESS_NAME"));
        return model;
    }


    private static List<String> merchantIdsInstallment112(String merchantIds) throws Exception {
        List<String> results = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(GET_INSTALLMENT_MERCHANT_IDS);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, merchantIds);

            cs.execute();
            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB installment_merchant_ids error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            results = new ArrayList<>();
            while (rs != null && rs.next())
                results.add(Util.getColumnString(rs, "S_MERCHANT_ID"));
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return results;
    }

    public static String getPartnerById(Integer partnerId) throws Exception {
        String partnerName = "";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            conn = getConnection114();
            ps = conn.prepareStatement("SELECT * FROM ONEPARTNER.tbl_partner WHERE n_id = ?");
            ps.setInt(1, partnerId);
            rs = ps.executeQuery();
            if (rs != null && rs.next())
                partnerName = Util.getColumnString(rs, "S_SHORT_NAME");
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, ps, null, conn);
        }
        if (exception != null)
            throw exception;
        return partnerName;

    }

    public static Map checkHasPaymentLink(String profileIds) throws Exception {
        Map paymentLink = new HashMap();
        Connection conn = null;
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(CHECK_HAS_PAYMENT_LINK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, profileIds);
            cs.execute();
            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB check_payment_link error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                paymentLink.put(Util.getColumnInteger(rs, "N_PROFILE_ID"), Util.getColumnString(rs, "S_TYPE"));
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return paymentLink;
    }

    public static Map getAcqList() throws Exception {
        Map result = new HashMap();
        List<String> acqList = new ArrayList<>();
        Connection conn = null;
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(GET_ACQUIRER_LIST);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.execute();
            Integer nResult = cs.getInt(1);
            if (nResult != 200)
                throw new Exception("DB error: " + cs.getString(2));
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                acqList.add(Util.getColumnString(rs, "S_ACQUIRER"));
            result.put("list", acqList);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static MerchantData getMerchantMspData(String merchantId) throws Exception {
        Exception exception = null;
        MerchantData result = new MerchantData();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118OneData();
            cs = con.prepareCall(GET_MERCHANT_MSP_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchantId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get merchant data by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.setAccessCode(Util.getColumnString(rs, "S_ACCESS_CODE"));
                    result.setHashCode(Util.getColumnString(rs, "S_HASH_CODE"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
    
    public static Map getConfigSendToSftp(String serviceId, String fileType) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map result = new HashMap();
        try {
            conn = getConnection114Cdr();
            if(fileType.equals("onepay")){
                cs = conn.prepareCall(GET_PATH_EXPORT_FILE);
                cs.registerOutParameter(1, OracleTypes.CURSOR);
                cs.setString(2, serviceId);
                cs.setString(3, "");
                cs.execute();

                rs = (ResultSet) cs.getObject(1);
                while (rs != null && rs.next()) {
                    result.put("url", Config.getString("dispute-service-sftp.merchant.host", ""));
                    result.put("host", Util.getColumnString(rs, "S_HOST"));
                    result.put("port", Util.getColumnInteger(rs, "N_PORT"));
                    result.put("user", Util.getColumnString(rs, "S_USER"));
                    result.put("password", Util.getColumnString(rs, "S_PASSWORD"));
                    result.put("privateKey", Util.getColumnString(rs, "S_PRIVATE_KEY"));
                    result.put("path", Util.getColumnString(rs, "S_PATH"));
                    result.put("backup", Util.getColumnString(rs, "S_PATH_BACKUP"));
                    result.put("sourceID", Util.getColumnString(rs, "S_SOURCE_ID"));
                    result.put("serviceID", Util.getColumnString(rs, "S_SERVICE_ID"));
                }
            }
            else if(fileType.equals("accquirer")){
                cs = conn.prepareCall(GET_PATH_IMPORT_FILE);
                cs.registerOutParameter(1, OracleTypes.CURSOR);
                cs.setString(2, serviceId);
                cs.setString(3, "");
                cs.execute();

                rs = (ResultSet) cs.getObject(1);
                while (rs != null && rs.next()) {
                    result.put("url", Config.getString("dispute-service-sftp.merchant.host", ""));
                    result.put("host", Util.getColumnString(rs, "S_HOST"));
                    result.put("port", Util.getColumnInteger(rs, "N_PORT"));
                    result.put("user", Util.getColumnString(rs, "S_USER"));
                    result.put("password", Util.getColumnString(rs, "S_PASSWORD"));
                    result.put("privateKey", Util.getColumnString(rs, "S_PRIVATE_KEY"));
                    result.put("path", Util.getColumnString(rs, "S_FOLDER_SCAN"));
                    result.put("backup", Util.getColumnString(rs, "S_FOLDER_BACKUP"));
                    result.put("sourceID", Util.getColumnString(rs, "S_SOURCE_ID"));
                    result.put("serviceID", Util.getColumnString(rs, "S_SERVICE_ID"));
                }
            }
            else{
                cs = conn.prepareCall(GET_PATH_EXPORT_FILE);
                cs.registerOutParameter(1, OracleTypes.CURSOR);
                cs.setString(2, serviceId);
                cs.setString(3, "");
                cs.execute();

                rs = (ResultSet) cs.getObject(1);
                while (rs != null && rs.next()) {
                    result.put("url", Config.getString("dispute-service-sftp.merchant.host", ""));
                    result.put("host", Util.getColumnString(rs, "S_HOST"));
                    result.put("port", Util.getColumnInteger(rs, "N_PORT"));
                    result.put("user", Util.getColumnString(rs, "S_USER"));
                    result.put("password", Util.getColumnString(rs, "S_PASSWORD"));
                    result.put("privateKey", Util.getColumnString(rs, "S_PRIVATE_KEY"));
                    result.put("path", Util.getColumnString(rs, "S_PATH"));
                    result.put("backup", Util.getColumnString(rs, "S_PATH_BACKUP"));
                    result.put("sourceID", Util.getColumnString(rs, "S_SOURCE_ID"));
                    result.put("serviceID", Util.getColumnString(rs, "S_SERVICE_ID"));
                }
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer InsertTbCdrFile(String fileName, String acquirerID) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        Integer n_id = 0;
        ResultSet rs = null;
        try {
            con = getConnection114Cdr();
            cs = con.prepareCall(INSERT_TB_CDR_FILE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setString(2, "ONEPAY");
            cs.setString(3, acquirerID);
            cs.setTimestamp(4, new Timestamp(System.currentTimeMillis()));
            cs.setTimestamp(5, new Timestamp(System.currentTimeMillis()));
            cs.setTimestamp(6, new Timestamp(System.currentTimeMillis()));
            cs.setString(7, fileName);
            cs.setString(8, "");
            cs.setString(9, "");
            cs.setString(10, "");
            cs.setString(11, "EXPORT");
            cs.setString(12, "EXPORT");
            cs.execute();
            n_id = cs.getInt(1);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if(exception != null){
            throw exception;
        }
        return n_id;
    }

    public static Integer InsertTbDisputeCase(int dispute_id, String trans_id, String case_id, String sender, Integer file_id, String description, String acquirer_id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        Integer n_res = 0;
        ResultSet rs = null;
        try {
            // con = getConnection112Fraud();
            con = getConnection118Dispute();
            cs = con.prepareCall(INSERT_TB_DISPUTE_CASE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, dispute_id);
            cs.setString(3, trans_id);
            cs.setString(4, case_id);
            cs.setString(5, sender);
            cs.setInt(6, file_id);
            cs.setString(7, description);
            cs.setString(8, acquirer_id);
            cs.execute();
            n_res = cs.getInt(1);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if(exception != null || n_res == 0){
            throw exception;
        }
        return n_res;
    }

    public static List<JsonObject> getFilesHistory(String disputeId) throws Exception {
        Exception exception = null;
        Connection conn14 = null;
        Connection conn18 = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<JsonObject> listFileHistory = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");

            // conn12 = getConnection112();
            conn18 = getConnection118Dispute();
            cs = conn18.prepareCall(GET_FILE_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Integer.parseInt(disputeId));
            cs.execute();

            List<JsonObject> listDisputeCase = new ArrayList<>();
            List<String> lstFileId = new ArrayList<String>();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject disputeCase = new JsonObject();
                disputeCase.put("n_id", Util.getColumnInteger(rs, "N_ID"));
                disputeCase.put("n_dispute_id", Util.getColumnInteger(rs, "N_DISPUTE_ID"));
                disputeCase.put("transID", Util.getColumnString(rs, "S_TRANS_ID"));
                disputeCase.put("caseID", Util.getColumnString(rs, "S_CASE_ID"));
                disputeCase.put("sender", Util.getColumnString(rs, "S_SENDER"));
                disputeCase.put("fileID", Util.getColumnInteger(rs, "N_FILE_ID"));
                disputeCase.put("d_create", sdf.format(new Date(rs.getDate("D_CREATE").getTime())));
                disputeCase.put("accquirer", Util.getColumnString(rs, "S_ACQUIRER_ID"));
                disputeCase.put("note", Util.getColumnString(rs, "S_DESCRIPTION"));
                lstFileId.add(Util.getColumnInteger(rs, "N_FILE_ID").toString());
                listDisputeCase.add(disputeCase);
            }

            String inputFileIds = String.join(",", lstFileId);

            //get all file from tb_cdr_file, sau đó lọc theo nid của transaction này
            conn14 = getConnection114Cdr();
            cs = conn14.prepareCall(GET_CDR_FILES_BY_IDS);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setString(3, inputFileIds);
            cs.execute();

            List<JsonObject> listCdrFile = new ArrayList<>();
            rs = (ResultSet) cs.getObject(2);
            while (rs != null && rs.next()) {
                JsonObject cdrFile = new JsonObject();
                cdrFile.put("n_id",  Util.getColumnString(rs, "N_ID"));
                cdrFile.put("source", Util.getColumnString(rs, "S_SOURCE"));
                cdrFile.put("fileName", Util.getColumnString(rs, "S_FILE_NAME"));
                listCdrFile.add(cdrFile);
            }

            //lọc theo nid
            listDisputeCase.forEach(disputeCase -> {
                listCdrFile.forEach(cdrFile -> {
                    if(disputeCase.getInteger("fileID").toString().equals(cdrFile.getString("n_id"))){
                        JsonObject fileHistory = new JsonObject();
                        fileHistory.put("n_id", disputeCase.getInteger("n_id"));
                        fileHistory.put("n_dispute_id", disputeCase.getInteger("n_dispute_id"));
                        fileHistory.put("transID", disputeCase.getString("transID"));
                        fileHistory.put("caseID", disputeCase.getString("caseID"));
                        fileHistory.put("sender", disputeCase.getString("sender"));
                        fileHistory.put("fileID", disputeCase.getInteger("fileID"));
                        fileHistory.put("accquirer", disputeCase.getString("accquirer"));
                        fileHistory.put("note", disputeCase.getString("note"));
                        fileHistory.put("d_create", disputeCase.getString("d_create"));
                        fileHistory.put("fileName", cdrFile.getString("fileName"));
                        listFileHistory.add(fileHistory);
                    }
                });
            });

        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn14);
            closeConnectionDB(rs, null, cs, conn18);
        }
        if (exception != null)
            throw exception;
        return listFileHistory;
    }

    public static JsonObject getCdrAccquirerRequest(Map request) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<JsonObject> listCdr = new ArrayList<>();
        JsonObject jRes = new JsonObject();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmss");
            String from = request.get(FROM_DATE) != null ? request.get(FROM_DATE).toString() : null;
            String to = request.get(TO_DATE) != null ? request.get(TO_DATE).toString() : null;
            con = getConnection114Cdr();
            cs = con.prepareCall(GET_CDR_FILE_ACCQUIRER_REQUEST);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setString(3, request.get("serviceId").toString());
            cs.setString(4, request.get("type").toString());
            cs.setInt(5, Integer.parseInt(request.get("page").toString()));
            cs.setInt(6, Integer.parseInt(request.get("page_size").toString()));
            cs.setDate(7, from != null ? new java.sql.Date(sdf.parse(from).getTime()) : null);
            cs.setDate(8, to != null ? new java.sql.Date(sdf.parse(to).getTime()) : null);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);
            SimpleDateFormat sdf_output = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
            while (rs != null && rs.next()) {
                JsonObject cdrFile = new JsonObject();
                cdrFile.put("n_id",  Util.getColumnString(rs, "N_ID"));
                cdrFile.put("source", Util.getColumnString(rs, "S_SOURCE"));
                cdrFile.put("fileName", Util.getColumnString(rs, "S_FILE_NAME"));
                cdrFile.put("d_create", sdf_output.format(new Date(rs.getTimestamp("D_CREATE").getTime())));
                cdrFile.put("rn", Util.getColumnString(rs, "RN"));
                listCdr.add(cdrFile);
            }
            jRes.put("result", listCdr);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if(exception != null){
            throw exception;
        }
        return jRes;
    }

    public static List<CardListDto> getCardLists() throws Exception {
        CallableStatement cs = null;
        ResultSet rs = null;
        Connection con = null;
        List<CardListDto> result = new ArrayList<>();
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call G_CARD_LISTS(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();

            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);

            if (nResult != 200) {
                throw new Exception("DB get card list : " + sResult);
            }
            CardListDto dto;
            while (rs != null && rs.next()) {
                dto = new CardListDto();
                dto.setAcquirer_id(Util.getColumnString(rs, "acquirer_id"));
                dto.setAcquirer_short_name(Util.getColumnString(rs, "acquirer_name"));
                result.add(dto);
            }

        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }
}
