package vn.onepay.portal.resources.dispute.dto;

import java.sql.Timestamp;

public class DisputeEmailDto {
    private int id;
    private int disputeId;
    private String email;
    private String type;
    private Timestamp date;
    private String operator;

    public DisputeEmailDto() {

    }

    public DisputeEmailDto(int id, int disputeId, String email, String type, Timestamp date, String operator) {
        this.id = id;
        this.disputeId = disputeId;
        this.email = email;
        this.type = type;
        this.date = date;
        this.operator = operator;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(int disputeId) {
        this.disputeId = disputeId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

}
