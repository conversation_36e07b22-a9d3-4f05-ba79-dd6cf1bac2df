package vn.onepay.portal.resources.dispute.handler;

import com.opencsv.CSVWriter;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.queue.listener.DownloadQueueSlowInListener;
import vn.onepay.portal.resources.dispute.dao.SftpDao;
import vn.onepay.portal.resources.dispute.dto.DisputeSftp;
import vn.onepay.portal.resources.dispute.job.Sftp;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Random;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import org.apache.commons.io.FileUtils;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.logging.Logger;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.client.DisputeClient;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.UUID;
import vn.onepay.portal.Config;
import static vn.onepay.portal.Util.sendResponse;

public class SendToSftpHandler implements IConstants {

    private static final String[] ATM_HEADER = {"Case No", "Status", "Dispute Date", "Due Date", "Merchant ID", "Channel", "Order Reference (WON)", "Merchant Trans Ref ", "Trans ID", "Transaction Type", "Transaction date", "Card Number", "Issuer Banks", "Original Amount", "Currency", "Dispute Amount", "Dispute Currency", "Dispute Reason", "Remarks from OnePay", "Outcome", "Dispute file from Issuers"};
    private static final String[] INT_HEADER = {"Case No", "Status", "Dispute Date", "Due Date", "Merchant ID", "Channel", "Order Reference (WON)", "Merchant Trans Ref ", "Acquirer Transaction Reference Number", "Transaction Type", "Transaction date", "Card Number", "Card brand", "BIN Country", "Autho Code", "Original Amount", "Currency", "Dispute Amount", "Dispute Currency", "Dispute Stage", "Dispute Code", "Remarks from OnePay", "Outcome", "Dispute file from Issuers"};
    private final static Logger LOGGER = Logger.getLogger(DisputeHandler.class.getName());
    private static List<String[]> list = new ArrayList<>();


    public static void addDataRow(String[] data) {
        list.add(data);
    }

    public static void clearList() {
        list = new ArrayList<>();
    }
    public static String convertIntCard (String card){
        if(card.equalsIgnoreCase("VISA")) {
            return "Visa";
        }
        if(card.equalsIgnoreCase("AMEX")) {
            return "Amex";
        }
        if(card.equalsIgnoreCase("MASTERCARD")) {
            return "Mastercard";
        }
            return card;
    }
    // public static void SendToSftm(RoutingContext ctx) {
        // call detail
    //     JsonObject res = new JsonObject();
    //     ctx.vertx().executeBlocking(future -> {
    //         Integer result = 0;
    //         try {

    //             String email = ctx.request().getParam("email");
    //             String disputeId = ctx.request().getParam("disputeId");
    //             JsonObject disputeTemp = DisputeClient.searchDisputeById(disputeId);
    //             JsonArray disputeDetail = disputeTemp.getJsonArray("list");
    //             // TO DO
    //             // body bao gom ********
    //             // call sang api dispute detail => Lay dc ta
    //             clearList();
    //             String paygate = "INT";
    //             if (!disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("paygate").equalsIgnoreCase("QT")) {
    //                 paygate = "ATM";
    //                 addDataRow(ATM_HEADER);
    //             } else {
    //                 addDataRow(INT_HEADER);
    //             }
    //             String check = "No";
    //             if (!ctx.fileUploads().isEmpty()) {
    //                 check = "Yes";
    //             }
    //             JsonObject detail = disputeDetail.getJsonObject(disputeDetail.size() - 1);
    //             String transactionId = detail.getString("transactionId", "");
    //             String pattern = "dd/MM/yyyy hh:mm a";
    //             SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
    //             SimpleDateFormat simpleDateFormatBase = new SimpleDateFormat("MMM dd, YYYY, HH:mm:ss a"); 
    //             Date disputeDates = simpleDateFormatBase.parse(detail.getString("disputeDate", ""));
    //             String disputeDate =  simpleDateFormat.format(disputeDates);
    //             Date dueDates = simpleDateFormatBase.parse(detail.getString("dueDate", ""));
    //             String dueDate =  simpleDateFormat.format(dueDates);
    //             Date transactionDates = simpleDateFormatBase.parse(detail.getString("transactionDate", ""));
    //             String transactionDate =  simpleDateFormat.format(transactionDates);
    //             if (paygate.equalsIgnoreCase("ATM")) {
    //                 // ATM 20 Feild
    //                 String[] tempData = {
    //                         paygate + "_" + detail.getInteger("id", 0), // Case No
    //                         detail.getString("disputeStatus", " "), // Status
    //                         disputeDate, // Dispute Date
    //                         dueDate, // Due Date
    //                         detail.getString("merchantId", ""), // Merchant ID
    //                         paygate, // Channel
    //                         detail.getString("orderReference", ""), // Order Reference (WON)
    //                         detail.getString("merchantTransactionReference", ""), // Merchant Trans Ref
    //                         detail.getString("transactionId", ""), // Trans ID
    //                         detail.getString("transactionType", ""), // Transaction Type
    //                         transactionDate, // Transaction date
    //                         detail.getString("cardNumber", ""), // Card Number
    //                         convertIntCard(detail.getString("cardTypeDownload", "")), // Issuer Banks
    //                         detail.getDouble("transactionAmount", 0.0).toString(), // Original Amount
    //                         detail.getString("transactionCurrency", ""), // Original Currency
    //                         detail.getDouble("disputeAmount", 0.0).toString(), // Dispute Amount
    //                         detail.getString("disputeCurrency", ""), // Dispute Currency
    //                         detail.getString("reasonDownload", ""), // Dispute Reason
    //                         detail.getString("note", ""), // Remarks from OnePay
    //                         detail.getString("outcomeDownload", "(Blank)"), // Outcome
    //                         check // Dispute file from Issuers
    //                 };
    //                 addDataRow(tempData);
    //             } else {
    //                 // INT 24 Feild
    //                 String[] tempData = {
    //                         paygate + "_" + detail.getInteger("id", 0), // Case No
    //                         detail.getString("disputeStatus", " "), // Status
    //                         disputeDate, // Dispute Date
    //                         dueDate, // Due Date
    //                         detail.getString("merchantId", " "), // Merchant ID
    //                         "International", // Channel
    //                         detail.getString("orderReference", " "), // Order Reference (WON)
    //                         detail.getString("merchantTransactionReference", " "), // Merchant Trans Ref
    //                         detail.getString("networkTransactionId", " "), // Acquirer Transaction Reference Number
    //                         detail.getString("transactionType", " "), // Transaction Type
    //                         transactionDate, // Transaction date
    //                         detail.getString("cardNumber", " "), // Card Number
    //                         convertIntCard(detail.getString("cardType", " ")), // Card brand
    //                         detail.getString("binCountry", " "), // BIN Country
    //                         detail.getString("authorisationCode", " "), // Autho Code
    //                         detail.getDouble("transactionAmount", 0.0).toString(), // Original Amount
    //                         detail.getString("transactionCurrency", " "), // Original Currency
    //                         detail.getDouble("disputeAmount", 0.0).toString(), // Dispute Amount
    //                         detail.getString("disputeCurrency", " "), // Dispute Currency
    //                         detail.getString("stageDownload", " "), // Dispute Stage
    //                         detail.getString("codeDownload", " "), // Dispute Code
    //                         detail.getString("note", " "), // Remarks from OnePay
    //                         detail.getString("outcomeDownload", "(Blank)"), // Outcome
    //                         check // Dispute file from Issuers
    //                 };
    //                 addDataRow(tempData);
    //             }


    //             DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    //             LocalDateTime now = LocalDateTime.now();
    //             String fileName = dtf.format(now) + "_" + paygate + "_" + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantId") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("orderReference").replace(" ", "_") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantTransactionReference") 
    //             + (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode")!=null ? "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode") : "");
    //             String fileFormat = "zip";


    //             // default all fields are enclosed in double quotes
    //             // default separator is a comma

    //             Path zip = Paths.get(Config.getString("dispute-service.path_upload_file", "") + "zip/" + fileName);
    //             Path source = Paths.get(Config.getString("dispute-service.path_upload_file", "") + fileName);
    //             if (!Files.isDirectory(source)) {
    //                 Files.createDirectories(source);
    //             }

    //             try (FileWriter writer = new FileWriter(Config.getString("dispute-service.path_upload_file", "") + fileName + "/" + fileName + ".csv")) {
    //                 for (int index = 0; index <= list.size() - 1; index++) {
    //                     String temp = "";
    //                     for (int index2 = 0; index2 <= list.get(index).length - 1; index2++) {
    //                         if (index2 > 0) {
    //                             temp += "," + "\"" + list.get(index)[index2] + "\"";
    //                         } else {
    //                             temp += "\"" + list.get(index)[index2] + "\"";
    //                         }
    //                     }
    //                     writer.write(temp + "\n");

    //                 }
    //                 writer.close();
    //                 //upload file từ frontend lên đây (iportal-service)
    //                 DisputeHandler.uploadFileDispute(ctx, Config.getString("dispute-service.path_upload_file", "") + fileName);
    //                 try {

    //                     if (!Files.isDirectory(zip)) {
    //                         Files.createDirectories(zip);
    //                     }

    //                     String zipFileName = Config.getString("dispute-service.path_upload_file", "") + "zip/" + fileName + "/" + source.getFileName().toString() + ".zip";
    //                     zipFolder(source, zipFileName);
    //                     //upload file sftp lên con 22
    //                     int checkup = uploadNotification(Config.getString("dispute-service.path_upload_file", "") + "zip/" + fileName, paygate, email);
    //                     if (checkup == 1) {
    //                         File removeFile = new File(Config.getString("dispute-service.path_upload_file", "") + fileName + "/" + fileName + ".csv");
    //                         File removeZipFile = new File(Config.getString("dispute-service.path_upload_file", "") + "zip/" + fileName + "/" + fileName + ".zip");
    //                         //xóa file upload từ frontend lên iportal-service
    //                         if (removeFile.delete() && removeZipFile.delete()) {
    //                             System.out.println("Deleted the file: " + removeFile.getName());
    //                         } else {
    //                             System.out.println("Failed to delete the file.");
    //                         }
    //                         Files.delete(zip);
    //                         File directory = new File(Config.getString("dispute-service.path_upload_file", "") + fileName);
    //                         FileUtils.cleanDirectory(directory);
    //                         Files.delete(source);
    //                     }
    //                 } catch (IOException e) {
    //                     res.put("status", "failed");
    //                     result = 500;
    //                     e.printStackTrace();
    //                 }

    //                 System.out.println("Done");
    //                 res.put("status", "success");
    //                 result = 200;
    //             }
    //         } catch (Exception e) {
    //             LOGGER.log(Level.WARNING, "Send to sftp Error: ", e);
    //             res.put("status", "failed");
    //             result = 500;
    //             ctx.fail(e);
    //         } finally {
    //             if (result != 500)
    //                 sendResponse(ctx, result, res);
    //         }

    //     }, false, null);
    // }

    // public static void download(RoutingContext ctx) {
    //     JsonObject res = new JsonObject();
    //     ctx.vertx().executeBlocking(future -> {
    //         try {
    //             String URL = Config.getString("dispute-service.path_upload_file", "") + "download/";
    //             String disputeId = ctx.request().getParam("disputeId");
    //             JsonObject disputeTemp = DisputeClient.searchDisputeById(disputeId);
    //             JsonArray disputeDetail = disputeTemp.getJsonArray("list");
    //             List<Long> listFileName = new ArrayList<>();
    //             List<String> listFileNameStr = new ArrayList<>();

    //             String fileFormat = "zip";
    //             String host = Config.getString("dispute-service-sftp.merchant.host", "");
    //             int port = Config.getInteger("dispute-service-sftp.merchant.port", 0);
    //             String user = Config.getString("dispute-service-sftp.merchant.user", "");
    //             String password = Config.getString("dispute-service-sftp.merchant.pass", "");
    //             String privateKey = Config.getString("dispute-service-sftp.merchant.privateKey", "");
    //             String folderFrom = Config.getString("dispute-service-sftp.merchant.folder", "");
    //             String backup = Config.getString("backup", "");
    //             String folderBackup = Config.getString("folder_backup", "");
    //             if (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("paygate").equalsIgnoreCase("QT")) {
    //                 folderFrom += "/INT_DisputeResponse_FromMerchant";
    //             } else {
    //                 folderFrom += "/ATM_DisputeResponse_FromMerchant";
    //             }
    //             listFileNameStr = Sftp.getSftpFileName(host, port, user, password, privateKey, folderFrom, URL, folderBackup, fileFormat);


    //             String fileName = "_" + (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("paygate").equals("QT") ? "INT" : "ATM") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantId") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("orderReference").replace(" ", "_") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantTransactionReference")
    //             + (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode")!=null ? "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode") : "")+".zip";
    //             File folderDir = new File(URL);
    //             // File[] listOfFiles = folderDir.listFiles();
    //             for (int i = 0; i < listFileNameStr.size(); i++) {
    //                 if (listFileNameStr.get(i).endsWith("zip")) {
    //                     if (listFileNameStr.get(i).endsWith(fileName)) {
    //                         listFileName.add(Long.parseLong(listFileNameStr.get(i).split("_")[0]));
    //                     }
    //                 }
    //             }
    //             if (listFileName.size() > 0) {
    //                 Long maxTime = Collections.max(listFileName);
    //                 String nameFileCurrent = String.valueOf(maxTime) + fileName;
    //                 Sftp.getSftpOneFile(host, port, user, password, privateKey, folderFrom, URL, folderBackup, fileFormat, nameFileCurrent);
    //                 ctx.response().setStatusMessage(nameFileCurrent).sendFile(URL + nameFileCurrent, (result) -> {
    //                     if (result.succeeded()) {
    //                         try {
    //                             FileUtils.cleanDirectory(folderDir);
    //                         } catch (IOException e) {
    //                             e.printStackTrace();
    //                         }
    //                     }
    //                 });
    //             } else {
    //                 res.put("fileName", "File Not found");
    //             }


    //         } catch (Exception e) {

    //         } finally {
    //             sendResponse(ctx, 200, res);
    //         }
    //     }, false, null);
    // }


    // public static void getFileNameDispute(RoutingContext ctx) {
    //     JsonObject res = new JsonObject();
    //     ctx.vertx().executeBlocking(future -> {
    //         try {
    //             String URL = Config.getString("dispute-service.path_upload_file", "") + "download/";
    //             String disputeId = ctx.request().getParam("disputeId");
    //             JsonObject disputeTemp = DisputeClient.searchDisputeById(disputeId);
    //             JsonArray disputeDetail = disputeTemp.getJsonArray("list");
    //             List<Long> listFileName = new ArrayList<>();
    //             List<String> listFileNameStr = new ArrayList<>();
    //             Path temp = Paths.get(Config.getString("dispute-service.path_upload_file", "") + "download");
    //             if (!Files.isDirectory(temp)) {
    //                 Files.createDirectories(temp);
    //             }else{
    //                 File directory = new File(temp.toString());
    //                 FileUtils.cleanDirectory(directory);
    //             }
    //             String fileFormat = "zip";
    //             String host = Config.getString("dispute-service-sftp.merchant.host", "");
    //             int port = Config.getInteger("dispute-service-sftp.merchant.port", 0);
    //             String user = Config.getString("dispute-service-sftp.merchant.user", "");
    //             String password = Config.getString("dispute-service-sftp.merchant.pass", "");
    //             String privateKey = Config.getString("dispute-service-sftp.merchant.privateKey", "");
    //             String folderFrom = Config.getString("dispute-service-sftp.merchant.folder", "");
    //             String backup = Config.getString("backup", "");
    //             String folderBackup = Config.getString("folder_backup", "");
    //             if (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("paygate").equalsIgnoreCase("QT")) {
    //                 folderFrom += "/INT_DisputeResponse_FromMerchant";
    //             } else {
    //                 folderFrom += "/ATM_DisputeResponse_FromMerchant";
    //             }
    //             listFileNameStr = Sftp.getSftpFileName(host, port, user, password, privateKey, folderFrom, URL, folderBackup, fileFormat);


    //             String fileName = "_" + (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("paygate").equals("QT") ? "INT" : "ATM") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantId") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("orderReference").replace(" ", "_") + "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("merchantTransactionReference") 
    //             + (disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode")!=null ? "_" 
    //             + disputeDetail.getJsonObject(disputeDetail.size() - 1).getString("authorisationCode") : "")+ ".zip";
    //             // File[] listOfFiles = folderDir.listFiles();
    //             for (int i = 0; i < listFileNameStr.size(); i++) {
    //                 if (listFileNameStr.get(i).endsWith("zip")) {
    //                     if (listFileNameStr.get(i).endsWith(fileName)) {
    //                         listFileName.add(Long.parseLong(listFileNameStr.get(i).split("_")[0]));
    //                     }
    //                 }
    //             }
    //             if (listFileName.size() > 0) {
    //                 Long maxTime = Collections.max(listFileName);
    //                 String nameFileCurrent = String.valueOf(maxTime) + fileName;
    //                 Sftp.getSftpOneFile(host, port, user, password, privateKey, folderFrom, URL, folderBackup, fileFormat, nameFileCurrent);
    //                 res.put("fileName", nameFileCurrent);

    //             } else {
    //                 res.put("fileName", "File Not found");
    //             }


    //         } catch (Exception e) {

    //         } finally {
    //             sendResponse(ctx, 200, res);
    //         }
    //     }, false, null);
    // }

    public static Integer uploadNotification(String folderFrom, String paygate, String email) {
        try {
            // JsonObject body = ctx.getBodyAsJson();
            String folderMerchant = "";
            String fileFormatMerchant = "zip";
            // config iportal
            String hostMerchant = Config.getString("dispute-service-sftp.merchant.host", "");
            int portMerchant = Config.getInteger("dispute-service-sftp.merchant.port", 0);
            String userMerchant = Config.getString("dispute-service-sftp.merchant.user", "");
            String passwordMerchant = Config.getString("dispute-service-sftp.merchant.pass", "");
            String privateKeyMerchant = Config.getString("dispute-service-sftp.merchant.privateKey", "");
            String folderToMerchant = Config.getString("dispute-service-sftp.merchant.folder", "");
            String backupMerchant = Config.getString("backup", "");
            String folderBackupMerchant = Config.getString("folder_backup", "");
            String fileFormat = "zip";
            // config iportal
            String host = Config.getString("dispute-service-sftp.onepay.host", "");
            int port = Config.getInteger("dispute-service-sftp.onepay.port", 0);
            String user = Config.getString("dispute-service-sftp.onepay.user", "");
            String password = Config.getString("dispute-service-sftp.onepay.pass", "");
            String privateKey = Config.getString("dispute-service-sftp.onepay.privateKey", "");
            String folderTo = Config.getString("dispute-service-sftp.onepay.folder", "");
            String backup = Config.getString("backup", "");
            String folderBackup = Config.getString("folder_backup", "");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        LOGGER.info("Service Dispute Upload");
                        LOGGER.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                InputStream fileStream1 = new FileInputStream(file);
                                // InputStream fileStream2 = new FileInputStream(file);
                                if (paygate.equalsIgnoreCase("ATM")) {
                                    folderTo += "/ATM_DisputeNotification_FromOnePay";
                                    folderToMerchant += "/ATM_DisputeNotification_FromOnePay";
                                } else {
                                    folderTo += "/INT_DisputeNotification_FromOnePay";
                                    folderToMerchant += "/INT_DisputeNotification_FromOnePay";
                                }
                                if (Sftp.pushSftp(host, port, user, password, privateKey, folderTo, backup, file.getName(), fileStream1, null)) {
                                    DisputeSftp dispute = new DisputeSftp();
                                    dispute.setFile_name(file.getName());
                                    dispute.setEmail_operator(email);
                                    SftpDao.InsertDisputeSftp(dispute);
                                } ;

                                fileStream1.close();
                                // fileStream2.close();
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    OutputStream outStream = new FileOutputStream(fileBackup);
                                    file.delete();
                                    try {
                                        if (outStream != null)
                                            outStream.close();
                                    } catch (IOException e) {
                                        LOGGER.log(Level.SEVERE, "", e);
                                    }
                                }
                                try {
                                    if (fileStream1 != null)
                                        fileStream1.close();
                                    // if (fileStream2 != null)
                                    // fileStream2.close();
                                } catch (IOException e) {
                                    LOGGER.log(Level.SEVERE, "", e);
                                }
                            } catch (Exception e) {// Catch exception if any
                                LOGGER.log(Level.SEVERE, "", e);
                            }
                        } else {
                            LOGGER.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                LOGGER.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "", ex);
        } finally {
            return 1;
        }

    }

    public static Integer uploadFileToMerchantConfig(String folderFrom, Map merchantConfig) {
        try {
            // JsonObject body = ctx.getBodyAsJson();
            // String fileFormat = "zip";
            // config iportal
            String host = merchantConfig.get("host").toString();
            int port = (Integer)merchantConfig.get("port");
            String user = merchantConfig.get("user").toString();
            String password = merchantConfig.get("password").toString();
            String privateKey = "";
            if(merchantConfig.get("privateKey") != null){
                privateKey = merchantConfig.get("privateKey").toString();
            }
            String folderTo = merchantConfig.get("path").toString();
            String backup = merchantConfig.get("backup").toString();
            String folderBackup = merchantConfig.get("backup").toString();
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        LOGGER.info("Service Dispute Upload");
                        // LOGGER.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile("");
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                InputStream fileStream1 = new FileInputStream(file);
                                //file2 is for backup folder
                                InputStream fileStream2 = new FileInputStream(file);
                                //send file to merchant folder and onepay's backup folder
                                if (Sftp.pushSftp(host, port, user, password, privateKey, folderTo, backup, file.getName(), fileStream1, fileStream2)) {
                                    DisputeSftp dispute = new DisputeSftp();
                                    dispute.setFile_name(file.getName());
                                    // dispute.setEmail_operator(email);
                                    // SftpDao.InsertDisputeSftp(dispute);
                                }

                                fileStream2.close();
                                fileStream1.close();
                                // fileStream2.close();
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    OutputStream outStream = new FileOutputStream(fileBackup);
                                    file.delete();
                                    try {
                                        if (outStream != null)
                                            outStream.close();
                                    } catch (IOException e) {
                                        LOGGER.log(Level.SEVERE, "", e);
                                    }
                                }
                                try {
                                    if (fileStream1 != null)
                                        fileStream1.close();
                                    if (fileStream2 != null){
                                        fileStream2.close();
                                    }
                                } catch (IOException e) {
                                    LOGGER.log(Level.SEVERE, "", e);
                                }
                            } catch (Exception e) {// Catch exception if any
                                LOGGER.log(Level.SEVERE, "", e);
                            }
                        } else {
                            LOGGER.info("file not match format");
                        }
                    }
                }
            } else {
                LOGGER.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "", ex);
        } finally {
            return 1;
        }

    }

    // zip a directory, including sub files and sub directories
    public static void  zipFolder(Path source, String zipFileName) throws IOException {
        byte[] buffer = new byte[2000 * 1024];
        try {
            ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFileName));


            for (int i = 0; i < source.toFile().list().length; i++) {
                File srcFile = source.toFile().listFiles()[i];
                FileInputStream fis = new FileInputStream(srcFile);
                zos.putNextEntry(new ZipEntry(srcFile.getName()));



                int len;
                while ((len = fis.read(buffer)) >= 0) {
                    zos.write(buffer, 0, len);
                }
                zos.closeEntry();
                fis.close();


                System.out.printf("Zip file : %s%n", srcFile.getName());
            }
            // close the ZipOutputStream
            zos.close();


        } catch (IOException ioe) {
            System.out.println("Error creating zip file: ");
        }



    }
}
