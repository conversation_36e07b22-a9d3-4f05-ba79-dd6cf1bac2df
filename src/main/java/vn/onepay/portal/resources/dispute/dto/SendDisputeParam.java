package vn.onepay.portal.resources.dispute.dto;

public class SendDisputeParam {
    private int id;
    private String disputeSender;
    private String onepayPic;
    private String sendDisputeTo;
    private String merchantRespond;
    private String businessCategory;
    private String merchantGroup;
    private String disputeStage;
    private String disputeReason;
    private String disputeCode;
    private Double disputedAmount;
    private String disputeCurrency;
    private String dueDate;
    private String outcome;
    private String refNumber;
    private String note;
    private String operator;
    private int parentId;

    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public String getDisputeSender() {
        return disputeSender;
    }
    public void setDisputeSender(String disputeSender) {
        this.disputeSender = disputeSender;
    }
    public String getOnepayPic() {
        return onepayPic;
    }
    public void setOnepayPic(String onepayPic) {
        this.onepayPic = onepayPic;
    }
    public String getSendDisputeTo() {
        return sendDisputeTo;
    }
    public void setSendDisputeTo(String sendDisputeTo) {
        this.sendDisputeTo = sendDisputeTo;
    }
    public String getMerchantRespond() {
        return merchantRespond;
    }
    public void setMerchantRespond(String merchantRespond) {
        this.merchantRespond = merchantRespond;
    }
    public String getBusinessCategory() {
        return businessCategory;
    }
    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }
    public String getMerchantGroup() {
        return merchantGroup;
    }
    public void setMerchantGroup(String merchantGroup) {
        this.merchantGroup = merchantGroup;
    }
    public String getDisputeStage() {
        return disputeStage;
    }
    public void setDisputeStage(String disputeStage) {
        this.disputeStage = disputeStage;
    }
    public String getDisputeReason() {
        return disputeReason;
    }
    public void setDisputeReason(String disputeReason) {
        this.disputeReason = disputeReason;
    }
    public String getDisputeCode() {
        return disputeCode;
    }
    public void setDisputeCode(String disputeCode) {
        this.disputeCode = disputeCode;
    }
    public Double getDisputedAmount() {
        return disputedAmount;
    }
    public void setDisputedAmount(Double disputedAmount) {
        this.disputedAmount = disputedAmount;
    }
    public String getDisputeCurrency() {
        return disputeCurrency;
    }
    public void setDisputeCurrency(String disputeCurrency) {
        this.disputeCurrency = disputeCurrency;
    }
    public String getDueDate() {
        return dueDate;
    }
    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }
    public String getOutcome() {
        return outcome;
    }
    public void setOutcome(String outcome) {
        this.outcome = outcome;
    }
    public String getRefNumber() {
        return refNumber;
    }
    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber;
    }
    public String getNote() {
        return note;
    }
    public void setNote(String note) {
        this.note = note;
    }
    public String getOperator() {
        return operator;
    }
    public void setOperator(String operator) {
        this.operator = operator;
    }
    public int getParentId() {
        return parentId;
    }
    public void setParentId(int parentId) {
        this.parentId = parentId;
    }
    
}
