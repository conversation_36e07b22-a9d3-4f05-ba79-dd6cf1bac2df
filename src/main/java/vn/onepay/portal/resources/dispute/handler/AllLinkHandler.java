package vn.onepay.portal.resources.dispute.handler;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.client.QuicklinkClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.utils.ParamsPool;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class AllLinkHandler implements IConstants {
        private static Logger logger = Logger.getLogger(AllLinkHandler.class.getName());
        private static final Gson gson = new Gson();

        public static void list(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                String partner = ctx.request().getParam(ParamsPool.PARTNER) == null ? ""
                                                : String.valueOf(ctx.request().getParam(ParamsPool.PARTNER));
                                int page = ctx.request().getParam(ParamsPool.PAGE) == null ? 0
                                                : Integer.valueOf(ctx.request().getParam(ParamsPool.PAGE));
                                int pageSize = ctx.request().getParam("pageSize") == null ? 20
                                                : Integer.valueOf(ctx.request().getParam("pageSize"));
                                String keywords = ctx.request().getParam(ParamsPool.KEY_WORDS) == null ? ""
                                                : String.valueOf(ctx.request().getParam(ParamsPool.KEY_WORDS));
                                String state = ctx.request().getParam(ParamsPool.STATE) == null ? ""
                                                : String.valueOf(ctx.request().getParam(ParamsPool.STATE));
                                String installmentMerchantId = ctx.request()
                                                .getParam(ParamsPool.INSTALLMENT_MERC_ID) == null ? ""
                                                                : String.valueOf(ctx.request().getParam(
                                                                                ParamsPool.INSTALLMENT_MERC_ID));
                                String paynowMerchantId = ctx.request().getParam(ParamsPool.PAYNOW_MERC_ID) == null ? ""
                                                : String.valueOf(ctx.request().getParam(ParamsPool.PAYNOW_MERC_ID));
                                String email = ctx.request().getParam(ParamsPool.EMAIL) == null ? ""
                                                : String.valueOf(ctx.request().getParam(ParamsPool.EMAIL));
                                String fromDate = ctx.request().getParam("fromDate");
                                String toDate = ctx.request().getParam("toDate");

                                Map<String, Object> mapBody = new HashMap<>();
                                mapBody.put("page", page);
                                mapBody.put("pageSize", pageSize);
                                mapBody.put("keyword", keywords);
                                mapBody.put("state", state);
                                mapBody.put("installmentMerchantId", installmentMerchantId);
                                mapBody.put("paynowMerchantId", paynowMerchantId);
                                mapBody.put("email", email);
                                mapBody.put("fromDate", fromDate);
                                mapBody.put("toDate", toDate);
                                mapBody.put("partner", partner);
                                sendResponse(ctx, 200, QuicklinkClient.getAllLink(mapBody));
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }
                }, false, null);
        }

        public static void download(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                JsonObject dataParam = ctx.getBodyAsJson();
                                String partner = dataParam.getString(ParamsPool.PARTNER) == null ? "" : dataParam.getString(ParamsPool.PARTNER);
                                String page = dataParam.getString(ParamsPool.PAGE) == null ? "0" : String.valueOf(dataParam.getString(ParamsPool.PAGE));
                                String pageSize = dataParam.getString("pageSize") == null ? "50" : String.valueOf(dataParam.getString("pageSize"));
                                String keywords = dataParam.getString(ParamsPool.KEY_WORDS) == null ? "" : String.valueOf(dataParam.getString(ParamsPool.KEY_WORDS));
                                String state = dataParam.getString(ParamsPool.STATE) == null ? "" : String.valueOf(dataParam.getString(ParamsPool.STATE));
                                String installmentMerchantId = dataParam.getString(ParamsPool.INSTALLMENT_MERC_ID) == null ? "" : String.valueOf(dataParam.getString(ParamsPool.INSTALLMENT_MERC_ID));
                                String paynowMerchantId = dataParam.getString(ParamsPool.PAYNOW_MERC_ID) == null ? "" : String.valueOf(dataParam.getString(ParamsPool.PAYNOW_MERC_ID));
                                String email = dataParam.getString(ParamsPool.EMAIL) == null ? "" : String.valueOf(dataParam.getString(ParamsPool.EMAIL));
                                String fromDate = dataParam.getString("fromDate");
                                String toDate = dataParam.getString("toDate");
                                Map<String, Object> mapBody = new HashMap<>();
                                mapBody.put("page", page);
                                mapBody.put("pageSize", pageSize);
                                mapBody.put("keyword", keywords);
                                mapBody.put("state", state);
                                mapBody.put("installmentMerchantId", installmentMerchantId);
                                mapBody.put("paynowMerchantId", paynowMerchantId);
                                mapBody.put("email", email);
                                mapBody.put("fromDate", fromDate);
                                mapBody.put("toDate", toDate);
                                mapBody.put("partner", partner);

                                Map<String, Object> requestData = new HashMap<>();
                                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                                String fileName = "quicklink" + "_" + "alllink" + "_";

                                long date = new java.util.Date().getTime();
                                fileName += date;
                                mapBody.put("fileName", fileName);
                                String fileHashName = "";
                                try {
                                        fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                                        ctx.fail(e);
                                }
                                requestData.put(ParamsPool.FILE_HASH_NAME, fileHashName);
                                requestData.put(ParamsPool.FILE_NAME, fileName);
                                requestData.put(ParamsPool.FILE_EXT, "xlsx");
                                FileDownloadDto fileDownload = new FileDownloadDto();
                                fileDownload.setUser(requestData.get(X_USER_ID).toString());
                                fileDownload.setFile_type("quicklink_paymentlink");
                                fileDownload.setExt("xlsx");
                                fileDownload.setFile_name(fileName);
                                fileDownload.setFile_hash_name(fileHashName);
                                fileDownload.setConditions(gson.toJson(mapBody));
                                FileDownloadDao.insert(fileDownload);
                                QueueProducer.sendMessage(new Message(mapBody, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));

                                sendResponse(ctx, 200, fileDownload);
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }

                }, false, null);
        }

        public static void detail(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                String linkId = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                                sendResponse(ctx, 200, QuicklinkClient.getAllLinkDetail(linkId));
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }
                }, false, null);
        }

        public static void merchantProfileId(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                sendResponse(ctx, 200, QuicklinkClient.getMerchantProfileById());
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }
                }, false, null);
        }

        public static void merchantIdByProfile(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                String merchantId = "ALL";
                                String payMethod = ctx.request().getParam(ParamsPool.PAYMETHOD) == null ? "" : String.valueOf(ctx.request().getParam(ParamsPool.PAYMETHOD));
                                sendResponse(ctx, 200, QuicklinkClient.getMerchantIdByProfile(merchantId, payMethod));
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }
                }, false, null);
        }

        public static void merchantProfile(RoutingContext ctx) {
                ctx.vertx().executeBlocking(future -> {
                        try {
                                sendResponse(ctx, 200, QuicklinkClient.getAllMerchantProfile());
                        } catch (Exception e) {
                                logger.log(Level.WARNING, "GET ALL MERCHANT NAME: ", e);
                                ctx.fail(e);
                        }
                }, false, null);
        }
}
