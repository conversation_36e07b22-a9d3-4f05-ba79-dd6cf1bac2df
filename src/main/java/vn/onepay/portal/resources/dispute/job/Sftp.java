package vn.onepay.portal.resources.dispute.job;

import com.jcraft.jsch.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Sftp {
    private static Log logger = LogFactory.getLog(Sftp.class.getName());

    public static boolean pushSftp(String host, int port, String user, String pass, String privateKey, String folderExport, 
        String backupFolderExport, String fileName, InputStream data, InputStream dataBackup) throws Exception {
        JSch jsch = new JSch();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        //logger.info("sftpChannel.cd(folderExport="+folderExport+")");
        sftpChannel.cd(getFullPath(folderExport, ""));
        sftpChannel.cd(folderExport);
        sftpChannel.put(data, fileName, ChannelSftp.OVERWRITE);

        //send to backup folder onpay
        if(dataBackup != null){
            sftpChannel.cd(getFullPath(backupFolderExport, ""));
            sftpChannel.cd(backupFolderExport);
            sftpChannel.put(dataBackup, fileName, ChannelSftp.OVERWRITE);
        }

        sftpChannel.disconnect();
        session.disconnect();
        return true;
    }

    public static boolean getSftp(String host, int port, String user, String pass, String privateKey, String folderFrom, String folderTo, String folderBackup, String fileFormat) throws Exception {
        JSch jsch = new JSch();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        try {
            scanFolder(sftpChannel, getFullPath(folderFrom, ""), folderTo, folderBackup, fileFormat);
        } catch (Exception ex) {
            logger.error(ex);
        }
        sftpChannel.disconnect();
        session.disconnect();
        return true;
    }
    public static boolean getSftpOneFile(String host, int port, String user, String pass, String privateKey, String folderFrom, String folderTo, String folderBackup, String fileFormat, String fileName) throws Exception {
        JSch jsch = new JSch();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        try {
            scanFolderOneFile(sftpChannel, getFullPath(folderFrom, ""), folderTo, folderBackup, fileFormat, fileName);
        } catch (Exception ex) {
            logger.error(ex);
        }
        sftpChannel.disconnect();
        session.disconnect();
        return true;
    }
    public static List<String> getSftpFileName(String host, int port, String user, String pass, String privateKey, String folderFrom, String folderTo, String folderBackup, String fileFormat) throws Exception {
        JSch jsch = new JSch();
        List<String> listNameFile = new ArrayList<>();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        try {
            listNameFile= scanFolderGetListFileName(sftpChannel, getFullPath(folderFrom, ""), folderTo, folderBackup, fileFormat);
        } catch (Exception ex) {
            logger.error(ex);
        }
        sftpChannel.disconnect();
        session.disconnect();
        return listNameFile;
    }

    public static void scanFolder(ChannelSftp sftpChannel, String foldFrom, String folderTo, String folderBackup, String fileFormat) throws Exception {
        sftpChannel.cd(foldFrom);
        Vector<ChannelSftp.LsEntry> list = sftpChannel.ls("*");
        for (ChannelSftp.LsEntry entry : list) {
            final String fileName = entry.getFilename();
            if (entry.getAttrs().isDir()) {
                scanFolder(sftpChannel, entry.getFilename(), folderTo, folderBackup, fileFormat);
            } else {
                Pattern pattern = Pattern.compile(fileFormat);
                Matcher matcher = pattern.matcher(entry.getFilename());
                if (matcher.find()) {
                    sftpChannel.get(fileName, getFullPath(folderTo, fileName));
                    if (folderBackup != null && !"".equalsIgnoreCase(folderBackup)) {
                        sftpChannel.rename(fileName, getFullPath(folderBackup, fileName));
                    }
                }
            }
        }
        sftpChannel.cd("..");
    }
    public static void scanFolderOneFile(ChannelSftp sftpChannel, String foldFrom, String folderTo, String folderBackup, String fileFormat, String fileName) throws Exception {
        sftpChannel.cd(foldFrom);
        Vector<ChannelSftp.LsEntry> list = sftpChannel.ls("*");
        for (ChannelSftp.LsEntry entry : list) {
            // final String fileName = entry.getFilename();
            if (entry.getAttrs().isDir()) {
                scanFolder(sftpChannel, entry.getFilename(), folderTo, folderBackup, fileFormat);
            } else {
                Pattern pattern = Pattern.compile(fileFormat);
                Matcher matcher = pattern.matcher(entry.getFilename());
                if (matcher.find()) {
                    sftpChannel.get(fileName, getFullPath(folderTo, fileName));
                    if (folderBackup != null && !"".equalsIgnoreCase(folderBackup)) {
                        sftpChannel.rename(fileName, getFullPath(folderBackup, fileName));
                    }
                }
            }
        }
        sftpChannel.cd("..");
    }
    public static List<String> scanFolderGetListFileName(ChannelSftp sftpChannel, String foldFrom, String folderTo, String folderBackup, String fileFormat) throws Exception {
        sftpChannel.cd(foldFrom);
        List<String> listFileName = new ArrayList<>();
        Vector<ChannelSftp.LsEntry> list = sftpChannel.ls("*");
        for (ChannelSftp.LsEntry entry : list) {
            final String fileName = entry.getFilename();
            listFileName.add(fileName);
            // if (entry.getAttrs().isDir()) {
            //     scanFolder(sftpChannel, entry.getFilename(), folderTo, folderBackup, fileFormat);
            // } else {
            //     Pattern pattern = Pattern.compile(fileFormat);
            //     Matcher matcher = pattern.matcher(entry.getFilename());
            //     if (matcher.find()) {
            //         sftpChannel.get(fileName, getFullPath(folderTo, fileName));
            //         if (folderBackup != null && !"".equalsIgnoreCase(folderBackup)) {
            //             sftpChannel.rename(fileName, getFullPath(folderBackup, fileName));
            //         }
            //     }
            // }
        }
        sftpChannel.cd("..");

        return listFileName;
    }

    private static String getFullPath(String path, String fileName) {
        String startSeparator = path.startsWith("/") ? "" : "/";
        String endSeparator = path.endsWith("/") ? "" : "/";
        return startSeparator + path + endSeparator + fileName;
    }
}