package vn.onepay.portal.resources.dispute.handler;


import static vn.onepay.portal.Util.sendResponse;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MspClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.dispute.dao.DisputeDao;
import vn.onepay.portal.resources.dispute.dto.MerchantData;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.UserProfileDto;

public class DisputeRefundHandler implements IConstants {
    private static final Logger logger = Logger
            .getLogger(DisputeRefundHandler.class.getName());

    public static void refundDispute(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer userId = ctx.get(X_USER_ID);
                String userEmail;
                JsonObject body = ctx.getBodyAsJson();
                if (userId == null) {
                    logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                } else {
                    try {
                        UserProfileDto userProfile = UserDao.getProfileInfo(userId);
                        userEmail = userProfile.getEmail();
                    } catch (Exception e) {
                        logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] => ERROR GETTING USER PROFILE", e);
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                }
                if (userEmail == null) {
                    logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] => USER EMAIL EMPTY");
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                Double amount = body.getDouble("amount");
                if (amount == null) {
                    logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] =>  AMOUNT EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                Map<String, String> mIn = new HashMap();

                String refundRef;
                String requestId = xRequestId;

                // generate refund reference
                if (body.getString("refund_reference") == null || body.getString("refund_reference").isEmpty()) {
                    // Init refund reference
                    refundRef = body.getString("merchant_id") + "_" + requestId;
                    if (refundRef.length() > 32) {
                        int largerInt = refundRef.length() - 32 + 1;
                        requestId = requestId.substring(0, requestId.length() - largerInt);
                        refundRef = body.getString("merchant_id") + "_" + requestId;
                    }
                } else {
                    // Get refund reference
                    refundRef = body.getString("refund_reference");
                }

                String merchantId = body.getString("merchant_id");
                mIn.put("vpc_Merchant", merchantId);
                
                mIn.put("vpc_MerchTxnRef", refundRef); 
                mIn.put("vpc_OrgMerchTxnRef", body.getString("merchant_trans_ref"));
                
                mIn.put("vpc_Amount", Convert.toString(amount * 100, "0"));
                mIn.put("vpc_Note", "");
                mIn.put("vpc_DisputeReason", body.getString("dispute_reason"));
                mIn.put("vpc_Operator", userEmail);
                
                mIn.put("vpc_Command", body.getString("command"));

                mIn.put("vpc_Version", "2");
                mIn.put("vpc_Dispute", "y");
                mIn.put("vpc_Direct", "n");

                logger.info("vpc_Dispute: " + mIn.get("vpc_Dispute"));
                logger.info("vpc_Direct: " + mIn.get("vpc_Direct"));
                logger.info("vpc_OrgMerchTxnRef: " + mIn.get("vpc_OrgMerchTxnRef"));
                logger.info("vpc_MerchTxnRef: " + mIn.get("vpc_MerchTxnRef"));
                logger.info("vpc_Operator: " + mIn.get("vpc_Operator"));

                MerchantData merchantData = DisputeDao.getMerchantMspData(merchantId);
                String merchantKey = merchantData.getHashCode();
                if (merchantKey == null) {
                    logger.log(Level.WARNING, "Get Merchant MSP hashcode error: " + merchantId);
                    sendResponse(ctx, 500, new JsonObject());
                    return;
                }

                String secureHash = Util.createMerchantHash(mIn, merchantKey,  "2");
                mIn.put("vpc_SecureHash", secureHash);

                JsonObject mspResponse = MspClient.refundDispute(mIn);
                String mspMessage = mspResponse.getString("vpc_Message");
                String mspResponseCode = mspResponse.getString("vpc_TxnResponseCode") + "";
                logger.log(Level.INFO, String.format("MspClient.refundDispute response: vpc_Message=%s | vpc_TxnResponseCode=%s"
                    , mspMessage, mspResponseCode)); 

                int statusCode;

                if ("0".equals(mspResponseCode)) {
                    //ep dong bo refund -> his
                    synchronizeDataHistoryRefundDispute();
                    
                    // ep dong bo dispute
                    synchronizeDataDispute();
                    statusCode = 200;
                } else if ("4".equals(mspResponseCode)) {
                    statusCode = 400;
                } else if ("15".equals(mspResponseCode)) {
                    statusCode = 400;
                } else if ("300".equals(mspResponseCode)) {
                    statusCode = 300;
                } else {
                    statusCode = 500;
                } 

                JsonObject response = new JsonObject();
                response.put("code", statusCode);
                response.put("message", mspMessage);
                sendResponse(ctx, statusCode, response);

            } catch (Exception e) {
                logger.log(Level.WARNING, "DISPUTE REFUND: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static void synchronizeDataHistoryRefundDispute() {
        logger.log(Level.INFO, () -> "===IN JOB SYNC HISTORY" + Config.getString("onesched-service.general_synx_id", ""));
        try {
            OneSchedClient.synchronizeLong(Config.getString("onesched-service.general_synx_id", ""), "5s");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "error ep dong bo general loi : ", e);
        }
    }

    private static void synchronizeDataDispute() {
        try {
            JsonObject jsonDispute = OneSchedClient.synchronize(Config.getString("onesched-service.sync_data_search_dispute", ""), "0m");
            logger.log(Level.INFO, () -> "SEARCH DISPUTE SYNC: " + jsonDispute.encode());
        } catch (Exception e) {
            logger.log(Level.WARNING, "SEARCH DISPUTE SYNC DATA ERROR: ", e);
        }
    }

    public static void syncGeneralTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String timeSkip = "0s";
                JsonObject response = new JsonObject();
                int statusCode;
                String message;
                JsonObject sync_TB_GENERAL_SEARCH;

                try {
                    sync_TB_GENERAL_SEARCH = OneSchedClient.synchronize(Config.getString("import_all_trans_to_onereport118_tb_general_trans", "2105"), timeSkip);
                    if (sync_TB_GENERAL_SEARCH == null) {
                        statusCode = 404;
                        message = "Not Found";
                    } else {
                        statusCode = 200;
                        message = sync_TB_GENERAL_SEARCH.encode();
                    }
                } catch (Exception e) {
                    logger.log(Level.WARNING, "ONESCHED JOB ERROR: ", e);
                    statusCode = 500;
                    message = "Synchonize database failed";
                }

                response.put("code", statusCode);
                response.put("message", message);
                sendResponse(ctx, statusCode, response);
            } catch (Exception e) {
                logger.log(Level.WARNING, "SYNCHONIZE ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
