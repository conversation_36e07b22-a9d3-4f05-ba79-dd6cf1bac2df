package vn.onepay.portal.resources.dispute.dao;

import vn.onepay.portal.resources.Db;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.dispute.dto.DisputeSftp;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class SftpDao extends Db{

    private static final Logger LOGGER = Logger.getLogger(SftpDao.class.getName());
    private static final String INSERT_DISPUTE_SFTP = "{call PKG_SFTP.INSERT_SFTP(?,?,?,?,?) }";
    public static Map<String, Object> InsertDisputeSftp(DisputeSftp info) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        Map<String, Object> result = new HashMap<>();
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(INSERT_DISPUTE_SFTP);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, info.getFile_name());
            cs.setString(5, info.getEmail_operator());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 201) {
                throw new Exception("DB INSERT SFTP: " + error);
            } else {
                result.put("status", "success");
                return result;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
            result.put("status", "failed");
            return result;
    }

}
