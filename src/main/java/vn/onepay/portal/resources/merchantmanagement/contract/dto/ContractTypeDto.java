/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 4/1/19 5:40 PM
 */

package vn.onepay.portal.resources.merchantmanagement.contract.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ContractTypeDto {
    private String type;
    private String name;
    private String bank;
    private String flowType;
    private int order;

    public ContractTypeDto() {
    }

    public ContractTypeDto(String type, String name, String bank, String flowType, int order) {
        this.type = type;
        this.name = name;
        this.bank = bank;
        this.flowType = flowType;
        this.order = order;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBank() {
        return this.bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getFlowType() {
        return this.flowType;
    }

    public void setFlowType(String flowType) {
        this.flowType = flowType;
    }

    public int getOrder() {
        return this.order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public ContractTypeDto type(String type) {
        this.type = type;
        return this;
    }

    public ContractTypeDto name(String name) {
        this.name = name;
        return this;
    }

    public ContractTypeDto bank(String bank) {
        this.bank = bank;
        return this;
    }

    public ContractTypeDto flowType(String flowType) {
        this.flowType = flowType;
        return this;
    }

    public ContractTypeDto order(int order) {
        this.order = order;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " type='" + getType() + "'" + ", name='" + getName() + "'" + ", bank='" + getBank() + "'"
                + ", flowType='" + getFlowType() + "'" + ", order='" + getOrder() + "'" + "}";
    }
}
