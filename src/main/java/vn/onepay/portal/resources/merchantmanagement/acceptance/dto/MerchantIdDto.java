package vn.onepay.portal.resources.merchantmanagement.acceptance.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class MerchantIdDto {
    private int id;
    private int acceptance_id;
    private String merchant_id_qt;
    private String mid;
    private String currency;

    public MerchantIdDto() {
    }

    public MerchantIdDto(int id, int acceptance_id, String merchant_id_qt, String mid, String currency) {
        this.id = id;
        this.acceptance_id = acceptance_id;
        this.merchant_id_qt = merchant_id_qt;
        this.mid = mid;
        this.currency = currency;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAcceptance_id() {
        return this.acceptance_id;
    }

    public void setAcceptance_id(int acceptance_id) {
        this.acceptance_id = acceptance_id;
    }

    public String getMerchant_id_qt() {
        return this.merchant_id_qt;
    }

    public void setMerchant_id_qt(String merchant_id_qt) {
        this.merchant_id_qt = merchant_id_qt;
    }

    public String getMid() {
        return this.mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public MerchantIdDto id(int id) {
        this.id = id;
        return this;
    }

    public MerchantIdDto acceptance_id(int acceptance_id) {
        this.acceptance_id = acceptance_id;
        return this;
    }

    public MerchantIdDto merchant_id_qt(String merchant_id_qt) {
        this.merchant_id_qt = merchant_id_qt;
        return this;
    }

    public MerchantIdDto mid(String mid) {
        this.mid = mid;
        return this;
    }

    public MerchantIdDto currency(String currency) {
        this.currency = currency;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", acceptance_id='" + getAcceptance_id() + "'" + ", merchant_id_qt='"
                + getMerchant_id_qt() + "'" + ", mid='" + getMid() + "'" + ", currency='" + getCurrency() + "'" + "}";
    }
}
