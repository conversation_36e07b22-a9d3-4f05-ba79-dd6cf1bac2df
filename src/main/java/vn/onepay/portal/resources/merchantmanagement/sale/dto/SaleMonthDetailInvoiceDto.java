package vn.onepay.portal.resources.merchantmanagement.sale.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class SaleMonthDetailInvoiceDto {

    private String paygate;
    private Integer month;
    private Integer countSuccess;
    private Integer countFail;
    private Double total;
    private String currency;

    public SaleMonthDetailInvoiceDto() {
    }

    public SaleMonthDetailInvoiceDto(Integer month, Integer countSuccess,  Integer countFail,Double total,String paygate,String currency) {
        this.month = month;
        this.countSuccess = countSuccess;
        this.countFail = countFail;
        this.total = total;
        this.paygate = paygate;
        this.currency = currency;
    }

    public Integer getCountFail() {
        return countFail;
    }

    public void setCountFail(Integer countFail) {
        this.countFail = countFail;
    }

    public Integer getMonth() {
        return this.month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getCountSuccess() {
        return this.countSuccess;
    }

    public void setCountSuccess(Integer countSuccess) {
        this.countSuccess = countSuccess;
    }

    public Double getTotal() {
        return this.total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public SaleMonthDetailInvoiceDto month(Integer month) {
        this.month = month;
        return this;
    }

    public SaleMonthDetailInvoiceDto countSuccess(Integer countSuccess) {
        this.countSuccess = countSuccess;
        return this;
    }

    public SaleMonthDetailInvoiceDto countFail(Integer countFail) {
        this.countFail = countFail;
        return this;
    }

    public SaleMonthDetailInvoiceDto total(Double total) {
        this.total = total;
        return this;
    }

    public SaleMonthDetailInvoiceDto paygate(String paygate) {
        this.paygate = paygate;
        return this;
    }

    public SaleMonthDetailInvoiceDto currency(String currency) {
        this.currency = currency;
        return this;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    @Override
    public String toString() {
        return "{" + " month='" + getMonth() + "'" + ", count='" + getCountSuccess() + "'" + ", total='" + getTotal() + "'"
                + "}";
    }
}
