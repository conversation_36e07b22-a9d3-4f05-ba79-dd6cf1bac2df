package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

//import lombok.Builder;

//@Builder
public class AcquirerDto {
    Integer id;
    String name;
    String short_name;
    String description;
    String code;
    String service;

    public AcquirerDto() {
    }

    public AcquirerDto(Integer id, String name, String short_name, String description, String code) {
        this.id = id;
        this.name = name;
        this.short_name = short_name;
        this.description = description;
        this.code = code;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShort_name() {
        return this.short_name;
    }

    public void setShort_name(String short_name) {
        this.short_name = short_name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public AcquirerDto id(Integer id) {
        this.id = id;
        return this;
    }

    public AcquirerDto name(String name) {
        this.name = name;
        return this;
    }

    public AcquirerDto short_name(String short_name) {
        this.short_name = short_name;
        return this;
    }

    public AcquirerDto description(String description) {
        this.description = description;
        return this;
    }

    public AcquirerDto code(String code) {
        this.code = code;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", name='" + getName() + "'" + ", short_name='" + getShort_name() + "'"
                + ", description='" + getDescription() + "'" + ", code='" + getCode() + "'" + "}";
    }
}
