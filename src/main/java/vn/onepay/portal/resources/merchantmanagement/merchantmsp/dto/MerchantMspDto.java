package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

import java.util.List;

public class MerchantMspDto {
    // General
    private String merchant_id;
    private String merchant_name;
    private String hash_code;
    private String access_code;
    private String category_code;
    private String country_code;
    private String city;
    private String reference_type;
    private String state;
    private String address;
    private String secret_access_key;
    private String token;
    private String pr_merchant_id;
    private String partnerId;
    private String data;
    
    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    // Client Merchant
    private List<MerchantMspClientDto> client_merchants;

    // Notification
    private MerchantMspNotificationDto notification;

    // Installment Merchant
    private List<InstallmentMerchantDto> installment_merchants;

    // Customer Fee Merchant
    private CustomerFeeMerchantDto customer_fee_merchants;

    // Theme
    private ThemeColorMerchantDto theme_color_merchants;

    // Bin group config
    private List<MspBinRuleConfigDto> bin_rule_group_config;

    // Direct Debit
    private List<DirectDebitDto> direct_debit;

    public MerchantMspDto() {
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getMerchant_name() {
        return merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getHash_code() {
        return hash_code;
    }

    public void setHash_code(String hash_code) {
        this.hash_code = hash_code;
    }

    public String getAccess_code() {
        return access_code;
    }

    public void setAccess_code(String access_code) {
        this.access_code = access_code;
    }

    public String getCategory_code() {
        return category_code;
    }

    public void setCategory_code(String category_code) {
        this.category_code = category_code;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getReference_type() {
        return reference_type;
    }

    public void setReference_type(String reference_type) {
        this.reference_type = reference_type;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public List<MerchantMspClientDto> getClient_merchants() {
        return client_merchants;
    }

    public void setClient_merchants(List<MerchantMspClientDto> client_merchants) {
        this.client_merchants = client_merchants;
    }

    public List<InstallmentMerchantDto> getInstallment_merchants() {
        return installment_merchants;
    }

    public void setInstallment_merchants(List<InstallmentMerchantDto> installment_merchants) {
        this.installment_merchants = installment_merchants;
    }

    public MerchantMspNotificationDto getNotification() {
        return notification;
    }

    public void setNotification(MerchantMspNotificationDto notification) {
        this.notification = notification;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSecret_access_key() {
        return secret_access_key;
    }

    public void setSecret_access_key(String secret_access_key) {
        this.secret_access_key = secret_access_key;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPr_merchant_id() {
        return pr_merchant_id;
    }

    public void setPr_merchant_id(String pr_merchant_id) {
        this.pr_merchant_id = pr_merchant_id;
    }

    public CustomerFeeMerchantDto getCustomer_fee_merchants() {
        return customer_fee_merchants;
    }

    public void setCustomer_fee_merchants(CustomerFeeMerchantDto customer_fee_merchants) {
        this.customer_fee_merchants = customer_fee_merchants;
    }

    public ThemeColorMerchantDto getTheme_color_merchants() {
        return theme_color_merchants;
    }

    public void setTheme_color_merchants(ThemeColorMerchantDto theme_color_merchants) {
        this.theme_color_merchants = theme_color_merchants;
    }

    public List<MspBinRuleConfigDto> getBin_rule_group_config() {
        return bin_rule_group_config;
    }

    public void setBin_rule_group_config(List<MspBinRuleConfigDto> bin_rule_group_config) {
        this.bin_rule_group_config = bin_rule_group_config;
    }

    public List<DirectDebitDto> getDirect_debit() {
        return direct_debit;
    }

    public void setDirect_debit(List<DirectDebitDto> direct_debit) {
        this.direct_debit = direct_debit;
    }
    
    /**
     * @return String return the data
     */
    public String getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(String data) {
        this.data = data;
    }

}
