package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class InstallmentMspDao extends Db implements IConstants {

    /**
     * Get list all installments
     * @return
     * @throws Exception
     */
    public static BaseList<InstallmentDto> getInstallments() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<InstallmentDto> result = new BaseList<>();
        List<InstallmentDto> listInstallments = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.ITA_GET_ALL(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST INSTALLMENTS error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listInstallments.add(bindInstallment(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listInstallments);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Get list of installment_merchant by merchant_id
     * @param merchant_id
     * @return
     */
    public static List<InstallmentMerchantDto> getInstallmentMerchantByMerchantId(String merchant_id) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        List<InstallmentMerchantDto> list = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.ITA_MERCHANT_GET_BY_ID(?,?,?,?,?) }");
            cs.setString(1, merchant_id);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(4);
            String sError = cs.getString(5);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET LIST INSTALLMENT_MERCHANT error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    list.add(bindInstallmentMerchant(rs));
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return list;
    }

    /**
     * Upsert installment_merchant
     * @param dto
     * @throws Exception
     */
    public static void upsertInstallmentMerchant(String merchant_id, InstallmentMerchantDto dto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        if(dto == null) {
            return;
        }

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.ITA_MERCHANT_UPSERT(?,?,?,?,?,?,?,?,?,?,?) }");
            cs.setString(1, dto.getInstallment_id());
            cs.setString(2, merchant_id);
            cs.setInt(3, dto.getCancel_days());
            cs.setString(4, "approved");
            cs.setString(5, dto.getDesc() == null ? "" : dto.getDesc());
            cs.setString(6, dto.getTime_periods());
            cs.setString(7, dto.getFee());
            cs.setInt(8, dto.getIta_Fee());
            cs.registerOutParameter(9, OracleTypes.NUMBER);
            cs.registerOutParameter(10, OracleTypes.NUMBER);
            cs.registerOutParameter(11, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(10);
            String sError = cs.getString(11);
            if (nError != 200) {
                logger.severe("UPSERT MSP INSTALLMENT-MERCHANT error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    /**
     * Delete installment-merchant
     * @param merchantId
     * @param installmentId
     * @throws Exception
     */
    public static void deleteInstallmentMerchant(String merchantId, String installmentId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.ITA_MERCHANT_DELETE(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.setString(2, installmentId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("DELETE INSTALLMENT MERCHANT error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    /**
     * Bind resultset to InstallmentDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static InstallmentDto bindInstallment(ResultSet rs) throws Exception {
        InstallmentDto dto = new InstallmentDto();
        dto.setId(rs.getString("S_ID"));
        dto.setName(rs.getString("S_NAME"));
        dto.setBins(rs.getString("S_BINS"));
        dto.setSwift_code(rs.getString("S_SWIFT_CODE"));
        dto.setTimes(rs.getString("S_TIMES"));
        dto.setState(rs.getString("S_STATE"));
        return dto;
    }

    /**
     * Bind resultset to InstallmentMerchantDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static InstallmentMerchantDto bindInstallmentMerchant(ResultSet rs) throws Exception {
        InstallmentMerchantDto dto = new InstallmentMerchantDto();
        dto.setMerchant_id(rs.getString("S_MERCHANT_ID"));
        dto.setInstallment_id(rs.getString("S_INSTALLMENT_ID"));
        dto.setCancel_days(rs.getInt("N_CANCEL_DAYS"));
        dto.setDesc(rs.getString("S_DESC"));
        dto.setState(rs.getString("S_STATE"));
        dto.setTime_periods(rs.getString("S_TIMES"));
        dto.setFee(rs.getString("S_FEES"));
        dto.setIta_fee(rs.getInt("N_ITA_FEE"));
        dto.setData(rs.getString("S_DATA"));
        Timestamp create_time = rs.getTimestamp("D_CREATE");
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
        dto.setCreate_time(sdf.format(create_time));
        return dto;
    }

}
