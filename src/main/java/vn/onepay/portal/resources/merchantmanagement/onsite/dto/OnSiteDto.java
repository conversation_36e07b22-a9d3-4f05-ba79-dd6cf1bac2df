package vn.onepay.portal.resources.merchantmanagement.onsite.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class OnSiteDto {
  private int id;
  private Date createDate;
  private String staff;
  private String content;
  private String channel;
  private int partnerId;
  private String target;
  private String group;

  public OnSiteDto() {
  }

  public OnSiteDto(int id, Date createDate, String staff, String content, String channel, int partnerId, String target,
      String group) {
    this.id = id;
    this.createDate = createDate;
    this.staff = staff;
    this.content = content;
    this.channel = channel;
    this.partnerId = partnerId;
    this.target = target;
    this.group = group;
  }

  public int getId() {
    return this.id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public Date getCreateDate() {
    return this.createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public String getStaff() {
    return this.staff;
  }

  public void setStaff(String staff) {
    this.staff = staff;
  }

  public String getContent() {
    return this.content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getChannel() {
    return this.channel;
  }

  public void setChannel(String channel) {
    this.channel = channel;
  }

  public int getPartnerId() {
    return this.partnerId;
  }

  public void setPartnerId(int partnerId) {
    this.partnerId = partnerId;
  }

  public String getTarget() {
    return this.target;
  }

  public void setTarget(String target) {
    this.target = target;
  }

  public String getGroup() {
    return this.group;
  }

  public void setGroup(String group) {
    this.group = group;
  }

  public OnSiteDto id(int id) {
    this.id = id;
    return this;
  }

  public OnSiteDto createDate(Date createDate) {
    this.createDate = createDate;
    return this;
  }

  public OnSiteDto staff(String staff) {
    this.staff = staff;
    return this;
  }

  public OnSiteDto content(String content) {
    this.content = content;
    return this;
  }

  public OnSiteDto channel(String channel) {
    this.channel = channel;
    return this;
  }

  public OnSiteDto partnerId(int partnerId) {
    this.partnerId = partnerId;
    return this;
  }

  public OnSiteDto target(String target) {
    this.target = target;
    return this;
  }

  public OnSiteDto group(String group) {
    this.group = group;
    return this;
  }

  @Override
  public String toString() {
    return "{" + " id='" + getId() + "'" + ", createDate='" + getCreateDate() + "'" + ", staff='" + getStaff() + "'"
        + ", content='" + getContent() + "'" + ", channel='" + getChannel() + "'" + ", partnerId='" + getPartnerId()
        + "'" + ", target='" + getTarget() + "'" + ", group='" + getGroup() + "'" + "}";
  }
}
