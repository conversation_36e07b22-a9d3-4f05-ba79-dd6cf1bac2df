package vn.onepay.portal.resources.merchantmanagement.thirdPartner;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto.HistoryLogDto;
import vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto.ThirdPartner;

import java.util.logging.Level;
import java.util.logging.Logger;

import oracle.jdbc.OracleTypes;

public class ThirdPartnerDAO extends Db implements IConstants {

    private static final Logger logger = Logger.getLogger(ThirdPartnerDAO.class.getName());

    public static Integer totalPartner(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_3RD_PARTNER.search_partner(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "TOTAL");
            cs.setString(5, mIn.getOrDefault(KEYWORD, ""));
            cs.setObject(6, mIn.getOrDefault(PAGE, "0"));
            cs.setObject(7, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("PKG_3RD_PARTNER.search_partner error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = Util.getColumnInteger(rs, "N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            //
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ThirdPartner> searchPartner(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<ThirdPartner> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_3RD_PARTNER.search_partner(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "SELECT");
            cs.setString(5, mIn.getOrDefault(KEYWORD, ""));
            cs.setObject(6, mIn.getOrDefault(PAGE, "0"));
            cs.setObject(7, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB search_partner error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindThirdPartner(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ThirdPartner getThirdPartnerDetailById(String id) throws Exception {
        Exception exception = null;
        ThirdPartner result = new ThirdPartner();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_3RD_PARTNER.get_partner_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get_partner_by_id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = new ThirdPartner(
                        rs.getString("S_ID"),
                        rs.getString("S_BUSINESS_NUMBER") == null ? "" : rs.getString("S_BUSINESS_NUMBER"),
                        rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"),
                        rs.getString("S_PROVIDED_DATE") == null ? "" : rs.getString("S_PROVIDED_DATE"),
                        rs.getString("S_BUSINESS_ADDRESS") == null ? "" : rs.getString("S_BUSINESS_ADDRESS"),
                        rs.getString("S_OFFICE_ADDRESS") == null ? "" : rs.getString("S_OFFICE_ADDRESS"),
                        rs.getString("S_WEBSITE") == null ? "" : rs.getString("S_WEBSITE"),
                        rs.getString("S_SOURCE") == null ? "" : rs.getString("S_SOURCE"),
                        rs.getString("S_REPRESENT") == null ? "" : rs.getString("S_REPRESENT"),
                        rs.getString("S_REPRESENT_TITLE") == null ? "" : rs.getString("S_REPRESENT_TITLE"),
                        rs.getString("S_CONTACT_MOBILE") == null ? "" : rs.getString("S_CONTACT_MOBILE"),
                        rs.getString("S_ADV_ACCOUNT_NUMBER") == null ? "" : rs.getString("S_ADV_ACCOUNT_NUMBER"),
                        rs.getString("S_ADV_ACCOUNT_NAME") == null ? "" : rs.getString("S_ADV_ACCOUNT_NAME"),
                        rs.getString("S_ADV_BANK") == null ? "" : rs.getString("S_ADV_BANK"),
                        rs.getString("S_BUSINESS_AREA") == null ? "" : rs.getString("S_BUSINESS_AREA"),
                        rs.getString("S_CURR_PAY_METHOD") == null ? "" : rs.getString("S_CURR_PAY_METHOD"),
                        rs.getString("S_CONTACT_EMAIL") == null ? "" : rs.getString("S_CONTACT_EMAIL"),
                        rs.getString("S_BUSINESS_FILE_URL") == null ? "" : rs.getString("S_BUSINESS_FILE_URL"),
                        rs.getString("S_IDENTITY_CARD_URL") == null ? "" : rs.getString("S_IDENTITY_CARD_URL"),
                        rs.getString("S_BUSINESS_AREA_FILE_URL") == null ? "" : rs.getString("S_BUSINESS_AREA_FILE_URL"),
                        rs.getString("S_IMAGE_URL") == null ? "" : rs.getString("S_IMAGE_URL"),
                        rs.getString("S_STATE") == null ? "" : rs.getString("S_STATE"));
                }
            } 
        } catch (Exception e) {
            logger.log(Level.WARNING, "LOAD ERROR: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto reviewThirdPartner(int userId, String partnerId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".PKG_3RD_PARTNER.update_review(?,?,?,?)}");
            cs.setInt(1, userId);
            cs.setString(2, partnerId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                logger.severe("DB update review error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto approveThirdPartner(int userId, String partnerId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".PKG_3RD_PARTNER.update_approve(?,?,?,?)}");
            cs.setInt(1, userId);
            cs.setString(2, partnerId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                logger.severe("DB update approve error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto rejectThirdPartner(int userId, String partnerId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".PKG_3RD_PARTNER.update_reject(?,?,?,?)}");
            cs.setInt(1, userId);
            cs.setString(2, partnerId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                logger.severe("DB update reject error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto createThirdPartner(int userId, String partnerId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".PKG_3RD_PARTNER.update_create(?,?,?,?)}");
            cs.setInt(1, userId);
            cs.setString(2, partnerId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                logger.severe("DB update create error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<HistoryLogDto> getDetailLogById(String id) throws Exception {
        Exception exception = null;
        List<HistoryLogDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_3RD_PARTNER.get_detail_history(?,?,?,?,?)}");
            cs.setString(1, id);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER); 
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(5);
            int nError = cs.getInt(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                throw new Exception("DB get_detail_history error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindHistoryLog(rs));
                }
            } 
        } catch (Exception e) {
            logger.log(Level.WARNING, "LOAD ERROR: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static ThirdPartner bindThirdPartner(ResultSet rs) throws Exception {
        ThirdPartner p = new ThirdPartner();
        p.setId(Util.getColumnString(rs, "S_ID"));
        p.setBusinessNumber(Util.getColumnString(rs, "S_BUSINESS_NUMBER"));
        p.setBusinessName(Util.getColumnString(rs, "S_BUSINESS_NAME"));
        p.setSource(Util.getColumnString(rs, "S_SOURCE")); 
        p.setPartnerStatus(Util.getColumnString(rs, "S_HISTORI_PARTNER"));
        p.setState(Util.getColumnString(rs, "S_STATE"));       
        return p;
    }

    private static HistoryLogDto bindHistoryLog(ResultSet rs) throws Exception {
        HistoryLogDto h = new HistoryLogDto();
        h.setPartnerId(Util.getColumnString(rs, "S_PARTNER_ID"));
        h.setUserEmail(Util.getColumnString(rs, "S_EMAIL"));
        h.setCreatedDate(Util.getColumnTimeStamp(rs, "D_CREATE"));
        h.setAction(Util.getColumnString(rs, "S_ACTION")); 
        return h;
    }
}