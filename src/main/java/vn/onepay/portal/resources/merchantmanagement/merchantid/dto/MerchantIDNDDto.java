package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class MerchantIDNDDto {
    private String merchant_name;
    private String merchant_id;
    private String trading_name;
    private String vcb_merchant_id;
    private String vcb_merchant_offline_id;
    private String active;
    private String card_type;

    public MerchantIDNDDto() {
    }

    public MerchantIDNDDto(String merchant_name, String merchant_id, String trading_name, String vcb_merchant_id,
            String vcb_merchant_offline_id, String active, String card_type) {
        this.merchant_name = merchant_name;
        this.merchant_id = merchant_id;
        this.trading_name = trading_name;
        this.vcb_merchant_id = vcb_merchant_id;
        this.vcb_merchant_offline_id = vcb_merchant_offline_id;
        this.active = active;
        this.card_type = card_type;
    }

    public String getMerchant_name() {
        return this.merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getMerchant_id() {
        return this.merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getTrading_name() {
        return this.trading_name;
    }

    public void setTrading_name(String trading_name) {
        this.trading_name = trading_name;
    }

    public String getVcb_merchant_id() {
        return this.vcb_merchant_id;
    }

    public void setVcb_merchant_id(String vcb_merchant_id) {
        this.vcb_merchant_id = vcb_merchant_id;
    }

    public String getVcb_merchant_offline_id() {
        return this.vcb_merchant_offline_id;
    }

    public void setVcb_merchant_offline_id(String vcb_merchant_offline_id) {
        this.vcb_merchant_offline_id = vcb_merchant_offline_id;
    }

    public String getActive() {
        return this.active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getCard_type() {
        return this.card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public MerchantIDNDDto merchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
        return this;
    }

    public MerchantIDNDDto merchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
        return this;
    }

    public MerchantIDNDDto trading_name(String trading_name) {
        this.trading_name = trading_name;
        return this;
    }

    public MerchantIDNDDto vcb_merchant_id(String vcb_merchant_id) {
        this.vcb_merchant_id = vcb_merchant_id;
        return this;
    }

    public MerchantIDNDDto vcb_merchant_offline_id(String vcb_merchant_offline_id) {
        this.vcb_merchant_offline_id = vcb_merchant_offline_id;
        return this;
    }

    public MerchantIDNDDto active(String active) {
        this.active = active;
        return this;
    }

    public MerchantIDNDDto card_type(String card_type) {
        this.card_type = card_type;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " merchant_name='" + getMerchant_name() + "'" + ", merchant_id='" + getMerchant_id() + "'"
                + ", trading_name='" + getTrading_name() + "'" + ", vcb_merchant_id='" + getVcb_merchant_id() + "'"
                + ", vcb_merchant_offline_id='" + getVcb_merchant_offline_id() + "'" + ", active='" + getActive() + "'"
                + ", card_type='" + getCard_type() + "'" + "}";
    }
}