/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 2/15/19 6:01 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo;

import static vn.onepay.portal.Util.sendResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;
import vn.onepay.portal.resources.mm.MmSubscribeDao;
import vn.onepay.portal.resources.sale.SaleSubscribeDao;

public class MerchantInfoHandler implements IConstants {
    private static Logger logger = Logger.getLogger(MerchantInfoHandler.class.getName());

    public static void loadProvinceList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantInfoDao.getProvinceList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadCareerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantInfoDao.getCareerList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchPartnerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);
                Integer page = request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE));
                Integer pageSize = request.getParam(PAGE_SIZE) == null ? 20
                        : Integer.parseInt(request.getParam(PAGE_SIZE));
                String keyWord = request.getParam(KEYWORD) == null ? BLANK : request.getParam(KEYWORD);
                String partnerName = request.getParam(PARTNER_NAME) == null ? BLANK : request.getParam(PARTNER_NAME);
                String mid = request.getParam(MID) == null ? BLANK : request.getParam(MID);
                String email = request.getParam(EMAIL) == null ? BLANK : request.getParam(EMAIL);
                String phoneNumber = request.getParam(PHONE_NUMBER) == null ? BLANK : request.getParam(PHONE_NUMBER);
                String address = request.getParam(ADDRESS) == null ? BLANK : request.getParam(ADDRESS);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(USER_ID, userId);
                mIn.put(PAGE_ACTIVE, page);
                mIn.put(PAGE_SIZE, pageSize);
                mIn.put(KEYWORD, keyWord);
                mIn.put(PARTNER_NAME, partnerName);
                mIn.put(MID, mid);
                mIn.put(EMAIL, email);
                mIn.put(PHONE_NUMBER, phoneNumber);
                mIn.put(ADDRESS, address);

                String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                mIn.put(PROVINCE_ID, provinceIds);
                String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));
                mIn.put("sale_id", saleSubs);
                String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));
                mIn.put("mm_id", mmSubs);
                logger.info("[mIn]" + mIn.toString());
                BaseList<PartnerDto> lstPartnerWithPermission = MerchantInfoDao.searchPartnerList(mIn);
                logger.info("[lstPartnerWithPermission]=" + lstPartnerWithPermission.getList().size());
                sendResponse(ctx, 200, lstPartnerWithPermission);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchPartnerListByKeyword(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);
                Integer page = request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE));
                Integer pageSize = request.getParam(PAGE_SIZE) == null ? 50
                        : Integer.parseInt(request.getParam(PAGE_SIZE));
                String keyWord = request.getParam(KEYWORD) == null ? BLANK : request.getParam(KEYWORD);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(USER_ID, userId);
                mIn.put(PAGE_ACTIVE, page);
                mIn.put(PAGE_SIZE, pageSize);
                mIn.put(KEYWORD, keyWord);
                sendResponse(ctx, 200, MerchantInfoDao.searchPartnerListByKeyword(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartnerStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String partnerId = request.getParam(PARTNER_ID) == null ? BLANK : request.getParam(PARTNER_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                sendResponse(ctx, 200, MerchantInfoDao.getPartnerStatus(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deletePartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String partnerId = request.getParam(PARTNER_ID) == null ? BLANK : request.getParam(PARTNER_ID);
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(Integer.parseInt(partnerId), userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                sendResponse(ctx, 200, MerchantInfoDao.deletePartner(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updatePartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, request.getParam(PARTNER_ID) == null ? BLANK : request.getParam(PARTNER_ID));
                mIn.put(SHORT_NAME, body.getString(SHORT_NAME) == null ? BLANK : body.getString(SHORT_NAME));
                mIn.put(PARTNER_NAME, body.getString(PARTNER_NAME) == null ? BLANK : body.getString(PARTNER_NAME));
                mIn.put(WEBSITE, body.getString(WEBSITE) == null ? BLANK : body.getString(WEBSITE));
                mIn.put(ADDRESS_LINE1, body.getString(ADDRESS_LINE1) == null ? BLANK : body.getString(ADDRESS_LINE1));
                mIn.put(ADDRESS_LINE2, body.getString(ADDRESS_LINE2) == null ? BLANK : body.getString(ADDRESS_LINE2));
                mIn.put(PROVINCE_ID, body.getString(PROVINCE_ID) == null ? BLANK : body.getString(PROVINCE_ID));
                mIn.put(PROVINCE_ID_OFFICE,
                        body.getString(PROVINCE_ID_OFFICE) == null ? BLANK : body.getString(PROVINCE_ID_OFFICE));
                mIn.put(EMAIL, body.getString(EMAIL) == null ? BLANK : body.getString(EMAIL));
                mIn.put(TEL, body.getString(TEL) == null ? BLANK : body.getString(TEL));
                mIn.put(FAX, body.getString(FAX) == null ? BLANK : body.getString(FAX));
                mIn.put(SALE, body.getString(SALE) == null ? BLANK : body.getString(SALE));
                mIn.put(MM, body.getString(MM) == null ? BLANK : body.getString(MM));
                mIn.put(TAX_CODE, body.getString(TAX_CODE) == null ? BLANK : body.getString(TAX_CODE));
                mIn.put(BUSINESS_CODE, body.getString(BUSINESS_CODE) == null ? BLANK : body.getString(BUSINESS_CODE));
                mIn.put(BUSINESS_REGISER_DATE,
                        body.getString(BUSINESS_REGISER_DATE) == null ? BLANK : body.getString(BUSINESS_REGISER_DATE));
                mIn.put(CAREER_ID, body.getString(CAREER_ID) == null ? BLANK : body.getString(CAREER_ID));
                mIn.put(CAREER_OTHER_DESC,
                        body.getString(CAREER_OTHER_DESC) == null ? BLANK : body.getString(CAREER_OTHER_DESC));
                mIn.put(DESCRIPTION, body.getString(DESCRIPTION) == null ? BLANK : body.getString(DESCRIPTION));
                sendResponse(ctx, 200, MerchantInfoDao.updatePartner(mIn));
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "UPDATE PARTNER ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartnerById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String partnerId = request.getParam(PARTNER_ID);
                Integer userId = ctx.get(N_USER_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(USER_ID, userId);
                String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                mIn.put(PROVINCE_ID, provinceIds);
                String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));
                mIn.put("sale_id", saleSubs);
                String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));
                mIn.put("mm_id", mmSubs);

                sendResponse(ctx, 200, MerchantInfoDao.getPartnerByID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getContactList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String partnerId = request.getParam(PARTNER_ID) == null ? BLANK : request.getParam(PARTNER_ID);
                Integer userId = ctx.get(N_USER_ID);

                // log partnerId and userId
                logger.info("partnerId: " + partnerId + " userId: " + userId);

                MerchantInfoHandler.checkPermissionPartner(Integer.parseInt(partnerId), userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                sendResponse(ctx, 200, MerchantInfoDao.getContactList(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateContact(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTACT_ID, request.getParam(CONTACT_ID) == null ? BLANK : request.getParam(CONTACT_ID));
                mIn.put(PARTNER_ID, body.getString(PARTNER_ID) == null ? BLANK : body.getString(PARTNER_ID));
                mIn.put(NAME, body.getString(NAME) == null ? BLANK : body.getString(NAME));
                mIn.put(PREFIX, body.getString(PREFIX) == null ? BLANK : body.getString(PREFIX));
                mIn.put(TITLE, body.getString(TITLE) == null ? BLANK : body.getString(TITLE));
                mIn.put(IS_WORK, body.getString(IS_WORK) == null ? 0 : body.getString(IS_WORK));
                mIn.put(PHONE_NUMBER, body.getString(PHONE_NUMBER) == null ? BLANK : body.getString(PHONE_NUMBER));
                mIn.put(MOBILE, body.getString(MOBILE) == null ? BLANK : body.getString(MOBILE));
                mIn.put(EMAIL, body.getString(EMAIL) == null ? BLANK : body.getString(EMAIL));
                mIn.put(POSITION, body.getString(POSITION) == null ? BLANK : body.getString(POSITION));
                mIn.put(USER_ID, body.getString(USER_ID) == null ? BLANK : body.getString(USER_ID));
                mIn.put(GROUP_ID, body.getString("groupIds") == null ? BLANK : body.getString("groupIds"));
                ActionDto actionDto = MerchantInfoDao.updateContact(mIn);

                // remove all contact group
                MerchantInfoDao.removeContactGroup(actionDto.getId());
                String groups = body.getString("groupIds") == null ? BLANK : body.getString("groupIds");
                String[] ids = groups.split(",");
                for (String id : ids) {
                    // inert contact group
                    MerchantInfoDao.addContactGroup(actionDto.getId(), Integer.parseInt(id));
                }

                sendResponse(ctx, 200, actionDto);
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "UPDATE CONTACT ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addOrRemoveContactGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String command = request.getParam(COMMAND) == null ? BLANK : request.getParam(COMMAND);
                String contactId = request.getParam(CONTACT_ID) == null ? BLANK : request.getParam(CONTACT_ID);
                String groupId = request.getParam(GROUP_ID) == null ? BLANK : request.getParam(GROUP_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(COMMAND, command);
                mIn.put(CONTACT_ID, contactId);
                mIn.put(GROUP_ID, groupId);
                sendResponse(ctx, 200, MerchantInfoDao.addOrRemoveContactGroup(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteContact(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String contactId = request.getParam(CONTACT_ID) == null ? BLANK : request.getParam(CONTACT_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTACT_ID, contactId);
                sendResponse(ctx, 200, MerchantInfoDao.deleteContact(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadGroupList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantInfoDao.getGroupList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadContactGroupsList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String contactId = request.getParam(CONTACT_ID) == null ? BLANK : request.getParam(CONTACT_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTACT_ID, contactId);
                sendResponse(ctx, 200, MerchantInfoDao.getGroupsByContactID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadMerchantList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String merchantId = request.getParam(MERCHANT_ID) == null ? BLANK : request.getParam(MERCHANT_ID);
                Integer page = request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE));
                Integer pageSize = request.getParam(PAGE_SIZE) == null ? 50
                        : Integer.parseInt(request.getParam(PAGE_SIZE));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MERCHANT_ID, merchantId);
                mIn.put(PAGE, page);
                mIn.put(PAGE_SIZE, pageSize);
                sendResponse(ctx, 200, MerchantInfoDao.getMerchantList(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkPermissionPartner(Integer partnerId, Integer userId) throws Exception {
        // log partnerId and userId
        logger.info("partnerId: " + partnerId + " userId: " + userId);

        Map<String, Object> mInPermissionMap = new HashMap<>();
        mInPermissionMap.put(PARTNER_ID, partnerId);
        mInPermissionMap.put(USER_ID, userId);
        String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
        mInPermissionMap.put(PROVINCE_ID, provinceIds);
        String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                .stream().map(x -> x.getId().toString())
                .collect(Collectors.joining(","));
        mInPermissionMap.put("sale_id", saleSubs);
        String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                .stream().map(x -> x.getId().toString())
                .collect(Collectors.joining(","));
        mInPermissionMap.put("mm_id", mmSubs);
        PartnerDto partnerDetail = MerchantInfoDao.getPartnerByID(mInPermissionMap);
        if (partnerDetail.getShortName() == null) {
            logger.info("Permission denied");
            throw IErrors.FORBIDDEN;
        }
    }
}
