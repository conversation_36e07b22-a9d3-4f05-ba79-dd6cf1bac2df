package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/11/2020
 * Time: 12:01 PM
 * To change this iportal-service.
 */

public class MspCustomerFeeDao extends Db implements IConstants {
    public static CustomerFeeMerchantDto get(String merchant_id) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<CustomerFeeConfigDto> feeConfigDtos = new ArrayList<>();
        CustomerFeeMerchantDto customerFeeMerchantDto = new CustomerFeeMerchantDto();
        customerFeeMerchantDto.setFee(feeConfigDtos);
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_cus_fee_by_merchant(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchant_id);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST get_cus_fee_by_merchant error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    customerFeeMerchantDto = bindCustomerFee(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return customerFeeMerchantDto;
    }
    public static void put(String merchant_id , CustomerFeeMerchantDto customerFeeMerchantDto) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.PUT_CUSTOMER_FEE(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, customerFeeMerchantDto.getId());
            cs.setString(5, merchant_id);
            cs.setString(6, "customer");
            cs.setString(7, "approved");
//            cs.setString(8,new Gson().toJson(feeToMap(customerFeeMerchantDto)));
            cs.setString(8,new Gson().toJson(customerFeeMerchantDto.getFee()));
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            int idReturn = cs.getInt(1);
            if (nError != 200) {
                logger.severe("GET LIST INSTALLMENT_MERCHANT error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }
    public static Integer delete(String merchant_id) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.delete_cus_fee(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, merchant_id);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            result = cs.getInt(1);
            if (nError != 200) {
                logger.severe("GET DELETE CUSTOMER FEE error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    private static CustomerFeeMerchantDto bindCustomerFee(ResultSet rs) throws Exception {
        CustomerFeeMerchantDto dto = new CustomerFeeMerchantDto();

        dto.setMerchantId(rs.getString("S_MERCHANT_ID"));
        dto.setType(rs.getString("S_TYPE"));
        dto.setState(rs.getString("S_STATE"));
        String arrayFee = rs.getString("S_FEE");
        ObjectMapper mapper = new ObjectMapper();
        List<CustomerFeeConfigDto> feeConfigDtos = mapper.readValue(arrayFee, new TypeReference<List<CustomerFeeConfigDto>>(){});
        dto.setFee(feeConfigDtos);
//        ArrayList<CustomerFeeConfigDto> configDtoArrayList = new ArrayList<>();
//        JsonObject jsonObject  = new JsonObject(arrayFee);
//        jsonObject.getMap().keySet().forEach(key -> {
//            CustomerFeeConfigDto a = new CustomerFeeConfigDto();
//            a.setC(key);
//            a.setD(jsonObject.getJsonObject(key).getString("d"));
//            a.setF(jsonObject.getJsonObject(key).getString("f"));
//            a.setP(jsonObject.getJsonObject(key).getString("p"));
//            configDtoArrayList.add(a);
//        });
//        dto.setFee(configDtoArrayList);
        dto.setId(rs.getString("N_ID"));
        return dto;
    }
//    private static Map feeToMap(CustomerFeeMerchantDto dto){
//        Map map = new HashMap();
//        for(CustomerFeeConfigDto configDto: dto.getFee()){
//
//            CustomerFeeConfigDataDto dataDto = new CustomerFeeConfigDataDto();
//            dataDto.setD(configDto.getD());
//            dataDto.setF(configDto.getF());
//            dataDto.setP(configDto.getP());
//            map.put(configDto.getC(),dataDto);
//        }
//        return map;
//    }
}
