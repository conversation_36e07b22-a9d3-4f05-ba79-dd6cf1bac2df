/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 4/2/19 10:47 AM
 */

package vn.onepay.portal.resources.merchantmanagement.contract.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ContractDto {
  private int id;
  private int partner_id;
  private String contract_code;
  private String type_id;
  private Date sign_date;
  private Date completed_date;
  private String card_type_payment;
  private String bank_id;
  private String branch;
  private String branchName;
  private String name;
  private String title;
  private String account;
  private String status;
  private Date close_date;
  private Date expire_date;
  private int fin_account;
  private String type;
  private String guarantee_type;
  private String guarantee_detail;
  private String note;
  private int parent;
  private String account_name;
  private String tel;
  private String fax;
  private String address;
  private String fee_setup;
  private String position;
  private String website;
  private String settlement;
  private String ten_dkkd;
  private String bank_gd_id;
  private String thoi_gian_tam_ngung;
  private String hinh_thuc_tam_ung;
  private String user_update;
  private Date update_time;
  private String user_approve;
  private Date approve_time;
  private String state_approve;
  private String name_bank_account;
  private String active;
  private int setup_fee;
  private String currency_setup_fee;
  private String short_name;
  private String so_dkkd;
  private Date issue;
  private int guarantee;
  private String guarantee_account;
  private String advance_1st;
  private String advance_2st_date;
  private String init_fee;
  private String inter_card_fee;
  private String domes_card_init_fee;
  private String domes_token_fee;
  private String monthly_service_fee;
  private String trans_process_fee;
  private String trans_process_fee_qt;
  private String trans_process_fee_nd;
  private String card_fee;
  private int fd_month;
  private String fee_collect_form;
  private String bank_notify_email;
  private String trans_notify_email;
  private String inter_card_fee_currency;
  private String domes_card_init_fee_currency;
  private String domes_token_fee_currency;

  private String init_fee_currency;
  private String trans_process_fee_currency;
  private String trans_process_fee_qt_currency;
  private String trans_process_fee_nd_currency;

  private String monthly_service_fee_currency;
  private String note_card_fee;
  private String contract_form;

  public ContractDto() {
  }

  public ContractDto(int id, int partner_id, String contract_code, String type_id, Date sign_date, Date completed_date,
                     String card_type_payment, String bank_id, String branch, String branchName, String name, String title, String account, String status,
                     Date close_date, Date expire_date, int fin_account, String type, String guarantee_type, String guarantee_detail,
                     String note, int parent, String account_name, String tel, String fax, String address, String fee_setup,
                     String position, String website, String settlement, String ten_dkkd, String bank_gd_id,
                     String thoi_gian_tam_ngung, String hinh_thuc_tam_ung, String user_update, Date update_time, String user_approve,
                     Date approve_time, String state_approve, String name_bank_account, String active, int setup_fee,
                     String currency_setup_fee, String short_name, String so_dkkd, Date issue, int guarantee, String guarantee_account,
                     String advance_1st, String advance_2st_date, String init_fee, String inter_card_fee, String domes_card_init_fee,
                     String domes_token_fee, String monthly_service_fee, String trans_process_fee, String trans_process_fee_qt,
                     String trans_process_fee_nd, String card_fee, int fd_month, String fee_collect_form, String bank_notify_email,
                     String trans_notify_email, String inter_card_fee_currency, String domes_card_init_fee_currency,
                     String domes_token_fee_currency, String init_fee_currency, String trans_process_fee_currency,
                     String trans_process_fee_qt_currency, String trans_process_fee_nd_currency, String monthly_service_fee_currency,
                     String note_card_fee, String contract_form) {
    this.id = id;
    this.partner_id = partner_id;
    this.contract_code = contract_code;
    this.type_id = type_id;
    this.sign_date = sign_date;
    this.completed_date = completed_date;
    this.card_type_payment = card_type_payment;
    this.bank_id = bank_id;
    this.branch = branch;
    this.name = name;
    this.branchName = branchName;
    this.title = title;
    this.account = account;
    this.status = status;
    this.close_date = close_date;
    this.expire_date = expire_date;
    this.fin_account = fin_account;
    this.type = type;
    this.guarantee_type = guarantee_type;
    this.guarantee_detail = guarantee_detail;
    this.note = note;
    this.parent = parent;
    this.account_name = account_name;
    this.tel = tel;
    this.fax = fax;
    this.address = address;
    this.fee_setup = fee_setup;
    this.position = position;
    this.website = website;
    this.settlement = settlement;
    this.ten_dkkd = ten_dkkd;
    this.bank_gd_id = bank_gd_id;
    this.thoi_gian_tam_ngung = thoi_gian_tam_ngung;
    this.hinh_thuc_tam_ung = hinh_thuc_tam_ung;
    this.user_update = user_update;
    this.update_time = update_time;
    this.user_approve = user_approve;
    this.approve_time = approve_time;
    this.state_approve = state_approve;
    this.name_bank_account = name_bank_account;
    this.active = active;
    this.setup_fee = setup_fee;
    this.currency_setup_fee = currency_setup_fee;
    this.short_name = short_name;
    this.so_dkkd = so_dkkd;
    this.issue = issue;
    this.guarantee = guarantee;
    this.guarantee_account = guarantee_account;
    this.advance_1st = advance_1st;
    this.advance_2st_date = advance_2st_date;
    this.init_fee = init_fee;
    this.inter_card_fee = inter_card_fee;
    this.domes_card_init_fee = domes_card_init_fee;
    this.domes_token_fee = domes_token_fee;
    this.monthly_service_fee = monthly_service_fee;
    this.trans_process_fee = trans_process_fee;
    this.trans_process_fee_qt = trans_process_fee_qt;
    this.trans_process_fee_nd = trans_process_fee_nd;
    this.card_fee = card_fee;
    this.fd_month = fd_month;
    this.fee_collect_form = fee_collect_form;
    this.bank_notify_email = bank_notify_email;
    this.trans_notify_email = trans_notify_email;
    this.inter_card_fee_currency = inter_card_fee_currency;
    this.domes_card_init_fee_currency = domes_card_init_fee_currency;
    this.domes_token_fee_currency = domes_token_fee_currency;
    this.init_fee_currency = init_fee_currency;
    this.trans_process_fee_currency = trans_process_fee_currency;
    this.trans_process_fee_qt_currency = trans_process_fee_qt_currency;
    this.trans_process_fee_nd_currency = trans_process_fee_nd_currency;
    this.monthly_service_fee_currency = monthly_service_fee_currency;
    this.note_card_fee = note_card_fee;
    this.contract_form = contract_form;
  }

  public String getBranchName() {
    return branchName;
  }

  public void setBranchName(String branchName) {
    this.branchName = branchName;
  }

  public int getId() {
    return this.id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getPartner_id() {
    return this.partner_id;
  }

  public void setPartner_id(int partner_id) {
    this.partner_id = partner_id;
  }

  public String getContract_code() {
    return this.contract_code;
  }

  public void setContract_code(String contract_code) {
    this.contract_code = contract_code;
  }

  public String getType_id() {
    return this.type_id;
  }

  public void setType_id(String type_id) {
    this.type_id = type_id;
  }

  public Date getSign_date() {
    return this.sign_date;
  }

  public void setSign_date(Date sign_date) {
    this.sign_date = sign_date;
  }

  public Date getCompleted_date() {
    return this.completed_date;
  }

  public void setCompleted_date(Date completed_date) {
    this.completed_date = completed_date;
  }

  public String getCard_type_payment() {
    return this.card_type_payment;
  }

  public void setCard_type_payment(String card_type_payment) {
    this.card_type_payment = card_type_payment;
  }

  public String getBank_id() {
    return this.bank_id;
  }

  public void setBank_id(String bank_id) {
    this.bank_id = bank_id;
  }

  public String getBranch() {
    return this.branch;
  }

  public void setBranch(String branch) {
    this.branch = branch;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getTitle() {
    return this.title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getAccount() {
    return this.account;
  }

  public void setAccount(String account) {
    this.account = account;
  }

  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public Date getClose_date() {
    return this.close_date;
  }

  public void setClose_date(Date close_date) {
    this.close_date = close_date;
  }

  public Date getExpire_date() {
    return this.expire_date;
  }

  public void setExpire_date(Date expire_date) {
    this.expire_date = expire_date;
  }

  public int getFin_account() {
    return this.fin_account;
  }

  public void setFin_account(int fin_account) {
    this.fin_account = fin_account;
  }

  public String getType() {
    return this.type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getGuarantee_type() {
    return this.guarantee_type;
  }

  public void setGuarantee_type(String guarantee_type) {
    this.guarantee_type = guarantee_type;
  }

  public String getGuarantee_detail() {
    return this.guarantee_detail;
  }

  public void setGuarantee_detail(String guarantee_detail) {
    this.guarantee_detail = guarantee_detail;
  }

  public String getNote() {
    return this.note;
  }

  public void setNote(String note) {
    this.note = note;
  }

  public int getParent() {
    return this.parent;
  }

  public void setParent(int parent) {
    this.parent = parent;
  }

  public String getAccount_name() {
    return this.account_name;
  }

  public void setAccount_name(String account_name) {
    this.account_name = account_name;
  }

  public String getTel() {
    return this.tel;
  }

  public void setTel(String tel) {
    this.tel = tel;
  }

  public String getFax() {
    return this.fax;
  }

  public void setFax(String fax) {
    this.fax = fax;
  }

  public String getAddress() {
    return this.address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getFee_setup() {
    return this.fee_setup;
  }

  public void setFee_setup(String fee_setup) {
    this.fee_setup = fee_setup;
  }

  public String getPosition() {
    return this.position;
  }

  public void setPosition(String position) {
    this.position = position;
  }

  public String getWebsite() {
    return this.website;
  }

  public void setWebsite(String website) {
    this.website = website;
  }

  public String getSettlement() {
    return this.settlement;
  }

  public void setSettlement(String settlement) {
    this.settlement = settlement;
  }

  public String getTen_dkkd() {
    return this.ten_dkkd;
  }

  public void setTen_dkkd(String ten_dkkd) {
    this.ten_dkkd = ten_dkkd;
  }

  public String getBank_gd_id() {
    return this.bank_gd_id;
  }

  public void setBank_gd_id(String bank_gd_id) {
    this.bank_gd_id = bank_gd_id;
  }

  public String getThoi_gian_tam_ngung() {
    return this.thoi_gian_tam_ngung;
  }

  public void setThoi_gian_tam_ngung(String thoi_gian_tam_ngung) {
    this.thoi_gian_tam_ngung = thoi_gian_tam_ngung;
  }

  public String getHinh_thuc_tam_ung() {
    return this.hinh_thuc_tam_ung;
  }

  public void setHinh_thuc_tam_ung(String hinh_thuc_tam_ung) {
    this.hinh_thuc_tam_ung = hinh_thuc_tam_ung;
  }

  public String getUser_update() {
    return this.user_update;
  }

  public void setUser_update(String user_update) {
    this.user_update = user_update;
  }

  public Date getUpdate_time() {
    return this.update_time;
  }

  public void setUpdate_time(Date update_time) {
    this.update_time = update_time;
  }

  public String getUser_approve() {
    return this.user_approve;
  }

  public void setUser_approve(String user_approve) {
    this.user_approve = user_approve;
  }

  public Date getApprove_time() {
    return this.approve_time;
  }

  public void setApprove_time(Date approve_time) {
    this.approve_time = approve_time;
  }

  public String getState_approve() {
    return this.state_approve;
  }

  public void setState_approve(String state_approve) {
    this.state_approve = state_approve;
  }

  public String getName_bank_account() {
    return this.name_bank_account;
  }

  public void setName_bank_account(String name_bank_account) {
    this.name_bank_account = name_bank_account;
  }

  public String getActive() {
    return this.active;
  }

  public void setActive(String active) {
    this.active = active;
  }

  public int getSetup_fee() {
    return this.setup_fee;
  }

  public void setSetup_fee(int setup_fee) {
    this.setup_fee = setup_fee;
  }

  public String getCurrency_setup_fee() {
    return this.currency_setup_fee;
  }

  public void setCurrency_setup_fee(String currency_setup_fee) {
    this.currency_setup_fee = currency_setup_fee;
  }

  public String getShort_name() {
    return this.short_name;
  }

  public void setShort_name(String short_name) {
    this.short_name = short_name;
  }

  public String getSo_dkkd() {
    return this.so_dkkd;
  }

  public void setSo_dkkd(String so_dkkd) {
    this.so_dkkd = so_dkkd;
  }

  public Date getIssue() {
    return this.issue;
  }

  public void setIssue(Date issue) {
    this.issue = issue;
  }

  public int getGuarantee() {
    return this.guarantee;
  }

  public void setGuarantee(int guarantee) {
    this.guarantee = guarantee;
  }

  public String getGuarantee_account() {
    return this.guarantee_account;
  }

  public void setGuarantee_account(String guarantee_account) {
    this.guarantee_account = guarantee_account;
  }

  public String getAdvance_1st() {
    return this.advance_1st;
  }

  public void setAdvance_1st(String advance_1st) {
    this.advance_1st = advance_1st;
  }

  public String getAdvance_2st_date() {
    return this.advance_2st_date;
  }

  public void setAdvance_2st_date(String advance_2st_date) {
    this.advance_2st_date = advance_2st_date;
  }

  public String getInit_fee() {
    return this.init_fee;
  }

  public void setInit_fee(String init_fee) {
    this.init_fee = init_fee;
  }

  public String getInter_card_fee() {
    return this.inter_card_fee;
  }

  public void setInter_card_fee(String inter_card_fee) {
    this.inter_card_fee = inter_card_fee;
  }

  public String getDomes_card_init_fee() {
    return this.domes_card_init_fee;
  }

  public void setDomes_card_init_fee(String domes_card_init_fee) {
    this.domes_card_init_fee = domes_card_init_fee;
  }

  public String getDomes_token_fee() {
    return this.domes_token_fee;
  }

  public void setDomes_token_fee(String domes_token_fee) {
    this.domes_token_fee = domes_token_fee;
  }

  public String getMonthly_service_fee() {
    return this.monthly_service_fee;
  }

  public void setMonthly_service_fee(String monthly_service_fee) {
    this.monthly_service_fee = monthly_service_fee;
  }

  public String getTrans_process_fee() {
    return this.trans_process_fee;
  }

  public void setTrans_process_fee(String trans_process_fee) {
    this.trans_process_fee = trans_process_fee;
  }

  public String getTrans_process_fee_qt() {
    return this.trans_process_fee_qt;
  }

  public void setTrans_process_fee_qt(String trans_process_fee_qt) {
    this.trans_process_fee_qt = trans_process_fee_qt;
  }

  public String getTrans_process_fee_nd() {
    return this.trans_process_fee_nd;
  }

  public void setTrans_process_fee_nd(String trans_process_fee_nd) {
    this.trans_process_fee_nd = trans_process_fee_nd;
  }

  public String getCard_fee() {
    return this.card_fee;
  }

  public void setCard_fee(String card_fee) {
    this.card_fee = card_fee;
  }

  public int getFd_month() {
    return this.fd_month;
  }

  public void setFd_month(int fd_month) {
    this.fd_month = fd_month;
  }

  public String getFee_collect_form() {
    return this.fee_collect_form;
  }

  public void setFee_collect_form(String fee_collect_form) {
    this.fee_collect_form = fee_collect_form;
  }

  public String getBank_notify_email() {
    return this.bank_notify_email;
  }

  public void setBank_notify_email(String bank_notify_email) {
    this.bank_notify_email = bank_notify_email;
  }

  public String getTrans_notify_email() {
    return this.trans_notify_email;
  }

  public void setTrans_notify_email(String trans_notify_email) {
    this.trans_notify_email = trans_notify_email;
  }

  public String getInter_card_fee_currency() {
    return this.inter_card_fee_currency;
  }

  public void setInter_card_fee_currency(String inter_card_fee_currency) {
    this.inter_card_fee_currency = inter_card_fee_currency;
  }

  public String getDomes_card_init_fee_currency() {
    return this.domes_card_init_fee_currency;
  }

  public void setDomes_card_init_fee_currency(String domes_card_init_fee_currency) {
    this.domes_card_init_fee_currency = domes_card_init_fee_currency;
  }

  public String getDomes_token_fee_currency() {
    return this.domes_token_fee_currency;
  }

  public void setDomes_token_fee_currency(String domes_token_fee_currency) {
    this.domes_token_fee_currency = domes_token_fee_currency;
  }

  public String getInit_fee_currency() {
    return this.init_fee_currency;
  }

  public void setInit_fee_currency(String init_fee_currency) {
    this.init_fee_currency = init_fee_currency;
  }

  public String getTrans_process_fee_currency() {
    return this.trans_process_fee_currency;
  }

  public void setTrans_process_fee_currency(String trans_process_fee_currency) {
    this.trans_process_fee_currency = trans_process_fee_currency;
  }

  public String getTrans_process_fee_qt_currency() {
    return this.trans_process_fee_qt_currency;
  }

  public void setTrans_process_fee_qt_currency(String trans_process_fee_qt_currency) {
    this.trans_process_fee_qt_currency = trans_process_fee_qt_currency;
  }

  public String getTrans_process_fee_nd_currency() {
    return this.trans_process_fee_nd_currency;
  }

  public void setTrans_process_fee_nd_currency(String trans_process_fee_nd_currency) {
    this.trans_process_fee_nd_currency = trans_process_fee_nd_currency;
  }

  public String getMonthly_service_fee_currency() {
    return this.monthly_service_fee_currency;
  }

  public void setMonthly_service_fee_currency(String monthly_service_fee_currency) {
    this.monthly_service_fee_currency = monthly_service_fee_currency;
  }

  public String getContract_form() {
    return contract_form;
  }

  public ContractDto setContract_form(String contract_form) {
    this.contract_form = contract_form;
    return this;
  }

  public String getNote_card_fee() {
    return this.note_card_fee;
  }

  public void setNote_card_fee(String note_card_fee) {
    this.note_card_fee = note_card_fee;
  }

  public ContractDto id(int id) {
    this.id = id;
    return this;
  }

  public ContractDto partner_id(int partner_id) {
    this.partner_id = partner_id;
    return this;
  }

  public ContractDto contract_code(String contract_code) {
    this.contract_code = contract_code;
    return this;
  }

  public ContractDto type_id(String type_id) {
    this.type_id = type_id;
    return this;
  }

  public ContractDto sign_date(Date sign_date) {
    this.sign_date = sign_date;
    return this;
  }

  public ContractDto completed_date(Date completed_date) {
    this.completed_date = completed_date;
    return this;
  }

  public ContractDto card_type_payment(String card_type_payment) {
    this.card_type_payment = card_type_payment;
    return this;
  }

  public ContractDto bank_id(String bank_id) {
    this.bank_id = bank_id;
    return this;
  }

  public ContractDto branch(String branch) {
    this.branch = branch;
    return this;
  }

  public ContractDto branchName(String branchName) {
    this.branchName = branchName;
    return this;
  }

  public ContractDto name(String name) {
    this.name = name;
    return this;
  }

  public ContractDto title(String title) {
    this.title = title;
    return this;
  }

  public ContractDto account(String account) {
    this.account = account;
    return this;
  }

  public ContractDto status(String status) {
    this.status = status;
    return this;
  }

  public ContractDto close_date(Date close_date) {
    this.close_date = close_date;
    return this;
  }

  public ContractDto expire_date(Date expire_date) {
    this.expire_date = expire_date;
    return this;
  }

  public ContractDto fin_account(int fin_account) {
    this.fin_account = fin_account;
    return this;
  }

  public ContractDto type(String type) {
    this.type = type;
    return this;
  }

  public ContractDto guarantee_type(String guarantee_type) {
    this.guarantee_type = guarantee_type;
    return this;
  }

  public ContractDto guarantee_detail(String guarantee_detail) {
    this.guarantee_detail = guarantee_detail;
    return this;
  }

  public ContractDto note(String note) {
    this.note = note;
    return this;
  }

  public ContractDto parent(int parent) {
    this.parent = parent;
    return this;
  }

  public ContractDto account_name(String account_name) {
    this.account_name = account_name;
    return this;
  }

  public ContractDto tel(String tel) {
    this.tel = tel;
    return this;
  }

  public ContractDto fax(String fax) {
    this.fax = fax;
    return this;
  }

  public ContractDto address(String address) {
    this.address = address;
    return this;
  }

  public ContractDto fee_setup(String fee_setup) {
    this.fee_setup = fee_setup;
    return this;
  }

  public ContractDto position(String position) {
    this.position = position;
    return this;
  }

  public ContractDto website(String website) {
    this.website = website;
    return this;
  }

  public ContractDto settlement(String settlement) {
    this.settlement = settlement;
    return this;
  }

  public ContractDto ten_dkkd(String ten_dkkd) {
    this.ten_dkkd = ten_dkkd;
    return this;
  }

  public ContractDto bank_gd_id(String bank_gd_id) {
    this.bank_gd_id = bank_gd_id;
    return this;
  }

  public ContractDto thoi_gian_tam_ngung(String thoi_gian_tam_ngung) {
    this.thoi_gian_tam_ngung = thoi_gian_tam_ngung;
    return this;
  }

  public ContractDto hinh_thuc_tam_ung(String hinh_thuc_tam_ung) {
    this.hinh_thuc_tam_ung = hinh_thuc_tam_ung;
    return this;
  }

  public ContractDto user_update(String user_update) {
    this.user_update = user_update;
    return this;
  }

  public ContractDto update_time(Date update_time) {
    this.update_time = update_time;
    return this;
  }

  public ContractDto user_approve(String user_approve) {
    this.user_approve = user_approve;
    return this;
  }

  public ContractDto approve_time(Date approve_time) {
    this.approve_time = approve_time;
    return this;
  }

  public ContractDto state_approve(String state_approve) {
    this.state_approve = state_approve;
    return this;
  }

  public ContractDto name_bank_account(String name_bank_account) {
    this.name_bank_account = name_bank_account;
    return this;
  }

  public ContractDto active(String active) {
    this.active = active;
    return this;
  }

  public ContractDto setup_fee(int setup_fee) {
    this.setup_fee = setup_fee;
    return this;
  }

  public ContractDto currency_setup_fee(String currency_setup_fee) {
    this.currency_setup_fee = currency_setup_fee;
    return this;
  }

  public ContractDto short_name(String short_name) {
    this.short_name = short_name;
    return this;
  }

  public ContractDto so_dkkd(String so_dkkd) {
    this.so_dkkd = so_dkkd;
    return this;
  }

  public ContractDto issue(Date issue) {
    this.issue = issue;
    return this;
  }

  public ContractDto guarantee(int guarantee) {
    this.guarantee = guarantee;
    return this;
  }

  public ContractDto guarantee_account(String guarantee_account) {
    this.guarantee_account = guarantee_account;
    return this;
  }

  public ContractDto advance_1st(String advance_1st) {
    this.advance_1st = advance_1st;
    return this;
  }

  public ContractDto advance_2st_date(String advance_2st_date) {
    this.advance_2st_date = advance_2st_date;
    return this;
  }

  public ContractDto init_fee(String init_fee) {
    this.init_fee = init_fee;
    return this;
  }

  public ContractDto inter_card_fee(String inter_card_fee) {
    this.inter_card_fee = inter_card_fee;
    return this;
  }

  public ContractDto domes_card_init_fee(String domes_card_init_fee) {
    this.domes_card_init_fee = domes_card_init_fee;
    return this;
  }

  public ContractDto domes_token_fee(String domes_token_fee) {
    this.domes_token_fee = domes_token_fee;
    return this;
  }

  public ContractDto monthly_service_fee(String monthly_service_fee) {
    this.monthly_service_fee = monthly_service_fee;
    return this;
  }

  public ContractDto trans_process_fee(String trans_process_fee) {
    this.trans_process_fee = trans_process_fee;
    return this;
  }

  public ContractDto trans_process_fee_qt(String trans_process_fee_qt) {
    this.trans_process_fee_qt = trans_process_fee_qt;
    return this;
  }

  public ContractDto trans_process_fee_nd(String trans_process_fee_nd) {
    this.trans_process_fee_nd = trans_process_fee_nd;
    return this;
  }

  public ContractDto card_fee(String card_fee) {
    this.card_fee = card_fee;
    return this;
  }

  public ContractDto fd_month(int fd_month) {
    this.fd_month = fd_month;
    return this;
  }

  public ContractDto fee_collect_form(String fee_collect_form) {
    this.fee_collect_form = fee_collect_form;
    return this;
  }

  public ContractDto bank_notify_email(String bank_notify_email) {
    this.bank_notify_email = bank_notify_email;
    return this;
  }

  public ContractDto trans_notify_email(String trans_notify_email) {
    this.trans_notify_email = trans_notify_email;
    return this;
  }

  public ContractDto inter_card_fee_currency(String inter_card_fee_currency) {
    this.inter_card_fee_currency = inter_card_fee_currency;
    return this;
  }

  public ContractDto domes_card_init_fee_currency(String domes_card_init_fee_currency) {
    this.domes_card_init_fee_currency = domes_card_init_fee_currency;
    return this;
  }

  public ContractDto domes_token_fee_currency(String domes_token_fee_currency) {
    this.domes_token_fee_currency = domes_token_fee_currency;
    return this;
  }

  public ContractDto init_fee_currency(String init_fee_currency) {
    this.init_fee_currency = init_fee_currency;
    return this;
  }

  public ContractDto trans_process_fee_currency(String trans_process_fee_currency) {
    this.trans_process_fee_currency = trans_process_fee_currency;
    return this;
  }

  public ContractDto trans_process_fee_qt_currency(String trans_process_fee_qt_currency) {
    this.trans_process_fee_qt_currency = trans_process_fee_qt_currency;
    return this;
  }

  public ContractDto trans_process_fee_nd_currency(String trans_process_fee_nd_currency) {
    this.trans_process_fee_nd_currency = trans_process_fee_nd_currency;
    return this;
  }

  public ContractDto contract_form(String contract_form) {
    this.contract_form = contract_form;
    return this;
  }

  public ContractDto monthly_service_fee_currency(String monthly_service_fee_currency) {
    this.monthly_service_fee_currency = monthly_service_fee_currency;
    return this;
  }

  public ContractDto note_card_fee(String note_card_fee) {
    this.note_card_fee = note_card_fee;
    return this;
  }

  @Override
  public String toString() {
    return "{" + " id='" + getId() + "'" + ", partner_id='" + getPartner_id() + "'" + ", contract_code='"
            + getContract_code() + "'" + ", type_id='" + getType_id() + "'" + ", sign_date='" + getSign_date() + "'"
            + ", completed_date='" + getCompleted_date() + "'" + ", card_type_payment='" + getCard_type_payment() + "'"
            + ", bank_id='" + getBank_id() + "'" + ", branch='" + getBranch() + "'" + ", name='" + getName() + "'"
            + ", title='" + getTitle() + "'" + ", account='" + getAccount() + "'" + ", status='" + getStatus() + "'"
            + ", close_date='" + getClose_date() + "'" + ", expire_date='" + getExpire_date() + "'" + ", fin_account='"
            + getFin_account() + "'" + ", type='" + getType() + "'" + ", guarantee_type='" + getGuarantee_type() + "'"
            + ", guarantee_detail='" + getGuarantee_detail() + "'" + ", note='" + getNote() + "'" + ", parent='"
            + getParent() + "'" + ", account_name='" + getAccount_name() + "'" + ", tel='" + getTel() + "'" + ", fax='"
            + getFax() + "'" + ", address='" + getAddress() + "'" + ", fee_setup='" + getFee_setup() + "'" + ", position='"
            + getPosition() + "'" + ", website='" + getWebsite() + "'" + ", settlement='" + getSettlement() + "'"
            + ", ten_dkkd='" + getTen_dkkd() + "'" + ", bank_gd_id='" + getBank_gd_id() + "'" + ", thoi_gian_tam_ngung='"
            + getThoi_gian_tam_ngung() + "'" + ", hinh_thuc_tam_ung='" + getHinh_thuc_tam_ung() + "'" + ", user_update='"
            + getUser_update() + "'" + ", update_time='" + getUpdate_time() + "'" + ", user_approve='" + getUser_approve()
            + "'" + ", approve_time='" + getApprove_time() + "'" + ", state_approve='" + getState_approve() + "'"
            + ", name_bank_account='" + getName_bank_account() + "'" + ", active='" + getActive() + "'" + ", setup_fee='"
            + getSetup_fee() + "'" + ", currency_setup_fee='" + getCurrency_setup_fee() + "'" + ", short_name='"
            + getShort_name() + "'" + ", so_dkkd='" + getSo_dkkd() + "'" + ", issue='" + getIssue() + "'" + ", guarantee='"
            + getGuarantee() + "'" + ", guarantee_account='" + getGuarantee_account() + "'" + ", advance_1st='"
            + getAdvance_1st() + "'" + ", advance_2st_date='" + getAdvance_2st_date() + "'" + ", init_fee='" + getInit_fee()
            + "'" + ", inter_card_fee='" + getInter_card_fee() + "'" + ", domes_card_init_fee='" + getDomes_card_init_fee()
            + "'" + ", domes_token_fee='" + getDomes_token_fee() + "'" + ", monthly_service_fee='"
            + getMonthly_service_fee() + "'" + ", trans_process_fee='" + getTrans_process_fee() + "'"
            + ", trans_process_fee_qt='" + getTrans_process_fee_qt() + "'" + ", trans_process_fee_nd='"
            + getTrans_process_fee_nd() + "'" + ", card_fee='" + getCard_fee() + "'" + ", fd_month='" + getFd_month() + "'"
            + ", fee_collect_form='" + getFee_collect_form() + "'" + ", bank_notify_email='" + getBank_notify_email() + "'"
            + ", trans_notify_email='" + getTrans_notify_email() + "'" + ", inter_card_fee_currency='"
            + getInter_card_fee_currency() + "'" + ", domes_card_init_fee_currency='" + getDomes_card_init_fee_currency()
            + "'" + ", domes_token_fee_currency='" + getDomes_token_fee_currency() + "'" + ", init_fee_currency='"
            + getInit_fee_currency() + "'" + ", trans_process_fee_currency='" + getTrans_process_fee_currency() + "'"
            + ", trans_process_fee_qt_currency='" + getTrans_process_fee_qt_currency() + "'"
            + ", trans_process_fee_nd_currency='" + getTrans_process_fee_nd_currency() + "'"
            + ", contract_form='" + getContract_form() + "'"
            + ", monthly_service_fee_currency='" + getMonthly_service_fee_currency() + "'" + ", note_card_fee='"
            + getNote_card_fee() + "'" + "}";
  }

}
