/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/28/19 3:16 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class PartnerStatusDto {
    private int countQtActive;
    private int countQtDisable;
    private int countInvoiceActive;
    private int countInvoiceDisable;
    private int countNdActive;
    private int countNdDisable;

    public PartnerStatusDto() {
    }

    public PartnerStatusDto(int countQtActive, int countQtDisable, int countInvoiceActive, int countInvoiceDisable, int countNdActive, int countNdDisable) {
        this.countQtActive = countQtActive;
        this.countQtDisable = countQtDisable;
        this.countInvoiceActive = countInvoiceActive;
        this.countInvoiceDisable = countInvoiceDisable;
        this.countNdActive = countNdActive;
        this.countNdDisable = countNdDisable;
    }

    public int getCountQtActive() {
        return countQtActive;
    }

    public void setCountQtActive(int countQtActive) {
        this.countQtActive = countQtActive;
    }

    public int getCountQtDisable() {
        return countQtDisable;
    }

    public void setCountQtDisable(int countQtDisable) {
        this.countQtDisable = countQtDisable;
    }

    public int getCountInvoiceActive() {
        return countInvoiceActive;
    }

    public void setCountInvoiceActive(int countInvoiceActive) {
        this.countInvoiceActive = countInvoiceActive;
    }

    public int getCountInvoiceDisable() {
        return countInvoiceDisable;
    }

    public void setCountInvoiceDisable(int countInvoiceDisable) {
        this.countInvoiceDisable = countInvoiceDisable;
    }

    public int getCountNdActive() {
        return countNdActive;
    }

    public void setCountNdActive(int countNdActive) {
        this.countNdActive = countNdActive;
    }

    public int getCountNdDisable() {
        return countNdDisable;
    }

    public void setCountNdDisable(int countNdDisable) {
        this.countNdDisable = countNdDisable;
    }

    public PartnerStatusDto countQtActive(int countQtActive) {
        this.countQtActive = countQtActive;
        return this;
    }

    public PartnerStatusDto countQtDisable(int countQtDisable) {
        this.countQtDisable = countQtDisable;
        return this;
    }

    public PartnerStatusDto countInvoiceActive(int countInvoiceActive) {
        this.countInvoiceActive = countInvoiceActive;
        return this;
    }

    public PartnerStatusDto countInvoiceDisable(int countInvoiceDisable) {
        this.countInvoiceDisable = countInvoiceDisable;
        return this;
    }

    public PartnerStatusDto countNdActive(int countNdActive) {
        this.countNdActive = countNdActive;
        return this;
    }

    public PartnerStatusDto countNdDisable(int countNdDisable) {
        this.countNdDisable = countNdDisable;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " countQtActive='" + getCountQtActive() + "'" + ", countQtDisable='"
                + getCountQtDisable() + "'" + ", getCountInvoiceActive='" + getCountInvoiceActive()
                + "'" + ", getCountInvoiceDisable='" + getCountInvoiceDisable()
                + "'" + ", getCountNdActive='" + getCountNdActive()
                + "'" + ", getCountNdDisable='" + getCountNdDisable()
                + "'" + "}";
    }
}
