/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 4/1/19 5:42 PM
 */

package vn.onepay.portal.resources.merchantmanagement.contract;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.resources.document_tracking.dto.SubDocumentDto;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.BranchDto;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractDto;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractOriginal;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractTypeDto;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.InfoFeeInstallment;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.PaymentGateways;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.UpdateFeeInstallmentReq;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import java.io.*;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.json.JsonObject;

import org.apache.commons.lang.StringUtils;

public class ContractDao extends Db implements IConstants {

    private static final String GET_FEE_INSTALLMENT = "{call PKG_INSTALLMENT.GET_FEE_INSTALLMENT(?,?,?) }";
    private static final String GET_CONTRACT_CLOB = "{call PKG_ONEPARTNER_114.GET_CONTRACT_CLOB(?,?,?,?) }";
    private static final String GET_SUB_CONTRACT_LIST = "{call PKG_ONEPARTNER_114.GET_SUB_CONTRACT_LIST(?,?,?,?) }";
    private static final String GET_CONTRACT_CLOB_DETAIL = "{call PKG_ONEPARTNER_114.GET_CONTRACT_CLOB_DETAIL(?,?,?,?) }";
    private static final String GET_SUB_CONTRACT_CLOB_DETAIL = "{call PKG_ONEPARTNER_114.GET_SUB_CONTRACT_CLOB_DETAIL(?,?,?,?) }";
    private static final String SAVE_SUB_CONTRACT_CLOB_DETAIL = "{call PKG_ONEPARTNER_114.SAVE_SUB_CONTRACT_TEMPLATE(?,?,?,?) }";
    private static final String DELETE_CONTRACT_ORIGINAL = "{call PKG_ONEPARTNER_114.DELETE_CONTRACT_CLOB(?,?,?) }";
    private static final String APPROVE_CONTRACT_ORIGINAL = "{call PKG_ONEPARTNER_114.APPROVE_CONTRACT_CLOB(?,?,?) }";
    private static final String REMOVE_APPROVE_CONTRACT = "{call PKG_ONEPARTNER_114.REMOVE_APPROVE_CONTRACT(?,?,?) }";
    private static final String PUSH_CONTRACT_ORIGINAL = "{call PKG_ONEPARTNER_114.PUSH_CONTRACT_CLOB(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String PUSH_CONTRACT_ORIGINAL_TEMPLATE = "{call PKG_ONEPARTNER_114.push_contract_clob_template(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String PUSH_CONTRACT_ORIGINAL_V2 = "{call PKG_ONEPARTNER_114.PUSH_CONTRACT_CLOB_V2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String CHECK_CONTRACT_NUMBER = "{call PKG_ONEPARTNER_114.CHECK_CONTRACT_NUMBER(?,?,?,?,?) }";
    private static final String UPDATE_FEE_INSTALLMENT = "{call PKG_INSTALLMENT.UPDATE_FEE_INSTALLMENT_V2(?,?,?,?,?,?,?,?,?) }";
    private static final Logger LOGGER = Logger.getLogger(ContractDao.class.getName());

    public static BaseList<ContractTypeDto> getContractTypeList() throws Exception {
        Exception exception = null;
        BaseList<ContractTypeDto> result = new BaseList<>();
        List<ContractTypeDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{? = call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.get_all_contract_types()}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                list.add(new ContractTypeDto().type(rs.getString("S_TYPE")).name(rs.getString("S_NAME"))
                        .bank(rs.getString("S_BANK")).flowType(rs.getString("S_FLOW_TYPE"))
                        .order(rs.getInt("N_ORDER")));
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<BranchDto> getBranchList() throws Exception {
        Exception exception = null;
        BaseList<BranchDto> result = new BaseList<>();
        List<BranchDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.LOAD_CHI_NHANH(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get list branch error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new BranchDto().id(rs.getInt("N_ID")).name(rs.getString("S_NAME"))
                            .bank(rs.getString("S_BANK")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<ContractDto> getContractsByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<ContractDto> result = new BaseList<>();
        List<ContractDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_contract_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load contract by partner id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ContractDto().id(rs.getInt("N_ID")).partner_id(rs.getInt("N_PARTNER_ID"))
                            .contract_code(rs.getString("S_CONTRACT_CODE")).type_id(rs.getString("S_TYPE_ID"))
                            .sign_date(rs.getDate("D_SIGN_DATE")).completed_date(rs.getDate("D_COMPLETED_DATE"))
                            .card_type_payment(rs.getString("S_CARD_TYPE_PAYMENT")).bank_id(rs.getString("S_BANK_ID"))
                            .branch(rs.getString("S_BRANCH_NAME")).name(rs.getString("S_NAME"))
                            .title(rs.getString("S_TITLE")).account(rs.getString("S_ACCOUNT"))
                            .status(rs.getString("S_STATUS")).close_date(rs.getDate("D_CLOSED_DATE"))
                            .expire_date(rs.getDate("D_EXPIRED_DATE")).fin_account(rs.getInt("N_FIN_ACCOUNT"))
                            .type(rs.getString("S_TYPE")).guarantee_type(rs.getString("S_GUARANTEE_TYPE"))
                            .guarantee_detail(rs.getString("S_GUARANTEE_DETAIL")).note(rs.getString("S_NOTE"))
                            .parent(rs.getInt("N_PARENT")).account_name(rs.getString("S_ACCOUNT_NAME"))
                            .tel(rs.getString("S_TEL")).fax(rs.getString("S_FAX")).address(rs.getString("S_ADDRESS"))
                            .fee_setup(rs.getString("S_FEE_SETUP")).position(rs.getString("S_POSITION"))
                            .website(rs.getString("S_WEBSITE")).settlement(rs.getString("S_SETTLEMENT"))
                            .ten_dkkd(rs.getString("S_TEN_DKKD")).bank_gd_id(rs.getString("S_BANK_GD_ID"))
                            .thoi_gian_tam_ngung(rs.getString("S_THOIGIAN_TAMUNG"))
                            .hinh_thuc_tam_ung(rs.getString("S_SETTELMENT_TYPE"))
                            .user_update(rs.getString("S_USER_UPDATE")).update_time(rs.getDate("D_UPDATE_TIME"))
                            .user_approve(rs.getString("S_USER_APPROVE")).approve_time(rs.getDate("D_APPROVE_TIME"))
                            .state_approve(rs.getString("S_STATE_APPROVE"))
                            .name_bank_account(rs.getString("S_NAME_BANK_ACCOUNT")).active(rs.getString("S_ACTIVE"))
                            .setup_fee(rs.getInt("N_SETUP_FEE"))
                            .currency_setup_fee(rs.getString("S_CURRENCY_SETUP_FEE"))
                            .short_name(rs.getString("S_SHORT_NAME")).so_dkkd(rs.getString("S_SO_DKKD"))
                            .issue(rs.getDate("D_ISSUE")).guarantee_account(rs.getString("S_GUARANTEE_ACCOUNT"))
                            .advance_1st(rs.getString("S_1ST_ADVANCE")).advance_2st_date(rs.getString("S_2ND_ADVANCE"))
                            .init_fee(rs.getString("S_INIT_FEE")).inter_card_fee(rs.getString("S_INTER_CARD_INIT_FEE"))
                            .domes_card_init_fee(rs.getString("S_DOMES_CARD_INIT_FEE"))
                            .domes_token_fee(rs.getString("S_DOMES_TOKEN_INIT_FEE"))
                            .monthly_service_fee(rs.getString("S_MONTHLY_SERVICE_FEE"))
                            .trans_process_fee(rs.getString("S_TRANS_PROCESS_FEE"))
                            .trans_process_fee_qt(rs.getString("S_TRANS_PROCESS_FEE_QT"))
                            .trans_process_fee_nd(rs.getString("S_TRANS_PROCESS_FEE_ND"))
                            .card_fee(rs.getString("S_CARD_FEE")).fd_month(rs.getInt("N_FD_MONTH"))
                            .fee_collect_form(rs.getString("S_FEE_COLLECT_FORM"))
                            .bank_notify_email(rs.getString("S_BANK_NOTIFY_BANK"))
                            .trans_notify_email(rs.getString("S_TRAN_NOTIFY_EMAIL"))
                            .inter_card_fee_currency(rs.getString("S_INTER_CARD_FEE_CURRENCY"))
                            .domes_card_init_fee_currency(rs.getString("S_DOMES_CARD_INIT_FEE_CURRENCY"))
                            .domes_token_fee_currency(rs.getString("S_DOMES_TOKEN_FEE_CURRENCY"))
                            .init_fee_currency(rs.getString("S_INIT_FEE_CURRENCY"))
                            .trans_process_fee_currency(rs.getString("S_TRANS_PROCESS_FEE_CURRENCY"))
                            .trans_process_fee_qt_currency(rs.getString("S_TXN_PROCESS_FEE_QT_CURRENCY"))
                            .trans_process_fee_nd_currency(rs.getString("S_TXN_PROCESS_FEE_ND_CURRENCY"))
                            .monthly_service_fee_currency(rs.getString("S_MONTHLY_SERVICE_FEE_CURRENCY"))
                            .note_card_fee(rs.getString("S_NOTE_CARD_FEE"))
                            .contract_form(rs.getString("S_CONTRACT_FORM")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static ActionDto RemoveContractByID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.remove_contract_by_id(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            if (nError == 0) {
                logger.severe("DB Remove Contract error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto InsertAndUpdateContract(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.insert_update_contract(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setInt(5, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(6, mIn.get(CONTRACT_CODE).toString());
            cs.setString(7, mIn.get(TYPE_ID).toString());
            cs.setTimestamp(8, Convert.toTimestamp(Convert.toDate(mIn.get(SIGN_DATE).toString(), "dd/MM/yyyy")));
            cs.setObject(9, (mIn.get(COMPLETED_DATE) == null || mIn.get(COMPLETED_DATE).toString().trim().isBlank() || mIn.get(COMPLETED_DATE).toString().equalsIgnoreCase("aN/aN/NaN")) ? null: Convert.toTimestamp(Convert.toDate(mIn.get(COMPLETED_DATE).toString(), "dd/MM/yyyy")));
            cs.setString(10, mIn.get(BANK_ID).toString());
            cs.setString(11, mIn.get(BRANCH).toString());
            cs.setString(12, mIn.get(NAME).toString());
            cs.setString(13, mIn.get(TITLE).toString());
            cs.setString(14, mIn.get(ACCOUNT).toString());
            cs.setString(15, mIn.get(STATUS).toString());
            cs.setTimestamp(16, Convert.toTimestamp(Convert.toDate(mIn.get(CLOSE_DATE).toString(), "dd/MM/yyyy")));
            cs.setTimestamp(17, Convert.toTimestamp(Convert.toDate(mIn.get(EXPIRED_DATE).toString(), "dd/MM/yyyy")));
            cs.setInt(18, Convert.parseInt(mIn.get(ACCOUNT).toString(), 0));
            cs.setString(19, mIn.get(TYPE).toString());
            cs.setString(20, mIn.get(GUARANTEE_TYPE).toString());
            cs.setString(21, mIn.get(GUARANTEE_DETAIL).toString());
            cs.setString(22, mIn.get(NOTE).toString());
            cs.setInt(23, Convert.parseInt(mIn.get(PARENT).toString(), 0));
            cs.setString(24, mIn.get(ACCOUNT_NAME).toString());
            cs.setString(25, mIn.get(TEL).toString());
            cs.setString(26, mIn.get(FAX).toString());
            cs.setString(27, mIn.get(ADDRESS).toString());
            cs.setString(28, mIn.get(SETUP_FEE).toString());
            cs.setString(29, mIn.get(POSITION).toString());
            cs.setString(30, mIn.get(WEBSITE).toString());
            cs.setString(31, mIn.get(SETTLEMENT).toString());
            cs.setString(32, mIn.get(TEN_DKKD).toString());
            cs.setString(33, mIn.get(BANK_GD_ID).toString());
            cs.setString(34, mIn.get(THOIGIAN_TAMUNG).toString());
            cs.setString(35, mIn.get(USER_UPDATE).toString());
            cs.setTimestamp(36, Convert.toTimestamp(Convert.toDate(mIn.get(UPDATE_TIME).toString(), "dd/MM/yyyy")));
            cs.setString(37, mIn.get(USER_APPROVE).toString());
            cs.setTimestamp(38, Convert.toTimestamp(Convert.toDate(mIn.get(APPROVE_TIME).toString(), "dd/MM/yyyy")));
            cs.setString(39, mIn.get(STATE_APPROVE).toString());
            cs.setString(40, mIn.get(NAME_BANK_ACCOUNT).toString());
            cs.setString(41, mIn.get(ACTIVE).toString());
            cs.setInt(42, Convert.parseInt(mIn.get(SETUP_FEE).toString(), 0));
            cs.setString(43, mIn.get(SETUP_FEE_CURRENCY).toString());
            cs.setString(44, mIn.get(SHORT_NAME).toString());
            cs.setString(45, mIn.get(SO_DKKD).toString());
            cs.setTimestamp(46, Convert.toTimestamp(Convert.toDate(mIn.get(ISSUE).toString(), "dd/MM/yyyy")));
            cs.setString(47, mIn.get(GUARANTEE_ACCOUNT).toString());
            cs.setString(48, mIn.get(ADVANCE_1ST).toString());
            cs.setString(49, mIn.get(ADVANCE_2ST_DATE).toString());
            // cs.setTimestamp(50,
            // Convert.toTimestamp(Convert.toDate(mIn.get(ADVANCE_2ST_DATE).toString(),
            // "dd/MM/yyyy")));
            cs.setString(50, mIn.get(INIT_FEE).toString());
            cs.setString(51, mIn.get(INTER_CARD_FEE).toString());
            cs.setString(52, mIn.get(DOMES_CARD_INIT_FEE).toString());
            cs.setString(53, mIn.get(DOMES_TOKEN_INIT_FEE).toString());
            cs.setString(54, mIn.get(MONTHLY_SERVICE_FEE).toString());
            cs.setString(55, mIn.get(TRANS_PROCESS_FEE).toString());
            cs.setString(56, mIn.get(CARD_FEE).toString());
            cs.setInt(57, Convert.parseInt(mIn.get(FD_MONTH).toString(), 0));
            cs.setString(58, mIn.get(FEE_COLLECT_FORM).toString());
            cs.setString(59, mIn.get(BANK_NOTIFY_EMAIL).toString());
            cs.setString(60, mIn.get(TRANS_NOTIFY_EMAIL).toString());
            cs.setString(61, mIn.get(SETTLEMENT_TYPE).toString());
            cs.setString(62, mIn.get(INTER_CARD_FEE_CURRENCY).toString());
            cs.setString(63, mIn.get(DOMES_CARD_INIT_FEE_CURRENCY).toString());
            cs.setString(64, mIn.get(DOMES_TOKEN_FEE_CURRENCY).toString());
            cs.setString(65, mIn.get(INIT_FEE_CURRENCY).toString());
            cs.setString(66, mIn.get(TRANS_PROCESS_FEE_CURRENCY).toString());
            cs.setString(67, mIn.get(MONTHLY_SERVICE_FEE_CURRENCY).toString());
            cs.setString(68, mIn.get(CARD_TYPE_PAYMENT).toString());
            cs.setString(69, mIn.get(NOTE_CARD_FEE).toString());
            cs.setString(70, mIn.get(TRANS_PROCESS_FEE_QT).toString());
            cs.setString(71, mIn.get(TRANS_PROCESS_FEE_QT_CURRENCY).toString());
            cs.setString(72, mIn.get(TRANS_PROCESS_FEE_ND).toString());
            cs.setString(73, mIn.get(TRANS_PROCESS_FEE_ND_CURRENCY).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                logger.severe("DB update Contract error: " + error + "| id Contract:" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // duynq get contractById
    public static ContractDto getContractById(Map mIn) throws Exception {
        Exception exception = null;
        ContractDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_contract_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load contract by id error: " + error);
            } else {
                if (rs != null && rs.next()) {
                    result = new ContractDto().id(rs.getInt("N_ID")).partner_id(rs.getInt("N_PARTNER_ID"))
                            .contract_code(rs.getString("S_CONTRACT_CODE")).type_id(rs.getString("S_TYPE_ID"))
                            .sign_date(Convert.toDate(rs.getString("D_SIGN_DATE"), "yyyyMMddHHmmss"))
                            .completed_date(Convert.toDate(rs.getString("D_COMPLETED_DATE"), "yyyyMMddHHmmss"))
                            .card_type_payment(rs.getString("S_CARD_TYPE_PAYMENT")).bank_id(rs.getString("S_BANK_ID"))
                            .branch(rs.getString("S_BRANCH_NAME")).name(rs.getString("S_NAME"))
                            .title(rs.getString("S_TITLE")).account(rs.getString("S_ACCOUNT"))
                            .status(rs.getString("S_STATUS"))
                            .close_date(Convert.toDate(rs.getString("D_CLOSED_DATE"), "yyyyMMddHHmmss"))
                            .expire_date(Convert.toDate(rs.getString("D_EXPIRED_DATE"), "yyyyMMddHHmmss"))
                            .fin_account(rs.getInt("N_FIN_ACCOUNT")).type(rs.getString("S_TYPE"))
                            .guarantee_type(rs.getString("S_GUARANTEE_TYPE"))
                            .guarantee_detail(rs.getString("S_GUARANTEE_DETAIL")).note(rs.getString("S_NOTE"))
                            .parent(rs.getInt("N_PARENT")).account_name(rs.getString("S_ACCOUNT_NAME"))
                            .tel(rs.getString("S_TEL")).fax(rs.getString("S_FAX")).address(rs.getString("S_ADDRESS"))
                            .fee_setup(rs.getString("S_FEE_SETUP")).position(rs.getString("S_POSITION"))
                            .website(rs.getString("S_WEBSITE")).settlement(rs.getString("S_SETTLEMENT"))
                            .ten_dkkd(rs.getString("S_TEN_DKKD")).bank_gd_id(rs.getString("S_BANK_GD_ID"))
                            .thoi_gian_tam_ngung(rs.getString("S_THOIGIAN_TAMUNG"))
                            .hinh_thuc_tam_ung(rs.getString("S_SETTELMENT_TYPE"))
                            .user_update(rs.getString("S_USER_UPDATE"))
                            .update_time(Convert.toDate(rs.getString("D_UPDATE_TIME"), "yyyyMMddHHmmss"))
                            .user_approve(rs.getString("S_USER_APPROVE"))
                            .approve_time(Convert.toDate(rs.getString("D_APPROVE_TIME"), "yyyyMMddHHmmss"))
                            .state_approve(rs.getString("S_STATE_APPROVE"))
                            .name_bank_account(rs.getString("S_NAME_BANK_ACCOUNT")).active(rs.getString("S_ACTIVE"))
                            .setup_fee(rs.getInt("N_SETUP_FEE"))
                            .currency_setup_fee(rs.getString("S_CURRENCY_SETUP_FEE"))
                            .short_name(rs.getString("S_SHORT_NAME")).so_dkkd(rs.getString("S_SO_DKKD"))
                            .issue(Convert.toDate(rs.getString("D_ISSUE"), "yyyyMMddHHmmss"))
                            .guarantee_account(rs.getString("S_GUARANTEE_ACCOUNT"))
                            .advance_1st(rs.getString("S_1ST_ADVANCE")).advance_2st_date(rs.getString("S_2ND_ADVANCE"))
                            .init_fee(rs.getString("S_INIT_FEE")).inter_card_fee(rs.getString("S_INTER_CARD_INIT_FEE"))
                            .domes_card_init_fee(rs.getString("S_DOMES_CARD_INIT_FEE"))
                            .domes_token_fee(rs.getString("S_DOMES_TOKEN_INIT_FEE"))
                            .monthly_service_fee(rs.getString("S_MONTHLY_SERVICE_FEE"))
                            .trans_process_fee(rs.getString("S_TRANS_PROCESS_FEE")).card_fee(rs.getString("S_CARD_FEE"))
                            .fd_month(rs.getInt("N_FD_MONTH")).fee_collect_form(rs.getString("S_FEE_COLLECT_FORM"))
                            .bank_notify_email(rs.getString("S_BANK_NOTIFY_BANK"))
                            .trans_notify_email(rs.getString("S_TRAN_NOTIFY_EMAIL"))
                            .inter_card_fee_currency(rs.getString("S_INTER_CARD_FEE_CURRENCY"))
                            .domes_card_init_fee_currency(rs.getString("S_DOMES_CARD_INIT_FEE_CURRENCY"))
                            .domes_token_fee_currency(rs.getString("S_DOMES_TOKEN_FEE_CURRENCY"))
                            .domes_card_init_fee_currency(rs.getString("S_DOMES_CARD_INIT_FEE_CURRENCY"))
                            .domes_token_fee_currency(rs.getString("S_DOMES_TOKEN_FEE_CURRENCY"))
                            .init_fee_currency(rs.getString("S_INIT_FEE_CURRENCY"))
                            .trans_process_fee_currency(rs.getString("S_TRANS_PROCESS_FEE_CURRENCY"))
                            .monthly_service_fee_currency(rs.getString("S_MONTHLY_SERVICE_FEE_CURRENCY"))
                            .note_card_fee(rs.getString("S_NOTE_CARD_FEE"))
                            .trans_process_fee_qt(rs.getString("S_TRANS_PROCESS_FEE_QT"))
                            .trans_process_fee_qt_currency(rs.getString("S_TXN_PROCESS_FEE_QT_CURRENCY"))
                            .trans_process_fee_nd(rs.getString("S_TRANS_PROCESS_FEE_ND"))
                            .trans_process_fee_nd_currency(rs.getString("S_TXN_PROCESS_FEE_ND_CURRENCY"))
                            .contract_form(rs.getString("S_CONTRACT_FORM"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // get list contract payment by contract id
    public static BaseList<PaymentGateways> getListContractPaymentbyContractId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<PaymentGateways> result = new BaseList<>();
        List<PaymentGateways> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_contact_payment(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(CONTRACT_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new PaymentGateways().id(rs.getInt("N_ID")).contract_id(rs.getInt("N_CONTRACT_ID"))
                            .merchant_name(rs.getString("S_MERCHANT_NAME")).paygate(rs.getString("S_PAYGATE"))
                            .advance_account(rs.getString("S_ADVANCE_ACCOUNT"))
                            .advance_account_name(rs.getString("S_ADVANCE_ACCOUNT_NAME"))
                            .advance_account_branch(rs.getString("S_ADVANCE_ACCOUNT_BRANCH"))
                            .currency(rs.getString("S_CURRENCY")).card_type(rs.getString("S_CARD_TYPE"))
                            .fee(rs.getString("S_FEE")).currency(rs.getString("S_CURRENCY"))
                            .create_date(rs.getDate("D_CREATE_DATE")).update_date(rs.getDate("D_UPDATE_DATE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static ActionDto deleteContractPayment(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.delete_contract_payment(?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(CONTRACT_PAYMENT_ID).toString(), 0));
            cs.execute();
            result = new ActionDto().nResult(1).sResult("");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update Contract Payment Gateways
    public static ActionDto approveContract(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.approve_contract(?,?,?,?)}");
            cs.setString(1, mIn.get(USER_ID).toString());
            cs.setInt(2, Convert.parseInt(mIn.get(CONTRACT_ID).toString(), 0));
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                throw new Exception("DB update Contract error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto unApproveContract(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.unapprove_contract(?,?,?,?)}");
            cs.setString(1, mIn.get(USER_ID).toString());
            cs.setInt(2, Convert.parseInt(mIn.get(CONTRACT_ID).toString(), 0));
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String error = cs.getString(4);
            if (nError == 0) {
                throw new Exception("DB update Contract error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update Contract Payment Gateways
    public static ActionDto InsertUpdateContractPaymentGateways(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_contract_payment(?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setInt(5, Convert.parseInt(mIn.get(CONTRACT_ID).toString(), 0));
            cs.setString(6, mIn.get(MERCHANT_NAME).toString());
            cs.setString(7, mIn.get(PAYGATE).toString());
            cs.setString(8, mIn.get(ADVANCE_ACCOUNT).toString());
            cs.setString(9, mIn.get(ADVANCE_ACCOUNT_NAME).toString());
            cs.setString(10, mIn.get(ADVANCE_ACCOUNT_BRANCH).toString());
            cs.setString(11, mIn.get(CARD_TYPE).toString());
            cs.setString(12, mIn.get(FEE).toString());
            cs.setString(13, mIn.get(CURRENCY).toString());
            logger.severe("DB update Contract payment input: " + mIn);
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                logger.severe("DB update Contract payment error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<ContractDto> customContractList(Map mIn) throws Exception {
        BaseList<ContractDto> contractDtoBaseList = new BaseList<>();
        List<ContractDto> contractDtos = new ArrayList<>();
        contractDtos = getContractsByPartnerId(mIn).getList();
        List<BranchDto> branchDtoList = getBranchList().getList();
        if (contractDtos.size() > 0) {
            if (branchDtoList.size() > 0) {
                for (int i = 0;i < contractDtos.size();i++) {
                    for (int j = 0;j < branchDtoList.size();j++) {
                        if (contractDtos.get(i).getBranch() != null) {
                            if (!contractDtos.get(i).getBranch().equals("")) {
                                if (contractDtos.get(i).getBranch().equals(String.valueOf(branchDtoList.get(j).getId()))) {
                                    contractDtos.get(i).setBranchName(branchDtoList.get(j).getName());
                                }
                            }
                        }
                    }
                }
            }
        }

        return contractDtoBaseList.setList(contractDtos);
    }

    // TIENNV 

    public static List<ContractOriginal> getContractListByPartnerId(int partnerId) throws Exception {
        Exception exception = null;
        List<ContractOriginal> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_CONTRACT_CLOB);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_CONTRACT_CLOB: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ContractOriginal(
                        rs.getInt("N_ID"),
                        rs.getInt("N_PARTNER_ID"),
                        rs.getInt("N_PARENT_ID"),
                        rs.getString("S_CONTRACT_NAME") == null ? "" : rs.getString("S_CONTRACT_NAME"), 
                        rs.getString("S_CONTRACT_CODE") == null ? "" : rs.getString("S_CONTRACT_CODE"),
                        rs.getString("D_SIGNATURE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_SIGNATURE_DATE")),
                        rs.getString("D_RANGE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_RANGE_DATE")),               
                        rs.getString("D_SIGNATURE_DATE") == null ? "" : rs.getString("D_SIGNATURE_DATE"),
                        rs.getString("D_RANGE_DATE") == null ? "" : rs.getString("D_RANGE_DATE"), 
                        rs.getString("D_CREATED_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CREATED_DATE")),
                        rs.getString("S_CREATED_BY") == null ? "" : rs.getString("S_CREATED_BY"), 
                        rs.getString("S_STATUS") == null ? "" : rs.getString("S_STATUS"),
                        rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"),
                        rs.getString("S_CONTRACT_NUMBER") == null ? "" : rs.getString("S_CONTRACT_NUMBER"),
                        rs.getString("JSON_CONTRACT_DETAIL") == null ? "" : rs.getString("JSON_CONTRACT_DETAIL"),
                        rs.getString("S_CONTRACT_TYPE") == null ? "" : rs.getString("S_CONTRACT_TYPE"),
                        rs.getInt("N_ORDER"),
                        rs.getString("S_TEMPLATE") == null ? "" : rs.getString("S_TEMPLATE"),
                        "",
                        rs.getString("S_REPRESENTATIVE") == null ? "" : rs.getString("S_REPRESENTATIVE"),
                        rs.getObject("N_ID_FORM")==null?0:rs.getInt("N_ID_FORM"),
                        rs.getObject("N_ID_TEMPLATE")==null?0:rs.getInt("N_ID_TEMPLATE")));
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<ContractOriginal> getSubContractListByParentId(int parentId) throws Exception {
        Exception exception = null;
        List<ContractOriginal> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_SUB_CONTRACT_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, parentId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_SUB_CONTRACT_LIST: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ContractOriginal(
                        rs.getInt("N_ID"),
                        rs.getInt("N_PARTNER_ID"),
                        rs.getInt("N_PARENT_ID"),
                        rs.getString("S_CONTRACT_NAME") == null ? "" : rs.getString("S_CONTRACT_NAME"), 
                        rs.getString("S_CONTRACT_CODE") == null ? "" : rs.getString("S_CONTRACT_CODE"),
                        rs.getString("D_SIGNATURE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_SIGNATURE_DATE")),
                        rs.getString("D_RANGE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_RANGE_DATE")),               
                        rs.getString("D_SIGNATURE_DATE") == null ? "" : rs.getString("D_SIGNATURE_DATE"),
                        rs.getString("D_RANGE_DATE") == null ? "" : rs.getString("D_RANGE_DATE"), 
                        rs.getString("D_CREATED_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CREATED_DATE")),
                        rs.getString("S_CREATED_BY") == null ? "" : rs.getString("S_CREATED_BY"), 
                        rs.getString("S_STATUS") == null ? "" : rs.getString("S_STATUS"),
                        rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"),
                        rs.getString("S_CONTRACT_NUMBER") == null ? "" : rs.getString("S_CONTRACT_NUMBER"),
                        rs.getString("JSON_CONTRACT_DETAIL") == null ? "" : rs.getString("JSON_CONTRACT_DETAIL"),
                        rs.getString("S_CONTRACT_TYPE") == null ? "" : rs.getString("S_CONTRACT_TYPE"),
                        rs.getInt("N_ORDER"),
                        rs.getString("S_TEMPLATE") == null ? "" : rs.getString("S_TEMPLATE"),
                        rs.getString("S_AUTHORIZATION_NUMBER") == null ? "" : rs.getString("S_AUTHORIZATION_NUMBER"),
                        rs.getString("S_REPRESENTATIVE") == null ? "" : rs.getString("S_REPRESENTATIVE"),
                        rs.getInt("N_ID_FORM"),
                        rs.getInt("N_ID_TEMPLATE")));
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static ContractOriginal getContractDetailById(int contractId) throws Exception {
        Exception exception = null;
        ContractOriginal contract = new ContractOriginal();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_CONTRACT_CLOB_DETAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, contractId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_CONTRACT_CLOB_DETAIL: " + error);
            } else {
                while (rs != null && rs.next()) {
                    contract = new ContractOriginal(
                        rs.getInt("N_ID"),
                        rs.getInt("N_PARTNER_ID"),
                        rs.getInt("N_PARENT_ID"),
                        rs.getString("S_CONTRACT_NAME") == null ? "" : rs.getString("S_CONTRACT_NAME"), 
                        rs.getString("S_CONTRACT_CODE") == null ? "" : rs.getString("S_CONTRACT_CODE"),
                        rs.getString("D_SIGNATURE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_SIGNATURE_DATE")),
                        rs.getString("D_RANGE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_RANGE_DATE")),               
                        rs.getString("D_SIGNATURE_DATE") == null ? "" : rs.getString("D_SIGNATURE_DATE"),
                        rs.getString("D_RANGE_DATE") == null ? "" : rs.getString("D_RANGE_DATE"), 
                        rs.getString("D_CREATED_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CREATED_DATE")),
                        rs.getString("S_CREATED_BY") == null ? "" : rs.getString("S_CREATED_BY"), 
                        rs.getString("S_STATUS") == null ? "" : rs.getString("S_STATUS"),
                        rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"),
                        rs.getString("S_CONTRACT_NUMBER") == null ? "" : rs.getString("S_CONTRACT_NUMBER"),
                        rs.getString("JSON_CONTRACT_DETAIL") == null ? "" : rs.getString("JSON_CONTRACT_DETAIL"),
                        rs.getString("S_CONTRACT_TYPE") == null ? "" : rs.getString("S_CONTRACT_TYPE"),
                        rs.getInt("N_ORDER"),
                        rs.getString("S_TEMPLATE") == null ? "" : rs.getString("S_TEMPLATE"),
                        "",
                        rs.getString("S_REPRESENTATIVE") == null ? "" : rs.getString("S_REPRESENTATIVE"),
                        rs.getInt("N_ID_FORM"),
                        rs.getInt("N_ID_TEMPLATE"));
                        contract.setIdVersion(rs.getInt("N_ID_VERSION"));
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return contract;
    }
    public static ContractOriginal getSubContractDetailById(int contractId) throws Exception {
        Exception exception = null;
        ContractOriginal contract = new ContractOriginal();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_SUB_CONTRACT_CLOB_DETAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, contractId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_SUB_CONTRACT_CLOB_DETAIL: " + error);
            } else {
                while (rs != null && rs.next()) {
                    contract = new ContractOriginal(
                        rs.getInt("N_ID"),
                        rs.getInt("N_PARTNER_ID"),
                        rs.getInt("N_PARENT_ID"),
                        rs.getString("S_CONTRACT_NAME") == null ? "" : rs.getString("S_CONTRACT_NAME"), 
                        rs.getString("S_CONTRACT_CODE") == null ? "" : rs.getString("S_CONTRACT_CODE"),
                        rs.getString("D_SIGNATURE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_SIGNATURE_DATE")),
                        rs.getString("D_RANGE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_RANGE_DATE")),               
                        rs.getString("D_SIGNATURE_DATE") == null ? "" : rs.getString("D_SIGNATURE_DATE"),
                        rs.getString("D_RANGE_DATE") == null ? "" : rs.getString("D_RANGE_DATE"), 
                        rs.getString("D_CREATED_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CREATED_DATE")),
                        rs.getString("S_CREATED_BY") == null ? "" : rs.getString("S_CREATED_BY"), 
                        rs.getString("S_STATUS") == null ? "" : rs.getString("S_STATUS"),
                        rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"),
                        rs.getString("S_CONTRACT_NUMBER") == null ? "" : rs.getString("S_CONTRACT_NUMBER"),
                        rs.getString("JSON_CONTRACT_DETAIL") == null ? "" : rs.getString("JSON_CONTRACT_DETAIL"),
                        rs.getString("S_CONTRACT_TYPE") == null ? "" : rs.getString("S_CONTRACT_TYPE"),
                        rs.getInt("N_ORDER"),
                        rs.getString("S_TEMPLATE") == null ? "" : rs.getString("S_TEMPLATE"),
                        "",
                        rs.getString("S_REPRESENTATIVE") == null ? "" : rs.getString("S_REPRESENTATIVE"),
                        rs.getInt("N_ID_FORM"),
                        rs.getInt("N_ID_TEMPLATE"),
                        rs.getInt("N_ID_VERSION"),
                        rs.getString("S_CONTRACT_NUMBER_PARENT") == null ? "" : rs.getString("S_CONTRACT_NUMBER_PARENT"),
                        rs.getString("D_SIGNATURE_DATE_PARENT") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_SIGNATURE_DATE_PARENT")),
                        rs.getString("S_CONTRACT_CODE_PARENT") == null ? "" : rs.getString("S_CONTRACT_CODE_PARENT"),
                        rs.getString("S_BUSINESS_NAME_PARENT") == null ? "" : rs.getString("S_BUSINESS_NAME_PARENT"),
                        rs.getString("JSON_CONTRACT_DETAIL_PARENT") == null ? "" : rs.getString("JSON_CONTRACT_DETAIL_PARENT"),
                        rs.getString("D_RANGE_DATE_PARENT") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_RANGE_DATE_PARENT")),
                        rs.getString("D_RANGE_DATE_PARENT") == null ? "" : rs.getString("D_RANGE_DATE_PARENT")
                        );
                        logger.info("rs.getString(S_CONTRACT_CODE_PARENT): " + rs.getString("S_CONTRACT_CODE_PARENT"));
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return contract;
    }

    public static BaseList<InfoFeeInstallment> getInfoFeeInstallmentList() throws Exception {
        Exception exception = null;
        BaseList<InfoFeeInstallment> result = new BaseList<>();
        List<InfoFeeInstallment> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_FEE_INSTALLMENT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_TRACKING_HN: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new InfoFeeInstallment(
                        rs.getInt("N_ID"),
                        rs.getInt("N_ORDER"),
                        rs.getString("S_NAME"),
                        rs.getString("S_SHORT_NAME"),
                        rs.getString("S_3_MONTH_FEE"), 
                        rs.getString("S_6_MONTH_FEE"), 
                        rs.getString("S_9_MONTH_FEE"), 
                        rs.getString("S_12_MONTH_FEE"),
                        rs.getString("S_15_MONTH_FEE"),
                        rs.getString("S_18_MONTH_FEE"),
                        rs.getString("S_24_MONTH_FEE"),
                        rs.getString("S_36_MONTH_FEE"),
                        0,
                        rs.getInt("N_FEE_TYPE")));
                }
            }
            
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static Integer updateFeeInstallment(List<UpdateFeeInstallmentReq> requests) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nResult = 500;
        try {
            con = getConnection112();
            con.setAutoCommit(false);
            cs = con.prepareCall(UPDATE_FEE_INSTALLMENT);
            for (UpdateFeeInstallmentReq req : requests) {
                cs.setInt(1, req.getId());
                cs.setString(2, req.getThreeMonths());
                cs.setString(3, req.getSixMonths());
                cs.setString(4, req.getNineMonths());
                cs.setString(5, req.getTwelveMonths());
                cs.setString(6, req.getFifteenMonths());
                cs.setString(7, req.getEighteenMonths());
                cs.setString(8, req.getTwentyFourMonths());
                cs.setString(9, req.getThirtySixMonths());
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            nResult = 200;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            if (con != null)
                con.rollback();
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

    public static Integer approveContractOriginal(Integer id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            cs = con.prepareCall(APPROVE_CONTRACT_ORIGINAL);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result != 200) {
                throw new Exception("DB APPROVE_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer removeApproveContractOriginal(Integer id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            cs = con.prepareCall(REMOVE_APPROVE_CONTRACT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result != 200) {
                throw new Exception("DB REMOVE_APPROVE_CONTRACT: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer deleteContract(Integer id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            cs = con.prepareCall(DELETE_CONTRACT_ORIGINAL);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result != 200) {
                throw new Exception("DB DELETE_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer saveTemplateSubContractOriginal(int contractId, String template) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(SAVE_SUB_CONTRACT_CLOB_DETAIL);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, contractId);
            cs.setString(4, template);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result == 500) {
                throw new Exception("DB PUSH_TEMPLATE_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }


    public static Integer pushContractOriginal(ContractOriginal contractOriginal) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(PUSH_CONTRACT_ORIGINAL);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, contractOriginal.getContractId());
            cs.setInt(5, contractOriginal.getPartnerId());
            cs.setInt(6, contractOriginal.getParentId());
            cs.setString(7, contractOriginal.getDataContractDetail());
            cs.setString(8, contractOriginal.getContractType());
            cs.setString(9, contractOriginal.getContractCode());
            cs.setString(10, contractOriginal.getContractName());
            cs.setString(11, contractOriginal.getBusinessName());
            cs.setString(12, contractOriginal.getSignatureDateString());
            cs.setString(13, contractOriginal.getRangeDateString());
            cs.setString(14, contractOriginal.getUserAction());
            cs.setString(15, contractOriginal.getStateContract());
            cs.setString(16, contractOriginal.getContractNumber());
            cs.setInt(17, contractOriginal.getOrder());
            cs.setString(18, contractOriginal.getRepresentative());

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result == 500) {
                throw new Exception("DB PUSH_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer pushContractOriginalTemplate(ContractOriginal contractOriginal) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(PUSH_CONTRACT_ORIGINAL_TEMPLATE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, contractOriginal.getContractId());
            cs.setInt(5, contractOriginal.getPartnerId());
            cs.setInt(6, contractOriginal.getParentId());
            cs.setString(7, contractOriginal.getDataContractDetail());
            cs.setString(8, contractOriginal.getContractType());
            cs.setString(9, contractOriginal.getContractCode());
            cs.setString(10, contractOriginal.getContractName());
            cs.setString(11, contractOriginal.getBusinessName());
            cs.setString(12, contractOriginal.getSignatureDateString());
            cs.setString(13, contractOriginal.getRangeDateString());
            cs.setString(14, contractOriginal.getUserAction());
            cs.setString(15, contractOriginal.getStateContract());
            cs.setString(16, contractOriginal.getContractNumber());
            cs.setInt(17, contractOriginal.getOrder());
            cs.setString(18, contractOriginal.getRepresentative());
            cs.setInt(19, contractOriginal.getIdTemplate());
            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result == 500) {
                throw new Exception("DB PUSH_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer pushContractAndTemplateOriginal(ContractOriginal contractOriginal, String content) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(PUSH_CONTRACT_ORIGINAL_V2);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, contractOriginal.getContractId());
            cs.setInt(5, contractOriginal.getPartnerId());
            cs.setInt(6, contractOriginal.getParentId());
            cs.setString(7, contractOriginal.getDataContractDetail());
            cs.setString(8, contractOriginal.getContractType());
            cs.setString(9, contractOriginal.getContractCode());
            cs.setString(10, contractOriginal.getContractName());
            cs.setString(11, contractOriginal.getBusinessName());
            cs.setString(12, contractOriginal.getSignatureDateString());
            cs.setString(13, contractOriginal.getRangeDateString());
            cs.setString(14, contractOriginal.getUserAction());
            cs.setString(15, contractOriginal.getStateContract());
            cs.setString(16, contractOriginal.getContractNumber());
            cs.setInt(17, contractOriginal.getOrder());
            cs.setString(18, content);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result == 500) {
                throw new Exception("DB PUSH_CONTRACT_ORIGINAL: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer checkContractNumber(int contractId, String contractNumber) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(CHECK_CONTRACT_NUMBER);
            cs.setInt(1, contractId);
            cs.setString(2, contractNumber);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(4);
            String sError = cs.getString(5);
            total =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("contract_number " + contractNumber +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    //get template by id contract
    public static Map<String, String> getTemplateByContractId(int id,boolean bilingual) throws Exception {
        Map<String, String> result = new HashMap<>();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String fileName = null;
        Blob blob = null;
        int idVersion = 0;
        try {
            conn = getConnection114();
            if(bilingual){
                cs = conn.prepareCall("select v.N_ID, v.S_VERSION S_FILE_NAME,v.B_TEMPLATE_BILINGUAL B_TEMPLATE_FILE from \n"+
                "onepartner.TBL_CONTRACT_TEMPLATE t inner join onepartner.TBL_CONTRACT_TMPL_VERSION v on t.N_ID = v.N_ID_TEMPLATE \n"+
                "where t.n_id=? and v.S_ACTIVE='activated' and v.S_APPROVE='approved'");
            }else{
                cs = conn.prepareCall("select v.N_ID, v.S_VERSION S_FILE_NAME,v.B_TEMPLATE_FILE from \n"+
                "onepartner.TBL_CONTRACT_TEMPLATE t inner join onepartner.TBL_CONTRACT_TMPL_VERSION v on t.N_ID = v.N_ID_TEMPLATE \n"+
                "where t.n_id=? and v.S_ACTIVE='activated' and v.S_APPROVE='approved'");
            }
            cs.setInt(1, id);
            rs = cs.executeQuery();
            while (rs != null && rs.next()) {
                fileName = rs.getString("S_FILE_NAME");
                blob = rs.getBlob("B_TEMPLATE_FILE");
                idVersion = rs.getInt("N_ID");
            }
            result.put("fileName",fileName);
            result.put("idVersion",String.valueOf(idVersion));
            logger.info("lob=" + blob);
            InputStream in = blob.getBinaryStream();
            // Define output file
            File file = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName+".docx");
            OutputStream outputStream = new FileOutputStream(new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName+".docx"));
            result.put("templatePath",file.getAbsolutePath());
            logger.info("filePath=" + file.getAbsolutePath());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int bytesRead;
            byte[] buffer = new byte[4096];
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.writeTo(outputStream);

            in.close();
            out.close();
            outputStream.close();
            int nError = 200;
            String sError = "OK";
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    /* update contract verion */
    public static Integer updateContractVersion(int contractId, int versionId,String emailExport) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer idContractVersion = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{call onepartner.PKG_CONTRACT_TEMPLATE.update_contract_version(?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, contractId);
            cs.setInt(5, versionId);
            cs.setString(6, emailExport);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            idContractVersion =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("update contract version error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return idContractVersion;
    }

    /**
     * Load list contract history by id contract
     * @param id contract
     * @return list contract history
     */
    public static Map<String, Object> loadListContractHistoryById(int id) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.PKG_CONTRACT#G_HISTORY(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "ONEPARTNER.PKG_CONTRACT#G_HISTORY error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("N_ID", Util.getColumnInteger(rs, "N_ID"));
                    m.put("N_ID_CONTRACT", Util.getColumnInteger(rs, "N_ID_CONTRACT"));
                    m.put("N_ID_FILE", Util.getColumnInteger(rs, "N_ID_FILE"));
                    m.put("D_CREATE", Util.getColumnString(rs, "D_CREATE"));
                    m.put("S_CREATE", Util.getColumnString(rs, "S_CREATE"));
                    m.put("N_ID_CONTRACT_VERSION", Util.getColumnInteger(rs, "N_ID_CONTRACT_VERSION"));
                    m.put("S_COMMENT", Util.getColumnString(rs, "S_COMMENT"));
                    m.put("S_VERSION", Util.getColumnString(rs, "S_VERSION"));
                    m.put("S_ACTION", Util.getColumnString(rs, "S_ACTION"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e
            );
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        return result;
    }

    /* download file scan from history */
    /**
     * download file scan from history
     * @param idFile id file history
     * @param fileName file name to save
     * @return result save file
     */
    public static Map<String, Object> downloadFileScan(int idFile,String fileName) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("select B_FILE_SCAN from onepartner.TBL_CONTRACT_FILE f " +
            "inner join onepartner.TBL_CONTRACT_HISTORY h on f.N_ID = h.N_ID_FILE " +
            "where h.N_ID = ?");
            cs.setInt(1, idFile);
            rs = cs.executeQuery();
            Blob blob = null;
            while (rs.next()) {
                blob = rs.getBlob("B_FILE_SCAN");
            }
            logger.info("lob=" + blob);
            InputStream in = blob.getBinaryStream();
            // Define output file
            File file = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName);
            OutputStream outputStream = new FileOutputStream(new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName));

            result.put("filePath",file.getAbsolutePath());
            logger.info("filePath=" + file.getAbsolutePath());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int bytesRead;
            byte[] buffer = new byte[4096];
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.writeTo(outputStream);

            in.close();
            out.close();
            outputStream.close();
            error="OK";
            nerror=200;
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error=e.getMessage();
            nerror=300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /**
     * insert log action cancel/approve/reject contract
     * @param idContract id contract
     * @param idFile id file
     * @param email email cancel
     * @param action action (cancel/approve/reject)
     * @return
     */
    public static Map<String,Object> insertLogActionContract(int idContract, Integer idFile, String email, String action) {
        Connection conn = null;
        CallableStatement cs = null;
        Integer id = null;
        Map<String,Object> result = new HashMap();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPARTNER.PKG_CONTRACT#I_HISTORY(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, idContract);
            cs.setInt(5, idFile);
            cs.setString(6, email);
            cs.setString(7, action);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            id =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("insert log cancel approve contract error: " + nError + " - " + sError);
            }
            result.put("id",id);
            result.put("nError",nError);
            result.put("sError",sError);
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            result.put("nError",500);
            result.put("sError",e.getMessage());
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return result;
    }

    /*
     * insert contract file blob
     * @param idPartner id partner
     * @param idContract id contract
     * @param idContractVersion id contract version
     * @param fileScan file scan
     * @return id file
     * 
     */
    public static Map<String, Object> insertContractFileBlob(String barcodeParam,int idPartner,int idContract, int idContractVersion, byte[] fileScan) {
        Connection conn = null;
        CallableStatement cs = null;
        Integer id = null;
        Map<String,Object> result = new HashMap();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPARTNER.PKG_CONTRACT#I_FILE_SCAN(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, idPartner);
            cs.setInt(5, idContract);
            cs.setInt(6, idContractVersion);
            cs.setBytes(7, fileScan);
            cs.setString(8, barcodeParam);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            id =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("insert contract file blob error: " + nError + " - " + sError);
            }
            result.put("id",id);
            result.put("nError",nError);
            result.put("sError",sError);
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return result;
    }

    /*
     * insert contract file blob
     * @param idPartner id partner
     * @param idContract id contract
     * @param idContractVersion id contract version
     * @param fileScan file scan
     * @return id file
     * 
     */
    public static JsonObject insertContractFileBlob2(String barcodeParam,int idPartner,int idContract, int idContractVersion, byte[] fileScan) {
        Connection conn = null;
        CallableStatement cs = null;
        Integer id = null;
        JsonObject result = new JsonObject();

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPARTNER.PKG_CONTRACT#I_FILE_SCAN2(?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, idPartner);
            cs.setInt(6, idContract);
            cs.setInt(7, idContractVersion);
            cs.setBytes(8, fileScan);
            cs.setString(9, barcodeParam);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            id =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("insert contract file blob error: " + nError + " - " + sError);
            }
            result.put("id",id);
            result.put("nError",nError);
            result.put("sError",sError);
            result.put("contractCode",cs.getString(4));
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return result;
    }

    /*
     * update contract file blob pdf compared
     * @param idContract id contract
     * @param idContractVersion id contract version
     * @param fileScan file scan
     * @return
     */
    public static Map<String, Object> updateContractFileBlobPdfCompared(int idContract, int idFile, String stateCompared, byte[] fileScan, byte[] fileContract) {
        Connection conn = null;
        CallableStatement cs = null;
        Integer id = null;
        Map<String,Object> result = new HashMap();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPARTNER.PKG_CONTRACT#U_FILE_COMPARED(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, idContract);
            cs.setInt(5, idFile);
            cs.setString(6, stateCompared);
            cs.setBytes(7, fileScan);
            cs.setBytes(8, fileContract);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            id =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("update contract file blob pdf compared error ONEPARTNER.PKG_CONTRACT#U_FILE_COMPARED: " + nError + " - " + sError);
            }
            result.put("id",id);
            result.put("nError",nError);
            result.put("sError",sError);
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return result;
    }


    /**
     * download the last file scan or contract compared
     * @param idContract id contract
     * @param type "scan" hoặc "contract"
     * @return result save file
     */
    public static Map<String, Object> downloadFileContractScanCompared(int idContract, String type) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("SELECT B_FILE_SCAN_COMPARED, B_FILE_CONTRACT_COMPARED FROM onepartner.TBL_CONTRACT_FILE WHERE N_ID_CONTRACT = ? AND D_COMPARED IS NOT NULL AND D_COMPARED = (SELECT MAX(D_COMPARED) FROM onepartner.TBL_CONTRACT_FILE WHERE N_ID_CONTRACT = ? AND D_COMPARED IS NOT NULL)");
            cs.setInt(1, idContract);
            cs.setInt(2, idContract);
            rs = cs.executeQuery();
            Blob blobScan = null;
            Blob blobContract = null;
            while (rs.next()) {
                blobScan = rs.getBlob("B_FILE_SCAN_COMPARED");
                blobContract = rs.getBlob("B_FILE_CONTRACT_COMPARED");
            }
            if ("scan".equalsIgnoreCase(type)) {
                if (blobScan != null) {
                    InputStream inScan = blobScan.getBinaryStream();
                    File fileScan = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + "scan_" + idContract + ".pdf");
                    OutputStream outputStreamScan = new FileOutputStream(fileScan);
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    int bytesRead;
                    byte[] buffer = new byte[4096];
                    while ((bytesRead = inScan.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    out.writeTo(outputStreamScan);
                    inScan.close();
                    out.close();
                    outputStreamScan.close();
                    result.put("filePath", fileScan.getAbsolutePath());
                    nerror = 200;
                    error = "OK";
                } else {
                    nerror = 404;
                    error = "File scan not found";
                }
            } else if ("contract".equalsIgnoreCase(type)) {
                if (blobContract != null) {
                    InputStream inContract = blobContract.getBinaryStream();
                    File fileContract = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + "contract_" + idContract + ".pdf");
                    OutputStream outputStreamContract = new FileOutputStream(fileContract);
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    int bytesRead;
                    byte[] buffer = new byte[4096];
                    while ((bytesRead = inContract.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    out.writeTo(outputStreamContract);
                    inContract.close();
                    out.close();
                    outputStreamContract.close();
                    result.put("filePath", fileContract.getAbsolutePath());
                    nerror = 200;
                    error = "OK";
                } else {
                    nerror = 404;
                    error = "File contract not found";
                }
            } else {
                nerror = 400;
                error = "Invalid type";
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error = e.getMessage();
            nerror = 300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /**
     * Get contract file scan information
     * @param idContract id contract
     * @return result save file
     */
    public static Map<String, Object> getContractFileScanInformation(int idContract) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("SELECT f.N_ID,f.D_SCAN,f.D_COMPARED,f.N_ID_PARTNER,f.N_ID_CONTRACT,f.N_ID_CONTRACT_VERSION,c.S_CONTRACT_NUMBER,c.S_STATUS FROM "
            + "onepartner.TBL_CONTRACT_FILE f "
            + "left join onepartner.TBL_CONTRACT_CLOB c on f.N_ID_CONTRACT=c.N_ID "
            + "WHERE f.N_ID_CONTRACT = ? " 
            + "AND f.D_COMPARED IS NOT NULL AND f.D_COMPARED = (SELECT MAX(D_COMPARED) FROM onepartner.TBL_CONTRACT_FILE WHERE N_ID_CONTRACT = ? AND D_COMPARED IS NOT NULL)");
            cs.setInt(1, idContract);
            cs.setInt(2, idContract);
            rs = cs.executeQuery();
            while (rs.next()) {
                Map<String, Object> m = new HashMap<>();
                m.put("N_ID", Util.getColumnInteger(rs, "N_ID"));
                m.put("D_SCAN", Util.getColumnString(rs, "D_SCAN"));
                m.put("D_COMPARED", Util.getColumnString(rs, "D_COMPARED"));
                m.put("N_ID_PARTNER", Util.getColumnInteger(rs, "N_ID_PARTNER"));
                m.put("N_ID_CONTRACT", Util.getColumnInteger(rs, "N_ID_CONTRACT"));
                m.put("N_ID_CONTRACT_VERSION", Util.getColumnInteger(rs, "N_ID_CONTRACT_VERSION"));
                m.put("S_CONTRACT_NUMBER", Util.getColumnString(rs, "S_CONTRACT_NUMBER"));
                m.put("S_STATUS", Util.getColumnString(rs, "S_STATUS"));
                result.put("data", m);
                logger.info("contract file scan information: " + m);
            }
            nerror = 200;
            error = "OK";
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error = e.getMessage();
            nerror = 300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /**
     * Get contract sync to document tracking
     * @param contractIds list contractid
     * @return result list contract
     */
    public static List<ContractOriginal> getContractById(String contractIds) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<ContractOriginal> list = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_DOC_TRACKING#GET_CONTRACT(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, contractIds);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_CONTRACT_BY_ID: " + error);
            } else {
                while (rs != null && rs.next()) {
                    ContractOriginal contractObj = new ContractOriginal();
                    contractObj.setContractId(rs.getInt("N_ID"));
                    contractObj.setParentId(rs.getInt("N_PARENT_ID"));
                    contractObj.setContractCode(rs.getString("S_CONTRACT_CODE") == null ? "" : rs.getString("S_CONTRACT_CODE"));
                    contractObj.setContractName(rs.getString("S_CONTRACT_NAME") == null ? "" : rs.getString("S_CONTRACT_NAME"));
                    contractObj.setContractNumber(rs.getString("S_CONTRACT_NUMBER") == null ? "" : rs.getString("S_CONTRACT_NUMBER"));
                    contractObj.setBusinessName(rs.getString("S_BUSINESS_NAME") == null ? "" : rs.getString("S_BUSINESS_NAME"));
                    contractObj.setSignatureDateString(rs.getString("D_SIGNATURE_DATE") == null ? "" : rs.getString("D_SIGNATURE_DATE"));
                    contractObj.setBusinessNumber(rs.getString("BUSINESS_NUMBER") == null ? "" : rs.getString("BUSINESS_NUMBER"));
                    contractObj.setContractNumberParent(rs.getString("S_PARENT_CONTRACT_NUMBER") == null ? "" : rs.getString("S_PARENT_CONTRACT_NUMBER"));
                    contractObj.setContractCodeParent(rs.getString("S_PARENT_CONTRACT_CODE") == null ? "" : rs.getString("S_PARENT_CONTRACT_CODE"));
                    contractObj.setContractType(rs.getString("BRANCH") == null ? "" : rs.getString("BRANCH"));

                    list.add(contractObj);
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    /**
     * Save subTrackingDocument
     * @param subDocuments list subDocumentTracking
     * @param docTrackingId docTrackingId
     * @return result save sub document tracking
     */
    public static Integer postSubDocumentTracking(List<SubDocumentDto> subDocuments, int docTrackingId, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        try {
            cs = con.prepareCall("{call ONEPORTAL.PKG_DOC_TRACKING#INSERT_SUB(?,?,?,?,?,?,?) }");
            for (SubDocumentDto doc : subDocuments) {
                cs.setString(1, doc.getDocumentNumber());
                cs.setString(2, doc.getBranch() != null ? doc.getBranch() : "");
                cs.setString(3, doc.getDocumentName());
                cs.setInt(4, docTrackingId);
                cs.setString(5, doc.getSignDay());
                cs.setString(6, doc.getCreateUser());
                cs.setInt(7, doc.getContractId());
                cs.addBatch();
            }
            cs.executeBatch();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return 200;
    }

    /**
     * Save documentTracking
     * @param documentTrackingDto documentTracking
     * @param subDocuments list subDocumentTracking
     * @param idContract id contract
     * @return result save document tracking
     */
    public static Integer postDocumentTracking(DocumentTrackingDto documentTrackingDto, List<SubDocumentDto> subDocuments, int contractId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall("{call ONEPORTAL.PKG_DOC_TRACKING#INSERT(?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, documentTrackingDto.getMerchantName());
            cs.setString(5, documentTrackingDto.getContractType());
            cs.setString(6, documentTrackingDto.getLocation());
            cs.setString(7, documentTrackingDto.getTaxCode());
            cs.setInt(8, contractId);
            cs.setString(9, documentTrackingDto.getBranchName());

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);

            if (n_result == 201) {
                return n_result;
            }
            if (n_result != 200) {
                throw new Exception("DB POST_DOCUMENT_TRACKING: " + error);
            }

            int docTrackingId = cs.getInt(3);
            postSubDocumentTracking(subDocuments, docTrackingId, con);
            updateContractStatus(contractId, con);

            con.commit();
        } catch (Exception e) {
            if (con != null)
                con.rollback();
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    /**
     * Update status sync contract
     * @param idContract id contract
     * @return result update contract
     */
    public static Integer updateContractStatus(int contractId, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        try {
            cs = con.prepareCall("{call onepartner.PKG_DOC_TRACKING#UPDATE_SYCN(?) }");
            cs.setInt(1, contractId);
            cs.execute();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return 200;
    }

    public static SubDocumentDto buidSubDocumentTrackingFromContract (ContractOriginal contract){
        SubDocumentDto obj = new SubDocumentDto();
        try {
            obj.setDocumentNumber(contract.getContractNumber() != null ? contract.getContractNumber() : "");
            obj.setDocumentName(contract.getContractName() != null ? contract.getContractName() : "");
            obj.setSignDay(StringUtils.isNotBlank(contract.getSignatureDateString()) ? contract.getSignatureDateString().substring(0, 10) : "");
            obj.setContractId(contract.getContractId());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            e.printStackTrace();
        }
        return obj;
    }

    public static DocumentTrackingDto buidDocumentTrackingFromContract (ContractOriginal contract, String location){
        DocumentTrackingDto obj = new DocumentTrackingDto();
        try {
            obj.setMerchantName(contract.getBusinessName() != null ? contract.getBusinessName() : "");
            obj.setTaxCode(contract.getBusinessNumber() != null ? contract.getBusinessNumber() : "");
            // obj.setLocation(contract.getContractNumberParent() != null ? contract.getContractNumberParent().contains("HCM") ? "HCM" : "HN" :"HN");
            obj.setLocation(location != null && !"".equalsIgnoreCase(location) ? location : "HN");
            obj.setContractType(contract.getContractCodeParent() != null ? containsContractCode(contract.getContractCodeParent()) ? "3B" : "2B" :"2B");
            if (containsContractCode(contract.getContractCodeParent()) && contract.getContractType() != null) {
                obj.setBranchName(getBranchNameById(contract.getContractType()));
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            e.printStackTrace();
        }
        return obj;
    }

    public static boolean containsContractCode(String source) {
        String s = "HD03-01,HD05,HD06,HD01,HD02";
        if (source == null || source.isEmpty()) return false;
        return Arrays.stream(s.split(","))
                .anyMatch(code -> Arrays.stream(source.split(","))
                        .map(String::trim)
                        .anyMatch(c -> c.equals(code)));
    }

    /**
     * Get branch name from n_id
     * @param branchId
     * @return result branch name
     */
    public static String getBranchNameById(String branchId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call ONEPARTNER.PKG_DOC_TRACKING#BRANCH_NAME(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, branchId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB getBranchNameById: " + error);
            } else {
                while (rs != null && rs.next()) {
                    return rs.getString("S_NAME");
                }
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return null;
    }
}
