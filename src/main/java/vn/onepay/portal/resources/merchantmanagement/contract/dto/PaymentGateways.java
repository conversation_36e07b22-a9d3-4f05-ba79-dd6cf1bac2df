package vn.onepay.portal.resources.merchantmanagement.contract.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class PaymentGateways {
  private int id;
  private int contract_id;
  private String merchant_name;
  private String paygate;
  private String advance_account;
  private String advance_account_name;
  private String advance_account_branch;
  private String card_type;
  private String currency;
  private String fee;
  private Date create_date;
  private Date update_date;

  public PaymentGateways() {
  }

  public PaymentGateways(int id, int contract_id, String merchant_name, String paygate, String advance_account,
      String advance_account_name, String advance_account_branch, String card_type, String currency, String fee,
      Date create_date, Date update_date) {
    this.id = id;
    this.contract_id = contract_id;
    this.merchant_name = merchant_name;
    this.paygate = paygate;
    this.advance_account = advance_account;
    this.advance_account_name = advance_account_name;
    this.advance_account_branch = advance_account_branch;
    this.card_type = card_type;
    this.currency = currency;
    this.fee = fee;
    this.create_date = create_date;
    this.update_date = update_date;
  }

  public int getId() {
    return this.id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getContract_id() {
    return this.contract_id;
  }

  public void setContract_id(int contract_id) {
    this.contract_id = contract_id;
  }

  public String getMerchant_name() {
    return this.merchant_name;
  }

  public void setMerchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
  }

  public String getPaygate() {
    return this.paygate;
  }

  public void setPaygate(String paygate) {
    this.paygate = paygate;
  }

  public String getAdvance_account() {
    return this.advance_account;
  }

  public void setAdvance_account(String advance_account) {
    this.advance_account = advance_account;
  }

  public String getAdvance_account_name() {
    return this.advance_account_name;
  }

  public void setAdvance_account_name(String advance_account_name) {
    this.advance_account_name = advance_account_name;
  }

  public String getAdvance_account_branch() {
    return this.advance_account_branch;
  }

  public void setAdvance_account_branch(String advance_account_branch) {
    this.advance_account_branch = advance_account_branch;
  }

  public String getCard_type() {
    return this.card_type;
  }

  public void setCard_type(String card_type) {
    this.card_type = card_type;
  }

  public String getCurrency() {
    return this.currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public String getFee() {
    return this.fee;
  }

  public void setFee(String fee) {
    this.fee = fee;
  }

  public Date getCreate_date() {
    return this.create_date;
  }

  public void setCreate_date(Date create_date) {
    this.create_date = create_date;
  }

  public Date getUpdate_date() {
    return this.update_date;
  }

  public void setUpdate_date(Date update_date) {
    this.update_date = update_date;
  }

  public PaymentGateways id(int id) {
    this.id = id;
    return this;
  }

  public PaymentGateways contract_id(int contract_id) {
    this.contract_id = contract_id;
    return this;
  }

  public PaymentGateways merchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
    return this;
  }

  public PaymentGateways paygate(String paygate) {
    this.paygate = paygate;
    return this;
  }

  public PaymentGateways advance_account(String advance_account) {
    this.advance_account = advance_account;
    return this;
  }

  public PaymentGateways advance_account_name(String advance_account_name) {
    this.advance_account_name = advance_account_name;
    return this;
  }

  public PaymentGateways advance_account_branch(String advance_account_branch) {
    this.advance_account_branch = advance_account_branch;
    return this;
  }

  public PaymentGateways card_type(String card_type) {
    this.card_type = card_type;
    return this;
  }

  public PaymentGateways currency(String currency) {
    this.currency = currency;
    return this;
  }

  public PaymentGateways fee(String fee) {
    this.fee = fee;
    return this;
  }

  public PaymentGateways create_date(Date create_date) {
    this.create_date = create_date;
    return this;
  }

  public PaymentGateways update_date(Date update_date) {
    this.update_date = update_date;
    return this;
  }

  @Override
  public String toString() {
    return "{" + " id='" + getId() + "'" + ", contract_id='" + getContract_id() + "'" + ", merchant_name='"
        + getMerchant_name() + "'" + ", paygate='" + getPaygate() + "'" + ", advance_account='" + getAdvance_account()
        + "'" + ", advance_account_name='" + getAdvance_account_name() + "'" + ", advance_account_branch='"
        + getAdvance_account_branch() + "'" + ", card_type='" + getCard_type() + "'" + ", currency='" + getCurrency()
        + "'" + ", fee='" + getFee() + "'" + ", create_date='" + getCreate_date() + "'" + ", update_date='"
        + getUpdate_date() + "'" + "}";
  }
}
