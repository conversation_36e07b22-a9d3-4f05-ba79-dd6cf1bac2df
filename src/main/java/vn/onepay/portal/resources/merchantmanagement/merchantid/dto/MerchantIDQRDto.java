package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class MerchantIDQRDto {
    private String merchant_name;
    private String merchant_id;
    private String active;
    private String accept_instrument;
    private String method;
    private String terminal_id;
    public MerchantIDQRDto() {
    }

    public MerchantIDQRDto(String merchant_name, String merchant_id, String active, String accept_instrument) {
        this.merchant_name = merchant_name;
        this.merchant_id = merchant_id;
        this.active = active;
        this.accept_instrument = accept_instrument;
    }

    public String getMerchant_name() {
        return this.merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getMerchant_id() {
        return this.merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getActive() {
        return this.active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getAccept_instrument() {
        return accept_instrument;
    }

    public void setAccept_instrument(String accept_instrument) {
        this.accept_instrument = accept_instrument;
    }

    public MerchantIDQRDto merchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
        return this;
    }

    public MerchantIDQRDto merchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
        return this;
    }

    public MerchantIDQRDto active(String active) {
        this.active = active;
        return this;
    }

    public MerchantIDQRDto accept_instrument(String accept_instrument) {
        this.accept_instrument = accept_instrument;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getTerminal_id() {
        return terminal_id;
    }

    public void setTerminal_id(String terminal_id) {
        this.terminal_id = terminal_id;
    }

    @Override
    public String toString() {
        return "{" + " merchant_name='" + getMerchant_name() + "'" + ", merchant_id='" + getMerchant_id() + "'"
                + ", active='" + getActive() + "'"
                + ", accept_instrument='" + getAccept_instrument() + "'" + "}";
    }
}
