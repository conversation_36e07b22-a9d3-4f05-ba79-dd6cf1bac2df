package vn.onepay.portal.resources.merchantmanagement.acceptance;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.acceptance.dto.MerchantIdDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.acceptance.dto.AcceptanceDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
//import java.util.logging.Logger;

public class AcceptanceDao extends Db implements IConstants {

    // get list Acceptance
    public static BaseList<AcceptanceDto> getListAcceptancebyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<AcceptanceDto> result = new BaseList<>();
        List<AcceptanceDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_acceptance_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                throw new Exception("DB load Acceptance list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new AcceptanceDto().id(rs.getInt("N_ID")).type(rs.getString("S_TYPE"))
                            .acceptance_date(Convert.toDate(rs.getString("D_ACCEPTANCE"), "yyy-MM-dd hh:mm:ss"))
                            .ten_dkkd(rs.getString("S_TEN_DKKD")).merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .website(rs.getString("S_WEBSITE")).service_type(rs.getString("S_SERVICE_TYPE"))
                            .guarantee_account(rs.getString("S_GUARANTEE_ACCOUNT"))
                            .merchant_id_qt(rs.getString("S_MERCHANT_ID_QT")).mid(rs.getString("S_MID"))
                            .currency(rs.getString("S_CURRENCY")).card_type_qt(rs.getString("S_CARD_TYPE_QT"))
                            .paygate(rs.getString("S_PAY_GATE")).merchant_id_nd(rs.getString("S_MERCHANT_ID_ND"))
                            .card_type_nd(rs.getString("S_CARD_TYPE_ND")).note(rs.getString("S_NOTE"))
                            .partner_id(rs.getInt("N_PARTNER_ID")).mcc(rs.getString("S_MCC"))
                            .merchant_id_nd_vcb(rs.getString("S_MERCHANT_ID_ND_VCB"))
                            .mid_nd_vcb(rs.getString("S_MID_ND_VCB")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list Merchant Id by Acceptance
    public static BaseList<MerchantIdDto> getListMerchantIdByAcceptance(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIdDto> result = new BaseList<>();
        List<MerchantIdDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_mid_by_acceptance(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ACCEPTANCE_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                throw new Exception("DB load Acceptance list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIdDto().id(rs.getInt("N_ID")).acceptance_id(rs.getInt("N_ACCEPTANCE_ID"))
                            .merchant_id_qt(rs.getString("S_MERCHANT_ID_QT")).mid(rs.getString("S_MID"))
                            .currency(rs.getString("S_CURRENCY")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list Acceptance
    public static AcceptanceDto getListAcceptancebyId(Integer id) throws Exception {
        Exception exception = null;
        AcceptanceDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_acceptance_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                throw new Exception("DB load Acceptance by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = new AcceptanceDto().id(rs.getInt("N_ID")).type(rs.getString("S_TYPE"))
                            .acceptance_date(Convert.toDate(rs.getString("D_ACCEPTANCE"), "yyy-MM-dd hh:mm:ss"))
                            .ten_dkkd(rs.getString("S_TEN_DKKD")).merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .website(rs.getString("S_WEBSITE")).service_type(rs.getString("S_SERVICE_TYPE"))
                            .guarantee_account(rs.getString("S_GUARANTEE_ACCOUNT"))
                            .merchant_id_qt(rs.getString("S_MERCHANT_ID_QT")).mid(rs.getString("S_MID"))
                            .currency(rs.getString("S_CURRENCY")).card_type_qt(rs.getString("S_CARD_TYPE_QT"))
                            .paygate(rs.getString("S_PAY_GATE")).merchant_id_nd(rs.getString("S_MERCHANT_ID_ND"))
                            .card_type_nd(rs.getString("S_CARD_TYPE_ND")).note(rs.getString("S_NOTE"))
                            .partner_id(rs.getInt("N_PARTNER_ID")).mcc(rs.getString("S_MCC"))
                            .merchant_id_nd_vcb(rs.getString("S_MERCHANT_ID_ND_VCB"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // - Remove Acceptance by id:
    public static ActionDto deleteAcceptancebyID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.delete_acceptance_by_id(?,?,?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            if (nError == 0) {
                logger.severe("DB insert onsite error: " + error);
            }
            result = new ActionDto().nResult(1).sResult("");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto deleteMerchantID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.delete_merchant_id(?,?,?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            if (nError == 0) {
                logger.severe("DB insert onsite error: " + error);
            }
            result = new ActionDto().nResult(1).sResult("");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update Acceptance
    public static ActionDto updateAcceptance(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_acceptance(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setString(5, mIn.get(TYPE).toString());
            cs.setTimestamp(6, Convert.toTimestamp(Convert.toDate(mIn.get(DATE).toString(), "dd/MM/yyyy")));
            cs.setString(7, mIn.get(TEN_DKKD).toString());
            cs.setString(8, mIn.get(MERCHANT_NAME).toString());
            cs.setString(9, mIn.get(WEBSITE).toString());
            cs.setString(10, mIn.get(SERVICE_TYPE).toString());
            cs.setString(11, mIn.get(GUARANTEE_ACCOUNT).toString());
            cs.setString(12, mIn.get(CARD_TYPE_QT).toString());
            cs.setString(13, mIn.get(PAYGATE).toString());
            cs.setString(14, mIn.get(MERCHANT_ID_ND).toString());
            cs.setString(15, mIn.get(CARD_TYPE_ND).toString());
            cs.setString(16, mIn.get(NOTE).toString());
            cs.setInt(17, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(18, mIn.get(MCC).toString());
            cs.setString(19, mIn.get(MERCHANT_ID_ND_VCB).toString());
            cs.setString(20, mIn.get(MID_ND_VCB).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                throw new Exception("DB update Acceptance error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto updateAcceptanceMerchantId(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_acceptance_mid(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setInt(5, Convert.parseInt(mIn.get(ACCEPTANCE_ID).toString(), 0));
            cs.setString(6, mIn.get(MERCHANT_ID_QT).toString());
            cs.setString(7, mIn.get(IConstants.MID).toString());
            cs.setString(8, mIn.get(CURRENCY).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                throw new Exception("DB update Acceptance error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
