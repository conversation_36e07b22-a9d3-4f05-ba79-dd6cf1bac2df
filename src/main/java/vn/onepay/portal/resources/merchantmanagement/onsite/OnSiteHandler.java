package vn.onepay.portal.resources.merchantmanagement.onsite;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoDao;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoHandler;
import vn.onepay.portal.resources.merchantmanagement.onsite.dto.OnSiteDto;
import vn.onepay.portal.resources.mm.MmSubscribeDao;
import vn.onepay.portal.resources.sale.SaleSubscribeDao;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import static vn.onepay.portal.Util.sendResponse;

public class OnSiteHandler implements IConstants {
    private static Logger logger = Logger.getLogger(OnSiteHandler.class.getName());

    // - Get list onsite: input: partner_id
    public static void getListOnsitebyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0 : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);
                
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(GROUP, request.getParam(GROUP) == null ? "" : request.getParam(GROUP));
                mIn.put(USER_ID, request.getParam(USER_ID) == null ? "" : request.getParam(USER_ID));
                sendResponse(ctx, 200, OnsiteDao.getListOnsitebyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // - Get onsite by id: input onsite id
    public static void getOnsitebyOnsiteId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer onsiteId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ONSITE_ID, onsiteId);
                sendResponse(ctx, 200, OnsiteDao.getListOnsitebyOnsiteId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // Remove Onsite by id
    public static void deleteOnsitebyOnsiteID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String onsiteId = request.getParam(ID) == null ? BLANK : request.getParam(ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ONSITE_ID, onsiteId);
                sendResponse(ctx, 200, OnsiteDao.deleteOnsitebyOnsiteID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }


    // insert and update Onsite
    public static void updateOnsitebyOnsiteID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();

                Integer id = ctx.get(X_USER_ID);
                if (id == null) {
                    logger.warning(ctx.get(REQUEST_UUID) + ": " + "user id not found");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }

                MerchantInfoHandler.checkPermissionPartner(Integer.parseInt(body.getString(PARTNER_ID)), id);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                mIn.put(DATE, body.getString(DATE) == null ? BLANK : body.getString(DATE));
                mIn.put(STAFF, body.getString(STAFF) == null ? BLANK : body.getString(STAFF));
                mIn.put(CONTENT, body.getString(CONTENT) == null ? BLANK : body.getString(CONTENT));
                mIn.put(CHANNEL, body.getString(CHANNEL) == null ? 0 : body.getString(CHANNEL));
                mIn.put(PARTNER_ID, body.getString(PARTNER_ID) == null ? BLANK : body.getString(PARTNER_ID));
                mIn.put(GROUP, body.getString(GROUP) == null ? BLANK : body.getString(GROUP));
                mIn.put(TARGET, body.getString(TARGET) == null ? BLANK : body.getString(TARGET));
                ActionDto actionDb = OnsiteDao.updateOnsitebyOnsiteID(mIn);
                if (null != actionDb) {
                    // send email
                    Map<String, Object> mInOnsite = new HashMap<>();
                    mInOnsite.put(ONSITE_ID, actionDb.getId());
                    BaseList<OnSiteDto> onsiteBase = OnsiteDao.getListOnsitebyOnsiteId(mInOnsite);
                    List<OnSiteDto> onsites = onsiteBase.getList();
                    if (!onsites.isEmpty()) {
                        String partnerId = body.getString(PARTNER_ID);
                        Integer userId = body.getInteger(USER_ID); // user login id
                        String userEmail = body.getString("user_email"); // user login email

                        Map<String, Object> mInPartner = new HashMap<>();
                        mInPartner.put(PARTNER_ID, partnerId);
                        mInPartner.put(USER_ID, userId);
                        String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                        mInPartner.put(PROVINCE_ID, provinceIds);
                        String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                                .stream().map(x -> x.getId().toString())
                                .collect(Collectors.joining(","));
                        mInPartner.put("sale_id", saleSubs);
                        String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                                .stream().map(x -> x.getId().toString())
                                .collect(Collectors.joining(","));
                        mInPartner.put("mm_id", mmSubs);
                        PartnerDto partner = MerchantInfoDao.getPartnerByID(mInPartner);

                        if (null != partner) {
                            StringBuilder cc = new StringBuilder("");
                            if (partner.getProvinceId() == 24) {
                                // ha noi
                                cc.append(Config.getString("email.onsite_hn_cc", ""));
                            } else if (partner.getProvinceId() == 31) {
                                // hcm
                                cc.append(Config.getString("email.onsite_hcm_cc", ""));
                            } else {
                                cc.append(Config.getString("email.onsite_other_cc", ""));
                            }
                            // cc.append(",").append(userEmail);

                            StringBuilder tittle = new StringBuilder("ONSITE  (").append(onsites.get(0).getChannel()).append(") / ");
                            tittle.append(partner.getShortName()).append(" / ").append(onsites.get(0).getStaff());

                            StringBuilder content = new StringBuilder("Dear All,<br><br>");
                            content.append("Nhân sự: ").append(onsites.get(0).getStaff()).append("<br>");
                            content.append("Ngày: ").append(Util.formatDate(onsites.get(0).getCreateDate(), "dd/MM/yyyy")).append("<br>");
                            content.append("Kênh: ").append(onsites.get(0).getChannel()).append("<br>");
                            content.append("Người gặp: ").append(onsites.get(0).getTarget()).append("<br>");
                            content.append("Nội dung: ").append(onsites.get(0).getContent()).append("<br>");
                            content.append("Thanks & Regards,");

                            List<String> base64 = extractBase64Images2(onsites.get(0).getContent());
                            String bodyEmail = getContentEmail(content.toString()).replaceAll("<table>", "<table style='font-family: arial, sans-serif; border-collapse: collapse;width: 100%;'>").replaceAll("<td>", "<td style='border: 1px solid #dddddd;text-align: left;padding: 8px;'>").replaceAll("<th>", "<th style='border: 1px solid #dddddd;text-align: left;padding: 8px;'>");
                            logger.info(ctx.get(REQUEST_UUID) + ": " + "send email to " + Config.getString("email.onsite_acceptance", ""));
                            logger.info(ctx.get(REQUEST_UUID) + ": " + "send email cc " + cc.toString());
                            logger.info(ctx.get(REQUEST_UUID) + ": " + "mail title: " + tittle.toString());
                            MailUtil.sendMailWithAttachBase64(Config.getString("email.onsite_acceptance", ""), cc.toString(), tittle.toString(),
                                    bodyEmail, "html", base64);
                        }

                    }
                }

                sendResponse(ctx, 200, actionDb);
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "UPDATE Acceptance ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    // onsite email
    public static void sendEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                Integer id = ctx.get(X_USER_ID);
                if (id == null) {
                    logger.warning(ctx.get(REQUEST_UUID) + ": " + "user id not found");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ONSITE_ID, body.getString("id") == null ? 0 : Integer.parseInt(body.getString("id")));
                BaseList<OnSiteDto> onsiteBase = OnsiteDao.getListOnsitebyOnsiteId(mIn);
                List<OnSiteDto> onsites = onsiteBase.getList();
                if (!onsites.isEmpty()) {
                    String partnerId = body.getString(PARTNER_ID);
                    Integer userId = body.getInteger(USER_ID); // user login id
                    String userEmail = body.getString("user_email"); // user login email

                    Map<String, Object> mInPartner = new HashMap<>();
                    mInPartner.put(PARTNER_ID, partnerId);
                    mInPartner.put(USER_ID, userId);
                    String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                    mInPartner.put(PROVINCE_ID, provinceIds);
                    String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                            .stream().map(x -> x.getId().toString())
                            .collect(Collectors.joining(","));
                    mInPartner.put("sale_id", saleSubs);
                    String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                            .stream().map(x -> x.getId().toString())
                            .collect(Collectors.joining(","));
                    mInPartner.put("mm_id", mmSubs);
                    PartnerDto partner = MerchantInfoDao.getPartnerByID(mInPartner);

                    if (null != partner) {
                        StringBuilder cc = new StringBuilder("");
                        if (partner.getProvinceId() == 24) {
                            // ha noi
                            cc.append(Config.getString("email.onsite_hn_cc", ""));
                        } else if (partner.getProvinceId() == 31) {
                            // hcm
                            cc.append(Config.getString("email.onsite_hcm_cc", ""));
                        } else {
                            cc.append(Config.getString("email.onsite_other_cc", ""));
                        }
                        cc.append(",").append(userEmail);

                        StringBuilder tittle = new StringBuilder("ONSITE  (").append(onsites.get(0).getChannel()).append(") / ");
                        tittle.append(partner.getShortName()).append(" / ").append(onsites.get(0).getStaff());

                        StringBuilder content = new StringBuilder("Dear All,<br><br>");
                        content.append("Nhân sự: ").append(onsites.get(0).getStaff()).append("<br>");
                        content.append("Ngày: ").append(Util.formatDate(onsites.get(0).getCreateDate(), "dd/MM/yyyy")).append("<br>");
                        content.append("Kênh: ").append(onsites.get(0).getChannel()).append("<br>");
                        content.append("Người gặp: ").append(onsites.get(0).getTarget()).append("<br>");
                        content.append("Nội dung: ").append(onsites.get(0).getContent()).append("<br>");
                        content.append("Thanks & Regards,");

                        List<String> base64 = extractBase64Images2(onsites.get(0).getContent());
                        String bodyEmail = getContentEmail(content.toString()).replaceAll("<table>", "<table style='font-family: arial, sans-serif; border-collapse: collapse;width: 100%;'>").replaceAll("<td>", "<td style='border: 1px solid #dddddd;text-align: left;padding: 8px;'>").replaceAll("<th>", "<th style='border: 1px solid #dddddd;text-align: left;padding: 8px;'>");
                        logger.info(ctx.get(REQUEST_UUID) + ": " + "send email to " + Config.getString("email.onsite_acceptance", ""));
                        logger.info(ctx.get(REQUEST_UUID) + ": " + "send email cc " + cc.toString());
                        logger.info(ctx.get(REQUEST_UUID) + ": " + "mail title: " + tittle.toString());
                        logger.info(ctx.get(REQUEST_UUID) + ": " + "mail content: " + content.toString());
                        MailUtil.sendMailWithAttachBase64(Config.getString("email.onsite_acceptance", ""), cc.toString(), tittle.toString(),
                                bodyEmail, "html", base64);
                    }

                }

                sendResponse(ctx, 200, new JsonObject());
            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "UPDATE ONSITE ERROR: ", e);
                ctx.fail(e);
            }

        }, false, null);
    }

    public static String getContentEmail(String content) {
        while (content.indexOf("<figure class=\"image\">") > 1) {
            int checkImg = content.indexOf("<figure class=\"image\">");
            if (checkImg > -1) {
                String temp = "";
                temp = content.substring(checkImg);
                temp = temp.substring(temp.indexOf("</figure>") + 9);
                content = content.substring(0, checkImg) + temp;
            }
        }
        return content;
    }

    public static List<String> extractBase64Images(String htmlString) {
        List<String> base64Images = new ArrayList<>();

        String regex = "<img[^>]+src=\"data:image/[^>]+>";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlString);

        while (matcher.find()) {
            String imgTag = matcher.group();
            String base64Image = imgTag.replaceFirst("^<img[^>]+src=\"data:image/[^\">]+base64,", "")
                    .replace("\">", "");
            base64Images.add(base64Image);
        }

        return base64Images;
    }

    public static List<String> extractBase64Images2(String htmlString) {
        List<String> base64Strings = new ArrayList<>();
        String regex = "data:image\\/\\w+;base64,([a-zA-Z0-9+/=]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlString);

        while (matcher.find()) {
            base64Strings.add(matcher.group(1));
        }

        return base64Strings;
    }
}
