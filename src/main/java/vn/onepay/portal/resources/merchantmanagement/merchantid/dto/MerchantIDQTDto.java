package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class MerchantIDQTDto {
  private String pay_gate;
  private String currency_code;
  private String mid;
  private String mcc;
  private String merchant_name;
  private String merchant_id;
  private String trading_name;
  private String paygate_merchant_id;
  private String card_type;
  private String pay_method;
  private String active;
  private Date createdDate;

  public MerchantIDQTDto() {
  }

  public MerchantIDQTDto(String pay_gate, String currency_code, String mid, String mcc, String merchant_name,
      String merchant_id, String trading_name, String paygate_merchant_id, String card_type, String pay_method,
      String active, Date createdDate) {
    this.pay_gate = pay_gate;
    this.currency_code = currency_code;
    this.mid = mid;
    this.mcc = mcc;
    this.merchant_name = merchant_name;
    this.merchant_id = merchant_id;
    this.trading_name = trading_name;
    this.paygate_merchant_id = paygate_merchant_id;
    this.card_type = card_type;
    this.pay_method = pay_method;
    this.active = active;
    this.createdDate = createdDate;
  }

  public String getPay_gate() {
    return this.pay_gate;
  }

  public void setPay_gate(String pay_gate) {
    this.pay_gate = pay_gate;
  }

  public String getCurrency_code() {
    return this.currency_code;
  }

  public void setCurrency_code(String currency_code) {
    this.currency_code = currency_code;
  }

  public String getMid() {
    return this.mid;
  }

  public void setMid(String mid) {
    this.mid = mid;
  }

  public String getMcc() {
    return this.mcc;
  }

  public void setMcc(String mcc) {
    this.mcc = mcc;
  }

  public String getMerchant_name() {
    return this.merchant_name;
  }

  public void setMerchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
  }

  public String getMerchant_id() {
    return this.merchant_id;
  }

  public void setMerchant_id(String merchant_id) {
    this.merchant_id = merchant_id;
  }

  public String getTrading_name() {
    return this.trading_name;
  }

  public void setTrading_name(String trading_name) {
    this.trading_name = trading_name;
  }

  public String getPaygate_merchant_id() {
    return this.paygate_merchant_id;
  }

  public void setPaygate_merchant_id(String paygate_merchant_id) {
    this.paygate_merchant_id = paygate_merchant_id;
  }

  public String getCard_type() {
    return this.card_type;
  }

  public void setCard_type(String card_type) {
    this.card_type = card_type;
  }

  public String getPay_method() {
    return this.pay_method;
  }

  public void setPay_method(String pay_method) {
    this.pay_method = pay_method;
  }

  public String getActive() {
    return this.active;
  }

  public void setActive(String active) {
    this.active = active;
  }

  public MerchantIDQTDto pay_gate(String pay_gate) {
    this.pay_gate = pay_gate;
    return this;
  }

  public MerchantIDQTDto currency_code(String currency_code) {
    this.currency_code = currency_code;
    return this;
  }

  public MerchantIDQTDto mid(String mid) {
    this.mid = mid;
    return this;
  }

  public MerchantIDQTDto createDate(Date createdDate) {
    this.createdDate = createdDate;
    return this;
  }

  public MerchantIDQTDto mcc(String mcc) {
    this.mcc = mcc;
    return this;
  }

  public MerchantIDQTDto merchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
    return this;
  }

  public MerchantIDQTDto merchant_id(String merchant_id) {
    this.merchant_id = merchant_id;
    return this;
  }

  public MerchantIDQTDto trading_name(String trading_name) {
    this.trading_name = trading_name;
    return this;
  }

  public MerchantIDQTDto paygate_merchant_id(String paygate_merchant_id) {
    this.paygate_merchant_id = paygate_merchant_id;
    return this;
  }

  public MerchantIDQTDto card_type(String card_type) {
    this.card_type = card_type;
    return this;
  }

  public MerchantIDQTDto pay_method(String pay_method) {
    this.pay_method = pay_method;
    return this;
  }

  public MerchantIDQTDto active(String active) {
    this.active = active;
    return this;
  }

  public Date getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Date createdDate) {
    this.createdDate = createdDate;
  }

  @Override
  public String toString() {
    return "{" + " pay_gate='" + getPay_gate() + "'" + ", currency_code='" + getCurrency_code() + "'" + ", mid='"
        + getMid() + "'" + ", mcc='" + getMcc() + "'" + ", merchant_name='" + getMerchant_name() + "'"
        + ", merchant_id='" + getMerchant_id() + "'" + ", trading_name='" + getTrading_name() + "'"
        + ", paygate_merchant_id='" + getPaygate_merchant_id() + "'" + ", card_type='" + getCard_type() + "'"
        + ", pay_method='" + getPay_method() + "'" + ", active='" + getActive() + "'" + "}";
  }
}
