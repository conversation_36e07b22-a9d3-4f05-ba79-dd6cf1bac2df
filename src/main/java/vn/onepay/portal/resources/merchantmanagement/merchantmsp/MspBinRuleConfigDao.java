package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;

import static vn.onepay.portal.Util.parseHexBinary;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;

/**
 * User: Hieunt
 * Date: 18/10/2023
 * To change this iportal-service.
 */

public class MspBinRuleConfigDao extends Db implements IConstants {
    public static List<MspBinRuleConfigDto> getBinRule(String merchant_id) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<MspBinRuleConfigDto> binRuleGroupConfigDtos = new ArrayList<>();
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_bin_rule_config(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchant_id);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST get_bin_rule_config error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    binRuleGroupConfigDtos.add(bindBinRuleConfig(rs));
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return binRuleGroupConfigDtos;
    }

    public static MspBinGroupDto getBinRuleById(String binGroupId) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        MspBinGroupDto binGroupDto = new MspBinGroupDto();
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_bin_group_by_id(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, binGroupId);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST get_bin_group_by_id error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    binGroupDto = bindBinGroup(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return binGroupDto;
    }

    public static List<MspBinGroupDto> getAllRuleGroupList() {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<MspBinGroupDto> binGroupDtos = new ArrayList<>();
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_all_bin_group(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST get_all_bin_group error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    binGroupDtos.add(bindBinGroup(rs));
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return binGroupDtos;
    }

    public static void addMspBinGroup(MspBinGroupExtendDto mspBinGroupExtendDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.ADD_BIN_GROUP(?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mspBinGroupExtendDto.getGroupName());
            cs.setString(5, mspBinGroupExtendDto.getBin());
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("ADD MSP BIN GROUP" +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    public static void updateMspBinGroup(MspBinGroupExtendDto mspBinGroupExtendDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.UPDATE_BIN_GROUP(?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mspBinGroupExtendDto.getGroupIdOld());
            cs.setString(5, mspBinGroupExtendDto.getGroupName());
            cs.setString(6, mspBinGroupExtendDto.getBin());
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("ADD MSP BIN GROUP" +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }
    public static void deleteMspBinGroup(String groupId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.DELETE_BIN_GROUP(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, groupId);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("DELETE MSP BIN GROUP" +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    public static void deleteBinRuleConfig(int merCardRuleId, String cardRuleId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.bin_rule_config_delete(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, merCardRuleId);
            cs.setString(4, cardRuleId);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            if (nError != 200) {
                logger.severe("DELETE BIN RULE GROUP error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    public static void editBinRuleConfig(Integer id,String intrumentType, String binGroup, Integer ruleAllow) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String cardType = "";
        String type = "";
        if (intrumentType.contains("ATM")){
            cardType = "atm";
            type = "debit";
        } else {
            cardType = intrumentType.substring(14).toLowerCase();
            type = "credit";
        }
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.bin_rule_config_edit(?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, intrumentType);
            cs.setString(4, binGroup);
            cs.setInt(5, ruleAllow);
            cs.setString(6, cardType);
            cs.setString(7, type);
            cs.setInt(8, id);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            if (nError != 200) {
                logger.severe("EDIT BIN RULE GROUP error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    public static void insertBinRuleConfig(String intrumentType, String binGroup, Integer ruleAllow, String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String cardRuleId = "CARDRULE-" + Base64.getUrlEncoder().withoutPadding()
                .encodeToString(parseHexBinary(UUID.randomUUID().toString().replaceAll("-", "")));
        String cardType = "";
        String type = "";
        if (intrumentType.contains("ATM")){
            cardType = "atm";
            type = "debit";
        } else {
            cardType = intrumentType.substring(15).toLowerCase();
            type = "credit";
        }
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.bin_rule_config_insert(?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, intrumentType);
            cs.setString(4, binGroup);
            cs.setInt(5, ruleAllow);
            cs.setString(6, cardRuleId);
            cs.setString(7, cardType);
            cs.setString(8, type);
            cs.setString(9, merchantId);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            if (nError != 200) {
                logger.severe("INSERT BIN RULE error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    public static List<Map<String,Object>> getBinList() {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<BinBankDto> binBankList = new ArrayList<>();
        List<Map<String,Object>> list = new ArrayList<>();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_bin_bank(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            String sBinListGroup = cs.getString(2);
            rs = (ResultSet) cs.getObject(1);

            String[] sBinListGroupArray = sBinListGroup.split(",");
            if (nError != 200) {
                logger.severe("GET LIST get_bin_bank error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    binBankList.add(bindBinBank(rs));
                }
            }
            for (String sBinList : sBinListGroupArray){
                Map<String, Object> dataBinBank = new HashMap<>();
                dataBinBank.put("bank", sBinList);
                ArrayList items = new ArrayList<>();
                for (BinBankDto binBankDto : binBankList){
                    if(sBinList.equalsIgnoreCase(binBankDto.getBank())){
                        items.add(binBankDto);
                    }
                }
                dataBinBank.put("binBank", items);
                list.add(dataBinBank);
            }


        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return list;
    }

    private static MspBinRuleConfigDto bindBinRuleConfig(ResultSet rs) throws Exception {
        MspBinRuleConfigDto dto = new MspBinRuleConfigDto();
        dto.setId(rs.getInt("N_ID"));
        dto.setMerchantId(rs.getString("S_MERCHANT_ID"));
        dto.setRuleAllow(rs.getInt("N_RULE_ALLOW"));
        dto.setIntrumentType(rs.getString("S_INTRUMENT_TYPE"));
        dto.setBinGroupName(rs.getString("S_BIN_GROUP_NAME"));
        dto.setCardRuleId(rs.getString("S_CARD_RULE_ID"));
        dto.setBinValue(rs.getString("S_BIN_VALUE"));
        return dto;
    }
    private static BinBankDto bindBinBank(ResultSet rs) throws Exception {
        BinBankDto dto = new BinBankDto();
        dto.setBank(rs.getString("S_BANK"));
        dto.setBin(rs.getString("S_BIN"));
        dto.setGate(rs.getString("S_GATE"));
        dto.setLabel(rs.getString("S_LABEL"));
        return dto;
    }
    private static MspBinGroupDto bindBinGroup(ResultSet rs) throws Exception {
        MspBinGroupDto dto = new MspBinGroupDto();
        dto.setGroupId(rs.getString("S_ID"));
        dto.setGroupName(rs.getString("S_NAME"));
        dto.setBin(rs.getString("S_BIN"));
        int checkApporvalState = checkUseBinGroupApproval(rs.getString("S_ID"));
        if (checkApporvalState > 0){
            dto.setState("pending");
        } else {
            dto.setState("ok");
        }
        return dto;
    }

    public static int validateDublicate(String groupId) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.bin_group_check(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, groupId);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            result = cs.getInt(3);
            if (nError != 200) {
                logger.severe("CHECK BIN RULE GROUP ID error: " + nError + " - " + sError);
            } 
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    public static int checkUseBinGroup(String groupId) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.bin_group_use_check(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, groupId);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            result = cs.getInt(3);
            if (nError != 200) {
                logger.severe("CHECK BIN RULE GROUP USING error: " + nError + " - " + sError);
            } 
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    public static int checkUseBinGroupApproval(String groupId) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_APPROVE.checkBinGroup(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, groupId);
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            result = cs.getInt(3);
            if (nError != 200) {
                logger.severe("CHECK BIN RULE GROUP USING APPROVAL error: " + nError + " - " + sError);
            } 
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

}
