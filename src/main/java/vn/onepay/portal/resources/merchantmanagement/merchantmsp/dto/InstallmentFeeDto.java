package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

public class InstallmentFeeDto {
    private int id;
    private String n_3_month_fee;
    private String n_6_month_fee;
    private String n_9_month_fee;
    private String n_12_month_fee;
    private String n_15_month_fee;
    private String n_18_month_fee;
    private String n_24_month_fee;
    private String s_data;
    private int n_fee_type;
    private String swift_code;
    private String name;
    private String description;
    private String string_id;

    public InstallmentFeeDto() {
    }

    public int getId() {
        return id;
    }

    public InstallmentFeeDto setId(int id) {
        this.id = id;
        return this;
    }

    public String getN_3_month_fee() {
        return n_3_month_fee;
    }

    public InstallmentFeeDto setN_3_month_fee(String n_3_month_fee) {
        this.n_3_month_fee = n_3_month_fee;
        return this;
    }

    public String getN_6_month_fee() {
        return n_6_month_fee;
    }

    public InstallmentFeeDto setN_6_month_fee(String n_6_month_fee) {
        this.n_6_month_fee = n_6_month_fee;
        return this;
    }

    public String getN_9_month_fee() {
        return n_9_month_fee;
    }

    public InstallmentFeeDto setN_9_month_fee(String n_9_month_fee) {
        this.n_9_month_fee = n_9_month_fee;
        return this;
    }

    public String getN_12_month_fee() {
        return n_12_month_fee;
    }

    public InstallmentFeeDto setN_12_month_fee(String n_12_month_fee) {
        this.n_12_month_fee = n_12_month_fee;
        return this;
    }

    public String getN_15_month_fee() {
        return n_15_month_fee;
    }

    public InstallmentFeeDto setN_15_month_fee(String n_15_month_fee) {
        this.n_15_month_fee = n_15_month_fee;
        return this;
    }

    public String getN_18_month_fee() {
        return n_18_month_fee;
    }

    public InstallmentFeeDto setN_18_month_fee(String n_18_month_fee) {
        this.n_18_month_fee = n_18_month_fee;
        return this;
    }

    public String getN_24_month_fee() {
        return n_24_month_fee;
    }

    public InstallmentFeeDto setN_24_month_fee(String n_24_month_fee) {
        this.n_24_month_fee = n_24_month_fee;
        return this;
    }

    public Integer getN_fee_type() {
        return n_fee_type;
    }

    public InstallmentFeeDto setN_fee_type(Integer n_fee_type) {
        this.n_fee_type = n_fee_type;
        return this;
    }

    public String getSwift_code() {
        return swift_code;
    }

    public InstallmentFeeDto setSwift_code(String swift_code) {
        this.swift_code = swift_code;
        return this;
    }

    public String getName() {
        return name;
    }

    public InstallmentFeeDto setName(String name) {
        this.name = name;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public InstallmentFeeDto setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getString_id() {
        return string_id;
    }

    public InstallmentFeeDto setString_id(String string_id) {
        this.string_id = string_id;
        return this;
    }

    public String getS_data() {
        return s_data;
    }

    public InstallmentFeeDto setS_data(String s_data) {
        this.s_data = s_data;
        return this;
    }
}
