package vn.onepay.portal.resources.merchantmanagement.contract;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

public class ContractUtils {
  private static final Logger logger = Logger.getLogger(ContractUtils.class.getName());

  public static String replaceVariable(String template, String oldText, String newText){
    if(!template.equals("")){
      return template = template.replaceAll(oldText, newText);
    }
    else return template;
  }
}

