package vn.onepay.portal.resources.merchantmanagement.merchantid;

import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collectors;

public class MerchantidDao extends Db implements IConstants {

    // get list BNPL by partner id
    public static BaseList<MerchantIDQRDto> getListGateBnplByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQRDto> result = new BaseList<>();
        List<MerchantIDQRDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_bnpl_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQRDto()
                            .merchant_id(rs.getString("S_ID"))
                            .merchant_name(rs.getString("S_NAME"))
                            .accept_instrument(rs.getString("S_ACCEPT_INSTRUMENT"))
                            .active(rs.getString("S_ACTIVE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list vietQR by partner id
    public static BaseList<MerchantIDQRDto> getListGateVietQRByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQRDto> result = new BaseList<>();
        List<MerchantIDQRDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_vietqr_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQRDto()
                            .merchant_id(rs.getString("S_ID"))
                            .merchant_name(rs.getString("S_NAME"))
                            .accept_instrument(rs.getString("S_ACCEPT_INSTRUMENT"))
                            .active(rs.getString("S_ACTIVE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }
    
     // get list direct debit by partner id
     public static BaseList<MerchantIDQRDto> getListGateDirectDebitByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQRDto> result = new BaseList<>();
        List<MerchantIDQRDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_dd_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQRDto()
                            .merchant_id(rs.getString("S_ID"))
                            .merchant_name(rs.getString("S_NAME"))
                            .accept_instrument(rs.getString("S_ACCEPT_INSTRUMENT"))
                            .active(rs.getString("S_ACTIVE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }
    
    // get list bank QT by partner id
    public static BaseList<MerchantIDQTDto> getListGateQTbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQTDto> result = new BaseList<>();
        List<MerchantIDQTDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_gate_qt_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQTDto().pay_gate(rs.getString("S_PAYGATE"))
                            .currency_code(rs.getString("S_CURRENCY_CODE")).mid(rs.getString("S_MID"))
                            .mcc(rs.getString("S_MCC")).merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .merchant_id(rs.getString("S_MERCHANT_ID")).trading_name(rs.getString("S_TRADING_NAME"))
                            .paygate_merchant_id(rs.getString("S_PAYGATE_MERCHANT_ID"))
                            .card_type(rs.getString("S_CARD_TYPE")).pay_method(rs.getString("S_PAY_METHOD"))
                            .active(rs.getString("S_ACTIVE")).createDate(rs.getDate("D_CREATE_DATE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list bank QT by partner id
    public static BaseList<MerchantIDQTDto> getListGateInvoiceQTbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQTDto> result = new BaseList<>();
        List<MerchantIDQTDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_gate_invoice(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQTDto().pay_gate(rs.getString("S_PAYGATE"))
                            .currency_code(rs.getString("S_CURRENCY_CODE")).mid(rs.getString("S_MID"))
                            .mcc(rs.getString("S_MCC")).merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .merchant_id(rs.getString("S_MERCHANT_ID")).trading_name(rs.getString("S_TRADING_NAME"))
                            .paygate_merchant_id(rs.getString("S_PAYGATE_MERCHANT_ID"))
                            .card_type(rs.getString("S_CARD_TYPE")).pay_method(rs.getString("S_PAY_METHOD"))
                            .active(rs.getString("S_ACTIVE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list bank ND by partner id
    public static BaseList<MerchantIDNDDto> getListGateNDbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDNDDto> result = new BaseList<>();
        List<MerchantIDNDDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_gate_nd_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDNDDto().merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .merchant_id(rs.getString("S_MERCHANT_ID")).trading_name(rs.getString("S_TRADING_NAME"))
                            .vcb_merchant_id(rs.getString("S_VCB_MERCHANT_ID"))
                            .vcb_merchant_offline_id(rs.getString("S_MERCHANT_BANK")).active(rs.getString("S_ACTIVE"))
                            .card_type(rs.getString("S_CARD_TYPE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // code cu de dung cho sale
    public static BaseList<MerchantIDNDDto> getListGateQRbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDNDDto> result = new BaseList<>();
        List<MerchantIDNDDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_gate_qr_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDNDDto().merchant_id(rs.getString("S_ID")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list bank QR by partner id code moi dung cho merchantid
    public static BaseList<MerchantIDQRDto> getListGateQRbyPartnerId2(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQRDto> result = new BaseList<>();
        List<MerchantIDQRDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_gate_qr_by_partner_id_2(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                List<String> listQrVietinApp = Util.getListQrVietinApp();
                while (rs != null && rs.next()) {
                    String acceptInstrument = rs.getString("S_ACCEPT_INSTRUMENT");
                    String qrType = rs.getString("S_QR_TYPE");
                    boolean checkQr = false;
                    //Check QR config by accept instructment 
                    if ("static".equalsIgnoreCase(qrType) || acceptInstrument.contains("_970415") || acceptInstrument.contains("_970426") || acceptInstrument.contains("_970436")) {
                        checkQr = true;
                    } else {
                        checkQr = Util.checkMerchantQrVietin(acceptInstrument,listQrVietinApp);
                    }
                    if ( checkQr ){
                        list.add(new MerchantIDQRDto().merchant_id(rs.getString("S_ID"))
                                .merchant_name(rs.getString("S_NAME"))
                                .accept_instrument(acceptInstrument).active(rs.getString("S_ACTIVE")));
                    }
                
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<AcquirerDto> getListAcquirer() throws Exception {
        Exception exception = null;
        BaseList<AcquirerDto> result = new BaseList<>();
        List<AcquirerDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_acquirer(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acquirer error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new AcquirerDto().id(rs.getInt("N_ACQUIRER_ID")).name(rs.getString("S_ACQUIRER_NAME"))
                            .short_name(rs.getString("S_ACQUIRER_SHORT_NAME"))
                            .description(rs.getString("S_DESCRIPTION")).code(rs.getString("S_ACQUIRER_CODE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<AppDto> getListApp() throws Exception {
        Exception exception = null;
        BaseList<AppDto> result = new BaseList<>();
        List<AppDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_app(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load App error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new AppDto().id(rs.getInt("N_APP_ID")).name(rs.getString("S_APP_NAME"))
                            .short_name(rs.getString("S_APP_SHORT_NAME"))
                            .description(rs.getString("S_DESCRIPTION")).code(rs.getString("S_APP_CODE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<AppDto> getListApp2() throws Exception {
        Exception exception = null;
        BaseList<AppDto> result = new BaseList<>();
        List<AppDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT_CONFIG.get_qr_app_all(?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                list.add(new AppDto().id(rs.getInt("N_APP_ID")).name(rs.getString("S_APP_NAME"))
                        .short_name(rs.getString("S_APP_SHORT_NAME"))
                        .description(rs.getString("S_DESCRIPTION")).code(rs.getString("S_APP_CODE")));
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // get list Invoice by partner id
    public static BaseList<MerchantIDInvoiceDto> getListInvoicebyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDInvoiceDto> result = new BaseList<>();
        List<MerchantIDInvoiceDto> listInvoice = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_gate_invoice_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    listInvoice.add(new MerchantIDInvoiceDto()
                            .onecredit_merchant_id(rs.getString("S_ONECREDIT_MERCHANT_ID") == null ? ""
                                    : rs.getString("S_ONECREDIT_MERCHANT_ID"))
                            .invoice_merchant_id(rs.getString("S_INVOICE_MERCHANT_ID") == null ? ""
                                    : rs.getString("S_INVOICE_MERCHANT_ID"))
                            .merchant_name(
                                    rs.getString("S_MERCHANT_NAME") == null ? "" : rs.getString("S_MERCHANT_NAME"))
                            .currency_code(
                                    rs.getString("S_CURRENCY_CODE") == null ? "" : rs.getString("S_CURRENCY_CODE"))
                            .active(rs.getString("S_ACTIVE") == null ? "" : rs.getString("S_ACTIVE")));
                }
            }
            if (listInvoice.size() != 0) {
                for (int i = 0; i < listInvoice.size(); i++) {
                    for (int j = 0; j < listInvoice.size(); j++) {

                        // Check dieu kien neu du lieu trung Invoice Merchant Id && Active => Group theo
                        // Invoice Merchant Id
                        if (listInvoice.get(i).getInvoice_merchant_id()
                                .equals(listInvoice.get(j).getInvoice_merchant_id()) && (i != j)
                                && (listInvoice.get(i).getActive().equals(listInvoice.get(j).getActive()))) {

                            // Neu trung Onecredit Merchant Id => Group Merchant Id
                            if (!listInvoice.get(i).getOnecredit_merchant_id()
                                    .equals(listInvoice.get(j).getOnecredit_merchant_id())) {
                                listInvoice.get(i)
                                        .setOnecredit_merchant_id(listInvoice.get(i).getOnecredit_merchant_id() + ", "
                                                + listInvoice.get(j).getOnecredit_merchant_id());
                            }
                            // Neu trung Onecredit Merchant Name => Group Merchant Name
                            if (!listInvoice.get(i).getMerchant_name().equals(listInvoice.get(j).getMerchant_name())) {
                                listInvoice.get(i).setMerchant_name(listInvoice.get(i).getMerchant_name() + ", "
                                        + listInvoice.get(j).getMerchant_name());
                            }
                            // Neu trung Onecredit Currency => Group Currency
                            if (!listInvoice.get(i).getCurrency_code().equals(listInvoice.get(j).getCurrency_code())) {
                                listInvoice.get(i).setCurrency_code(listInvoice.get(i).getCurrency_code() + ", "
                                        + listInvoice.get(j).getCurrency_code());
                            }

                            listInvoice.remove(listInvoice.get(j));
                        }
                    }
                }
            }

            result.setTotalItems(listInvoice.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(listInvoice);
        return result;
    }

    // get list QuickLink by partner id
    public static BaseList<MerchantIDQuickLinkDto> getListQuickLinkbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQuickLinkDto> result = new BaseList<>();
        List<MerchantIDQuickLinkDto> listInvoice = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_gate_ql_by_partner_id_v2(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    listInvoice.add(new MerchantIDQuickLinkDto()
                            .merchantProfileName(rs.getString("S_MERCHANT_PROFILE_NAME") == null ? ""
                                    : rs.getString("S_MERCHANT_PROFILE_NAME"))
                            .merchantId(rs.getString("S_MERCHANT_ID") == null ? ""
                                    : rs.getString("S_MERCHANT_ID"))
                            .merchantName(rs.getString("S_MERCHANT_NAME") == null ? "" : rs.getString("S_MERCHANT_NAME"))
                            .active(rs.getString("S_ACTIVE") == null ? "" : rs.getString("S_ACTIVE"))
                            .paymentMethod(
                                rs.getString("S_PAYMENT_METHOD") == null ? "" : rs.getString("S_PAYMENT_METHOD")));
                }
            }
            result.setTotalItems(listInvoice.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(listInvoice);
        return result;
    }
    //get list merchant Quicklink
    public static BaseList<MerchantIDQuickLinkDto> getListGateQuicklinkbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQuickLinkDto> result = new BaseList<>();
        List<MerchantIDQuickLinkDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_gate_ql_by_partner_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantIDQuickLinkDto().merchantId(rs.getString("S_ID")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static List<String> getMerchantIdUposByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        List<String> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_upos_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management.get_gate_upos_by_partner_id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(Util.getColumnString(rs, "S_ID"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static BaseList<MerchantIDQRDto> getListGateUposByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantIDQRDto> result = new BaseList<>();
        List<MerchantIDQRDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_upos_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management.get_gate_upos_by_partner_id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.addAll(getUposTeminalId(Util.getColumnString(rs, "S_ID"), Util.getColumnString(rs, "S_ACTIVE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<MerchantIDPayoutDto> getListGatePayoutByPartnerId(Map mIn) throws Exception {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        MerchantIDPayoutDto merchantPayoutDto = null;
        List<MerchantIDPayoutDto> list = new ArrayList<>();
        BaseList<MerchantIDPayoutDto> result = new BaseList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_payout_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management.get_gate_payout_by_partner_id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    merchantPayoutDto = new MerchantIDPayoutDto();
                    merchantPayoutDto.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
                    merchantPayoutDto.setMerchantName(Util.getColumnString(rs, "S_MERCHANT_NAME"));
                    merchantPayoutDto.setState(Util.getColumnString(rs, "S_STATE"));
                    list.add(merchantPayoutDto);
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<MerchantIDPaycollectDto> getListGatePaycollectByPartnerId(Map mIn) throws Exception {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        MerchantIDPaycollectDto merchantPaycollectDto = null;
        List<MerchantIDPaycollectDto> list = new ArrayList<>();
        BaseList<MerchantIDPaycollectDto> result = new BaseList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_gate_pc_by_partner_id(?,?,?,?)} ");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB p_merchant_management.get_gate_paycollect_by_partner_id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    merchantPaycollectDto = new MerchantIDPaycollectDto();
                    merchantPaycollectDto.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
                    merchantPaycollectDto.setMerchantName(Util.getColumnString(rs, "S_MERCHANT_NAME"));
                    merchantPaycollectDto.setState(Util.getColumnString(rs, "S_STATE"));
                    list.add(merchantPaycollectDto);
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    private static List<MerchantIDQRDto> getUposTeminalId(String merchantId, String status) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        MerchantIDQRDto merchantUposDto = null;
        List<MerchantIDQRDto> lstMerchantUpos = new ArrayList<>();
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call PSP_CONNECTOR.PKG_UPOS.get_terminal_id_by_mid_v2(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, merchantId);

            cs.execute();
            int n_result = cs.getInt(1);
            if (n_result != 200) {
                logger.severe("DB PSP_CONNECTOR.PKG_UPOS.get_terminal_id_by_mid error: " + cs.getString(2));
            } else {
                rs = (ResultSet) cs.getObject(3);
                while (rs != null && rs.next()) {
                    merchantUposDto = new MerchantIDQRDto();
                    merchantUposDto.setMethod(Util.getColumnString(rs, "S_METHOD"));
                    merchantUposDto.setMerchant_id(Util.getColumnString(rs, "S_MERCHANT_ID"));
                    merchantUposDto.setActive(status);
                    merchantUposDto.setTerminal_id(getTerminalIdByMid(Util.getColumnString(rs, "S_ID")));
                    lstMerchantUpos.add(merchantUposDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null) {
            throw exception;
        }
        return lstMerchantUpos;
    }

    private static String getTerminalIdByMid(String mid) throws Exception {
        List<String> lstResult = new ArrayList<>();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{call PSP_CONNECTOR.PKG_UPOS.get_terminal_id_by_shop_id(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mid);

            cs.execute();
            int n_result = cs.getInt(1);
            if (n_result != 200) {
                logger.severe("DB PSP_CONNECTOR.PKG_UPOS.get_terminal_id_by_mid error: " + cs.getString(2));
            } else {
                rs = (ResultSet) cs.getObject(3);
                while (rs != null && rs.next()) {
                  lstResult.add(Util.getColumnString(rs, "S_TERMINAL_ID"));
                }
            }
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null) {
            throw exception;
        }
        return lstResult.size() > 0 ? lstResult.stream().collect(Collectors.joining(",")) : "";
    }

}
