/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/28/19 9:56 AM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class PartnerDto {
    private int id;
    private int active;
    private String shortName;
    private String partnerName;
    private String registerName;
    private String tradingName;
    private String desc;
    private String tradingId;
    private String categoryCode;
    private String goodsDescription;
    private String website;
    private String locale;
    private String timezone;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String postalCode;
    private String country;
    private String contactName;
    private String phoneNumber;
    private String escalationPhoneNumber;
    private String mobileNumber;
    private String faxNumber;
    private String sale;
    private String mm;
    private String email;
    private String type;
    private String taxCode;
    private int provinceId;
    private int provinceIdOffice;
    private String businessCode;
    private Date businessRegisterDate;
    private int careerId;
    private String careerOther;
    private String riskReview;
    private Date createDate;
    private Date updateDate;
    private boolean isAssigned;
    private String status;

    public PartnerDto() {
    }

    public PartnerDto(int id) {
        this.id = id;
    }

    public PartnerDto(int id, int active, String shortName, String partnerName, String registerName, String tradingName,
            String desc, String tradingId, String categoryCode, String goodsDescription, String website, String locale,
            String timezone, String addressLine1, String addressLine2, String city, String state, String postalCode,
            String country, String contactName, String phoneNumber, String escalationPhoneNumber, String mobileNumber,
            String faxNumber, String sale, String email, String type, String taxCode, int provinceId,
            int provinceIdOffice, String businessCode, Date businessRegisterDate, int careerId, String careerOther,
            String riskReview, Date createDate, Date updateDate, String status) {
        this.id = id;
        this.active = active;
        this.shortName = shortName;
        this.partnerName = partnerName;
        this.registerName = registerName;
        this.tradingName = tradingName;
        this.desc = desc;
        this.tradingId = tradingId;
        this.categoryCode = categoryCode;
        this.goodsDescription = goodsDescription;
        this.website = website;
        this.locale = locale;
        this.timezone = timezone;
        this.addressLine1 = addressLine1;
        this.addressLine2 = addressLine2;
        this.city = city;
        this.state = state;
        this.postalCode = postalCode;
        this.country = country;
        this.contactName = contactName;
        this.phoneNumber = phoneNumber;
        this.escalationPhoneNumber = escalationPhoneNumber;
        this.mobileNumber = mobileNumber;
        this.faxNumber = faxNumber;
        this.sale = sale;
        this.email = email;
        this.type = type;
        this.taxCode = taxCode;
        this.provinceId = provinceId;
        this.provinceIdOffice = provinceIdOffice;
        this.businessCode = businessCode;
        this.businessRegisterDate = businessRegisterDate;
        this.careerId = careerId;
        this.careerOther = careerOther;
        this.riskReview = riskReview;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.status = status;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getActive() {
        return this.active;
    }

    public void setActive(int active) {
        this.active = active;
    }

    public String getShortName() {
        return this.shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getRegisterName() {
        return this.registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    public String getTradingName() {
        return this.tradingName;
    }

    public void setTradingName(String tradingName) {
        this.tradingName = tradingName;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTradingId() {
        return this.tradingId;
    }

    public void setTradingId(String tradingId) {
        this.tradingId = tradingId;
    }

    public String getCategoryCode() {
        return this.categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getGoodsDescription() {
        return this.goodsDescription;
    }

    public void setGoodsDescription(String goodsDescription) {
        this.goodsDescription = goodsDescription;
    }

    public String getWebsite() {
        return this.website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getLocale() {
        return this.locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getTimezone() {
        return this.timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getAddressLine1() {
        return this.addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return this.addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostalCode() {
        return this.postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountry() {
        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getContactName() {
        return this.contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEscalationPhoneNumber() {
        return this.escalationPhoneNumber;
    }

    public void setEscalationPhoneNumber(String escalationPhoneNumber) {
        this.escalationPhoneNumber = escalationPhoneNumber;
    }

    public String getMobileNumber() {
        return this.mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getFaxNumber() {
        return this.faxNumber;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getSale() {
        return this.sale;
    }

    public void setSale(String sale) {
        this.sale = sale;
    }

    public String getMm() {
        return this.mm;
    }

    public void setMm(String mm) {
        this.mm = mm;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTaxCode() {
        return this.taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public int getProvinceId() {
        return this.provinceId;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public int getProvinceIdOffice() {
        return this.provinceIdOffice;
    }

    public void setProvinceIdOffice(int provinceIdOffice) {
        this.provinceIdOffice = provinceIdOffice;
    }

    public String getBusinessCode() {
        return this.businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Date getBusinessRegisterDate() {
        return this.businessRegisterDate;
    }

    public void setBusinessRegisterDate(Date businessRegisterDate) {
        this.businessRegisterDate = businessRegisterDate;
    }

    public int getCareerId() {
        return this.careerId;
    }

    public void setCareerId(int careerId) {
        this.careerId = careerId;
    }

    public String getCareerOther() {
        return this.careerOther;
    }

    public void setCareerOther(String careerOther) {
        this.careerOther = careerOther;
    }

    public String getRiskReview() {
        return this.riskReview;
    }

    public void setRiskReview(String riskReview) {
        this.riskReview = riskReview;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PartnerDto id(int id) {
        this.id = id;
        return this;
    }

    public PartnerDto active(int active) {
        this.active = active;
        return this;
    }

    public PartnerDto shortName(String shortName) {
        this.shortName = shortName;
        return this;
    }

    public PartnerDto partnerName(String partnerName) {
        this.partnerName = partnerName;
        return this;
    }

    public PartnerDto registerName(String registerName) {
        this.registerName = registerName;
        return this;
    }

    public PartnerDto tradingName(String tradingName) {
        this.tradingName = tradingName;
        return this;
    }

    public PartnerDto desc(String desc) {
        this.desc = desc;
        return this;
    }

    public PartnerDto tradingId(String tradingId) {
        this.tradingId = tradingId;
        return this;
    }

    public PartnerDto categoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return this;
    }

    public PartnerDto goodsDescription(String goodsDescription) {
        this.goodsDescription = goodsDescription;
        return this;
    }

    public PartnerDto website(String website) {
        this.website = website;
        return this;
    }

    public PartnerDto locale(String locale) {
        this.locale = locale;
        return this;
    }

    public PartnerDto timezone(String timezone) {
        this.timezone = timezone;
        return this;
    }

    public PartnerDto addressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
        return this;
    }

    public PartnerDto addressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
        return this;
    }

    public PartnerDto city(String city) {
        this.city = city;
        return this;
    }

    public PartnerDto state(String state) {
        this.state = state;
        return this;
    }

    public PartnerDto postalCode(String postalCode) {
        this.postalCode = postalCode;
        return this;
    }

    public PartnerDto country(String country) {
        this.country = country;
        return this;
    }

    public PartnerDto contactName(String contactName) {
        this.contactName = contactName;
        return this;
    }

    public PartnerDto phoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public PartnerDto escalationPhoneNumber(String escalationPhoneNumber) {
        this.escalationPhoneNumber = escalationPhoneNumber;
        return this;
    }

    public PartnerDto mobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    public PartnerDto faxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
        return this;
    }

    public PartnerDto sale(String sale) {
        this.sale = sale;
        return this;
    }

    public PartnerDto mm(String mm) {
        this.mm = mm;
        return this;
    }

    public PartnerDto email(String email) {
        this.email = email;
        return this;
    }

    public PartnerDto type(String type) {
        this.type = type;
        return this;
    }

    public PartnerDto taxCode(String taxCode) {
        this.taxCode = taxCode;
        return this;
    }

    public PartnerDto provinceId(int provinceId) {
        this.provinceId = provinceId;
        return this;
    }

    public PartnerDto provinceIdOffice(int provinceIdOffice) {
        this.provinceIdOffice = provinceIdOffice;
        return this;
    }

    public PartnerDto businessCode(String businessCode) {
        this.businessCode = businessCode;
        return this;
    }

    public PartnerDto businessRegisterDate(Date businessRegisterDate) {
        this.businessRegisterDate = businessRegisterDate;
        return this;
    }

    public PartnerDto careerId(int careerId) {
        this.careerId = careerId;
        return this;
    }

    public PartnerDto careerOther(String careerOther) {
        this.careerOther = careerOther;
        return this;
    }

    public PartnerDto riskReview(String riskReview) {
        this.riskReview = riskReview;
        return this;
    }

    public PartnerDto createDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public PartnerDto updateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    @Override
    public String toString() {
        return "PartnerDto [id=" + id + ", active=" + active + ", shortName=" + shortName + ", partnerName="
                + partnerName + ", registerName=" + registerName + ", tradingName=" + tradingName + ", desc=" + desc
                + ", tradingId=" + tradingId + ", categoryCode=" + categoryCode + ", goodsDescription="
                + goodsDescription + ", website=" + website + ", locale=" + locale + ", timezone=" + timezone
                + ", addressLine1=" + addressLine1 + ", addressLine2=" + addressLine2 + ", city=" + city + ", state="
                + state + ", postalCode=" + postalCode + ", country=" + country + ", contactName=" + contactName
                + ", phoneNumber=" + phoneNumber + ", escalationPhoneNumber=" + escalationPhoneNumber
                + ", mobileNumber=" + mobileNumber + ", faxNumber=" + faxNumber + ", sale=" + sale + ", mm=" + mm
                + ", email=" + email + ", type=" + type + ", taxCode=" + taxCode + ", provinceId=" + provinceId
                + ", provinceIdOffice=" + provinceIdOffice + ", businessCode=" + businessCode
                + ", businessRegisterDate=" + businessRegisterDate + ", careerId=" + careerId + ", careerOther="
                + careerOther + ", riskReview=" + riskReview + ", createDate=" + createDate + ", updateDate="
                + updateDate + ", isAssigned=" + isAssigned + ", status=" + status + "]";
    }

    public boolean isIsAssigned() {
        return this.isAssigned;
    }

    public PartnerDto setIsAssigned(boolean isAssigned) {
        this.isAssigned = isAssigned;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public PartnerDto status(String status) {
        this.status = status;
        return this;
    }
    
}
