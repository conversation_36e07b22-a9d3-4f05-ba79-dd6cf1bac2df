/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 4/1/19 5:46 PM
 */

package vn.onepay.portal.resources.merchantmanagement.contract;

import io.vertx.core.buffer.Buffer;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.merchantmanagement.contract.contract_generator.ContractGenerator;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractDto;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractOriginal;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.UpdateFeeInstallmentReq;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V3Generator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V4BilingualGenerator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V4Generator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V5BilingualGenerator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V5Generator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD14Generator;
import vn.onepay.portal.resources.merchantmanagement.contract.html_generator.HD13V3BilingualGenerator;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoDao;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoHandler;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;
import vn.onepay.portal.resources.mm.MmSubscribeDao;
import vn.onepay.portal.resources.sale.SaleSubscribeDao;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.contract_template.dao.ContractTemplateDao;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.resources.document_tracking.dto.SubDocumentDto;
import vn.onepay.portal.resources.File.FileDownloadDao;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;

import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.queue.listener.excelBuilder.ExcelReportGenerator;
import vn.onepay.portal.queue.listener.wordBuilder.ExportWordFileBuilder;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.logging.Level;
import java.util.ArrayList;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;
import com.google.common.io.Files;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import static vn.onepay.portal.Util.sendResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipEntry;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.io.InputStreamReader;

public class ContractHandler implements IConstants {
    private static Logger logger = Logger.getLogger(ContractHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void loadContractTypeList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, ContractDao.getContractTypeList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadBranchList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, ContractDao.getBranchList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getContractsByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0 : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);
                String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                String saleSubs = SaleSubscribeDao.getSaleByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));
                String mmSubs = MmSubscribeDao.getMMByUserId(userId)
                        .stream().map(x -> x.getId().toString())
                        .collect(Collectors.joining(","));

                Map<String, Object> mIn = new HashMap<>();

                mIn.put(PARTNER_ID, partnerId);
                mIn.put(USER_ID, userId);
                Map<String, Object> rs = new HashMap<>();
                rs.put("list", ContractDao.customContractList(mIn).getList());
                mIn.put(PROVINCE_ID, provinceIds);
                mIn.put("sale_id", saleSubs);
                mIn.put("mm_id", mmSubs);
                rs.put("sale", Optional.ofNullable(MerchantInfoDao.getPartnerByID(mIn).getSale()).orElse(""));
                sendResponse(ctx, 200, rs);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // duynq get contractById
    public static void getContractById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer contractId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, contractId);

                // check permission
                ContractDto contract = ContractDao.getContractById(mIn);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartner_id(), userId);

                sendResponse(ctx, 200, contract);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void RemoveContractByID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                sendResponse(ctx, 200, ContractDao.RemoveContractByID(mIn));
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "DB Remove Contract error : " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    // duynq insert contract
    public static void InsertAndUpdateContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                mIn.put(PARTNER_ID, body.getString(PARTNER_ID) == null ? BLANK : body.getString(PARTNER_ID));
                mIn.put(CONTRACT_CODE, body.getString(CONTRACT_CODE) == null ? BLANK : body.getString(CONTRACT_CODE));
                mIn.put(TYPE_ID, body.getString(TYPE_ID) == null ? BLANK : body.getString(TYPE_ID));
                mIn.put(SIGN_DATE, body.getString(SIGN_DATE) == null ? 0 : body.getString(SIGN_DATE));
                mIn.put(COMPLETED_DATE,
                        body.getString(COMPLETED_DATE) == null ? BLANK : body.getString(COMPLETED_DATE));
                mIn.put(BANK_ID, body.getString(BANK_ID) == null ? BLANK : body.getString(BANK_ID));
                mIn.put(BRANCH, body.getString(BRANCH) == null ? BLANK : body.getString(BRANCH));
                mIn.put(NAME, body.getString(NAME) == null ? BLANK : body.getString(NAME));
                mIn.put(TITLE, body.getString(TITLE) == null ? BLANK : body.getString(TITLE));
                mIn.put(ACCOUNT, body.getString(ACCOUNT) == null ? BLANK : body.getString(ACCOUNT));
                mIn.put(STATUS, body.getString(STATUS) == null ? BLANK : body.getString(STATUS));
                mIn.put(CLOSE_DATE, body.getString(CLOSE_DATE) == null ? 0 : body.getString(CLOSE_DATE));
                mIn.put(EXPIRED_DATE, body.getString(EXPIRED_DATE) == null ? BLANK : body.getString(EXPIRED_DATE));
                mIn.put(FIN_ACCOUNT, body.getString(FIN_ACCOUNT) == null ? BLANK : body.getString(FIN_ACCOUNT));
                mIn.put(TYPE, body.getString(TYPE) == null ? BLANK : body.getString(TYPE));
                mIn.put(GUARANTEE_TYPE,
                        body.getString(GUARANTEE_TYPE) == null ? BLANK : body.getString(GUARANTEE_TYPE));
                mIn.put(GUARANTEE_DETAIL,
                        body.getString(GUARANTEE_DETAIL) == null ? BLANK : body.getString(GUARANTEE_DETAIL));
                mIn.put(NOTE, body.getString(NOTE) == null ? BLANK : body.getString(NOTE));
                mIn.put(PARENT, body.getString(PARENT) == null ? BLANK : body.getString(PARENT));
                mIn.put(ACCOUNT_NAME, body.getString(ACCOUNT_NAME) == null ? BLANK : body.getString(ACCOUNT_NAME));
                mIn.put(TEL, body.getString(TEL) == null ? BLANK : body.getString(TEL));
                mIn.put(FAX, body.getString(FAX) == null ? BLANK : body.getString(FAX));
                mIn.put(ADDRESS, body.getString(ADDRESS) == null ? BLANK : body.getString(ADDRESS));
                mIn.put(FEE_SETUP, body.getString(FEE_SETUP) == null ? BLANK : body.getString(FEE_SETUP));
                mIn.put(POSITION, body.getString(POSITION) == null ? BLANK : body.getString(POSITION));
                mIn.put(WEBSITE, body.getString(WEBSITE) == null ? BLANK : body.getString(WEBSITE));
                mIn.put(SETTLEMENT, body.getString(SETTLEMENT) == null ? BLANK : body.getString(SETTLEMENT));
                mIn.put(TEN_DKKD, body.getString(TEN_DKKD) == null ? BLANK : body.getString(TEN_DKKD));
                mIn.put(BANK_GD_ID, body.getString(BANK_GD_ID) == null ? BLANK : body.getString(BANK_GD_ID));
                mIn.put(THOIGIAN_TAMUNG,
                        body.getString(THOIGIAN_TAMUNG) == null ? BLANK : body.getString(THOIGIAN_TAMUNG));
                mIn.put(USER_UPDATE, body.getString(USER_UPDATE) == null ? BLANK : body.getString(USER_UPDATE));
                mIn.put(UPDATE_TIME, body.getString(UPDATE_TIME) == null ? BLANK : body.getString(UPDATE_TIME));
                mIn.put(USER_APPROVE, body.getString(USER_APPROVE) == null ? BLANK : body.getString(USER_APPROVE));
                mIn.put(APPROVE_TIME, body.getString(APPROVE_TIME) == null ? BLANK : body.getString(APPROVE_TIME));
                mIn.put(STATE_APPROVE, body.getString(STATE_APPROVE) == null ? BLANK : body.getString(STATE_APPROVE));
                mIn.put(NAME_BANK_ACCOUNT,
                        body.getString(NAME_BANK_ACCOUNT) == null ? BLANK : body.getString(NAME_BANK_ACCOUNT));
                mIn.put(ACTIVE, body.getString(ACTIVE) == null ? BLANK : body.getString(ACTIVE));
                mIn.put(SETUP_FEE, body.getString(SETUP_FEE) == null ? BLANK : body.getString(SETUP_FEE));
                mIn.put(SETUP_FEE_CURRENCY,
                        body.getString(SETUP_FEE_CURRENCY) == null ? BLANK : body.getString(SETUP_FEE_CURRENCY));
                mIn.put(SHORT_NAME, body.getString(SHORT_NAME) == null ? BLANK : body.getString(SHORT_NAME));
                mIn.put(SO_DKKD, body.getString(SO_DKKD) == null ? BLANK : body.getString(SO_DKKD));
                mIn.put(ISSUE, body.getString(ISSUE) == null ? BLANK : body.getString(ISSUE));
                mIn.put(GUARANTEE, body.getString(GUARANTEE) == null ? BLANK : body.getString(GUARANTEE));
                mIn.put(GUARANTEE_ACCOUNT,
                        body.getString(GUARANTEE_ACCOUNT) == null ? BLANK : body.getString(GUARANTEE_ACCOUNT));
                mIn.put(ADVANCE_1ST, body.getString(ADVANCE_1ST) == null ? BLANK : body.getString(ADVANCE_1ST));
                mIn.put(ADVANCE_2ST_DATE,
                        body.getString(ADVANCE_2ST_DATE) == null ? BLANK : body.getString(ADVANCE_2ST_DATE));
                mIn.put(INIT_FEE, body.getValue(INIT_FEE) == null ? "" : String.valueOf(body.getValue(INIT_FEE)));
                mIn.put(INTER_CARD_FEE,
                        body.getValue(INTER_CARD_FEE) == null ? "" : String.valueOf(body.getValue(INTER_CARD_FEE)));
                mIn.put(DOMES_CARD_INIT_FEE, body.getValue(DOMES_CARD_INIT_FEE) == null ? ""
                        : String.valueOf(body.getValue(DOMES_CARD_INIT_FEE)));
                mIn.put(DOMES_TOKEN_INIT_FEE, body.getValue(DOMES_TOKEN_INIT_FEE) == null ? ""
                        : String.valueOf(body.getValue(DOMES_TOKEN_INIT_FEE)));
                mIn.put(MONTHLY_SERVICE_FEE, body.getValue(MONTHLY_SERVICE_FEE) == null ? ""
                        : String.valueOf(body.getValue(MONTHLY_SERVICE_FEE)));
                mIn.put(TRANS_PROCESS_FEE, body.getValue(TRANS_PROCESS_FEE) == null ? ""
                        : String.valueOf(body.getValue(TRANS_PROCESS_FEE)));
                mIn.put(CARD_FEE, body.getString(CARD_FEE) == null ? BLANK : body.getString(CARD_FEE));
                mIn.put(FD_MONTH, body.getString(FD_MONTH) == null ? BLANK : body.getString(FD_MONTH));
                mIn.put(FEE_COLLECT_FORM,
                        body.getString(FEE_COLLECT_FORM) == null ? BLANK : body.getString(FEE_COLLECT_FORM));
                mIn.put(BANK_NOTIFY_EMAIL,
                        body.getString(BANK_NOTIFY_EMAIL) == null ? BLANK : body.getString(BANK_NOTIFY_EMAIL));
                mIn.put(TRANS_NOTIFY_EMAIL,
                        body.getString(TRANS_NOTIFY_EMAIL) == null ? BLANK : body.getString(TRANS_NOTIFY_EMAIL));
                mIn.put(SETTLEMENT_TYPE,
                        body.getString(SETTLEMENT_TYPE) == null ? BLANK : body.getString(SETTLEMENT_TYPE));
                mIn.put(INTER_CARD_FEE_CURRENCY, body.getString(INTER_CARD_FEE_CURRENCY) == null ? BLANK
                        : body.getString(INTER_CARD_FEE_CURRENCY));
                mIn.put(DOMES_CARD_INIT_FEE_CURRENCY, body.getString(DOMES_CARD_INIT_FEE_CURRENCY) == null ? BLANK
                        : body.getString(DOMES_CARD_INIT_FEE_CURRENCY));
                mIn.put(DOMES_TOKEN_FEE_CURRENCY, body.getString(DOMES_TOKEN_FEE_CURRENCY) == null ? BLANK
                        : body.getString(DOMES_TOKEN_FEE_CURRENCY));
                mIn.put(INIT_FEE_CURRENCY,
                        body.getString(INIT_FEE_CURRENCY) == null ? BLANK : body.getString(INIT_FEE_CURRENCY));
                mIn.put(TRANS_PROCESS_FEE_CURRENCY, body.getString(TRANS_PROCESS_FEE_CURRENCY) == null ? BLANK
                        : body.getString(TRANS_PROCESS_FEE_CURRENCY));
                mIn.put(MONTHLY_SERVICE_FEE_CURRENCY, body.getString(MONTHLY_SERVICE_FEE_CURRENCY) == null ? BLANK
                        : body.getString(MONTHLY_SERVICE_FEE_CURRENCY));
                mIn.put(CARD_TYPE_PAYMENT,
                        body.getString(CARD_TYPE_PAYMENT) == null ? BLANK : body.getString(CARD_TYPE_PAYMENT));
                mIn.put(NOTE_CARD_FEE, body.getString(NOTE_CARD_FEE) == null ? BLANK : body.getString(NOTE_CARD_FEE));
                mIn.put(TRANS_PROCESS_FEE_QT, body.getValue(TRANS_PROCESS_FEE_QT) == null ? ""
                        : String.valueOf(body.getValue(TRANS_PROCESS_FEE_QT)));
                mIn.put(TRANS_PROCESS_FEE_QT_CURRENCY, body.getString(TRANS_PROCESS_FEE_QT_CURRENCY) == null ? BLANK
                        : body.getString(TRANS_PROCESS_FEE_QT_CURRENCY));
                mIn.put(TRANS_PROCESS_FEE_ND, body.getValue(TRANS_PROCESS_FEE_ND) == null ? ""
                        : String.valueOf(body.getValue(TRANS_PROCESS_FEE_ND)));
                mIn.put(TRANS_PROCESS_FEE_ND_CURRENCY, body.getString(TRANS_PROCESS_FEE_ND_CURRENCY) == null ? BLANK
                        : body.getString(TRANS_PROCESS_FEE_ND_CURRENCY));
                sendResponse(ctx, 200, ContractDao.InsertAndUpdateContract(mIn));
            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "UPDATE Contract ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list contract payment by contract id
    public static void approveContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer contractId = Integer.parseInt(request.getParam(ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTRACT_ID, contractId);
                mIn.put(USER_ID, "22");
                sendResponse(ctx, 200, ContractDao.approveContract(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void unapproveContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer contractId = Integer.parseInt(request.getParam(ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTRACT_ID, contractId);
                mIn.put(USER_ID, "22");
                sendResponse(ctx, 200, ContractDao.unApproveContract(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list contract payment by contract id
    public static void getListContractPaymentbyContractId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer contractId = request.getParam(CONTRACT_ID) == null ? 0
                        : Integer.parseInt(request.getParam(CONTRACT_ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTRACT_ID, contractId);
                sendResponse(ctx, 200, ContractDao.getListContractPaymentbyContractId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // insert and update Contract Payment Gateways
    public static void InsertUpdateContractPaymentGateways(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                mIn.put(CONTRACT_ID, body.getString(CONTRACT_ID) == null ? BLANK : body.getString(CONTRACT_ID));
                mIn.put(MERCHANT_NAME, body.getString(MERCHANT_NAME) == null ? BLANK : body.getString(MERCHANT_NAME));
                mIn.put(PAYGATE, body.getString(PAYGATE) == null ? BLANK : body.getString(PAYGATE));
                mIn.put(ADVANCE_ACCOUNT,
                        body.getString(ADVANCE_ACCOUNT) == null ? BLANK : body.getString(ADVANCE_ACCOUNT));
                mIn.put(ADVANCE_ACCOUNT_NAME,
                        body.getString(ADVANCE_ACCOUNT_NAME) == null ? BLANK : body.getString(ADVANCE_ACCOUNT_NAME));
                mIn.put(ADVANCE_ACCOUNT_BRANCH, body.getString(ADVANCE_ACCOUNT_BRANCH) == null ? BLANK
                        : body.getString(ADVANCE_ACCOUNT_BRANCH));
                mIn.put(CARD_TYPE, body.getString(CARD_TYPE) == null ? BLANK : body.getString(CARD_TYPE));
                mIn.put(FEE, body.getString(FEE) == null ? BLANK : body.getString(FEE));
                mIn.put(CURRENCY, body.getString(CURRENCY) == null ? BLANK : body.getString(CURRENCY));
                sendResponse(ctx, 200, ContractDao.InsertUpdateContractPaymentGateways(mIn));
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "UPDATE Acceptance ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteContractPayment(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String contractPaymentId = request.getParam(ID) == null ? BLANK : request.getParam(ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(CONTRACT_PAYMENT_ID, contractPaymentId);
                sendResponse(ctx, 200, ContractDao.deleteContractPayment(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // ==================================== TIENNV ====================================

    public static void getContractListByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNERID) == null ? 0 : Integer.parseInt(request.getParam(PARTNERID));
                Integer userId = ctx.get(N_USER_ID);
                logger.info("userId: " + userId);
                logger.info("partnerId: " + partnerId);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);
                
                Map<String, Object> mIn = new HashMap<>();
                List<ContractOriginal> list = ContractDao.getContractListByPartnerId(partnerId);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        List<ContractOriginal> subContractList = ContractDao.getSubContractListByParentId(list.get(i).getContractId());
                        list.get(i).setSubContractList(subContractList);
                    }
                }
                mIn.put("list", list);
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getContractDetailById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int contractId = Integer.parseInt(ctx.request().getParam("contractId"));
                Map<String, Object> mIn = new HashMap<>();
                ContractOriginal contract = ContractDao.getContractDetailById(contractId);

                
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartnerId(), userId);

                mIn.put("contract", contract);
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getInfoFeeInstallment(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, ContractDao.getInfoFeeInstallmentList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateFeeInstallment(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String body = ctx.getBodyAsString();
                List<UpdateFeeInstallmentReq> requests = gson.fromJson(body, new TypeToken<List<UpdateFeeInstallmentReq>>() {}.getType());
                if (CollectionUtils.isEmpty(requests)) {
                    logger.severe(() -> "INVALID REQUEST");
                    throw IErrors.VALIDATION_ERROR;
                }

                int nResult = ContractDao.updateFeeInstallment(requests);
                Map<String, Object> res = new HashMap<>();
                if (nResult == 200) {
                    res.put("status", "SUCCESS");
                } else {
                    res.put("status", "FAIL");
                }
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveContractOriginal(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                int id = Integer.parseInt(bodyJson.getString("id"));
                
                ContractOriginal contract = ContractDao.getContractDetailById(id);
                
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartnerId(), userId);

                Integer nResult = 200;
                Map<String, Object> mInApprove = new HashMap<>();

                nResult = ContractDao.approveContractOriginal(id);
                mInApprove.put("n_result", nResult);

                sendResponse(ctx, nResult == 200 ? 200 : 500, mInApprove);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Hủy duyệt hợp đồng
     */
    public static void removeApproveContractOriginal(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                int id = Integer.parseInt(bodyJson.getString("id"));
                String email = bodyJson.getString("email");

                ContractOriginal contract = ContractDao.getContractDetailById(id);
                
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartnerId(), userId);

                Integer nResult = 200;
                Map<String, Object> mIn = new HashMap<>();

                nResult = ContractDao.removeApproveContractOriginal(id);
                mIn.put("n_result", nResult);

                // insert log hủy duyệt hợp đồng
                if (nResult == 200) {
                    Map<String, Object> result = ContractDao.insertLogActionContract(id, 0, email, "cancel");
                    if (result.get("nError").equals(200)) {
                        sendResponse(ctx, 200, mIn);
                    } else {
                        mIn.put("n_result", 201);
                        sendResponse(ctx, 201, mIn);
                    }
                } else {
                    sendResponse(ctx, 500, mIn);
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                int id = Integer.parseInt(bodyJson.getString("id"));

                ContractOriginal contract = ContractDao.getContractDetailById(id);
                
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartnerId(), userId);
                
                Integer nResult = 200;
                Map<String, Object> mIn = new HashMap<>();

                nResult = ContractDao.deleteContract(id);
                mIn.put("n_result", nResult);

                sendResponse(ctx, nResult == 200 ? 200 : 500, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static String handleTemplateHtml(ContractOriginal contractOriginal) throws IOException {
        String contractDetail = "";
        String templateHtml = "";
        contractDetail = contractDetail + contractOriginal.getDataContractDetail();
        JSONObject jsonObject = new JSONObject(contractDetail);
        String templateFolder = Config.getString("file.contract_template_folder", "/opt/iportal-service/classes/templates/contract_template");
        templateHtml = Files.asCharSource(new File(templateFolder + "/" + contractOriginal.getContractCode() + "/" + contractOriginal.getContractCode() + "-Utils" + ".html"), StandardCharsets.UTF_8).read();

        String cardListString = "";
        String careerTxt = "";
        String monthFee = "";
        String registerFee = "";
        String feeTransInternationalTbl = "";
        String feeTransDomesticAndAppTbl = "";
        String feeTransAppTbl = "";
        String approveCardType01InternationalTbl = "";
        String approveCardType02InternationalTbl = "";
        String americanExpress01InternationalTbl = "";
        String americanExpress02InternationalTbl = "";
        String feePaymentDomesticAndAppTbl = "";
        String feePercentQrMobileTbl = "";
        String feePercentQrGrabTbl = "";
        String feePercentQrShopeeTbl = "";
        String feePercentQrZaloTbl = "";
        String feePercentQrMoMoTbl = "";
        String feePercentQrOtherTbl = "";
        String feeVietQRTbl = "";
        String percentVietQRTbl = "";


        String tgTamUngSelection = "";
        String inputTgTamUngKhac = "";
        String inputTgTamUngKhacKetThucPhien = "";
        JSONArray subTableMerchant = new JSONArray();
        String merchantnamesTblOne = "";
        String merchantIdTblOne = "";
        String accountNumberTblOne = "";
        String accountnamesTblOne = "";
        String bankTblOne = "";
        String hinhThucThuPhi = "";
        String khoanDamBaoSelection = "";
        String kyQuyType = "";
        String dayApprove = "";
        String thuPhiKhacTxt = "";
        String accountNumberOther = "";
        String accountNumber = "";

        accountNumberOther += jsonObject.get("accountNumberOther").toString();
        accountNumber += jsonObject.get("accountNumber").toString();
        thuPhiKhacTxt += jsonObject.get("inputHinhThucThuPhiKhac").toString();
        cardListString = cardListString + jsonObject.get("cardList").toString();
        careerTxt = careerTxt + jsonObject.get("carrer").toString();
        dayApprove = dayApprove + jsonObject.get("dayApprove").toString();

        monthFee = monthFee + jsonObject.get("monthFee").toString();
        registerFee = registerFee + jsonObject.get("registerFee").toString();
        feeTransInternationalTbl = feeTransInternationalTbl + (jsonObject.get("feeTransInternational") != null ? jsonObject.get("feeTransInternational").toString() : "");
        feeTransDomesticAndAppTbl = feeTransDomesticAndAppTbl + (jsonObject.get("feeTransDomesticAndApp") != null ? jsonObject.get("feeTransDomesticAndApp").toString() : "");
        feeTransAppTbl = feeTransAppTbl + jsonObject.get("feeApp").toString();
        approveCardType01InternationalTbl = approveCardType01InternationalTbl + jsonObject.get("approveCardType01International").toString();
        approveCardType02InternationalTbl = approveCardType02InternationalTbl + jsonObject.get("approveCardType02International").toString();
        americanExpress01InternationalTbl = americanExpress01InternationalTbl + jsonObject.get("americanExpress01International").toString();
        americanExpress02InternationalTbl = americanExpress02InternationalTbl + jsonObject.get("americanExpress02International").toString();
        feePaymentDomesticAndAppTbl = feePaymentDomesticAndAppTbl + jsonObject.get("feePaymentDomesticAndApp").toString();
        feePercentQrMobileTbl = feePercentQrMobileTbl + jsonObject.get("percentQrMobile").toString();
        feePercentQrGrabTbl = feePercentQrGrabTbl + jsonObject.get("percentQrGrab").toString();
        feePercentQrShopeeTbl = feePercentQrShopeeTbl + jsonObject.get("percentQrShopee").toString();
        feePercentQrZaloTbl = feePercentQrZaloTbl + jsonObject.get("percentQrZalo").toString();
        feePercentQrMoMoTbl = feePercentQrMoMoTbl + jsonObject.get("percentQrMoMo").toString();
        feePercentQrOtherTbl = feePercentQrOtherTbl + jsonObject.get("percentQrOther").toString();
        feeVietQRTbl = feeVietQRTbl + jsonObject.get("feeVietQR").toString();
        percentVietQRTbl = percentVietQRTbl + jsonObject.get("percentVietQR").toString();

        tgTamUngSelection = tgTamUngSelection + jsonObject.get("tgTamUngSelection").toString();
        inputTgTamUngKhac = inputTgTamUngKhac + jsonObject.get("inputTgTamUngKhac").toString();
        inputTgTamUngKhacKetThucPhien = inputTgTamUngKhacKetThucPhien + jsonObject.get("inputTgTamUngKhacKetThucPhien").toString();
        hinhThucThuPhi = hinhThucThuPhi + jsonObject.get("hinhThucThuPhi").toString();
        khoanDamBaoSelection = khoanDamBaoSelection + jsonObject.get("khoanDamBaoSelection").toString();
        kyQuyType = kyQuyType + jsonObject.get("kyQuyType").toString();


        subTableMerchant = (JSONArray) jsonObject.get("subTableMerchant");
        if (subTableMerchant.length() != 0) {
            JSONObject tableMerchantIndex0 = subTableMerchant.getJSONObject(0);
            merchantnamesTblOne = merchantnamesTblOne + tableMerchantIndex0.get("merchant").toString();
            merchantIdTblOne = merchantIdTblOne + tableMerchantIndex0.get("merchantIDS").toString();
            accountNumberTblOne = accountNumberTblOne + tableMerchantIndex0.get("accountNumber").toString();
            accountnamesTblOne = accountnamesTblOne + tableMerchantIndex0.get("accountName").toString();
            bankTblOne = bankTblOne + tableMerchantIndex0.get("bank").toString();
        }


        int countDieu = 1;



        // xu ly dieu 1
        if (!cardListString.contains("MasterCard") && !cardListString.contains("Visa") && !cardListString.contains("UnionPay") && !cardListString.contains("ApproveOnepayDomesticCard") && !cardListString.contains("JCB") && !cardListString.contains("ApproveDomesticCard") && !cardListString.contains("ApproveInternationalCard") && !cardListString.contains("ApproveOnepayMobileApp") && !cardListString.contains("AmericanExpress")) {
            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu1Txt", "");
            templateHtml = ContractUtils.replaceVariable(templateHtml, "danhSachNganHangTxt", "");

        } else {
            String danhSachBank = "";
            danhSachBank = Files.asCharSource(new File(templateFolder + "/PL-K/" + "dsBank.html"), StandardCharsets.UTF_8).read();
            templateHtml = ContractUtils.replaceVariable(templateHtml, "danhSachNganHangTxt", danhSachBank);
            String dieu1Txt = "";
            if (cardListString.contains("ApproveDomesticCard") || cardListString.contains("ApproveInternationalCard") || cardListString.contains("MasterCard") || cardListString.contains("Visa") || cardListString.contains("UnionPay") || cardListString.contains("ApproveOnepayDomesticCard") || cardListString.contains("JCB") || cardListString.contains("ApproveOnepayMobileApp") || cardListString.contains("AmericanExpress")) {
                dieu1Txt = dieu1Txt + "<h2>Điều " + countDieu + ". Nội dung hợp tác</h2><p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;margin-left:0in;text-align:justify'><span style='line-height:107%;color:black'>ĐVCNTT đồng ý sử dụng dịch vụ Cổng thanh toán điện tử và dịch vụ Hỗ trợ thu hộ, chi hộ do OnePay cung cấp để cho phép Khách hàng thực hiện thanh toán trực tuyến với các nội dung cụ thể sau:</span></p><p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><spanstyle='color:black'>1.1.      Loại thẻ và ứng dụng di động chấp nhận thanh toán trực tuyến</span></b></p><p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><spanstyle='color:black'>1.1.1.   Thẻ quốc tế</span></p><p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;vertical-align:baseline'><span lang=PT-BR style='color:black'>visaBox </span><span style='color:black'>Visa; </span><span lang=PT-BRstyle='color:black'>masterCardBox </span><span style='color:black'>MasterCard; </span><spanlang=PT-BR style='color:black'>jcbBox </span><span style='color:black'>JCB; </span><spanlang=PT-BR style='color:black'>unionBox </span><span style='color:black'>UnionPay;</span><span lang=PT-BR style='color:black'>americanExpressBox </span><spanstyle='color:black'>American Express</span></p><p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><spanstyle='color:black'>            Phạm vi chấp nhận của Thẻ quốc tế:</span></p><p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;vertical-align:baseline'><span style='color:black'>approveDomesticCardBox Chấp nhận Thẻ quốc tế phát hành trong nước</span></p><p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;text-align:justify;vertical-align:baseline'><span style='color:black'>approveInternationalCardBox Chấp nhận Thẻ quốc tế phát hành ở nước ngoài</span></p>";
                if (cardListString.contains("ApproveOnepayDomesticCard") && cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Thẻ nội địa&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayDomesticCardBox Thẻ nội địa được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.   Ứng dụng di động&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayMobileAppBox Ứng dụng di động được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.6.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.7.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (cardListString.contains("ApproveOnepayDomesticCard") && !cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Thẻ nội địa&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayDomesticCardBox Thẻ nội địa được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.6.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (!cardListString.contains("ApproveOnepayDomesticCard") && cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Ứng dụng di động&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayMobileAppBox Ứng dụng di động được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.6.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (!cardListString.contains("ApproveOnepayDomesticCard") && !cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (!careerTxt.equals("")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>1.2.      Lĩnh vực kinh doanh của ĐVCNTT</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   ĐVCNTT cam kết chỉ sử dụng dịch vụ cổng thanh toán điện tử OnePay cho lĩnh vực kinh doanh sau: careerTxt.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Trong trường hợp có sự thay đổi, bổ sung lĩnh vực kinh doanh kết nối với OnePay, ĐVCNTT thực hiện ký bổ sung Phụ lục/ Hợp đồng hợp tác với OnePay. </span><span lang=PT-BR>Điều</span><span lang=PT-BR> </span>2. Phí dịch vụ</p> ";
                }
                countDieu++;
            } else {
                dieu1Txt = dieu1Txt + "<h2>Điều " + countDieu + ". Nội dung hợp tác</h2> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:0in;text-align:justify'><span style='line-height:107%;color:black'>ĐVCNTT đồng ý sử dụng dịch vụ Cổng thanh toán điện tử và dịch vụ Hỗ trợ thu hộ, chi hộ do OnePay cung cấp để cho phép Khách hàng thực hiện thanh toán trực tuyến với các nội dung cụ thể sau:</span></p> ";
                if (cardListString.contains("ApproveOnepayDomesticCard") && cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.1.   Thẻ nội địa&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayDomesticCardBox Thẻ nội địa được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Ứng dụng di động&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayMobileAppBox Ứng dụng di động được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.6.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (cardListString.contains("ApproveOnepayDomesticCard") && !cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.1.   Thẻ nội địa&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayDomesticCardBox Thẻ nội địa được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (!cardListString.contains("ApproveOnepayDomesticCard") && cardListString.contains("ApproveOnepayMobileApp")) {
                    dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.1.   Ứng dụng di động&nbsp;</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>approveOnepayMobileAppBox Ứng dụng di động được OnePay chấp nhận thanh toán</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.2.   Danh sách chi tiết Thẻ nội địa và Ứng dụng di động được thể hiện ở cuối phụ lục này. </span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.3.</span>   <span style='color:black'>OnePay sẽ là đầu mối phối hợp với các Đối tác thanh toán di động để đăng ký thông tin và phản hồi về tình trạng chấp nhận hoặc từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.4.   OnePay có quyền từ chối cung cấp dịch vụ cho ĐVCNTT trong trường hợp Đối tác thanh toán di động tạm ngưng, từ chối cung cấp dịch vụ cho ĐVCNTT.</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>1.1.5.   Đối với các loại Thẻ nội địa và Ứng dụng di động mà OnePay kết nối sau này OnePay sẽ thông báo cho ĐVCNTT trước 03 (ba) ngày mà không cần ký kết lại Hợp đồng.</span></p> ";
                }
                if (!careerTxt.equals("")) {
                    if (!(!cardListString.contains("ApproveDomesticCard") && !cardListString.contains("ApproveInternationalCard"))) {
                        dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>1.2.      Lĩnh vực kinh doanh của ĐVCNTT</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   ĐVCNTT cam kết chỉ sử dụng dịch vụ cổng thanh toán điện tử OnePay cho lĩnh vực kinh doanh sau: careerTxt.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Trong trường hợp có sự thay đổi, bổ sung lĩnh vực kinh doanh kết nối với OnePay, ĐVCNTT thực hiện ký bổ sung Phụ lục/ Hợp đồng hợp tác với OnePay. </span><span lang=PT-BR>Điều</span><span lang=PT-BR> </span>2. Phí dịch vụ</p> ";
                    } else {
                        dieu1Txt = dieu1Txt + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>1.1.      Lĩnh vực kinh doanh của ĐVCNTT</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   ĐVCNTT cam kết chỉ sử dụng dịch vụ cổng thanh toán điện tử OnePay cho lĩnh vực kinh doanh sau: careerTxt.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Trong trường hợp có sự thay đổi, bổ sung lĩnh vực kinh doanh kết nối với OnePay, ĐVCNTT thực hiện ký bổ sung Phụ lục/ Hợp đồng hợp tác với OnePay. </span><span lang=PT-BR>Điều</span><span lang=PT-BR> </span>2. Phí dịch vụ</p> ";
                    }
                }
                countDieu++;

            }

            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu1Txt", dieu1Txt);

        }

        // xu ly dieu 2
        if (monthFee.equals("") && registerFee.equals("") && feeTransInternationalTbl.equals("") && feeTransDomesticAndAppTbl.equals("")
                && feeTransAppTbl.equals("") && approveCardType01InternationalTbl.equals("") && approveCardType02InternationalTbl.equals("") && americanExpress01InternationalTbl.equals("")
                && americanExpress02InternationalTbl.equals("") && feePaymentDomesticAndAppTbl.equals("") && feePercentQrMobileTbl.equals("")
                && feePercentQrGrabTbl.equals("") && feePercentQrShopeeTbl.equals("") && feePercentQrZaloTbl.equals("") && feePercentQrMoMoTbl.equals("") && feePercentQrOtherTbl.equals("")) {
            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu2Txt", "");
            // templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu4Txt", "");
        } else {
            int sttTable = 1;
            String dieu2Txt = "<h2>Điều " + countDieu + ". Phí dịch vụ</h2> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:0in; text-align:justify;vertical-align:baseline'><b><span style='color:black'>&nbsp;</span></b></p> <table class=MsoNormalTable border=1 cellspacing=0 cellpadding=0 width=618  style='border-collapse:collapse;border:none'>  <tr style='height:22.9pt'>   <td width=48 style='width:35.75pt;border:solid black 1.0pt;background:#00529C;   padding:0in 5.4pt 0in 5.4pt;height:22.9pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><a name=\"_Hlk89273966\"><b><span style='font-size:10.0pt;   line-height:107%;color:white'>STT</span></b></a></p>   </td>   <td width=246 style='width:184.5pt;border:solid black 1.0pt;border-left:none;   background:#00529C;padding:0in 5.4pt 0in 5.4pt;height:22.9pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><b><span style='font-size:10.0pt;line-height:107%;color:white'>Tên   phí</span></b></p>   </td>   <td width=324 colspan=2 style='width:243.0pt;border:solid black 1.0pt;   border-left:none;background:#00529C;padding:0in 5.4pt 0in 5.4pt;height:22.9pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><b><span style='font-size:10.0pt;line-height:107%;color:white'>Mức   phí</span></b></p>   </td>  </tr>";
            if (!registerFee.equals("")) {
                dieu2Txt = dieu2Txt + " <tr style='height:23.35pt'>  <td width=48 style='width:35.75pt;border:solid black 1.0pt;  padding:0in 5.4pt 0in 5.4pt;height:23.35pt'>  <p  align=center style='margin-bottom:0in;text-align:center;  border:none'><b><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>" + sttTable + "</span></b></p>  </td>  <td width=246 style='width:184.5pt;border-left:none;  border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt;  height:23.35pt'>  <p  style='margin-bottom:0in;border:none'><b><span  style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>Phí đăng ký dịch vụ</span></b></p>  </td>  <td width=324 colspan=2 style='width:243.0pt;border-left:  none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;  padding:0in 5.4pt 0in 5.4pt;height:23.35pt'>  <p  align=center style='margin-bottom:0in;text-align:center;  border:none'><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>registerFeeTbl</span></p>  </td> </tr>";
                sttTable++;
            }
            if (!monthFee.equals("")) {
                dieu2Txt = dieu2Txt + " <tr style='height:23.35pt'>   <td width=48 style='width:35.75pt;border:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;height:23.35pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><b><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>" + sttTable + "</span></b></p>   </td>   <td width=246 style='width:184.5pt;border-left:none;   border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt;   height:23.35pt'>   <p  style='margin-bottom:0in;border:none'><b><span   style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>Phí duy trì dịch vụ hàng   tháng</span></b></p>   </td>   <td width=324 colspan=2 style='width:243.0pt;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;height:23.35pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>monthFeeTbl</span></p>   </td>  </tr>";
                sttTable++;
            }
            if (!feeTransInternationalTbl.equals("") || !feeTransDomesticAndAppTbl.equals("") || !feeTransAppTbl.equals("")) {
                String row1 = "<tr><td width=48 rowspan=2 valign=center style='width:35.75pt;border:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;'>   <p align=center ><b><span   style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>" + sttTable + "</span></b></p>   </td><td class='table__td' style='border: none;border-top: solid black 1.0pt'><p><b><span class='table__span' style='font-size: 10.0pt'>Phí xử lý giao dịch</span></b></p></td>   <td colspan=2  style='border-bottom: none;border-top: solid black 1.0pt'></td>  </tr>";
                String nameField = "";
                String valueField = "";
                if (!feeTransInternationalTbl.equals("")) {
                    nameField += "<p class='table__p'><span class='table__span'>Thẻ quốc tế</span></p>";
                    valueField += "<p align=center class='table__p'><span class='table__span'>feeTransInternationalTbl</span></p>";
                }
                if (!feeTransDomesticAndAppTbl.equals("")) {
                    nameField += "<p class='table__p'><span class='table__span'>Thẻ nội địa</span></p>";
                    valueField += "<p align=center class='table__p'><span class='table__span'>feeTransDomesticAndAppTbl</span></p>";
                }
                if (!feeTransAppTbl.equals("")) {
                    nameField += "<p class='table__p'><span class='table__span'>Ứng dụng di động</span></p>";
                    valueField += "<p align=center class='table__p'><span class='table__span'>feeTransAppTbl</span></p>";
                }

                String row2 = "<tr>" + "<td class='table__td' style='border-top: none' >" + nameField + "</td>" + "<td colspan=2 style='border-top: none' class='table__td'>" + valueField + "</td>" + "</tr>";
                String muc3Table = row1 + row2;

                dieu2Txt = dieu2Txt + muc3Table;
                sttTable++;
            }
            if (!approveCardType01InternationalTbl.equals("") || !americanExpress01InternationalTbl.equals("") || !approveCardType02InternationalTbl.equals("")
                    || !americanExpress02InternationalTbl.equals("") || !feePaymentDomesticAndAppTbl.equals("") || !feePercentQrMobileTbl.equals("")
                    || !feePercentQrGrabTbl.equals("") || !feePercentQrShopeeTbl.equals("") || !feePercentQrZaloTbl.equals("") || !feePercentQrMoMoTbl.equals("") || !feePercentQrOtherTbl.equals("")) {
                String muc4Table = " <tr style='height:24.25pt'><td width=48 rowspan=5 valign=center style='width:35.75pt;border:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;height:24.25pt'>   <p  align=center style='margin-top:5.4pt;text-align:center;   border:none'><b><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>" + sttTable + "</span></b></p>   </td>   <td width=570 colspan=3 style='width:427.5pt;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;height:24.25pt'>   <p  style='margin-bottom:0in;border:none'><b><span   style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>Phí thanh toán </span></b><span   style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>(% giá trị giao dịch)</span></p>   </td>  </tr>";
                String theQuocte = "";
                String theNoiDia = "";
                String ungDungDiDong = "";
                if (!approveCardType01InternationalTbl.equals("") || !americanExpress01InternationalTbl.equals("") || !approveCardType02InternationalTbl.equals("") || !americanExpress02InternationalTbl.equals("")) {

                    if ((!approveCardType01InternationalTbl.equals("") && !approveCardType02InternationalTbl.equals("") && americanExpress01InternationalTbl.equals("") && americanExpress02InternationalTbl.equals(""))
                            || (!approveCardType01InternationalTbl.equals("") && approveCardType02InternationalTbl.equals("") && americanExpress01InternationalTbl.equals("") && americanExpress02InternationalTbl.equals(""))
                            || (approveCardType01InternationalTbl.equals("") && !approveCardType02InternationalTbl.equals("") && americanExpress01InternationalTbl.equals("") && americanExpress02InternationalTbl.equals(""))) {
                        // case hien hang 1
                        theQuocte = theQuocte + "<tr>" + " <td class='table__td'><p class='table__p'><span class='table__span'>Thẻ quốc tế</span></p><p class='table__p'><span class='table__span'>- Visa, MasterCard, JCB, UnionPay</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại Việt Nam</span></p><p class='table__p' align=center><span class='table__span'>approveCardType01InternationalTbl</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại nước ngoài</span></p><p class='table__p' align=center><span class='table__span'>approveCardType02InternationalTbl</span></p></td>";
                        theQuocte += "</tr>";
                    } else if ((approveCardType01InternationalTbl.equals("") && approveCardType02InternationalTbl.equals("") && !americanExpress01InternationalTbl.equals("") && !americanExpress02InternationalTbl.equals(""))
                            || (approveCardType01InternationalTbl.equals("") && approveCardType02InternationalTbl.equals("") && americanExpress01InternationalTbl.equals("") && !americanExpress02InternationalTbl.equals(""))
                            || (approveCardType01InternationalTbl.equals("") && approveCardType02InternationalTbl.equals("") && !americanExpress01InternationalTbl.equals("") && americanExpress02InternationalTbl.equals(""))) {
                        // case hien hang 2
                        theQuocte = theQuocte + "<tr>" + " <td class='table__td'><p class='table__p'><span class='table__span'>Thẻ quốc tế</span></p><p class='table__p'><span class='table__span'>- American Express</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại Việt Nam</span></p><p class='table__p' align=center><span class='table__span'>americanExpress01InternationalTbl</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại nước ngoài</span></p><p class='table__p' align=center><span class='table__span'>americanExpress02InternationalTbl</span></p></td>";
                        theQuocte += "</tr>";
                    } else {
                        // case hien 2 hang
                        theQuocte = theQuocte + "<tr>" + " <td class='table__td'><p class='table__p'><span class='table__span'>Thẻ quốc tế</span></p><p class='table__p'><span class='table__span'>- Visa, MasterCard, JCB, UnionPay</span></p><p class='table__p'><span class='table__span'>- American Express</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại Việt Nam</span></p><p class='table__p' align=center><span class='table__span'>approveCardType01InternationalTbl</span></p><p class='table__p' align=center><span class='table__span'>americanExpress01InternationalTbl</span></p></td>";
                        theQuocte = theQuocte + "<td class='table__td'><p align=center class='table__p'><span class='table__span'>Phát hành tại nước ngoài</span></p><p class='table__p' align=center><span class='table__span'>approveCardType02InternationalTbl</span></p><p class='table__p' align=center><span class='table__span'>americanExpress02InternationalTbl</span></p></td>";
                        theQuocte += "</tr>";
                    }
                    muc4Table = muc4Table + theQuocte;

                }
                if (!feePaymentDomesticAndAppTbl.equals("")) {
                    theNoiDia = theNoiDia + " <tr style='height:24.25pt'>   <td width=246 style='width:184.5pt;border-left:none;   border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt;   height:24.25pt'>   <p  style='margin-bottom:0in;line-height:115%;border:none'><span   style='font-size:10.0pt;line-height:115%;color:#0D0D0D'>Thẻ nội địa</span></p>   </td>   <td width=324 colspan=2 style='width:243.0pt;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt;height:24.25pt'>   <p  align=center style='margin-bottom:0in;text-align:center;   border:none'><span style='font-size:10.0pt;line-height:107%;color:#0D0D0D'>feePaymentDomesticAndAppTbl</span></p>   </td>  </tr> ";
                    muc4Table = muc4Table + theNoiDia;
                }
                if (!feePercentQrMobileTbl.equals("") || !feePercentQrGrabTbl.equals("") || !feePercentQrShopeeTbl.equals("") || !feePercentQrZaloTbl.equals("") || !feePercentQrMoMoTbl.equals("") || !feePercentQrOtherTbl.equals("")) {
                    ungDungDiDong = ungDungDiDong + " <tr>  <td style='padding: 8.9pt 5.4pt 5.4pt 5.4pt; border-bottom: none;border-right: none'><p><span style='margin-top: 3pt; margin-bottom: 3pt; lineheight: 115%; font-size: 10.0pt'>Ứng dụng di động</span></p></td>   <td colspan=2 style='border-bottom: none;'></td>  </tr>";
                    ungDungDiDong = ungDungDiDong + " <tr>";
                    String leftTxt = "<td width=246 style='width:184.5pt;border-left:none;   border-bottom:solid black 1.0pt;border-right:solid black 1.0pt; padding: 6pt;border-top:none'>";
                    String rightTxt = "  <td width=324 colspan=2 style='width:243.0pt;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt; padding: 6pt;border-top:none'>";
                    if (!feePercentQrMobileTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- Ứng dụng Mobile banking</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrMobileTbl</span></p> ";
                    }
                    if (!feePercentQrGrabTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- GrabPay by Moca</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrGrabTbl</span></p> ";
                    }
                    if (!feePercentQrShopeeTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- ShopeePay</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrShopeeTbl</span></p> ";

                    }
                    if (!feePercentQrZaloTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- ZaloPay</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrZaloTbl</span></p> ";
                    }
                    if (!feePercentQrMoMoTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- MoMo</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrMoMoTbl</span></p> ";
                    }
                    if (!feePercentQrOtherTbl.equals("")) {
                        leftTxt = leftTxt + "  <p class='table__p'><span class='table__p__span'>- Ví điện tử và các ứng dụng còn lại</span></p> ";
                        rightTxt = rightTxt + "  <p class='table__p' align=center><span class='table__p__span'>feePercentQrOtherTbl</span></p> ";
                    }
                    ungDungDiDong = ungDungDiDong + leftTxt + "</td>" + rightTxt + "</td>" + "</tr>";
                    muc4Table = muc4Table + ungDungDiDong;

                }
                dieu2Txt = dieu2Txt + muc4Table;


            }
            countDieu++;
            dieu2Txt = dieu2Txt + "</table> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>a)    Mức phí trên đã bao gồm 10% VAT.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='color:black'>b)    Mức phí trên chỉ áp dụng cho lĩnh vực kinh doanh: careerTxt đã được ĐVCNTT đăng ký với OnePay.</span></p> ";
            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu2Txt", dieu2Txt);

        }

        // xu ly dieu 3
        if (tgTamUngSelection.equals("") && monthFee.equals("") && hinhThucThuPhi.equals("") && subTableMerchant.length() == 0 && khoanDamBaoSelection.equals("") && kyQuyType.equals("")) {
            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu3Txt", "");
        } else {
            String dieu3Txt = "<h2><span style='color:black'>Điều " + countDieu + ". Thanh toán tạm ứng</span></h2>";
            int index = 1;
            String textThreeOne = "";
            String textThreeTwo = "";
            String textThreeThree = "";
            String textThreeFour = "";
            String textThreeFive = "";
            int indexThree = 1;
            // xu ly 3.1
            if (tgTamUngSelection.equals("")) {
                dieu3Txt = dieu3Txt + textThreeOne;
            } else {
                if (tgTamUngSelection.equals("t1")) {
                    textThreeOne = textThreeOne + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời gian thực hiện thanh toán tạm ứng</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Đối với các giao dịch được thực hiện trước giờ kết thúc phiên giao dịch trên hệ thống OnePay: OnePay sẽ ghi có tài khoản của ĐVCNTT sau <span style=''>01 </span> <span style=''>(một)</span> ngày làm việc. </span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Đối với các giao dịch được thực hiện sau giờ kết thúc phiên giao dịch trên hệ thống OnePay: OnePay sẽ ghi có tài khoản của ĐVCNTT sau <span style=''>02 (hai)</span> ngày làm việc.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>c)   Giờ kết thúc phiên giao dịch:</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:31.5pt; text-align:justify;text-indent:22.5pt;vertical-align:baseline'><span style='color:black'>- Thẻ quốc tế: 17h00.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.35in; text-align:justify;text-indent:.4in;vertical-align:baseline'><span style='color:black'>- Thẻ nội địa và Ứng dụng di động: 23h59.</span></p>";
                }
                if (tgTamUngSelection.equals("t2")) {
                    textThreeOne = textThreeOne + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời gian thực hiện thanh toán tạm ứng</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Đối với các giao dịch được thực hiện trước giờ kết thúc phiên giao dịch trên hệ thống OnePay: OnePay sẽ ghi có tài khoản của ĐVCNTT sau <span style=''>02 </span> <span style=''>(hai)</span> ngày làm việc. </span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Đối với các giao dịch được thực hiện sau giờ kết thúc phiên giao dịch trên hệ thống OnePay: OnePay sẽ ghi có tài khoản của ĐVCNTT sau <span style=''>03 (ba)</span> ngày làm việc.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>c)   Giờ kết thúc phiên giao dịch:</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:31.5pt; text-align:justify;text-indent:22.5pt;vertical-align:baseline'><span style='color:black'>- Thẻ quốc tế: 17h00.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.35in; text-align:justify;text-indent:.4in;vertical-align:baseline'><span style='color:black'>- Thẻ nội địa và Ứng dụng di động: 23h59.</span></p>";
                }
                if (tgTamUngSelection.equals("other")) {
                    textThreeOne = textThreeOne + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời gian thực hiện thanh toán tạm ứng</span></b></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Đối với các giao dịch được thực hiện trước giờ kết thúc phiên giao dịch trên hệ thống OnePay: inputTgTamUngKhacTxt</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Đối với các giao dịch được thực hiện sau giờ kết thúc phiên giao dịch trên hệ thống OnePay: inputTgTamUngKhacKetThucPhienTxt</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in; text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>c)   Giờ kết thúc phiên giao dịch:</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:31.5pt; text-align:justify;text-indent:22.5pt;vertical-align:baseline'><span style='color:black'>- Thẻ quốc tế: 17h00.</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.35in; text-align:justify;text-indent:.4in;vertical-align:baseline'><span style='color:black'>- Thẻ nội địa và Ứng dụng di động: 23h59.</span></p>";
                }
                dieu3Txt = dieu3Txt + textThreeOne;
                index++;
            }
            // xu ly 3.2
            if (hinhThucThuPhi.equals("")) {
                dieu3Txt = dieu3Txt + textThreeTwo;
            } else {

                textThreeTwo = textThreeTwo + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Hình thức OnePay thu phí từ ĐVCNTT</span></b></p>";
                // if(!monthFee.equals("")){
                // textThreeTwo = textThreeTwo + "<p style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
                // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
                // style='color:black'>"+countDieu+"."+ index+"."+indexThree+"   Với phí duy trì dịch vụ hàng
                // tháng</span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;
                // text-align:justify;vertical-align:baseline'><span style='font-family:\"MS Gothic\";
                // color:black;'>☒</span><span style='color:black'> Thu phí theo quy định cụ thể tại Mục 4.7 tại Phụ
                // lục này</span></p>";
                // indexThree++;
                // }
                if (hinhThucThuPhi.equals("ThuPhiCungButToanBaoCo")) {
                    // ẩn mục 4.7 4.8
                    textThreeTwo = textThreeTwo + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black;background:white'>" + countDieu + "." + index + ".1.   Với phí xử lý giao dịch và phí thanh toán</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☒</span><span style='color:black; background:white'> Thu phí cùng bút toán báo có</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black'> <span style='background:white'>Thu phí theo tháng</span></span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal'><span style='font-family: \"MS Gothic\"'>☐</span> <span style='background:white'>Khác</span></p><p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='background:white'>&nbsp;</span></p> ";
                }
                if (hinhThucThuPhi.equals("ThuTheoThang")) {
                    // hien muc 4.7 4.8
                    textThreeTwo = textThreeTwo + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black;background:white'>" + countDieu + "." + index + ".1.   Với phí xử lý giao dịch và phí thanh toán</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black; background:white'> Thu phí cùng bút toán báo có</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☒</span><span style='color:black'> <span style='background:white'>Thu phí theo tháng</span></span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal'><span style='font-family: \"MS Gothic\"'>☐</span> <span style='background:white'>Khác</span></p><p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='background:white'>&nbsp;</span></p> ";
                }
                if (hinhThucThuPhi.equals("Khac")) {
                    textThreeTwo = textThreeTwo + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black;background:white'>3.2.1.   Với phí xử lý giao dịch và phí thanh toán</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black; background:white'> Thu phí cùng bút toán báo có</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black'> <span style='background:white'>Thu phí theo tháng</span></span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal'><span style='font-family: \"MS Gothic\"'>☒</span> <span style='background:white'>Khác</span></p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal'>“" + thuPhiKhacTxt + "”</p> <p  style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;line-height:normal;border:none'><span style='background:white'>&nbsp;</span></p> ";
                }
                dieu3Txt = dieu3Txt + textThreeTwo;
                index++;
            }

            // xu ly 3.3
            if (merchantnamesTblOne.equals("") && merchantIdTblOne.equals("") && accountNumberTblOne.equals("") && accountnamesTblOne.equals("") && bankTblOne.equals("")) {
                dieu3Txt = dieu3Txt + textThreeThree;
            } else {
                textThreeThree = textThreeThree + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Tài khoản nhận thanh toán tạm ứng của ĐVCNTT</span></b></p>";
                textThreeThree = textThreeThree + "<table class=MsoTableGrid border=1 cellspacing=0 cellpadding=0  style='border-collapse:collapse;border:none'>  <tr>   <td width=44 style='width:32.75pt;border:solid windowtext 1.0pt;background:   #00529C;padding:0in 5.4pt 0in 5.4pt'>   <p align=center style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;   margin-left:0in;text-align:center'><b><span style='font-size:10.0pt;   color:white'>STT</span></b></p>   </td>   <td width=149 style='width:111.65pt;border:solid windowtext 1.0pt;border-left:   none;background:#00529C;padding:0in 5.4pt 0in 5.4pt'>   <p align=center style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;   margin-left:0in;text-align:center'><b><span style='font-size:10.0pt;   color:white'>Hạng mục</span></b></p>   </td>   <td width=428 style='width:320.8pt;border:solid windowtext 1.0pt;border-left:   none;background:#00529C;padding:0in 5.4pt 0in 5.4pt'>   <p align=center style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;   margin-left:0in;text-align:center'><b><span style='font-size:10.0pt;   color:white'>Thông tin</span></b></p>   </td>  </tr>";
                for (int i = 1; i <= subTableMerchant.length(); i++) {
                    textThreeThree += "<tr><td width=44 rowspan=5 style='width:32.75pt;border:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p align=center style='margin:0in;text-align:center'><span style='font-size:   10.0pt;color:black'>" + i + "</span></p>   </td>   <td width=149 valign=top style='width:111.65pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in'><span style='font-size:10.0pt;color:black'>Merchant name</span></p>   </td>   <td width=428 valign=top style='width:320.8pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in;text-align:justify'><span style='font-size:10.0pt;color:black'>merchantnamesTbl" + i + "</span></p>   </td>  </tr>  <tr>   <td width=149 valign=top style='width:111.65pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in'><span style='font-size:10.0pt;color:black'>Merchant ID</span></p>   </td>   <td width=428 valign=top style='width:320.8pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in;text-align:justify'><span style='font-size:10.0pt;color:black'>merchantIdTbl" + i + "</span></p>   </td>  </tr>  <tr>   <td width=149 valign=top style='width:111.65pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in'><span style='font-size:10.0pt;color:black'>Số tài khoản</span></p>   </td>   <td width=428 valign=top style='width:320.8pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in;text-align:justify'><span style='font-size:10.0pt;color:black'>accountNumberTbl" + i + "</span></p>   </td>  </tr>  <tr>   <td width=149 valign=top style='width:111.65pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in'><span style='font-size:10.0pt;color:black'>Tên tài khoản</span></p>   </td>   <td width=428 valign=top style='width:320.8pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in;text-align:justify'><span style='font-size:10.0pt;color:black'>accountnamesTbl" + i + "</span></p>   </td>  </tr>  <tr>   <td width=149 valign=top style='width:111.65pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in'><span style='font-size:10.0pt;color:black'>Ngân hàng – Chi nhánh</span></p>   </td>   <td width=428 valign=top style='width:320.8pt;border-left:   none;border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:   0in;text-align:justify'><span style='font-size:10.0pt;color:black'>bankTbl" + i + "</span></p>   </td>  </tr>";
                }
                textThreeThree = textThreeThree + "</table>";
                dieu3Txt = dieu3Txt + textThreeThree;
                index++;
            }

            // xu ly 3.4
            if (khoanDamBaoSelection.equals("") && kyQuyType.equals("")) {
                dieu3Txt = dieu3Txt + textThreeFour;
            } else {
                String stkBank = "";
                String nameBank = "";
                String nameBankTimeOut = "";
                if (accountNumber.equals("other")) {
                    stkBank = accountNumberOther;
                    nameBank = accountNumberOther;
                    nameBankTimeOut = accountNumberOther;
                } else {
                    if (accountNumber.equals("techcombank")) {
                        stkBank = "**************";
                        nameBank = "Techcombank – Chi nhánh Hoàn Kiếm";
                        nameBankTimeOut = "Techcombank";
                    }
                    if (accountNumber.equals("vietcombank")) {
                        stkBank = "*************";
                        nameBank = "Vietcombank – Chi nhánh Sở Giao Dịch";
                        nameBankTimeOut = "Vietcombank";
                    }
                }
                int index3cham4 = 1;
                if (khoanDamBaoSelection.equals("Mien")) {
                    textThreeFour = textThreeFour + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Khoản đảm bảo khả năng thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   ĐVCNTT đồng ý cho OnePay giữ khoản đảm bảo khả năng thanh toán bằng hình thức sau: </span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='font-family:\"MS Gothic\"; color:black'>☒</span><span style='color:black'> OnePay miễn Khoản đảm bảo khả năng thanh toán cho ĐVCNTT.</span></p> ";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   Khoản đảm bảo khả năng thanh toán của ĐVCNTT có thể được thay đổi từng thời kỳ tùy theo đánh giá của OnePay về mức độ rủi ro của ĐVCNTT.</span></p> ";
                    index++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời điểm giải khoanh Khoản đảm bảo thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".1.   Sau 180 ngày kể từ ngày tạm ngưng hoặc thanh lý Hợp đồng, OnePay sẽ thực hiện giải khoanh Khoản đảm bảo thanh toán đang giữ của ĐVCNTT bằng cách chuyển khoản vào tài khoản số stkGiaiKhoanhTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>mở tại ngân hàng openByBankTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>của ĐVCNTT sau khi trừ đi tất cả các khoản nợ của ĐVCNTT đối với OnePay theo quy định của Hợp đồng và các khoản tiền trong các trường hợp cụ thể được quy định tại Điều 9 của Hợp đồng này.&nbsp;</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".2.   Thời điểm giải khoanh Khoản đảm bảo thanh toán này có thể thay đổi dựa trên những đánh giá của OnePay về mức độ rủi ro của ĐVCNTT nhưng tối đa không quá 180 ngày kể từ ngày tạm ngưng, thanh lý Hợp đồng.</span></p> ";
                }
                if (khoanDamBaoSelection.equals("") && kyQuyType.equals("kyQuyKeep")) {
                    textThreeFour = textThreeFour + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Khoản đảm bảo khả năng thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   ĐVCNTT đồng ý cho OnePay giữ khoản đảm bảo khả năng thanh toán bằng hình thức sau: </span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='font-family:\"MS Gothic\"; color:black'>☒</span><span style='color:black'> OnePay giữ lại keepPercentTxt giá trị thanh toán tạm ứng thẻ quốc tế của ĐVCNTT cho đến khi đủ số tiền là khoanDamBaoInputTxt VND (bằng chữ: ************************) làm khoản đảm bảo thanh toán.</span></p> <br>";
                    textThreeFour += "<table class=5 border=1 cellspacing=0 cellpadding=0 width=576 style='margin-left:  35.75pt;border-collapse:collapse;border:none'>  <tr>   <td width=114 style='width:85.5pt;border:solid black 1.0pt;background:#00529C;   padding:0in 5.4pt 0in 5.4pt'>   <p  align=center style='margin-top:4.0pt;margin-right:0in;   margin-bottom:4.0pt;margin-left:0in;text-align:center;line-height:normal;   border:none'><b><span style='font-size:10.0pt;color:white'>Hạng mục</span></b></p>   </td>   <td width=462 style='width:346.3pt;border:solid black 1.0pt;border-left:none;   background:#00529C;padding:0in 5.4pt 0in 5.4pt'>   <p  align=center style='margin-top:4.0pt;margin-right:0in;   margin-bottom:4.0pt;margin-left:0in;text-align:center;line-height:normal;   border:none'><b><span style='font-size:10.0pt;color:white'>Thông tin</span></b></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Số tài khoản</span></p>   </td>   <td width=462 style='width:346.3pt;border-top:none;border-left:none;   border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;text-align:justify;line-height:normal;border:none'><span   style='font-size:10.0pt;color:black;background:white'>VND " + stkBank + "</span></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Tên tài khoản</span></p>   </td>   <td width=462 valign=top style='width:346.3pt;border-top:none;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ TRỰC TUYẾN ONEPAY</span></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:-1.15pt;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Ngân hàng </span></p>   </td>   <td width=462 valign=top style='width:346.3pt;border-top:none;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;text-align:justify;line-height:normal;border:none'><span   style='font-size:10.0pt;color:black;background:white'>" + nameBank + "</span></p>   </td>  </tr> </table> ";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   OnePay sẽ thực hiện kết chuyển Khoản đảm bảo khả năng thanh toán của ĐVCNTT sang khoanh giữ tại tài khoản có kỳ hạn 06 tháng với lãi suất niêm yết của Ngân hàng openByBankTxt với số tiền </span><span style='color:black'>khoanDamBaoInputTxt </span><span style='color:black'>VND (bằng chữ: </span><span style='color:black'>************************</span><span style='color:black'>). Số tiền lãi cuối kỳ của Khoản đảm bảo khả năng thanh toán sẽ được OnePay chuyển khoản cho ĐVCNTT vào tài khoản số VND stkGiaiKhoanhTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>mở tại openByBankTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>tại thời điểm giải khoanh.&nbsp;</span></p>";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   Hết thời hạn kyHanFDTxt tháng, khoản đảm bảo khả năng thanh toán kể trên sẽ tự động được gia hạn nếu hai bên không có bất kỳ một thỏa thuận nào khác. Nếu có thỏa thuận giải khoanh Khoản đảm bảo khả năng thanh toán trước kỳ hạn 6 tháng, đơn vị sẽ hưởng lãi suất tiền gửi không kỳ hạn của Ngân hàng " + nameBankTimeOut + " tại thời điểm giải khoanh.&nbsp;</span></p> ";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   Khoản đảm bảo khả năng thanh toán của ĐVCNTT có thể được thay đổi từng thời kỳ tùy theo đánh giá của OnePay về mức độ rủi ro của ĐVCNTT.</span></p> ";
                    index3cham4++;
                    index++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời điểm giải khoanh Khoản đảm bảo thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".1.   Sau 180 ngày kể từ ngày tạm ngưng hoặc thanh lý Hợp đồng, OnePay sẽ thực hiện giải khoanh Khoản đảm bảo thanh toán đang giữ của ĐVCNTT bằng cách chuyển khoản vào tài khoản số stkGiaiKhoanhTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>mở tại ngân hàng openByBankTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>của ĐVCNTT sau khi trừ đi tất cả các khoản nợ của ĐVCNTT đối với OnePay theo quy định của Hợp đồng và các khoản tiền trong các trường hợp cụ thể được quy định tại Điều 9 của Hợp đồng này.&nbsp;</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".2.   Thời điểm giải khoanh Khoản đảm bảo thanh toán này có thể thay đổi dựa trên những đánh giá của OnePay về mức độ rủi ro của ĐVCNTT nhưng tối đa không quá 180 ngày kể từ ngày tạm ngưng, thanh lý Hợp đồng.</span></p> ";
                }
                if (khoanDamBaoSelection.equals("") && kyQuyType.equals("kyQuyStandard")) {
                    textThreeFour = textThreeFour + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Khoản đảm bảo khả năng thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   ĐVCNTT đồng ý cho OnePay giữ khoản đảm bảo khả năng thanh toán bằng hình thức sau: </span></p> <p style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in; text-align:justify;vertical-align:baseline'><span style='font-family:\"MS Gothic\"; color:black'>☒</span><span style='color:black'> ĐVCNTT đồng ý nộp tiền vào tài khoản của OnePay theo thông tin tài khoản bên dưới này làm khoản đảm bảo thanh toán của ĐVCNTT với số tiền là: khoanDamBaoInputTxt VND (bằng chữ: ************************).</span></p><br> ";
                    textThreeFour += "<table class=5 border=1 cellspacing=0 cellpadding=0 width=576 style='margin-left:  35.75pt;border-collapse:collapse;border:none'>  <tr>   <td width=114 style='width:85.5pt;border:solid black 1.0pt;background:#00529C;   padding:0in 5.4pt 0in 5.4pt'>   <p  align=center style='margin-top:4.0pt;margin-right:0in;   margin-bottom:4.0pt;margin-left:0in;text-align:center;line-height:normal;   border:none'><b><span style='font-size:10.0pt;color:white'>Hạng mục</span></b></p>   </td>   <td width=462 style='width:346.3pt;border:solid black 1.0pt;border-left:none;   background:#00529C;padding:0in 5.4pt 0in 5.4pt'>   <p  align=center style='margin-top:4.0pt;margin-right:0in;   margin-bottom:4.0pt;margin-left:0in;text-align:center;line-height:normal;   border:none'><b><span style='font-size:10.0pt;color:white'>Thông tin</span></b></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Số tài khoản</span></p>   </td>   <td width=462 style='width:346.3pt;border-top:none;border-left:none;   border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;text-align:justify;line-height:normal;border:none'><span   style='font-size:10.0pt;color:black;background:white'>VND " + stkBank + "</span></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Tên tài khoản</span></p>   </td>   <td width=462 valign=top style='width:346.3pt;border-top:none;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ TRỰC TUYẾN ONEPAY</span></p>   </td>  </tr>  <tr>   <td width=114 valign=top style='width:85.5pt;border:solid black 1.0pt;   border-top:none;padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:-1.15pt;margin-bottom:   2.0pt;margin-left:0in;line-height:normal;border:none'><span style='font-size:   10.0pt;color:black;background:white'>Ngân hàng </span></p>   </td>   <td width=462 valign=top style='width:346.3pt;border-top:none;border-left:   none;border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;   padding:0in 5.4pt 0in 5.4pt'>   <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:   2.0pt;margin-left:0in;text-align:justify;line-height:normal;border:none'><span   style='font-size:10.0pt;color:black;background:white'>" + nameBank + "</span></p>   </td>  </tr> </table> ";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   OnePay sẽ thực hiện kết chuyển Khoản đảm bảo khả năng thanh toán của ĐVCNTT sang khoanh giữ tại tài khoản có kỳ hạn 06 tháng với lãi suất niêm yết của Ngân hàng openByBankTxt với số tiền </span><span style='color:black'>khoanDamBaoInputTxt </span><span style='color:black'>VND (bằng chữ: </span><span style='color:black'>************************</span><span style='color:black'>). Số tiền lãi cuối kỳ của Khoản đảm bảo khả năng thanh toán sẽ được OnePay chuyển khoản cho ĐVCNTT vào tài khoản số VND stkGiaiKhoanhTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>mở tại openByBankTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>tại thời điểm giải khoanh.&nbsp;</span></p>";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   Hết thời hạn kyHanFDTxt tháng, khoản đảm bảo khả năng thanh toán kể trên sẽ tự động được gia hạn nếu hai bên không có bất kỳ một thỏa thuận nào khác. Nếu có thỏa thuận giải khoanh Khoản đảm bảo khả năng thanh toán trước kỳ hạn 6 tháng, đơn vị sẽ hưởng lãi suất tiền gửi không kỳ hạn của Ngân hàng " + nameBankTimeOut + " tại thời điểm giải khoanh.&nbsp;</span></p> ";
                    index3cham4++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + "." + index3cham4 + ".   Khoản đảm bảo khả năng thanh toán của ĐVCNTT có thể được thay đổi từng thời kỳ tùy theo đánh giá của OnePay về mức độ rủi ro của ĐVCNTT.</span></p> ";
                    index3cham4++;
                    index++;
                    textThreeFour = textThreeFour + "<p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Thời điểm giải khoanh Khoản đảm bảo thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".1.   Sau 180 ngày kể từ ngày tạm ngưng hoặc thanh lý Hợp đồng, OnePay sẽ thực hiện giải khoanh Khoản đảm bảo thanh toán đang giữ của ĐVCNTT bằng cách chuyển khoản vào tài khoản số stkGiaiKhoanhTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>mở tại ngân hàng openByBankTxt</span><span style='font-size:10.0pt;color:black'> </span><span style='color:black'>của ĐVCNTT sau khi trừ đi tất cả các khoản nợ của ĐVCNTT đối với OnePay theo quy định của Hợp đồng và các khoản tiền trong các trường hợp cụ thể được quy định tại Điều 9 của Hợp đồng này.&nbsp;</span></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".2.   Thời điểm giải khoanh Khoản đảm bảo thanh toán này có thể thay đổi dựa trên những đánh giá của OnePay về mức độ rủi ro của ĐVCNTT nhưng tối đa không quá 180 ngày kể từ ngày tạm ngưng, thanh lý Hợp đồng.</span></p> ";
                }
                if (khoanDamBaoSelection.equals("") && kyQuyType.equals("kyQuyKhac")) {
                    textThreeFour = textThreeFour + "<p  style='margin-top:8.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span style='color:black'>" + countDieu + "." + index + ".      Khoản đảm bảo khả năng thanh toán</span></b></p> <p  style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in; margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span style='color:black'>" + countDieu + "." + index + ".1.   ĐVCNTT đồng ý cho OnePay giữ khoản đảm bảo khả năng thanh toán bằng hình thức sau: inputKyQuyKhacTxt.</span></p> ";
                }

                dieu3Txt = dieu3Txt + textThreeFour;
                index++;
            }
            countDieu++;

            templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu3Txt", dieu3Txt);

        }

        // xu ly dieu 4
        // if(!(monthFee.equals("")&&registerFee.equals("")&&feeTransInternationalTbl.equals("")&&feeTransDomesticAndAppTbl.equals("")
        // &&feeTransAppTbl.equals("")&&approveCardType01InternationalTbl.equals("")&&approveCardType02InternationalTbl.equals("")&&americanExpress01InternationalTbl.equals("")
        // &&americanExpress02InternationalTbl.equals("")&&feePaymentDomesticAndAppTbl.equals("")&&feePercentQrMobileTbl.equals("")
        // &&feePercentQrGrabTbl.equals("")&&feePercentQrShopeeTbl.equals("")&&feePercentQrZaloTbl.equals("")&&feePercentQrMoMoTbl.equals("")&&feePercentQrOtherTbl.equals(""))){
        // String dieu4Txt = "<h2>Điều "+countDieu+". Điều khoản phí và thanh toán</h2> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".1.      ĐVCNTT cam kết thanh toán các khoản phí cho OnePay theo
        // quy định tại Điều 2 của Phụ lục này.</span></p> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".2.      Phí đăng ký dịch vụ:</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;
        // text-align:justify;vertical-align:baseline'><span style='color:black'>Phí đăng ký dịch vụ sẽ được
        // OnePay thông báo đến ĐVCNTT và tiến hành thu trước thời điểm các Bên nghiệm thu dịch
        // vụ.</span></p> <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".3.      Phí duy trì dịch vụ hàng tháng</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.5in;
        // text-align:justify;vertical-align:baseline'><span style='color:black'>Được bắt đầu tính kể từ
        // thời điểm OnePay kích hoạt Dịch vụ cổng thanh toán điện tử trên hệ thống cho ĐVCNTT.</span></p>
        // <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".4.      Phí xử lý giao dịch:</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Thẻ
        // nội địa và Ứng dụng di động áp dụng đối với các giao dịch: giao dịch thanh toán, giao dịch điều
        // chỉnh khác (nếu có) và giao dịch hoàn trả.&nbsp;</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Thẻ
        // quốc tế áp dụng đối với mọi giao dịch OnePay nhận được từ ĐVCNTT thông qua hệ thống cổng thanh
        // toán bao gồm: các giao dịch cấp phép (authorization), giao dịch yêu cầu quyết toán (capture),
        // giao dịch thanh toán (purchase), giao dịch hủy (void), giao dịch hoàn trả (refund) và các giao
        // dịch khác theo quy định của TCTQT.&nbsp;</span></p> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".5.      Phí thanh toán:</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Áp
        // dụng với các giao dịch thanh toán thành công. </span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)   Đối
        // với các giao dịch hủy, giao dịch hoàn trả theo yêu cầu của ĐVCNTT, ĐVCNTT sẽ được hoàn lại phí
        // thanh toán tương ứng với giá trị hủy, hoàn trả mà ĐVCNTT thực hiện.</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>c)   Với
        // các giao dịch ĐVCNTT hoàn trả do phát sinh tranh chấp, khiếu nại theo quy định tại Điều 9 của Hợp
        // đồng, OnePay không hoàn phí thanh toán.</span></p> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:0in;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".6.      Sự thay đổi của mức phí:</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>a)   Mức
        // phí có thể thay đổi theo từng thời kỳ. OnePay sẽ thông báo bằng văn bản cho ĐVCNTT trước ít nhất
        // mười (10) ngày trước thời điểm thực hiện điều chỉnh.&nbsp;</span></p> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:.75in;
        // text-align:justify;text-indent:-.25in;vertical-align:baseline'><span style='color:black'>b)  
        // Trong trường hợp có sự thay đổi phí, trong vòng 05 (năm) ngày làm việc kể từ ngày nhận được thông
        // báo của OnePay, nếu ĐVCNTT không có ý kiến nào khác bằng văn bản gửi đến OnePay, đề xuất của
        // OnePay được mặc nhiên thừa nhận và áp dụng bởi các bên. Văn bản điều chỉnh mức phí này sẽ là một
        // phần không tách rời của Hợp đồng.</span></p> ";
        // if(hinhThucThuPhi.equals("ThuPhiCungButToanBaoCo") || hinhThucThuPhi.equals("ThuTheoThang") ||
        // hinhThucThuPhi.equals("Khac") || !monthFee.equals("")){
        // dieu4Txt = dieu4Txt + "<p style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><b><span
        // style='color:black'>"+countDieu+".7.      Đối soát giao dịch, thanh toán phí và hóa đơn
        // GTGT:</span></b></p> <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>"+countDieu+".7.1.   Trường hợp thu phí xử lý giao dịch, phí thanh toán cùng
        // bút toán báo có</span></p> <table class=MsoNormalTable border=1 cellspacing=0 cellpadding=0
        // width=618 style='border-collapse:collapse;border:none'> <tr style='height:22.9pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;background:#00529C; padding:0in 5.4pt 0in
        // 5.4pt;height:22.9pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Bước</span></b></p> </td> <td width=384
        // style='width:4.0in;border:solid black 1.0pt;border-left:none; background:#00529C;padding:0in
        // 5.4pt 0in 5.4pt;height:22.9pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Chi tiết thực hiện</span></b></p> </td> <td
        // width=174 style='width:130.5pt;border:solid black 1.0pt;border-left:none;
        // background:#00529C;padding:0in 5.4pt 0in 5.4pt;height:22.9pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Thực hiện bởi</span></b></p> </td> </tr> <tr
        // style='height:53.05pt'> <td width=60 style='width:44.75pt;border:solid black 1.0pt;b padding:0in
        // 5.4pt 0in 5.4pt;height:53.05pt'> <p align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>1</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:53.05pt'> <p
        // style='margin-bottom:0in;line-height:normal'>Việc thu phí sẽ được tính bởi OnePay trên cơ sở biểu
        // phí dịch vụ và các nghĩa vụ tài chính khác áp dụng cho ĐVCNTT được quy định tại Hợp đồng này.</p>
        // </td> <td width=174 style='width:130.5pt;bborder-left:none; border-bottom:solid black
        // 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:53.05pt'> <p
        // align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'>Hệ
        // thống của OnePay</p> </td> </tr> <tr style='height:89.05pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:89.05pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>2</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:89.05pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'><span lang=EN-GB>Trong
        // thời</span><span lang=VI> hạn </span>10<span lang=VI> (</span><span lang=EN-GB>mười</span><span
        // lang=VI>)</span><span lang=EN-GB> ngày làm việc đầu tiên hàng tháng, Các</span><span lang=EN-GB>
        // </span><span lang=EN-GB>Bên thực hiện đối soát và xác nhận số liệu thanh toán bằng Biên bản đối
        // soát </span><span lang=VI>(“<b>Biên Bản Đối Soát</b>”) do </span>OnePay<span lang=VI> gửi cho
        // </span>ĐVCNTT <span lang=EN-GB>qua email cho phần phí gồm phí xử lý giao dịch và phí thanh toán ở
        // tháng trước đó.</span></p> </td> <td width=174 style='width:130.5pt;bborder-left:none;
        // border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt;
        // height:89.05pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay, ĐVCNTT</span></p> </td> </tr> <tr style='height:116.5pt'> <td
        // width=60 style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in
        // 5.4pt;height:116.5pt'> <p align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>3</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:116.5pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>Nếu quá 05 (năm) ngày làm việc
        // kể từ <span lang=EN-GB>thời điểm OnePay</span><span lang=VI> gửi dự thảo Biên Bản Đối
        // Soát</span>, nếu ĐVCNTT không có phản hồi, <span lang=EN-GB>ĐVCNTT</span><span lang=VI> coi như
        // đã đồng ý với các </span><span lang=EN-GB>số liệu đối</span><span lang=VI> soát ghi trong dự thảo
        // Biên Bản Đối Soát</span>. Các phát sinh thu phí thừa/thiếu sẽ được hạch toán bù trừ vào kỳ đối
        // soát phí tiếp theo hoặc ĐVCNTT chuyển khoản bổ sung vào tài khoản chỉ định của OnePay hoặc theo
        // thỏa thuận thống nhất giữa các bên.</p> </td> <td width=174
        // style='width:130.5pt;bborder-left:none; border-bottom:solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:116.5pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay, ĐVCNTT</span></p> </td> </tr> <tr style='height:64.3pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:64.3pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>4</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:64.3pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>OnePay thực hiện xuất hóa đơn
        // GTGT cho ĐVCNTT gồm <span lang=EN-GB>phí xử lý giao dịch và phí thanh toán </span>theo Biên Bản
        // Đối Soát và phí duy trì dịch vụ hàng tháng (nếu có) theo quy định của Hợp đồng này.</p> </td> <td
        // width=174 style='width:130.5pt;bborder-left:none; border-bottom:solid black
        // 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:64.3pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay</span></p> </td> </tr> <tr style='height:151.15pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:151.15pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>5</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:151.15pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>Trong vòng 03 (ba) ngày làm việc
        // tiếp theo, ĐVCNTT thực hiện thanh toán phí duy trì dịch vụ hàng tháng (nếu có) cho OnePay bằng
        // một trong các cách sau:</p> <p style='margin-bottom:0in;text-align:justify;line-height:
        // normal'><span style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black'>
        // Chuyển khoản vào tài khoản theo thông tin ở Mục 3.8.2 của Phụ lục này. Trường hợp thông tin tài
        // khoản nhận thanh toán thay đổi, OnePay sẽ gửi thông báo thông tin tài khoản cập nhật cho
        // ĐVCNTT.</span></p> <p style='margin-bottom:0in;text-align:justify;line-height: normal'><span
        // style='font-family:\"MS Gothic\";color:black'>☐</span><span style='color:black'> OnePay khấu trừ
        // khoản thanh toán tạm ứng của ĐVCNTT cho đến khi đủ số tiền phí.</span></p> </td> <td width=174
        // style='width:130.5pt;bborder-left:none; border-bottom:solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:151.15pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>ĐVCNTT</span></p> </td> </tr> </table>";
        // dieu4Txt = dieu4Txt + "<p style='margin-top:12.0pt;margin-right:0in;margin-bottom:
        // 4.0pt;margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>4.7.2.   Trường hợp thu phí xử lý giao dịch, phí thanh toán hàng
        // tháng</span></p> <table class=MsoNormalTable border=1 cellspacing=0 cellpadding=0 width=618
        // style='border-collapse:collapse;border:none'> <tr style='height:22.9pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;background:#00529C; padding:0in 5.4pt 0in
        // 5.4pt;height:22.9pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Bước</span></b></p> </td> <td width=384
        // style='width:4.0in;border:solid black 1.0pt;border-left:none; background:#00529C;padding:0in
        // 5.4pt 0in 5.4pt;height:22.9pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Chi tiết thực hiện</span></b></p> </td> <td
        // width=174 style='width:130.5pt;border:solid black 1.0pt;border-left:none;
        // background:#00529C;padding:0in 5.4pt 0in 5.4pt;height:22.9pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in;
        // line-height:normal'><b><span style='color:white'>Thực hiện bởi</span></b></p> </td> </tr> <tr
        // style='height:53.05pt'> <td width=60 style='width:44.75pt;border:solid black 1.0pt;b padding:0in
        // 5.4pt 0in 5.4pt;height:53.05pt'> <p align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>1</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:53.05pt'> <p
        // style='margin-bottom:0in;line-height:normal'>Việc thu phí sẽ được tính bởi OnePay trên cơ sở biểu
        // phí dịch vụ và các nghĩa vụ tài chính khác áp dụng cho ĐVCNTT được quy định tại Hợp đồng này.</p>
        // </td> <td width=174 style='width:130.5pt;bborder-left:none; border-bottom:solid black
        // 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:53.05pt'> <p
        // align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'>Hệ
        // thống của OnePay</p> </td> </tr> <tr style='height:89.05pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:89.05pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>2</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:89.05pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'><span lang=EN-GB>Trong
        // thời</span><span lang=VI> hạn </span>10<span lang=VI> (</span><span lang=EN-GB>mười</span><span
        // lang=VI>)</span><span lang=EN-GB> ngày làm việc đầu tiên hàng tháng, Các</span><span lang=EN-GB>
        // </span><span lang=EN-GB>Bên thực hiện đối soát và xác nhận số liệu thanh toán bằng Biên bản đối
        // soát </span><span lang=VI>(“<b>Biên Bản Đối Soát</b>”) do </span>OnePay<span lang=VI> gửi cho
        // </span>ĐVCNTT <span lang=EN-GB>qua email cho phần phí gồm phí xử lý giao dịch và phí thanh toán ở
        // tháng trước đó.</span></p> </td> <td width=174 style='width:130.5pt;bborder-left:none;
        // border-bottom:solid black 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt;
        // height:89.05pt'> <p align=center style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay, ĐVCNTT</span></p> </td> </tr> <tr style='height:116.5pt'> <td
        // width=60 style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in
        // 5.4pt;height:116.5pt'> <p align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>3</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:116.5pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>Nếu quá 05 (năm) ngày làm việc
        // kể từ <span lang=EN-GB>thời điểm OnePay</span><span lang=VI> gửi dự thảo Biên Bản Đối
        // Soát</span>, nếu ĐVCNTT không có phản hồi, <span lang=EN-GB>ĐVCNTT</span><span lang=VI> coi như
        // đã đồng ý với các </span><span lang=EN-GB>số liệu đối</span><span lang=VI> soát ghi trong dự thảo
        // Biên Bản Đối Soát</span>. Các phát sinh thu phí thừa/thiếu sẽ được hạch toán bù trừ vào kỳ đối
        // soát phí tiếp theo hoặc ĐVCNTT chuyển khoản bổ sung vào tài khoản chỉ định của OnePay hoặc theo
        // thỏa thuận thống nhất giữa các bên.</p> </td> <td width=174
        // style='width:130.5pt;bborder-left:none; border-bottom:solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:116.5pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay, ĐVCNTT</span></p> </td> </tr> <tr style='height:64.3pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:64.3pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>4</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:64.3pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>OnePay thực hiện xuất hóa đơn
        // GTGT cho ĐVCNTT gồm <span lang=EN-GB>phí xử lý giao dịch và phí thanh toán </span>theo Biên Bản
        // Đối Soát và phí duy trì dịch vụ hàng tháng (nếu có) theo quy định của Hợp đồng này.</p> </td> <td
        // width=174 style='width:130.5pt;bborder-left:none; border-bottom:solid black
        // 1.0pt;border-right:solid black 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:64.3pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>OnePay</span></p> </td> </tr> <tr style='height:137.2pt'> <td width=60
        // style='width:44.75pt;border:solid black 1.0pt;b padding:0in 5.4pt 0in 5.4pt;height:137.2pt'> <p
        // align=center style='margin-top:6.0pt;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>5</span></p> </td> <td width=384
        // style='width:4.0in;bborder-left:none;border-bottom: solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:137.2pt'> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'>Trong vòng 03 (ba) ngày làm việc
        // tiếp theo, ĐVCNTT thực hiện thanh toán phí gồm phí xử lý giao dịch, phí thanh toán và phí duy trì
        // dịch vụ hàng tháng (nếu có) cho OnePay bằng một trong các cách sau:</p> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'><span style='font-family:\"MS
        // Gothic\";color:black'>☐</span><span style='color:black'> Chuyển khoản vào tài khoản theo thông
        // tin ở Mục 3.8.2 của Phụ lục này. Trường hợp thông tin tài khoản nhận thanh toán thay đổi, OnePay
        // sẽ gửi thông báo thông tin tài khoản cập nhật cho ĐVCNTT.</span></p> <p
        // style='margin-bottom:0in;text-align:justify;line-height: normal'><span style='font-family:\"MS
        // Gothic\";color:black'>☐</span><span style='color:black'> OnePay khấu trừ khoản thanh toán tạm ứng
        // của ĐVCNTT cho đến khi đủ số tiền phí.</span></p> </td> <td width=174
        // style='width:130.5pt;bborder-left:none; border-bottom:solid black 1.0pt;border-right:solid black
        // 1.0pt;padding:0in 5.4pt 0in 5.4pt; height:137.2pt'> <p align=center
        // style='margin-top:0in;margin-right:0in;
        // margin-bottom:0in;margin-left:.5in;text-align:center;text-indent:-.5in; line-height:normal'><span
        // style='color:black'>ĐVCNTT</span></p> </td> </tr> </table> ";
        // dieu4Txt = dieu4Txt + "<p style='margin-top:12.0pt;margin-right:0in;margin-bottom:
        // 4.0pt;margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>4.8</span><span style='color:black'>       </span><span
        // style='color:black'>Danh sách tài khoản ngân hàng của OnePay</span></p> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>4.8.1.   Tài khoản OnePay nhận khoản đảm bảo thanh toán từ ĐVCNTT</span></p>
        // <table class=MsoTableGrid border=1 cellspacing=0 cellpadding=0
        // style='margin-left:35.75pt;border-collapse:collapse;border:none'> <tr> <td width=114
        // style='width:85.5pt;border:solid windowtext 1.0pt;background: #00529C;padding:0in 5.4pt 0in
        // 5.4pt'> <p align=center style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Hạng
        // mục</span></b></p> </td> <td width=456 style='width:4.75in;border:solid windowtext
        // 1.0pt;border-left: none;background:#00529C;padding:0in 5.4pt 0in 5.4pt'> <p align=center
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Thông
        // tin</span></b></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Số tài khoản</span></p> </td> <td width=456
        // style='width:4.75in;bborder-left:none; border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify;vertical-align:baseline'><span style='font-size:10.0pt; color:black'>VND
        // **************</span></p> </td> </tr> <tr> <td width=114 valign=top
        // style='width:85.5pt;border:solid windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Tên tài khoản</span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ TRỰC TUYẾN
        // ONEPAY</span></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:-1.15pt;margin-bottom:2.0pt; margin-left:0in'><span
        // style='font-size:10.0pt;color:black'>Ngân hàng </span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify'><span style='font-size:10.0pt;color:black'>Techcombank – Chi nhánh Hoàn
        // Kiếm</span></p> </td> </tr> </table> ";
        // dieu4Txt = dieu4Txt + "<p style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:.5in;text-align:justify;text-indent:-.5in;line-height:normal'><span
        // style='color:black'>4.8.2.   Tài khoản OnePay nhận thanh toán phí dịch vụ và các nghĩa vụ tài
        // chính khác được quy định trong Hợp đồng này từ ĐVCNTT</span></p> <table class=MsoTableGrid
        // border=1 cellspacing=0 cellpadding=0
        // style='margin-left:35.75pt;border-collapse:collapse;border:none'> <tr> <td width=114
        // style='width:85.5pt;border:solid windowtext 1.0pt;background: #00529C;padding:0in 5.4pt 0in
        // 5.4pt'> <p align=center style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Hạng
        // mục</span></b></p> </td> <td width=456 style='width:4.75in;border:solid windowtext
        // 1.0pt;border-left: none;background:#00529C;padding:0in 5.4pt 0in 5.4pt'> <p align=center
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Thông
        // tin</span></b></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Số tài khoản</span></p> </td> <td width=456
        // style='width:4.75in;bborder-left:none; border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify;vertical-align:baseline'><span style='font-size:10.0pt; color:black'>VND
        // 0011002227647</span></p> </td> </tr> <tr> <td width=114 valign=top
        // style='width:85.5pt;border:solid windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Tên tài khoản</span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ TRỰC TUYẾN
        // ONEPAY</span></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:-1.15pt;margin-bottom:2.0pt; margin-left:0in'><span
        // style='font-size:10.0pt;color:black'>Ngân hàng </span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify'><span style='font-size:10.0pt;color:black'>Vietcombank – Chi nhánh Sở
        // Giao Dịch</span></p> </td> </tr> <tr> <td width=114 style='width:85.5pt;border:solid windowtext
        // 1.0pt;border-top: none;padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:2.0pt;margin-right:-1.45pt;margin-bottom:2.0pt; margin-left:0in'><span
        // style='font-size:10.0pt;color:black'>Nội dung</span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify'><span style='font-size:10.0pt;color:black'>(Mã số thuế/Tên công ty) +
        // (Merchant Name/Merchant ID) + thanh toan phi Ecom thang … nam … theo hop dong so …</span></p>
        // </td> </tr> </table> <p style='margin-top:6.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:.5in;text-indent:-.5in;line-height:normal'><span style='color:black'> 4.8.3. Tài
        // khoản OnePay nhận khoản hoàn trả từ ĐVCNTT</span></p> <table class=MsoTableGrid border=1
        // cellspacing=0 cellpadding=0 style='margin-left:35.75pt;border-collapse:collapse;border:none'>
        // <tr> <td width=114 style='width:85.5pt;border:solid windowtext 1.0pt;background:
        // #00529C;padding:0in 5.4pt 0in 5.4pt'> <p align=center
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Hạng
        // mục</span></b></p> </td> <td width=456 style='width:4.75in;border:solid windowtext
        // 1.0pt;border-left: none;background:#00529C;padding:0in 5.4pt 0in 5.4pt'> <p align=center
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:4.0pt;
        // margin-left:0in;text-align:center'><b><span style='font-size:10.0pt; color:white'>Thông
        // tin</span></b></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Số tài khoản</span></p> </td> <td width=456
        // style='width:4.75in;bborder-left:none; border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify;vertical-align:baseline'><span style='font-size:10.0pt; color:black'>VND
        // *************</span></p> </td> </tr> <tr> <td width=114 valign=top
        // style='width:85.5pt;border:solid windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>Tên tài khoản</span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left: 0in'><span
        // style='font-size:10.0pt;color:black'>CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ TRỰC TUYẾN
        // ONEPAY</span></p> </td> </tr> <tr> <td width=114 valign=top style='width:85.5pt;border:solid
        // windowtext 1.0pt; bpadding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:-1.15pt;margin-bottom:2.0pt; margin-left:0in'><span
        // style='font-size:10.0pt;color:black'>Ngân hàng </span></p> </td> <td width=456 valign=top
        // style='width:4.75in;bborder-left: none;border-bottom:solid windowtext 1.0pt;border-right:solid
        // windowtext 1.0pt; padding:0in 5.4pt 0in 5.4pt'> <p
        // style='margin-top:6.0pt;margin-right:0in;margin-bottom:2.0pt;margin-left:
        // 0in;text-align:justify'><span style='font-size:10.0pt;color:black'>Vietcombank – Chi nhánh Sở
        // Giao Dịch</span></p> </td> </tr> </table> <p
        // style='margin-top:4.0pt;margin-right:0in;margin-bottom:0in;margin-left:0in;
        // text-align:justify;vertical-align:baseline'><span style='color:black'>&nbsp;</span></p> ";
        // }
        // countDieu ++;
        // templateHtml = ContractUtils.replaceVariable(templateHtml, "dieu4Txt", dieu4Txt);

        // }
        // xu ly dieu 4
        templateHtml = ContractUtils.replaceVariable(templateHtml, "countDieuInt", String.valueOf(countDieu));
        // templateHtml = ContractUtils.replaceVariable(templateHtml, "countDieuTxt",
        // convertCountDieuIntTotxt(countDieu));

        return templateHtml;

    }

    public static void pushContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String templateHtml = "";
                Map<String, Object> mIn = new HashMap<>();
                ContractOriginal contractOriginal = getBodyContract(ctx.getBodyAsJson());


                                
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contractOriginal.getPartnerId(), userId);

                Integer nResult = 200;
                int contractId = contractOriginal.getContractId();
                String contractNumber = contractOriginal.getContractNumber();
                String contractType = contractOriginal.getContractType();

                Integer countContractNumber = contractType.equals("PL") ? 0 : ContractDao.checkContractNumber(contractId, contractNumber);
                if (countContractNumber > 0) {
                    throw new ErrorException(500, "Thất bại", "Số hợp đồng đã tồn tại", "", "");
                }

                // task handle template voi phu luc khac
                if (contractOriginal.getContractCode().equals("PL-K")) {
                    templateHtml = templateHtml + handleTemplateHtml(contractOriginal);
                    nResult = ContractDao.pushContractAndTemplateOriginal(contractOriginal, templateHtml);
                } else {
                    nResult = ContractDao.pushContractOriginalTemplate(contractOriginal);
                }

                mIn.put("n_result", nResult);

                sendResponse(ctx, nResult == 200 ? 200 : 500, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "PUSH CONTRACT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static String convertCountDieuIntTotxt(int count) {
        switch (count) {
            case 1:
                return "một";
            case 2:
                return "hai";
            case 3:
                return "ba";
            case 4:
                return "bốn";
            case 5:
                return "năm";
            default:
                return "";

        }
    }

    public static void saveSubContracTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            String content = "";
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String template = bodyJson.getString("template");
                int contractId = Integer.parseInt(bodyJson.getString("id"));
                int result = ContractDao.saveTemplateSubContractOriginal(contractId, template);
                Map map = new HashMap<>();
                if (result == 200) {
                    map.put("key", "OK");
                    sendResponse(ctx, 200, map);
                    map.put("key", "");
                    sendResponse(ctx, 200, map);
                } else {
                    map.put("key", "ERROR");
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "SAVE TEMPLATE CONTRACT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportTemplateContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            String content = "";
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String contractCode = bodyJson.getString("contractCode");
                int contractId = Integer.parseInt(bodyJson.getString("id"));
                ContractOriginal contract = ContractDao.getSubContractDetailById(contractId);
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(contract.getPartnerId(), userId);

                Map map = new HashMap<>();
                if (contract.getTemplate().equals("")) {
                    if (contractCode != null) {
                        String templateFolder = Config.getString("file.contract_template_folder", "/opt/iportal-service/classes/templates/contract_template");
                        content = Files.asCharSource(new File(templateFolder + "/" + contractCode + "/" + contractCode + ".html"), StandardCharsets.UTF_8).read();
                        // ContractDao.saveTemplateSubContractOriginal(contractId, content);
                        if (!content.isEmpty()) {
                            map.put("key", content);
                            sendResponse(ctx, 200, map);
                        } else {
                            map.put("key", "");
                            sendResponse(ctx, 200, map);
                        }
                    }
                } else {
                    map.put("key", contract.getTemplate());
                    sendResponse(ctx, 200, map);
                }

            }

            catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportContractHtml(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map mapResponse = new HashMap<>();
                int contractId = Integer.parseInt(bodyJson.getString("id", "0"));
                String contractCode = bodyJson.getString("contractCode", "");
                String version = bodyJson.getString("version", "");
                boolean bilingual = bodyJson.getBoolean("bilingual", false);

                if ("PL-K".equals(contractCode)) {
                    exportContractPKL(ctx);
                } else if ("HD13".equals(contractCode) && "v3".equals(version) && !bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (VNE)";
                    String html = (new HD13V3Generator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD13".equals(contractCode) && "v3".equals(version) && bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (Song ngu)";
                    String html = (new HD13V3BilingualGenerator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD13".equals(contractCode) && "v4".equals(version) && !bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (VNE)";
                    String html = (new HD13V4Generator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD13".equals(contractCode) && "v4".equals(version) && bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (Song ngu)";
                    String html = (new HD13V4BilingualGenerator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD13".equals(contractCode) && "v5".equals(version) && !bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (VNE)";
                    String html = (new HD13V5Generator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD13".equals(contractCode) && "v5".equals(version) && bilingual) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "Hop dong cong thanh toan dien tu (Song ngu)";
                    String html = (new HD13V5BilingualGenerator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else if ("HD14".equals(contractCode)) {
                    ContractOriginal contractOriginal = ContractDao.getContractDetailById(contractId);
                    String fileName = "CN_Hop dong cong thanh toan dien tu - ban day du thong tin - VNE - OP & Ca nhan";
                    String html = (new HD14Generator(contractOriginal)).generateHtml();
                    mapResponse.put("templateHtml", html);
                    mapResponse.put("fileName", fileName);
                    sendResponse(ctx, 200, mapResponse);
                } else {
                    throw new ErrorException(500, "Thất bại", "Hiện chưa có template cho loại hợp đồng này", "", "");
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT HTML: ", e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    // hàm export contract by template
    public static void exportContractByTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map mapResponse = new HashMap<>();
                int contractId = Integer.parseInt(request.getParam("id") == null ? "0" : request.getParam("id"));
                String contractCode = request.getParam("contractCode") == null ? BLANK : request.getParam("contractCode");
                String version = request.getParam("version") == null ? BLANK : request.getParam("version");
                boolean bilingual = request.getParam("bilingual") == null ? false : Boolean.parseBoolean(request.getParam("bilingual"));
                String emailExport = request.getParam("emailExport") == null ? BLANK : request.getParam("emailExport");
                // get contract detail and template
                ContractOriginal contractOriginal;
                //lấy subcontract nếu là export công văn phụ lục
                //kiểm tra nếu contractCode là 1 trong các mã sau thì lấy contract detail: CV-DCHD hoặc PL02_v2
                
                if("CV-DCHD".equals(contractCode) || "PL02_v2".equals(contractCode)|| "PL-K_v2".equals(contractCode)|| "VBUQ".equals(contractCode)|| "BBTLv2".equals(contractCode)|| "BBTTv2".equals(contractCode)|| "PL15v2".equals(contractCode)){
                    contractOriginal = ContractDao.getSubContractDetailById(contractId);
                }else{
                    //lấy contract detail nếu là export hợp đồng
                    contractOriginal = ContractDao.getContractDetailById(contractId);
                }
                
                // get file name and form from db
                Map<String, String> mFileTemplate = ContractDao.getTemplateByContractId(contractOriginal.getIdTemplate(), bilingual);
                if (mFileTemplate.get("fileName") != null && !mFileTemplate.get("fileName").isEmpty()) {
                    // update contract version
                    int idContractVersion = ContractDao.updateContractVersion(contractOriginal.getIdVersion(), contractOriginal.getContractId(), emailExport);
                    ContractGenerator contractGenerator = new ContractGenerator(contractOriginal);
                    Map<String, Object> mIn = new HashMap<>();
                    // support HD13 (ecom) và HD13 (Ecom bilingual) và HD14 (Ecom cá nhân)
                    Map<String, String> mDataReplace = contractGenerator.generateParam(bilingual);
                    mIn.put("N_ID_VERSION", contractOriginal.getIdVersion() + "");
                    mIn.put("N_ID_CONTRACT", contractOriginal.getContractId() + "");
                    mIn.put("N_ID_CONTRACT_VERSION", idContractVersion + "");
                    mIn.put("N_ID_PARTNER", contractOriginal.getPartnerId() + "");
                    mIn.putAll(mFileTemplate);
                    mIn.put("mDataReplace", mDataReplace);
                    mIn.put("hide", contractGenerator.getHide());
                    mIn.put("bilingual", bilingual);
                    //tên file lấy thêm thông tin thời gian xuất file định dạng yyyyMMdd
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    String formattedDate = now.format(formatter);

                    String fileName = mFileTemplate.get("fileName") + "_"+formattedDate;
                    String fileHashName = Convert.hash(fileName);

                    Map<String, Object> data = new HashMap<>();
                    data.put("parameter", mIn);

                    // requestData in message
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    requestData.put(ParamsPool.FILE_EXT, ".docx");


                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                    fileDownloadDto.setFile_type("export_contract_docx");
                    fileDownloadDto.setFile_name(fileName);
                    fileDownloadDto.setFile_hash_name(fileHashName);
                    fileDownloadDto.setConditions("EXPORT CONTRACT");
                    fileDownloadDto.setExt("docx");
                    FileDownloadDao.insert(fileDownloadDto);

                    Map<String, Object> mOut = new HashMap<>();
                    logger.log(Level.WARNING, "mIn", mIn);
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);
                    mOut.put("fileDownloadDto", fileDownloadDto);
                    mOut.put("mIn", mIn);

                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                    mOut.put("nerror", "200");
                    sendResponse(ctx, 200, mOut);
                } else {
                    Map<String, Object> mOut = new HashMap<>();
                    mOut.put("nerror", "500");
                    sendResponse(ctx, 201, mOut);
                }

            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE: " + e, e);
                ctx.fail(e);
                e.printStackTrace();
            }
        }, false, null);
    }

    // hàm export contract by template pdf
    public static void exportContractByTemplatePdf(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int contractId = Integer.parseInt(request.getParam("id") == null ? "0" : request.getParam("id"));
                String contractCode = request.getParam("contractCode") == null ? BLANK : request.getParam("contractCode");
                boolean bilingual = request.getParam("bilingual") == null ? false : Boolean.parseBoolean(request.getParam("bilingual"));
                int idContractVersion = request.getParam("idContractVersion") == null ? 0 : Integer.parseInt(request.getParam("idContractVersion"));
                
                // Lấy thông tin hợp đồng
                ContractOriginal contractOriginal;
                //lấy subcontract nếu là export công văn phụ lục
                //kiểm tra nếu contractCode là 1 trong các mã sau thì lấy contract detail: CV-DCHD hoặc PL02_v2
                
                if("CV-DCHD".equals(contractCode) || "PL02_v2".equals(contractCode)|| "PL-K_v2".equals(contractCode)){
                    contractOriginal = ContractDao.getSubContractDetailById(contractId);
                }else{
                    //lấy contract detail nếu là export hợp đồng
                    contractOriginal = ContractDao.getContractDetailById(contractId);
                }
                
                String exportFileName = "";
                String exportFileNamePdf = "";
                final String fileName = String.valueOf(contractOriginal.getContractId()) + (bilingual ? "_bilingual" : "");
                final String fileExt = "pdf";

                // get file name and form from db
                Map<String, String> mFileTemplate = ContractDao.getTemplateByContractId(contractOriginal.getIdTemplate(), bilingual);
                if (mFileTemplate.get("fileName") != null && !mFileTemplate.get("fileName").isEmpty()) {
                    // Tạo dữ liệu cho hợp đồng
                    ContractGenerator contractGenerator = new ContractGenerator(contractOriginal);
                    Map<String, String> mDataReplace = contractGenerator.generateParam(bilingual);
                    
                    // Chuẩn bị dữ liệu đầu vào
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("N_ID_VERSION", String.valueOf(contractOriginal.getIdVersion()));
                    mIn.put("N_ID_CONTRACT", String.valueOf(contractOriginal.getContractId()));
                    mIn.put("N_ID_CONTRACT_VERSION", String.valueOf(idContractVersion));
                    mIn.put("N_ID_PARTNER", String.valueOf(contractOriginal.getPartnerId()));
                    mIn.putAll(mFileTemplate);
                    mIn.put("mDataReplace", mDataReplace);
                    mIn.put("hide", contractGenerator.getHide());
                    
                    // Tạo tên file với định dạng thời gian
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    String formattedDate = now.format(formatter);
                    String fileNameRequest = mFileTemplate.get("fileName") + "_" + formattedDate;
                    String fileHashName = Convert.hash(fileName);

                    // Chuẩn bị thông tin request
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    requestData.put(ParamsPool.FILE_EXT, ".docx");
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileNameRequest);

                    // Tạo file Word từ template
                    String template = mFileTemplate.get("templatePath");
                    exportFileName = Config.getString("export_contract_api_location", "/opt/onesched/export/contract/") + mIn.get("N_ID_CONTRACT") + (bilingual ? "_bilingual" : "") + ".docx";
                    logger.log(Level.INFO, "-------------START EXPORT CONTRACT DOCX FILE-----------------------");
                    logger.log(Level.INFO, "TEMPLATE FILE: " + template);
                    
                    try (InputStream templateFileStream = new FileInputStream(template);
                        OutputStream outputStream = new FileOutputStream(exportFileName)) {
                        
                        XWPFDocument document = new XWPFDocument(templateFileStream);
                        List<Map<String, String>> listHideData = contractGenerator.getHide();
                        
                        // Xử lý ẩn các phần trong template
                        for (Map<String, String> hideData : listHideData) {
                            String type = hideData.get("type");
                            if ("table".equals(type)) {
                                document = ExcelReportGenerator.removeTableRowWithText(document, hideData.get("key"));
                            } else if ("row".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphWithText(document, hideData.get("key"));
                            } else if ("tag".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphsBetweenTags(document, hideData.get("start"), hideData.get("end"));
                            } else if ("line_in_table".equals(type)) {
                                document = ExcelReportGenerator.removeLineWithTextInTable(document, hideData.get("key"));
                            } else if ("full_table".equals(type)) {
                                document = ExcelReportGenerator.removeTableWithText(document, hideData.get("key"));
                            }
                        }
                        
                        // Thay thế nội dung trong template
                        document = ExcelReportGenerator.replaceTextInDocument(document, mDataReplace);
                        
                        // Tạo và thêm mã QR vào footer
                        String b = bilingual ? "y" : "n";
                        String qrCodeText = mIn.get("N_ID_PARTNER") + "?c=" + mIn.get("N_ID_CONTRACT") + "&v=" + mIn.get("N_ID_CONTRACT_VERSION") + "&b=" + b;
                        byte[] qrCodeImage = ExcelReportGenerator.generateBarcodeImageByte(qrCodeText);
                        ExcelReportGenerator.replaceTextWithBarcodeInFooter(document, "qrcode", qrCodeImage);
                        
                        // Ghi file
                        document.write(outputStream);
                        logger.log(Level.INFO, "EXPORT FILE COMPLETED: " + exportFileName);
                        //convert to pdf
                        JsonObject jsonBody = new JsonObject();
                        jsonBody.put("FILE_PATH", exportFileName);
                        jsonBody.put("file_http", "http://127.0.0.1/contract-compare/" + mIn.get("N_ID_CONTRACT") + (bilingual ? "_bilingual" : "") + ".docx");
                        // Create URL object
                        URL url = new URL( "http://127.0.0.1/convert-word-to-pdf-return");
                        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                        
                        // Set request method and headers
                        connection.setRequestMethod("POST");
                        connection.setRequestProperty("Content-Type", "application/json");
                        connection.setDoOutput(true);
                        
                        // Send request body
                        try (OutputStream os = connection.getOutputStream()) {
                            byte[] input = jsonBody.toString().getBytes("utf-8");
                            os.write(input, 0, input.length);
                        }
                        
                        // Get response code
                        int responseCode = connection.getResponseCode();
                        logger.log(Level.INFO, "Response Code: " + responseCode);
                        
                        if (responseCode == 200) {
                            // Get content type to determine if it's a PDF
                            String contentType = connection.getContentType();
                            logger.log(Level.INFO, "Content Type: " + contentType);
                            
                            // Read response and save as PDF file
                            try (InputStream inputStream = connection.getInputStream()) {
                                String fileNamepdf = "converted_" + System.currentTimeMillis() + ".pdf";
                                String filePath = "/opt/onesched/export/contract/" + fileNamepdf;
                                exportFileNamePdf = filePath;
                                java.nio.file.Files.copy(inputStream, Paths.get(filePath));
                                logger.log(Level.INFO, "PDF file saved successfully to: " + filePath);
                            }
                        } else {
                            // Read error response
                            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                                String line;
                                StringBuilder response = new StringBuilder();
                                while ((line = reader.readLine()) != null) {
                                    response.append(line);
                                }
                                logger.log(Level.INFO, "Error response: " + response.toString());
                            }
                        }
                    }
                } else {
                    logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE PDF ERROR: ", "Không tìm thấy template hợp đồng");
                }

                //exportFileName = "/opt/onesched/export/contract/1144192.docx"
                //exportFileNamePdf = "/opt/onesched/export/contract/converted_1759381463338.pdf"
                Path requestPath = FileSystems.getDefault().getPath(exportFileNamePdf).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                        Map<String, Object> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(java.nio.file.Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        } else if (extFile.equals("pdf")) {
                            contentType = "application/pdf";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                // logger.info("Download Success");
                            } else {
                                // logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });



            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE PDF ERROR: ", e);
                Map<String, Object> mOut = new HashMap<>();
                mOut.put("nerror", "500");
                mOut.put("message", "Lỗi khi xuất hợp đồng: " + e.getMessage());
                sendResponse(ctx, 500, mOut);
            }
        }, false, null);
    }

    // hàm export contract by template for API, just create file docx and return path file exported
    public static void exportContractByTemplateForAPI(RoutingContext ctx) {
       
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int contractId = Integer.parseInt(request.getParam("id") == null ? "0" : request.getParam("id"));
                String contractCode = request.getParam("contractCode") == null ? BLANK : request.getParam("contractCode");
                boolean bilingual = request.getParam("bilingual") == null ? false : Boolean.parseBoolean(request.getParam("bilingual"));
                int idContractVersion = request.getParam("idContractVersion") == null ? 0 : Integer.parseInt(request.getParam("idContractVersion"));
                
                // Lấy thông tin hợp đồng
                ContractOriginal contractOriginal;
                //lấy subcontract nếu là export công văn phụ lục
                //kiểm tra nếu contractCode là 1 trong các mã sau thì lấy contract detail: CV-DCHD hoặc PL02_v2
                
                if("CV-DCHD".equals(contractCode) || "PL02_v2".equals(contractCode)|| "PL-K_v2".equals(contractCode)){
                    contractOriginal = ContractDao.getSubContractDetailById(contractId);
                }else{
                    //lấy contract detail nếu là export hợp đồng
                    contractOriginal = ContractDao.getContractDetailById(contractId);
                }
                
                // get file name and form from db
                Map<String, String> mFileTemplate = ContractDao.getTemplateByContractId(contractOriginal.getIdTemplate(), bilingual);
                if (mFileTemplate.get("fileName") != null && !mFileTemplate.get("fileName").isEmpty()) {
                    // Tạo dữ liệu cho hợp đồng
                    ContractGenerator contractGenerator = new ContractGenerator(contractOriginal);
                    Map<String, String> mDataReplace = contractGenerator.generateParam(bilingual);
                    
                    // Chuẩn bị dữ liệu đầu vào
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("N_ID_VERSION", String.valueOf(contractOriginal.getIdVersion()));
                    mIn.put("N_ID_CONTRACT", String.valueOf(contractOriginal.getContractId()));
                    mIn.put("N_ID_CONTRACT_VERSION", String.valueOf(idContractVersion));
                    mIn.put("N_ID_PARTNER", String.valueOf(contractOriginal.getPartnerId()));
                    mIn.putAll(mFileTemplate);
                    mIn.put("mDataReplace", mDataReplace);
                    mIn.put("hide", contractGenerator.getHide());
                    
                    // Tạo tên file với định dạng thời gian
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    String formattedDate = now.format(formatter);
                    String fileName = mFileTemplate.get("fileName") + "_" + formattedDate;
                    String fileHashName = Convert.hash(fileName);

                    // Chuẩn bị thông tin request
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    requestData.put(ParamsPool.FILE_EXT, ".docx");
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);

                    // Tạo file Word từ template
                    String template = mFileTemplate.get("templatePath");
                    String exportFileName = Config.getString("export_contract_api_location", "/opt/onesched/export/contract/") + mIn.get("N_ID_CONTRACT") + (bilingual ? "_bilingual" : "") + ".docx";
                    
                    logger.log(Level.INFO, "-------------START EXPORT CONTRACT DOCX FILE-----------------------");
                    logger.log(Level.INFO, "TEMPLATE FILE: " + template);
                    
                    try (InputStream templateFileStream = new FileInputStream(template);
                         OutputStream outputStream = new FileOutputStream(exportFileName)) {
                        
                        XWPFDocument document = new XWPFDocument(templateFileStream);
                        List<Map<String, String>> listHideData = contractGenerator.getHide();
                        
                        // Xử lý ẩn các phần trong template
                        for (Map<String, String> hideData : listHideData) {
                            String type = hideData.get("type");
                            if ("table".equals(type)) {
                                document = ExcelReportGenerator.removeTableRowWithText(document, hideData.get("key"));
                            } else if ("row".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphWithText(document, hideData.get("key"));
                            } else if ("tag".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphsBetweenTags(document, hideData.get("start"), hideData.get("end"));
                            } else if ("line_in_table".equals(type)) {
                                document = ExcelReportGenerator.removeLineWithTextInTable(document, hideData.get("key"));
                            } else if ("full_table".equals(type)) {
                                document = ExcelReportGenerator.removeTableWithText(document, hideData.get("key"));
                            }
                        }
                        
                        // Thay thế nội dung trong template
                        document = ExcelReportGenerator.replaceTextInDocument(document, mDataReplace);
                        
                        // Tạo và thêm mã QR vào footer
                        String b = bilingual ? "y" : "n";
                        String qrCodeText = mIn.get("N_ID_PARTNER") + "?c=" + mIn.get("N_ID_CONTRACT") + "&v=" + mIn.get("N_ID_CONTRACT_VERSION") + "&b=" + b;
                        byte[] qrCodeImage = ExcelReportGenerator.generateBarcodeImageByte(qrCodeText);
                        ExcelReportGenerator.replaceTextWithBarcodeInFooter(document, "qrcode", qrCodeImage);
                        
                        // Ghi file
                        document.write(outputStream);
                        logger.log(Level.INFO, "EXPORT FILE COMPLETED: " + exportFileName);
                    }

                    // Trả về kết quả
                    Map<String, Object> mOut = new HashMap<>();
                    mOut.put("nerror", "200");
                    mOut.put("exportFileName", exportFileName);
                    mOut.put("mIn", mIn);
                    sendResponse(ctx, 200, mOut);
                } else {
                    Map<String, Object> mOut = new HashMap<>();
                    mOut.put("nerror", "500");
                    mOut.put("message", "Không tìm thấy template hợp đồng");
                    sendResponse(ctx, 201, mOut);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE ERROR: ", e);
                Map<String, Object> mOut = new HashMap<>();
                mOut.put("nerror", "500");
                mOut.put("message", "Lỗi khi xuất hợp đồng: " + e.getMessage());
                sendResponse(ctx, 500, mOut);
            } 
        }, false, null);
    }

    // hàm export contract by template for API, just create file docx and let download file from url
    public static void exportContractByTemplateForAPI2(RoutingContext ctx) {
        
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int contractId = Integer.parseInt(request.getParam("id") == null ? "0" : request.getParam("id"));
                String contractCode = request.getParam("contractCode") == null ? BLANK : request.getParam("contractCode");
                boolean bilingual = request.getParam("bilingual") == null ? false : Boolean.parseBoolean(request.getParam("bilingual"));
                int idContractVersion = request.getParam("idContractVersion") == null ? 0 : Integer.parseInt(request.getParam("idContractVersion"));
                
                // Lấy thông tin hợp đồng
                ContractOriginal contractOriginal;
                //lấy subcontract nếu là export công văn phụ lục
                //kiểm tra nếu contractCode là 1 trong các mã sau thì lấy contract detail: CV-DCHD hoặc PL02_v2
                
                if("CV-DCHD".equals(contractCode) || "PL02_v2".equals(contractCode)|| "PL-K_v2".equals(contractCode)){
                    contractOriginal = ContractDao.getSubContractDetailById(contractId);
                }else{
                    //lấy contract detail nếu là export hợp đồng
                    contractOriginal = ContractDao.getContractDetailById(contractId);
                }
                
                String exportFileName = "";
                final String fileName = String.valueOf(contractOriginal.getContractId()) + (bilingual ? "_bilingual" : "");
                final String fileExt = "docx";

                // get file name and form from db
                Map<String, String> mFileTemplate = ContractDao.getTemplateByContractId(contractOriginal.getIdTemplate(), bilingual);
                if (mFileTemplate.get("fileName") != null && !mFileTemplate.get("fileName").isEmpty()) {
                    // Tạo dữ liệu cho hợp đồng
                    ContractGenerator contractGenerator = new ContractGenerator(contractOriginal);
                    Map<String, String> mDataReplace = contractGenerator.generateParam(bilingual);
                    
                    // Chuẩn bị dữ liệu đầu vào
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("N_ID_VERSION", String.valueOf(contractOriginal.getIdVersion()));
                    mIn.put("N_ID_CONTRACT", String.valueOf(contractOriginal.getContractId()));
                    mIn.put("N_ID_CONTRACT_VERSION", String.valueOf(idContractVersion));
                    mIn.put("N_ID_PARTNER", String.valueOf(contractOriginal.getPartnerId()));
                    mIn.putAll(mFileTemplate);
                    mIn.put("mDataReplace", mDataReplace);
                    mIn.put("hide", contractGenerator.getHide());
                    
                    // Tạo tên file với định dạng thời gian
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    String formattedDate = now.format(formatter);
                    String fileNameRequest = mFileTemplate.get("fileName") + "_" + formattedDate;
                    String fileHashName = Convert.hash(fileName);

                    // Chuẩn bị thông tin request
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    requestData.put(ParamsPool.FILE_EXT, ".docx");
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileNameRequest);

                    // Tạo file Word từ template
                    String template = mFileTemplate.get("templatePath");
                    exportFileName = Config.getString("export_contract_api_location", "/opt/onesched/export/contract/") + mIn.get("N_ID_CONTRACT") + (bilingual ? "_bilingual" : "") + ".docx";
                    logger.log(Level.INFO, "-------------START EXPORT CONTRACT DOCX FILE-----------------------");
                    logger.log(Level.INFO, "TEMPLATE FILE: " + template);
                    
                    try (InputStream templateFileStream = new FileInputStream(template);
                        OutputStream outputStream = new FileOutputStream(exportFileName)) {
                        
                        XWPFDocument document = new XWPFDocument(templateFileStream);
                        List<Map<String, String>> listHideData = contractGenerator.getHide();
                        
                        // Xử lý ẩn các phần trong template
                        for (Map<String, String> hideData : listHideData) {
                            String type = hideData.get("type");
                            if ("table".equals(type)) {
                                document = ExcelReportGenerator.removeTableRowWithText(document, hideData.get("key"));
                            } else if ("row".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphWithText(document, hideData.get("key"));
                            } else if ("tag".equals(type)) {
                                document = ExcelReportGenerator.removeParagraphsBetweenTags(document, hideData.get("start"), hideData.get("end"));
                            } else if ("line_in_table".equals(type)) {
                                document = ExcelReportGenerator.removeLineWithTextInTable(document, hideData.get("key"));
                            } else if ("full_table".equals(type)) {
                                document = ExcelReportGenerator.removeTableWithText(document, hideData.get("key"));
                            }
                        }
                        
                        // Thay thế nội dung trong template
                        document = ExcelReportGenerator.replaceTextInDocument(document, mDataReplace);
                        
                        // Tạo và thêm mã QR vào footer
                        String b = bilingual ? "y" : "n";
                        String qrCodeText = mIn.get("N_ID_PARTNER") + "?c=" + mIn.get("N_ID_CONTRACT") + "&v=" + mIn.get("N_ID_CONTRACT_VERSION") + "&b=" + b;
                        byte[] qrCodeImage = ExcelReportGenerator.generateBarcodeImageByte(qrCodeText);
                        ExcelReportGenerator.replaceTextWithBarcodeInFooter(document, "qrcode", qrCodeImage);
                        
                        // Ghi file
                        document.write(outputStream);
                        logger.log(Level.INFO, "EXPORT FILE COMPLETED: " + exportFileName);
                    }

                    // // Trả về kết quả
                    // Map<String, Object> mOut = new HashMap<>();
                    // mOut.put("nerror", "200");
                    // mOut.put("exportFileName", exportFileName);
                    // mOut.put("mIn", mIn);
                    // sendResponse(ctx, 200, mOut);
                } else {
                    logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE FOR API2 ERROR: ", "Không tìm thấy template hợp đồng");
                    // Map<String, Object> mOut = new HashMap<>();
                    // mOut.put("nerror", "500");
                    // mOut.put("message", "Không tìm thấy template hợp đồng");
                    // sendResponse(ctx, 201, mOut);
                }

                //exportFileName = "/opt/onesched/export/contract/1144192.docx"
                Path requestPath = FileSystems.getDefault().getPath(exportFileName).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                        Map<String, Object> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(java.nio.file.Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                // logger.info("Download Success");
                            } else {
                                // logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT BY TEMPLATE FOR API2 ERROR: ", e);
                Map<String, Object> mOut = new HashMap<>();
                mOut.put("nerror", "500");
                mOut.put("message", "Lỗi khi xuất hợp đồng: " + e.getMessage());
                sendResponse(ctx, 500, mOut);
            } 
        }, false, null);
    }

    public static void exportContractPKL(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                // task

                Map mapResponse = new HashMap<>();
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<Object, Object> mIn = new HashMap<>();
                JsonArray parentTableMerchant = bodyJson.getJsonArray("parentTableMerchant");
                JsonArray subTableMerchant = bodyJson.getJsonArray("subTableMerchant");
                JsonArray subTableShopifyMerchant = bodyJson.getJsonArray("subTableShopifyMerchant");
                JsonArray subTableNonShopifyMerchant = bodyJson.getJsonArray("subTableNonShopifyMerchant");
                JsonArray subTableNoMerchant01 = bodyJson.getJsonArray("subTableNoMerchant01");
                JsonArray subTableFee = bodyJson.getJsonArray("subTableFee");
                JsonArray merchantTablePause = bodyJson.getJsonArray("merchantTablePause");
                String contractCode = bodyJson.getString("contractCode");
                int contractId = Integer.parseInt(bodyJson.getString("id"));
                ContractOriginal contract = ContractDao.getSubContractDetailById(contractId);
                String templateHtml = contract.getTemplate();

                List<Map<Object, Object>> subDataMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataNoMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataParentTable = new ArrayList<>();
                List<Map<Object, Object>> subDataFeeTable = new ArrayList<>();
                List<Map<Object, Object>> subDataPauseTable = new ArrayList<>();
                String merchantIds = "";
                List<Map<Object, Object>> subDataShopifyMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataNonShopifyMerchant = new ArrayList<>();

                if (!subTableMerchant.isEmpty()) {
                    for (int i = 0; i < subTableMerchant.size(); i++) {
                        JsonObject jsonobject = subTableMerchant.getJsonObject(i);
                        if (contractCode.startsWith("HD13_TH") || contractCode.startsWith("PL-K") || contractCode.startsWith("HD13_Shopify")) {
                            mIn.put("merchantnamesTbl", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                            mIn.put("merchantIdTbl", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                            mIn.put("accountNumberTbl", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                            mIn.put("accountnamesTbl", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                            mIn.put("bankTbl", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                            break;
                        } else {
                            Map<Object, Object> m = new HashMap<>();
                            merchantIds += jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "; " : jsonobject.getString("merchantIDS") + "; ";
                            m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                            m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                            m.put("data02", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                            m.put("data03", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                            m.put("data04", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                            subDataMerchant.add(m);
                        }
                    }
                }

                // du lieu bang merchant vs 3 truong du lieu
                if (!subTableNoMerchant01.isEmpty()) {
                    for (int i = 0; i < subTableNoMerchant01.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableNoMerchant01.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                        m.put("data02", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                        m.put("data03", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                        subDataNoMerchant.add(m);
                    }
                }

                if (!parentTableMerchant.isEmpty()) {
                    for (int i = 0; i < parentTableMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = parentTableMerchant.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                        m.put("data03", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                        m.put("data04", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                        subDataParentTable.add(m);
                    }
                }

                // du lieu bang phu luc tam ngung
                if (!merchantTablePause.isEmpty()) {
                    for (int i = 0; i < merchantTablePause.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = merchantTablePause.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchantName") == null || jsonobject.getString("merchantName").isEmpty() ? "" : jsonobject.getString("merchantName"));
                        m.put("data02", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data03", jsonobject.getString("partnerNumber") == null || jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataPauseTable.add(m);
                    }
                }

                // du lieu bang phu luc phi shopify
                if (!subTableShopifyMerchant.isEmpty()) {
                    for (int i = 0; i < subTableShopifyMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableShopifyMerchant.getJsonObject(i);
                        // m.put("id", jsonobject.getString("merchantId") == null ||
                        // jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                        // m.put("data03", jsonobject.getString("partnerNumber") == null ||
                        // jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataShopifyMerchant.add(m);
                    }
                }
                if (!subTableNonShopifyMerchant.isEmpty()) {
                    for (int i = 0; i < subTableNonShopifyMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableNonShopifyMerchant.getJsonObject(i);
                        // m.put("id", jsonobject.getString("merchantId") == null ||
                        // jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                        // m.put("data03", jsonobject.getString("partnerNumber") == null ||
                        // jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataNonShopifyMerchant.add(m);
                    }
                }

                // du lieu bang phi tra gop
                if (!subTableFee.isEmpty()) {
                    for (int i = 0; i < subTableFee.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableFee.getJsonObject(i);
                        String shortName = jsonobject.getString("shortName");
                        m.put((shortName + "a_Tbl"), jsonobject.getString("threeMonths") == null || "".equals(jsonobject.getString("threeMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("threeMonths") : (jsonobject.getString("threeMonths") + "%"));
                        m.put((shortName + "b_Tbl"), jsonobject.getString("sixMonths") == null || "".equals(jsonobject.getString("sixMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("sixMonths") : (jsonobject.getString("sixMonths") + "%"));
                        m.put((shortName + "c_Tbl"), jsonobject.getString("nineMonths") == null || "".equals(jsonobject.getString("nineMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("nineMonths") : (jsonobject.getString("nineMonths") + "%"));
                        m.put((shortName + "d_Tbl"), jsonobject.getString("twelveMonths") == null || "".equals(jsonobject.getString("twelveMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("twelveMonths") : (jsonobject.getString("twelveMonths") + "%"));
                        m.put((shortName + "e_Tbl"), jsonobject.getString("fifteenMonths") == null || "".equals(jsonobject.getString("fifteenMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("fifteenMonths") : (jsonobject.getString("fifteenMonths") + "%"));
                        m.put((shortName + "f_Tbl"), jsonobject.getString("eighteenMonths") == null || "".equals(jsonobject.getString("eighteenMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("eighteenMonths") : (jsonobject.getString("eighteenMonths") + "%"));
                        m.put((shortName + "g_Tbl"), jsonobject.getString("twentyFourMonths") == null || "".equals(jsonobject.getString("twentyFourMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("twentyFourMonths") : (jsonobject.getString("twentyFourMonths") + "%"));
                        m.put((shortName + "h_Tbl"), jsonobject.getString("thirtySixMonths") == null || "".equals(jsonobject.getString("thirtySixMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("thirtySixMonths") : (jsonobject.getString("thirtySixMonths") + "%"));
                        subDataFeeTable.add(m);
                    }
                }

                // convert number to money
                String khoanDamBaoInput = bodyJson.getString("khoanDamBaoInput");
                String khoanDamBaoInputMoney = vn.onepay.portal.Util.convertStringToInt(khoanDamBaoInput);
                String ************************ = null;
                if (!khoanDamBaoInputMoney.equals("")) {
                    List<String> listData = ConvertMoneyNumberToString.readNumberToMoney(khoanDamBaoInputMoney);
                    StringBuilder stringtemp = new StringBuilder();
                    if (!listData.isEmpty()) {
                        for (int i = 0; i < listData.size(); i++) {
                            String element = listData.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtemp.append(element);
                            stringtemp.append(" ");
                        }
                    }
                    ************************ = stringtemp.toString() + "đồng";
                } else {
                    ************************ = "……………";
                }

                String timeAdvance = bodyJson.getString("timeAdvance");
                String timeAdvanceOneConvert = null;
                String timeAdvanceTwoConvert = null;

                if (null != timeAdvance && !timeAdvance.isEmpty() && ("t1".equalsIgnoreCase(timeAdvance) || "t2".equalsIgnoreCase(timeAdvance))) {
                    Integer numberOne = null;
                    if ("t1".equalsIgnoreCase(timeAdvance)) {
                        numberOne = 1;
                        timeAdvanceOneConvert = "01";
                        timeAdvanceTwoConvert = "02";
                    } else if ("t2".equalsIgnoreCase(timeAdvance)) {
                        numberOne = 2;
                        timeAdvanceOneConvert = "02";
                        timeAdvanceTwoConvert = "03";
                    }
                    Integer numberTwo = numberOne + 1;

                    List<String> listDataOne = ConvertMoneyNumberToString.readNumberToMoney(String.valueOf(numberOne));
                    StringBuilder stringtempOne = new StringBuilder();
                    if (!listDataOne.isEmpty()) {
                        for (int i = 0; i < listDataOne.size(); i++) {
                            String element = listDataOne.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtempOne.append(element);
                        }
                    }

                    List<String> listDataTwo = ConvertMoneyNumberToString.readNumberToMoney(String.valueOf(numberTwo));
                    StringBuilder stringtempTwo = new StringBuilder();
                    if (!listDataTwo.isEmpty()) {
                        for (int i = 0; i < listDataTwo.size(); i++) {
                            String element = listDataTwo.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtempTwo.append(element);
                        }
                    }
                    timeAdvanceOneConvert += " (" + stringtempOne.toString().toLowerCase() + ")";
                    timeAdvanceTwoConvert += " (" + stringtempTwo.toString().toLowerCase() + ")";
                }

                // mIn.put("uyQuyenTxt", uyQuyenTxt);
                mIn.put("contractCode", contractCode);
                mIn.put("contractName", bodyJson.getString("contractName"));
                mIn.put("contractIdTxt", bodyJson.getString("id"));

                String businessName = bodyJson.getString("businessName");
                String otherInfo = bodyJson.getString("otherInfo");
                String shortName = bodyJson.getString("shortName");
                mIn.put("businessNameTxt", businessName);
                mIn.put("businessnamesTxt", businessName);
                mIn.put("signatureDateTxt", (null != bodyJson.getString("signatureDate") && !bodyJson.getString("signatureDate").isEmpty()) ? bodyJson.getString("signatureDate") : "……/……/…………");
                mIn.put("rangeDateTxt", bodyJson.getString("rangeDate"));
                mIn.put("contractNumberTxt", (null != bodyJson.getString("contractNumber") && !bodyJson.getString("contractNumber").isEmpty()) ? bodyJson.getString("contractNumber") : "……………");

                // parent Data
                if (contractCode.equals("BBNT-HD13")) {
                    mIn.put("MerchantIdTxt", merchantIds.substring(0, merchantIds.length() - 2));
                } else {
                    mIn.put("MerchantIdTxt", merchantIds);
                }
                mIn.put("parentContractFullNameTxt", bodyJson.getString("parentContractFullName"));
                mIn.put("parentContractNumberTxt", bodyJson.getString("parentContractNumber"));
                mIn.put("parentSignatureDateTxt", bodyJson.getString("parentSignatureDate"));
                mIn.put("parentPtTamUngTxt", bodyJson.getString("parentPtTamUng"));
                mIn.put("parentTgTamUngTxt", bodyJson.getString("parentTgTamUng"));
                mIn.put("parentStkGiaiKhoanhTxt", bodyJson.getString("parentStkGiaiKhoanh"));
                mIn.put("parentCardTransactionFeeTxt", bodyJson.getString("parentCardTransactionFee"));
                mIn.put("parentFeeForCardTxt", bodyJson.getString("parentFeeForCard"));
                mIn.put("parentAccountBankTxt", bodyJson.getString("parentAccountBank"));

                // mIn.put("thuPhiKhacTxt", bodyJson.getString("inputHinhThucThuPhiKhac"));
                mIn.put("noiDungThayDoiTxt", bodyJson.getString("noiDungThayDoi"));
                mIn.put("dayApproveTxt", bodyJson.getString("dayApprove").equals("") ? "……………" : bodyJson.getString("dayApprove"));
                mIn.put("uyQuyenTxt", bodyJson.getString("uyQuyen"));
                mIn.put("inputKyQuyKhacTxt", bodyJson.getString("inputKyQuyKhac"));
                mIn.put("inputTgTamUngKhacTxt", bodyJson.getString("inputTgTamUngKhac"));
                mIn.put("inputTgTamUngKhacKetThucPhienTxt", bodyJson.getString("inputTgTamUngKhacKetThucPhien"));

                mIn.put("otherInfoTxt", otherInfo);
                mIn.put("otherInfoaTxt", "(" + otherInfo + ")");
                mIn.put("otherInfobTxt", "Mức phí trên " + bodyJson.getString("otherInfo"));
                mIn.put("peopleIdTxt", bodyJson.getString("peopleId"));
                mIn.put("carrerTxt", bodyJson.getString("carrer"));
                mIn.put("branchTxt", bodyJson.getString("branch"));
                mIn.put("shortNameTxt", shortName);
                mIn.put("shortnamesTxt", shortName);
                mIn.put("addressBusinessTxt", bodyJson.getString("addressBusiness"));
                mIn.put("addressOfficeTxt", bodyJson.getString("addressOffice"));
                mIn.put("emailTxt", bodyJson.getString("email"));
                mIn.put("phoneTxt", bodyJson.getString("phone"));
                mIn.put("websiteTxt", bodyJson.getString("website"));
                mIn.put("numberBusinessTxt", bodyJson.getString("numberBusiness"));
                mIn.put("positionTxt", bodyJson.getString("position"));
                mIn.put("monthFeeTxt", bodyJson.getString("monthFee").equals("Miễn phí") ? "Miễn phí" : (convertFormatNumberString(bodyJson.getString("monthFee")) + " VND"));
                mIn.put("cardTransactionFeeTxt", bodyJson.getString("cardTransactionFee"));
                mIn.put("vietNamCardTxt", bodyJson.getString("vietNamCard"));
                mIn.put("feeForCardTxt", bodyJson.getString("feeForCard"));
                mIn.put("feeForMobileTxt", bodyJson.getString("feeForMobile"));
                mIn.put("otherCardTxt", bodyJson.getString("otherCard"));
                mIn.put("ptTamUngTxt", bodyJson.getString("ptTamUng"));
                mIn.put("ptTamUngMoiTxt", bodyJson.getString("ptTamUngMoi"));
                mIn.put("tgTamUngTxt", bodyJson.getString("tgTamUng"));
                mIn.put("tgTamUngSelectionTxt", bodyJson.getString("timeAdvance"));// Set Thời gian tạm ứng config
                mIn.put("khoanDamBaoTxt", bodyJson.getString("khoanDamBao"));
                mIn.put("khoanDamBaoMoiTxt", bodyJson.getString("khoanDamBaoMoi"));
                mIn.put("kyHanFDTxt", bodyJson.getString("kyHanFD"));
                mIn.put("stkGiaiKhoanhTxt", bodyJson.getString("stkGiaiKhoanh"));
                mIn.put("accountBankTxt", bodyJson.getString("accountBank"));
                mIn.put("alertEmailAddressTxt", bodyJson.getString("alertEmailAddress"));
                mIn.put("detailEmailAddressTxt", bodyJson.getString("detailEmailAddress"));
                mIn.put("cardTypeTxt", bodyJson.getString("cardType"));
                mIn.put("changeContentTxt", bodyJson.getString("changeContent"));

                mIn.put("internationalCard01Txt", bodyJson.getString("internationalCard01"));
                mIn.put("domesticCard01Txt", bodyJson.getString("domesticCard01"));
                mIn.put("domesticCardToken01Txt", bodyJson.getString("domesticCardToken01"));
                mIn.put("internationalCard02Txt", bodyJson.getString("internationalCard02"));
                mIn.put("domesticCard02Txt", bodyJson.getString("domesticCard02"));
                mIn.put("domesticCardToken02Txt", bodyJson.getString("domesticCardToken02"));
                mIn.put("internationalCard03Txt", bodyJson.getString("internationalCard03"));
                mIn.put("domesticCard03Txt", bodyJson.getString("domesticCard03"));
                mIn.put("internationalCard04Txt", bodyJson.getString("internationalCard04"));
                mIn.put("domesticCard04Txt", bodyJson.getString("domesticCard04"));
                mIn.put("visaCardTxt", bodyJson.getString("visaCard"));
                mIn.put("masterCardTxt", bodyJson.getString("masterCard"));
                mIn.put("jcbCardTxt", bodyJson.getString("jcbCard"));
                mIn.put("americanCardTxt", bodyJson.getString("americanCard"));
                mIn.put("kyQuyTxt", bodyJson.getString("kyQuyValue"));
                mIn.put("khoanhGiuTxt", bodyJson.getString("khoanhGiuValue"));

                mIn.put("hinhThucBaoCoTxt", bodyJson.getString("hinhThucBaoCo01"));
                mIn.put("hinhThucThuPhiNgayTxt", bodyJson.getString("hinhThucThuPhiTheoNgay01"));
                mIn.put("hinhThucThuPhiThangTxt", bodyJson.getString("hinhThucThuPhiTheoThang01"));
                mIn.put("accountNameBaoCoTxt", bodyJson.getString("accountNameBaoCo"));
                mIn.put("accountNameThuPhiTxt", bodyJson.getString("accountNameThuPhi"));
                mIn.put("accountNumberBaoCoTxt", bodyJson.getString("accountNumberBaoCo"));
                mIn.put("accountNumberThuPhiTxt", bodyJson.getString("accountNumberThuPhi"));
                mIn.put("bankBaoCoTxt", bodyJson.getString("bankBaoCo"));
                mIn.put("bankThuPhiTxt", bodyJson.getString("bankThuPhi"));
                mIn.put("openByBankTxt", bodyJson.getString("openByBank"));
                mIn.put("careerTxt", (null != bodyJson.getString("career") && !bodyJson.getString("career").isEmpty() && !bodyJson.getString("career").isBlank()) ? bodyJson.getString("career").trim() : ".......");
                mIn.put("timeAdvanceOneTbl", (null != timeAdvanceOneConvert && !timeAdvanceOneConvert.isEmpty()) ? timeAdvanceOneConvert : ".......");
                mIn.put("timeAdvanceTwoTbl", (null != timeAdvanceTwoConvert && !timeAdvanceTwoConvert.isEmpty()) ? timeAdvanceTwoConvert : ".......");

                mIn.put("monthFeeTbl", bodyJson.getString("monthFee").equals("...") ? "Không áp dụng" : bodyJson.getString("monthFee"));
                mIn.put("feePaymentDomesticAndAppTbl", bodyJson.getString("feePaymentDomesticAndApp"));
                mIn.put("feePercentQrMobileTbl", (null == bodyJson.getString("percentQrMobile") || bodyJson.getString("percentQrMobile").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMobile") + "");
                mIn.put("feePercentQrGrabTbl", (null == bodyJson.getString("percentQrGrab") || bodyJson.getString("percentQrGrab").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrGrab"));
                mIn.put("feePercentQrShopeeTbl", (null == bodyJson.getString("percentQrShopee") || bodyJson.getString("percentQrShopee").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrShopee"));
                mIn.put("feePercentQrZaloTbl", (null == bodyJson.getString("percentQrZalo") || bodyJson.getString("percentQrZalo").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrZalo"));
                mIn.put("feePercentQrMoMoTbl", (null == bodyJson.getString("percentQrMoMo") || bodyJson.getString("percentQrMoMo").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMoMo"));
                mIn.put("feePercentQrOtherTbl", (null == bodyJson.getString("percentQrOther") || bodyJson.getString("percentQrOther").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrOther"));
                mIn.put("approveCardType01InternationalTbl", bodyJson.getString("approveCardType01International"));
                mIn.put("americanExpress01InternationalTbl", bodyJson.getString("americanExpress01International"));
                mIn.put("approveCardType02InternationalTbl", bodyJson.getString("approveCardType02International"));
                mIn.put("americanExpress02InternationalTbl", bodyJson.getString("americanExpress02International"));
                mIn.put("************************", ************************);
                mIn.put("keepPercentTxt", (null != bodyJson.getString("keepPercent") && !bodyJson.getString("keepPercent").isEmpty()) ? bodyJson.getString("keepPercent") + "%" : ".......");
                mIn.put("authorizedPersonNameTxt", bodyJson.getString("authorizedPersonName"));
                mIn.put("authorizedPersonIdTxt", bodyJson.getString("authorizedPersonId"));
                mIn.put("authorizedIssuedDateTxt", bodyJson.getString("authorizedIssuedDate"));
                mIn.put("authorizedIssuedByTxt", bodyJson.getString("authorizedIssuedBy"));
                mIn.put("accountNameTxt", bodyJson.getString("accountName"));
                mIn.put("accountNumberTxt", bodyJson.getString("accountNumber"));
                mIn.put("authorizationPeriodFromTxt", bodyJson.getString("authorizationPeriodFrom"));
                mIn.put("authorizationPeriodToTxt", bodyJson.getString("authorizationPeriodTo"));
                mIn.put("authorizedBirthDateTxt", bodyJson.getString("authorizedBirthDate"));
                mIn.put("authorizedAddressTxt", bodyJson.getString("authorizedAddress"));
                mIn.put("authorizationNumberTxt", bodyJson.getString("authorizationNumber"));
                mIn.put("parentMerchantIdTxt", bodyJson.getString("parentMerchantIdTxt"));

                // --------------------------------------------------------------------------------------

                String feeService = bodyJson.getString("feeService");
                String registerFee = bodyJson.getString("registerFee");
                String danhXung = bodyJson.getString("danhXung");
                String signaturer = bodyJson.getString("signaturer");
                String feeTransInternational = bodyJson.getString("feeTransInternational");
                String feeTransDomestic = bodyJson.getString("feeTransDomesticAndApp");
                String feeTransApp = bodyJson.getString("feeApp");

                mIn.put("registerFeeTxt", registerFee.equals("...") ? "Không áp dụng" : (convertFormatNumberString(registerFee) + " VND"));
                mIn.put("signaturerTxt", danhXung == null || danhXung.equals("") ? signaturer : danhXung);
                mIn.put("signatureR", signaturer);

                if (khoanDamBaoInput != null && !khoanDamBaoInput.equals("") && !khoanDamBaoInput.equals("...") && contractCode.startsWith("HD13_TH"))
                    khoanDamBaoInput = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(khoanDamBaoInput));

                if (feeService.equals("normalFee") && feeTransInternational != null && !feeTransInternational.equals("") && !feeTransInternational.equals("Không áp dụng") && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransInternational.equals("...")) {
                        feeTransInternational = convertFormatNumberString(feeTransInternational);
                        feeTransInternational = feeTransInternational + " VND/ giao dịch";
                    } else {
                        feeTransInternational = "Không áp dụng";
                    }
                }
                if (feeService.equals("normalFee") && feeTransDomestic != null && !feeTransDomestic.equals("") && !feeTransDomestic.equals("Không áp dụng") && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransDomestic.equals("...")) {
                        feeTransDomestic = convertFormatNumberString(feeTransDomestic);
                        feeTransDomestic = feeTransDomestic + " VND/ giao dịch";
                    } else {
                        feeTransDomestic = "Không áp dụng";
                    }
                }

                if (feeService.equals("normalFee") && feeTransApp != null && !feeTransApp.equals("")
                        && !feeTransApp.equals("Không áp dụng")
                        && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransApp.equals("...")) {
                        if (feeTransApp.matches("\\d+(\\.\\d+)?")) {
                            feeTransApp = convertFormatNumberString(feeTransApp.replaceAll("(\\.)*+(\\,)*", ""));
                        }
                        // feeTransApp = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(feeTransApp));
                        feeTransApp = feeTransApp + " VND/ giao dịch";
                    } else {
                        feeTransApp = "Không áp dụng";
                    }
                }
                if (registerFee != null && !registerFee.equals("") && !registerFee.equals("...") && contractCode.startsWith("HD13_TH")) {
                    registerFee = convertFormatNumberString(registerFee) + " VND";
                }
                // Set Thời gian tạm ứng config
                if (bodyJson.getString("timeAdvance").equals("t1")) {
                    mIn.put("advanceTime1Txt", "01 (một)");
                    mIn.put("advanceTime2Txt", "02 (hai)");
                } else {
                    mIn.put("advanceTime1Txt", "02 (hai)");
                    mIn.put("advanceTime2Txt", "03 (ba)");
                }
                mIn.put("khoanDamBaoInputTxt", (null != khoanDamBaoInput && !khoanDamBaoInput.isEmpty()) ? convertFormatNumberString(khoanDamBaoInput) : ".......");
                mIn.put("registerFeeTbl", registerFee.equals("...") ? "Không áp dụng" : registerFee);
                mIn.put("feeTransInternationalTbl", convertFormatNumberString(feeTransInternational) + " VND");
                mIn.put("feeTransDomesticAndAppTbl", convertFormatNumberString(feeTransDomestic) + " VND");
                mIn.put("feeTransAppTbl", convertFormatNumberString(feeTransApp) + " VND");

                String checked = "☒";
                String unChecked = "☐";
                if (bodyJson.getString("americanExpressBox").isEmpty() && bodyJson.getString("masterCardBox").isEmpty() && bodyJson.getString("visaBox").isEmpty() && bodyJson.getString("amexBox").isEmpty()
                        && bodyJson.getString("jcbBox").isEmpty() && bodyJson.getString("unionBox").isEmpty()) {
                    mIn.put("InternationalBox", unChecked);
                } else {
                    mIn.put("InternationalBox", checked);
                }
                mIn.put("appMobileBox", bodyJson.getString("appMobileBox").isEmpty() ? unChecked : checked);
                mIn.put("otherBankBox", bodyJson.getString("otherBankBox").isEmpty() ? unChecked : checked);
                mIn.put("visaBox", bodyJson.getString("visaBox").isEmpty() ? unChecked : checked);
                mIn.put("masterCardBox", bodyJson.getString("masterCardBox").isEmpty() ? unChecked : checked);
                mIn.put("amexBox", bodyJson.getString("amexBox").isEmpty() ? unChecked : checked);
                mIn.put("jcbBox", bodyJson.getString("jcbBox").isEmpty() ? unChecked : checked);
                mIn.put("unionBox", bodyJson.getString("unionBox").isEmpty() ? unChecked : checked);
                mIn.put("domesticBox", bodyJson.getString("domesticBox").isEmpty() ? unChecked : checked);
                mIn.put("kyQuyBox", bodyJson.getString("kyQuyBox").isEmpty() ? unChecked : checked);
                mIn.put("khoanhGiuBox", bodyJson.getString("khoanhGiuBox").isEmpty() ? unChecked : checked);
                mIn.put("migsBox", bodyJson.getString("migsBox").isEmpty() ? unChecked : checked);
                mIn.put("mpgsBox", bodyJson.getString("mpgsBox").isEmpty() ? unChecked : checked);
                mIn.put("cyberSourceBox", bodyJson.getString("cyberSourceBox").isEmpty() ? unChecked : checked);
                mIn.put("secureBox", bodyJson.getString("secureBox").isEmpty() ? unChecked : checked);
                mIn.put("americanExpressBox", bodyJson.getString("americanExpressBox").isEmpty() ? unChecked : checked);
                mIn.put("approveDomesticCardBox", bodyJson.getString("approveDomesticCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveInternationalCardBox", bodyJson.getString("approveInternationalCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveInstallmentBox", bodyJson.getString("approveInstallmentBox").isEmpty() ? unChecked : checked);
                mIn.put("approveOnepayDomesticCardBox", bodyJson.getString("approveOnepayDomesticCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveOnepayMobileAppBox", bodyJson.getString("approveOnepayMobileAppBox").isEmpty() ? unChecked : checked);
                mIn.put("approveBnblBox", bodyJson.getString("approveBnblBox").isEmpty() ? unChecked : checked);
                mIn.put("subDataMerchant", subDataMerchant);
                mIn.put("subDataNoMerchant", subDataNoMerchant);
                mIn.put("subDataParentTable", subDataParentTable);
                mIn.put("subDataFeeTable", subDataFeeTable);
                mIn.put("subDataPauseTable", subDataPauseTable);
                mIn.put("subShopifyData", subDataShopifyMerchant);
                mIn.put("subNonShopifyData", subDataNonShopifyMerchant);


                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                // Initial file information
                String fileName = (contractCode.equals("PL-K") ? "Phu luc dieu chinh thong tin hop dong" : contractCode);

                // xu ly replace bien
                for (int i = 1; i <= subTableMerchant.size(); i++) {
                    templateHtml = ContractUtils.replaceVariable(templateHtml, "merchantnamesTbl" + i, subTableMerchant.getJsonObject(i - 1).getString("merchant"));
                    templateHtml = ContractUtils.replaceVariable(templateHtml, "merchantIdTbl" + i, subTableMerchant.getJsonObject(i - 1).getString("merchantIDS"));
                    templateHtml = ContractUtils.replaceVariable(templateHtml, "accountNumberTbl" + i, subTableMerchant.getJsonObject(i - 1).getString("accountNumber"));
                    templateHtml = ContractUtils.replaceVariable(templateHtml, "accountnamesTbl" + i, subTableMerchant.getJsonObject(i - 1).getString("accountName"));
                    templateHtml = ContractUtils.replaceVariable(templateHtml, "bankTbl" + i, subTableMerchant.getJsonObject(i - 1).getString("bank"));
                }

                // templateHtml = ContractUtils.replaceVariable(templateHtml, "thuPhiKhacTxt",
                // mIn.get("thuPhiKhacTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "dayApproveTxt", mIn.get("dayApproveTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "khoanDamBaoInputTxt", mIn.get("khoanDamBaoInputTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "************************", mIn.get("************************").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "keepPercentTxt", mIn.get("keepPercentTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "openByBankTxt", mIn.get("openByBankTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "kyHanFDTxt", mIn.get("kyHanFDTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "stkGiaiKhoanhTxt", mIn.get("stkGiaiKhoanhTxt").toString());
                // phan 1
                templateHtml = ContractUtils.replaceVariable(templateHtml, "inputTgTamUngKhacTxt", mIn.get("inputTgTamUngKhacTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "inputTgTamUngKhacKetThucPhienTxt", mIn.get("inputTgTamUngKhacKetThucPhienTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "contractNumberTxt", mIn.get("contractNumberTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "parentContractNumberTxt", mIn.get("parentContractNumberTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "signatureDateTxt", mIn.get("signatureDateTxt") != null ? mIn.get("signatureDateTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "parentSignatureDateTxt", mIn.get("parentSignatureDateTxt") != null ? mIn.get("parentSignatureDateTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "businessnamesTxt", mIn.get("businessnamesTxt") != null ? mIn.get("businessnamesTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "shortnamesTxt", mIn.get("shortnamesTxt") != null ? mIn.get("shortnamesTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "addressBusinessTxt", mIn.get("addressBusinessTxt") != null ? mIn.get("addressBusinessTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "addressOfficeTxt", mIn.get("addressOfficeTxt") != null ? mIn.get("addressOfficeTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "phoneTxt", mIn.get("phoneTxt") != null ? mIn.get("phoneTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "websiteTxt", mIn.get("websiteTxt") != null ? mIn.get("websiteTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "numberBusinessTxt", mIn.get("numberBusinessTxt") != null ? mIn.get("numberBusinessTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "rangeDateTxt", mIn.get("rangeDateTxt") != null ? mIn.get("rangeDateTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "signaturerTxt", mIn.get("signaturerTxt") != null ? mIn.get("signaturerTxt").toString() + " " + mIn.get("signatureR").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "signatureR", mIn.get("signatureR") != null ? mIn.get("signatureR").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "positionTxt", mIn.get("positionTxt") != null ? mIn.get("positionTxt").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "noiDungThayDoiTxt", mIn.get("noiDungThayDoiTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "uyQuyenTxt", mIn.get("uyQuyenTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "inputKyQuyKhacTxt", mIn.get("inputKyQuyKhacTxt").toString());

                templateHtml = ContractUtils.replaceVariable(templateHtml, "registerFeeTbl", mIn.get("registerFeeTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "monthFeeTbl", mIn.get("monthFeeTxt").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feeTransInternationalTbl", mIn.get("feeTransInternationalTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feeTransDomesticAndAppTbl", mIn.get("feeTransDomesticAndAppTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feeTransAppTbl", mIn.get("feeTransAppTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveCardType01InternationalTbl", mIn.get("approveCardType01InternationalTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveCardType02InternationalTbl", mIn.get("approveCardType02InternationalTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "americanExpress01InternationalTbl", mIn.get("americanExpress01InternationalTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "americanExpress02InternationalTbl", mIn.get("americanExpress02InternationalTbl").toString());

                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePaymentDomesticAndAppTbl", mIn.get("feePaymentDomesticAndAppTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrMobileTbl", mIn.get("feePercentQrMobileTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrGrabTbl", mIn.get("feePercentQrGrabTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrShopeeTbl", mIn.get("feePercentQrShopeeTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrZaloTbl", mIn.get("feePercentQrZaloTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrMoMoTbl", mIn.get("feePercentQrMoMoTbl").toString());
                templateHtml = ContractUtils.replaceVariable(templateHtml, "feePercentQrOtherTbl", mIn.get("feePercentQrOtherTbl").toString());

                templateHtml = ContractUtils.replaceVariable(templateHtml, "merchantnamesTbl", mIn.get("merchantnamesTbl") != null ? mIn.get("merchantnamesTbl").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "merchantIdTbl", mIn.get("merchantnamesTbl") != null ? mIn.get("merchantIdTbl").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "accountNumberTbl", mIn.get("accountNumberTbl") != null ? mIn.get("accountNumberTbl").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "accountnamesTbl", mIn.get("accountnamesTbl") != null ? mIn.get("accountnamesTbl").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "bankTbl", mIn.get("bankTbl") != null ? mIn.get("bankTbl").toString() : "");

                // phan 2.1 Loai the va ung dung di dong chap nhan thanh toan
                // 2.1.1. The quoc te
                templateHtml = ContractUtils.replaceVariable(templateHtml, "visaBox", mIn.get("visaBox") != null ? mIn.get("visaBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "masterCardBox", mIn.get("masterCardBox") != null ? mIn.get("masterCardBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "jcbBox", mIn.get("jcbBox") != null ? mIn.get("jcbBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "unionBox", mIn.get("unionBox") != null ? mIn.get("unionBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "americanExpressBox", mIn.get("americanExpressBox") != null ? mIn.get("americanExpressBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveDomesticCardBox", mIn.get("approveDomesticCardBox") != null ? mIn.get("approveDomesticCardBox").toString() : "");
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveInternationalCardBox", mIn.get("approveInternationalCardBox") != null ? mIn.get("approveInternationalCardBox").toString() : "");
                // 2.1.2. Tra gop
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveInstallmentBox", mIn.get("approveInstallmentBox") != null ? mIn.get("approveInstallmentBox").toString() : "");
                // 2.1.3. Mua truoc tra sau
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveBnblBox", mIn.get("approveBnblBox") != null ? mIn.get("approveBnblBox").toString() : "");
                // 2.1.4. The noi dia
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveOnepayDomesticCardBox", mIn.get("approveOnepayDomesticCardBox") != null ? mIn.get("approveOnepayDomesticCardBox").toString() : "");
                // 2.1.5. Ung dung di dong
                templateHtml = ContractUtils.replaceVariable(templateHtml, "approveOnepayMobileAppBox", mIn.get("approveOnepayMobileAppBox") != null ? mIn.get("approveOnepayMobileAppBox").toString() : "");

                // phan 2.2 Lĩnh vực kinh doanh của ĐVCNTT
                templateHtml = ContractUtils.replaceVariable(templateHtml, "careerTxt", mIn.get("careerTxt") != null ? mIn.get("careerTxt").toString() : "");



                mapResponse.put("templateHtml", templateHtml);
                mapResponse.put("fileName", fileName);
                mapResponse.put("mIn", mIn);
                sendResponse(ctx, 200, mapResponse);
            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<Object, Object> mIn = new HashMap<>();
                JsonArray parentTableMerchant = bodyJson.getJsonArray("parentTableMerchant");
                JsonArray subTableMerchant = bodyJson.getJsonArray("subTableMerchant");
                JsonArray subTableShopifyMerchant = bodyJson.getJsonArray("subTableShopifyMerchant");
                JsonArray subTableNonShopifyMerchant = bodyJson.getJsonArray("subTableNonShopifyMerchant");
                JsonArray subTableNoMerchant01 = bodyJson.getJsonArray("subTableNoMerchant01");
                JsonArray subTableFee = bodyJson.getJsonArray("subTableFee");
                JsonArray merchantTablePause = bodyJson.getJsonArray("merchantTablePause");
                String contractCode = bodyJson.getString("contractCode");
                int idTemplate = bodyJson.getValue("idTemplate") == null ? 0 : bodyJson.getInteger("idTemplate");
                String emailExport = bodyJson.getString("emailExport");

                List<Map<Object, Object>> subDataMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataNoMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataParentTable = new ArrayList<>();
                List<Map<Object, Object>> subDataFeeTable = new ArrayList<>();
                List<Map<Object, Object>> subDataPauseTable = new ArrayList<>();
                String merchantIds = "";
                List<Map<Object, Object>> subDataShopifyMerchant = new ArrayList<>();
                List<Map<Object, Object>> subDataNonShopifyMerchant = new ArrayList<>();

                // du lieu bang merchant vs 4 truong du lieu
                if (!subTableMerchant.isEmpty()) {
                    for (int i = 0; i < subTableMerchant.size(); i++) {
                        JsonObject jsonobject = subTableMerchant.getJsonObject(i);
                        if (contractCode.startsWith("HD13_TH") || contractCode.startsWith("HD13_Shopify")) {
                            mIn.put("merchantnamesTbl", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                            mIn.put("merchantIdTbl", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                            mIn.put("accountNumberTbl", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                            mIn.put("accountnamesTbl", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                            mIn.put("bankTbl", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                            break;
                        } else {
                            Map<Object, Object> m = new HashMap<>();
                            merchantIds += jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "; " : jsonobject.getString("merchantIDS") + "; ";
                            m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                            m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                            m.put("data02", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                            m.put("data03", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                            m.put("data04", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                            subDataMerchant.add(m);
                        }
                    }
                }

                // du lieu bang merchant vs 3 truong du lieu
                if (!subTableNoMerchant01.isEmpty()) {
                    for (int i = 0; i < subTableNoMerchant01.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableNoMerchant01.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                        m.put("data02", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                        m.put("data03", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                        subDataNoMerchant.add(m);
                    }
                }

                if (!parentTableMerchant.isEmpty()) {
                    for (int i = 0; i < parentTableMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = parentTableMerchant.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("accountNumber") == null || jsonobject.getString("accountNumber").isEmpty() ? "" : jsonobject.getString("accountNumber"));
                        m.put("data03", jsonobject.getString("accountName") == null || jsonobject.getString("accountName").isEmpty() ? "" : jsonobject.getString("accountName"));
                        m.put("data04", jsonobject.getString("bank") == null || jsonobject.getString("bank").isEmpty() ? "" : jsonobject.getString("bank"));
                        subDataParentTable.add(m);
                    }
                }

                // du lieu bang phu luc tam ngung
                if (!merchantTablePause.isEmpty()) {
                    for (int i = 0; i < merchantTablePause.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = merchantTablePause.getJsonObject(i);
                        m.put("id", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchantName") == null || jsonobject.getString("merchantName").isEmpty() ? "" : jsonobject.getString("merchantName"));
                        m.put("data02", jsonobject.getString("merchantId") == null || jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data03", jsonobject.getString("partnerNumber") == null || jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataPauseTable.add(m);
                    }
                }
                if (!merchantTablePause.isEmpty()) {
                    mIn.put("merchantAccountNameTxt", subDataPauseTable.get(0).get("data01"));
                    mIn.put("merchantAccountIdTxt", subDataPauseTable.get(0).get("data02"));
                }

                // du lieu bang phu luc phi shopify
                if (!subTableShopifyMerchant.isEmpty()) {
                    for (int i = 0; i < subTableShopifyMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableShopifyMerchant.getJsonObject(i);
                        // m.put("id", jsonobject.getString("merchantId") == null ||
                        // jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                        // m.put("data03", jsonobject.getString("partnerNumber") == null ||
                        // jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataShopifyMerchant.add(m);
                    }
                }
                if (!subTableNonShopifyMerchant.isEmpty()) {
                    for (int i = 0; i < subTableNonShopifyMerchant.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableNonShopifyMerchant.getJsonObject(i);
                        // m.put("id", jsonobject.getString("merchantId") == null ||
                        // jsonobject.getString("merchantId").isEmpty() ? "" : jsonobject.getString("merchantId"));
                        m.put("data01", jsonobject.getString("merchant") == null || jsonobject.getString("merchant").isEmpty() ? "" : jsonobject.getString("merchant"));
                        m.put("data02", jsonobject.getString("merchantIDS") == null || jsonobject.getString("merchantIDS").isEmpty() ? "" : jsonobject.getString("merchantIDS"));
                        // m.put("data03", jsonobject.getString("partnerNumber") == null ||
                        // jsonobject.getString("partnerNumber").isEmpty() ? "" : jsonobject.getString("partnerNumber"));
                        subDataNonShopifyMerchant.add(m);
                    }
                }

                // du lieu bang phi tra gop
                if (!subTableFee.isEmpty()) {
                    for (int i = 0; i < subTableFee.size(); i++) {
                        Map<Object, Object> m = new HashMap<>();
                        JsonObject jsonobject = subTableFee.getJsonObject(i);
                        String shortName = jsonobject.getString("shortName");
                        m.put((shortName + "a_Tbl"), jsonobject.getString("threeMonths") == null || "".equals(jsonobject.getString("threeMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("threeMonths") : (jsonobject.getString("threeMonths") + "%"));
                        m.put((shortName + "b_Tbl"), jsonobject.getString("sixMonths") == null || "".equals(jsonobject.getString("sixMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("sixMonths") : (jsonobject.getString("sixMonths") + "%"));
                        m.put((shortName + "c_Tbl"), jsonobject.getString("nineMonths") == null || "".equals(jsonobject.getString("nineMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("nineMonths") : (jsonobject.getString("nineMonths") + "%"));
                        m.put((shortName + "d_Tbl"), jsonobject.getString("twelveMonths") == null || "".equals(jsonobject.getString("twelveMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("twelveMonths") : (jsonobject.getString("twelveMonths") + "%"));
                        m.put((shortName + "e_Tbl"), jsonobject.getString("fifteenMonths") == null || "".equals(jsonobject.getString("fifteenMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("fifteenMonths") : (jsonobject.getString("fifteenMonths") + "%"));
                        m.put((shortName + "f_Tbl"), jsonobject.getString("eighteenMonths") == null || "".equals(jsonobject.getString("eighteenMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("eighteenMonths") : (jsonobject.getString("eighteenMonths") + "%"));
                        m.put((shortName + "g_Tbl"), jsonobject.getString("twentyFourMonths") == null || "".equals(jsonobject.getString("twentyFourMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("twentyFourMonths") : (jsonobject.getString("twentyFourMonths") + "%"));
                        m.put((shortName + "h_Tbl"), jsonobject.getString("thirtySixMonths") == null || "".equals(jsonobject.getString("thirtySixMonths")) ? "" : (!contractCode.startsWith("HD13_TH") && !contractCode.startsWith("HD13_Shopify")) ? jsonobject.getString("thirtySixMonths") : (jsonobject.getString("thirtySixMonths") + "%"));
                        subDataFeeTable.add(m);
                    }
                }

                // convert number to money
                String khoanDamBaoInput = bodyJson.getString("khoanDamBaoInput");
                String khoanDamBaoInputMoney = vn.onepay.portal.Util.convertStringToInt(khoanDamBaoInput);
                String ************************ = null;
                if (!khoanDamBaoInputMoney.equals("")) {
                    List<String> listData = ConvertMoneyNumberToString.readNumberToMoney(khoanDamBaoInputMoney);
                    StringBuilder stringtemp = new StringBuilder();
                    if (!listData.isEmpty()) {
                        for (int i = 0; i < listData.size(); i++) {
                            String element = listData.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtemp.append(element);
                            stringtemp.append(" ");
                        }
                    }
                    ************************ = stringtemp.toString() + "đồng";
                } else {
                    ************************ = "……………";
                }

                String timeAdvance = bodyJson.getString("timeAdvance");
                String timeAdvanceOneConvert = null;
                String timeAdvanceTwoConvert = null;

                if (null != timeAdvance && !timeAdvance.isEmpty() && ("t1".equalsIgnoreCase(timeAdvance) || "t2".equalsIgnoreCase(timeAdvance))) {
                    Integer numberOne = null;
                    if ("t1".equalsIgnoreCase(timeAdvance)) {
                        numberOne = 1;
                        timeAdvanceOneConvert = "01";
                        timeAdvanceTwoConvert = "02";
                    } else if ("t2".equalsIgnoreCase(timeAdvance)) {
                        numberOne = 2;
                        timeAdvanceOneConvert = "02";
                        timeAdvanceTwoConvert = "03";
                    }
                    Integer numberTwo = numberOne + 1;

                    List<String> listDataOne = ConvertMoneyNumberToString.readNumberToMoney(String.valueOf(numberOne));
                    StringBuilder stringtempOne = new StringBuilder();
                    if (!listDataOne.isEmpty()) {
                        for (int i = 0; i < listDataOne.size(); i++) {
                            String element = listDataOne.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtempOne.append(element);
                        }
                    }

                    List<String> listDataTwo = ConvertMoneyNumberToString.readNumberToMoney(String.valueOf(numberTwo));
                    StringBuilder stringtempTwo = new StringBuilder();
                    if (!listDataTwo.isEmpty()) {
                        for (int i = 0; i < listDataTwo.size(); i++) {
                            String element = listDataTwo.get(i);
                            if (i == 0)
                                element = vn.onepay.portal.Util.capitalizeString(element, element);
                            stringtempTwo.append(element);
                        }
                    }
                    timeAdvanceOneConvert += " (" + stringtempOne.toString().toLowerCase() + ")";
                    timeAdvanceTwoConvert += " (" + stringtempTwo.toString().toLowerCase() + ")";
                }

                String uyQuyenTxt = "Theo Giấy Ủy quyền số 010121 - 1/UQ-OP ký ngày 01 tháng 01 năm 2021 của Chủ tịch HĐQT Công ty CP TM và DV Trực tuyến OnePAY";
                mIn.put("uyQuyenTxt", uyQuyenTxt);
                mIn.put("contractCode", contractCode);
                mIn.put("contractName", bodyJson.getString("contractName"));
                mIn.put("contractIdTxt", bodyJson.getString("id"));

                String businessName = bodyJson.getString("businessName");
                String otherInfo = bodyJson.getString("otherInfo");
                String shortName = bodyJson.getString("shortName");
                mIn.put("businessNameTxt", businessName);
                mIn.put("businessnamesTxt", businessName);
                mIn.put("signatureDateTxt", (null != bodyJson.getString("signatureDate") && !bodyJson.getString("signatureDate").isEmpty()) ? bodyJson.getString("signatureDate") : "……/……/…………");
                if (contractCode.equalsIgnoreCase("BBNT-HD13")) {
                    mIn.put("rangeDateTxt", (null != bodyJson.getString("rangeDate") && !bodyJson.getString("rangeDate").isEmpty()) ? bodyJson.getString("rangeDate") : "……/……/…………");
                } else {
                    mIn.put("rangeDateTxt", bodyJson.getString("rangeDate"));
                }
                if (contractCode.equalsIgnoreCase("PL16")) {
                    mIn.put("contractNumberTxt", (null != bodyJson.getString("contractNumber") && !bodyJson.getString("contractNumber").isEmpty()) ? bodyJson.getString("contractNumber") : "……………");
                } else {
                    mIn.put("contractNumberTxt", (null != bodyJson.getString("contractNumber") && !bodyJson.getString("contractNumber").isEmpty()) ? bodyJson.getString("contractNumber") : "……………");
                }

                // parent Data
                if (contractCode.equals("BBNT-HD13")) {
                    mIn.put("MerchantIdTxt", merchantIds.substring(0, merchantIds.length() - 2));
                } else {
                    mIn.put("MerchantIdTxt", merchantIds);
                }
                mIn.put("parentContractFullNameTxt", bodyJson.getString("parentContractFullName"));
                mIn.put("parentContractNumberTxt", bodyJson.getString("parentContractNumber"));
                mIn.put("parentSignatureDateTxt", bodyJson.getString("parentSignatureDate"));
                mIn.put("parentPtTamUngTxt", bodyJson.getString("parentPtTamUng"));
                mIn.put("parentTgTamUngTxt", bodyJson.getString("parentTgTamUng"));
                mIn.put("parentStkGiaiKhoanhTxt", bodyJson.getString("parentStkGiaiKhoanh"));
                mIn.put("parentCardTransactionFeeTxt", bodyJson.getString("parentCardTransactionFee"));
                mIn.put("parentFeeForCardTxt", bodyJson.getString("parentFeeForCard"));
                mIn.put("parentAccountBankTxt", bodyJson.getString("parentAccountBank"));

                mIn.put("otherInfoTxt", otherInfo);
                mIn.put("otherInfoaTxt", "(" + otherInfo + ")");
                mIn.put("otherInfobTxt", "Mức phí trên " + bodyJson.getString("otherInfo"));
                mIn.put("peopleIdTxt", bodyJson.getString("peopleId"));
                mIn.put("carrerTxt", bodyJson.getString("carrer"));
                mIn.put("branchTxt", bodyJson.getString("branch"));
                mIn.put("shortNameTxt", shortName);
                mIn.put("shortnamesTxt", shortName);
                mIn.put("addressBusinessTxt", bodyJson.getString("addressBusiness"));
                mIn.put("addressOfficeTxt", bodyJson.getString("addressOffice"));
                mIn.put("emailTxt", bodyJson.getString("email"));
                mIn.put("phoneTxt", bodyJson.getString("phone"));
                mIn.put("websiteTxt", bodyJson.getString("website"));
                mIn.put("numberBusinessTxt", bodyJson.getString("numberBusiness"));
                mIn.put("positionTxt", bodyJson.getString("position"));
                mIn.put("monthFeeTxt", bodyJson.getString("monthFee").equals("...") ? "Không áp dụng" : bodyJson.getString("monthFee"));
                mIn.put("cardTransactionFeeTxt", bodyJson.getString("cardTransactionFee"));
                mIn.put("vietNamCardTxt", bodyJson.getString("vietNamCard"));
                mIn.put("feeForCardTxt", bodyJson.getString("feeForCard"));
                mIn.put("feeForMobileTxt", bodyJson.getString("feeForMobile"));
                mIn.put("otherCardTxt", bodyJson.getString("otherCard"));
                mIn.put("ptTamUngTxt", bodyJson.getString("ptTamUng"));
                mIn.put("ptTamUngMoiTxt", bodyJson.getString("ptTamUngMoi"));
                mIn.put("tgTamUngTxt", bodyJson.getString("tgTamUng"));
                mIn.put("tgTamUngSelectionTxt", bodyJson.getString("timeAdvance"));// Set Thời gian tạm ứng config
                mIn.put("khoanDamBaoTxt", bodyJson.getString("khoanDamBao"));
                mIn.put("khoanDamBaoMoiTxt", bodyJson.getString("khoanDamBaoMoi"));
                mIn.put("kyHanFDTxt", bodyJson.getString("kyHanFD"));
                mIn.put("stkGiaiKhoanhTxt", bodyJson.getString("stkGiaiKhoanh"));
                mIn.put("accountBankTxt", bodyJson.getString("accountBank"));
                mIn.put("alertEmailAddressTxt", bodyJson.getString("alertEmailAddress"));
                mIn.put("detailEmailAddressTxt", bodyJson.getString("detailEmailAddress"));
                mIn.put("cardTypeTxt", bodyJson.getString("cardType"));
                mIn.put("changeContentTxt", bodyJson.getString("changeContent"));

                mIn.put("internationalCard01Txt", bodyJson.getString("internationalCard01"));
                mIn.put("domesticCard01Txt", bodyJson.getString("domesticCard01"));
                mIn.put("domesticCardToken01Txt", bodyJson.getString("domesticCardToken01"));
                mIn.put("internationalCard02Txt", bodyJson.getString("internationalCard02"));
                mIn.put("domesticCard02Txt", bodyJson.getString("domesticCard02"));
                mIn.put("domesticCardToken02Txt", bodyJson.getString("domesticCardToken02"));
                mIn.put("internationalCard03Txt", bodyJson.getString("internationalCard03"));
                mIn.put("domesticCard03Txt", bodyJson.getString("domesticCard03"));
                mIn.put("internationalCard04Txt", bodyJson.getString("internationalCard04"));
                mIn.put("domesticCard04Txt", bodyJson.getString("domesticCard04"));
                mIn.put("visaCardTxt", bodyJson.getString("visaCard"));
                mIn.put("masterCardTxt", bodyJson.getString("masterCard"));
                mIn.put("jcbCardTxt", bodyJson.getString("jcbCard"));
                mIn.put("americanCardTxt", bodyJson.getString("americanCard"));
                mIn.put("kyQuyTxt", bodyJson.getString("kyQuyValue"));
                mIn.put("khoanhGiuTxt", bodyJson.getString("khoanhGiuValue"));

                mIn.put("hinhThucBaoCoTxt", bodyJson.getString("hinhThucBaoCo01"));
                mIn.put("hinhThucThuPhiNgayTxt", bodyJson.getString("hinhThucThuPhiTheoNgay01"));
                mIn.put("hinhThucThuPhiThangTxt", bodyJson.getString("hinhThucThuPhiTheoThang01"));
                mIn.put("accountNameBaoCoTxt", bodyJson.getString("accountNameBaoCo"));
                mIn.put("accountNameThuPhiTxt", bodyJson.getString("accountNameThuPhi"));
                mIn.put("accountNumberBaoCoTxt", bodyJson.getString("accountNumberBaoCo"));
                mIn.put("accountNumberThuPhiTxt", bodyJson.getString("accountNumberThuPhi"));
                mIn.put("bankBaoCoTxt", bodyJson.getString("bankBaoCo"));
                mIn.put("bankThuPhiTxt", bodyJson.getString("bankThuPhi"));
                mIn.put("openByBankTxt", bodyJson.getString("openByBank"));
                mIn.put("careerTxt", (null != bodyJson.getString("career") && !bodyJson.getString("career").isEmpty() && !bodyJson.getString("career").isBlank()) ? bodyJson.getString("career").trim() : ".......");
                mIn.put("timeAdvanceOneTbl", (null != timeAdvanceOneConvert && !timeAdvanceOneConvert.isEmpty()) ? timeAdvanceOneConvert : ".......");
                mIn.put("timeAdvanceTwoTbl", (null != timeAdvanceTwoConvert && !timeAdvanceTwoConvert.isEmpty()) ? timeAdvanceTwoConvert : ".......");

                mIn.put("monthFeeTbl", bodyJson.getString("monthFee").equals("...") ? "Không áp dụng" : bodyJson.getString("monthFee"));
                mIn.put("feePaymentDomesticAndAppTbl", bodyJson.getString("feePaymentDomesticAndApp"));
                mIn.put("feePercentQrMobileTbl", (null == bodyJson.getString("percentQrMobile") || bodyJson.getString("percentQrMobile").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMobile") + "");
                mIn.put("feePercentQrGrabTbl", (null == bodyJson.getString("percentQrGrab") || bodyJson.getString("percentQrGrab").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrGrab"));
                mIn.put("feePercentQrShopeeTbl", (null == bodyJson.getString("percentQrShopee") || bodyJson.getString("percentQrShopee").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrShopee"));
                mIn.put("feePercentQrZaloTbl", (null == bodyJson.getString("percentQrZalo") || bodyJson.getString("percentQrZalo").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrZalo"));
                mIn.put("feePercentQrMoMoTbl", (null == bodyJson.getString("percentQrMoMo") || bodyJson.getString("percentQrMoMo").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMoMo"));
                mIn.put("feePercentQrOtherTbl", (null == bodyJson.getString("percentQrOther") || bodyJson.getString("percentQrOther").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrOther"));
                mIn.put("approveCardType01InternationalTbl", bodyJson.getString("approveCardType01International"));
                mIn.put("americanExpress01InternationalTbl", bodyJson.getString("americanExpress01International"));
                mIn.put("approveCardType02InternationalTbl", bodyJson.getString("approveCardType02International"));
                mIn.put("americanExpress02InternationalTbl", bodyJson.getString("americanExpress02International"));
                mIn.put("************************", ************************);
                mIn.put("keepPercentTxt", (null != bodyJson.getString("keepPercent") && !bodyJson.getString("keepPercent").isEmpty()) ? bodyJson.getString("keepPercent") + "%" : ".......");
                mIn.put("authorizedPersonNameTxt", bodyJson.getString("authorizedPersonName"));
                mIn.put("authorizedPersonIdTxt", bodyJson.getString("authorizedPersonId"));
                mIn.put("authorizedIssuedDateTxt", bodyJson.getString("authorizedIssuedDate"));
                mIn.put("authorizedIssuedByTxt", bodyJson.getString("authorizedIssuedBy"));
                mIn.put("accountNameTxt", bodyJson.getString("accountName"));
                mIn.put("accountNumberTxt", bodyJson.getString("accountNumber"));
                mIn.put("authorizationPeriodFromTxt", bodyJson.getString("authorizationPeriodFrom"));
                mIn.put("authorizationPeriodToTxt", bodyJson.getString("authorizationPeriodTo"));
                mIn.put("authorizedBirthDateTxt", bodyJson.getString("authorizedBirthDate"));
                mIn.put("authorizedAddressTxt", bodyJson.getString("authorizedAddress"));
                mIn.put("authorizationNumberTxt", bodyJson.getString("authorizationNumber"));
                mIn.put("parentMerchantIdTxt", bodyJson.getString("parentMerchantIdTxt"));

                // Phu phi shopify PL16
                mIn.put("feePaymentDomesticAndAppShopifyTbl", bodyJson.getString("feePaymentDomesticAndAppShopify"));
                mIn.put("feePercentQrMobileShopifyTbl", (null == bodyJson.getString("percentQrMobileShopify") || bodyJson.getString("percentQrMobileShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMobileShopify") + "");
                mIn.put("feePercentQrGrabShopifyTbl", (null == bodyJson.getString("percentQrGrabShopify") || bodyJson.getString("percentQrGrabShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrGrabShopify"));
                mIn.put("feePercentQrShopeeShopifyTbl", (null == bodyJson.getString("percentQrShopeeShopify") || bodyJson.getString("percentQrShopeeShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrShopeeShopify"));
                mIn.put("feePercentQrZaloShopifyTbl", (null == bodyJson.getString("percentQrZaloShopify") || bodyJson.getString("percentQrZaloShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrZaloShopify"));
                mIn.put("feePercentQrMoMoShopifyTbl", (null == bodyJson.getString("percentQrMoMoShopify") || bodyJson.getString("percentQrMoMoShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrMoMoShopify"));
                mIn.put("feePercentQrOtherShopifyTbl", (null == bodyJson.getString("percentQrOtherShopify") || bodyJson.getString("percentQrOtherShopify").isEmpty()) ? "Không áp dụng" : bodyJson.getString("percentQrOtherShopify"));
                mIn.put("approveCardType01InternationalShopifyTbl", bodyJson.getString("approveCardType01InternationalShopify"));
                mIn.put("americanExpress01InternationalShopifyTbl", bodyJson.getString("americanExpress01InternationalShopify"));
                mIn.put("approveCardType02InternationalShopifyTbl", bodyJson.getString("approveCardType02InternationalShopify"));
                mIn.put("americanExpress02InternationalShopifyTbl", bodyJson.getString("americanExpress02InternationalShopify"));
                String feeTransInternationalShopify = bodyJson.getString("feeTransInternationalShopify");
                String feeTransDomesticShopify = bodyJson.getString("feeTransDomesticAndAppShopify");
                String feeTransAppShopify = bodyJson.getString("feeAppShopify");

                if (feeTransInternationalShopify != null && !feeTransInternationalShopify.equals("") && !feeTransInternationalShopify.equals("Không áp dụng")) {
                    if (!feeTransInternationalShopify.equals("...")) {
                        feeTransInternationalShopify = convertFormatNumberString(feeTransInternationalShopify);
                        feeTransInternationalShopify = feeTransInternationalShopify + " VND/ giao dịch";
                    } else {
                        feeTransInternationalShopify = "Không áp dụng";
                    }
                }
                if (feeTransDomesticShopify != null && !feeTransDomesticShopify.equals("") && !feeTransDomesticShopify.equals("Không áp dụng")) {
                    if (!feeTransDomesticShopify.equals("...")) {
                        feeTransDomesticShopify = convertFormatNumberString(feeTransDomesticShopify);
                        feeTransDomesticShopify = feeTransDomesticShopify + " VND/ giao dịch";
                    } else {
                        feeTransDomesticShopify = "Không áp dụng";
                    }
                }

                if (feeTransAppShopify != null && !feeTransAppShopify.equals("") && !feeTransAppShopify.equals("Không áp dụng")) {
                    if (!feeTransAppShopify.equals("...")) {
                        // feeTransApp = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(feeTransApp));
                        feeTransAppShopify = feeTransAppShopify + " VND/ giao dịch";
                    } else {
                        feeTransAppShopify = "Không áp dụng";
                    }
                }
                mIn.put("feeTransInternationalShopifyTbl", feeTransInternationalShopify);
                mIn.put("feeTransDomesticAndAppShopifyTbl", feeTransDomesticShopify);
                mIn.put("feeTransAppShopifyTbl", feeTransAppShopify);
                // --------------------------------------------------------------------------------------

                String feeService = bodyJson.getString("feeService");
                String registerFee = bodyJson.getString("registerFee");
                String danhXung = bodyJson.getString("danhXung");
                String signaturer = bodyJson.getString("signaturer");
                String feeTransInternational = bodyJson.getString("feeTransInternational");
                String feeTransDomestic = bodyJson.getString("feeTransDomesticAndApp");
                String feeTransApp = bodyJson.getString("feeApp");

                mIn.put("registerFeeTxt", registerFee.equals("...") ? "Không áp dụng" : (convertFormatNumberString(registerFee) + " VND"));
                mIn.put("signaturerTxt", danhXung == null || danhXung.equals("") ? signaturer : danhXung);
                mIn.put("signatureR", signaturer);

                if (khoanDamBaoInput != null && !khoanDamBaoInput.equals("") && !khoanDamBaoInput.equals("...") && contractCode.startsWith("HD13_TH"))
                    khoanDamBaoInput = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(khoanDamBaoInput));

                if (feeService.equals("normalFee") && feeTransInternational != null && !feeTransInternational.equals("") && !feeTransInternational.equals("Không áp dụng") && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransInternational.equals("...")) {
                        feeTransInternational = convertFormatNumberString(feeTransInternational);
                        feeTransInternational = feeTransInternational + " VND/ giao dịch";
                    } else {
                        feeTransInternational = "Không áp dụng";
                    }
                }
                if (feeService.equals("normalFee") && feeTransDomestic != null && !feeTransDomestic.equals("") && !feeTransDomestic.equals("Không áp dụng") && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransDomestic.equals("...")) {
                        feeTransDomestic = convertFormatNumberString(feeTransDomestic);
                        feeTransDomestic = feeTransDomestic + " VND/ giao dịch";
                    } else {
                        feeTransDomestic = "Không áp dụng";
                    }
                }

                if (feeService.equals("normalFee") && feeTransApp != null && !feeTransApp.equals("")
                        && !feeTransApp.equals("Không áp dụng")
                        && (contractCode.startsWith("HD13_TH") || contractCode.equalsIgnoreCase("PL16"))) {
                    if (!feeTransApp.equals("...")) {
                        if (feeTransApp.matches("\\d+(\\.\\d+)?")) {
                            feeTransApp = convertFormatNumberString(feeTransApp.replaceAll("(\\.)*+(\\,)*", ""));
                        }
                        // feeTransApp = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(feeTransApp));
                        feeTransApp = feeTransApp + " VND/ giao dịch";
                    } else {
                        feeTransApp = "Không áp dụng";
                    }
                }
                if (registerFee != null && !registerFee.equals("") && !registerFee.equals("...") && contractCode.startsWith("HD13_TH")) {
                    registerFee = convertFormatNumberString(registerFee) + " VND";
                }
                // Set Thời gian tạm ứng config
                if (bodyJson.getString("timeAdvance").equals("t1")) {
                    mIn.put("advanceTime1Txt", "01 (một)");
                    mIn.put("advanceTime2Txt", "02 (hai)");
                } else {
                    mIn.put("advanceTime1Txt", "02 (hai)");
                    mIn.put("advanceTime2Txt", "03 (ba)");
                }
                mIn.put("khoanDamBaoInputTxt", (null != khoanDamBaoInput && !khoanDamBaoInput.isEmpty()) ? khoanDamBaoInput : ".......");
                mIn.put("registerFeeTbl", registerFee.equals("...") ? "Không áp dụng" : registerFee);
                mIn.put("feeTransInternationalTbl", feeTransInternational);
                mIn.put("feeTransDomesticAndAppTbl", feeTransDomestic);
                mIn.put("feeTransAppTbl", feeTransApp);

                String checked = "☒";
                String unChecked = "☐";
                if (bodyJson.getString("americanExpressBox").isEmpty() && bodyJson.getString("masterCardBox").isEmpty() && bodyJson.getString("visaBox").isEmpty() && bodyJson.getString("amexBox").isEmpty()
                        && bodyJson.getString("jcbBox").isEmpty() && bodyJson.getString("unionBox").isEmpty()) {
                    mIn.put("InternationalBox", unChecked);
                } else {
                    mIn.put("InternationalBox", checked);
                }

                String hinhThucThuPhi = bodyJson.getString("hinhThucThuPhi");
                String khoanDamBaoThanhToanSelection = bodyJson.getString("khoanDamBaoSelection");
                String khoanDamBaoThanhToanType = bodyJson.getString("kyQuyType");
                String thoiGianTamUng = bodyJson.getString("timeAdvance");

                mIn.put("appMobileBox", bodyJson.getString("appMobileBox").isEmpty() ? unChecked : checked);
                mIn.put("otherBankBox", bodyJson.getString("otherBankBox").isEmpty() ? unChecked : checked);
                mIn.put("visaBox", bodyJson.getString("visaBox").isEmpty() ? unChecked : checked);
                mIn.put("masterCardBox", bodyJson.getString("masterCardBox").isEmpty() ? unChecked : checked);
                mIn.put("amexBox", bodyJson.getString("amexBox").isEmpty() ? unChecked : checked);
                mIn.put("jcbBox", bodyJson.getString("jcbBox").isEmpty() ? unChecked : checked);
                mIn.put("unionBox", bodyJson.getString("unionBox").isEmpty() ? unChecked : checked);
                mIn.put("domesticBox", bodyJson.getString("domesticBox").isEmpty() ? unChecked : checked);
                mIn.put("kyQuyBox", bodyJson.getString("kyQuyBox").isEmpty() ? unChecked : checked);
                mIn.put("khoanhGiuBox", bodyJson.getString("khoanhGiuBox").isEmpty() ? unChecked : checked);
                mIn.put("migsBox", bodyJson.getString("migsBox").isEmpty() ? unChecked : checked);
                mIn.put("mpgsBox", bodyJson.getString("mpgsBox").isEmpty() ? unChecked : checked);
                mIn.put("cyberSourceBox", bodyJson.getString("cyberSourceBox").isEmpty() ? unChecked : checked);
                mIn.put("secureBox", bodyJson.getString("secureBox").isEmpty() ? unChecked : checked);
                mIn.put("americanExpressBox", bodyJson.getString("americanExpressBox").isEmpty() ? unChecked : checked);
                mIn.put("approveDomesticCardBox", bodyJson.getString("approveDomesticCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveInternationalCardBox", bodyJson.getString("approveInternationalCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveInstallmentBox", bodyJson.getString("approveInstallmentBox").isEmpty() ? unChecked : checked);
                mIn.put("approveOnepayDomesticCardBox", bodyJson.getString("approveOnepayDomesticCardBox").isEmpty() ? unChecked : checked);
                mIn.put("approveOnepayMobileAppBox", bodyJson.getString("approveOnepayMobileAppBox").isEmpty() ? unChecked : checked);
                mIn.put("approveBnblBox", bodyJson.getString("approveBnblBox").isEmpty() ? unChecked : checked);
                mIn.put("subDataMerchant", subDataMerchant);
                mIn.put("subDataNoMerchant", subDataNoMerchant);
                mIn.put("subDataParentTable", subDataParentTable);
                mIn.put("subDataFeeTable", subDataFeeTable);
                mIn.put("subDataPauseTable", subDataPauseTable);
                mIn.put("subShopifyData", subDataShopifyMerchant);
                mIn.put("subNonShopifyData", subDataNonShopifyMerchant);
                mIn.put("thuPhiCungButToanBaoCoBox", hinhThucThuPhi.equals("ThuPhiCungButToanBaoCo") ? checked : unChecked);
                mIn.put("thuPhiTheoThangBox", hinhThucThuPhi.equals("ThuTheoThang") ? checked : unChecked);
                mIn.put("khoanDamBaoMienBox", khoanDamBaoThanhToanSelection.equals("Mien") ? checked : unChecked);
                mIn.put("khoanDamBaoGiuLaiBox", khoanDamBaoThanhToanType.equals("kyQuyKeep") ? checked : unChecked);
                mIn.put("khoanDamBaoChuanBox", khoanDamBaoThanhToanType.equals("kyQuyStandard") ? checked : unChecked);
                mIn.put("thoiGianTamUngb", thoiGianTamUng.equals("t1") ? "01 (một)" : "02 (hai)");
                mIn.put("thoiGianTamUngc", thoiGianTamUng.equals("t1") ? "02 (hai)" : "03 (ba)");

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                // Initial file information
                String fileName = contractCode + " " +
                        (contractCode.equals("HD03-01") ? "Hop dong Ecom Vietcombank (tu thang 10-2018 den nay)"
                                : contractCode.equals("HD04") ? "Hop dong cung cap dich vu cong thanh toan dien tu (OneStart cu)"
                                        : contractCode.equals("HD05") ? "Hop dong Ecom BIDV"
                                                : contractCode.equals("HD06") ? "Hop dong Ecom Vietinbank"
                                                        : contractCode.equals("HD08") ? "Hop dong noi dia 2 ben"
                                                                : contractCode.equals("HD09") ? "Hop dong dich vu tra gop"
                                                                        : contractCode.equals("HD13_TH0") ? "Hop dong cong thanh toan dien tu"
                                                                                : contractCode.equals("HD13_TH00") ? "Hop dong cong thanh toan dien tu"
                                                                                        : contractCode.equals("HD13_TH1") ? "Hop dong cong thanh toan dien tu"
                                                                                                : contractCode.equals("HD13_TH2") ? "Hop dong cong thanh toan dien tu"
                                                                                                        : contractCode.equals("HD13_TH3") ? "Hop dong cong thanh toan dien tu"
                                                                                                                : contractCode.equals("HD13_TH4") ? "Hop dong cong thanh toan dien tu"
                                                                                                                        : contractCode.equals("HD13_TH5") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                : contractCode.equals("HD13_TH6") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                        : contractCode.equals("HD13_TH7") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                : contractCode.equals("HD13_TH8") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                        : contractCode.equals("HD13_TH9") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                                : contractCode.equals("HD13_TH10") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                                        : contractCode.equals("HD13_TH11") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                                                : contractCode.equals("HD13_TH12") ? "Hop dong cong thanh toan dien tu"
                                                                                                                                                                                        : contractCode.equals("HD13_Shopify") ? "Hop dong cong thanh toan dien tu"

                                                                                                                                                                                                : contractCode.equals("BBTL-2BEN") ? "BB thanh ly - HD 2 ben"
                                                                                                                                                                                                        : contractCode.equals("BBTL-3BEN") ? "BB thanh ly - HD 3 ben"
                                                                                                                                                                                                                : contractCode.equals("BBTL-CUP") ? "BB thanh ly - HD CUP"
                                                                                                                                                                                                                        : contractCode.equals("BBNT-HD03-01") ? "BBNT VCB Ecom"
                                                                                                                                                                                                                                : contractCode.equals("BBNT-HD04") ? "BBNT OneSTART"
                                                                                                                                                                                                                                        : contractCode.equals("BBNT-HD05") ? "BBNT BIDV Ecom"
                                                                                                                                                                                                                                                : contractCode.equals("BBNT-HD06") ? "BBNT Vietinbank Ecom"
                                                                                                                                                                                                                                                        : contractCode.equals("BBNT-HD08") ? "BBNT Noi dia"
                                                                                                                                                                                                                                                                : contractCode.equals("PL01") ? "Phu luc thay doi thong tin don vi"
                                                                                                                                                                                                                                                                        : contractCode.equals("PL02") ? "Phu luc-Mo them Merchant Account ONESTART"
                                                                                                                                                                                                                                                                                : contractCode.equals("PL03") ? "Phu luc Dieu chinh phi thanh toan the ONESTART"
                                                                                                                                                                                                                                                                                        : contractCode.equals("PL04") ? "Phu luc Thay doi tai khoan tam ung ONESTART"
                                                                                                                                                                                                                                                                                                : contractCode.equals("PL05") ? "Phu luc Dieu chinh Thoi gian tam ung ONESTART"
                                                                                                                                                                                                                                                                                                        : contractCode.equals("PL06") ? "Phu luc chap nhan thanh toan ung dung di dong"
                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL07") ? "Phu luc Dieu chinh Khoan dam bao thanh toan ONESTART"
                                                                                                                                                                                                                                                                                                                        : contractCode.equals("PL08") ? "Phu luc Chap nhan them the ONESTART"
                                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL09") ? "Phu luc dich vu tra gop HD OneSTART FINAL"
                                                                                                                                                                                                                                                                                                                                        : contractCode.equals("PL11-2BEN") ? "Phu luc Tam ngung - HD 2 ben"
                                                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL11-3BEN") ? "Phu luc Tam ngung - HD 3 ben"
                                                                                                                                                                                                                                                                                                                                                        : contractCode.equals("PL12") ? "Phu luc Ket noi lai Dich vu"
                                                                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL14") ? "Phu luc bo sung pham vi va doi tuong hop tac"
                                                                                                                                                                                                                                                                                                                                                                        : contractCode.equals("PL15-2BEN") ? "Phu luc 3D Secure - HD 2 ben"
                                                                                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL15-3BEN") ? "Phu luc 3D Secure - HD 3 ben"
                                                                                                                                                                                                                                                                                                                                                                                        : contractCode.equals("VBUQ") ? "Phu luc van ban uy quyen"
                                                                                                                                                                                                                                                                                                                                                                                                : contractCode.equals("BBNT-HD13") ? "Bien ban nghiem thu hop dong Ecom"
                                                                                                                                                                                                                                                                                                                                                                                                        : contractCode.equals("PLBNPL") ? "Phu luc BNPL"
                                                                                                                                                                                                                                                                                                                                                                                                                : contractCode.equals("PL16") ? "Phu luc phi (danh cho Merchant dung shopify)"
                                                                                                                                                                                                                                                                                                                                                                                                                        : "CONTRACT NAME");

                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_REQUEST_ID));
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }

                // get file name and form from db
                if (idTemplate != 0) {
                    Map<String, String> mFileTemplate = ContractDao.getTemplateByContractId(idTemplate, false);
                    mIn.put("mFileTemplate", mFileTemplate);

                    // update contract version
                    int idContractVersion = ContractDao.updateContractVersion(Convert.parseInt(mFileTemplate.get("idVersion"), 0), Convert.parseInt(bodyJson.getString("id"), 0), emailExport);
                    mIn.put("N_ID_VERSION", mFileTemplate.get("idVersion"));
                    mIn.put("N_ID_CONTRACT", bodyJson.getString("id"));
                    mIn.put("N_ID_CONTRACT_VERSION", idContractVersion);
                }


                Map mOut = new HashMap<>();
                logger.log(Level.WARNING, "mIn", mIn);
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("export_contract");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions("EXPORT CONTRACT");
                fileDownloadDto.setExt("docx");
                mOut.put("fileDownloadDto", fileDownloadDto);
                mOut.put("mIn", mIn);
                FileDownloadDao.insert(fileDownloadDto);

                QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));

                sendResponse(ctx, 200, mOut);
            } catch (Exception e) {
                logger.log(Level.WARNING, "EXPORT CONTRACT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static ContractOriginal getBodyContract(JsonObject bodyJson) {
        ContractOriginal contractOriginal = new ContractOriginal();
        try {
            int id = bodyJson.getString("id") == null || bodyJson.getString("id").equals("") ? 0
                    : Integer.parseInt(bodyJson.getString("id"));
            int partnerId = bodyJson.getString("partnerId") == null || bodyJson.getString("partnerId").equals("") ? 0
                    : Integer.parseInt(bodyJson.getString("partnerId"));
            int parentId = bodyJson.getString("parentId") == null || bodyJson.getString("parentId").equals("") ? 0
                    : Integer.parseInt(bodyJson.getString("parentId"));
            String contractCode = bodyJson.getString("contractCode");
            String contractName = bodyJson.getString("contractName");
            String businessName = bodyJson.getString("businessName");
            String signatureDate = bodyJson.getString("signatureDate");
            String rangeDate = bodyJson.getString("rangeDate");
            String userAction = bodyJson.getString("userAction");
            String contractNumber = bodyJson.getString("contractNumber");
            String contractType = bodyJson.getString("contractType");
            String state = bodyJson.getString("state");
            int order = bodyJson.getString("order") == null || bodyJson.getString("order").equals("") ? null
                    : Integer.parseInt(bodyJson.getString("order"));
            String dataContractDetail = bodyJson.getString("dataContractDetail");
            String template = bodyJson.getString("template");
            String authorizationNumber = bodyJson.getString("authorizationNumber");
            String representative = bodyJson.getString("representative");

            int idForm = bodyJson.getString("idForm") == null || bodyJson.getString("idForm").equals("") ? 0
                    : Integer.parseInt(bodyJson.getString("idForm"));
            int idTemplate = bodyJson.getString("idTemplate") == null || bodyJson.getString("idTemplate").equals("") ? 0
                    : Integer.parseInt(bodyJson.getString("idTemplate"));

            contractOriginal = new ContractOriginal(id, partnerId, parentId, contractName, contractCode, null, null,
                    signatureDate, rangeDate, null, userAction, state, businessName, contractNumber, dataContractDetail,
                    contractType, order, template, authorizationNumber, representative, idForm, idTemplate);
        } catch (Exception e) {
            logger.log(Level.WARNING, "GET BODY CONTRACT: ", e);
        }
        return contractOriginal;
    }

    public static String convertFormatNumberString(String string) {
        boolean isNum = false;
        isNum = string.matches("[0-9]+[\\.]?[0-9]*");
        if (isNum) {
            return Convert.toString(Double.parseDouble(string), "###,###");

            // String convertFormat = vn.onepay.portal.Util.formatCurrencyDouble(Double.parseDouble(string));
            // return convertFormat.replaceAll(",", ".");
        } else {
            return string;
        }
    }

    /* load list contract history by id contract */
    public static void loadListContractHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idContract = request.getParam("contractId") == null ? 0 : Integer.parseInt(request.getParam("contractId"));
                Map<String, Object> result = ContractDao.loadListContractHistoryById(idContract);
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Generate a random string 5 characters long
     */
    public static int generateRandomNumber() {
        long currentTimeMillis = System.currentTimeMillis();
        Random random = new Random(currentTimeMillis);
        int randomNumber = 10000 + random.nextInt(90000); // Generates a random number between 10000 and 99999
        return randomNumber;
    }

    /* download file scan from history */
    public static void downloadContractHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idHistory = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                // file name = contract_scan + random 5 char + .pdf
                String fileNameFull = "contract_scan_" + generateRandomNumber() + ".pdf";
                if (idHistory != 0) {
                    Map<String, Object> resultLoadFile = ContractDao.downloadFileScan(idHistory, fileNameFull);
                    if (resultLoadFile.get("nerror").equals(200)) {
                        String[] arr = fileNameFull.split("\\.");
                        String fileName = arr[0];
                        String fileExt = arr[1];

                        String filePath = resultLoadFile.get("filePath") == null ? BLANK : resultLoadFile.get("filePath").toString();
                        Path requestPath = FileSystems.getDefault().getPath(filePath).normalize();
                        FileSystem fs = ctx.vertx().fileSystem();
                        fs.exists(requestPath.toString(), ar -> {
                            if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                                Map<String, Object> data = new HashMap<>();
                                data.put(ParamsPool.PATH_FILE, requestPath.toString());
                                data.put(ParamsPool.FILE_NAME, fileName);
                                data.put(ParamsPool.FILE_EXT, fileExt);
                                try {
                                    data.put(ParamsPool.FILE_SIZE, String.valueOf(java.nio.file.Files.size(Paths.get(requestPath.toString()))));
                                } catch (IOException e) {
                                    ctx.fail(e);
                                }
                                ctx.response().setChunked(true);

                                String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                                String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                                String contentType = "application/octet-stream";
                                if (extFile.equals("zip")) {
                                    contentType = "application/zip";
                                }
                                ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                                ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                                ctx.response().sendFile(requestPath.toString(), result -> {
                                    if (result.succeeded()) {
                                        logger.log(Level.INFO, "Download Success");
                                    } else {
                                        logger.log(Level.INFO, "Download Fail");
                                    }
                                });
                            } else {
                                ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * insert log contract cancel/approve/reject contract
     * 
     * @param ctx RoutingContext
     */
    public static void insertLogContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject bodyJson = ctx.getBodyAsJson();

                int idContract = bodyJson.getString("idContract") == null ? 0 : Integer.parseInt(bodyJson.getString("idContract"));
                Integer idFile = bodyJson.getString("idFile") == null ? 0 : Integer.parseInt(bodyJson.getString("idFile"));
                String email = bodyJson.getString("email");
                String action = bodyJson.getString("action");

                Map<String, Object> result = ContractDao.insertLogActionContract(idContract, idFile, email, action);
                if (result.get("nError").equals(200)) {
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, result);
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Handler to receive POST file request and save it to DB
     * 
     * @param ctx RoutingContext có chứa file upload, và thông tin file upload (url với params)
     */
    public static void handleFileUpload(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Set<FileUpload> uploads = ctx.fileUploads();
                if (uploads.isEmpty()) {
                    sendResponse(ctx, 400, new JsonObject().put("error", "No file uploaded"));
                    return;
                }

                FileUpload fileUpload = uploads.iterator().next();
                Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                String fileName = fileUpload.fileName();
                String contentType = fileUpload.contentType();
                logger.log(Level.INFO, "File uploaded: " + fileName + " (" + contentType + ")");

                //đọc param url kèm theo
                String barcodeParam = ctx.request().getParam("barcode") != null ? ctx.request().getParam("barcode") : BLANK;
                //barcodeParam có dạng sau: contractData.get("N_ID_PARTNER")+"?c="+contractData.get("N_ID_CONTRACT")+"&v="+contractData.get("N_ID_CONTRACT_VERSION");
                //tách lấy giá trị N_ID_PARTNER, N_ID_CONTRACT, N_ID_CONTRACT_VERSION
                String[] barcodeParams = barcodeParam.split("\\?c=|&v=");
                int idPartner = barcodeParams.length > 0 ? Integer.parseInt(barcodeParams[0]) : 0;
                int idContract = barcodeParams.length > 1 ? Integer.parseInt(barcodeParams[1]) : 0;
                int idContractVersion = 0;
                if (barcodeParams.length > 2) {
                    // Loại bỏ các parameter khác sau &v= (như &b=n)
                    String versionParam = barcodeParams[2];
                    if (versionParam.contains("&")) {
                        versionParam = versionParam.substring(0, versionParam.indexOf("&"));
                    }
                    idContractVersion = Integer.parseInt(versionParam);
                }



                Map<String, Object> result = ContractDao.insertContractFileBlob(barcodeParam,idPartner, idContract, idContractVersion, uploadedFile.getBytes());
                if (result.get("nError").equals(200)) {
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, result);
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Handler to receive POST file request and save it to DB, then return url contract download
     * 
     * @param ctx RoutingContext có chứa file upload, và thông tin file upload (url với params)
     */
    public static void handleFileUpload2(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Set<FileUpload> uploads = ctx.fileUploads();
                if (uploads.isEmpty()) {
                    sendResponse(ctx, 400, new JsonObject().put("error", "No file uploaded"));
                    return;
                }

                FileUpload fileUpload = uploads.iterator().next();
                Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                String fileName = fileUpload.fileName();
                String contentType = fileUpload.contentType();
                logger.log(Level.INFO, "File uploaded: " + fileName + " (" + contentType + ")");

                //đọc param url kèm theo
                String barcodeParam = ctx.request().getParam("barcode") != null ? ctx.request().getParam("barcode") : BLANK;
                //barcodeParam có dạng sau: contractData.get("N_ID_PARTNER")+"?c="+contractData.get("N_ID_CONTRACT")+"&v="+contractData.get("N_ID_CONTRACT_VERSION");
                //tách lấy giá trị N_ID_PARTNER, N_ID_CONTRACT, N_ID_CONTRACT_VERSION
                String[] barcodeParams = barcodeParam.split("\\?c=|&v=");
                int idPartner = barcodeParams.length > 0 ? Integer.parseInt(barcodeParams[0]) : 0;
                int idContract = barcodeParams.length > 1 ? Integer.parseInt(barcodeParams[1]) : 0;
                int idContractVersion = 0;
                if (barcodeParams.length > 2) {
                    // Loại bỏ các parameter khác sau &v= (như &b=n)
                    String versionParam = barcodeParams[2];
                    if (versionParam.contains("&")) {
                        versionParam = versionParam.substring(0, versionParam.indexOf("&"));
                    }
                    idContractVersion = Integer.parseInt(versionParam);
                }
                boolean bilingual = (barcodeParam != null && barcodeParam.contains("b=y")) ? true : false;


                JsonObject result = ContractDao.insertContractFileBlob2(barcodeParam,idPartner, idContract, idContractVersion, uploadedFile.getBytes());
                //check result nError then build url contract download
                String urlContractDownload = "";
                if (result.getInteger("nError").equals(200)) {
                    urlContractDownload = "https://iportal.onepay.vn/iportal/api/v1/contract-export-by-template-docx-api2?id=" + idContract + "&contractCode=" + result.getString("contractCode") + "&bilingual=" + bilingual + "&idContractVersion=" + idContractVersion;
                    result.put("urlContractDownload", urlContractDownload);
                }
                if (result.getInteger("nError").equals(200)) {
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, result);
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* Handler to receive POST file request zip and extract and save file pdf compared and change status to DB 
     * @param ctx RoutingContext có chứa file upload, và thông tin file upload (url với params)
    */
    public static void handleFileUploadCompared(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                // Xử lý file zip và trích xuất file PDF
                Set<FileUpload> uploads = ctx.fileUploads();
                if (uploads.isEmpty()) {
                    sendResponse(ctx, 400, new JsonObject().put("error", "No file uploaded"));
                    return;
                }

                FileUpload fileUpload = uploads.iterator().next();
                Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                String fileName = fileUpload.fileName();
                String contentType = fileUpload.contentType();
                logger.log(Level.INFO, "File uploaded: " + fileName + " (" + contentType + ")");

                // Lấy idFile từ tên file zip (bỏ .zip)
                String idFile = fileName.toLowerCase().endsWith(".zip") ? fileName.substring(0, fileName.length() - 4) : fileName;
                logger.log(Level.INFO, "idFile lấy từ tên file zip: " + idFile);

                // Giải nén file zip
                ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(uploadedFile.getBytes()));
                ZipEntry entry;
                byte[] fileScanBytes = null;
                byte[] fileContractBytes = null;
                String idContract = null;
                String stateCompared = "compared";

                // Đọc các file pdf trong zip
                while ((entry = zipInputStream.getNextEntry()) != null) {
                    String entryName = entry.getName();
                    if (entryName.toLowerCase().endsWith(".pdf")) {
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                        // Xử lý loại bỏ "output/" nếu có trong tên file
                        String fileNameWithoutExt = entryName.substring(0, entryName.lastIndexOf("."));
                        if (fileNameWithoutExt.startsWith("output/")) {
                            fileNameWithoutExt = fileNameWithoutExt.substring("output/".length());
                        }
                        logger.log(Level.INFO, "Tên file PDF trong zip (đã loại bỏ output/ nếu có): " + fileNameWithoutExt);

                        // Nếu tên file pdf (không gồm .pdf) giống tên file zip => fileScanBytes
                        if (fileNameWithoutExt.equals(idFile)) {
                            fileScanBytes = outputStream.toByteArray();
                            logger.log(Level.INFO, "Đã tìm thấy file scan (giống tên file zip): " + fileNameWithoutExt);
                        } else {
                            // Nếu khác tên file zip => fileContractBytes và lấy idContract là tên file này
                            fileContractBytes = outputStream.toByteArray();
                            idContract = fileNameWithoutExt;
                            logger.log(Level.INFO, "Đã tìm thấy file contract (khác tên file zip): " + fileNameWithoutExt);
                        }
                    }
                }
                zipInputStream.close();

                // Gọi API cập nhật nếu có idContract và kiểm tra idContract là số
                if (fileScanBytes != null && fileContractBytes != null && idContract != null && !idContract.isEmpty()) {
                    try {
                        int idContractInt = Integer.parseInt(idContract);
                        int idFileInt = Integer.parseInt(idFile);
                        Map<String, Object> result = ContractDao.updateContractFileBlobPdfCompared(idContractInt, idFileInt, stateCompared, fileScanBytes, fileContractBytes);
                        logger.log(Level.INFO, "Kết quả cập nhật file PDF so sánh: " + result);
                    } catch (NumberFormatException ex) {
                        logger.log(Level.WARNING, "idContract hoặc idFile không phải dạng số: idFile=" + idFile + ", idContract=" + idContract);
                    }
                } else {
                    logger.log(Level.WARNING, "Không tìm thấy đủ thông tin để cập nhật: idFile=" + idFile + ", idContract=" + idContract);
                }
                sendResponse(ctx, 200, new JsonObject().put("message", "File zip đã được xử lý và cập nhật trạng thái contract"));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* download file scan and contract compared */
    public static void downloadContractCompared(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idContract = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                String type = request.getParam("type") == null ? "scan" : request.getParam("type");
                if (idContract != 0 && type != null) {
                    Map<String, Object> resultLoadFile = ContractDao.downloadFileContractScanCompared(idContract, type);
                    if (resultLoadFile.get("nerror").equals(200)) {
                        // Đọc file PDF từ đường dẫn cụ thể
                        Path pdfPath = Paths.get(resultLoadFile.get("filePath").toString());
                        byte[] fileContent = java.nio.file.Files.readAllBytes(pdfPath);
                        String fileName = pdfPath.getFileName().toString();

                        // Thiết lập phản hồi HTTP
                        ctx.response()
                                .putHeader(HttpHeaders.CONTENT_TYPE, "application/pdf")
                                .putHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                                .putHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileContent.length))
                                .write(Buffer.buffer(fileContent))
                                .end();
                    } else {
                        sendResponse(ctx, 201, new JsonObject().put("error", "File not found"));
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* get contract file scan information */
    public static void getContractFileScanInformation(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idContract = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                Map<String, Object> result = ContractDao.getContractFileScanInformation(idContract);
                if (result.get("nerror").equals(200)) {
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 201, result);
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* sync contract to document tracking */
    public static void syncContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject bodyJson = ctx.getBodyAsJson();
                String idContract = bodyJson.getString("ids");
                String email = bodyJson.getString("email");
                String location = bodyJson.getString("location");
                List<ContractOriginal>  lstContract = ContractDao.getContractById(idContract);
                int nValue = 0;
                String strMessageExist = "";
                for (ContractOriginal contract : lstContract) {
                    DocumentTrackingDto document = ContractDao.buidDocumentTrackingFromContract(contract, location);
                    SubDocumentDto subDocument = ContractDao.buidSubDocumentTrackingFromContract(contract);
                    subDocument.setCreateUser(email);
                    List<SubDocumentDto> lstSubDocument = new ArrayList<SubDocumentDto>();
                    lstSubDocument.add(subDocument);
                    int nResult = ContractDao.postDocumentTracking(document, lstSubDocument, contract.getContractId());
                    if (nResult == 201) {
                        nValue ++;
                        strMessageExist = strMessageExist.isEmpty() ? contract.getContractName() : strMessageExist +", "+ contract.getContractName();
                    }
                }
                Map<String, Object> result = new HashMap<>();
                result.put("count", nValue);
                result.put("message", strMessageExist);
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    
}