/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/28/19 4:38 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ActionDto {
    private int nResult;
    private String sResult;
    private int id;

    public ActionDto() {
    }

    public ActionDto(int nResult, String sResult, int id) {
        this.nResult = nResult;
        this.sResult = sResult;
        this.id = id;
    }

    public int getNResult() {
        return this.nResult;
    }

    public void setNResult(int nResult) {
        this.nResult = nResult;
    }

    public String getSResult() {
        return this.sResult;
    }

    public void setSResult(String sResult) {
        this.sResult = sResult;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public ActionDto nResult(int nResult) {
        this.nResult = nResult;
        return this;
    }

    public ActionDto sResult(String sResult) {
        this.sResult = sResult;
        return this;
    }

    public ActionDto id(int id) {
        this.id = id;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " nResult='" + getNResult() + "'" + ", sResult='" + getSResult() + "'" + ", id='" + getId() + "'"
                + "}";
    }
}
