package vn.onepay.portal.resources.merchantmanagement.searchkeyword;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.DateTimeUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.searchkeyword.dto.SearchKeywordDto;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class SearchKeywordHandler implements IConstants {

    private static Logger logger = Logger.getLogger(SearchKeywordHandler.class.getName());

    // - Get list Search Keyword : input: user_id
    public static void getListKeywordbyUserId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = request.getParam(USER_ID) == null ? 0 : Integer.parseInt(request.getParam(USER_ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(USER_ID, userId);
                sendResponse(ctx, 200, SeachKeywordDao.getListKeywordbyUserId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // insert and update keyword by userid
    public static void updateKeywordByUserId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();

                Integer id = ctx.get(X_USER_ID);
                if (id == null) {
                    logger.warning(ctx.get(REQUEST_UUID) + ": " + "user id not found");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(USER_ID, request.getParam(USER_ID) == null ? BLANK : request.getParam(USER_ID));
                mIn.put(KEYWORD, body.getString(KEYWORD) == null ? BLANK : body.getString(KEYWORD));
                sendResponse(ctx, 200, SeachKeywordDao.updateKeywordByUserId(mIn));
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) + ": " + "UPDATE keyword by user id ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

}
