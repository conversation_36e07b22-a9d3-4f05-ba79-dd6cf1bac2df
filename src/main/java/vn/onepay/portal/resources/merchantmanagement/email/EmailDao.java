/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 5/9/19 2:44 PM
 */

package vn.onepay.portal.resources.merchantmanagement.email;

import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.email.dto.BatchStateDto;
import vn.onepay.portal.resources.merchantmanagement.email.dto.EmailDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class EmailDao extends Db implements IConstants {
    public static BaseList<EmailDto> getListEmailBulk() throws Exception {
        Exception exception = null;
        BaseList<EmailDto> result = new BaseList<>();
        List<EmailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            // reload state first
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.check_all_batch_state(?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.execute();
            // load list group mail
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_list_group_mail(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB getListEmailBulk error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new EmailDto().id_batch(rs.getInt("N_ID_BATCH")).name(rs.getString("S_NAME"))
                            .contract_type(rs.getString("S_CONTRACT_TYPE")).state(rs.getString("S_STATE"))
                            .total(rs.getInt("N_TOTAL")).sent(rs.getInt("N_SENT")).createDate(rs.getDate("D_CREATE")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // update and get state email batch
    public static BatchStateDto checkEmailBatchStateID(Map mIn) throws Exception {
        Exception exception = null;
        BatchStateDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.check_batch_state(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.setInt(6, Convert.parseInt(mIn.get(ID_EMAIL_BATCH).toString(), 0));
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 0) {
                logger.severe("DB search partner error: " + error);
            } else {
                result = new BatchStateDto().state(cs.getString(3)).total(cs.getInt(4)).sent(cs.getInt(5));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // delete group by update state group
    public static ActionDto deleteGroupByBatchId(int idEmailBatch) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.delete_group_by_batch_id(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idEmailBatch);
            cs.execute();
            result = new ActionDto().nResult(cs.getInt(1)).sResult(cs.getString(2)).id(idEmailBatch);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update email and mail bulk condition
    public static ActionDto updateEmailBulk(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_email_batch(?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID_EMAIL_BATCH).toString(), 0));
            cs.setString(5, mIn.get(GROUP_NAME).toString());
            cs.setString(6, mIn.get(CONTRACT_TYPE).toString());
            cs.setString(7, mIn.get(PAYGATE).toString());
            cs.setString(8, mIn.get(SERVICE).toString());
            cs.setString(9, mIn.get(GROUPS).toString());
            cs.setString(10, mIn.get(SUBJECT).toString());
            cs.setString(11, mIn.get(EMAIL_CONTENT).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            if (nError == 0) {
                logger.severe("DB updateEmailBulk: " + error + "| id :" + mIn.get(ID_EMAIL_BATCH));
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(cs.getInt(3));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // send mail bulk
    public static ActionDto sendEmailBulk(int idEmailBatch) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.insert_contacts_group(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idEmailBatch);
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = idEmailBatch;
            if (nError == 0) {
                logger.severe("DB sendEmailBulk: " + error + "| id :" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
            // update batch state to sending
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_batch_state(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idEmailBatch);
            cs.setString(4, "SENDING");
            cs.execute();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // get email detail by id batch
    public static EmailDto getEmailDetail(int idEmailBatch) throws Exception {
        Exception exception = null;
        EmailDto result = null;
        List<EmailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            // load mail
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_mail(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, idEmailBatch);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB getEmailDetail error: " + error);
            } else {
                if (rs != null && rs.next()) {
                    result = new EmailDto().id_batch(rs.getInt("N_ID_BATCH")).name(rs.getString("S_NAME"))
                            .contract_type(rs.getString("S_CONTRACT_TYPE")).state(rs.getString("S_STATE"))
                            .total(rs.getInt("N_TOTAL")).sent(rs.getInt("N_SENT")).createDate(rs.getDate("D_CREATE"))
                            .paygate(rs.getString("S_PAYGATE")).service(rs.getString("S_SERVICE"))
                            .groups(rs.getString("S_GROUPS")).subject(rs.getString("S_SUBJECT"))
                            .emailContent(rs.getString("S_CONTENT"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update email and mail bulk condition
    public static ActionDto updateMerchantIDs(int idEmailBatch, String merchantIDs) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        PreparedStatement prstm = null;

        int nResult = 0;
        String sResult = "";
        try {
            con = getConnection114();

            StringReader reader = new StringReader(merchantIDs);
            prstm = con.prepareStatement("UPDATE " + Config.getOnepartnerSchema() + ".TBL_EMAIL_BATCH SET C_MERCHANTS = ? WHERE N_ID = ?");
            prstm.setCharacterStream(1, reader, merchantIDs.length());
            prstm.setInt(2, idEmailBatch);
            prstm.executeUpdate();

            nResult = 1;
            sResult = "OK";
        } catch (Exception e) {
            nResult = 0;
            sResult = e.getMessage();
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            result = new ActionDto().nResult(nResult).sResult(sResult).id(idEmailBatch);
            closeConnectionDB(null, prstm, null, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto updateListMerchantID(int idEmailBatch, List<String> merchantIDs) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        PreparedStatement prstm = null;

        int nResult = 0;
        String sResult = "";
        try {
            if (merchantIDs.size() > 0) {
                con = getConnection114();
                con.setAutoCommit(false);
                // delete first
                prstm = con.prepareStatement("DELETE " + Config.getOnepartnerSchema() + ".TBL_EMAIL_MERCHANT_ID WHERE N_ID_BATCH = ?");
                prstm.setInt(1, idEmailBatch);
                prstm.execute();
                con.commit();
                // update list
                prstm = con.prepareStatement(
                        "INSERT INTO " + Config.getOnepartnerSchema() + ".TBL_EMAIL_MERCHANT_ID(N_ID_BATCH,S_MERCHANT_ID) VALUES(?,?)");
                int count = 0;
                for (String merchantId : merchantIDs) {
                    prstm.setInt(1, idEmailBatch);
                    prstm.setString(2, merchantId);
                    prstm.execute();
                    count++;
                    if (count > 100) {
                        con.commit();
                        count = 0;
                    }
                }
                if (count > 0)
                    con.commit();
            }
            nResult = 1;
            sResult = "OK";
        } catch (Exception e) {
            nResult = 0;
            sResult = e.getMessage();
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            result = new ActionDto().nResult(nResult).sResult(sResult).id(idEmailBatch);
            closeConnectionDB(null, prstm, null, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto updateAttachEmail(int idEmailBatch, byte[] bArray, String fileName) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        PreparedStatement prstm = null;

        int nResult = 0;
        String sResult = "";
        try {
            InputStream inputStream = new ByteArrayInputStream(bArray);
            con = getConnection114();
            prstm = con.prepareCall("update " + Config.getOnepartnerSchema() + ".tbl_email set B_ATTACHED=?,S_FILE_NAME=? where n_id_batch=?");
            prstm.setBinaryStream(1, inputStream, bArray.length);
            prstm.setString(2, fileName);
            prstm.setInt(3, idEmailBatch);
            prstm.executeUpdate();

            nResult = 1;
            sResult = "OK";
        } catch (Exception e) {
            nResult = 0;
            sResult = e.getMessage();
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            result = new ActionDto().nResult(nResult).sResult(sResult).id(idEmailBatch);
            closeConnectionDB(null, prstm, null, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
