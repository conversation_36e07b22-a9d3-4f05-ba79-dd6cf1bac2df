package vn.onepay.portal.resources.merchantmanagement.sale;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantid.MerchantidDao;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDNDDto;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDPaycollectDto;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDPayoutDto;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDQRDto;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDQTDto;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.MerchantIDQuickLinkDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoHandler;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.ExchangeRateDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleMonthDetailDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleNdDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleQtDto;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
//import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class SaleHandler implements IConstants {

    private static Logger logger = Logger.getLogger(SaleHandler.class.getName());

    public static void getSaleQTbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                        
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                // mIn.put(EXCHANGE_RARE, exchange);
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQTDto> merchantIdQts = MerchantidDao.getListGateQTbyPartnerId(mIn).getList();
                BaseList<SaleQtDto> baseList = new BaseList<>();
                List<SaleQtDto> resultlist = new ArrayList<>();
                for (MerchantIDQTDto dto : merchantIdQts) {
                    Map<String, Object> input = new HashMap<>();
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(PAYGATE, dto.getPay_gate() == null ? "" : dto.getPay_gate());
                    input.put(MERCHANT_NAME, dto.getMerchant_name() == null ? "" : dto.getMerchant_name());
                    input.put(CURRENCY, dto.getCurrency_code() == null ? "" : dto.getCurrency_code());
                    input.put(MID, dto.getMid() == null ? "" : dto.getMid());
                    input.put(MCC, dto.getMcc() == null ? "" : dto.getMcc());
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleQtDto saleQtDto = new SaleQtDto().pay_gate(dto.getPay_gate())
                            .currency_code(dto.getCurrency_code()).mid(dto.getMid()).mcc(dto.getMcc())
                            .merchant_name(dto.getMerchant_name()).merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleQTbyPartnerId2(input));
                    resultlist.add(saleQtDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleInvoiceQTbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                // mIn.put(EXCHANGE_RARE, exchange);
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQTDto> merchantIdQts = MerchantidDao.getListGateInvoiceQTbyPartnerId(mIn).getList();
                BaseList<SaleQtDto> baseList = new BaseList<>();
                List<SaleQtDto> resultlist = new ArrayList<>();
                for (MerchantIDQTDto dto : merchantIdQts) {
                    Map<String, Object> input = new HashMap<>();
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(PAYGATE, dto.getPay_gate() == null ? "" : dto.getPay_gate());
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleQtDto saleQtDto = new SaleQtDto().pay_gate(dto.getPay_gate())
                            .merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleInvoicebyPartnerId(input));
                    resultlist.add(saleQtDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleQuickLinkbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                // mIn.put(EXCHANGE_RARE, exchange);
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQuickLinkDto> merchantIdQuicklink = MerchantidDao.getListGateQuicklinkbyPartnerId(mIn)
                        .getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> resultlist = new ArrayList<>();
                for (MerchantIDQuickLinkDto dto : merchantIdQuicklink) {
                    Map<String, Object> input = new HashMap<>();
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    // input.put(PAYGATE, dto.getPay_gate() == null ? "" : dto.getPay_gate());
                    input.put(MERCHANT_ID, dto.getMerchantId() == null ? "" : dto.getMerchantId());
                    SaleNdDto saleQuicklinkDto = new SaleNdDto().merchant_id(dto.getMerchantId())
                            .sale_detail_by_month(SaleDao.getSaleQuickLinkbyPartnerId(input));
                    resultlist.add(saleQuicklinkDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleNdbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDNDDto> merchantIdQts = MerchantidDao.getListGateNDbyPartnerId(mIn).getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> resultlist = new ArrayList<>();
                for (MerchantIDNDDto dto : merchantIdQts) {
                    Map<String, Object> input = new HashMap<>();
                    input.put(PARTNER_ID, partnerId);
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleNdDto saleQtDto = new SaleNdDto().merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleNDbyPartnerId2(input));
                    resultlist.add(saleQtDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleQRbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDNDDto> merchantIdQts = MerchantidDao.getListGateQRbyPartnerId(mIn).getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> resultlist = new ArrayList<>();
                for (MerchantIDNDDto dto : merchantIdQts) {
                    Map<String, Object> input = new HashMap<>();
                    input.put(PARTNER_ID, partnerId);
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleNdDto saleQtDto = new SaleNdDto().merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleQRbyPartnerId2(input));
                    resultlist.add(saleQtDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getExchangeRate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                List<ExchangeRateDto> exchangeRateDtos = SaleDao.getExchangeRateByYear(Integer.parseInt(year));
                BaseList<ExchangeRateDto> baseList = new BaseList<>();
                baseList.setList(exchangeRateDtos);
                baseList.setTotalItems(exchangeRateDtos.size());
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleBnplByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQRDto> merchantBnpl = MerchantidDao.getListGateBnplByPartnerId(mIn).getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (MerchantIDQRDto dto : merchantBnpl) {
                    input = new HashMap<>();
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleBnplByPartnerId(input));
                    result.add(saleQtDto);
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleUposInstallmentByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQRDto> merchantUpos = MerchantidDao.getListGateUposByPartnerId(mIn).getList();
                List<MerchantIDQRDto> merchantInstallment = merchantUpos.stream()
                        .filter(item -> Objects.equals(item.getMethod(), "Installment")).collect(Collectors.toList());
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (MerchantIDQRDto dto : merchantInstallment) {
                    input = new HashMap<>();
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleUposInstallment(input));
                    result.add(saleQtDto);
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleUposCardQrByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDQRDto> merchantUpos = MerchantidDao.getListGateUposByPartnerId(mIn).getList();
                List<MerchantIDQRDto> merchantCardQr = merchantUpos.stream()
                        .filter(item -> Objects.equals(item.getMethod(), "Card, QR")).collect(Collectors.toList());
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (MerchantIDQRDto dto : merchantCardQr) {
                    input = new HashMap<>();
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, dto.getMerchant_id() == null ? "" : dto.getMerchant_id());
                    input.put(TERMINAL_ID, dto.getTerminal_id() == null ? "" : dto.getTerminal_id());
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(dto.getMerchant_id())
                            .sale_detail_by_month(SaleDao.getSaleUposCardQr(input));
                    result.add(saleQtDto);
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleUposCardByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<String> lstMerchantId = MerchantidDao.getMerchantIdUposByPartnerId(mIn);
                List<ExchangeRateDto> lstExchangeRateDto = SaleDao.getExchangRate(mIn);
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (String merchantId : lstMerchantId) {
                    // int cnt = 0;
                    input = new HashMap<>();
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, merchantId);
                    List<SaleMonthDetailDto> lstSaleMonthDetailDto = SaleDao.getSaleUposCard(input);
                    
                    for (SaleMonthDetailDto saleMonthDetailDto : lstSaleMonthDetailDto){
                        // if (saleMonthDetailDto.getCountFail() == 0 && saleMonthDetailDto.getCountSuccess() == 0 && saleMonthDetailDto.getTotal() == 0){
                        //     cnt += 1;
                        // }
                        int monthSale = Integer.parseInt(saleMonthDetailDto.getMonth());
                        for(ExchangeRateDto exchangeRateDto : lstExchangeRateDto){
                            if(monthSale == exchangeRateDto.getMonth() && saleMonthDetailDto.getTotal() != null && exchangeRateDto.getValue() != 0){
                                saleMonthDetailDto.totalUSD(exchangeRateDto.getValue());
                            } 
                        }
                    }
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(merchantId)
                            .sale_detail_by_month(lstSaleMonthDetailDto);
                    // if (cnt < 12){
                        result.add(saleQtDto);
                    // }
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleUposQrByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<String> lstMerchantId = MerchantidDao.getMerchantIdUposByPartnerId(mIn);
                List<ExchangeRateDto> lstExchangeRateDto = SaleDao.getExchangRate(mIn);
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (String merchantId : lstMerchantId) {
                    // int cnt = 0;
                    input = new HashMap<>();
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, merchantId);
                    List<SaleMonthDetailDto> lstSaleMonthDetailDto = SaleDao.getSaleUposQr(input);
                    
                    for (SaleMonthDetailDto saleMonthDetailDto : lstSaleMonthDetailDto){
                        // if (saleMonthDetailDto.getCountFail() == 0 && saleMonthDetailDto.getCountSuccess() == 0 && saleMonthDetailDto.getTotal() == 0){
                        //     cnt += 1;
                        // }
                        int monthSale = Integer.parseInt(saleMonthDetailDto.getMonth());
                        for(ExchangeRateDto exchangeRateDto : lstExchangeRateDto){
                            if(monthSale == exchangeRateDto.getMonth() && saleMonthDetailDto.getTotal() != null && exchangeRateDto.getValue() != 0){
                                saleMonthDetailDto.totalUSD(exchangeRateDto.getValue());
                            } 
                        }
                    }
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(merchantId)
                            .sale_detail_by_month(lstSaleMonthDetailDto);
                    // if (cnt < 12){
                        result.add(saleQtDto);
                    // }
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSaleUposVietQrByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<String> lstMerchantId = MerchantidDao.getMerchantIdUposByPartnerId(mIn);
                List<ExchangeRateDto> lstExchangeRateDto = SaleDao.getExchangRate(mIn);
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (String merchantId : lstMerchantId) {
                    // int cnt = 0;
                    input = new HashMap<>();
                    input.put(YEAR, year);
                    input.put(MERCHANT_ID, merchantId);
                    List<SaleMonthDetailDto> lstSaleMonthDetailDto = SaleDao.getSaleUposVietQr(input);
                    
                    for (SaleMonthDetailDto saleMonthDetailDto : lstSaleMonthDetailDto){
                        // if (saleMonthDetailDto.getCountFail() == 0 && saleMonthDetailDto.getCountSuccess() == 0 && saleMonthDetailDto.getTotal() == 0){
                        //     cnt += 1;
                        // }
                        int monthSale = Integer.parseInt(saleMonthDetailDto.getMonth());
                        for(ExchangeRateDto exchangeRateDto : lstExchangeRateDto){
                            if(monthSale == exchangeRateDto.getMonth() && saleMonthDetailDto.getTotal() != null && exchangeRateDto.getValue() != 0){
                                saleMonthDetailDto.totalUSD(exchangeRateDto.getValue());
                            } 
                        }
                    }
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(merchantId)
                            .sale_detail_by_month(lstSaleMonthDetailDto);
                    // if (cnt < 12){
                        result.add(saleQtDto);
                    // }
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /*
     * public static void getSaleInvoicebyPartnerId(RoutingContext ctx) {
     * ctx.vertx().executeBlocking(future -> { try { HttpServerRequest request =
     * ctx.request(); Integer partnerId = request.getParam(PARTNER_ID) == null ? 0 :
     * Integer.parseInt(request.getParam(PARTNER_ID)); String year =
     * request.getParam(YEAR) == null ?
     * String.valueOf(Calendar.getInstance().get(Calendar.YEAR)) :
     * request.getParam(YEAR); Map<String,
     * Object> mIn = new HashMap<>(); mIn.put(PARTNER_ID, partnerId); mIn.put(YEAR,
     * year); sendResponse(ctx, 200, SaleDao.getSaleQTbyPartnerId(mIn)); } catch
     * (Exception e) { ctx.fail(e); } }, false, null); }
     */

    public static void getSalePayoutbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                // mIn.put(EXCHANGE_RARE, exchange);
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDPayoutDto> merchantIdPayout = MerchantidDao.getListGatePayoutByPartnerId(mIn)
                        .getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> resultlist = new ArrayList<>();
                for (MerchantIDPayoutDto dto : merchantIdPayout) {
                    Map<String, Object> input = new HashMap<>();
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    // input.put(PAYGATE, dto.getPay_gate() == null ? "" : dto.getPay_gate());
                    input.put(MERCHANT_ID, dto.getMerchantId() == null ? "" : dto.getMerchantId());
                    SaleNdDto salePayoutDto = new SaleNdDto().merchant_id(dto.getMerchantId())
                            .sale_detail_by_month(SaleDao.getSalePayoutbyPartnerId(input));
                    resultlist.add(salePayoutDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSalePaycollectbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                // mIn.put(EXCHANGE_RARE, exchange);
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                List<MerchantIDPaycollectDto> merchantIdPaycollect = MerchantidDao.getListGatePaycollectByPartnerId(mIn)
                        .getList();
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> resultlist = new ArrayList<>();
                for (MerchantIDPaycollectDto dto : merchantIdPaycollect) {
                    Map<String, Object> input = new HashMap<>();
                    // input.put(EXCHANGE_RARE, exchange);
                    input.put(PARTNER_ID, partnerId);
                    input.put(ID, partnerId);
                    input.put(YEAR, year);
                    // input.put(PAYGATE, dto.getPay_gate() == null ? "" : dto.getPay_gate());
                    input.put(MERCHANT_ID, dto.getMerchantId() == null ? "" : dto.getMerchantId());
                    SaleNdDto salePaycollectDto = new SaleNdDto().merchant_id(dto.getMerchantId())
                            .sale_detail_by_month(SaleDao.getSalePaycollectbyPartnerId(input));
                    resultlist.add(salePaycollectDto);
                }
                baseList.setList(resultlist);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    
    public static void getSaleVietQrByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                BaseList<MerchantIDQRDto> lstMerchantId = MerchantidDao.getListGateVietQRByPartnerId(mIn);
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (MerchantIDQRDto merchantId : lstMerchantId.getList()) {
                    // int cnt = 0;
                    input = new HashMap<>();
                    mIn.put(PARTNER_ID, partnerId);
                    input.put(YEAR, year);
                    input.put(PARTNER_ID, partnerId);
                    input.put(MERCHANT_ID, merchantId.getMerchant_id());
                    List<SaleMonthDetailDto> lstSaleMonthDetailDto = SaleDao.getSaleVietQRbyPartnerId(input);
                    
                    // for (SaleMonthDetailDto saleMonthDetailDto : lstSaleMonthDetailDto){
                    //     if (saleMonthDetailDto.getCountFail() == 0 && saleMonthDetailDto.getCountSuccess() == 0 && saleMonthDetailDto.getTotal() == 0){
                    //         cnt += 1;
                    //     }
                    // }
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(merchantId.getMerchant_id())
                            .sale_detail_by_month(lstSaleMonthDetailDto);
                    // if (cnt < 12){
                        result.add(saleQtDto);
                    // }
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    
    public static void getSaleDirectDebitByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0
                        : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                String year = request.getParam(YEAR) == null ? String.valueOf(Calendar.getInstance().get(Calendar.YEAR))
                        : request.getParam(YEAR);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                mIn.put(ID, partnerId);
                mIn.put(YEAR, year);
                BaseList<MerchantIDQRDto> lstMerchantId = MerchantidDao.getListGateDirectDebitByPartnerId(mIn);
                BaseList<SaleNdDto> baseList = new BaseList<>();
                List<SaleNdDto> result = new ArrayList<>();
                Map<String, Object> input;
                for (MerchantIDQRDto merchantId : lstMerchantId.getList()) {
                    // int cnt = 0;
                    input = new HashMap<>();
                    input.put(YEAR, year);
                    input.put(PARTNER_ID, partnerId);
                    input.put(MERCHANT_ID, merchantId.getMerchant_id());
                    List<SaleMonthDetailDto> lstSaleMonthDetailDto = SaleDao.getSaleDDbyPartnerId(input);
                    
                    // for (SaleMonthDetailDto saleMonthDetailDto : lstSaleMonthDetailDto){
                        // if (saleMonthDetailDto.getCountFail() == 0 && saleMonthDetailDto.getCountSuccess() == 0 && saleMonthDetailDto.getTotal() == 0){
                            // cnt += 1;
                        // }
                    // }
                    SaleNdDto saleQtDto = new SaleNdDto()
                            .merchant_id(merchantId.getMerchant_id())
                            .sale_detail_by_month(lstSaleMonthDetailDto);
                    // if (cnt < 12){
                        result.add(saleQtDto);
                    // }
                }
                baseList.setList(result);
                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

}
