package vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto;


public class ThirdPartner {
    private String id;
    private String businessNumber;
    private String businessName;
    private String providedDate;
    private String businessAddress;
    private String officeAddress;
    private String website;
    private String source;
    private String represent;
    private String representTitle;
    private String contactMobile;
    private String accountNumber;
    private String accountName;
    private String bank;
    private String businessArea;
    private String payMethod;
    private String contactEmail;
    private String businessFileUrl;
    private String identityCardUrl;
    private String businessAreaFileUrl;
    private String imageUrl;
    private String partnerStatus;
    private String state;
    
    public ThirdPartner() {
    }

    public ThirdPartner(String id, String businessNumber, String businessName, String providedDate,
            String businessAddress, String officeAddress, String website, String source, String represent, String representTitle,
            String contactMobile, String accountNumber, String accountName, String bank, String businessArea,
            String payMethod, String contactEmail, String businessFileUrl, String identityCardUrl,
            String businessAreaFileUrl, String imageUrl, String state) {
        this.id = id;
        this.businessNumber = businessNumber;
        this.businessName = businessName;
        this.providedDate = providedDate;
        this.businessAddress = businessAddress;
        this.officeAddress = officeAddress;
        this.website = website;
        this.source = source;
        this.represent = represent;
        this.representTitle = representTitle;
        this.contactMobile = contactMobile;
        this.accountNumber = accountNumber;
        this.accountName = accountName;
        this.bank = bank;
        this.businessArea = businessArea;
        this.payMethod = payMethod;
        this.contactEmail = contactEmail;
        this.businessFileUrl = businessFileUrl;
        this.identityCardUrl = identityCardUrl;
        this.businessAreaFileUrl = businessAreaFileUrl;
        this.imageUrl = imageUrl;
        this.state = state;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBusinessNumber() {
        return businessNumber;
    }

    public void setBusinessNumber(String businessNumber) {
        this.businessNumber = businessNumber;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getProvidedDate() {
        return providedDate;
    }

    public void setProvidedDate(String providedDate) {
        this.providedDate = providedDate;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public String getOfficeAddress() {
        return officeAddress;
    }

    public void setOfficeAddress(String officeAddress) {
        this.officeAddress = officeAddress;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRepresent() {
        return represent;
    }

    public void setRepresent(String represent) {
        this.represent = represent;
    }

    public String getRepresentTitle() {
        return representTitle;
    }

    public void setRepresentTitle(String representTitle) {
        this.representTitle = representTitle;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBusinessArea() {
        return businessArea;
    }

    public void setBusinessArea(String businessArea) {
        this.businessArea = businessArea;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getBusinessFileUrl() {
        return businessFileUrl;
    }

    public void setBusinessFileUrl(String businessFileUrl) {
        this.businessFileUrl = businessFileUrl;
    }

    public String getIdentityCardUrl() {
        return identityCardUrl;
    }

    public void setIdentityCardUrl(String identityCardUrl) {
        this.identityCardUrl = identityCardUrl;
    }

    public String getBusinessAreaFileUrl() {
        return businessAreaFileUrl;
    }

    public void setBusinessAreaFileUrl(String businessAreaFileUrl) {
        this.businessAreaFileUrl = businessAreaFileUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(String partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

}
