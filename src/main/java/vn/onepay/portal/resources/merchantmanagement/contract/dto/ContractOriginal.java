package vn.onepay.portal.resources.merchantmanagement.contract.dto;

import java.sql.Timestamp;
import java.util.List;

public class ContractOriginal {

    private int contractId;
    private int partnerId;
    private int parentId;
    private String contractName;
    private String contractCode;
    private Timestamp signatureDate;
    private Timestamp rangeDate;
    private String signatureDateString;
    private String rangeDateString;
    private Timestamp createdDate;
    private String userAction;
    private String stateContract;
    private String businessName;
    private String contractNumber;
    private String dataContractDetail;
    private String contractType;
    private int order;
    private List<ContractOriginal> subContractList;
    private String template;
    private String authorizationNumber;
    private String representative;
    private int idForm;
    private int idTemplate;
    private int idVersion;
    private String contractCodeParent;
    private Timestamp signatureDateParent;
    private String contractCodeTypeParent;
    private String businessNameParent;
    private String businessNumber;
    private String contractNumberParent;
    private String dataContractDetailParent;
    private Timestamp rangeDateParent;
    private String rangeDateParentString;


    public ContractOriginal() {
    }

    public ContractOriginal(int contractId, int partnerId, int parentId, String contractName,
            String contractCode, Timestamp signatureDate, Timestamp rangeDate, String signatureDateString,
            String rangeDateString, Timestamp createdDate,
            String userAction, String stateContract, String businessName, String contractNumber,
            String dataContractDetail, String contractType, List<ContractOriginal> subContractList) {
        this.contractId = contractId;
        this.partnerId = partnerId;
        this.parentId = parentId;
        this.contractName = contractName;
        this.contractCode = contractCode;
        this.signatureDate = signatureDate;
        this.rangeDate = rangeDate;
        this.signatureDateString = signatureDateString;
        this.rangeDateString = rangeDateString;
        this.createdDate = createdDate;
        this.userAction = userAction;
        this.stateContract = stateContract;
        this.businessName = businessName;
        this.contractNumber = contractNumber;
        this.dataContractDetail = dataContractDetail;
        this.contractType = contractType;
        this.subContractList = subContractList;
    }

    // public ContractOriginal(int contractId, int partnerId, int parentId, String contractName,
    //         String contractCode, Timestamp signatureDate, Timestamp rangeDate, String signatureDateString,
    //         String rangeDateString, Timestamp createdDate,
    //         String userAction, String stateContract, String businessName, String contractNumber,
    //         String dataContractDetail, String contractType, int order, String template, String authorizationNumber,
    //         String representative) {
    //     this.contractId = contractId;
    //     this.partnerId = partnerId;
    //     this.parentId = parentId;
    //     this.contractName = contractName;
    //     this.contractCode = contractCode;
    //     this.signatureDate = signatureDate;
    //     this.rangeDate = rangeDate;
    //     this.signatureDateString = signatureDateString;
    //     this.rangeDateString = rangeDateString;
    //     this.createdDate = createdDate;
    //     this.userAction = userAction;
    //     this.stateContract = stateContract;
    //     this.businessName = businessName;
    //     this.contractNumber = contractNumber;
    //     this.dataContractDetail = dataContractDetail;
    //     this.contractType = contractType;
    //     this.order = order;
    //     this.template = template;
    //     this.authorizationNumber = authorizationNumber;
    //     this.representative = representative;
    // }

    public ContractOriginal(int contractId, int partnerId, int parentId, String contractName,
            String contractCode, Timestamp signatureDate, Timestamp rangeDate, String signatureDateString,
            String rangeDateString, Timestamp createdDate,
            String userAction, String stateContract, String businessName, String contractNumber,
            String dataContractDetail, String contractType, int order, String template, String authorizationNumber,
            String representative,int idForm, int idTemplate) {
        this.contractId = contractId;
        this.partnerId = partnerId;
        this.parentId = parentId;
        this.contractName = contractName;
        this.contractCode = contractCode;
        this.signatureDate = signatureDate;
        this.rangeDate = rangeDate;
        this.signatureDateString = signatureDateString;
        this.rangeDateString = rangeDateString;
        this.createdDate = createdDate;
        this.userAction = userAction;
        this.stateContract = stateContract;
        this.businessName = businessName;
        this.contractNumber = contractNumber;
        this.dataContractDetail = dataContractDetail;
        this.contractType = contractType;
        this.order = order;
        this.template = template;
        this.authorizationNumber = authorizationNumber;
        this.representative = representative;
        this.idForm = idForm;
        this.idTemplate = idTemplate;
    }

    public ContractOriginal(int contractId, int partnerId, int parentId, String contractName,
            String contractCode, Timestamp signatureDate, Timestamp rangeDate, String signatureDateString,
            String rangeDateString, Timestamp createdDate,
            String userAction, String stateContract, String businessName, String contractNumber,
            String dataContractDetail, String contractType, int order, String template, String authorizationNumber,
            String representative,int idForm, int idTemplate, int idVersion, String contractCodeParent, Timestamp signatureDateParent, 
            String contractCodeTypeParent, String businessNameParent, String dataContractDetailParent, Timestamp rangeDateParent, String rangeDateParentString) {
        this.contractId = contractId;
        this.partnerId = partnerId;
        this.parentId = parentId;
        this.contractName = contractName;
        this.contractCode = contractCode;
        this.signatureDate = signatureDate;
        this.rangeDate = rangeDate;
        this.signatureDateString = signatureDateString;
        this.rangeDateString = rangeDateString;
        this.createdDate = createdDate;
        this.userAction = userAction;
        this.stateContract = stateContract;
        this.businessName = businessName;
        this.contractNumber = contractNumber;
        this.dataContractDetail = dataContractDetail;
        this.contractType = contractType;
        this.order = order;
        this.template = template;
        this.authorizationNumber = authorizationNumber;
        this.representative = representative;
        this.idForm = idForm;
        this.idTemplate = idTemplate;
        this.idVersion = idVersion;
        this.contractCodeParent = contractCodeParent;
        this.signatureDateParent = signatureDateParent;
        this.contractCodeTypeParent = contractCodeTypeParent;
        this.businessNameParent = businessNameParent;
        this.dataContractDetailParent = dataContractDetailParent;
        this.rangeDateParent = rangeDateParent;
        this.rangeDateParentString = rangeDateParentString;
    }

    public String getTemplate() {
        return this.template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public int getContractId() {
        return contractId;
    }

    public void setContractId(int contractId) {
        this.contractId = contractId;
    }

    public int getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Timestamp getSignatureDate() {
        return signatureDate;
    }

    public void setSignatureDate(Timestamp signatureDate) {
        this.signatureDate = signatureDate;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getStateContract() {
        return stateContract;
    }

    public void setStateContract(String stateContract) {
        this.stateContract = stateContract;
    }

    public String getUserAction() {
        return userAction;
    }

    public void setUserAction(String userAction) {
        this.userAction = userAction;
    }

    public String getSignatureDateString() {
        return signatureDateString;
    }

    public void setSignatureDateString(String signatureDateString) {
        this.signatureDateString = signatureDateString;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public String getDataContractDetail() {
        return dataContractDetail;
    }

    public void setDataContractDetail(String dataContractDetail) {
        this.dataContractDetail = dataContractDetail;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public List<ContractOriginal> getSubContractList() {
        return subContractList;
    }

    public void setSubContractList(List<ContractOriginal> subContractList) {
        this.subContractList = subContractList;
    }

    public Timestamp getRangeDate() {
        return rangeDate;
    }

    public void setRangeDate(Timestamp rangeDate) {
        this.rangeDate = rangeDate;
    }

    public String getRangeDateString() {
        return rangeDateString;
    }

    public void setRangeDateString(String rangeDateString) {
        this.rangeDateString = rangeDateString;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getAuthorizationNumber() {
        return authorizationNumber;
    }

    public void setAuthorizationNumber(String authorizationNumber) {
        this.authorizationNumber = authorizationNumber;
    }

    public String getRepresentative() {
        return representative;
    }

    public void setRepresentative(String representative) {
        this.representative = representative;
    }

    public int getIdForm() {
        return idForm;
    }

    public void setIdForm(int idForm) {
        this.idForm = idForm;
    }

    public int getIdTemplate() {
        return idTemplate;
    }

    public void setIdTemplate(int idTemplate) {
        this.idTemplate = idTemplate;
    }
    
    public int getIdVersion() {
        return idVersion;
    }

    public void setIdVersion(int idVersion) {
        this.idVersion = idVersion;
    }

    public String getContractCodeParent() {
        return contractCodeParent;
    }

    public void setContractCodeParent(String contractCodeParent) {
        this.contractCodeParent = contractCodeParent;
    }

    public Timestamp getSignatureDateParent() {
        return signatureDateParent;
    }

    public void setSignatureDateParent(Timestamp signatureDateParent) {
        this.signatureDateParent = signatureDateParent;
    }

    public String getContractCodeTypeParent() {
        return contractCodeTypeParent;
    }

    public void setContractCodeTypeParent(String contractCodeTypeParent) {
        this.contractCodeTypeParent = contractCodeTypeParent;
    }

    public String getBusinessNameParent() {
        return businessNameParent;
    }

    public void setBusinessNameParent(String businessNameParent) {
        this.businessNameParent = businessNameParent;
    }

    public String getBusinessNumber() {
        return businessNumber;
    }

    public void setBusinessNumber(String businessNumber) {
        this.businessNumber = businessNumber;
    }

    public String getContractNumberParent() {
        return contractNumberParent;
    }

    public void setContractNumberParent(String contractNumberParent) {
        this.contractNumberParent = contractNumberParent;
    }

    public String getDataContractDetailParent() {
        return dataContractDetailParent;
    }

    public void setDataContractDetailParent(String dataContractDetailParent) {
        this.dataContractDetailParent = dataContractDetailParent;
    }

    public Timestamp getRangeDateParent() {
        return rangeDateParent;
    }

    public void setRangeDateParent(Timestamp rangeDateParent) {
        this.rangeDateParent = rangeDateParent;
    }
    
    public String getRangeDateParentString() {
        return rangeDateParentString;
    }

    public void setRangeDateParentString(String rangeDateParentString) {
        this.rangeDateParentString = rangeDateParentString;
    }
}
