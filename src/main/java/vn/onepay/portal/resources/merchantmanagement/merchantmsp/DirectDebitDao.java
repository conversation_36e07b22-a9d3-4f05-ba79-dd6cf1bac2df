package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;

import org.json.JSONObject;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.DirectDebitDto;

public class DirectDebitDao extends Db implements IConstants {

    public static List<DirectDebitDto> getDirectDebitById(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<DirectDebitDto> result = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.GET_DIRECT_DEBIT_BY_ID(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET DIRECT DEBIT BY MERCHANT ID " + merchantId + " error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    result = bindDirectDebit(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    public static void upsertDirectDebit(String merchantId, JsonObject clientData) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.UPSERT_DIRECT_DEBIT(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.setString(2, clientData.toString());
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("UPSERT DIRECT DEBIT ERROR: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    private static List<DirectDebitDto> bindDirectDebit(ResultSet rs) throws Exception {
        List<DirectDebitDto> result = new ArrayList<>();
        JSONObject  data = new JSONObject(rs.getString("S_CLIENT_DATA").trim());
        Iterator<String> keys = data.keys();
        if (data.length() > 0) {
            while(keys.hasNext()) {
                String key = keys.next();
                JSONObject bank = (JSONObject) data.get(key);
                if (data.get(key) instanceof JSONObject) {
                    DirectDebitDto directDebit = new DirectDebitDto();
                    String[] listBanks = key.split(";");
                    directDebit.setBank(listBanks);
                    if (bank.has("max_amount_per_transaction")){
                        directDebit.setMax_amount_per_transaction(bank.getDouble("max_amount_per_transaction"));
                    }
                    if (bank.has("max_amount_per_token_per_day")){
                        directDebit.setMax_amount_per_token_per_day(bank.getDouble("max_amount_per_token_per_day"));
                    }
                    result.add(directDebit);
                }
            }
        }
        return result;
    }

}
