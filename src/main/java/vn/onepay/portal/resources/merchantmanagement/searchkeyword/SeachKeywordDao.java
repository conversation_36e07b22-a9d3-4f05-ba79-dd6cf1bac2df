package vn.onepay.portal.resources.merchantmanagement.searchkeyword;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.searchkeyword.dto.SearchKeywordDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
//import java.util.logging.Logger;

public class SeachKeywordDao extends Db implements IConstants {

    // get list Search Keyword
    public static BaseList<SearchKeywordDto> getListKeywordbyUserId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<SearchKeywordDto> result = new BaseList<>();
        List<SearchKeywordDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_keyword_by_user_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                throw new Exception("DB load Keyword list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SearchKeywordDto().user_id(rs.getInt("N_USER_ID"))
                            .keyword(rs.getString("S_KEYWORD")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // insert and update keyword by user id
    public static ActionDto updateKeywordByUserId(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_keyword_by_user_id(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setString(5, mIn.get(KEYWORD).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                throw new Exception("DB update keyword by user id error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
