package vn.onepay.portal.resources.merchantmanagement.acceptance.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class AcceptanceDto {
    private int id;
    private String type;
    private Date acceptance_date;
    private String ten_dkkd;
    private String merchant_name;
    private String website;
    private String service_type;
    private String guarantee_account;
    private String merchant_id_qt;
    private String mid;
    private String currency;
    private String card_type_qt;
    private String paygate;
    private String merchant_id_nd;
    private String card_type_nd;
    private String note;
    private Integer partner_id;
    private String mcc;
    private String merchant_id_nd_vcb;
    private String mid_nd_vcb;


    public AcceptanceDto() {
    }

    public AcceptanceDto(int id, String type, Date acceptance_date, String ten_dkkd, String merchant_name, String website, String service_type, String guarantee_account, String merchant_id_qt, String mid, String currency, String card_type_qt, String paygate, String merchant_id_nd, String card_type_nd, String note, Integer partner_id, String mcc, String merchant_id_nd_vcb, String mid_nd_vcb) {
        this.id = id;
        this.type = type;
        this.acceptance_date = acceptance_date;
        this.ten_dkkd = ten_dkkd;
        this.merchant_name = merchant_name;
        this.website = website;
        this.service_type = service_type;
        this.guarantee_account = guarantee_account;
        this.merchant_id_qt = merchant_id_qt;
        this.mid = mid;
        this.currency = currency;
        this.card_type_qt = card_type_qt;
        this.paygate = paygate;
        this.merchant_id_nd = merchant_id_nd;
        this.card_type_nd = card_type_nd;
        this.note = note;
        this.partner_id = partner_id;
        this.mcc = mcc;
        this.merchant_id_nd_vcb = merchant_id_nd_vcb;
        this.mid_nd_vcb = mid_nd_vcb;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getAcceptance_date() {
        return this.acceptance_date;
    }

    public void setAcceptance_date(Date acceptance_date) {
        this.acceptance_date = acceptance_date;
    }

    public String getTen_dkkd() {
        return this.ten_dkkd;
    }

    public void setTen_dkkd(String ten_dkkd) {
        this.ten_dkkd = ten_dkkd;
    }

    public String getMerchant_name() {
        return this.merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getWebsite() {
        return this.website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getService_type() {
        return this.service_type;
    }

    public void setService_type(String service_type) {
        this.service_type = service_type;
    }

    public String getGuarantee_account() {
        return this.guarantee_account;
    }

    public void setGuarantee_account(String guarantee_account) {
        this.guarantee_account = guarantee_account;
    }

    public String getMerchant_id_qt() {
        return this.merchant_id_qt;
    }

    public void setMerchant_id_qt(String merchant_id_qt) {
        this.merchant_id_qt = merchant_id_qt;
    }

    public String getMid() {
        return this.mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCard_type_qt() {
        return this.card_type_qt;
    }

    public void setCard_type_qt(String card_type_qt) {
        this.card_type_qt = card_type_qt;
    }

    public String getPaygate() {
        return this.paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getMerchant_id_nd() {
        return this.merchant_id_nd;
    }

    public void setMerchant_id_nd(String merchant_id_nd) {
        this.merchant_id_nd = merchant_id_nd;
    }

    public String getCard_type_nd() {
        return this.card_type_nd;
    }

    public void setCard_type_nd(String card_type_nd) {
        this.card_type_nd = card_type_nd;
    }

    public String getNote() {
        return this.note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getPartner_id() {
        return this.partner_id;
    }

    public void setPartner_id(Integer partner_id) {
        this.partner_id = partner_id;
    }

    public String getMcc() {
        return this.mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMerchant_id_nd_vcb() {
        return this.merchant_id_nd_vcb;
    }

    public void setMerchant_id_nd_vcb(String merchant_id_nd_vcb) {
        this.merchant_id_nd_vcb = merchant_id_nd_vcb;
    }

    public String getMid_nd_vcb() {
        return this.mid_nd_vcb;
    }

    public void setMid_nd_vcb(String mid_nd_vcb) {
        this.mid_nd_vcb = mid_nd_vcb;
    }

    public AcceptanceDto id(int id) {
        this.id = id;
        return this;
    }

    public AcceptanceDto type(String type) {
        this.type = type;
        return this;
    }

    public AcceptanceDto acceptance_date(Date acceptance_date) {
        this.acceptance_date = acceptance_date;
        return this;
    }

    public AcceptanceDto ten_dkkd(String ten_dkkd) {
        this.ten_dkkd = ten_dkkd;
        return this;
    }

    public AcceptanceDto merchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
        return this;
    }

    public AcceptanceDto website(String website) {
        this.website = website;
        return this;
    }

    public AcceptanceDto service_type(String service_type) {
        this.service_type = service_type;
        return this;
    }

    public AcceptanceDto guarantee_account(String guarantee_account) {
        this.guarantee_account = guarantee_account;
        return this;
    }

    public AcceptanceDto merchant_id_qt(String merchant_id_qt) {
        this.merchant_id_qt = merchant_id_qt;
        return this;
    }

    public AcceptanceDto mid(String mid) {
        this.mid = mid;
        return this;
    }

    public AcceptanceDto currency(String currency) {
        this.currency = currency;
        return this;
    }

    public AcceptanceDto card_type_qt(String card_type_qt) {
        this.card_type_qt = card_type_qt;
        return this;
    }

    public AcceptanceDto paygate(String paygate) {
        this.paygate = paygate;
        return this;
    }

    public AcceptanceDto merchant_id_nd(String merchant_id_nd) {
        this.merchant_id_nd = merchant_id_nd;
        return this;
    }

    public AcceptanceDto card_type_nd(String card_type_nd) {
        this.card_type_nd = card_type_nd;
        return this;
    }

    public AcceptanceDto note(String note) {
        this.note = note;
        return this;
    }

    public AcceptanceDto partner_id(Integer partner_id) {
        this.partner_id = partner_id;
        return this;
    }

    public AcceptanceDto mcc(String mcc) {
        this.mcc = mcc;
        return this;
    }

    public AcceptanceDto merchant_id_nd_vcb(String merchant_id_nd_vcb) {
        this.merchant_id_nd_vcb = merchant_id_nd_vcb;
        return this;
    }

    public AcceptanceDto mid_nd_vcb(String mid_nd_vcb) {
        this.mid_nd_vcb = mid_nd_vcb;
        return this;
    }

    @Override
    public String toString() {
        return "{" +
            " id='" + getId() + "'" +
            ", type='" + getType() + "'" +
            ", acceptance_date='" + getAcceptance_date() + "'" +
            ", ten_dkkd='" + getTen_dkkd() + "'" +
            ", merchant_name='" + getMerchant_name() + "'" +
            ", website='" + getWebsite() + "'" +
            ", service_type='" + getService_type() + "'" +
            ", guarantee_account='" + getGuarantee_account() + "'" +
            ", merchant_id_qt='" + getMerchant_id_qt() + "'" +
            ", mid='" + getMid() + "'" +
            ", currency='" + getCurrency() + "'" +
            ", card_type_qt='" + getCard_type_qt() + "'" +
            ", paygate='" + getPaygate() + "'" +
            ", merchant_id_nd='" + getMerchant_id_nd() + "'" +
            ", card_type_nd='" + getCard_type_nd() + "'" +
            ", note='" + getNote() + "'" +
            ", partner_id='" + getPartner_id() + "'" +
            ", mcc='" + getMcc() + "'" +
            ", merchant_id_nd_vcb='" + getMerchant_id_nd_vcb() + "'" +
            ", mid_nd_vcb='" + getMid_nd_vcb() + "'" +
            "}";
    }

}
