package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

import io.vertx.core.json.JsonObject;

import java.sql.Timestamp;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/15/2020
 * Time: 3:55 PM
 * To change this iportal-service.
 */

public class MerchantApprovalRequest {
    private Integer id;
    private String merchantId;
    private Timestamp createdDate;
    private MerchantMspDto newValue;
    private MerchantMspDto oldValue;
    private String userName;
private String type;
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public MerchantMspDto getNewValue() {
        return newValue;
    }

    public void setNewValue(MerchantMspDto newValue) {
        this.newValue = newValue;
    }

    public MerchantMspDto getOldValue() {
        return oldValue;
    }

    public void setOldValue(MerchantMspDto oldValue) {
        this.oldValue = oldValue;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
}
