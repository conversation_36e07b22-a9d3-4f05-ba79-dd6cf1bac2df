/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/29/19 5:29 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class GroupDto {
    private int id;
    private String name;
    private String desc;

    public GroupDto() {
    }

    public GroupDto(int id, String name, String desc) {
        this.id = id;
        this.name = name;
        this.desc = desc;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public GroupDto id(int id) {
        this.id = id;
        return this;
    }

    public GroupDto name(String name) {
        this.name = name;
        return this;
    }

    public GroupDto desc(String desc) {
        this.desc = desc;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", name='" + getName() + "'" + ", desc='" + getDesc() + "'" + "}";
    }
}
