package vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto;

import java.sql.Timestamp;

public class HistoryLogDto {
    private String partnerId;
    private String userEmail;
    private Timestamp createdDate;
    private String action;

    public HistoryLogDto() {
        
    }

    public HistoryLogDto(String partnerId, String userEmail, Timestamp createdDate, String action) {
        this.partnerId = partnerId;
        this.userEmail = userEmail;
        this.createdDate = createdDate;
        this.action = action;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
    
}
