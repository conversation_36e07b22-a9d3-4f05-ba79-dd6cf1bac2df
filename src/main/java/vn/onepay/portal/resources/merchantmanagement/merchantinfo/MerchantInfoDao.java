/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 2/15/19 5:52 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

public class MerchantInfoDao extends Db {

    public static BaseList<ProvinceDto> getProvinceList() throws Exception {
        Exception exception = null;
        BaseList<ProvinceDto> result = new BaseList<>();
        List<ProvinceDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.LOAD_TINHTHANH(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get province error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ProvinceDto().id(rs.getInt("N_ID")).tinh(rs.getString("S_TINH"))
                            .trungTam(rs.getString("S_TRUNGTAM")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<CareerDto> getCareerList() throws Exception {
        Exception exception = null;
        BaseList<CareerDto> result = new BaseList<>();
        List<CareerDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "DB get careers error: ";
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.LOAD_NGANH_NGHE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                error += cs.getString(3);
                logger.severe(error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new CareerDto().id(rs.getInt("N_ID")).tenNganhNghe(rs.getString("S_TEN_NGANH_NGHE"))
                            .order(rs.getInt("N_ORDER")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<PartnerDto> searchPartnerList(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        BaseList<PartnerDto> list = new BaseList<>();
        List<PartnerDto> temp = new ArrayList<>();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "DB search partner v4 error: ";
        try {
            conn = getConnection114();
            cs = conn.prepareCall(
                    "{ call ONEPARTNER.pkg_merchant_management.search_partners_v4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setInt(6, Convert.parseInt(mIn.get(PAGE_ACTIVE).toString(), 0));
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.setString(8, mIn.get(KEYWORD).toString());
            cs.setString(9, mIn.get(PARTNER_NAME).toString());
            cs.setString(10, mIn.get(MID).toString());
            cs.setString(11, mIn.get(EMAIL).toString());
            cs.setString(12, mIn.get(PHONE_NUMBER).toString());
            cs.setString(13, mIn.get(ADDRESS).toString());
            cs.setString(14, mIn.get(PROVINCE_ID).toString());
            cs.setString(15, mIn.get("sale_id").toString());
            cs.setString(16, mIn.get("mm_id").toString());
            cs.execute();
            error += cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe(error);
            } else {
                while (rs != null && rs.next())
                    temp.add(bindPartner(rs));
                list.setList(temp);
                list.setTotalItems(cs.getInt(4));
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        if (exception != null)
            throw exception;
        return list;
    }

    public static BaseList<PartnerDto> searchPartnerListByKeyword(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<PartnerDto> result = new BaseList<>();
        List<PartnerDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.get_partners_by_keyword(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setInt(6, Convert.parseInt(mIn.get(PAGE_ACTIVE).toString(), 0));
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.setString(8, mIn.get(KEYWORD).toString());
            cs.execute();
            
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB search partner error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new PartnerDto()
                            .id(rs.getInt("N_ID"))
                            .shortName(rs.getString("S_SHORT_NAME"))
                            .partnerName(rs.getString("S_PARTNER_NAME"))
                            .sale(rs.getString("S_SALE")));
                }
            }
            result.setTotalItems(cs.getInt(4));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static PartnerStatusDto getPartnerStatus(Map mIn) throws Exception {
        Exception exception = null;
        PartnerStatusDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".P_MERCHANT_MANAGEMENT.get_partner_status(?,?,?,?,?,?,?,?,?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.registerOutParameter(9, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(9);
            int nError = cs.getInt(8);
            if (nError == 0) {
                logger.severe("DB get partner status error: " + error);
            } else {
                result = new PartnerStatusDto().countQtActive(cs.getInt(2))
                        .countQtDisable(cs.getInt(3)).countInvoiceActive(cs.getInt(4))
                        .countInvoiceDisable(cs.getInt(5)).countNdActive(cs.getInt(6))
                        .countNdDisable(cs.getInt(7));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto deletePartner(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ ?= call " + Config.getOnepartnerSchema()
                    + ".P_MERCHANT_MANAGEMENT.delete_partner_by_state(?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(2);
            if (error != null && !"".equals(error)) {
                logger.severe("DB delete partner error: " + error);
            } else {
                result = new ActionDto().nResult(1).sResult(cs.getString(2));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto updatePartner(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.update_partner_new_v2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.setInt(1, Convert.parseInt((String) mIn.get(PARTNER_ID), 0));
            cs.setString(2, mIn.get(SHORT_NAME).toString());
            cs.setString(3, mIn.get(PARTNER_NAME).toString());
            cs.setString(4, mIn.get(WEBSITE).toString());
            cs.setString(5, mIn.get(ADDRESS_LINE2).toString());
            cs.setInt(6, Convert.parseInt(mIn.get(PROVINCE_ID).toString(), 0));
            cs.setString(7, mIn.get(ADDRESS_LINE1).toString());
            cs.setInt(8, Convert.parseInt(mIn.get(PROVINCE_ID_OFFICE).toString(), 0));
            cs.setString(9, mIn.get(EMAIL).toString());
            cs.setString(10, mIn.get(TEL).toString());
            cs.setString(11, mIn.get(FAX).toString());
            cs.setString(12, mIn.get(SALE).toString());
            cs.setString(13, mIn.get(TAX_CODE).toString());
            cs.setString(14, mIn.get(BUSINESS_CODE).toString());
            if (mIn.get(BUSINESS_REGISER_DATE) == null || mIn.get(BUSINESS_REGISER_DATE) == "") {
                cs.setString(15, "");
            }
            else {
                cs.setTimestamp(15, Convert.toTimestamp(Convert.toDate(mIn.get(BUSINESS_REGISER_DATE).toString(), "dd/MM/yyyy HH:mm:ss")));
            }
            cs.setInt(16, Convert.parseInt(mIn.get(CAREER_ID).toString(), 0));
            cs.setString(17, mIn.get(CAREER_OTHER_DESC).toString());
            cs.setString(18, mIn.get(DESCRIPTION).toString());
            cs.setString(19, mIn.get(MM).toString());
            cs.registerOutParameter(20, OracleTypes.NUMBER);
            cs.registerOutParameter(21, OracleTypes.NUMBER);
            cs.registerOutParameter(22, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(22);
            int nError = cs.getInt(21);
            int nId = cs.getInt(20);
            if (nError == 0) {
                logger.severe("DB update partner error: " + error + "| id partner:" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static PartnerDto getPartnerByID(Map<String, Object> mIn) throws Exception {
        int partnerId = Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0);
        Exception exception = null;
        PartnerDto result = new PartnerDto(partnerId);
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".pkg_merchant_management.get_partner_by_id_v4(?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.setInt(5, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setString(6, mIn.get(PROVINCE_ID).toString());
            cs.setString(7, mIn.get("sale_id").toString());
            cs.setString(8, mIn.get("mm_id").toString());
            cs.registerOutParameter(9, OracleTypes.NUMBER);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            Boolean isAssigned = cs.getInt(9) == 1;
            result.setIsAssigned(isAssigned);
            if (nError == 0) {
                logger.severe("DB search partner error: " + error);
            } else {
                if (rs != null && rs.next()) {
                    result = new PartnerDto().id(rs.getInt("N_ID")).active(rs.getInt("N_ACTIVE"))
                            .shortName(rs.getString("S_SHORT_NAME")).partnerName(rs.getString("S_PARTNER_NAME"))
                            .tradingName(rs.getString("S_TRADING_NAME")).tradingId(rs.getString("S_TRADING_ID"))
                            .registerName(rs.getString("S_REGISTER_NAME")).desc(rs.getString("S_DESCRIPTION"))
                            .categoryCode(rs.getString("S_CATEGORY_CODE"))
                            .goodsDescription(rs.getString("S_GOODS_DESCRIPTION")).website(rs.getString("S_WEBSITE"))
                            .locale(rs.getString("S_LOCALE")).timezone(rs.getString("S_TIMEZONE"))
                            .addressLine1(rs.getString("S_ADDRESS_LINE1")).addressLine2(rs.getString("S_ADDRESS_LINE2"))
                            .city(rs.getString("S_CITY")).state(rs.getString("S_STATE"))
                            .postalCode(rs.getString("S_POSTCODE")).country(rs.getString("S_COUNTRY"))
                            .contactName(rs.getString("S_CONTACT_NAME")).phoneNumber(rs.getString("S_PHONE_NUMBER"))
                            .escalationPhoneNumber(rs.getString("S_ESCALATION_PHONE_NUMBER"))
                            .mobileNumber(rs.getString("S_MOBILE_PHONE_NUMBER")).faxNumber(rs.getString("S_FAX_NUMBER"))
                            .sale(rs.getString("S_SALE_ID")).mm(rs.getString("S_MM")).email(rs.getString("S_EMAIL"))
                            .type(rs.getString("S_TYPE"))
                            .taxCode(rs.getString("S_MST")).provinceId(rs.getInt("N_TINHTHANH_ID"))
                            .provinceIdOffice(rs.getInt("N_TINHTHANH_VPGD_ID")).businessCode(rs.getString("S_SODKKD"))
                            .businessRegisterDate(rs.getDate("D_NGAY_DKKD")).careerId(rs.getInt("N_NGANHNGHE_ID"))
                            .careerOther(rs.getString("S_NGANHNGHE_KHAC")).riskReview(rs.getString("S_DANHGIA_RISK"))
                            .createDate(rs.getDate("D_CREATE_DATE")).updateDate(rs.getDate("D_LAST_UPDATE"))
                            .status(rs.getString("S_STATUS"))
                            .setIsAssigned(isAssigned);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<ContactDto> getContactList(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<ContactDto> result = new BaseList<>();
        List<ContactDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{? = call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_contact_by_partner_id(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB get list contact by partner id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ContactDto().id(rs.getInt("N_ID")).groupIds(rs.getString("S_GROUP_IDS"))
                            .partnerId(rs.getInt("N_PARTNER_ID")).name(rs.getString("S_NAME"))
                            .phone(rs.getString("S_PHONE")).mobile(rs.getString("S_MOBILE"))
                            .email(rs.getString("S_EMAIL"))
                            // .position(rs.getString("S_POSITION"))
                            .title(rs.getString("S_TITLE")).isWork(rs.getInt("N_IS_WORK"))
                            .createDate(rs.getDate("D_CREATE")).updateDate(rs.getDate("D_UPDATE"))
                            .userIdUpdate(rs.getInt("N_USER_ID_UPDATE")).prefix(rs.getString("S_PREFIX")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static ActionDto updateContact(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.update_contact_new(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(CONTACT_ID).toString(), 0));
            cs.setInt(5, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(6, mIn.get(NAME).toString());
            cs.setString(7, mIn.get(TITLE).toString());
            cs.setString(8, mIn.get(PHONE_NUMBER).toString());
            cs.setString(9, mIn.get(EMAIL).toString());
            cs.setString(10, mIn.get(POSITION).toString());
            cs.setInt(11, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setInt(12, Convert.parseInt(mIn.get(IS_WORK).toString(), 0));
            cs.setString(13, mIn.get(PREFIX).toString());
            cs.setString(14, mIn.get(MOBILE).toString());
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            int nId = cs.getInt(3);
            if (nError == 0) {
                logger.severe("DB update contact error: " + error + "| id contact:" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto removeContactGroup(int contactId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.remove_contant_group(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, contactId);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 0) {
                logger.severe("DB remove contact group error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto addContactGroup(int contactId, int groupId) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.insert_contact_group(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, contactId);
            cs.setInt(4, groupId);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 0) {
                logger.severe("DB insert contact group error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto addOrRemoveContactGroup(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".P_MERCHANT_MANAGEMENT.ADD_REMOVE_CONTACT_GROUP(?,?,?,?,?)}");
            cs.setString(1, mIn.get(COMMAND).toString());
            cs.setInt(2, Convert.parseInt(mIn.get(CONTACT_ID).toString(), 0));
            cs.setInt(3, Convert.parseInt(mIn.get(GROUP_ID).toString(), 0));
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(5);
            int nError = cs.getInt(4);
            if (nError == 0) {
                logger.severe("DB update contact group error: " + error);
            } else {
                result = new ActionDto().nResult(nError).sResult(error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ActionDto deleteContact(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.delete_contact(?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(CONTACT_ID).toString(), 0));
            cs.execute();
            result = new ActionDto().nResult(1).sResult("");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<GroupDto> getGroupList() throws Exception {
        Exception exception = null;
        BaseList<GroupDto> result = new BaseList<>();
        List<GroupDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.LOAD_LIST_GROUP(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get group error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new GroupDto().id(rs.getInt("N_ID")).name(rs.getString("S_NAME"))
                            .desc(rs.getString("S_DESC")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<ContactGroupsDto> getGroupsByContactID(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<ContactGroupsDto> result = new BaseList<>();
        List<ContactGroupsDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call " + Config.getOnepartnerSchema()
                    + ".P_MERCHANT_MANAGEMENT.LOAD_LIST_GROUP_CONTACT(?,?,?,?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(CONTACT_ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nerror == 0) {
                logger.severe("DB get group by contact id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ContactGroupsDto().contactId(rs.getInt("N_ID_CONTACT"))
                            .groupId(rs.getInt("N_ID_GROUP")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static BaseList<MerchantInfoDto> getMerchantList(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        BaseList<MerchantInfoDto> result = new BaseList<>();
        List<MerchantInfoDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{? = call PKG_MMVN.search_merchant_by_page(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, mIn.get(MERCHANT_ID).toString());
            cs.setString(5, mIn.get(PAGE).toString());
            cs.setString(6, mIn.get(PAGE_SIZE).toString());
            cs.execute();
            String error = cs.getString(2);
            rs = (ResultSet) cs.getObject(1);
            if (error != null && !"".equals(error)) {
                logger.severe("DB get Merchant error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new MerchantInfoDto().id(rs.getString("S_MERCHANT_ID"))
                            .name(rs.getString("S_MERCHANT_NAME")));
                }
            }
            result.setTotalItems(cs.getInt(3));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static String getProvinceIdByUserId(Integer userId) throws Exception {
        String provinceIds = null;
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{call PKG_ROLE_MANAGEMENT.get_id_tinhthanh(?,?,?,?)}");
            cs.setInt(1, userId);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.execute();

            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            rs = (ResultSet) cs.getObject(4);
            if (nResult != 200)
                logger.log(Level.FINE, "DB get_province_id_by_user_id error: ", sResult);
            else {
                Set<Integer> result = new HashSet<>();
                while (rs != null && rs.next())
                    result.add(Util.getColumnInteger(rs, "N_ID"));

                provinceIds = String.join(",",
                        result.stream().map(item -> item.toString()).collect(Collectors.toList()));
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        if (exception != null)
            throw exception;
        return provinceIds;
    }

    private static PartnerDto bindPartner(ResultSet rs) {
        PartnerDto partnerDto;
        try {
            partnerDto = new PartnerDto().id(rs.getInt("N_ID")).active(rs.getInt("N_ACTIVE"))
                    .shortName(rs.getString("S_SHORT_NAME")).partnerName(rs.getString("S_PARTNER_NAME"))
                    .tradingName(rs.getString("S_TRADING_NAME")).tradingId(rs.getString("S_TRADING_ID"))
                    .registerName(rs.getString("S_REGISTER_NAME")).desc(rs.getString("S_DESCRIPTION"))
                    .categoryCode(rs.getString("S_CATEGORY_CODE"))
                    .goodsDescription(rs.getString("S_GOODS_DESCRIPTION")).website(rs.getString("S_WEBSITE"))
                    .locale(rs.getString("S_LOCALE")).timezone(rs.getString("S_TIMEZONE"))
                    .addressLine1(rs.getString("S_ADDRESS_LINE1")).addressLine2(rs.getString("S_ADDRESS_LINE2"))
                    .city(rs.getString("S_CITY")).state(rs.getString("S_STATE"))
                    .postalCode(rs.getString("S_POSTCODE")).country(rs.getString("S_COUNTRY"))
                    .contactName(rs.getString("S_CONTACT_NAME")).phoneNumber(rs.getString("S_PHONE_NUMBER"))
                    .escalationPhoneNumber(rs.getString("S_ESCALATION_PHONE_NUMBER"))
                    .mobileNumber(rs.getString("S_MOBILE_PHONE_NUMBER")).faxNumber(rs.getString("S_FAX_NUMBER"))
                    .sale(rs.getString("S_SALE")).email(rs.getString("S_EMAIL")).type(rs.getString("S_TYPE"))
                    .taxCode(rs.getString("S_MST")).provinceId(rs.getInt("N_TINHTHANH_ID"))
                    .provinceIdOffice(rs.getInt("N_TINHTHANH_VPGD_ID")).businessCode(rs.getString("S_SODKKD"))
                    .businessRegisterDate(rs.getDate("D_NGAY_DKKD")).careerId(rs.getInt("N_NGANHNGHE_ID"))
                    .careerOther(rs.getString("S_NGANHNGHE_KHAC")).riskReview(rs.getString("S_DANHGIA_RISK"))
                    .createDate(rs.getDate("D_CREATE_DATE")).updateDate(rs.getDate("D_LAST_UPDATE"));
        } catch (Exception e) {
            logger.log(Level.FINE, "Error when bind data ParnerDTO ");
            return null;
        }

        return partnerDto;
    }

    public static List<Integer> getPartnerAssign(Map<String, Object> mIn) throws Exception {      
        Exception exception = null;        
        Connection con = null;
        CallableStatement cs = null;        
        List<Integer> list = new ArrayList<>();
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".CHECK_PARTNER_ASSIGN(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);            
            cs.setInt(4, Convert.parseInt(mIn.get(USER_ID).toString(), 0));
            cs.setString(5, mIn.get(PROVINCE_ID).toString());
            cs.setString(6, mIn.get("sale_id").toString());
            cs.setString(7, mIn.get("mm_id").toString());
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            Boolean isAssigned = cs.getInt(8) == 1;           
            if (nError == 0) {
                logger.severe("DB search partner error: " + error);
            } else {
                while (rs != null && rs.next())
                {
                    list.add(rs.getInt("partnerId"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }    
}
