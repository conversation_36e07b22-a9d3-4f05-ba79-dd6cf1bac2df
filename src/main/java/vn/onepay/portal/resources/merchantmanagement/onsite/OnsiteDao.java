package vn.onepay.portal.resources.merchantmanagement.onsite;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.onsite.dto.OnSiteDto;

import java.io.StringReader;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class OnsiteDao extends Db implements IConstants {

    // - Get list onsite: input: partner_id
    public static BaseList<OnSiteDto> getListOnsitebyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<OnSiteDto> result = new BaseList<>();
        List<OnSiteDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_onsite_by_partner_id(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(GROUP).toString());
            cs.setString(6, mIn.get(USER_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load contract by partner id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new OnSiteDto().id(rs.getInt("N_ID"))
                            .createDate(Convert.toDate(rs.getString("D_DATE"), "yyyy-MM-dd"))
                            .staff(rs.getString("S_NHAN_SU")).content(rs.getString("S_NOI_DUNG"))
                            .channel(rs.getString("S_KENH")).partnerId(rs.getInt("N_PARTNER_ID"))
                            .target(rs.getString("S_NGUOI_GAP")).group(rs.getString("S_GROUP")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // - Get onsite by id: input onsite id
    public static BaseList<OnSiteDto> getListOnsitebyOnsiteId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<OnSiteDto> result = new BaseList<>();
        List<OnSiteDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.load_onsite_by_onsite_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ONSITE_ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load contract by partner id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new OnSiteDto().id(rs.getInt("N_ID"))
                            .createDate(Convert.toDate(rs.getString("D_DATE"), "yyyy-MM-dd"))
                            .staff(rs.getString("S_NHAN_SU")).content(rs.getString("S_NOI_DUNG"))
                            .channel(rs.getString("S_KENH")).partnerId(rs.getInt("N_PARTNER_ID"))
                            .target(rs.getString("S_NGUOI_GAP")).group(rs.getString("S_GROUP")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // - Remove Onsite by id:
    public static ActionDto deleteOnsitebyOnsiteID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.delete_onsite_by_onsite_id(?,?,?)}");
            cs.setInt(1, Convert.parseInt(mIn.get(ONSITE_ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            if (nError == 0) {
                logger.severe("DB insert onsite error: " + error);
            }
            result = new ActionDto().nResult(1).sResult("");

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    // insert and update Onsite
    public static ActionDto updateOnsitebyOnsiteID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.update_onsite_by_onsite_id(?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setTimestamp(5, Convert.toTimestamp(Convert.toDate(mIn.get(DATE).toString(), "dd/MM/yyyy")));
            cs.setString(6, mIn.get(STAFF).toString());
            cs.setCharacterStream(7, new StringReader(mIn.get(CONTENT).toString()), mIn.get(CONTENT).toString().length());
            // cs.setString(7, mIn.get(CONTENT).toString());
            cs.setString(8, mIn.get(CHANNEL).toString());
            cs.setInt(9, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(10, mIn.get(TARGET).toString());
            cs.setString(11, mIn.get(GROUP).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                logger.severe("DB update Onsite error: " + error + "| id Onsite:" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

}
