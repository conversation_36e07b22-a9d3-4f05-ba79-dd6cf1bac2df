package vn.onepay.portal.resources.merchantmanagement.acceptance;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.DateTimeUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.resources.merchantmanagement.acceptance.dto.AcceptanceDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;


public class AcceptanceHandler implements IConstants {

    private static Logger logger = Logger.getLogger(AcceptanceHandler.class.getName());

    // - Get list Acceptance : input: partner_id
    public static void getListAcceptancebyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(PARTNER_ID) == null ? 0 : Integer.parseInt(request.getParam(PARTNER_ID));
                Integer userId = ctx.get(N_USER_ID);
                
                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(PARTNER_ID, partnerId);
                sendResponse(ctx, 200, AcceptanceDao.getListAcceptancebyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // - Get list Acceptance : input: partner_id
    public static void getListMerchantIdByAcceptance(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer acceptanceId = request.getParam(ACCEPTANCE_ID) == null ? 0 : Integer.parseInt(request.getParam(ACCEPTANCE_ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ACCEPTANCE_ID, acceptanceId);
                sendResponse(ctx, 200, AcceptanceDao.getListMerchantIdByAcceptance(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    //Remove Acceptance by id
    public static void deleteAcceptancebyID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String acceptanceId = request.getParam(ID) == null ? BLANK : request.getParam(ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, acceptanceId);
                sendResponse(ctx, 200, AcceptanceDao.deleteAcceptancebyID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteMerchantId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String merchantId = request.getParam(ID) == null ? BLANK : request.getParam(ID);
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, merchantId);
                sendResponse(ctx, 200, AcceptanceDao.deleteMerchantID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }


    // insert and update Acceptance
    public static void updateAcceptance(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                mIn.put(TYPE, body.getString(TYPE) == null ? BLANK : body.getString(TYPE));
                mIn.put(TEN_DKKD, body.getString(TEN_DKKD) == null ? BLANK : body.getString(TEN_DKKD));
                mIn.put(DATE, body.getString(DATE) == null ? BLANK : body.getString(DATE));
                mIn.put(MERCHANT_NAME, body.getString(MERCHANT_NAME) == null ? BLANK : body.getString(MERCHANT_NAME));
                mIn.put(WEBSITE, body.getString(WEBSITE) == null ? BLANK : body.getString(WEBSITE));
                mIn.put(SERVICE_TYPE, body.getString(SERVICE_TYPE) == null ? BLANK : body.getString(SERVICE_TYPE));
                mIn.put(GUARANTEE_ACCOUNT, body.getString(GUARANTEE_ACCOUNT) == null ? BLANK : body.getString(GUARANTEE_ACCOUNT));
                mIn.put(CARD_TYPE_QT, body.getString(CARD_TYPE_QT) == null ? BLANK : body.getString(CARD_TYPE_QT));
                mIn.put(PAYGATE, body.getString(PAYGATE) == null ? BLANK : body.getString(PAYGATE));
                mIn.put(MERCHANT_ID_ND, body.getString(MERCHANT_ID_ND) == null ? BLANK : body.getString(MERCHANT_ID_ND));
                mIn.put(CARD_TYPE_ND, body.getString(CARD_TYPE_ND) == null ? BLANK : body.getString(CARD_TYPE_ND));
                mIn.put(NOTE, body.getString(NOTE) == null ? BLANK : body.getString(NOTE));
                mIn.put(PARTNER_ID, body.getString(PARTNER_ID));
                mIn.put(MCC, body.getString(MCC) == null ? BLANK : body.getString(MCC));
                mIn.put(MERCHANT_ID_ND_VCB, body.getString(MERCHANT_ID_ND_VCB) == null ? BLANK : body.getString(MERCHANT_ID_ND_VCB));
                mIn.put(MID_ND_VCB, body.getString(MID_ND_VCB) == null ? BLANK : body.getString(MID_ND_VCB));
                mIn.put(MERCHANTIDS, body.getJsonArray(MERCHANTIDS));
                Integer acceptanceId = AcceptanceDao.updateAcceptance(mIn).getId();
                JsonArray array = body.getJsonArray(MERCHANTIDS);
                array.forEach(item -> {
                    JsonObject a = (JsonObject) item;
                    Map<String, Object> mIn2 = new HashMap<>();
                    try {
                        mIn2.put(ID, a.getInteger(ID));
                        mIn2.put(ACCEPTANCE_ID, acceptanceId);
                        mIn2.put(MERCHANT_ID_QT, a.getString(MERCHANT_ID_QT));
                        mIn2.put(MID, a.getString(MID));
                        mIn2.put(CURRENCY, a.getString(CURRENCY));
                        sendResponse(ctx, 200, AcceptanceDao.updateAcceptanceMerchantId(mIn2));
                    } catch (Exception e) {
                        logger.severe(ctx.get(REQUEST_UUID) +": "+"UPDATE Acceptance ERROR: " + e.getMessage());
                        ctx.fail(e);
                    }
                });
                sendResponse(ctx, 200, new JsonObject());
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) +": "+"UPDATE Acceptance ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    // insert and update Acceptance
    public static void sendEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                AcceptanceDto acceptanceDto = AcceptanceDao.getListAcceptancebyId(Integer.parseInt(request.getParam(ID)));

                String tittle = "Thông báo nghiệm thu";
                String content = "Dear All,\n\n";
                content += "Thông báo " + acceptanceDto.getType() + "\n";
                content += "Ngày nghiệm thu: " + DateTimeUtil.convertDatetoString(acceptanceDto.getAcceptance_date(), DateTimeUtil.DateTemplate.DD_MM_YYYY) + "\n";
                content += "Tên ĐKKD: " + acceptanceDto.getTen_dkkd() + "\n";
                content += "Merchant Name: " + acceptanceDto.getMerchant_name() + "\n";
                content += "Website: " + acceptanceDto.getWebsite() + "\n";
                content += "Loại dịch vụ " + acceptanceDto.getService_type().replaceAll(",", "/ ") + "\n";
                content += "Merchant ID\n";
                content += acceptanceDto.getMerchant_id_qt() == null ? "QT: " + acceptanceDto.getMerchant_id_qt() + "\n" : "";
                content += acceptanceDto.getMerchant_id_nd() == null ? "NĐ: " + acceptanceDto.getMerchant_id_nd() + "\n" : "";
                content += acceptanceDto.getMerchant_id_qt() == null ? "Số MID " + acceptanceDto.getCurrency() + ": " + acceptanceDto.getMid() + "\n" : "";
                content += "Cổng thanh toán: \n";
                content += acceptanceDto.getPaygate().replaceAll(",", "\n") + "\n";
                content += acceptanceDto.getMerchant_id_nd() == null ? "Nội địa\n" : "";
                content += "Loại thẻ:\n";
                content += acceptanceDto.getCard_type_qt() + "\n";
                content += acceptanceDto.getCard_type_nd() + "\n";
                content += " Đề nghị các bộ phận phối hợp:\n";
                content += "@Payment: thực hiện Active tài khoản trên hệ thống thanh toán toán ứng với ĐVCNT. \n";
                content += "@Finance: kiểm tra bản cứng Hợp đồng, nếu chưa có, báo lại bên Hỗ trợ Kinh doanh và theo dõi đến khi thu hồi Hợp đồng đủ chữ ký về.\t";
                logger.severe(ctx.get(REQUEST_UUID) +": "+"send email to " + Config.getString("email.acceptance", "<EMAIL>"));
                logger.severe(ctx.get(REQUEST_UUID) +": "+"mail title: " + tittle);
                logger.severe(ctx.get(REQUEST_UUID) +": "+"mail content: " + content);
                MailUtil.sendMail(Config.getString("email.acceptance", "<EMAIL>"), tittle, content);

                sendResponse(ctx, 200, "OK");
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) +": "+"Acceptance send email ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

}
