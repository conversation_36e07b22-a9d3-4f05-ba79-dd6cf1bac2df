package vn.onepay.portal.resources.merchantmanagement.searchkeyword.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class SearchKeywordDto {
    private Integer user_id;
    private String keyword;

    public SearchKeywordDto() {
    }

    public SearchKeywordDto(Integer user_id, String keyword) {
        this.user_id = user_id;
        this.keyword = keyword;
    }

    public Integer getUser_id() {
        return this.user_id;
    }

    public void setUser_id(Integer user_id) {
        this.user_id = user_id;
    }

    public String getKeyword() {
        return this.keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public SearchKeywordDto user_id(Integer user_id) {
        this.user_id = user_id;
        return this;
    }

    public SearchKeywordDto keyword(String keyword) {
        this.keyword = keyword;
        return this;
    }

    @Override
    public String toString() {
        return "{" + "User id='" + getUser_id() + "'" + ", keyword='" + getKeyword() + "'" + "}";
    }
}
