package vn.onepay.portal.resources.merchantmanagement.thirdPartner;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;

import com.google.gson.Gson;
import vn.onepay.portal.queue.message.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.contract.ContractDao;
import vn.onepay.portal.resources.merchantmanagement.contract.ContractHandler;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractOriginal;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoDao;
import vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto.HistoryLogDto;
import vn.onepay.portal.resources.merchantmanagement.thirdPartner.dto.ThirdPartner;
import vn.onepay.portal.utils.ParamsPool;

import static vn.onepay.portal.Util.sendResponse;

public class ThirdPartnerHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(ThirdPartnerHandler.class.getName());
    private static final Gson gson = new Gson();

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());
                BaseList<ThirdPartner> r = new BaseList<>();
                r.setList(ThirdPartnerDAO.searchPartner(mIn));
                r.setTotalItems(ThirdPartnerDAO.totalPartner(mIn));

                sendResponse(ctx, 200, r);

            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH THIRD PARTNER: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getThirdPartnerDetailById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String partnerId = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                ThirdPartner thirdPartner = ThirdPartnerDAO.getThirdPartnerDetailById(partnerId);
                mIn.put("thirdPartner", thirdPartner);

                int userId = ctx.get(X_USER_ID);
                ThirdPartnerDAO.reviewThirdPartner(userId, partnerId);
                
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveThirdPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String partnerId = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                ThirdPartner thirdPartner = ThirdPartnerDAO.getThirdPartnerDetailById(partnerId);
                mIn.put("thirdPartner", thirdPartner);

                int userId = ctx.get(X_USER_ID);
                ThirdPartnerDAO.approveThirdPartner(userId, partnerId);
                
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void rejectThirdPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String partnerId = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                ThirdPartner thirdPartner = ThirdPartnerDAO.getThirdPartnerDetailById(partnerId);
                mIn.put("thirdPartner", thirdPartner);

                int userId = ctx.get(X_USER_ID);
                ThirdPartnerDAO.rejectThirdPartner(userId, partnerId);
                
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createThirdPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String partnerId = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                ThirdPartner thirdPartner = ThirdPartnerDAO.getThirdPartnerDetailById(partnerId);
                mIn.put("thirdPartner", thirdPartner);

                int userId = ctx.get(X_USER_ID);
                ThirdPartnerDAO.createThirdPartner(userId, partnerId);
                
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addNewPartnerAndContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                // insert partner -> partner ID
                Map<String, Object> mIn = new HashMap<>();
                JsonObject partnerBody = body.getJsonObject("partner");
                mIn.put(SHORT_NAME, partnerBody.getString(SHORT_NAME) == null ? BLANK : partnerBody.getString(SHORT_NAME));
                mIn.put(PARTNER_NAME, partnerBody.getString(PARTNER_NAME) == null ? BLANK : partnerBody.getString(PARTNER_NAME));
                mIn.put(WEBSITE, partnerBody.getString(WEBSITE) == null ? BLANK : partnerBody.getString(WEBSITE));
                mIn.put(ADDRESS_LINE1, partnerBody.getString(ADDRESS_LINE1) == null ? BLANK : partnerBody.getString(ADDRESS_LINE1));
                mIn.put(ADDRESS_LINE2, partnerBody.getString(ADDRESS_LINE2) == null ? BLANK : partnerBody.getString(ADDRESS_LINE2));
                mIn.put(PROVINCE_ID, partnerBody.getString(PROVINCE_ID) == null ? BLANK : partnerBody.getString(PROVINCE_ID));
                mIn.put(PROVINCE_ID_OFFICE, partnerBody.getString(PROVINCE_ID_OFFICE) == null ? BLANK : partnerBody.getString(PROVINCE_ID_OFFICE));
                mIn.put(EMAIL, partnerBody.getString(EMAIL) == null ? BLANK : partnerBody.getString(EMAIL));
                mIn.put(TEL, partnerBody.getString(TEL) == null ? BLANK : partnerBody.getString(TEL));
                mIn.put(FAX, partnerBody.getString(FAX) == null ? BLANK : partnerBody.getString(FAX));
                mIn.put(SALE, partnerBody.getString(SALE) == null ? BLANK : partnerBody.getString(SALE));
                mIn.put(MM, partnerBody.getString(MM) == null ? BLANK : partnerBody.getString(MM));
                mIn.put(TAX_CODE, partnerBody.getString(TAX_CODE) == null ? BLANK : partnerBody.getString(TAX_CODE));
                mIn.put(BUSINESS_CODE, partnerBody.getString(BUSINESS_CODE) == null ? BLANK : partnerBody.getString(BUSINESS_CODE));
                mIn.put(BUSINESS_REGISER_DATE, partnerBody.getString(BUSINESS_REGISER_DATE) == null ? BLANK : partnerBody.getString(BUSINESS_REGISER_DATE));
                mIn.put(CAREER_ID, partnerBody.getString(CAREER_ID) == null ? BLANK : partnerBody.getString(CAREER_ID));
                mIn.put(CAREER_OTHER_DESC, partnerBody.getString(CAREER_OTHER_DESC) == null ? BLANK : partnerBody.getString(CAREER_OTHER_DESC));
                mIn.put(DESCRIPTION, partnerBody.getString(DESCRIPTION) == null ? BLANK : partnerBody.getString(DESCRIPTION));
                Integer partnerId = MerchantInfoDao.updatePartner(mIn).getId();

                // insert contract clob          
                Integer nResult = 200;
                JsonObject contractBody = body.getJsonObject("contract");
                ContractOriginal contractOriginal = ContractHandler.getBodyContract(contractBody);
                contractOriginal.setPartnerId(partnerId);
                HashMap mresult = new HashMap<>();
                nResult = ContractDao.pushContractOriginal(contractOriginal);
                mresult.put("n_result", nResult);
                sendResponse(ctx, nResult == 200 ? 200 : 500, mresult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDetailHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam("id") == null ? "" : String.valueOf(ctx.request().getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                List<HistoryLogDto> logList = ThirdPartnerDAO.getDetailLogById(id);
                mIn.put("logList", logList);
                
                sendResponse(ctx, 200, mIn);         
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

}
