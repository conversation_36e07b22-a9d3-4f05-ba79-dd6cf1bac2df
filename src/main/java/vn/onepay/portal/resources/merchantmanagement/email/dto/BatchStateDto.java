/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 5/11/19 12:24 PM
 */

package vn.onepay.portal.resources.merchantmanagement.email.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class BatchStateDto {
    private String state;
    private int total;
    private int sent;

    public BatchStateDto() {
    }

    public BatchStateDto(String state, int total, int sent) {
        this.state = state;
        this.total = total;
        this.sent = sent;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getTotal() {
        return this.total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSent() {
        return this.sent;
    }

    public void setSent(int sent) {
        this.sent = sent;
    }

    public BatchStateDto state(String state) {
        this.state = state;
        return this;
    }

    public BatchStateDto total(int total) {
        this.total = total;
        return this;
    }

    public BatchStateDto sent(int sent) {
        this.sent = sent;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " state='" + getState() + "'" + ", total='" + getTotal() + "'" + ", sent='" + getSent() + "'"
                + "}";
    }
}
