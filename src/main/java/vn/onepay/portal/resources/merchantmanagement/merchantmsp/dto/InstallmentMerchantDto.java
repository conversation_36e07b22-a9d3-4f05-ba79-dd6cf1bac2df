package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

public class InstallmentMerchantDto {
    private String installment_id;
    private String merchant_id;
    private int cancel_days;
    private String desc;
    private String state;
    private String create_time;
    private String time_periods;
    private String fee;
    private int ita_fee;
    private String data;

    public InstallmentMerchantDto() {
    }

    public String getInstallment_id() {
        return installment_id;
    }

    public void setInstallment_id(String installment_id) {
        this.installment_id = installment_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public int getCancel_days() {
        return cancel_days;
    }

    public void setCancel_days(int cancel_days) {
        this.cancel_days = cancel_days;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getTime_periods() {
        return time_periods;
    }

    public void setTime_periods(String time_periods) {
        this.time_periods = time_periods;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public int getIta_Fee() {
        return ita_fee;
    }

    public void setIta_fee(int ita_fee) {
        this.ita_fee = ita_fee;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
