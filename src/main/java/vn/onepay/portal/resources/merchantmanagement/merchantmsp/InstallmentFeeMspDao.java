package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class InstallmentFeeMspDao extends Db implements IConstants {

    /**
     * Get list all installments
     * @return
     * @throws Exception
     */
    public static BaseList<InstallmentFeeDto> getInstallmentsFee() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<InstallmentFeeDto> result = new BaseList<>();
        List<InstallmentFeeDto> listInstallmentsFee = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_installment_fee(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST INSTALLMENTS FEE error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listInstallmentsFee.add(bindInstallmentFee(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listInstallmentsFee);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Bind resultset to InstallmentFeeDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static InstallmentFeeDto bindInstallmentFee(ResultSet rs) throws Exception {
        InstallmentFeeDto dto = new InstallmentFeeDto();
        dto.setId(rs.getInt("N_ID"));
        dto.setN_3_month_fee(rs.getString("S_3_MONTH_FEE"));
        dto.setN_6_month_fee(rs.getString("S_6_MONTH_FEE"));
        dto.setN_9_month_fee(rs.getString("S_9_MONTH_FEE"));
        dto.setN_12_month_fee(rs.getString("S_12_MONTH_FEE"));
        dto.setN_15_month_fee(rs.getString("S_15_MONTH_FEE"));
        dto.setN_18_month_fee(rs.getString("S_18_MONTH_FEE"));
        dto.setN_24_month_fee(rs.getString("S_24_MONTH_FEE"));
        dto.setN_fee_type(rs.getInt("N_FEE_TYPE"));
        dto.setSwift_code(rs.getString("S_SWIFT_CODE"));
        dto.setName(rs.getString("S_NAME"));
        dto.setDescription(rs.getString("S_DESCRIPTION"));
        dto.setString_id(rs.getString("S_ID"));
        dto.setS_data(rs.getString("S_DATA"));
        return dto;
    }

}
