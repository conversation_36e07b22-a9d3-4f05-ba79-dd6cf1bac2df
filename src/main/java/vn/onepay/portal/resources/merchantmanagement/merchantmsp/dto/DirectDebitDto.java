package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;
import java.util.List;

public class DirectDebitDto {
    private String[] bank;
    private Double max_amount_per_transaction;
    private Double max_amount_per_token_per_day;


    public DirectDebitDto() {
    }

    public DirectDebitDto(String[] bank, Double max_amount_per_transaction, Double max_amount_per_token_per_day) {
        this.bank = bank;
        this.max_amount_per_transaction = max_amount_per_transaction;
        this.max_amount_per_token_per_day = max_amount_per_token_per_day;

    }


    public Double getMax_amount_per_transaction() {
        return max_amount_per_transaction;
    }

    public void setMax_amount_per_transaction(Double max_amount_per_transaction) {
        this.max_amount_per_transaction = max_amount_per_transaction;
    }

    public Double getMax_amount_per_token_per_day() {
        return max_amount_per_token_per_day;
    }

    public void setMax_amount_per_token_per_day(Double max_amount_per_token_per_day) {
        this.max_amount_per_token_per_day = max_amount_per_token_per_day;
    }

    public String[] getBank() {
        return bank;
    }

    public void setBank(String[] bank) {
        this.bank = bank;
    }




}
