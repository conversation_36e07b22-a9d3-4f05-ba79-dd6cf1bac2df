package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

import java.util.ArrayList;
import org.apache.commons.lang.StringUtils;
import io.vertx.core.json.JsonObject;

public class IpnSimpleDto {
    private String merchantId;
    private String merchantName;
    private String service;
    private String ndAuth;
    private String state;
    private String createDate;
    private String updateDate;
    private String desc;
    private String configType;
    private String method;
    private String url;
    private String retry;
    private String timeRanger;
    private String transactionType;

    public IpnSimpleDto() {
        this.merchantId = "";
        this.merchantName = "";
        this.service = "";
        this.state = "";
        this.desc = "";
        this.createDate = "";
        this.updateDate = "";
        this.configType = "";
        this.method = "";
        this.url = "";
        this.retry = "";
        this.timeRanger = "";
        this.transactionType = "";
    }

    public IpnSimpleDto(IpnDto ipn) {
        this.merchantId = ipn.getMerchantId();
        this.merchantName = ipn.getMerchantName();
        this.service = ipn.getService();
        this.ndAuth = ipn.getNdAuth();
        this.state = ipn.getState();
        this.desc = ipn.getDesc();
        this.createDate = ipn.getCreateDate();
        this.updateDate = ipn.getUpdateDate();
        this.configType = ipn.getConfigType();
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getService() {
        return service;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getRetry() {
        return retry;
    }

    public void setRetry(String retry) {
        this.retry = retry;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTimeRanger() {
        return timeRanger;
    }

    public void setTimeRanger(String timeRanger) {
        this.timeRanger = timeRanger;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMethod() {
        return method;
    }

    public String getNdAuth() {
        return ndAuth;
    }

    public void setNdAuth(String ndAuth) {
        this.ndAuth = ndAuth;
    }

    public void setMethod(String method) {
        this.method = method;
    }
    
    public String getConfigInsert() {
        ArrayList<String> result = new ArrayList<String>();
        if (!StringUtils.isEmpty(this.method)) {
            result.add(this.method);
        }
        if (!StringUtils.isEmpty(this.url)) {
            result.add(this.url);
        }
        if (!StringUtils.isEmpty(this.retry)) {
        result.add(this.retry);
        }
        if (!StringUtils.isEmpty(this.timeRanger)) {
            result.add(this.timeRanger);
        }
        if (!StringUtils.isEmpty(this.transactionType)) {
            result.add(this.transactionType);
        }
        String listString = String.join(" ", result);
        return listString;
    }

    public String toJson() {
        JsonObject data = new JsonObject();
        data.put("merchantId", this.merchantId);
        data.put("merchantName", this.merchantName);
        data.put("service", this.service);
        data.put("ndAuth", this.ndAuth);
        data.put("state", this.state);
        data.put("createDate", this.createDate);
        data.put("updateDate", this.updateDate);
        data.put("desc", this.desc);
        data.put("configType", this.configType);
        data.put("method", this.method);
        data.put("url", this.url);
        data.put("retry", this.retry);
        data.put("timeRanger", this.timeRanger);
        data.put("transactionType", this.transactionType);
        return data.toString();
    }
}
