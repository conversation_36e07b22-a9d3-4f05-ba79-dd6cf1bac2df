package vn.onepay.portal.resources.merchantmanagement.sale;

import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.ExchangeRateDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.MerchantInvoice;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleMonthDetailDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleMonthDetailInvoiceDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleNdDto;
import vn.onepay.portal.resources.merchantmanagement.sale.dto.SaleQtDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.time.Year;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class SaleDao extends Db implements IConstants {

    // get list Sale QT by partner id
    public static BaseList<SaleQtDto> getSaleQTbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<SaleQtDto> result = new BaseList<>();
        List<SaleQtDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_sale_qt_by_partner_id(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setInt(6, Convert.parseInt(mIn.get(EXCHANGE_RARE).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleQtDto().pay_gate(rs.getString("S_PAYGATE"))
                            .currency_code(rs.getString("S_CURRENCY_CODE")).mid(rs.getString("S_MID"))
                            .mcc(rs.getString("S_MCC")).merchant_name(rs.getString("S_MERCHANT_NAME"))
                            .merchant_id(rs.getString("S_MERCHANT_ID"))
                    // .month(rs.getString("S_MONTH"))
                    // .count(rs.getInt("N_COUNT_PURCHASE_SS"))
                    // .total(rs.getDouble("N_AMOUNT_PURCHASE_SS"))
                    );
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static String convertMonth(int month) throws Exception {
        String monthString = new String();
        switch (month) {
            case 1:
                monthString = "1";
                break;
            case 2:
                monthString = "2";
                break;
            case 3:
                monthString = "3";
                break;
            case 4:
                monthString = "4";
                break;
            case 5:
                monthString = "5";
                break;
            case 6:
                monthString = "6";
                break;
            case 7:
                monthString = "7";
                break;
            case 8:
                monthString = "8";
                break;
            case 9:
                monthString = "9";
                break;
            case 10:
                monthString = "10";
                break;
            case 11:
                monthString = "11";
                break;
            case 12:
                monthString = "12";
                break;
            default:
                monthString = "";
                break;
        }
        return monthString;
    }

    public static List<MerchantInvoice> getMerchantIdNd(int partnerId) throws Exception {
        List<MerchantInvoice> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ CALL " + Config.getOnepartnerSchema() + ".get_merchant_dom(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 500) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new MerchantInvoice()
                                    .merchantId(rs.getString("s_merchant_id"))
                                    .merchantNd(rs.getString("s_merchant_nd")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailInvoiceDto> getSaleInvoicebyMerchantId(String merchantId, int year) throws Exception {
        List<SaleMonthDetailInvoiceDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        String result = merchantId;
        try {
            con = getConnection119Report();
            cs = con.prepareCall(
                    "{ CALL get_invoice_sale_rp(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchantId);
            cs.setInt(5, year);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 500) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailInvoiceDto()
                                    .currency(rs.getString("S_CURRENCY"))
                                    .paygate(rs.getString("S_PAYGATE"))
                                    .month(rs.getInt("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }
    // get list Sale QT by partner id
    public static List<SaleMonthDetailDto> getSaleInvoicebyPartnerId(Map mIn) throws Exception {
        int year = Convert.parseInt(mIn.get(YEAR).toString(), 0);
        int partnerId = Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0);
        String merchantId = mIn.get(MERCHANT_ID).toString();
        Exception exception = null;
        List<String> merchants = new ArrayList<String>();
        List<SaleMonthDetailDto> listSales = new ArrayList<>();
        try {
            for (int index = 1; index < 13; index++) {
                SaleMonthDetailDto saletemp = new SaleMonthDetailDto();
                saletemp.setMonth(convertMonth(index));
                saletemp.setCountFail(0);
                saletemp.countSuccess(0);
                saletemp.setTotal(0.0);
                listSales.add(saletemp);
            }
        List<ExchangeRateDto> listExchangeRate = getExchangeRateByYear(year);
        logger.info("List<ExchangeRateDto> listExchangeRate:"+listExchangeRate);
        List<MerchantInvoice> listMerchantNd = getMerchantIdNd(partnerId);
        logger.info("List<MerchantInvoice> listMerchantNd:"+listMerchantNd);
        if(listMerchantNd.size() == 0)
        {
            MerchantInvoice invoiceTemp = new MerchantInvoice(merchantId, merchantId);
            listMerchantNd.add(invoiceTemp);
        }
        for (MerchantInvoice invoice : listMerchantNd) {
                if(invoice.getMerchantId().equals(merchantId)) {
                    logger.info("invoice.getMerchantId():"+invoice.getMerchantnd());
                    merchants.add(invoice.getMerchantnd());
                }
        }
        for (String merchant :merchants){
        List<SaleMonthDetailInvoiceDto> listInvoice = getSaleInvoicebyMerchantId(merchant, year);
        if(listInvoice.size()>0)
        {
            for (SaleMonthDetailInvoiceDto item : listInvoice) {
                // calculate to exrate
                if (item.getCurrency().equals("VND")) {
                    for (ExchangeRateDto exchange : listExchangeRate) {
                        if (item.getMonth() == exchange.getMonth()) {
                            logger.info("listExchangeRate: M "+item.getMonth());
                            logger.info("listExchangeRate: getTotal "+item.getTotal() / exchange.getValue());
                            item.setTotal(item.getTotal() / exchange.getValue());
                            break;
                        }
                    }
                }
            }
            for (SaleMonthDetailInvoiceDto item : listInvoice) {
                SaleMonthDetailDto sale = new SaleMonthDetailDto();
                sale.setMonth(convertMonth(item.getMonth()));
                sale.setCountFail(item.getCountFail());
                sale.countSuccess(item.getCountSuccess());
                sale.setTotal(item.getTotal());
                if (listSales.isEmpty()) {
                    listSales.add(sale);
                    logger.info("sale add1: "+sale.toString());
                } else {
                    boolean exist = false;
                    for (SaleMonthDetailDto dto : listSales) {
                        if (dto.getMonth().equals(sale.getMonth())) {
                            dto.countSuccess(dto.getCountSuccess() + sale.getCountSuccess());
                            dto.countFail(dto.getCountFail() + sale.getCountFail());
                            dto.setTotal(dto.getTotal() + sale.getTotal());
                            exist = true;
                            logger.info("sale add2: "+dto.toString());
                            break;
                        }
                    }
                    if(!exist){
                        listSales.add(sale);
                        logger.info("sale add3: "+sale.toString());
                    }
                }
            }
        }
        }
   
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        }
        if (exception != null)
            throw exception;

        return listSales;
    }

    // get list Sale QT by partner id
    public static List<SaleMonthDetailDto> getSaleQuickLinkbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        // BaseList<SaleQtDto> result = new BaseList<>();
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.get_sale_quicklink(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load List sale quicklink error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    // get list Sale QT by partner id
    public static List<SaleMonthDetailDto> getSaleQTbyPartnerId2(Map mIn) throws Exception {
        Exception exception = null;
        // BaseList<SaleQtDto> result = new BaseList<>();
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.get_sale_qt_by_partner_id_4(?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(PAYGATE).toString());
            cs.setString(7, mIn.get(MERCHANT_NAME).toString());
            cs.setString(8, mIn.get(CURRENCY).toString());
            cs.setString(9, mIn.get(MID).toString());
            cs.setString(10, mIn.get(MCC).toString());
            cs.setString(11, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
            // result.setTotalItems(list.size());
            // if (list.size() > 0) {
            // for (int j = 0;j < exchangeRateDtos.size();j++) {
            // for (int i =0; i < list.size(); i++) {
            // if(list.get(i).getMonth().equals(exchangeRateDtos.get(i).getMonth().toString()))
            // {
            // list.get(i).setTotal(list.get(i).getTotal() /
            // exchangeRateDtos.get(i).getValue());
            // }
            // }
            // }
            // }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleNDbyPartnerId2(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_sale_nd_by_partner_id_3(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            // cs.setInt(4, Convert.parseInt(mIn.get(EXCHANGE_RARE).toString(), 0));
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleMonthDetailDto().month(rs.getString("S_MONTH"))
                            .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                            .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                            .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleQRbyPartnerId2(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_sale_qr_by_partner_id_3(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            // cs.setInt(4, Convert.parseInt(mIn.get(EXCHANGE_RARE).toString(), 0));
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleMonthDetailDto().month(rs.getString("S_MONTH"))
                            .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                            .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                            .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static BaseList<SaleNdDto> getSaleNDbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<SaleNdDto> result = new BaseList<>();
        List<SaleNdDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_sale_nd_by_partner_id(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleNdDto().merchant_id(rs.getString("S_MERCHANT_ID"))
                    // .month(rs.getString("S_MONTH"))
                    // .count(rs.getInt("N_COUNT_PURCHASE_SS"))
                    // .total(rs.getDouble("N_AMOUNT_PURCHASE_SS"))
                    );
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static List<ExchangeRateDto> getExchangeRateByYear(Integer year) throws Exception {
        Exception exception = null;
        List<ExchangeRateDto> exchangeRateDtoList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema()
                    + ".p_merchant_management.get_exchange_rate_by_year(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, year.toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load data error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    exchangeRateDtoList.add(new ExchangeRateDto().month(rs.getInt("MONTH"))
                            .value(rs.getDouble("VALUE")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return exchangeRateDtoList;
    }

    public static List<SaleMonthDetailDto> getSaleBnplByPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_BNPL.get_sale_bnpl_by_partner_id(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleMonthDetailDto().month(rs.getString("S_MONTH"))
                            .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                            .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                            .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleUposInstallment(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_UPOS.get_upos_installment_report(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleMonthDetailDto().month(rs.getString("S_MONTH"))
                            .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                            .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                            .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleUposCardQr(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_UPOS.get_upos_card_qr_report(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            if (StringUtils.isBlank(mIn.get(TERMINAL_ID).toString())) {
                cs.setNull(6, OracleTypes.NULL);
            } else {
                cs.setString(6, mIn.get(TERMINAL_ID).toString());
            }
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new SaleMonthDetailDto().month(rs.getString("S_MONTH"))
                            .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                            .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                            .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleUposCard(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call ONEreport.PKG_UPOS_REPORT_IPORTAL.get_card_sale_by_year(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    SaleMonthDetailDto saleMonthDetailDto = new SaleMonthDetailDto().month(String.valueOf(rs.getInt("S_MONTH")))
                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS"));
                    list.add(saleMonthDetailDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleUposQr(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call ONEreport.PKG_UPOS_REPORT_IPORTAL.get_qr_sale_by_year(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    SaleMonthDetailDto saleMonthDetailDto = new SaleMonthDetailDto().month(String.valueOf(rs.getInt("S_MONTH")))
                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS"));
                    list.add(saleMonthDetailDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSaleUposVietQr(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call ONEreport.PKG_UPOS_REPORT_IPORTAL.get_vietqr_sale_by_year(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    SaleMonthDetailDto saleMonthDetailDto = new SaleMonthDetailDto().month(String.valueOf(rs.getInt("S_MONTH")))
                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS"));
                    list.add(saleMonthDetailDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<ExchangeRateDto> getExchangRate(Map mIn) throws Exception {
        Exception exception = null;
        List<ExchangeRateDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_UPOS.get_exchange_rate_usd_by_year(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(YEAR).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new ExchangeRateDto().month(rs.getInt("N_MONTH"))
                            .value(rs.getDouble("N_EXCHANGE_VALUE")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSalePayoutbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        // BaseList<SaleQtDto> result = new BaseList<>();
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.get_sale_payout(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load List sale quicklink error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<SaleMonthDetailDto> getSalePaycollectbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        // BaseList<SaleQtDto> result = new BaseList<>();
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call " + Config.getOnepartnerSchema()
                            + ".p_merchant_management.get_sale_paycollect(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load List sale quicklink error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    

    public static List<SaleMonthDetailDto> getSaleVietQRbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_sale_vietqr_by_partner_id(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }
    
    public static List<SaleMonthDetailDto> getSaleDDbyPartnerId(Map mIn) throws Exception {
        Exception exception = null;
        List<SaleMonthDetailDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPARTNER.p_merchant_management.get_sale_dd_by_partner_id(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(PARTNER_ID).toString(), 0));
            cs.setString(5, mIn.get(YEAR).toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load Acceptance error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(
                            new SaleMonthDetailDto()
                                    .month(rs.getString("S_MONTH"))
                                    .countSuccess(rs.getInt("N_COUNT_PURCHASE_SS"))
                                    .countFail(rs.getInt("N_COUNT_PURCHASE_FAIL"))
                                    .total(rs.getDouble("N_AMOUNT_PURCHASE_SS")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

}
