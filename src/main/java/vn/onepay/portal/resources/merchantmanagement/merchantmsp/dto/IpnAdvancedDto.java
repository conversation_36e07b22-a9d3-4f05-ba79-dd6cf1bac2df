package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

import io.vertx.core.json.JsonObject;

public class IpnAdvancedDto {

    private String merchantId;
    private String merchantName;
    private String service;
    private String ndAuth;
    private String state;
    private String createDate;
    private String updateDate;
    private String desc;
    private String configType;
    private String config;

    public IpnAdvancedDto() {
        this.merchantId = "";
        this.merchantName = "";
        this.service = "";
        this.state = "";
        this.desc = "";
        this.createDate = "";
        this.updateDate = "";
        this.configType = "";
        this.config = "";
    }

    public IpnAdvancedDto(IpnDto ipn) {
        this.merchantId = ipn.getMerchantId();
        this.merchantName = ipn.getMerchantName();
        this.service = ipn.getService();
        this.ndAuth =ipn.getNdAuth();
        this.state = ipn.getState();
        this.desc = ipn.getDesc();
        this.createDate = ipn.getCreateDate();
        this.updateDate = ipn.getUpdateDate();
        this.configType = ipn.getConfigType();
        this.config = ipn.getConfig();
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getConfigInsert() {
        return this.config;
    }

    public String getNdAuth() {
        return ndAuth;
    }

    public void setNdAuth(String ndAuth) {
        this.ndAuth = ndAuth;
    }

    public String toJson() {
        JsonObject data = new JsonObject();
        data.put("merchantId", this.merchantId);
        data.put("merchantName", this.merchantName);
        data.put("service", this.service);
        data.put("ndAuth", this.ndAuth);
        data.put("state", this.state);
        data.put("createDate", this.createDate);
        data.put("updateDate", this.updateDate);
        data.put("desc", this.desc);
        data.put("configType", this.configType);
        data.put("config", this.config);
        return data.toString();
    }
}