/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 4/2/19 9:44 AM
 */

package vn.onepay.portal.resources.merchantmanagement.contract.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class BranchDto {
    private int id;
    private String name;
    private String bank;

    public BranchDto() {
    }

    public BranchDto(int id, String name, String bank) {
        this.id = id;
        this.name = name;
        this.bank = bank;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBank() {
        return this.bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public BranchDto id(int id) {
        this.id = id;
        return this;
    }

    public BranchDto name(String name) {
        this.name = name;
        return this;
    }

    public BranchDto bank(String bank) {
        this.bank = bank;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", name='" + getName() + "'" + ", bank='" + getBank() + "'" + "}";
    }
}
