package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

public class MerchantMspClientDto {
    private String client_id;
    private String client_merchant_id;
    private String currency;
    private String client_terminal_id;
    private String qr_type;
    private String qr_data;
    private String instrument;
    private String state;

    public MerchantMspClientDto() {
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getClient_merchant_id() {
        return client_merchant_id;
    }

    public void setClient_merchant_id(String client_merchant_id) {
        this.client_merchant_id = client_merchant_id;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getClient_terminal_id() {
        return client_terminal_id;
    }

    public void setClient_terminal_id(String client_terminal_id) {
        this.client_terminal_id = client_terminal_id;
    }

    public String getQr_type() {
        return qr_type;
    }

    public void setQr_type(String qr_type) {
        this.qr_type = qr_type;
    }

    public String getQr_data() {
        return qr_data;
    }

    public void setQr_data(String qr_data) {
        this.qr_data = qr_data;
    }

    public String getInstrument() {
        return instrument;
    }

    public void setInstrument(String instrument) {
        this.instrument = instrument;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

}
