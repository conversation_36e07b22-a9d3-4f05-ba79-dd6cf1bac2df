/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 5/9/19 2:43 PM
 */

package vn.onepay.portal.resources.merchantmanagement.email;

import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.*;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;
import static vn.onepay.portal.Util.sendResponse;

public class EmailHandler implements IConstants {
    private static Logger logger = Logger.getLogger(EmailHandler.class.getName());

    // - Get list batch email
    public static void getListEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, EmailDao.getListEmailBulk());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    //- Get batch state
    public static void getBatchStateById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? 0 : Integer.parseInt(request.getParam(ID_EMAIL_BATCH));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID_EMAIL_BATCH, idEmailBatch);
                sendResponse(ctx, 200, EmailDao.checkEmailBatchStateID(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    //Remove group and email batch by id batch
    public static void deleteEmailGroupByBatchID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                sendResponse(ctx, 200, EmailDao.deleteGroupByBatchId(Convert.parseInt(idEmailBatch, 0)));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // insert and update email
    public static void updateEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID_EMAIL_BATCH, request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH));
                mIn.put(GROUP_NAME, body.getString(GROUP_NAME) == null ? BLANK : body.getString(GROUP_NAME));
                mIn.put(CONTRACT_TYPE, body.getString(CONTRACT_TYPE) == null ? BLANK : body.getString(CONTRACT_TYPE));
                mIn.put(PAYGATE, body.getString(PAYGATE) == null ? BLANK : body.getString(PAYGATE));
                mIn.put(SERVICE, body.getString(SERVICE) == null ? 0 : body.getString(SERVICE));
                mIn.put(GROUPS, body.getString(GROUPS) == null ? BLANK : body.getString(GROUPS));
                mIn.put(SUBJECT, body.getString(SUBJECT) == null ? BLANK : body.getString(SUBJECT));
                mIn.put(EMAIL_CONTENT, body.getString(EMAIL_CONTENT) == null ? BLANK : body.getString(EMAIL_CONTENT));
                sendResponse(ctx, 200, EmailDao.updateEmailBulk(mIn));
            } catch (Exception e) {
                logger.severe(ctx.get(REQUEST_UUID) +": "+"UPDATE Acceptance ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    //Send email by id batch
    public static void sendMailByBatchID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                sendResponse(ctx, 200, EmailDao.sendEmailBulk(Convert.parseInt(idEmailBatch, 0)));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // Get email by id batch
    public static void getEmailById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                sendResponse(ctx, 200, EmailDao.getEmailDetail(Convert.parseInt(idEmailBatch, 0)));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // upload email file
    public static void uploadMerchantFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                for (FileUpload fileUpload : fileUploadSet) {
                    // To get the uploaded file do
                    Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                    byte[] bArray = uploadedFile.getBytes();

                    InputStream inputStream = new ByteArrayInputStream(bArray);

                    Workbook workbook = new XSSFWorkbook(inputStream);
                    Sheet datatypeSheet = workbook.getSheetAt(0);
                    Iterator<Row> iterator = datatypeSheet.iterator();
                    //String strMerchantIDs = "";
                    List<String> listMerchant = new ArrayList<>();
                    while (iterator.hasNext()) {
                        Row currentRow = iterator.next();
                        String cellVal = currentRow.getCell(0).getStringCellValue();
                        if (cellVal != null && cellVal.length() > 0) {
                            listMerchant.add(cellVal);
                        }
                    }
                    String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                    sendResponse(ctx, 200, EmailDao.updateListMerchantID(Convert.parseInt(idEmailBatch, 0), listMerchant));
                    // Use the Event Bus to dispatch the file now
                    // Since Event Bus does not support POJOs by default so we need to create a MessageCodec implementation
                    // and provide methods for encode and decode the bytes
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // upload attach file
    public static void uploadAttachFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                for (FileUpload fileUpload : fileUploadSet) {
                    // To get the uploaded file do
                    Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                    byte[] bArray = uploadedFile.getBytes();
                    String fileName = URLDecoder.decode(fileUpload.fileName(), UTF_8.toString());
                    String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                    sendResponse(ctx, 200, EmailDao.updateAttachEmail(Convert.parseInt(idEmailBatch, 0), bArray, fileName));
                    // Use the Event Bus to dispatch the file now
                    // Since Event Bus does not support POJOs by default so we need to create a MessageCodec implementation
                    // and provide methods for encode and decode the bytes
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
