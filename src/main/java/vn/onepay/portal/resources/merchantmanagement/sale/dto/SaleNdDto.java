package vn.onepay.portal.resources.merchantmanagement.sale.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.List;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class SaleNdDto {
    private String merchant_id;
    private List<SaleMonthDetailDto> sale_detail_by_month;
    private List<ExchangeRateDto> exchange_date;

    public SaleNdDto() {
    }

    public SaleNdDto(String merchant_id, List<SaleMonthDetailDto> sale_detail_by_month, List<ExchangeRateDto> exchange_date) {
        this.merchant_id = merchant_id;
        this.sale_detail_by_month = sale_detail_by_month;
        this.exchange_date = exchange_date;
    }

    public List<ExchangeRateDto> getExchange_date() {
        return exchange_date;
    }

    public void setExchange_date(List<ExchangeRateDto> exchange_date) {
        this.exchange_date = exchange_date;
    }

    public String getMerchant_id() {
        return this.merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public List<SaleMonthDetailDto> getSale_detail_by_month() {
        return this.sale_detail_by_month;
    }

    public void setSale_detail_by_month(List<SaleMonthDetailDto> sale_detail_by_month) {
        this.sale_detail_by_month = sale_detail_by_month;
    }

    public SaleNdDto merchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
        return this;
    }

    public SaleNdDto sale_detail_by_month(List<SaleMonthDetailDto> sale_detail_by_month) {
        this.sale_detail_by_month = sale_detail_by_month;
        return this;
    }

    public SaleNdDto exchange_date(List<ExchangeRateDto> exchange_date) {
        this.exchange_date = exchange_date;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " merchant_id='" + getMerchant_id() + "'" + ", sale_detail_by_month='" + getSale_detail_by_month()
                + "'" + "}";
    }
}
