package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

public class ShopifyDomainDto {
    
    private String merchantId;
    private String shopifyDomain;
    private String createDate;
    private String updateDate;
    private String shopifyAccessToken;
    private String shopifyAppId;
    private String merchantCurrency;
    private String description;
    private String state;
    private String activeDate;

    public ShopifyDomainDto() {
        
    }

    public ShopifyDomainDto(String merchantId, String shopifyDomain, String createDate, String updateDate,
            String description, String shopifyAccessToken, String shopfifyAppId, String merchantCurrency,
            String state, String activeDate) {
        this.merchantId = merchantId;
        this.shopifyDomain = shopifyDomain;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.description = description;
        this.shopifyAccessToken = shopifyAccessToken;
        this.shopifyAppId = shopfifyAppId;
        this.merchantCurrency = merchantCurrency;
        this.state = state;
        this.activeDate = activeDate;
    }
    public String getShopifyDomain() {
        return shopifyDomain;
    }

    public void setShopifyDomain(String shopifyDomain) {
        this.shopifyDomain = shopifyDomain;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getShopifyAccessToken() {
        return shopifyAccessToken;
    }

    public void setShopifyAccessToken(String shopifyAccessToken) {
        this.shopifyAccessToken = shopifyAccessToken;
    }

    public String getShopfifyAppId() {
        return shopifyAppId;
    }

    public void setShopifyAppId(String shopifyAppId) {
        this.shopifyAppId = shopifyAppId;
    }

    public String getMerchantCurrency() {
        return merchantCurrency;
    }

    public void setMerchantCurrency(String merchantCurrency) {
        this.merchantCurrency = merchantCurrency;
    }
 
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(String activeDate) {
        this.activeDate = activeDate;
    }
}
