/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/28/19 9:51 AM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class CareerDto {
    private int id;
    private String tenNganhNghe;
    private int order;

    public CareerDto() {
    }

    public CareerDto(int id, String tenNganhNghe, int order) {
        this.id = id;
        this.tenNganhNghe = tenNganhNghe;
        this.order = order;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTenNganhNghe() {
        return this.tenNganhNghe;
    }

    public void setTenNganhNghe(String tenNganhNghe) {
        this.tenNganhNghe = tenNganhNghe;
    }

    public int getOrder() {
        return this.order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public CareerDto id(int id) {
        this.id = id;
        return this;
    }

    public CareerDto tenNganhNghe(String tenNganhNghe) {
        this.tenNganhNghe = tenNganhNghe;
        return this;
    }

    public CareerDto order(int order) {
        this.order = order;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", tenNganhNghe='" + getTenNganhNghe() + "'" + ", order='" + getOrder()
                + "'" + "}";
    }
}
