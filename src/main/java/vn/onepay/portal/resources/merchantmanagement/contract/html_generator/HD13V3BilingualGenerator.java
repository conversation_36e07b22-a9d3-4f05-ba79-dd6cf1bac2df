package vn.onepay.portal.resources.merchantmanagement.contract.html_generator;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.google.common.io.Files;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractOriginal;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import vn.onepay.portal.utils.ConvertNumberToWordEnglish;

public class HD13V3BilingualGenerator {
    public static Logger logger = Logger.getLogger(HD13V3BilingualGenerator.class.getName());
    public static String checked = "☒";
    public static String unChecked = "☐";

    private String templateFolder;
    private ContractOriginal contractOriginal;
    private JsonObject contractDetail;
    private String html;
    private Map<String, String> replaceMap;

    private boolean approveVisa, approveMaster, approveJCB, approveUnionPay, approveAmex, approveApple, approveGoogle, approveSamsungInter,
        approveShopify, approveINT, approveINTgroup1, approveINTgroup2, approveINT_domesRelease, approveINT_foreignRelease, 
        approveDOM, approveAPP, approveInstallment, approveBNPL, approveSamsungDomes;

    public HD13V3BilingualGenerator(ContractOriginal contractOriginal) {
        this();
        this.contractOriginal = contractOriginal;
        this.contractDetail = new JsonObject(contractOriginal.getDataContractDetail());
    }

    public HD13V3BilingualGenerator() {
        this.replaceMap = new LinkedHashMap<>();
        String contractTemplateFolder = Config.getString("file.contract_template_folder","/opt/iportal-service/classes/templates/contract_template");
        this.templateFolder = contractTemplateFolder + "/HD13_v3_bilingual/";
    }

    public String generateHtml() throws Exception{
        this.getHTMLTemplate();

        this.processCardListArray();

        this.fillBasicInfo();
        this.fillDieu2();
        this.fillPL1();
        this.fillPL2();
        this.fillPL3();

        this.fillSTT();

        this.replaceVariables();

        return this.html;
    }

    private void getHTMLTemplate() throws Exception {
        this.html = readTemplateFromFile("hd13_v3_full.html");
    }

    private void replaceVariables() throws Exception {
        // Replace đúng theo thứ tự khi push vì replaceMap là LinkedHashMap
        for (String key : this.replaceMap.keySet()) {
            String value = this.replaceMap.get(key);
            // logger.info(() -> String.format("replace \"%s\" with \"%s\"", key, value));
            this.html = this.html.replaceAll(key, value);
        }
    }

    private void fillBasicInfo() {
        try {
            String datePattern = "dd/MM/yyyy";
            this.replaceMap.put("contractNumberTxt", this.contractOriginal.getContractNumber());
            this.replaceMap.put("signatureDateTxt", this.formatDate(this.contractOriginal.getSignatureDate(), datePattern, "............"));
            this.replaceMap.put("businessNameTxt", this.contractOriginal.getBusinessName());
            this.replaceMap.put("shortNameTxt", this.contractDetail.getString("shortName"));
            this.replaceMap.put("addressBusinessTxt", this.contractDetail.getString("addressBusiness"));
            this.replaceMap.put("addressOfficeTxt", this.contractDetail.getString("addressOffice"));
            this.replaceMap.put("phoneTxt", this.contractDetail.getString("phone"));
            this.replaceMap.put("websiteTxt", this.contractDetail.getString("website"));
            this.replaceMap.put("numberBusinessTxt", this.contractDetail.getString("numberBusiness"));
            this.replaceMap.put("rangeDateTxt", this.formatDate(this.contractOriginal.getRangeDate(), datePattern, "............"));
            this.replaceMap.put("danhXungTxt", this.contractDetail.getString("danhXung"));
            this.replaceMap.put("signaturerTxt", this.contractDetail.getString("signaturer"));
            this.replaceMap.put("positionTxt", this.contractDetail.getString("position"));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void processCardListArray() {
            JsonArray cardListArray = this.contractDetail.getJsonArray("cardListArray", new JsonArray());
            this.approveVisa = cardListArray.contains("Visa");
            this.approveMaster = cardListArray.contains("MasterCard");
            this.approveJCB = cardListArray.contains("JCB");
            this.approveUnionPay = cardListArray.contains("UnionPay");
            this.approveAmex = cardListArray.contains("AmericanExpress");
            this.approveApple = cardListArray.contains("ApplePay");
            this.approveGoogle = cardListArray.contains("GooglePay");
            this.approveSamsungInter = cardListArray.contains("SamsungPay");
            this.approveShopify = cardListArray.contains("ApproveShopify");
            this.approveINT = this.approveVisa || this.approveMaster || this.approveJCB || this.approveUnionPay || this.approveAmex || this.approveApple || this.approveGoogle || this.approveSamsungInter;
            this.approveINTgroup1 = this.approveVisa || this.approveMaster || this.approveJCB || this.approveUnionPay;
            this.approveINTgroup2 = this.approveAmex;
            this.approveINT_domesRelease = cardListArray.contains("ApproveDomesticCard");
            this.approveINT_foreignRelease = cardListArray.contains("ApproveInternationalCard");
            this.approveDOM =  cardListArray.contains("ApproveOnepayDomesticCard");
            this.approveAPP =  cardListArray.contains("ApproveOnepayMobileApp");
            this.approveInstallment =  cardListArray.contains("ApproveInstallment");
            this.approveBNPL =  cardListArray.contains("ApproveBNPL");
            this.approveSamsungDomes = cardListArray.contains("ApproveSamsungPay");
    }

    private void fillDieu2() {
        try {
            this.replaceMap.put("checkBoxVisa", this.approveVisa ? checked : unChecked);
            this.replaceMap.put("checkBoxMaster", this.approveMaster ? checked : unChecked);
            this.replaceMap.put("checkBoxJCB", this.approveJCB ? checked : unChecked);
            this.replaceMap.put("checkBoxUnionPay", this.approveUnionPay ? checked : unChecked);
            this.replaceMap.put("checkBoxAmex", this.approveAmex ? checked : unChecked);
            this.replaceMap.put("checkBoxApple", this.approveApple ? checked : unChecked);
            this.replaceMap.put("checkBoxGoogle", this.approveGoogle ? checked : unChecked);
            this.replaceMap.put("checkBoxSamsung", this.approveSamsungInter ? checked : unChecked);
            this.replaceMap.put("ApproveDomesticCard", this.approveINT_domesRelease? checked : unChecked);
            this.replaceMap.put("ApproveInternationalCard", this.approveINT_foreignRelease ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayMobileApp", this.approveAPP ? checked : unChecked);
            this.replaceMap.put("ApproveInstallment", this.approveInstallment ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayMobileApp", this.approveAPP ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayDomesticCard", this.approveDOM ? checked : unChecked);
            this.replaceMap.put("ApproveBNPL", this.approveBNPL ? checked : unChecked);
            this.replaceMap.put("ApproveShopify", this.approveShopify ? checked : unChecked);
            this.replaceMap.put("ApproveSamsung", this.approveSamsungDomes ? checked : unChecked);
            this.replaceMap.put("carrerTxt", this.contractDetail.getString("carrer"));

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillPL1() {
        try {
            // STT 1, 2
            this.replaceMap.put("registerFeeTxt", this.readFormatMoney("registerFee") + " VND");
            if (Objects.equals(this.contractDetail.getString("monthFee"), "Miễn phí")) {
                this.replaceMap.put("monthFeeTxt", "Miễn phí<br><span style='color:#0070C0'>Free</span>");
            } else {
                this.replaceMap.put("monthFeeTxt", this.contractDetail.getString("monthFee"));
            }

            // STT 3
            String feeTransInternationalLabel = "";
            String feeTransInternationalTxt = "";
            if (this.approveINT) {
                feeTransInternationalTxt = this.readFormatMoney("feeTransInternational") + " VND/ giao dịch"
                    + " <i><span style='font-size:10.0pt;color:#0070C0'>(transaction)</span></i>";
                feeTransInternationalLabel = "- Thẻ quốc tế/ <i><span style='font-size:10.0pt;color:#0070C0'>International cards</span></i>";
            }
            this.replaceMap.put("feeTransInternationalTxt", feeTransInternationalTxt);
            this.replaceMap.put("feeTransInternationalLabel", feeTransInternationalLabel);

            String feeTransDomesticAndAppLabel = "";
            String feeTransDomesticAndAppTxt = "";
            if (this.approveDOM) {
                feeTransDomesticAndAppTxt = this.readFormatMoney("feeTransDomesticAndApp") + " VND/ giao dịch "
                    + " <i><span style='font-size:10.0pt;color:#0070C0'>(transaction)</span></i>";
                feeTransDomesticAndAppLabel = "- Thẻ nội địa/ <i><span style='font-size:10.0pt;color:#0070C0'>Domestic cards</span></i>";
            }
            this.replaceMap.put("feeTransDomesticAndAppLabel", feeTransDomesticAndAppLabel);
            this.replaceMap.put("feeTransDomesticAndAppTxt", feeTransDomesticAndAppTxt);

            String feeAppLabel = "";
            String feeAppTxt = "";
            if (this.approveAPP) {
                feeAppTxt = this.readFormatMoney("feeApp") + " VND/ giao dịch"
                    + " <i><span style='font-size:10.0pt;color:#0070C0'>(transaction)</span></i>";
                feeAppLabel = "- Ứng dụng di động/ <i><span style='font-size:10.0pt;color:#0070C0'>Mobile applications</span></i>";
            }
            this.replaceMap.put("feeAppLabel", feeAppLabel);
            this.replaceMap.put("feeAppTxt", feeAppTxt);

            int table_fee__stt4_rowspan = 1;

            // STT 4
            if (this.approveINT) {
                table_fee__stt4_rowspan++;
                String table_fee__payment_int = "";
                String list_card_int_group1 = "";
                String list_card_int_group2 = "";
                table_fee__payment_int = readTemplateFromFile("PL1/table_fee__payment_int.html");
                this.replaceMap.put("table_fee__payment_int", table_fee__payment_int);

                if (this.approveINTgroup1) {
                    List<String> listCardInt = new LinkedList<String>();
                    if (this.approveVisa) listCardInt.add("Visa");
                    if (this.approveMaster) listCardInt.add("MasterCard");
                    if (this.approveJCB) listCardInt.add("JCB");
                    if (this.approveUnionPay) listCardInt.add("UnionPay");
                    list_card_int_group1 = "- " + listCardInt.stream().collect(Collectors.joining(", "));
                }
                if (this.approveINTgroup2) list_card_int_group2 = "- American Express";
                this.replaceMap.put("list_card_int_group1", list_card_int_group1);
                this.replaceMap.put("list_card_int_group2", list_card_int_group2);

                this.replaceMap.put("approveCardType01InternationalTxt", 
                    !this.approveINTgroup1 ? ""
                    : !this.approveINT_domesRelease? "Không áp dụng / <span style='color:#0070C0'>Not apply</span>"
                    : this.readFeePercent("approveCardType01International"));
                this.replaceMap.put("americanExpress01InternationalTxt", 
                    !this.approveINTgroup2 ? ""
                    : !this.approveINT_domesRelease? "Không áp dụng / <span style='color:#0070C0'>Not apply</span>"
                    : this.readFeePercent("americanExpress01International"));
                this.replaceMap.put("approveCardType02InternationalTxt", 
                    !this.approveINTgroup1 ? ""
                    : !this.approveINT_foreignRelease? "Không áp dụng / <span style='color:#0070C0'>Not apply</span>"
                    : this.readFeePercent("approveCardType02International"));
                this.replaceMap.put("americanExpress02InternationalTxt", 
                    !this.approveINTgroup2 ? ""
                    : !this.approveINT_foreignRelease? "Không áp dụng / <span style='color:#0070C0'>Not apply</span>"
                    : this.readFeePercent("americanExpress02International"));
            } else {
                this.replaceMap.put("table_fee__payment_int", "");
            }

            if (this.approveDOM) {
                table_fee__stt4_rowspan++;
                String table_fee__payment_dom = readTemplateFromFile("PL1/table_fee__payment_dom.html");
                this.replaceMap.put("table_fee__payment_dom", table_fee__payment_dom);
                this.replaceMap.put("feePaymentDomesticAndAppTxt", this.readFeePercent("feePaymentDomesticAndApp"));
            } else {
                this.replaceMap.put("table_fee__payment_dom", "");
            }

            if (this.approveAPP) {
                table_fee__stt4_rowspan++;
                String table_fee__payment_app = readTemplateFromFile("PL1/table_fee__payment_app.html");
                this.replaceMap.put("table_fee__payment_app", table_fee__payment_app);
                this.replaceMap.put("percentQrMobileTxt", this.readFeePercent("percentQrMobile"));
                this.replaceMap.put("percentQrGrabTxt", this.readFeePercent("percentQrGrab"));
                this.replaceMap.put("percentQrShopeeTxt", this.readFeePercent("percentQrShopee"));
                this.replaceMap.put("percentQrZaloTxt", this.readFeePercent("percentQrZalo"));
                this.replaceMap.put("percentQrMoMoTxt", this.readFeePercent("percentQrMoMo"));
                this.replaceMap.put("percentQrOtherTxt", this.readFeePercent("percentQrOther"));
            } else {
                this.replaceMap.put("table_fee__payment_app", "");
            }

            String table_fee__shopify = "";
            if (this.approveShopify) {
                table_fee__shopify += this.readTemplateFromFile("PL1/table_fee__shopify.html");
            }
            this.replaceMap.put("table_fee__shopify", table_fee__shopify);
            this.replaceMap.put("table_fee__stt4_rowspan", String.valueOf(table_fee__stt4_rowspan));

            if (this.approveDOM || this.approveAPP) {
                String PL1__dieu_1_c = this.readTemplateFromFile("PL1/dieu_1_c.html");
                this.replaceMap.put("PL1__dieu_1_c", PL1__dieu_1_c);
            } else {
                this.replaceMap.put("PL1__dieu_1_c", "");
            }

            String PL1__dieu_2_1_bcd = this.readTemplateFromFile("PL1/dieu_2_1_bcd.html");
            this.replaceMap.put("PL1__dieu_2_1_bcd", PL1__dieu_2_1_bcd);
            if ("t1".equalsIgnoreCase(this.contractDetail.getString("tgTamUngSelection"))) {
                this.replaceMap.put("tam_ung_truoc_phien_EN", "01 (one) day");
                this.replaceMap.put("tam_ung_sau_phien_EN", "02 (two) days");
                this.replaceMap.put("tam_ung_truoc_phien", "01 (một) ngày");
                this.replaceMap.put("tam_ung_sau_phien", "02 (hai) ngày");
            } else if ("t2".equalsIgnoreCase(this.contractDetail.getString("tgTamUngSelection"))) {
                this.replaceMap.put("tam_ung_truoc_phien_EN", "02 (two) days");
                this.replaceMap.put("tam_ung_sau_phien_EN", "03 (three) days");
                this.replaceMap.put("tam_ung_truoc_phien", "02 (hai) ngày");
                this.replaceMap.put("tam_ung_sau_phien", "03 (ba) ngày");
            } else {
                this.replaceMap.put("tam_ung_truoc_phien_EN", "..............");
                this.replaceMap.put("tam_ung_sau_phien_EN", "..............");
                this.replaceMap.put("tam_ung_truoc_phien", "..............");
                this.replaceMap.put("tam_ung_sau_phien", "..............");
            }

            if ("Miễn phí".equalsIgnoreCase(this.contractDetail.getString("monthFee"))) {
                this.replaceMap.put("PL1__CB_month_fee_free", checked);
                this.replaceMap.put("PL1__CB_month_fee_collect", unChecked);
            } else {
                this.replaceMap.put("PL1__CB_month_fee_free", unChecked);
                this.replaceMap.put("PL1__CB_month_fee_collect", checked);
            }

            if ("ThuPhiCungButToanBaoCo".equalsIgnoreCase(this.contractDetail.getString("hinhThucThuPhi"))) {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", checked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", unChecked);
            } else if ("ThuTheoThang".equalsIgnoreCase(this.contractDetail.getString("hinhThucThuPhi"))) {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", unChecked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", checked);
            } else {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", unChecked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", unChecked);
            }

            StringBuilder table_merchant__body = new StringBuilder();
            JsonArray subTableMerchant = this.contractDetail.getJsonArray("subTableMerchant");
            for (int i = 0; i < subTableMerchant.size(); i++) {
                JsonObject row = subTableMerchant.getJsonObject(i);
                String rowHtml = readTemplateFromFile("PL1/table_merchant__body.html");

                rowHtml = rowHtml.replaceAll("countTxt", String.valueOf(i + 1));
                rowHtml = rowHtml.replaceAll("merchantTxt", row.getString("merchant", ""));
                rowHtml = rowHtml.replaceAll("accountNameTxt", row.getString("accountName", ""));
                rowHtml = rowHtml.replaceAll("accountNumberTxt", row.getString("accountNumber", ""));
                rowHtml = rowHtml.replaceAll("bankTxt", row.getString("bank", ""));
                table_merchant__body.append(rowHtml);
            }
            this.replaceMap.put("table_merchant__body", table_merchant__body.toString());

            String PL1__dieu_2_4_1_a = this.readTemplateFromFile("PL1/dieu_2_4_1_a.html");
            String PL1__dieu_2_4_1_b = this.readTemplateFromFile("PL1/dieu_2_4_1_b.html");
            String PL1__dieu_2_4_1_c = this.readTemplateFromFile("PL1/dieu_2_4_1_c.html");
            String PL1__dieu_2_4_2v3 = this.readTemplateFromFile("PL1/dieu_2_4_2v3.html");
            String PL1__index_2_4_4 = "2.4.4";
            String PL1__dieu_2_5 = this.readTemplateFromFile("PL1/dieu_2_5.html");
            String n_amount_khoanDamBaoInputTxt ="";
            String s_amount_khoanDamBaoInputTxt ="";
            String s_amount_khoanDamBaoInputTxtEN ="";
            String khoanDamBaoKeepPercentTxt ="";
            String s_khoanDamBaoKeepPercentTxt_EN = "";
            if ("Mien".equalsIgnoreCase(this.contractDetail.getString("khoanDamBaoSelection"))) {
                PL1__index_2_4_4 = "2.4.2";
                PL1__dieu_2_4_2v3 = "";
                PL1__dieu_2_4_1_a = "";
                PL1__dieu_2_4_1_b = "";
                PL1__dieu_2_5 = "";
                this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
            } else if ("kyQuyKeep".equalsIgnoreCase(this.contractDetail.getString("kyQuyType"))) {
                try {
                    PL1__dieu_2_4_1_a = "";
                    PL1__dieu_2_4_1_c = "";
                    this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                    this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                    this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
                    n_amount_khoanDamBaoInputTxt = this.readFormatMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxt = this.readFormatStringMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxtEN = this.readFormatStringMoneyEnglish("khoanDamBaoInput");
                    khoanDamBaoKeepPercentTxt = this.contractDetail.getInteger("keepPercent").toString();
                    s_khoanDamBaoKeepPercentTxt_EN = this.readFormatStringMoneyEnglish("keepPercent").toLowerCase();
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "", e);
                }
            } else if ("kyQuyStandard".equalsIgnoreCase(this.contractDetail.getString("kyQuyType"))) {
                try {
                    PL1__dieu_2_4_1_b = "";
                    PL1__dieu_2_4_1_c = "";
                    this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                    this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                    this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
                    n_amount_khoanDamBaoInputTxt = this.readFormatMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxt = this.readFormatStringMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxtEN = this.readFormatStringMoneyEnglish("khoanDamBaoInput");
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "", e);
                }
            } else {
                this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
            }
            this.replaceMap.put("PL1__dieu_2_4_2v3", PL1__dieu_2_4_2v3);
            this.replaceMap.put("PL1__dieu_2_5", PL1__dieu_2_5);
            String stkGiaiKhoanhTxt = this.contractDetail.getString("stkGiaiKhoanh");
            String giaiKhoanhOpenByBankTxt = this.contractDetail.getString("openByBank");
            this.replaceMap.put("stkGiaiKhoanhTxt", stkGiaiKhoanhTxt);
            this.replaceMap.put("giaiKhoanhOpenByBankTxt", giaiKhoanhOpenByBankTxt);
            this.replaceMap.put("n_amount_khoanDamBaoInputTxt", n_amount_khoanDamBaoInputTxt);
            this.replaceMap.put("s_amount_khoanDamBaoInputTxtEN", s_amount_khoanDamBaoInputTxtEN);
            this.replaceMap.put("s_amount_khoanDamBaoInputTxt", s_amount_khoanDamBaoInputTxt);
            this.replaceMap.put("s_khoanDamBaoKeepPercentTxt_EN", s_khoanDamBaoKeepPercentTxt_EN);
            this.replaceMap.put("khoanDamBaoKeepPercentTxt", khoanDamBaoKeepPercentTxt);

            //    HD song ngu khong auto fill phan nay
            // this.replaceMap.put("kyHanFDTxtEN", this.contractDetail.getString("kyHanFD", "..."));
            // this.replaceMap.put("kyHanFDTxt", this.contractDetail.getString("kyHanFD", "..."));
            // String stkOPKyQuy = this.contractDetail.getString("accountNumber");
            // if (Objects.equals(stkOPKyQuy, "vietinbank")) {
            //     stkOPKyQuy = "Vietinbank";
            // } else if (Objects.equals(stkOPKyQuy, "vietcombank")) {
            //     stkOPKyQuy = "Vietcombank";
            // } else if (Objects.equals(stkOPKyQuy, "") || Objects.equals(stkOPKyQuy, "other")) {
            //     stkOPKyQuy = this.contractDetail.getString("accountNumberOther");
            // }
            // this.replaceMap.put("stkOPKyQuyTxt", stkOPKyQuy);

            this.replaceMap.put("PL1__index_2_4_4", PL1__index_2_4_4);

            String PL1__dieu_3_6 = this.readTemplateFromFile("PL1/dieu_3_6.html");
            String PL1__index_3_7 = "3.7";
            String PL1__index_3_8 = "3.8";
            String PL1__index_3_9 = "3.9";
            if (!this.approveShopify) {
                PL1__dieu_3_6 = "";
                PL1__index_3_7 = "3.6";
                PL1__index_3_8 = "3.7";
                PL1__index_3_9 = "3.8";
            } 
            this.replaceMap.put("PL1__dieu_3_6", PL1__dieu_3_6);
            this.replaceMap.put("PL1__index_3_7", PL1__index_3_7);
            this.replaceMap.put("PL1__index_3_8", PL1__index_3_8);
            this.replaceMap.put("PL1__index_3_9", PL1__index_3_9);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillPL2() {
        try {
            String PL2__main = this.readTemplateFromFile("PL2/main.html");
            if (!this.approveInstallment) {
                PL2__main = "";
            }
            this.replaceMap.put("PL2__main", PL2__main);

            JsonArray subTableFee = this.contractDetail.getJsonArray("subTableFee");
            StringBuilder PL2__table_fee__body = new StringBuilder();
            String templateRow = this.readTemplateFromFile("PL2/table_fee__body.html");
            for (int i = 0; i < subTableFee.size(); i++) {
                JsonObject row = subTableFee.getJsonObject(i);
                String rowHtml = templateRow.replace("soThuTu", String.valueOf(i + 1));
                String bankName = row.getString("bankName");
                if ("MSB".equalsIgnoreCase(bankName))
                    bankName = "(*) " + bankName;
                if ("FECredit".equalsIgnoreCase(bankName))
                    bankName = "(**) " + bankName;
                rowHtml = rowHtml.replace("bankName", bankName);
                rowHtml = rowHtml.replace("threeMonths", !isEmpty(row.getString("threeMonths")) ? row.getString("threeMonths") + "%" : "");
                rowHtml = rowHtml.replace("sixMonths", !isEmpty(row.getString("sixMonths")) ? row.getString("sixMonths") + "%" : "");
                rowHtml = rowHtml.replace("nineMonths", !isEmpty(row.getString("nineMonths")) ? row.getString("nineMonths") + "%" : "");
                rowHtml = rowHtml.replace("twelveMonths", !isEmpty(row.getString("twelveMonths")) ? row.getString("twelveMonths") + "%" : "");
                rowHtml = rowHtml.replace("fifteenMonths", !isEmpty(row.getString("fifteenMonths")) ? row.getString("fifteenMonths") + "%" : "");
                rowHtml = rowHtml.replace("eighteenMonths", !isEmpty(row.getString("eighteenMonths")) ? row.getString("eighteenMonths") + "%" : "");
                rowHtml = rowHtml.replace("twentyFourMonths", !isEmpty(row.getString("twentyFourMonths")) ? row.getString("twentyFourMonths") + "%" : "");
                PL2__table_fee__body.append(rowHtml);
            }
            this.replaceMap.put("PL2__table_fee__body", PL2__table_fee__body.toString());

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillPL3() {
        try {
            String PL3__main = this.approveBNPL? this.readTemplateFromFile("PL3/main.html") : "";

            this.replaceMap.put("PL3__main", PL3__main);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }

    }

    private void fillSTT() {
        try {
            String PL2__index_main = "02";
            String PL3__index_main = "03";

            if (!this.approveInstallment && this.approveBNPL) {
                PL2__index_main = "03";
                PL3__index_main = "02";
            } else if (this.approveInstallment && !this.approveBNPL) {
                PL2__index_main = "02";
                PL3__index_main = "03";
            } else if (!this.approveInstallment && !this.approveBNPL) {
                PL2__index_main = "02";
                PL3__index_main = "03";
            }

            this.replaceMap.put("PL2__index_main", PL2__index_main);
            this.replaceMap.put("PL3__index_main", PL3__index_main);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }

    }

    private String readTemplateFromFile(String filename) throws Exception {
        String fullPath = this.templateFolder + filename;
        logger.info(() -> "readTemplateFromFile: " + fullPath);
        String content = Files.asCharSource(new File(fullPath), StandardCharsets.UTF_8).read();
        return content;
    }

    private String readFormatMoney(String key) {
        DecimalFormat df = new DecimalFormat("#,##0");
        Object value = this.contractDetail.getValue(key);

        if (value instanceof String) {
            try {
                return Objects.isNull(value) || Objects.equals(value, "")? 
                    "0" : df.format(Integer.parseInt( (String) value));
            } catch (Exception e) {
                return value.toString();
            }
        }
        if (value instanceof Integer) {
            Integer zero = new Integer(0);
            return Objects.equals((Integer) value, zero)? "0" : df.format(value);
        }
        if (value instanceof Long) {
            Long zero = new Long(0);
            return Objects.equals((Long) value, zero)? "0" : df.format(value);
        }
        return "";
    }

    private String readFormatStringMoney(String key) {
        Object value = this.contractDetail.getValue(key);
        String s_value = "";

        if (Objects.isNull(value) || Objects.equals(value, "")) 
            return "Không";

        if (value instanceof Integer || value instanceof String) {
            s_value = String.valueOf(value);
        }

        String result = s_value;
        try {
            result =  ConvertMoneyNumberToString.readNumberToMoney(s_value)
                .stream()
                .collect(Collectors.joining(" "));
        } catch (Exception e) {
            logger.warning(() -> "*** Exception trong ham util nhung ket qua van dung ***");
            logger.warning(() -> e.getStackTrace().toString());
        }

        return result;
    }

    private String readFormatStringMoneyEnglish(String key) {
        Object value = this.contractDetail.getValue(key);
        int i_value = 0;

        if (Objects.isNull(value) || Objects.equals(value, "")) 
            return "Zero";

        if (value instanceof String) {
            try {
                i_value = Integer.parseInt((String) value);
            } catch (Exception e) {
                return (String) value;
            }
        }

        if (value instanceof Integer) {
            i_value = (Integer) value;
        }

        if (i_value > 999999999) return "Number too large";
        String result = value.toString();
        try {
            result =  ConvertNumberToWordEnglish.convertNumberToWord(i_value);
            
            // Capitalize first letter
            if (result.length() > 0)
                result = result.substring(0, 1).toUpperCase()
                    + result.substring(1).toLowerCase();
        } catch (Exception e) {
            logger.warning(() -> e.getStackTrace().toString());
        }

        return result;
    }

    private String readFeePercent(String key) {
        Object value = this.contractDetail.getValue(key);
        
        if (Objects.isNull(value) || Objects.equals(value, ""))
            return "-";

        return value.toString() + " %";
    }

    private static Boolean isEmpty(String s) {
        return s == null || s.length() == 0;
    }

    private String formatDate(Timestamp date, String pattern, String def) {
        if (date == null) return def;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

}
