package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class ClientMspHandler implements IConstants {

    /**
     * GET /msp/client
     * @param ctx
     */
    public static void getClients(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, ClientMspDao.getClients());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static Logger logger = Logger.getLogger(ClientMspHandler.class.getName());

}
