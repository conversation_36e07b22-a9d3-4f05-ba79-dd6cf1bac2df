package vn.onepay.portal.resources.merchantmanagement.sale.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.List;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class SaleQtDto {
    private String pay_gate;
    private String currency_code;
    private String mid;
    private String mcc;
    private String merchant_name;
    private String merchant_id;
    private List<SaleMonthDetailDto> sale_detail_by_month;
    private List<ExchangeRateDto> exchange_date;

    public SaleQtDto() {
    }

    public SaleQtDto(String pay_gate, String currency_code, String mid, String mcc, String merchant_name,
            String merchant_id, List<SaleMonthDetailDto> sale_detail_by_month, List<ExchangeRateDto> exchange_date) {
        this.pay_gate = pay_gate;
        this.currency_code = currency_code;
        this.mid = mid;
        this.mcc = mcc;
        this.merchant_name = merchant_name;
        this.merchant_id = merchant_id;
        this.sale_detail_by_month = sale_detail_by_month;
        this.exchange_date = exchange_date;
    }

    public List<ExchangeRateDto> getExchange_date() {
        return exchange_date;
    }

    public void setExchange_date(List<ExchangeRateDto> exchange_date) {
        this.exchange_date = exchange_date;
    }

    public String getPay_gate() {
        return this.pay_gate;
    }

    public void setPay_gate(String pay_gate) {
        this.pay_gate = pay_gate;
    }

    public String getCurrency_code() {
        return this.currency_code;
    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
    }

    public String getMid() {
        return this.mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getMcc() {
        return this.mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMerchant_name() {
        return this.merchant_name;
    }

    public void setMerchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
    }

    public String getMerchant_id() {
        return this.merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public List<SaleMonthDetailDto> getSale_detail_by_month() {
        return this.sale_detail_by_month;
    }

    public void setSale_detail_by_month(List<SaleMonthDetailDto> sale_detail_by_month) {
        this.sale_detail_by_month = sale_detail_by_month;
    }

    public SaleQtDto pay_gate(String pay_gate) {
        this.pay_gate = pay_gate;
        return this;
    }

    public SaleQtDto currency_code(String currency_code) {
        this.currency_code = currency_code;
        return this;
    }

    public SaleQtDto mid(String mid) {
        this.mid = mid;
        return this;
    }

    public SaleQtDto mcc(String mcc) {
        this.mcc = mcc;
        return this;
    }

    public SaleQtDto merchant_name(String merchant_name) {
        this.merchant_name = merchant_name;
        return this;
    }

    public SaleQtDto merchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
        return this;
    }

    public SaleQtDto sale_detail_by_month(List<SaleMonthDetailDto> sale_detail_by_month) {
        this.sale_detail_by_month = sale_detail_by_month;
        return this;
    }

    public SaleQtDto exchange_date(List<ExchangeRateDto> exchange_date) {
        this.exchange_date = exchange_date;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " pay_gate='" + getPay_gate() + "'" + ", currency_code='" + getCurrency_code() + "'" + ", mid='"
                + getMid() + "'" + ", mcc='" + getMcc() + "'" + ", merchant_name='" + getMerchant_name() + "'"
                + ", merchant_id='" + getMerchant_id() + "'" + ", sale_detail_by_month='" + getSale_detail_by_month()
                + "'" + "}";
    }
}
