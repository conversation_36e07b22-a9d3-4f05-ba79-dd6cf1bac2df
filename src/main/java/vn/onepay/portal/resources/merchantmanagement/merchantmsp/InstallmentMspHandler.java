package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.InstallmentMerchantDto;

import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class InstallmentMspHandler implements IConstants {

    /**
     * GET /msp/installment
     * @param ctx
     */
    public static void getInstallment(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, InstallmentMspDao.getInstallments());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static Logger logger = Logger.getLogger(InstallmentMspHandler.class.getName());

}
