package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.ClientDto;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MerchantMspClientDto;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MerchantMspDto;
import vn.onepay.portal.utils.QrUtil;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class ClientMspDao extends Db implements IConstants {

    /**
     * Get all clients
     * @return
     * @throws Exception
     */
    public static BaseList<ClientDto> getClients() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<ClientDto> result = new BaseList<>();
        List<ClientDto> listClients = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.CLIENT_GET_ALL(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST CLIENTS error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listClients.add(bindClient(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listClients);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }


    /**
     * Get List of MerchantMspClientDto by merchant id
     * @param merchantId
     * @return
     * @throws Exception
     */
    public static List<MerchantMspClientDto> getClientMerchantByMerchantId(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        List<MerchantMspClientDto> result = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.CLIENT_MERCH_GET_BY_ID(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET MSP CLIENT MERCHANT BY MERCHANT ID " + merchantId +" error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindClientMerchant(rs));
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Upsert Merchant client by merchant id
     * @param merchantId
     * @param merchantClient
     * @throws Exception
     */
    public static void upsertMerchantMspClient(String merchantId, MerchantMspClientDto merchantClient) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        MerchantMspDto merchantMspDao = MerchantMspDao.getMerchantGeneralById(merchantId);
        if (merchantClient == null) {
            return;
        }

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.CLIENT_MERCHANT_UPSERT_V2(?,?,?,?,?,?,?,?,?,?,?,?) }");
            cs.setString(1, merchantClient.getClient_merchant_id());
            cs.setString(2, merchantId);
            cs.setString(3, merchantClient.getClient_id());
            cs.setString(4, "active");
            cs.setString(5, merchantClient.getCurrency());
            cs.setString(6, merchantClient.getInstrument());
            cs.setString(7, merchantClient.getClient_terminal_id());
            cs.setString(8, merchantClient.getQr_type());
            cs.setString(9, QrUtil.createMpayQrData(merchantMspDao, merchantClient.getClient_terminal_id()));
            cs.registerOutParameter(10, OracleTypes.NUMBER);
            cs.registerOutParameter(11, OracleTypes.NUMBER);
            cs.registerOutParameter(12, OracleTypes.VARCHAR);

            cs.execute();
            int nError = cs.getInt(11);
            String sError = cs.getString(12);
            if (nError != 200) {
                logger.severe("UPSERT MSP MERCHANT CLIENT WITH MERCHANT ID " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    /**
     * Delete client-merchant
     * @param merchantId
     * @param clientId
     * @throws Exception
     */
    public static void deleteClientMerchant(String merchantId, String clientId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.CLIENT_MERCHANT_DELETE(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.setString(2, clientId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("DELETE CLIENT MERCHANT error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    /**
     * Bind resultset to ClientDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static ClientDto bindClient(ResultSet rs) throws Exception {
        ClientDto clientDto = new ClientDto();
        clientDto.setId(rs.getString("S_ID"));
        clientDto.setDescription(rs.getString("S_DESCRIPTION"));
        clientDto.setState(rs.getString("S_STATE"));
        return clientDto;
    }


    /**
     * Bind resulset to MerchantMspClientDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static MerchantMspClientDto bindClientMerchant(ResultSet rs) throws Exception {
        MerchantMspClientDto clientMerchant = new MerchantMspClientDto();
        clientMerchant.setClient_id(rs.getString("S_CLIENT_ID"));
        clientMerchant.setClient_merchant_id(rs.getString("S_CLIENT_MERCHANT_ID"));
        clientMerchant.setCurrency(rs.getString("S_CURRENCY"));
        clientMerchant.setClient_terminal_id(rs.getString("S_CLIENT_TERMINAL_ID"));
        clientMerchant.setQr_type(rs.getString("S_QR_TYPE"));
        clientMerchant.setQr_data(rs.getString("S_QR_DATA"));
        clientMerchant.setInstrument(rs.getString("S_ACCEPT_INSTRUMENTS"));
        return clientMerchant;
    }

}
