package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;
import vn.onepay.portal.resources.system_management.config_merchant_msp.SystemManagerMspDao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Level;

import io.vertx.core.json.JsonObject;

public class MerchantMspDao extends Db {

    /**
     * Get list of MerchantMspDto with conditions
     * @param map
     * @return
     * @throws Exception
     */
    public static BaseList<MerchantMspDto> getMerchants(Map map) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<MerchantMspDto> result = new BaseList<>();
        List<MerchantMspDto> listMerchants = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.MERCHANT_SEARCH3(?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, map.get(KEYWORD).toString());
            cs.setInt(6, Convert.parseInt(map.get(PAGE_SIZE).toString(), Integer.MAX_VALUE));
            cs.setInt(7, Convert.parseInt(map.get(PAGE).toString(), 0));
            if (map.get(MERCHANT_ID) == null) {
                cs.setNull(8, OracleTypes.NULL);
            } else {
                cs.setString(8, map.get(MERCHANT_ID).toString().trim());
            }
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("SEARCH MSP MERCHANT error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listMerchants.add(bindMerchantGeneral(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listMerchants);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;

        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Get full merchant information by merchant id
     * @param merchantId
     * @return
     * @throws Exception
     */
    public static MerchantMspDto getMerchantById(String merchantId) throws Exception {
        MerchantMspDto merchant = getMerchantGeneralById(merchantId);
        merchant.setNotification(getNotificationByMerchantId(merchantId));
        merchant.setClient_merchants(ClientMspDao.getClientMerchantByMerchantId(merchantId));
        merchant.setInstallment_merchants(InstallmentMspDao.getInstallmentMerchantByMerchantId(merchantId));
        merchant.setCustomer_fee_merchants(MspCustomerFeeDao.get(merchantId));
        merchant.setTheme_color_merchants(MspThemeColorDao.get(merchantId));
        merchant.setBin_rule_group_config(MspBinRuleConfigDao.getBinRule(merchantId));
        merchant.setDirect_debit(DirectDebitDao.getDirectDebitById(merchantId));
        return merchant;
    }

    /**
     * Upsert full merchant information
     * @param merchantDto
     * @throws Exception
     */
    public static int upsertMerchant(MerchantMspDto merchantDto) throws Exception {
        // 1. Upsert general information
        int res = upsertMerchantMspGeneral(merchantDto);

        if (res == 1) {
            // 2. Upsert notification information
            upsertMerchantMspNotification(merchantDto.getMerchant_id(), merchantDto.getNotification());

            // 3. Upsert merchant clients
            // 3.1 Get list of merchantClients to check if there is a record which need to delete
            List<MerchantMspClientDto> listMerchantClients = ClientMspDao.getClientMerchantByMerchantId(merchantDto.getMerchant_id());
            for(MerchantMspClientDto merchantClient : listMerchantClients) {
                boolean isExist = merchantDto.getClient_merchants().stream().anyMatch(mc -> mc.getClient_id().equals(merchantClient.getClient_id()));
                if(!isExist) {
                    ClientMspDao.deleteClientMerchant(merchantDto.getMerchant_id(), merchantClient.getClient_id());
                }
            }

            // 3.2 Loop to upsert
            for (MerchantMspClientDto merchantClient : merchantDto.getClient_merchants()) {
                ClientMspDao.upsertMerchantMspClient(merchantDto.getMerchant_id(), merchantClient);
            }

            // 4. Upsert installment merchant
            // 4.1 Get list of installmentMerchants to check if there is a record which need to delete
            List<InstallmentMerchantDto> listInstallmentMerchant = InstallmentMspDao.getInstallmentMerchantByMerchantId(merchantDto.getMerchant_id());
            for(InstallmentMerchantDto installmentMerchant : listInstallmentMerchant) {
                boolean isExist = merchantDto.getInstallment_merchants().stream().anyMatch(im -> im.getInstallment_id().equals(installmentMerchant.getInstallment_id()));
                if(!isExist) {
                    InstallmentMspDao.deleteInstallmentMerchant(merchantDto.getMerchant_id(), installmentMerchant.getInstallment_id());
                }
            }

            // 4.2 Loop to upsert
            for (InstallmentMerchantDto installmentMerchant : merchantDto.getInstallment_merchants()) {
                InstallmentMspDao.upsertInstallmentMerchant(merchantDto.getMerchant_id(), installmentMerchant);
            }

            // 5.2 Loop to upsert
            //delete customer fee by merchant
            Integer result = MspCustomerFeeDao.delete(merchantDto.getMerchant_id());
            if(result == 200 && merchantDto.getCustomer_fee_merchants().getFee().size() > 0){
//                for (CustomerFeeMerchantDto customerFeeMerchantDto : merchantDto.getCustomer_fee_merchants()) {
                    MspCustomerFeeDao.put(merchantDto.getMerchant_id(), merchantDto.getCustomer_fee_merchants());
//                }
            }

            // 6.2 Loop to upsert
            MspThemeColorDao.put(merchantDto.getMerchant_id(), merchantDto.getTheme_color_merchants());

            // 7. Upsert delete BIN Rule group config
            List<MspBinRuleConfigDto> listBinRuleConfig1 = MspBinRuleConfigDao.getBinRule(merchantDto.getMerchant_id());
            List<MspBinRuleConfigDto> listBinRuleConfig2 = merchantDto.getBin_rule_group_config();
            
            for(MspBinRuleConfigDto binruleConfig : listBinRuleConfig2) {
                if(binruleConfig.getId() == 0) { //add
                    MspBinRuleConfigDao.insertBinRuleConfig(binruleConfig.getIntrumentType(),binruleConfig.getBinGroupName(), binruleConfig.getRuleAllow(), merchantDto.getMerchant_id());
                } else { //edit
                    MspBinRuleConfigDao.editBinRuleConfig(binruleConfig.getId() ,binruleConfig.getIntrumentType(),binruleConfig.getBinGroupName(), binruleConfig.getRuleAllow());
                }
            }
            for(MspBinRuleConfigDto binruleConfigCheck : listBinRuleConfig1) { //delete
                boolean isExist = listBinRuleConfig2.stream().anyMatch(mc -> mc.getId() == binruleConfigCheck.getId());
                if(!isExist){
                    MspBinRuleConfigDao.deleteBinRuleConfig(binruleConfigCheck.getId(), binruleConfigCheck.getCardRuleId());
                }
            }
            // 7.2 Loop to upsert
            JsonObject clientData = new JsonObject();
            for (DirectDebitDto directDebit : merchantDto.getDirect_debit()) {
                JsonObject bankData = new JsonObject();
                if (directDebit.getMax_amount_per_token_per_day() != null) bankData.put("max_amount_per_token_per_day", directDebit.getMax_amount_per_token_per_day());
                
                if (directDebit.getMax_amount_per_transaction() != null) bankData.put("max_amount_per_transaction", directDebit.getMax_amount_per_transaction());
                clientData.put(String.join(";",directDebit.getBank()), bankData);
            }
            DirectDebitDao.upsertDirectDebit(merchantDto.getMerchant_id(), clientData);

            // 7.3 Insert update partner id if VIETTINQR (QR static)
            List<MerchantMspClientDto> listClientMerchant = ClientMspDao.getClientMerchantByMerchantId(merchantDto.getMerchant_id());
            boolean checkMerchantViettinQr = listClientMerchant.stream().anyMatch(obj -> ("VIETINQR".equals(obj.getClient_id()) && "static".equals(obj.getQr_type())));

            if (checkMerchantViettinQr){

                Map<String, Object> map1 = new HashMap<>();
                map1.put("merchant_id", merchantDto.getMerchant_id());
                map1.put("partner_id", merchantDto.getPartnerId());
                map1.put("paygate", "QR");
                SystemManagerMspDao.insertTbMerchantId(map1);

                //insert onepartner.tbl_mid_merchant_id
                Map<String, Object> map2 = new HashMap<>();
                map2.put("merchant_id", merchantDto.getMerchant_id());
                map2.put("partner_id", merchantDto.getPartnerId());
                map2.put("paygate", "MPAY");
                SystemManagerMspDao.insertTblMidMerchantId(map2);
            }
        }
        return res;
    }

    /**
     * Upsert merchant to MSP.TB_MERCHANT
     * @param merchantDto
     * @throws Exception
     */
    private static int upsertMerchantMspGeneral(MerchantMspDto merchantDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        int result = 1;
        if(merchantDto == null) {
            return 0;
        }

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.MERCHANT_UPSERT_v2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }");
            cs.setString(1, merchantDto.getMerchant_id());
            cs.setString(2, merchantDto.getMerchant_name());
            cs.setString(3, merchantDto.getState() == null ? "active" : merchantDto.getState());
            cs.setString(4, merchantDto.getHash_code() == null ? "" : merchantDto.getHash_code());
            cs.setString(5, merchantDto.getAccess_code() == null ? "" : merchantDto.getAccess_code());
            cs.setString(6, merchantDto.getCategory_code() == null ? "" : merchantDto.getCategory_code());
            cs.setString(7, merchantDto.getCity() == null ? "" : merchantDto.getCity());
            cs.setString(8, merchantDto.getCountry_code() == null ? "" : merchantDto.getCountry_code());
            cs.setString(9, merchantDto.getReference_type());
            cs.setString(10, merchantDto.getAddress() == null ? "" : merchantDto.getAddress());
            cs.setString(11, merchantDto.getSecret_access_key() == null ? "" : merchantDto.getSecret_access_key());
            cs.setString(12, merchantDto.getToken() == null ? "" : merchantDto.getToken());
            cs.setString(13, merchantDto.getPr_merchant_id() == null ? "" : merchantDto.getPr_merchant_id());
            cs.setString(14, merchantDto.getPartnerId() == null ? "" : merchantDto.getPartnerId());
            cs.registerOutParameter(15, OracleTypes.NUMBER);
            cs.registerOutParameter(16, OracleTypes.NUMBER);
            cs.registerOutParameter(17, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(16);
            String sError = cs.getString(17);
            if (nError != 200) {
                result = 0;
                logger.severe("UPSERT MSP MERCHANT " + merchantDto.getMerchant_id() +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Upsert Notification by merchant id
     * @param merchantId
     * @param notificationDto
     * @throws Exception
     */
    private static void upsertMerchantMspNotification(String merchantId, MerchantMspNotificationDto notificationDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        if(notificationDto == null) {
            return;
        }

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.NOTIFICATION_UPSERT2(?,?,?,?,?,?,?,?,?,?,?) }");
            cs.setString(1, "approved");
            cs.setString(2, notificationDto.getDesc());
            cs.setString(3, merchantId);
            cs.setString(4, notificationDto.getPhone());
            cs.setString(5, notificationDto.getEmail());
            cs.setString(6, notificationDto.getEvent_type());
            cs.setString(7, notificationDto.getUrl());
            cs.setString(8, notificationDto.getTelegramId());
            cs.registerOutParameter(9, OracleTypes.NUMBER);
            cs.registerOutParameter(10, OracleTypes.NUMBER);
            cs.registerOutParameter(11, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(10);
            String sError = cs.getString(11);
            if (nError != 200) {
                logger.severe("UPSERT MSP NOTIFICATION WITH MERCHANT ID " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    /**
     * Delete merchant msp by merchant id
     * @param merchantId
     * @throws Exception
     */
    public static Integer deleteMerchantById(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.MERCHANT_DELETE_BY_ID(?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
             result = cs.getInt(2);
            String sError = cs.getString(3);
            if (result != 200) {
                logger.severe("DELETE MERCHANT MSP BY MERCHANT ID " + merchantId +" error: " + result + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result==200 ? 1 : 0;
    }

    /**
     * change state merchant msp by merchant id
     * @param merchantId
     * @throws Exception
     */
    public static Integer changeStateMerchantById(String merchantId, String state) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.merchant_change_state(?,?,?, ?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, merchantId);
            cs.setString(4, state);
            cs.execute();
             result = cs.getInt(1);
            String sError = cs.getString(2);
            if (result != 200) {
                logger.severe("CHANGE STATE MERCHANT MSP BY MERCHANT ID " + merchantId +" error: " + result + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result==200 ? 1 : 0;
    }


    /**
     * Get general merchant information by merchant id
     * @param merchantId
     * @return
     * @throws Exception
     */
    public static MerchantMspDto getMerchantGeneralById(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        MerchantMspDto result = new MerchantMspDto();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.MERCHANT_GET_BY_ID(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET MSP MERCHANT BY ID " + merchantId +" error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    result = bindMerchantGeneral(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    public static Integer checkMerchantById(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.merchant_check(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            total =  cs.getInt(2);
            if (nError != 200) {
                logger.severe("merchant_check " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    public static Integer checkMerchantByName(String merchantId, String merchantName) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.check_merchant_name_vietinqr(?,?,?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.setString(2, merchantName);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.registerOutParameter(6, OracleTypes.CURSOR);
            cs.execute();
            int nError = cs.getInt(4);
            String sError = cs.getString(5);
            total =  cs.getInt(3);
            if (nError != 200) {
                logger.severe("merchant_check " + merchantName +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    public static Integer checkTransactionWSP(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.check_trans_of_wsp(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            total =  cs.getInt(2);
            if (nError != 200) {
                logger.severe("merchant_check " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    public static Integer checkTransNotVIETINQR(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.check_trans_not_vietinqr(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            total =  cs.getInt(2);
            if (nError != 200) {
                logger.severe("merchant_check " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    public static Integer checkMerchantConfigVIETINQR(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        Integer total = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.check_merchant_config_vietinqr(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            total =  cs.getInt(2);
            if (nError != 200) {
                logger.severe("merchant_check " + merchantId +" error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        return total;
    }

    /**
     * Get MerchantMspNotificationDto object by merchant id
     * @param merchantId
     * @return
     * @throws Exception
     */
    private static MerchantMspNotificationDto getNotificationByMerchantId(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        MerchantMspNotificationDto result = new MerchantMspNotificationDto();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.NOTIFICATION_GET_BY_ID(?,?,?,?) }");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET MSP NOTIFICATION BY MERCHANT ID " + merchantId +" error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    result = bindNotification(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    /**
     * Merchant Approval Request
     * @param merchantNewDto
     * @throws Exception
     */
    public static Integer merchantApprovalRequest(MerchantMspDto merchantNewDto,MerchantMspDto merchantOldDto,String state) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MERCHANT_APPROVAL.MERCHANT_APPROVAL_REQUEST(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "MERCHANT_APPROVAL_REQUEST");
            cs.setString(5, "PKG_MSP_MERCHANT");
            cs.setString(6, "TB_APRROVAL");
            cs.setString(7, "ALL");
            cs.setString(8, "PENDDING");
            cs.setString(9, merchantNewDto != null?merchantNewDto.getMerchant_id():merchantOldDto.getMerchant_id());
//            cs.setString(10,merchantNewDto != null? merchantNewDto.getUser_request():merchantOldDto.getUser_request());
            cs.setString(11, state);
            cs.setString(12, "");
            cs.setString(13,merchantOldDto != null?Util.gson.toJson(merchantOldDto):"");
            cs.setString(14,merchantNewDto != null? Util.gson.toJson(merchantNewDto):"");
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                result = 0;
                logger.severe("MERCHANT_APPROVAL_REQUEST " + merchantNewDto +" error: " + nError + " - " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }else {
                result = cs.getInt(1);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }

    /**
     * Merchant Approval Request
     * @param merchantId
     * @throws Exception
     */
    public static String checkApproval(String merchantId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MERCHANT_APPROVAL.CHECK_MERCHANT_APPROVAL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchantId);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("CHECK_MERCHANT_APPROVAL " + merchantId +" error: " + nError + " - " + sError);
            }
            result = cs.getString(1);
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }
    /**
     * Merchant Approval Request
     * @param
     * @throws Exception
     */
    public static BaseList<MerchantApprovalRequest> getApproval() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result = null;
        int total = 0;
        BaseList<MerchantApprovalRequest> merchantApprovalRequestBaseList = new BaseList<>();
        List<MerchantApprovalRequest> arrayList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MERCHANT_APPROVAL.GET_ALL_APPROVAL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(2);
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            if (nError != 200) {
                logger.severe("CHECK_MERCHANT_APPROVAL "  +" error: " + nError + " - " + sError);
            }else{
                while (rs != null && rs.next()) {
                    arrayList.add(bindMerchantApproval(rs));
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        merchantApprovalRequestBaseList.setList(arrayList);
        merchantApprovalRequestBaseList.setTotalItems(total);
        return merchantApprovalRequestBaseList;
    }

    /**
     * Merchant Approval Request
     * @param
     * @throws Exception
     */
    public static MerchantApprovalRequest getApprovalById(Integer id) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        MerchantApprovalRequest merchantApprovalRequest = new MerchantApprovalRequest();
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MERCHANT_APPROVAL.get_approval_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            if (nError != 200) {
                logger.severe("get_approval_by_id "  +" error: " + nError + " - " + sError);
            }else{
                while (rs != null && rs.next()) {
                    merchantApprovalRequest = bindMerchantApproval(rs);
                }
                
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return merchantApprovalRequest;
    }

    /**
     * Merchant Approval Request
     * @param
     * @throws Exception
     */
    public static Integer updateMerchantApprovalStatus(Integer id,int userId, String status) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MERCHANT_APPROVAL.update_status(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, status);
            cs.setString(5, Integer.toString(userId));
            cs.execute();
            result = cs.getInt(1);
            String sError = cs.getString(2);
            if (result != 200) {
                logger.severe("update_status "  +" error: " + result + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return result;
    }



        /**
     * Bind resultset to MerchantMspDto object
     * 
     * @param rs
     * @return
     * @throws Exception
     */
    private static MerchantMspDto bindMerchantGeneral(ResultSet rs) throws Exception {
        MerchantMspDto merchant = new MerchantMspDto();
        merchant.setMerchant_id(Util.getColumnString(rs, "S_ID"));
        merchant.setMerchant_name(Util.getColumnString(rs, "S_NAME"));
        merchant.setHash_code(Util.getColumnString(rs,"S_HASH_CODE"));
        merchant.setAccess_code(Util.getColumnString(rs,"S_ACCESS_CODE"));
        merchant.setCategory_code(Util.getColumnString(rs, "S_CATEGORY_CODE"));
        merchant.setCountry_code(Util.getColumnString(rs, "S_COUNTRY_CODE"));
        merchant.setCity(Util.getColumnString(rs, "S_CITY"));
        merchant.setReference_type(Util.getColumnString(rs, "S_REFERENCE_TYPE"));
        merchant.setState(Util.getColumnString(rs, "S_STATE"));
        merchant.setAddress(Util.getColumnString(rs, "S_ADDRESS"));
        merchant.setSecret_access_key(Util.getColumnString(rs, "S_SECRET_ACCESS_KEY"));
        merchant.setToken(Util.getColumnString(rs,"S_TOKEN_GROUP"));
        merchant.setPr_merchant_id(Util.getColumnString(rs, "S_PR_MERCHANT_ID"));
        merchant.setPartnerId(Util.getColumnString(rs, "N_PARTNER_ID"));
        merchant.setData(Util.getColumnString(rs, "S_DATA"));
        return merchant;
    }

    /**
     * Bind resultset to MerchantMspNotificationDto object
     * @param rs
     * @return
     * @throws Exception
     */
    private static MerchantMspNotificationDto bindNotification(ResultSet rs) throws Exception {
        MerchantMspNotificationDto notification = new MerchantMspNotificationDto();
        notification.setPhone(rs.getString("S_PHONES"));
        notification.setEmail(rs.getString("S_EMAILS"));
        notification.setUrl(rs.getString("S_URLS"));
        notification.setEvent_type(rs.getString("S_EVENT_TYPES"));
        notification.setDesc(rs.getString("S_DESCRIPTION"));
        notification.setTelegramId(rs.getString("S_TELEGRAM_IDS"));
        return notification;
    }
    private static  MerchantApprovalRequest bindMerchantApproval(ResultSet rs) throws Exception {
        MerchantApprovalRequest merchantApprovalRequest = new MerchantApprovalRequest();
        merchantApprovalRequest.setCreatedDate(rs.getTimestamp("D_CREATE"));
        merchantApprovalRequest.setId(rs.getInt("N_ID"));
        merchantApprovalRequest.setNewValue(Util.gson.fromJson(rs.getString("S_NEW_VALUE"), MerchantMspDto.class));
        merchantApprovalRequest.setOldValue(Util.gson.fromJson(rs.getString("S_OLD_VALUE"), MerchantMspDto.class));
        merchantApprovalRequest.setUserName(rs.getString("S_USER_NAME"));
        merchantApprovalRequest.setType(rs.getString("S_TYPE"));
        merchantApprovalRequest.setMerchantId(rs.getString("S_MERCHANT_ID"));
        return merchantApprovalRequest;
    }

    public static String randomString(int size) {
        String SALTCHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
        StringBuilder salt = new StringBuilder();
        Random rnd = new Random();
        while (salt.length() < size) { // length of the random string.
            int index = (int) (rnd.nextFloat() * SALTCHARS.length());
            salt.append(SALTCHARS.charAt(index));
        }
        String saltStr = salt.toString();
        return saltStr;
    }

}
