/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 2/15/19 6:05 PM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class MerchantInfoDto {
    private String id;
    private String name;
    private String address;

    public MerchantInfoDto() {
    }

    public MerchantInfoDto(String id, String name, String address) {
        this.id = id;
        this.name = name;
        this.address = address;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public MerchantInfoDto id(String id) {
        this.id = id;
        return this;
    }

    public MerchantInfoDto name(String name) {
        this.name = name;
        return this;
    }

    public MerchantInfoDto address(String address) {
        this.address = address;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", name='" + getName() + "'" + ", address='" + getAddress() + "'" + "}";
    }
}
