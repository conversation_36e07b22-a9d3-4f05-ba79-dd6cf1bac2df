package vn.onepay.portal.resources.merchantmanagement.contract.contract_generator;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.jexl2.UnifiedJEXL.Exception;
// import org.apache.xpath.operations.String;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
// import jxl.write.Boolean;
import vn.onepay.portal.resources.merchantmanagement.contract.dto.ContractOriginal;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import vn.onepay.portal.utils.ConvertNumberToWordEnglish;
import java.util.TimeZone;
import java.util.Calendar;
public class ContractGenerator {
    public static Logger logger = Logger.getLogger(ContractGenerator.class.getName());
    public static String checked = "☒";
    public static String unChecked = "☐";

    private ContractOriginal contractOriginal;
    private JsonObject contractDetail;
    private JsonObject contractDetailParent;
    // private String html;
    private Map<String, String> replaceMap;
    private List<Map<String, String>> hide;
    private boolean bilingual=false;

    private boolean approveVisa, approveMaster, approveJCB, approveUnionPay, approveAmex, approveApple, approveGoogle, approveSamsungInter,
        approveShopify, approveINT, approveINTgroup1, approveINTgroup2, approveINT_domesRelease, approveINT_foreignRelease, 
        approveDOM, approveAPP, approveInstallment, approveBNPL, approveSamsungDomes,approveAppleDomes,approveDischargeVietQR;

    public ContractGenerator(ContractOriginal contractOriginal) {
        this();
        this.contractOriginal = contractOriginal;
        logger.info("contractOriginal.getContractCodeTypeParent: " + contractOriginal.getContractCodeTypeParent());
        this.contractDetail = new JsonObject(contractOriginal.getDataContractDetail());
        if(contractOriginal.getDataContractDetailParent() != null && !contractOriginal.getDataContractDetailParent().isEmpty()){
            this.contractDetailParent = new JsonObject(contractOriginal.getDataContractDetailParent());
        }else{
            this.contractDetailParent = new JsonObject();
        }
    }

    public ContractGenerator() {
        this.replaceMap = new LinkedHashMap<>();
        this.hide = new LinkedList<>();
    }

    public Map<String, String> generateParam(boolean bilingual) throws Exception{
        // this.getHTMLTemplate();
        this.bilingual = bilingual;

        this.processCardListArray();

        this.fillBasicInfo();
        this.fillDieu2();
        this.fillPL1();
        this.fillPL2();
        this.fillPL3();

        this.fillSTT();

        // this.replaceVariables();

        this.processCongVanDieuChinh();
        this.fillPayOut();
        this.fillPayCollect();
        this.fillPL2ThemMerchantAccount();
        this.fillVBUQ();
        this.fillBBTLv2();
        this.fillPL15v2();//phụ lục 3D
        this.fillPL_ThongTinCaNhan();
        return this.replaceMap;
    }

    private void fillBasicInfo() {
        try {
            logger.info("Fill basic info");
            logger.info("Contract number: " + this.contractOriginal.getContractNumber()+"|signdate: "+this.contractOriginal.getSignatureDate());
            String datePattern = "dd/MM/yyyy";
            this.replaceMap.put("contractNumberTxt", this.contractOriginal.getContractNumber());
            this.replaceMap.put("signatureDateTxt", this.formatDate(this.contractOriginal.getSignatureDate(), datePattern, ".........."));
            this.replaceMap.put("signatureDateText", this.formatDateText(this.contractOriginal.getSignatureDate(), "ngày ...tháng ... năm ......"));
            this.replaceMap.put("representativeTxt", this.contractOriginal.getRepresentative());
            this.replaceMap.put("permanentAddressTxt", this.contractDetail.getString("permanentAddress"));
            this.replaceMap.put("businessNameTxt", this.contractOriginal.getBusinessName());
            this.replaceMap.put("shortNameTxt", this.contractDetail.getString("shortName"));
            this.replaceMap.put("addressBusinessTxt", this.contractDetail.getString("addressBusiness"));
            this.replaceMap.put("addressOfficeTxt", this.contractDetail.getString("addressOffice"));
            this.replaceMap.put("phoneTxt", this.contractDetail.getString("phone"));

            this.replaceMap.put("emailTxt", this.contractDetail.getString("email"));
            this.replaceMap.put("peopleIdTxt", this.contractDetail.getString("peopleId"));
            this.replaceMap.put("rangeDateTxt", this.formatDate(this.contractOriginal.getRangeDate(), datePattern, ".........."));
            this.replaceMap.put("issuedByTxt", this.contractDetail.getString("issuedBy"));

            this.replaceMap.put("websiteTxt", this.contractDetail.getString("website"));
            this.replaceMap.put("numberBusinessTxt", this.contractDetail.getString("numberBusiness"));
            this.replaceMap.put("danhXungTxt", this.contractDetail.getString("danhXung"));
            this.replaceMap.put("signaturerTxt", this.contractDetail.getString("signaturer"));
            this.replaceMap.put("positionTxt", this.contractDetail.getString("position"));

            this.replaceMap.put("maSoThue", this.contractDetail.getString("maSoThue"));

            //format ngày cấp mã số thuế
            String ngayCapMaSoThueStr = this.contractDetail.getString("ngayCapMaSoThue");
            String formattedDate = "..........";
            try {
                if (ngayCapMaSoThueStr != null && !ngayCapMaSoThueStr.isEmpty()) {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                    inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                    SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                    outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                    formattedDate = outputFormat.format(inputFormat.parse(ngayCapMaSoThueStr));
                }
            } catch (ParseException e) {
                logger.log(Level.SEVERE, "Lỗi khi format ngày cấp mã số thuế", e);
            } 
            this.replaceMap.put("ngayCapMaSoThue", formattedDate);


        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void processCardListArray() {
            JsonArray cardListArray = this.contractDetail.getJsonArray("cardListArray", new JsonArray());
            this.approveVisa = cardListArray.contains("Visa");
            this.approveMaster = cardListArray.contains("MasterCard");
            this.approveJCB = cardListArray.contains("JCB");
            this.approveUnionPay = cardListArray.contains("UnionPay");
            this.approveAmex = cardListArray.contains("AmericanExpress");
            this.approveApple = cardListArray.contains("ApplePay");
            this.approveGoogle = cardListArray.contains("GooglePay");
            this.approveSamsungInter = cardListArray.contains("SamsungPay");
            this.approveShopify = cardListArray.contains("ApproveShopify");
            this.approveINT = this.approveVisa || this.approveMaster || this.approveJCB || this.approveUnionPay || this.approveAmex || this.approveApple || this.approveGoogle || this.approveSamsungInter;
            this.approveINTgroup1 = this.approveVisa || this.approveMaster || this.approveJCB || this.approveUnionPay;
            this.approveINTgroup2 = this.approveAmex;
            this.approveINT_domesRelease = cardListArray.contains("ApproveDomesticCard");
            this.approveINT_foreignRelease = cardListArray.contains("ApproveInternationalCard");
            this.approveDOM = cardListArray.contains("ApproveOnepayDomesticCard");
            this.approveAPP = cardListArray.contains("ApproveOnepayMobileApp") || !Objects.equals(this.contractDetail.getValue("percentVietQR"), "");
            this.approveInstallment = cardListArray.contains("ApproveInstallment");
            this.approveBNPL = cardListArray.contains("ApproveBNPL");
            this.approveSamsungDomes = cardListArray.contains("ApproveSamsungPay");
            this.approveAppleDomes = cardListArray.contains("ApproveApplePay");
            this.approveDischargeVietQR = cardListArray.contains("ApproveDischargeVietQR");
    }

    /**
     * Công văn điều chỉnh HD
     * Đọc array nội dung điều chỉnh và đánh số index
     * cố định có 5 loại điều chỉnh
     * Kiểm tra điều chỉnh nào được chọn cho CV điều chỉnh thì sẽ được đánh số theo thứ tự từ 1 đến 5
     * Thứ tự của 5 loại lần lượt là: 
     * 1.Điều chỉnh phí dịch vụ
     * 2.Điều chỉnh khoản đảm bảo thanh toán
     * 3.Điều chỉnh hình thức thu phí
     * 4.Điều chỉnh tài khoản tạm ứng
     * 5.Điều chỉnh thời gian tạm ứng
     */
    private void processCongVanDieuChinh() {
        try {
            //xử lý với index nội dung điều chỉnh
            JsonArray array = this.contractDetail.getJsonArray("adjustment", new JsonArray());
            
            // Tạo chuỗi adjustment từ array
            StringBuilder adjustmentBuilder = new StringBuilder();
            for (int i = 0; i < array.size(); i++) {
                if (i > 0) {
                    adjustmentBuilder.append(", ");
                }
                Object item = array.getValue(i);
                adjustmentBuilder.append(item != null ? item.toString() : "");
            }
            this.replaceMap.put("adjustment", adjustmentBuilder.toString());
            
            int index = 1;
            if (array.contains("Điều chỉnh phí dịch vụ")) {
                this.replaceMap.put("CVDC_INDEX1", ""+index);
                index++;
                this.replaceMap.put("<start-1-dieu-chinh-phi>", "");
                this.replaceMap.put("<end-1-dieu-chinh-phi>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-1-dieu-chinh-phi>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-1-dieu-chinh-phi>");
                hide.add(map2);
            }else{
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-1-dieu-chinh-phi>");
                map.put("end", "<end-1-dieu-chinh-phi>");
                hide.add(map);
            }
            if (array.contains("Điều chỉnh khoản đảm bảo thanh toán")) {
                this.replaceMap.put("CVDC_INDEX2", ""+index);
                index++;
                this.replaceMap.put("<start-2-dieu-chinh-khoan-dam-bao>", "");
                this.replaceMap.put("<end-2-dieu-chinh-khoan-dam-bao>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-2-dieu-chinh-khoan-dam-bao>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-2-dieu-chinh-khoan-dam-bao>");
                hide.add(map2);
            }else{
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-2-dieu-chinh-khoan-dam-bao>");
                map.put("end", "<end-2-dieu-chinh-khoan-dam-bao>");
                hide.add(map);
            }
            if (array.contains("Điều chỉnh hình thức thu phí")) {
                this.replaceMap.put("CVDC_INDEX3", ""+index);
                index++;
                this.replaceMap.put("<start-3-dieu-chinh-hinh-thuc-thu-phi>", "");
                this.replaceMap.put("<end-3-dieu-chinh-hinh-thuc-thu-phi>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-3-dieu-chinh-hinh-thuc-thu-phi>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-3-dieu-chinh-hinh-thuc-thu-phi>");
                hide.add(map2);
            }else{
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-3-dieu-chinh-hinh-thuc-thu-phi>");
                map.put("end", "<end-3-dieu-chinh-hinh-thuc-thu-phi>");
                hide.add(map);
            }
            if (array.contains("Điều chỉnh tài khoản tạm ứng")) {
                this.replaceMap.put("CVDC_INDEX4", ""+index);
                index++;
                this.replaceMap.put("<start-4-dieu-chinh-tai-khoan>", "");
                this.replaceMap.put("<end-4-dieu-chinh-tai-khoan>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-4-dieu-chinh-tai-khoan>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-4-dieu-chinh-tai-khoan>");
                hide.add(map2);
            }else{
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-4-dieu-chinh-tai-khoan>");
                map.put("end", "<end-4-dieu-chinh-tai-khoan>");
                hide.add(map);
            }
            if (array.contains("Điều chỉnh thời gian tạm ứng")) {
                this.replaceMap.put("CVDC_INDEX5", ""+index);
                this.replaceMap.put("<start-5-dieu-chinh-thoi-gian-thanh-toan>", "");
                this.replaceMap.put("<end-5-dieu-chinh-thoi-gian-thanh-toan>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-5-dieu-chinh-thoi-gian-thanh-toan>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-5-dieu-chinh-thoi-gian-thanh-toan>");
                hide.add(map2);
            }else{
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-5-dieu-chinh-thoi-gian-thanh-toan>");
                map.put("end", "<end-5-dieu-chinh-thoi-gian-thanh-toan>");
                hide.add(map);
            }

            //xử lý với Số hợp đồng và ngày ký hợp đồng của HD cha
            this.replaceMap.put("contractNumberParentTxt", this.contractOriginal.getContractCodeParent());
            this.replaceMap.put("signDateParentTxt", this.formatDate(this.contractOriginal.getSignatureDateParent(), "dd/MM/yyyy", ".........."));
            this.replaceMap.put("businessNameParentTxt", this.contractOriginal.getBusinessNameParent());
            //xử lý công văn theo PL hay HD
            if (getStringValue(this.contractDetail, "contractContentBasis") != null && getStringValue(this.contractDetail, "contractContentBasis").equalsIgnoreCase("Hợp đồng")) {
                this.replaceMap.put("contractContentBasis", "4 Phụ lục 01");
            } else {
                this.replaceMap.put("contractContentBasis", "01, Phụ lục …");
            }


            logger.info("this.contractOriginal.getContractCode() = " + this.contractOriginal.getContractCode());
            //ẩn dòng  phí điều chỉnh với phụ lục điều chỉnh hợp đồng
            if ("PL-K_v2".equals(this.contractOriginal.getContractCode())) {
                // boolean isHideQT = false;
                // boolean isHideND = false;
                // boolean isHideQR = false;
                //nếu this.contractDetail.getString("monthFee") null thì ẩn dòng phí monthFee
                if (isEmpty(getStringValue(this.contractDetail, "monthFee"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "monthFeeTxt");
                    hide.add(map);
                }
                //nếu this.contractDetail.getString("registerFee") null thì ẩn dòng phí registerFee
                if (isEmpty(getStringValue(this.contractDetail, "registerFee"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "registerFeeTxt");
                    hide.add(map);
                }
                //nếu this.contractDetail.getString("feeTransInternational") null thì ẩn dòng phí feeTransInternational
                if (isEmpty(getStringValue(this.contractDetail, "feeTransInternational"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "feeTransInternationalLabel");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    map2.put("key", "feeTransInternationalTxt");
                    hide.add(map2);
                }
                //nếu this.contractDetail.getString("feeTransDomesticAndApp") null thì ẩn dòng phí feeTransDomestic
                if (isEmpty(getStringValue(this.contractDetail, "feeTransDomesticAndApp"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "feeTransDomesticAndAppLabel");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    map2.put("key", "feeTransDomesticAndAppTxt");
                    hide.add(map2);
                }
                //nếu this.contractDetail.getString("feeApp") null thì ẩn dòng phí feeApp
                if (isEmpty(getStringValue(this.contractDetail, "feeApp"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "feeAppLabel");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    map2.put("key", "feeAppTxt");
                    hide.add(map2);
                }
                //nếu this.contractDetail.getString("feeVietQR") null thì ẩn dòng phí feeVietQR
                if (isEmpty(getStringValue(this.contractDetail, "feeVietQR"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "feeVietQRLabel");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    map2.put("key", "feeVietQRTxt");
                    hide.add(map2);
                }
                //ẩn cả dòng phí fix nếu 4 loại trên đều ko có
                // if (this.contractDetail.getString("feeTransInternational") == null || this.contractDetail.getString("feeTransInternational").equals("")
                //     && this.contractDetail.getString("feeTransDomesticAndApp") == null || this.contractDetail.getString("feeTransDomesticAndApp").equals("")
                //     && this.contractDetail.getString("feeApp") == null || this.contractDetail.getString("feeApp").equals("")
                //     && this.contractDetail.getString("feeVietQR") == null || this.contractDetail.getString("feeVietQR").equals("")) {
                //     Map <String, String> map = new LinkedHashMap<>();
                //     map.put("type", "table");
                //     map.put("key", "Phí xử lý giao dịch (VND/giao dich)");
                //     hide.add(map);
                // }

                //ẩn this.contractDetail.getString("approveCardType01International") null và this.contractDetail.getString("approveCardType02International") null thì ẩn dòng phí feeTransInternational
                if (isEmpty(getStringValue(this.contractDetail, "approveCardType01International"))
                    && isEmpty(getStringValue(this.contractDetail, "approveCardType02International"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "approveCardType01InternationalTxt");
                    hide.add(map);
                }
                //ẩn this.contractDetail.getString("americanExpress01International") null và this.contractDetail.getString("americanExpress02International") null thì ẩn dòng phí feeTransInternational
                if (isEmpty(getStringValue(this.contractDetail, "americanExpress01International"))
                    && isEmpty(getStringValue(this.contractDetail, "americanExpress02International"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "americanExpress01InternationalTxt");
                    hide.add(map);
                }
                //this.contractDetail.getString("approveCardType01International") null và this.contractDetail.getString("approveCardType02International") null và this.contractDetail.getString("americanExpress01International") null và this.contractDetail.getString("americanExpress02International") null thì ẩn dòng Phát hành tại Việt Nam
                if (isEmpty(getStringValue(this.contractDetail, "approveCardType01International"))
                    && isEmpty(getStringValue(this.contractDetail, "approveCardType02International"))
                    && isEmpty(getStringValue(this.contractDetail, "americanExpress01International"))
                    && isEmpty(getStringValue(this.contractDetail, "americanExpress02International"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "Phát hành tại Việt Nam");
                    hide.add(map);
                    // isHideQT = true;
                }

                //ẩn percentQrShopeeTxt
                if (isEmpty(getStringValue(this.contractDetail, "percentQrShopee"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "percentQrShopeeTxt");
                    hide.add(map);
                }
                //ẩn percentQrZaloTxt
                if (isEmpty(getStringValue(this.contractDetail, "percentQrZalo"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "percentQrZaloTxt");
                    hide.add(map);
                }
                //ẩn percentQrMoMoTxt
                if (isEmpty(getStringValue(this.contractDetail, "percentQrMoMo"))) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "percentQrMoMoTxt");
                    hide.add(map);
                }
                //ẩn percentVietQRTxt
                if (this.contractDetail.getString("percentVietQR") == null || this.contractDetail.getString("percentVietQR").equals("")) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "percentVietQRTxt");
                    hide.add(map);
                }
                //ẩn percentQrOtherTxt
                if (this.contractDetail.getString("percentQrOther") == null || this.contractDetail.getString("percentQrOther").equals("")) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "percentQrOtherTxt");
                    hide.add(map);
                }
                //ẩn Ứng dụng di động nếu cả 5 dòng trên đều null
                if (this.contractDetail.getString("percentQrShopee") == null || this.contractDetail.getString("percentQrShopee").equals("")
                    && this.contractDetail.getString("percentQrZalo") == null || this.contractDetail.getString("percentQrZalo").equals("")
                    && this.contractDetail.getString("percentQrMoMo") == null || this.contractDetail.getString("percentQrMoMo").equals("")
                    && this.contractDetail.getString("percentVietQR") == null || this.contractDetail.getString("percentVietQR").equals("")
                    && this.contractDetail.getString("percentQrOther") == null || this.contractDetail.getString("percentQrOther").equals("")) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "Ứng dụng di động");
                    hide.add(map);
                    // isHideQR = true;
                }

                //ẩn feePaymentDomesticAndAppTxt
                if (this.contractDetail.getString("feePaymentDomesticAndApp") == null || this.contractDetail.getString("feePaymentDomesticAndApp").equals("")) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "feePaymentDomesticAndAppTxt");
                    hide.add(map);
                    // isHideND = true;
                }
                //ẩn Phí thanh toán (% giá trị giao dịch) nếu cả 3 isHide đều true
                // if (isHideQT && isHideND && isHideQR) {
                //     Map <String, String> map = new LinkedHashMap<>();
                //     map.put("type", "table");
                //     map.put("key", "Phí thanh toán (% giá trị giao dịch)");
                //     hide.add(map);
                // }
                
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillDieu2() {
        try {
            this.replaceMap.put("checkBoxVisa", this.approveVisa ? checked : unChecked);
            this.replaceMap.put("checkBoxMaster", this.approveMaster ? checked : unChecked);
            this.replaceMap.put("checkBoxJCB", this.approveJCB ? checked : unChecked);
            this.replaceMap.put("checkBoxUnionPay", this.approveUnionPay ? checked : unChecked);
            this.replaceMap.put("checkBoxAmex", this.approveAmex ? checked : unChecked);
            this.replaceMap.put("checkBoxApple", this.approveApple ? checked : unChecked);
            this.replaceMap.put("checkBoxGoogle", this.approveGoogle ? checked : unChecked);
            this.replaceMap.put("checkBoxSamsung", this.approveSamsungInter ? checked : unChecked);
            this.replaceMap.put("ApproveDomesticCard", this.approveINT_domesRelease? checked : unChecked);
            this.replaceMap.put("ApproveInternationalCard", this.approveINT_foreignRelease ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayMobileApp", this.approveAPP ? checked : unChecked);
            this.replaceMap.put("ApproveInstallment", this.approveInstallment ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayMobileApp", this.approveAPP ? checked : unChecked);
            this.replaceMap.put("ApproveOnepayDomesticCard", this.approveDOM ? checked : unChecked);
            this.replaceMap.put("ApproveBNPL", this.approveBNPL ? checked : unChecked);
            this.replaceMap.put("ApproveShopify", this.approveShopify ? checked : unChecked);
            this.replaceMap.put("ApproveSamsung", this.approveSamsungDomes ? checked : unChecked);
            this.replaceMap.put("ApproveApple", this.approveAppleDomes ? checked : unChecked);
            this.replaceMap.put("carrerTxt", this.contractDetail.getString("carrer"));
            this.replaceMap.put("ApproveDischargeVietQR", this.approveDischargeVietQR ? checked : unChecked);

            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    /**
     * Fill contract Payout HD11
     */
    private void fillPayOut(){
        this.replaceMap.put("feeForCard", this.contractDetail.getString("feeForCard"));
        this.replaceMap.put("carrerTxt", this.contractDetail.getString("carrer"));
    }

    /**
     * Fill contract Paycollect HD12
     */
    private void fillPayCollect(){
        this.replaceMap.put("feeForCard", this.contractDetail.getString("feeForCard"));
        String feePaycollect = this.contractDetail.getString("cardType");
        //index cho step trên phụ lục khi ẩn hiện vì lựa chọn loại phí
        String PL3__index5 = "Bước 5";
        
        //kiểm tra chuỗi feePaycollect có chứa text transactionFee hay không
        if (feePaycollect.contains("transactionFee")) {
            this.replaceMap.put("<start-phigiaodich-paycollect>", "");
            this.replaceMap.put("<end-phigiaodich-paycollect>", "");
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "row");
            map.put("key", "<start-phigiaodich-paycollect>");
            hide.add(map);
            Map <String, String> map2 = new LinkedHashMap<>();
            map2.put("type", "row");
            map2.put("key", "<end-phigiaodich-paycollect>");
            hide.add(map2);

            Map <String, String> map3 = new LinkedHashMap<>();
            map3.put("type", "tag");
            map3.put("start", "<start-phithang-paycollect>");
            map3.put("end", "<end-phithang-paycollect>");
            hide.add(map3);

            //ẩn hiện điều kiện trong PHỤ LỤC SỐ 03   hình thức thanh toán
            Map <String, String> map4 = new LinkedHashMap<>();
            map4.put("type", "row");
            map4.put("key", "giá trị giao dịch của dịch vụ hỗ trợ thu hộ");
            hide.add(map4);

            //ẩn bước 4 trong phụ lục 3 với lựa chọn phí giao dịch
            Map <String, String> map5 = new LinkedHashMap<>();
            map5.put("type", "table");
            map5.put("key", "cho Bên A theo thông tin số tài khoản được quy định tại Điều 6 của Hợp đồng");
            hide.add(map5);

            PL3__index5 = "Bước 4";
        } else if(feePaycollect.contains("monthFee")){
            this.replaceMap.put("<start-phithang-paycollect>", "");
            this.replaceMap.put("<end-phithang-paycollect>", "");
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "row");
            map.put("key", "<start-phithang-paycollect>");
            hide.add(map);
            Map <String, String> map2 = new LinkedHashMap<>();
            map2.put("type", "row");
            map2.put("key", "<end-phithang-paycollect>");
            hide.add(map2);

            Map <String, String> map3 = new LinkedHashMap<>();
            map3.put("type", "tag");
            map3.put("start", "<start-phigiaodich-paycollect>");
            map3.put("end", "<end-phigiaodich-paycollect>");
            hide.add(map3);

            //ẩn hiện điều kiện trong PHỤ LỤC SỐ 03   hình thức thanh toán
            Map <String, String> map4 = new LinkedHashMap<>();
            map4.put("type", "row");
            map4.put("key", "giá trị hàng hóa dịch vụ trừ đi các khoản phí liên quan");
            hide.add(map4);
        }

        this.replaceMap.put("tgTamUng", this.contractDetail.getString("tgTamUng"));

        this.replaceMap.put("PL3__index5", PL3__index5);
    }

    /**
     * Fill phụ lục thêm merchant PL02
     */
    private void fillPL2ThemMerchantAccount(){
        String datePattern = "dd/MM/yyyy";
        this.replaceMap.put("parentContractNumberBusiness", this.contractDetailParent.getString("numberBusiness"));
        this.replaceMap.put("parentContractRangeDate", this.formatDate(this.contractOriginal.getRangeDateParent(), datePattern, ".........."));
        if (this.approveShopify) {
            this.replaceMap.put("<start-shopify>", "");
            this.replaceMap.put("<end-shopify>", "");
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "row");
            map.put("key", "<start-shopify>");
            hide.add(map);
            Map <String, String> map2 = new LinkedHashMap<>();
            map2.put("type", "row");
            map2.put("key", "<end-shopify>");
            hide.add(map2);

        } else {
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "tag");
            map.put("start", "<start-shopify>");
            map.put("end", "<end-shopify>");
            hide.add(map);
        }

        String PL__index4 ="4",PL__index5="5",PL__index6="6",PL__index7="7";

        if(this.approveInstallment){
            PL__index4 = "4";
            if(this.approveBNPL){
                PL__index5 = "5";
                PL__index6 = "6";
                PL__index7 = "7";
            }else{
                PL__index6 = "5";
                PL__index7 = "6";
            }
        }else{
            if(this.approveBNPL){
                PL__index5 = "4";
                PL__index6 = "5";
                PL__index7 = "6";
            }else{
                PL__index6 = "4";
                PL__index7 = "5";
            }
        }
        
        this.replaceMap.put("PL__index4", PL__index4);
        this.replaceMap.put("PL__index5", PL__index5);
        this.replaceMap.put("PL__index6", PL__index6);
        this.replaceMap.put("PL__index7", PL__index7);
        
    }

    /**
     * Fill VBUQ
     */
    private void fillVBUQ(){
        this.replaceMap.put("authorizedPersonName", this.contractDetail.getString("authorizedPersonName"));
        this.replaceMap.put("authorizationNumber", this.contractDetail.getString("authorizationNumber"));
        String authorizedBirthDateStr = this.contractDetail.getString("authorizedBirthDate");
        String formattedDate = "..........";
        try {
            if (authorizedBirthDateStr != null && !authorizedBirthDateStr.isEmpty()) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                formattedDate = outputFormat.format(inputFormat.parse(authorizedBirthDateStr));
            }
        } catch (ParseException e) {
            logger.log(Level.SEVERE, "Lỗi khi format ngày sinh người được ủy quyền", e);
        } 
        this.replaceMap.put("authorizedBirthDate", formattedDate);
        this.replaceMap.put("authorizedAddress", this.contractDetail.getString("authorizedAddress"));
        this.replaceMap.put("authorizedPersonId", this.contractDetail.getString("authorizedPersonId"));
        String rangeDateStr = this.contractDetail.getString("rangeDate");
        String formattedRangeDate = "..........";
        try {
            if (rangeDateStr != null && !rangeDateStr.isEmpty()) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                formattedRangeDate = outputFormat.format(inputFormat.parse(rangeDateStr));
            }
        } catch (ParseException e) {
            logger.log(Level.SEVERE, "Lỗi khi format ngày cấp CMND người được ủy quyền", e);
        }
        this.replaceMap.put("rangeDate", formattedRangeDate);
        this.replaceMap.put("authorizedIssuedBy", this.contractDetail.getString("authorizedIssuedBy"));
        String authorizationPeriodFromStr = this.contractDetail.getString("authorizationPeriodFrom");
        String formattedAuthorizationPeriodFrom = "..........";
        try {
            if (authorizationPeriodFromStr != null && !authorizationPeriodFromStr.isEmpty()) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                formattedAuthorizationPeriodFrom = outputFormat.format(inputFormat.parse(authorizationPeriodFromStr));
            }
        } catch (ParseException e) {
            logger.log(Level.SEVERE, "Lỗi khi format ngày bắt đầu thời hạn ủy quyền", e);
        }
        this.replaceMap.put("authorizationPeriodFrom", formattedAuthorizationPeriodFrom);
        this.replaceMap.put("accountName", this.contractDetail.getString("accountName"));
        this.replaceMap.put("accountNumber", this.contractDetail.getString("accountNumber"));
        this.replaceMap.put("accountBank", this.contractDetail.getString("accountBank"));
        
    }

    /**
     * Fill BBTL v2
     */
    private void fillBBTLv2(){
        logger.info("contractOriginal: " + contractOriginal.getContractCodeTypeParent());
        if ("HD14".equals(this.contractOriginal.getContractCodeTypeParent())) {
            //ẩn thông tin hợp đồng khác cá nhân (ECOM)
            Map <String, String> map1 = new LinkedHashMap<>();
            map1.put("type", "tag");
            map1.put("start", "<start-dvecom>");
            map1.put("end", "<end-dvecom>");
            hide.add(map1);
            //remove tag <start-dvcanhan> và <end-dvcanhan>
            removeTag("<start-dvcanhan>", "<end-dvcanhan>");
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "row");
            map.put("key", "<start-dvcanhan>");
            hide.add(map);
            Map <String, String> map2 = new LinkedHashMap<>();
            map2.put("type", "row");
            map2.put("key", "<end-dvcanhan>");
            hide.add(map2);
            this.replaceMap.put("signaturerTxt", this.contractOriginal.getRepresentative());
        }else{
            //ẩn thông tin hợp đồng cá nhân
            Map <String, String> map1 = new LinkedHashMap<>();
            map1.put("type", "tag");
            map1.put("start", "<start-dvcanhan>");
            map1.put("end", "<end-dvcanhan>");
            hide.add(map1);
            //remove tag <start-dvecom> và <end-dvecom>
            removeTag("<start-dvecom>", "<end-dvecom>");
            Map <String, String> map = new LinkedHashMap<>();
            map.put("type", "row");
            map.put("key", "<start-dvecom>");
            hide.add(map);
            Map <String, String> map2 = new LinkedHashMap<>();
            map2.put("type", "row");
            map2.put("key", "<end-dvecom>");
            hide.add(map2);
        }
        this.replaceMap.put("thoigianGiaiKhoanh", this.contractDetail.getString("thoigianGiaiKhoanh"));
        this.replaceMap.put("stkGiaiKhoanh", this.contractDetail.getString("stkGiaiKhoanh"));
        this.replaceMap.put("tenTaiKhoanGiaiKhoanh", this.contractDetail.getString("tenTaiKhoanGiaiKhoanh"));
        this.replaceMap.put("openByBank", this.contractDetail.getString("openByBank"));
        String ngayThanhLyStr = this.contractDetail.getString("ngayThanhLy");
        String formattedNgayThanhLy = "..........";
        try {
            if (ngayThanhLyStr != null && !ngayThanhLyStr.isEmpty()) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                formattedNgayThanhLy = outputFormat.format(inputFormat.parse(ngayThanhLyStr));
            }
        } catch (ParseException e) {
            logger.log(Level.SEVERE, "Lỗi khi format ngày thanh lý", e);
        } 
        this.replaceMap.put("ngayThanhLy", formattedNgayThanhLy);
    }

    /**
     * Fill phụ lục 3D
     */
    private void fillPL15v2(){
        String thoiGianApDungStr = this.contractDetail.getString("thoiGianApDung");
        String formattedThoiGianApDung = "..........";
        try {
            if (thoiGianApDungStr != null && !thoiGianApDungStr.isEmpty()) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
                outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                formattedThoiGianApDung = outputFormat.format(inputFormat.parse(thoiGianApDungStr));
            }
        } catch (ParseException e) {
            logger.log(Level.SEVERE, "Lỗi khi format thời gian áp dụng", e);
        } 
        this.replaceMap.put("thoiGianApDung", formattedThoiGianApDung);

    }

    /**
     * Phụ lục Thông tin cá nhân
     */
    private void fillPL_ThongTinCaNhan() {
        try {
            this.replaceMap.put("noiKy", this.contractDetail.getString("noiKy"));
            this.replaceMap.put("ngayKy", this.extractFromDate(this.contractOriginal.getSignatureDate(), "day") + "");
            this.replaceMap.put("thangKy", this.extractFromDate(this.contractOriginal.getSignatureDate(), "month") + "");
            this.replaceMap.put("namKy", this.extractFromDate(this.contractOriginal.getSignatureDate(), "year") + "");
            this.replaceMap.put("diaChiNhanThongBao", this.contractDetail.getString("diaChiNhanThongBao"));
            this.replaceMap.put("nguoiNhanThongBao", this.contractDetail.getString("nguoiNhanThongBao"));
            this.replaceMap.put("emailNhanThongBao", this.contractDetail.getString("emailNhanThongBao"));
            this.replaceMap.put("soDienThoaiNhanThongBao", this.contractDetail.getString("soDienThoaiNhanThongBao"));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillPL1() {
        try {
            // STT 1, 2
            this.replaceMap.put("registerFeeTxt", this.readFormatMoney("registerFee") + " VND");
            this.replaceMap.put("monthFeeTxt", this.contractDetail.getString("monthFee"));

            // STT 3
            String feeTransInternationalLabel = "";
            String feeTransInternationalTxt = "";
            if (this.approveINT) {
                feeTransInternationalTxt = this.readFormatMoney("feeTransInternational") + " VND/ giao dịch";
                feeTransInternationalLabel = "- Thẻ quốc tế";
                this.replaceMap.put("feeTransInternationalTxt", feeTransInternationalTxt);
                this.replaceMap.put("feeTransInternationalLabel", feeTransInternationalLabel);
            }else {
                //ẩn thông tin phí thanh toán thẻ quốc tế
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "line_in_table");
                map.put("key", "feeTransInternationalTxt");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "line_in_table");
                map2.put("key", "feeTransInternationalLabel");
                hide.add(map2);
            }
            

            String feeTransDomesticAndAppLabel = "";
            String feeTransDomesticAndAppTxt = "";
            if (this.approveDOM) {
                feeTransDomesticAndAppTxt = this.readFormatMoney("feeTransDomesticAndApp") + " VND/ giao dịch";
                feeTransDomesticAndAppLabel = "- Thẻ nội địa";
                this.replaceMap.put("feeTransDomesticAndAppLabel", feeTransDomesticAndAppLabel);
                this.replaceMap.put("feeTransDomesticAndAppTxt", feeTransDomesticAndAppTxt);
            }else{
                //ẩn thông tin phí thanh toán thẻ nội địa
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "line_in_table");
                map.put("key", "feeTransDomesticAndAppLabel");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "line_in_table");
                map2.put("key", "feeTransDomesticAndAppTxt");
                hide.add(map2);
            }
            

            String feeAppLabel = "";
            String feeAppTxt = "";
            if (this.approveAPP) {
                feeAppTxt = this.readFormatMoney("feeApp") + " VND/ giao dịch";
                feeAppLabel = "- Ứng dụng di động";
                this.replaceMap.put("feeAppLabel", feeAppLabel);
                this.replaceMap.put("feeAppTxt", feeAppTxt);
            } else {
                //ẩn thông tin phí thanh toán ứng dụng
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "line_in_table");
                map.put("key", "feeAppLabel");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "line_in_table");
                map2.put("key", "feeAppTxt");
                hide.add(map2);
            }
            

            String feeVietQRLabel = "";
            String feeVietQRTxt = "";
            if (this.approveDischargeVietQR) {
                feeVietQRTxt = this.readFormatMoney("feeVietQR") + " VND/ giao dịch";
                feeVietQRLabel = "- VietQR";
                this.replaceMap.put("feeVietQRLabel", feeVietQRLabel);
                this.replaceMap.put("feeVietQRTxt", feeVietQRTxt);
            } else {
                //ẩn thông tin phí thanh toán VietQR
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "line_in_table");
                map.put("key", "feeVietQRLabel");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "line_in_table");
                map2.put("key", "feeVietQRTxt");
                hide.add(map2);
            }
            

            int table_fee__stt4_rowspan = 1;

            // STT 4
            if (this.approveINT) {
                table_fee__stt4_rowspan++;
                String table_fee__payment_int = "";
                String list_card_int_group1 = "";
                String list_card_int_group2 = "";
                // table_fee__payment_int = readTemplateFromFile("PL1/table_fee__payment_int.html");
                this.replaceMap.put("table_fee__payment_int", table_fee__payment_int);

                if (this.approveINTgroup1) {
                    List<String> listCardInt = new LinkedList<String>();
                    if (this.approveVisa) listCardInt.add("Visa");
                    if (this.approveMaster) listCardInt.add("MasterCard");
                    if (this.approveJCB) listCardInt.add("JCB");
                    if (this.approveUnionPay) listCardInt.add("UnionPay");
                    list_card_int_group1 = "- " + listCardInt.stream().collect(Collectors.joining(", "));
                    this.replaceMap.put("list_card_int_group1", list_card_int_group1);
                } else {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "list_card_int_group1");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    hide.add(map2);
                    map2.put("key", "approveCardType01InternationalTxt");
                    Map <String, String> map3 = new LinkedHashMap<>();
                    map3.put("type", "line_in_table");
                    map3.put("key", "approveCardType02InternationalTxt");
                    hide.add(map3);
                }
                if (this.approveINTgroup2) {
                    list_card_int_group2 = "- American Express";
                    this.replaceMap.put("list_card_int_group2", list_card_int_group2);
                } else {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "line_in_table");
                    map.put("key", "list_card_int_group2");
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "line_in_table");
                    map2.put("key", "americanExpress01InternationalTxt");
                    hide.add(map2);
                    Map <String, String> map3 = new LinkedHashMap<>();
                    map3.put("type", "line_in_table");
                    map3.put("key", "americanExpress02InternationalTxt");
                    hide.add(map3);
                }
                
                
    
                this.replaceMap.put("approveCardType01InternationalTxt", 
                    !this.approveINTgroup1 ? ""
                    : !this.approveINT_domesRelease? "Không áp dụng"
                    : this.readFeePercent("approveCardType01International"));
                this.replaceMap.put("americanExpress01InternationalTxt", 
                    !this.approveINTgroup2 ? ""
                    : !this.approveINT_domesRelease? "Không áp dụng"
                    : this.readFeePercent("americanExpress01International"));
                this.replaceMap.put("approveCardType02InternationalTxt", 
                    !this.approveINTgroup1 ? ""
                    : !this.approveINT_foreignRelease? "Không áp dụng"
                    : this.readFeePercent("approveCardType02International"));
                this.replaceMap.put("americanExpress02InternationalTxt", 
                    !this.approveINTgroup2 ? ""
                    : !this.approveINT_foreignRelease? "Không áp dụng"
                    : this.readFeePercent("americanExpress02International"));
            } else {
                this.replaceMap.put("table_fee__payment_int", "");
                //ẩn thông tin phí thanh toán thẻ quốc tế
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "table");
                map.put("key", "list_card_int_group1");
                hide.add(map);
            }

            if (this.approveDOM) {
                table_fee__stt4_rowspan++;
                // String table_fee__payment_dom = readTemplateFromFile("PL1/table_fee__payment_dom.html");
                // this.replaceMap.put("table_fee__payment_dom", table_fee__payment_dom);
                this.replaceMap.put("feePaymentDomesticAndAppTxt", this.readFeePercent("feePaymentDomesticAndApp"));
            } else {
                this.replaceMap.put("table_fee__payment_dom", "");
                //ẩn thông tin phí thanh toán thẻ nội địa
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "table");
                map.put("key", "feePaymentDomesticAndAppTxt");
                hide.add(map);
            }

            if (this.approveAPP) {
                table_fee__stt4_rowspan++;
                // String table_fee__payment_app = readTemplateFromFile("PL1/table_fee__payment_app.html");
                // this.replaceMap.put("table_fee__payment_app", table_fee__payment_app);
                this.replaceMap.put("percentQrMobileTxt", this.readFeePercent("percentQrMobile"));
                this.replaceMap.put("percentQrGrabTxt", this.readFeePercent("percentQrGrab"));
                this.replaceMap.put("percentQrShopeeTxt", this.readFeePercent("percentQrShopee"));
                this.replaceMap.put("percentQrZaloTxt", this.readFeePercent("percentQrZalo"));
                this.replaceMap.put("percentQrMoMoTxt", this.readFeePercent("percentQrMoMo"));
                this.replaceMap.put("percentVietQRTxt",  this.readFeePercent("percentVietQR"));
                this.replaceMap.put("percentQrOtherTxt", this.readFeePercent("percentQrOther"));
            } else {
                this.replaceMap.put("table_fee__payment_app", "");
                //ẩn thông tin phí thanh toán ứng dụng
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "table");
                map.put("key", "percentQrOtherTxt");
                hide.add(map);
            }

            if (this.approveDischargeVietQR) {
                table_fee__stt4_rowspan++;
                // String table_fee__payment_vietQR = readTemplateFromFile("PL1/table_fee__payment_vietQR.html");
                // this.replaceMap.put("table_fee__payment_vietQR", table_fee__payment_vietQR);
                this.replaceMap.put("percentVietQRTxt", "-".equalsIgnoreCase(this.readFeePercent("percentVietQR")) ? "Không áp dụng" : this.readFeePercent("percentVietQR") );
            } else {
                this.replaceMap.put("table_fee__payment_vietQR", "");
                //ẩn thông tin phí thanh toán VietQR
                // Map <String, String> map = new LinkedHashMap<>();
                // map.put("type", "table");
                // map.put("key", "VietQR");
                // hide.add(map);
            }

            String table_fee__shopify = "";
            if (this.approveShopify) {
                // table_fee__shopify += this.readTemplateFromFile("PL1/table_fee__shopify.html");
            }else{
                //ẩn thông tin phí thanh toán Shopify
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "table");
                map.put("key", "Phí Shopify");
                hide.add(map);
            }
            this.replaceMap.put("table_fee__shopify", table_fee__shopify);
            this.replaceMap.put("table_fee__stt4_rowspan", String.valueOf(table_fee__stt4_rowspan));

            if (this.approveDOM || this.approveAPP) {
                // String PL1__dieu_1_c = this.readTemplateFromFile("PL1/dieu_1_c.html");
                // this.replaceMap.put("PL1__dieu_1_c", PL1__dieu_1_c);
            } else {
                this.replaceMap.put("PL1__dieu_1_c", "");
                // ẩn thông tin dòng 1c
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "c)   Danh sách chi tiết các ngân hàng chấp nhận thanh toán thẻ nội địa");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "https://onepay.vn/documents/acceptance_atm_qrc.pdf");
                hide.add(map2);
                Map <String, String> map3 = new LinkedHashMap<>();
                map3.put("type", "row");
                map3.put("key", "https://onepay.vn/documents/acceptance_atm_qrc_bilingual.pdf");
                hide.add(map3);
            }

            if (this.approveDischargeVietQR) {
                // String PL1__dieu_1_d = this.readTemplateFromFile("PL1/dieu_1_d.html");
                // this.replaceMap.put("PL1__dieu_1_d", PL1__dieu_1_d);
            } else {
                this.replaceMap.put("PL1__dieu_1_d", "");
                // ẩn thông tin dòng 1d
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "d)    Danh sách các ứng dụng di động chấp nhận thanh toán VietQR");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "https://onepay.vn/documents/acceptance_vietqr.pdf");
                hide.add(map2);
                Map <String, String> map3 = new LinkedHashMap<>();
                map3.put("type", "row");
                map3.put("key", "List of Mobile pplications that accept VietQR payment shown and updated at the");
                hide.add(map3);
            }

            // String PL1__dieu_2_1_bcd = this.readTemplateFromFile("PL1/dieu_2_1_bcd.html");
            // this.replaceMap.put("PL1__dieu_2_1_bcd", PL1__dieu_2_1_bcd);
            if ("t1".equalsIgnoreCase(this.contractDetail.getString("tgTamUngSelection"))) {
                this.replaceMap.put("tam_ung_truoc_phien", "01 (một) ngày");
                this.replaceMap.put("tam_ung_sau_phien", "02 (hai) ngày");
                this.replaceMap.put("tam_ung_truoc_phien_EN", "01 (one) day");
                this.replaceMap.put("tam_ung_sau_phien_EN", "02 (two) days");
            } else if ("t2".equalsIgnoreCase(this.contractDetail.getString("tgTamUngSelection"))) {
                this.replaceMap.put("tam_ung_truoc_phien", "02 (hai) ngày");
                this.replaceMap.put("tam_ung_sau_phien", "03 (ba) ngày");
                this.replaceMap.put("tam_ung_truoc_phien_EN", "02 (two) days");
                this.replaceMap.put("tam_ung_sau_phien_EN", "03 (three) days");
            } else {
                this.replaceMap.put("tam_ung_truoc_phien", "..............");
                this.replaceMap.put("tam_ung_sau_phien", "..............");
            }

            if ("Miễn phí".equalsIgnoreCase(this.contractDetail.getString("monthFee"))) {
                this.replaceMap.put("PL1__CB_month_fee_free", checked);
                this.replaceMap.put("PL1__CB_month_fee_collect", unChecked);
            } else {
                this.replaceMap.put("PL1__CB_month_fee_free", unChecked);
                this.replaceMap.put("PL1__CB_month_fee_collect", checked);
            }

            if ("ThuPhiCungButToanBaoCo".equalsIgnoreCase(this.contractDetail.getString("hinhThucThuPhi"))) {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", checked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", unChecked);
                this.replaceMap.put("PL1__CB_ThuPhiKhac", unChecked);
                this.replaceMap.put("inputHinhThucThuPhiKhac", "..............");
                //bỏ đoạn này vì đang ảnh hưởng công văn điều chỉnh, chỉ tìm thấy sử dụng trên template payout bản cũ ko dùng
                // Map <String, String> map = new LinkedHashMap<>();
                // map.put("type", "row");
                // map.put("key", "Thu phí theo tháng theo quy định cụ thể tại Mục");
                // hide.add(map);

                // Map <String, String> map2 = new LinkedHashMap<>();
                // map2.put("type", "row");
                // map2.put("key", "Trường hợp thu phí xử lý giao dịch, phí thanh toán hàng tháng");
                // hide.add(map2);
                // Map <String, String> map3 = new LinkedHashMap<>();
                // map3.put("type", "full_table");
                // map3.put("key", "Việc thu phí sẽ được tính bởi OnePay trên cơ sở biểu phí");
                // hide.add(map3);
                
                Map <String, String> map4 = new LinkedHashMap<>();
                map4.put("type", "row");
                map4.put("key", "Collect the fees monthly according to prescribed");
                hide.add(map4);
                // Map <String, String> map5 = new LinkedHashMap<>();
                // map5.put("type", "row");
                // map5.put("key", "commission fees monthly");
                // hide.add(map5);
            } else if ("ThuTheoThang".equalsIgnoreCase(this.contractDetail.getString("hinhThucThuPhi"))) {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", unChecked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", checked);
                this.replaceMap.put("PL1__CB_ThuPhiKhac", unChecked);
                this.replaceMap.put("inputHinhThucThuPhiKhac", "..............");
            } else {
                this.replaceMap.put("PL1__CB_ThuPhiCungButToanBaoCo", unChecked);
                this.replaceMap.put("PL1__CB_ThuTheoThang", unChecked);
                this.replaceMap.put("PL1__CB_ThuPhiKhac", checked);
                this.replaceMap.put("inputHinhThucThuPhiKhac", this.contractDetail.getString("inputHinhThucThuPhiKhac"));
            }

            if ("khongLay".equalsIgnoreCase(this.contractDetail.getString("hoaDonVAT"))) {
                this.replaceMap.put("PL1__KhongLayHoaDon", checked);
                this.replaceMap.put("PL1__CoLayHoaDon", unChecked);
            } else if ("coLay".equalsIgnoreCase(this.contractDetail.getString("hoaDonVAT"))) {
                this.replaceMap.put("PL1__KhongLayHoaDon", unChecked);
                this.replaceMap.put("PL1__CoLayHoaDon", checked);
            } else {
                this.replaceMap.put("PL1__KhongLayHoaDon", unChecked);
                this.replaceMap.put("PL1__CoLayHoaDon", unChecked);
            }

            JsonArray subTableMerchant = this.contractDetail.getJsonArray("subTableMerchant");
            //lặp qua 10 bản ghi nếu bản ghi nào có dữ liệu thì thêm vào table_merchant__body bản ghi nào không có thì remove
            for (int i = 0; i < 10; i++) {
                String strI = String.valueOf(i);
                if (i < 10) {
                    strI = "0" + i;
                }
                if (i < subTableMerchant.size()) {
                    JsonObject row = subTableMerchant.getJsonObject(i);
                    this.replaceMap.put("merchantTxt"+strI, row.getString("merchant", ""));
                    this.replaceMap.put("accountNameTxt"+strI, row.getString("accountName", ""));
                    this.replaceMap.put("accountNumberTxt"+strI, row.getString("accountNumber", ""));
                    this.replaceMap.put("bankTxt"+strI, row.getString("bank", ""));

                    this.replaceMap.put("merchantIdTxt"+strI, row.getString("merchantIDS", ""));
                } else {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "merchantTxt"+strI);
                    hide.add(map);
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "table");
                    map2.put("key", "accountNameTxt"+strI);
                    hide.add(map2);
                    Map <String, String> map3 = new LinkedHashMap<>();
                    map3.put("type", "table");
                    map3.put("key", "accountNumberTxt"+strI);
                    hide.add(map3);
                    Map <String, String> map4 = new LinkedHashMap<>();
                    map4.put("type", "table");
                    map4.put("key", "bankTxt"+strI);
                    hide.add(map4);
                    Map <String, String> map5 = new LinkedHashMap<>();
                    map5.put("type", "table");
                    map5.put("key", "merchantIdTxt"+strI);
                    hide.add(map5);
                }
            }

            // this.replaceMap.put("table_merchant__body", table_merchant__body.toString());

            // String PL1__dieu_2_4_1_a = this.readTemplateFromFile("PL1/dieu_2_4_1_a.html");
            // String PL1__dieu_2_4_1_b = this.readTemplateFromFile("PL1/dieu_2_4_1_b.html");
            // String PL1__dieu_2_4_1_c = this.readTemplateFromFile("PL1/dieu_2_4_1_c.html");
            // String PL1__dieu_2_4_2v3 = this.readTemplateFromFile("PL1/dieu_2_4_2v3.html");
            String PL1__index_2_4_4 = "2.4.4";
            // String PL1__dieu_2_5 = this.readTemplateFromFile("PL1/dieu_2_5.html");
            String n_amount_khoanDamBaoInputTxt ="";
            String s_amount_khoanDamBaoInputTxt ="";
            String s_amount_khoanDamBaoInputTxtEN ="";
            String khoanDamBaoKeepPercentTxt ="";
            String s_khoanDamBaoKeepPercentTxt_EN = "";
            String PL__indexkhoandambao="4";
            //bỏ ký tự đánh dấu table nếu table được dùng
            this.replaceMap.put("tableInfo1", "");
            if ("Mien".equalsIgnoreCase(this.contractDetail.getString("khoanDamBaoSelection"))) {
                PL1__index_2_4_4 = "2.4.2";
                // PL1__dieu_2_4_1_a = "";
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "ĐVCNTT đồng ý nộp tiền vào tài khoản của OnePay");
                hide.add(map);
                // PL1__dieu_2_4_1_b = "";
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "giá trị thanh toán tạm ứng thẻ quốc tế của ĐVCNTT");
                hide.add(map2);
                // PL1__dieu_2_4_2v3 = "";
                Map <String, String> map3 = new LinkedHashMap<>();
                map3.put("type", "row");
                map3.put("key", "2.4.2.");
                hide.add(map3);
                Map <String, String> map4 = new LinkedHashMap<>();
                map4.put("type", "row");
                map4.put("key", "2.4.3.");
                hide.add(map4);
                // PL1__dieu_2_5 = "";
                Map <String, String> map5 = new LinkedHashMap<>();
                map5.put("type", "row");
                map5.put("key", "Thời điểm giải khoanh Khoản đảm bảo thanh toán");
                hide.add(map5);
                Map <String, String> map6 = new LinkedHashMap<>();
                map6.put("type", "row");
                map6.put("key", "Sau 180 ngày kể từ ngày tạm ngưng hoặc thanh lý Hợp đồng");
                hide.add(map6);
                // this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                // this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                // this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
                Map <String, String> map7 = new LinkedHashMap<>();
                map7.put("type", "row");
                map7.put("key", "Appendix to be considered as The collateral");
                Map <String, String> map8 = new LinkedHashMap<>();
                map8.put("type", "row");
                map8.put("key", "value of each advanced payment for Merchant until it reaches enough");
                Map <String, String> map9 = new LinkedHashMap<>();
                map9.put("type", "row");
                map9.put("key", "OnePay shall transfer Merchant‘s collateral amount");
                Map <String, String> map10 = new LinkedHashMap<>();
                map10.put("type", "row");
                map10.put("key", "Exceed the 6 months period, The collateral amount will be extended automatically");
                Map <String, String> map11 = new LinkedHashMap<>();
                map11.put("type", "row");
                map11.put("key", "Time to release The collateral amount");
                Map <String, String> map12 = new LinkedHashMap<>();
                map12.put("type", "row");
                map12.put("key", "The releasing time of The collateral amount is adjusted based on the evaluation");
                Map <String, String> map13 = new LinkedHashMap<>();
                map13.put("type", "row");
                map13.put("key", "After 180 days from the date of suspending or liquidating the Contract");
                hide.add(map7);
                hide.add(map8);
                hide.add(map9);
                hide.add(map10);
                hide.add(map11);
                hide.add(map12);
                hide.add(map13);
                Map <String, String> map14 = new LinkedHashMap<>();
                map14.put("type", "row");
                map14.put("key", "Khoản đảm bảo thanh toán này có thể thay đổi dựa trên những đánh");
                hide.add(map14);

                //hide thông tin khoản đảm bảo theo tag
                Map <String, String> map15 = new LinkedHashMap<>();
                map15.put("type", "tag");
                map15.put("start", "<start-khoangiaikhoanh>");
                map15.put("end", "<end-khoangiaikhoanh>");
                hide.add(map15);
                PL__indexkhoandambao="2";
                //remove full table không dùng trong file template
                Map <String, String> map16 = new LinkedHashMap<>();
                map16.put("type", "full_table");
                map16.put("key", "tableInfo1");
                hide.add(map16);
                Map <String, String> map17 = new LinkedHashMap<>();
                map17.put("type", "row");
                map17.put("key", "Trong vòng thoigianGiaiKhoanh");
                hide.add(map17);
            } else if ("kyQuyKeep".equalsIgnoreCase(this.contractDetail.getString("kyQuyType"))) {
                try {
                    // PL1__dieu_2_4_1_a = "";
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "row");
                    map.put("key", "ĐVCNTT đồng ý nộp tiền vào tài khoản của OnePay");
                    hide.add(map);
                    // PL1__dieu_2_4_1_c = "";
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "row");
                    map2.put("key", "OnePay miễn Khoản đảm bảo khả năng thanh toán cho ĐVCNTT");
                    hide.add(map2);
                    Map <String, String> map3 = new LinkedHashMap<>();
                    map3.put("type", "row");
                    map3.put("key", "Appendix to be considered as The collateral");
                    Map <String, String> map4 = new LinkedHashMap<>();
                    map4.put("type", "row");
                    map4.put("key", "OnePay waives The collateral amount for Merchant");
                    hide.add(map3);
                    hide.add(map4);
                    // this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                    // this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                    // this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
                    n_amount_khoanDamBaoInputTxt = this.readFormatMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxt = this.readFormatStringMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxtEN = this.readFormatStringMoneyEnglish("khoanDamBaoInput");
                    String keepPercentStr = this.contractDetail.getValue("keepPercent") != null ? this.contractDetail.getValue("keepPercent").toString() : "0";
                    int keepPercent = 0;
                    try {
                        keepPercent = Integer.parseInt(keepPercentStr);
                    } catch (NumberFormatException e) {
                        logger.warning("[fillPL1] keepPercent không phải số hợp lệ: " + keepPercentStr);
                    }
                    khoanDamBaoKeepPercentTxt = String.valueOf(keepPercent);
                    s_khoanDamBaoKeepPercentTxt_EN = this.readFormatStringMoneyEnglish("keepPercent").toLowerCase();
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "", e);
                }

                //remove tag không dùng khoangiaikhoanh trong file template
                removeTag("<start-khoangiaikhoanh>","<end-khoangiaikhoanh>");

            } else if ("kyQuyStandard".equalsIgnoreCase(this.contractDetail.getString("kyQuyType")) || "KyQuy".equalsIgnoreCase(this.contractDetail.getString("khoanDamBaoSelection"))) {
                try {
                    // PL1__dieu_2_4_1_b = "";
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "row");
                    map.put("key", "giá trị thanh toán tạm ứng thẻ quốc tế của ĐVCNTT");
                    hide.add(map);
                    // PL1__dieu_2_4_1_c = "";
                    Map <String, String> map2 = new LinkedHashMap<>();
                    map2.put("type", "row");
                    map2.put("key", "OnePay miễn Khoản đảm bảo khả năng thanh toán cho ĐVCNTT");
                    hide.add(map2);
                    Map <String, String> map3 = new LinkedHashMap<>();
                    map3.put("type", "row");
                    map3.put("key", "value of each advanced payment for Merchant until it reaches enough");
                    hide.add(map3);
                    Map <String, String> map4 = new LinkedHashMap<>();
                    map4.put("type", "row");
                    map4.put("key", "OnePay waives The collateral amount for Merchant");
                    hide.add(map4);
                    // this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                    // this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                    // this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
                    n_amount_khoanDamBaoInputTxt = this.readFormatMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxt = this.readFormatStringMoney("khoanDamBaoInput");
                    s_amount_khoanDamBaoInputTxtEN = this.readFormatStringMoneyEnglish("khoanDamBaoInput");
                    // s_amount_khoanDamBaoInputTxt = this.readFormatStringMoney("khoanDamBaoInput");
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "", e);
                }

                //remove tag không dùng khoangiaikhoanh trong file template
                removeTag("<start-khoangiaikhoanh>","<end-khoangiaikhoanh>");
            } else {
                //remove tag không dùng khoangiaikhoanh trong file template
                removeTag("<start-khoangiaikhoanh>","<end-khoangiaikhoanh>");
                // this.replaceMap.put("PL1__dieu_2_4_1_a", PL1__dieu_2_4_1_a);
                // this.replaceMap.put("PL1__dieu_2_4_1_b", PL1__dieu_2_4_1_b);
                // this.replaceMap.put("PL1__dieu_2_4_1_c", PL1__dieu_2_4_1_c);
            }

            this.replaceMap.put("PL__indexkhoandambao", PL__indexkhoandambao);

            // this.replaceMap.put("PL1__dieu_2_4_2v3", PL1__dieu_2_4_2v3);
            // this.replaceMap.put("PL1__dieu_2_5", PL1__dieu_2_5);
            String stkGiaiKhoanhTxt = this.contractDetail.getString("stkGiaiKhoanh");
            String giaiKhoanhOpenByBankTxt = this.contractDetail.getString("openByBank");
            this.replaceMap.put("stkGiaiKhoanhTxt", stkGiaiKhoanhTxt);
            this.replaceMap.put("giaiKhoanhOpenByBankTxt", giaiKhoanhOpenByBankTxt);
            this.replaceMap.put("n_amount_khoanDamBaoInputTxt", n_amount_khoanDamBaoInputTxt);
            this.replaceMap.put("s_amount_khoanDamBaoInputTxtEN", s_amount_khoanDamBaoInputTxtEN);
            this.replaceMap.put("s_amount_khoanDamBaoInputTxt", s_amount_khoanDamBaoInputTxt);
            this.replaceMap.put("s_khoanDamBaoKeepPercentTxt_EN", s_khoanDamBaoKeepPercentTxt_EN);
            this.replaceMap.put("khoanDamBaoKeepPercentTxt", khoanDamBaoKeepPercentTxt);

            this.replaceMap.put("kyHanFDTxt", this.contractDetail.getString("kyHanFD", "..."));
            String stkOPKyQuy = this.contractDetail.getString("accountNumber");
            if (Objects.equals(stkOPKyQuy, "vietinbank")) {
                stkOPKyQuy = "Vietinbank";
            } else if (Objects.equals(stkOPKyQuy, "vietcombank")) {
                stkOPKyQuy = "Vietcombank";
            } else if (Objects.equals(stkOPKyQuy, "") || Objects.equals(stkOPKyQuy, "other")) {
                stkOPKyQuy = this.contractDetail.getString("accountNumberOther");
            }
            this.replaceMap.put("stkOPKyQuyTxt", stkOPKyQuy);

            this.replaceMap.put("PL1__index_2_4_4", PL1__index_2_4_4);

            // String PL1__dieu_3_6 = this.readTemplateFromFile("PL1/dieu_3_6.html");
            String PL1__index_3_7 = "3.7";
            String PL1__index_3_8 = "3.8";
            String PL1__index_3_9 = "3.9";
            if (!this.approveShopify) {
                // PL1__dieu_3_6 = "";
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "3.6.\tPhí Shopify");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "Áp dụng với các giao dịch thành công trên nền tảng Shopify");
                hide.add(map2);
                Map <String, String> map3 = new LinkedHashMap<>();
                map3.put("type", "row");
                map3.put("key", "Đối với các giao dịch của các Merchant ID chạy qua nền tảng Shopify");
                hide.add(map3);
                Map <String, String> map4 = new LinkedHashMap<>();
                map4.put("type", "row");
                map4.put("key", "Shall be applied for successful transactions on Shopify");
                hide.add(map4);
                Map <String, String> map5 = new LinkedHashMap<>();
                map5.put("type", "row");
                map5.put("key", "As regards the transactions belonging to those Merchant IDs which are on Shopify");
                hide.add(map5);
                PL1__index_3_7 = "3.6";
                PL1__index_3_8 = "3.7";
                PL1__index_3_9 = "3.8";
            } 
            // this.replaceMap.put("PL1__dieu_3_6", PL1__dieu_3_6);
            this.replaceMap.put("PL1__index_3_7", PL1__index_3_7);
            this.replaceMap.put("PL1__index_3_8", PL1__index_3_8);
            this.replaceMap.put("PL1__index_3_9", PL1__index_3_9);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void removeTag(String tagStart,String tagEnd){
        // this.replaceMap.put(tagStart, "");
        // this.replaceMap.put(tagEnd, "");
        Map <String, String> map = new LinkedHashMap<>();
        map.put("type", "row");
        map.put("key", tagStart);
        hide.add(map);
        Map <String, String> map2 = new LinkedHashMap<>();
        map2.put("type", "row");
        map2.put("key", tagEnd);
        hide.add(map2);
    }

    private void fillPL2() {
        try {
            // String PL2__main = this.readTemplateFromFile("PL2/main.html");
            if (!this.approveInstallment) {
                // PL2__main = "";
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-phuluc2>");
                map.put("end", "<end-phuluc2>");
                hide.add(map);
            }else{
                this.replaceMap.put("<start-phuluc2>", "");
                this.replaceMap.put("<end-phuluc2>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-phuluc2>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-phuluc2>");
                hide.add(map2);
            }
            // this.replaceMap.put("PL2__main", PL2__main);

            JsonArray subTableFee = this.contractDetail.getJsonArray("subTableFee");
            // StringBuilder PL2__table_fee__body = new StringBuilder();
            // String templateRow = this.readTemplateFromFile("PL2/table_fee__body.html");
            for (int i = 0; i < 41; i++) {
                String strI = String.valueOf(i);
                if (i < 10) {
                    strI = "0" + i;
                }
                if (i >= subTableFee.size()) {
                    Map <String, String> map = new LinkedHashMap<>();
                    map.put("type", "table");
                    map.put("key", "bankName"+strI);
                    hide.add(map);
                    continue;
                }else{
                    JsonObject row = subTableFee.getJsonObject(i);
                    // String rowHtml = templateRow.replace("soThuTu", String.valueOf(i + 1));
                    String bankName = row.getString("bankName");
                    if ("MSB".equalsIgnoreCase(bankName))
                        bankName = "(*) " + bankName;
                    if ("FECredit".equalsIgnoreCase(bankName))
                        bankName = "(**) " + bankName;
                    
                    this.replaceMap.put("bankName"+strI, bankName);
                    this.replaceMap.put("threeMonths"+strI, !isEmpty(row.getString("threeMonths")) ? row.getString("threeMonths") + "%" : "");
                    this.replaceMap.put("sixMonths"+strI, !isEmpty(row.getString("sixMonths")) ? row.getString("sixMonths") + "%" : "");
                    this.replaceMap.put("nineMonths"+strI, !isEmpty(row.getString("nineMonths")) ? row.getString("nineMonths") + "%" : "");
                    this.replaceMap.put("twelveMonths"+strI, !isEmpty(row.getString("twelveMonths")) ? row.getString("twelveMonths") + "%" : "");
                    this.replaceMap.put("fifteenMonths"+strI, !isEmpty(row.getString("fifteenMonths")) ? row.getString("fifteenMonths") + "%" : "");
                    this.replaceMap.put("eighteenMonths"+strI, !isEmpty(row.getString("eighteenMonths")) ? row.getString("eighteenMonths") + "%" : "");
                    this.replaceMap.put("twentyFourMonths"+strI, !isEmpty(row.getString("twentyFourMonths")) ? row.getString("twentyFourMonths") + "%" : "");
                    this.replaceMap.put("thirtySixMonths"+strI, !isEmpty(row.getString("thirtySixMonths")) ? row.getString("thirtySixMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("bankName", bankName);
                    // rowHtml = rowHtml.replace("threeMonths", !isEmpty(row.getString("threeMonths")) ? row.getString("threeMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("sixMonths", !isEmpty(row.getString("sixMonths")) ? row.getString("sixMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("nineMonths", !isEmpty(row.getString("nineMonths")) ? row.getString("nineMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("twelveMonths", !isEmpty(row.getString("twelveMonths")) ? row.getString("twelveMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("fifteenMonths", !isEmpty(row.getString("fifteenMonths")) ? row.getString("fifteenMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("eighteenMonths", !isEmpty(row.getString("eighteenMonths")) ? row.getString("eighteenMonths") + "%" : "");
                    // rowHtml = rowHtml.replace("twentyFourMonths", !isEmpty(row.getString("twentyFourMonths")) ? row.getString("twentyFourMonths") + "%" : "");
                    // PL2__table_fee__body.append(rowHtml);
                }

            }
            // this.replaceMap.put("PL2__table_fee__body", PL2__table_fee__body.toString());

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }
    }

    private void fillPL3() {
        try {
            // String PL3__main = this.approveBNPL? this.readTemplateFromFile("PL3/main.html") : "";
            if (!this.approveBNPL) {
                // PL3__main = "";
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "tag");
                map.put("start", "<start-phuluc3>");
                map.put("end", "<end-phuluc3>");
                hide.add(map);
            }else{
                this.replaceMap.put("<start-phuluc3>", "");
                this.replaceMap.put("<end-phuluc3>", "");
                Map <String, String> map = new LinkedHashMap<>();
                map.put("type", "row");
                map.put("key", "<start-phuluc3>");
                hide.add(map);
                Map <String, String> map2 = new LinkedHashMap<>();
                map2.put("type", "row");
                map2.put("key", "<end-phuluc3>");
                hide.add(map2);
            }
            // this.replaceMap.put("PL3__main", PL3__main);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }

    }

    private void fillSTT() {
        try {
            String PL2__index_main = "02";
            String PL3__index_main = "03";

            if (!this.approveInstallment && this.approveBNPL) {
                PL2__index_main = "03";
                PL3__index_main = "02";
            } else if (this.approveInstallment && !this.approveBNPL) {
                PL2__index_main = "02";
                PL3__index_main = "03";
            } else if (!this.approveInstallment && !this.approveBNPL) {
                PL2__index_main = "02";
                PL3__index_main = "03";
            }

            this.replaceMap.put("PL2__index_main", PL2__index_main);
            this.replaceMap.put("PL3__index_main", PL3__index_main);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        }

    }

    private String readFormatMoney(String key) {
        DecimalFormat df = new DecimalFormat("#,##0");
        Object value = this.contractDetail.getValue(key);

        // Nếu giá trị là null hoặc rỗng, trả về "0"
        if (Objects.isNull(value) || Objects.equals(value, "")) {
            return "0";
        }

        // Chuyển giá trị về dạng chuỗi
        String stringValue = String.valueOf(value);
        
        // Nếu là chuỗi, xử lý định dạng số
        if (value instanceof String) {
            // Loại bỏ dấu phẩy và khoảng trắng
            String cleanValue = stringValue.replaceAll("[,\\s]", "");
            
            // Kiểm tra xem có phải số nguyên không
            if (!cleanValue.matches("^\\d+$")) {
                logger.warning("Giá trị '" + stringValue + "' không phải là số nguyên hợp lệ");
                return stringValue;
            }
            
            try {
                // Thử parse thành số để kiểm tra lần cuối
                int numValue = Integer.parseInt(cleanValue);
                return df.format(numValue);
            } catch (Exception e) {
                logger.warning("Lỗi khi định dạng số: " + e.getMessage());
                return stringValue;
            }
        }
        
        // Xử lý cho kiểu Integer
        if (value instanceof Integer) {
            Integer intValue = (Integer) value;
            return intValue.equals(0) ? "0" : df.format(intValue);
        }
        
        // Xử lý cho kiểu Long
        if (value instanceof Long) {
            Long longValue = (Long) value;
            return longValue.equals(0L) ? "0" : df.format(longValue);
        }
        
        // Trả về giá trị gốc dưới dạng chuỗi nếu không phải các trường hợp trên
        return stringValue;
    }

    private String readFormatStringMoney(String key) {
        Object value = this.contractDetail.getValue(key);
        logger.info("[readFormatStringMoney] key: " + key + ", value: " + value);
        // Trả về "Không" nếu giá trị là null hoặc rỗng
        if (Objects.isNull(value) || Objects.equals(value, "")) {
            logger.info("[readFormatStringMoney] key: " + key + " is null or empty, return 'Không'");
            return "Không";
        }
        String s_value = String.valueOf(value);
        // Loại bỏ dấu phẩy và khoảng trắng để kiểm tra
        String cleanValue = s_value.replaceAll("[,\\s]", "");
        logger.info("[readFormatStringMoney] key: " + key + ", cleanValue: " + cleanValue);
        // Kiểm tra kỹ lưỡng xem chuỗi có phải là số nguyên không
        // Nếu không phải là số nguyên hợp lệ, trả về giá trị gốc
        if (!cleanValue.matches("^\\d+$")) {
            logger.warning("[readFormatStringMoney] Giá trị '" + s_value + "' không phải là số nguyên hợp lệ");
            return s_value;
        }
        try {
            // Thử chuyển đổi thành số để kiểm tra thêm một lần nữa
            Long.parseLong(cleanValue);
            logger.info("[readFormatStringMoney] key: " + key + ", gọi ConvertMoneyNumberToString.readNumberToMoney với cleanValue: " + cleanValue);
            // Nếu là số hợp lệ, tiến hành chuyển đổi sang chữ
            String result = ConvertMoneyNumberToString.readNumberToMoney(cleanValue)
                .stream()
                .collect(Collectors.joining(" "));
            logger.info("[readFormatStringMoney] key: " + key + ", kết quả chuyển đổi: " + result);
            return result;
        } catch (Exception e) {
            // Nếu có bất kỳ lỗi nào, trả về giá trị gốc
            logger.warning("[readFormatStringMoney] Lỗi khi chuyển đổi số thành chữ: " + e.getMessage());
            return s_value;
        }
    }

    private String readFormatStringMoneyEnglish(String key) {
        Object value = this.contractDetail.getValue(key);

        // Trả về "Zero" nếu giá trị là null hoặc rỗng
        if (Objects.isNull(value) || Objects.equals(value, "")) {
            return "Zero";
        }

        // Chuyển giá trị về dạng chuỗi
        String stringValue = String.valueOf(value);
        
        // Nếu là chuỗi, kiểm tra và xử lý
        if (value instanceof String) {
            // Loại bỏ dấu phẩy và khoảng trắng
            String cleanValue = stringValue.replaceAll("[,\\s]", "");
            
            // Kiểm tra xem có phải số nguyên không
            if (!cleanValue.matches("^\\d+$")) {
                logger.warning("Giá trị '" + stringValue + "' không phải là số nguyên hợp lệ cho chuyển đổi MoneyEnglish");
                return stringValue;
            }
            
            try {
                // Thử parse thành số để tiếp tục xử lý
                int numValue = Integer.parseInt(cleanValue);
                
                // Kiểm tra giới hạn
                if (numValue > 999999999) {
                    logger.warning("Giá trị '" + numValue + "' quá lớn để chuyển đổi (> 999,999,999)");
                    return "Number too large";
                }
                
                // Chuyển đổi sang chữ
                String result = ConvertNumberToWordEnglish.convertNumberToWord(numValue);
                
                // Viết hoa chữ cái đầu
                if (result.length() > 0) {
                    result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                }
                
                return result;
            } catch (Exception e) {
                logger.warning("Lỗi khi Format Money English: " + e.getMessage());
                return stringValue;
            }
        }
        
        // Xử lý cho kiểu Integer
        if (value instanceof Integer) {
            int numValue = (Integer) value;
            
            // Kiểm tra giới hạn
            if (numValue > 999999999) {
                return "Number too large";
            }
            
            try {
                // Chuyển đổi sang chữ
                String result = ConvertNumberToWordEnglish.convertNumberToWord(numValue);
                
                // Viết hoa chữ cái đầu
                if (result.length() > 0) {
                    result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                }
                
                return result;
            } catch (Exception e) {
                logger.warning("Lỗi khi chuyển đổi số thành chữ (tiếng Anh): " + e.getMessage());
                return stringValue;
            }
        }
        
        // Trả về giá trị gốc dưới dạng chuỗi nếu không phải các trường hợp trên
        return stringValue;
    }

    private String readFeePercent(String key) {
        Object value = this.contractDetail.getValue(key);
        if (Objects.isNull(value) || Objects.equals(value, ""))
            return "-";
        else if (Objects.equals(value, "Không áp dụng phí thanh toán"))
            return "Không áp dụng";
        else
            return value.toString() + " %";
    }

    private static Boolean isEmpty(String s) {
        return s == null || s.length() == 0;
    }

    private String formatDate(Timestamp date, String pattern, String def) {
        if (date == null) return def;
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    private String formatDateText(Timestamp date, String def) {
        if (date == null) return def;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH) + 1;
        int year = cal.get(Calendar.YEAR);
        return String.format("ngày %d tháng %d năm %d", day, month, year);
    }

    /**
     * Extract from date
     * @return
     */
    private int extractFromDate(Timestamp date,String type) {
        if (date == null) return 0;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int month = cal.get(Calendar.MONTH) + 1;
        int year = cal.get(Calendar.YEAR);
        if (type.equals("day")) {
            return day;
        } else if (type.equals("month")) {
            return month;
        } else if (type.equals("year")) {
            return year;
        }
        return 0;
    }

    public List<Map<String, String>> getHide() {
        return hide;
    }

    /**
     * Lấy giá trị từ JsonObject dưới dạng String một cách an toàn
     * Phương thức này xử lý các trường hợp khi giá trị là kiểu Integer hoặc các kiểu khác
     */
    private String getStringValue(JsonObject jsonObject, String key) {
        if (jsonObject == null || !jsonObject.containsKey(key)) {
            return null;
        }
        
        Object value = jsonObject.getValue(key);
        if (value == null) {
            return null;
        }
        
        return value.toString();
    }
}
