package vn.onepay.portal.resources.merchantmanagement.sale.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class SaleMonthDetailDto {
    private String month;
    private Integer countSuccess;
    private Integer countFail;
    private Double total;

    public SaleMonthDetailDto() {
    }

    public SaleMonthDetailDto(String month, Integer countSuccess,  Integer countFail,Double total) {
        this.month = month;
        this.countSuccess = countSuccess;
        this.countFail = countFail;
        this.total = total;
    }

    public Integer getCountFail() {
        return countFail;
    }

    public void setCountFail(Integer countFail) {
        this.countFail = countFail;
    }

    public String getMonth() {
        return this.month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public Integer getCountSuccess() {
        return this.countSuccess;
    }

    public void setCountSuccess(Integer countSuccess) {
        this.countSuccess = countSuccess;
    }

    public Double getTotal() {
        return this.total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public SaleMonthDetailDto month(String month) {
        this.month = month;
        return this;
    }

    public SaleMonthDetailDto countSuccess(Integer countSuccess) {
        this.countSuccess = countSuccess;
        return this;
    }

    public SaleMonthDetailDto countFail(Integer countFail) {
        this.countFail = countFail;
        return this;
    }

    public SaleMonthDetailDto total(Double total) {
        this.total = total;
        return this;
    }

    public SaleMonthDetailDto totalUSD(Double exchangeRate) {
        this.total /= exchangeRate;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " month='" + getMonth() + "'" + ", count='" + getCountSuccess() + "'" + ", total='" + getTotal() + "'"
                + "}";
    }
}
