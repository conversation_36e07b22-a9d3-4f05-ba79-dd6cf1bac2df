/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/29/19 11:40 AM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ContactDto {
    private int id;
    private String groupIds;
    private int partnerId;
    private String name;
    private String phone;
    private String mobile;
    private String email;
    // private String position;
    private String title;
    private int isWork;
    private Date createDate;
    private Date updateDate;
    private int userIdUpdate;
    private String prefix;

    public ContactDto() {
    }

    public ContactDto(int id, String groupIds, int partnerId, String name, String phone, String mobile, String email,
            String title, int isWork, Date createDate, Date updateDate, int userIdUpdate, String prefix) {
        this.id = id;
        this.groupIds = groupIds;
        this.partnerId = partnerId;
        this.name = name;
        this.phone = phone;
        this.mobile = mobile;
        this.email = email;
        this.title = title;
        this.isWork = isWork;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.userIdUpdate = userIdUpdate;
        this.prefix = prefix;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGroupIds() {
        return this.groupIds;
    }

    public void setGroupIds(String groupIds) {
        this.groupIds = groupIds;
    }

    public int getPartnerId() {
        return this.partnerId;
    }

    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getIsWork() {
        return this.isWork;
    }

    public void setIsWork(int isWork) {
        this.isWork = isWork;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public int getUserIdUpdate() {
        return this.userIdUpdate;
    }

    public void setUserIdUpdate(int userIdUpdate) {
        this.userIdUpdate = userIdUpdate;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public ContactDto id(int id) {
        this.id = id;
        return this;
    }

    public ContactDto groupIds(String groupIds) {
        this.groupIds = groupIds;
        return this;
    }

    public ContactDto partnerId(int partnerId) {
        this.partnerId = partnerId;
        return this;
    }

    public ContactDto name(String name) {
        this.name = name;
        return this;
    }

    public ContactDto phone(String phone) {
        this.phone = phone;
        return this;
    }

    public ContactDto mobile(String mobile) {
        this.mobile = mobile;
        return this;
    }

    public ContactDto email(String email) {
        this.email = email;
        return this;
    }

    public ContactDto title(String title) {
        this.title = title;
        return this;
    }

    public ContactDto isWork(int isWork) {
        this.isWork = isWork;
        return this;
    }

    public ContactDto createDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public ContactDto updateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public ContactDto userIdUpdate(int userIdUpdate) {
        this.userIdUpdate = userIdUpdate;
        return this;
    }

    public ContactDto prefix(String prefix) {
        this.prefix = prefix;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", groupIds='" + getGroupIds() + "'" + ", partnerId='" + getPartnerId()
                + "'" + ", name='" + getName() + "'" + ", phone='" + getPhone() + "'" + ", mobile='" + getMobile() + "'"
                + ", email='" + getEmail() + "'" + ", title='" + getTitle() + "'" + ", isWork='" + getIsWork() + "'"
                + ", createDate='" + getCreateDate() + "'" + ", updateDate='" + getUpdateDate() + "'"
                + ", userIdUpdate='" + getUserIdUpdate() + "'" + ", prefix='" + getPrefix() + "'" + "}";
    }
}
