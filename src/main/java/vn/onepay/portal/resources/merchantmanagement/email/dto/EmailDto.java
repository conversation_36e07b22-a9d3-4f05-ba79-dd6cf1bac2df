/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 5/9/19 2:45 PM
 */

package vn.onepay.portal.resources.merchantmanagement.email.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

import java.util.Date;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class EmailDto {
    private int id_batch;
    private String name;
    private String contract_type;
    private String state;
    private int total;
    private int sent;
    private Date createDate;
    private String paygate;
    private String service;
    private String groups;
    private String subject;
    private String emailContent;

    public EmailDto() {
    }

    public EmailDto(int id_batch, String name, String contract_type, String state, int total, int sent, Date createDate,
            String paygate, String service, String groups, String subject, String emailContent) {
        this.id_batch = id_batch;
        this.name = name;
        this.contract_type = contract_type;
        this.state = state;
        this.total = total;
        this.sent = sent;
        this.createDate = createDate;
        this.paygate = paygate;
        this.service = service;
        this.groups = groups;
        this.subject = subject;
        this.emailContent = emailContent;
    }

    public int getId_batch() {
        return this.id_batch;
    }

    public void setId_batch(int id_batch) {
        this.id_batch = id_batch;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContract_type() {
        return this.contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getTotal() {
        return this.total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSent() {
        return this.sent;
    }

    public void setSent(int sent) {
        this.sent = sent;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getPaygate() {
        return this.paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getService() {
        return this.service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getGroups() {
        return this.groups;
    }

    public void setGroups(String groups) {
        this.groups = groups;
    }

    public String getSubject() {
        return this.subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getEmailContent() {
        return this.emailContent;
    }

    public void setEmailContent(String emailContent) {
        this.emailContent = emailContent;
    }

    public EmailDto id_batch(int id_batch) {
        this.id_batch = id_batch;
        return this;
    }

    public EmailDto name(String name) {
        this.name = name;
        return this;
    }

    public EmailDto contract_type(String contract_type) {
        this.contract_type = contract_type;
        return this;
    }

    public EmailDto state(String state) {
        this.state = state;
        return this;
    }

    public EmailDto total(int total) {
        this.total = total;
        return this;
    }

    public EmailDto sent(int sent) {
        this.sent = sent;
        return this;
    }

    public EmailDto createDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public EmailDto paygate(String paygate) {
        this.paygate = paygate;
        return this;
    }

    public EmailDto service(String service) {
        this.service = service;
        return this;
    }

    public EmailDto groups(String groups) {
        this.groups = groups;
        return this;
    }

    public EmailDto subject(String subject) {
        this.subject = subject;
        return this;
    }

    public EmailDto emailContent(String emailContent) {
        this.emailContent = emailContent;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id_batch='" + getId_batch() + "'" + ", name='" + getName() + "'" + ", contract_type='"
                + getContract_type() + "'" + ", state='" + getState() + "'" + ", total='" + getTotal() + "'"
                + ", sent='" + getSent() + "'" + ", createDate='" + getCreateDate() + "'" + ", paygate='" + getPaygate()
                + "'" + ", service='" + getService() + "'" + ", groups='" + getGroups() + "'" + ", subject='"
                + getSubject() + "'" + ", emailContent='" + getEmailContent() + "'" + "}";
    }
}
