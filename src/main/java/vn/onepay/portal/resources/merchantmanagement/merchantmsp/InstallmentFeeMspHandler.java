package vn.onepay.portal.resources.merchantmanagement.merchantmsp;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;

import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class InstallmentFeeMspHandler implements IConstants {

    /**
     * GET /msp/installment-fee
     * @param ctx
     */
    public static void getInstallmentFee(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, InstallmentFeeMspDao.getInstallmentsFee());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static Logger logger = Logger.getLogger(InstallmentMspHandler.class.getName());

}
