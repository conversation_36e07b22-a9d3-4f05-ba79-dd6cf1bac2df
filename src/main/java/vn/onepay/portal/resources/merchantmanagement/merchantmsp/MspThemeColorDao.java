package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 9/11/2020
 * Time: 12:01 PM
 * To change this portal2-service.
 */

public class MspThemeColorDao extends Db implements IConstants {
    public static ThemeColorMerchantDto get(String merchant_id) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ThemeColorMerchantDto themeColorMerchantDto = new ThemeColorMerchantDto();
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.get_theme_color_by_merchant(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchant_id);
            cs.execute();
            int nError = cs.getInt(2);
            String sError = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST get_theme_color_by_merchant error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    themeColorMerchantDto = bindThemeColor(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return themeColorMerchantDto;
    }
    public static void put(String merchant_id , ThemeColorMerchantDto themeColorMerchantDto) {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP_MERCHANT.PUT_THEME_COLOR(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, merchant_id);
            cs.setString(4, themeColorMerchantDto.getTheme());
            cs.execute();
            int nError = cs.getInt(1);
            String sError = cs.getString(2);
            if (nError != 200) {
                logger.severe("GET LIST THEME COLOR error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
    }

    private static ThemeColorMerchantDto bindThemeColor(ResultSet rs) throws Exception {
        ThemeColorMerchantDto dto = new ThemeColorMerchantDto();

        dto.setMerchantId(rs.getString("S_ID"));
        String arrayThemeColor = rs.getString("S_THEMES");
        dto.setTheme(arrayThemeColor);
        return dto;
    }
}
