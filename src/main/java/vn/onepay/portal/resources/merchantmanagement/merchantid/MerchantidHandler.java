package vn.onepay.portal.resources.merchantmanagement.merchantid;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantidHandler implements IConstants {

    private static Logger logger = Logger.getLogger(MerchantidHandler.class.getName());

    // get list bnpl by partner id
    public static void getListGateBnplByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateBnplByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list vietqr by partner id
    public static void getListGateVietQRByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateVietQRByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    // get list directdebit by partner id 
    public static void getListGateDirectDebitByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateDirectDebitByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list bank QT by partner id
    public static void getListGateQTbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateQTbyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list bank ND by partner id
    public static void getListGateNDbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateNDbyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list bank QR by partner id
    public static void getListGateQRbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateQRbyPartnerId2(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // get list Invoice by partner id
    public static void getListInvoicebyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListInvoicebyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    // get list Quicklink by partner id
    public static void getListQuickLinkbyPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListQuickLinkbyPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAcquirer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantidDao.getListAcquirer());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getApp(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantidDao.getListApp2());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListGateUposByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGateUposByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListGatePayoutByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGatePayoutByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListGatePaycollectByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Integer userId = ctx.get(N_USER_ID);

                MerchantInfoHandler.checkPermissionPartner(partnerId, userId);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, partnerId);
                sendResponse(ctx, 200, MerchantidDao.getListGatePaycollectByPartnerId(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

}
