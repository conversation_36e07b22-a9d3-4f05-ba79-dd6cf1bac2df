/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/30/19 9:05 AM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;
//import lombok.Builder;

//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ContactGroupsDto {
    private int contactId;
    private int groupId;

    public ContactGroupsDto() {
    }

    public ContactGroupsDto(int contactId, int groupId) {
        this.contactId = contactId;
        this.groupId = groupId;
    }

    public int getContactId() {
        return this.contactId;
    }

    public void setContactId(int contactId) {
        this.contactId = contactId;
    }

    public int getGroupId() {
        return this.groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public ContactGroupsDto contactId(int contactId) {
        this.contactId = contactId;
        return this;
    }

    public ContactGroupsDto groupId(int groupId) {
        this.groupId = groupId;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " contactId='" + getContactId() + "'" + ", groupId='" + getGroupId() + "'" + "}";
    }
}
