package vn.onepay.portal.resources.merchantmanagement.merchantmsp;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.approval_request.ApprovalRequestDAO;
import vn.onepay.portal.resources.approval_request.dto.ApprovalConvertDto;
import vn.onepay.portal.resources.approval_request.dto.ApprovalDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.InstallmentMerchantDto;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MerchantMspDto;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MspBinGroupDto;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MspBinGroupExtendDto;
import vn.onepay.portal.resources.permission.PermissionDao;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.Util.gson;
import static vn.onepay.portal.Util.sendResponse;

public class MerchantMspHandler implements IConstants {

    /**
     * GET /msp/merchant
     *
     * @param ctx
     */
    public static void getMerchants(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String keyword = request.getParam(KEYWORD) == null ? BLANK : request.getParam(KEYWORD);
            int page = request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE));
            int pageSize = request.getParam(PAGE_SIZE) == null ? Integer.MAX_VALUE : Integer.parseInt(request.getParam(PAGE_SIZE));
            Integer userId = ctx.get(X_USER_ID);
            Map<String, Object> map = new HashMap<>();
            map.put(KEYWORD, keyword);
            map.put(PAGE, page);
            map.put(PAGE_SIZE, pageSize);
            try {
                List<String> roles = PermissionDao.getListRoleIDbyUserId(userId);
                String roleName = Config.getString("role_merchant.role_id", "");
                if (roles.contains(roleName)) {
                    String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                    if (!merchantsConf.isBlank()) {
                        map.put(MERCHANT_ID, merchantsConf);
                    }
                }
                sendResponse(ctx, 200, MerchantMspDao.getMerchants(map));
            } catch (Exception e) {

                logger.log(Level.SEVERE, "ERROR ON get MERCHANT CONFIG: " , e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * GET /msp/merchant/:id
     *
     * @param ctx
     */
    public static void getMerchantById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam(ID);

            if (id == null) {
                throw IErrors.VALIDATION_ERROR;
            }

            try {
                sendResponse(ctx, 200, MerchantMspDao.getMerchantById(id));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkMspMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam(ID);
            String name = request.getParam(NAME);
            if (id == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            try {
                Integer countMerchantId = MerchantMspDao.checkMerchantById(id);
                Integer countMerchantName = MerchantMspDao.checkMerchantByName(id, name);
                Integer countTransWSP = MerchantMspDao.checkTransactionWSP(id);
                Integer countTransNotVIETINQR = MerchantMspDao.checkTransNotVIETINQR(id);
                JsonObject res = new JsonObject();
                String statusMessage1 = countMerchantId == 0 ? "Successful" : "Merchant ID already exists";
                String statusMessage2 = countMerchantName == 0 ? "Successful" : "Merchant Name already exists";
                String statusMessage3 = countTransWSP == 0 ? "Successful" : "Failed";
                String statusMessage4 = countTransNotVIETINQR == 0 ? "Successful" : "Failed";
                res.put("checkMerchantId", statusMessage1);
                res.put("checkMerchantName", statusMessage2);
                res.put("checkTransWSP", statusMessage3);
                res.put("checkTransNotVIETINQR", statusMessage4);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkTransNotVIETINQR(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam(ID);
            if (id == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            try {
                Integer countTransNotVIETINQR = MerchantMspDao.checkTransNotVIETINQR(id);
                JsonObject res = new JsonObject();
                boolean statusMessage = countTransNotVIETINQR == 0 ? false : true;
                res.put("result", statusMessage);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkMerchantConfigVIETINQR(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam(ID);
            if (id == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            try {
                Integer countClientId = MerchantMspDao.checkMerchantConfigVIETINQR(id);
                JsonObject res = new JsonObject();
                String statusMessage = countClientId == 0 ? "Successful" : "Failed";
                res.put("result", statusMessage);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void genHashCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            try {
                String hashCode = com.onepay.commons.util.Convert.toHexString(com.onepay.commons.util.Convert.md5(MerchantMspDao.randomString(32).getBytes()));
                JsonObject res = new JsonObject();
                res.put("result", hashCode);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void genAccessCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            try {
                String accessCode = MerchantMspDao.randomString(8);
                JsonObject res = new JsonObject();
                res.put("result", accessCode);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * POST /msp/merchant
     *
     * @param ctx
     */
    public static void upsertMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();

            MerchantMspDto merchantDto = Util.gson.fromJson(body, MerchantMspDto.class);
            if (merchantDto == null
                    || merchantDto.getMerchant_id() == null
                    || merchantDto.getMerchant_name() == null) {
                throw IErrors.VALIDATION_ERROR;
            }

            try {
                int result = MerchantMspDao.upsertMerchant(merchantDto);
                JsonObject res = new JsonObject();
                int statusCode = result == 1 ? 200 : 500;
                String statusMessage = result == 1 ? "Successful" : "Failed";
                res.put("status", statusMessage);
                sendResponse(ctx, statusCode, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * POST /msp/merchant-approval-request
     *
     * @param ctx
     */
    public static void insertAproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
//            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();
            int userId = ctx.get(X_USER_ID);

            MerchantMspDto merchantNewDto = new Gson().fromJson(body, MerchantMspDto.class);
            if (merchantNewDto == null
                    || merchantNewDto.getMerchant_id() == null
                    || merchantNewDto.getMerchant_name() == null) {
                throw IErrors.VALIDATION_ERROR;
            }


            try {
                //validate lại tổng pf = icf + cf
                if (merchantNewDto.getInstallment_merchants() != null && merchantNewDto.getInstallment_merchants().size() > 0) {
                    if (!validatePercentageFee(merchantNewDto.getInstallment_merchants())) {
                        logger.info("Error in function validatePercentageFee");
                        throw IErrors.VALIDATION_ERROR;
                    }
                }
//                MerchantMspDto getMerchantGeneralById = MerchantMspDao.getMerchantById(merchantNewDto.getMerchant_id());
                MerchantMspDto merchantOldDto = MerchantMspDao.getMerchantById(merchantNewDto.getMerchant_id());
                JsonObject res = new JsonObject();
                Integer result = 0;
                String statusMessage = null;
                if (!Util.gson.toJson(merchantOldDto).equals(Util.gson.toJson(merchantNewDto))) {
                    JsonObject condition = new JsonObject();
                    condition.put("merchantId", merchantNewDto.getMerchant_id());
                    ApprovalDto approvalDto = new ApprovalDto();
                    approvalDto.setNewValue(gson.toJson(merchantNewDto));
                    approvalDto.setOldValue(gson.toJson(merchantOldDto));
                    approvalDto.setCondition(condition.encode());
                    approvalDto.setUserRequest(Integer.toString(userId));
                    approvalDto.setDescription("insert new merchant config");
                    approvalDto.setField("ALL");
                    approvalDto.setSchema("MSP");
                    approvalDto.setTable("TB_MERCHANT");
                    approvalDto.setType("INSERT_MERCHANT_CONFIG");
                    result = ApprovalRequestDAO.insertApproval(approvalDto) > 0 ? 200: 500;
                    statusMessage = result > 0 ? "Successful" : "Failed";
                } else {
                    result = 200;
                    statusMessage = "Data Not change";
                }
                res.put("status", statusMessage);
                sendResponse(ctx,result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static boolean validatePercentageFee(List<InstallmentMerchantDto> installmentMerchants) {
        JsonArray feeArr;
        JsonObject fee;
        BigDecimal bigCf;
        BigDecimal bigIcf;
        BigDecimal bigPf;
        String cf;
        String icf;
        String pf;
        int compare;
        for (InstallmentMerchantDto item : installmentMerchants) {
            feeArr = new JsonArray(item.getFee());
            if (feeArr.size() > 0) {
                for (int i = 0; i < feeArr.size(); i++) {
                    fee = feeArr.getJsonObject(i);
                    if (!fee.containsKey("cf") && !fee.containsKey("icf")) continue;
                    pf = String.valueOf(fee.getDouble("pf"));
                    cf = String.valueOf(fee.getDouble("cf"));
                    icf = String.valueOf(fee.getDouble("icf"));
                    if (!checkNumberAfterDot(pf) || !checkNumberAfterDot(cf) || !checkNumberAfterDot(icf)) {
                        logger.info("Phan thap phan > 2 chu so!");
                        logger.info("pf: " + pf);
                        logger.info("icf: " + pf);
                        logger.info("cf: " + pf);
                        return false;
                    }
                    bigPf = new BigDecimal(pf).setScale(2, RoundingMode.HALF_UP);
                    bigCf = new BigDecimal(cf).setScale(2, RoundingMode.HALF_UP);
                    bigIcf = new BigDecimal(icf).setScale(2, RoundingMode.HALF_UP);
                    compare = bigIcf.add(bigCf).compareTo(bigPf);
                    if (!Objects.equals(compare, 0)) {
                        logger.info("pf != icf + cf");
                        logger.info("pf: " + pf);
                        logger.info("icf: " + pf);
                        logger.info("cf: " + pf);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private static boolean checkNumberAfterDot(String value) {
        if (Double.parseDouble(value) < 0) {
            return false;
        }
        String[] numberArr = value.split("\\.");
        if (Objects.equals(numberArr.length, 2) && (Objects.nonNull(numberArr[1]) && numberArr[1].length() > 2)) {
            return false;
        }
        return true;
    }

    /**
     * Bin group add Approval
     *
     * @param ctx
     */
    public static void insertAprovalBinGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
//            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();
            int userId = ctx.get(X_USER_ID);

            MspBinGroupExtendDto binGroupDto = Util.gson.fromJson(body, MspBinGroupExtendDto.class);
            if (binGroupDto == null
                    || binGroupDto.getGroupId() == null
                    || binGroupDto.getGroupName() == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            try {
                // MspBinGroupDto mspBinGroupDto = MspBinRuleConfigDao.getBinRuleById(binGroupDto.getGroupId());
                JsonObject res = new JsonObject();
                Integer result = 0;
                String statusMessage = null;
                // if (!Util.gson.toJson(mspBinGroupDto).equals(Util.gson.toJson(merchantNewDto))) {
                    JsonObject condition = new JsonObject();
                    condition.put("groupId", binGroupDto.getGroupId());
                    ApprovalDto approvalDto = new ApprovalDto();
                    approvalDto.setNewValue(gson.toJson(binGroupDto));
                    approvalDto.setOldValue("");
                    approvalDto.setCondition(condition.encode());
                    approvalDto.setUserRequest(Integer.toString(userId));
                    approvalDto.setDescription("insert new bin group");
                    approvalDto.setField("ALL");
                    approvalDto.setSchema("MSP");
                    approvalDto.setTable("TB_BIN_GROUP");
                    approvalDto.setType("INSERT_BIN_GROUP");
                    result = ApprovalRequestDAO.insertApproval(approvalDto) > 0 ? 200: 500;
                    statusMessage = result > 0 ? "Successful" : "Failed";
                // } else {
                //     result = 200;
                //     statusMessage = "Data Not change";
                // }
                res.put("status", statusMessage);
                sendResponse(ctx,result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Bin group edit Approval
     *
     * @param ctx
     */
    public static void editAprovalBinGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
//            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();
            int userId = ctx.get(X_USER_ID);

            MspBinGroupExtendDto binGroupDto = Util.gson.fromJson(body, MspBinGroupExtendDto.class);
            if (binGroupDto == null
                    || binGroupDto.getGroupId() == null
                    || binGroupDto.getGroupName() == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            try {
                MspBinGroupDto mspBinGroupDto = MspBinRuleConfigDao.getBinRuleById(binGroupDto.getGroupIdOld());
                JsonObject res = new JsonObject();
                Integer result = 0;
                String statusMessage = null;
                if (!binGroupDto.getGroupId().equalsIgnoreCase(mspBinGroupDto.getGroupId()) || !binGroupDto.getBin().equalsIgnoreCase(mspBinGroupDto.getBin())) {
                    JsonObject condition = new JsonObject();
                    condition.put("groupId", binGroupDto.getGroupIdOld());
                    ApprovalDto approvalDto = new ApprovalDto();
                    approvalDto.setNewValue(gson.toJson(binGroupDto));
                    approvalDto.setOldValue(gson.toJson(mspBinGroupDto));
                    approvalDto.setCondition(condition.encode());
                    approvalDto.setUserRequest(Integer.toString(userId));
                    approvalDto.setDescription("update new bin group");
                    approvalDto.setField("ALL");
                    approvalDto.setSchema("MSP");
                    approvalDto.setTable("TB_BIN_GROUP");
                    approvalDto.setType("UPDATE_BIN_GROUP");
                    result = ApprovalRequestDAO.insertApproval(approvalDto) > 0 ? 200: 500;
                    statusMessage = result > 0 ? "Successful" : "Failed";
                } else {
                    result = 200;
                    statusMessage = "Data Not change";
                }
                res.put("status", statusMessage);
                sendResponse(ctx,result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /**
     * DELETE Bin group Approval
     *
     * @param ctx
     */
    public static void deleteRequestBinGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int userId = ctx.get(X_USER_ID);
            String binGroupId = request.getParam(ID);
            try {
                MspBinGroupDto mspBinGroupDto = MspBinRuleConfigDao.getBinRuleById(binGroupId);
                JsonObject condition = new JsonObject();
                condition.put("groupId", binGroupId);
                ApprovalDto approvalDto = new ApprovalDto();
                approvalDto.setNewValue(null);
                approvalDto.setOldValue(gson.toJson(mspBinGroupDto));
                approvalDto.setCondition(condition.encode());
                approvalDto.setUserRequest(Integer.toString(userId));
                approvalDto.setDescription("delete bin group config");
                approvalDto.setField("ALL");
                approvalDto.setSchema("MSP");
                approvalDto.setTable("TB_BIN_GROUP");
                approvalDto.setType("DELETE_BIN_GROUP");
                JsonObject res = new JsonObject();
                Integer result = ApprovalRequestDAO.insertApproval(approvalDto) > 0 ? 200: 500;
                String statusMessage = result > 0 ? "Successful" : "Failed";
                res.put("status", statusMessage);
                sendResponse(ctx, result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * PUT /msp/merchant-approvalchange-state
     *
     * @param ctx
     */
    public static void changeStateRequest(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            int userId = ctx.get(X_USER_ID);
            JsonObject dataReq = ctx.getBodyAsJson();
            if (dataReq == null || !dataReq.containsKey(MERCHANTID) || !dataReq.containsKey(STATE)) {
                throw IErrors.VALIDATION_ERROR;
            }

            try {
                MerchantMspDto merchantOldDto = MerchantMspDao.getMerchantById(dataReq.getString(MERCHANTID));
                JsonObject condition = new JsonObject();
                condition.put("merchantId", dataReq.getString(MERCHANTID));
                condition.put("state", dataReq.getString(STATE));
                ApprovalDto approvalDto = new ApprovalDto();
                approvalDto.setNewValue(null);
                approvalDto.setOldValue(gson.toJson(merchantOldDto));
                approvalDto.setCondition(condition.encode());
                approvalDto.setUserRequest(Integer.toString(userId));
                approvalDto.setDescription("insert new merchant config");
                approvalDto.setField("ALL");
                approvalDto.setSchema("MSP");
                approvalDto.setTable("TB_MERCHANT");
                approvalDto.setType("CHANGE_STATE_MERCHANT_CONFIG");
                JsonObject res = new JsonObject();
                Integer result = ApprovalRequestDAO.insertApproval(approvalDto) > 0 ? 200: 500;
                String statusMessage = result > 0 ? "Successful" : "Failed";
                res.put("status", statusMessage);
                sendResponse(ctx, result, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * POST /msp/merchant-approval-request
     *
     * @param ctx
     */
    public static void checkAproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String merchantId = request.getParam(ID);
            try {
                String result = ApprovalRequestDAO.countApproval(merchantId);
                JsonObject res = new JsonObject();
                res.put("status", result);
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());

            try {
                Integer userId = ctx.get(X_USER_ID);
                logger.info("userId: "+userId);
                List<String> roles = PermissionDao.getListRoleIDbyUserId(userId);
                String roleName = Config.getString("role_merchant.role_id", "");
                if (roles.contains(roleName)) {
                    String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                    if (!merchantsConf.isBlank()) {
                        mIn.put(MERCHANT_ID, merchantsConf);
                    }
                }
                
                // convert to converted DTO
                BaseList<ApprovalDto> approvalDtoBaseList = ApprovalRequestDAO.getMerchantConfigApproval(mIn);
                List<ApprovalConvertDto> convertedList = approvalDtoBaseList.getList().stream().map(dto -> {
                    ApprovalConvertDto convertDto = new ApprovalConvertDto(dto);
                    return convertDto;
                }).collect(Collectors.toList());
                BaseList<ApprovalConvertDto> rs = new BaseList<>();
                rs.setTotalItems(approvalDtoBaseList.getTotalItems());
                rs.setList(convertedList);
                sendResponse(ctx, 200, rs);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ERROR ON get Approval: " , e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approvalReject(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            String action = body.getString("path");
            Integer id = body.getInteger("id");
            String acceptInstrument = body.getString("accept_instrument");
            Integer result = 0;
            int userId = ctx.get(X_USER_ID);
            ApprovalDto approvalDto = new ApprovalDto();
            approvalDto.setId(id);
            approvalDto.setUserConfirm(Integer.toString(userId));
            try {
                if (action.equals("/approve")) {
                    ApprovalDto approval = ApprovalRequestDAO.getApprovalById(id);
                    ApprovalConvertDto approvalConvertDto = new ApprovalConvertDto(approval);
                    if (approvalConvertDto.getType().contains("BIN_GROUP")){
                        String binGroupId = approvalConvertDto.getCondition().get("groupId").toString();
                        Map newValue = approvalConvertDto.getNewValue() != null ? approvalConvertDto.getNewValue() : new HashMap<>();
                        MspBinGroupExtendDto binGroupDto = new MspBinGroupExtendDto();

                        // MspBinGroupExtendDto binGroupDto = Util.gson.fromJson(body, MspBinGroupExtendDto.class);
                        if(approval.getType().contains("INSERT")){
                            if (!newValue.isEmpty()){
                                binGroupDto.setGroupId(newValue.get("groupId").toString());
                                binGroupDto.setGroupName(newValue.get("groupName").toString());
                                binGroupDto.setBin(newValue.get("bin").toString());
                                binGroupDto.setAction(newValue.get("action").toString());
                                MspBinRuleConfigDao.addMspBinGroup(binGroupDto);
                            }
                        } else if(approval.getType().contains("UPDATE")){
                            if (!newValue.isEmpty()){
                                binGroupDto.setGroupIdOld(newValue.get("groupIdOld").toString());
                                binGroupDto.setGroupId(newValue.get("groupId").toString());
                                binGroupDto.setGroupName(newValue.get("groupName").toString());
                                binGroupDto.setBin(newValue.get("bin").toString());
                                binGroupDto.setAction(newValue.get("action").toString());
                                MspBinRuleConfigDao.updateMspBinGroup(binGroupDto);
                            }
                        } else if(approval.getType().contains("DELETE")){
                            MspBinRuleConfigDao.deleteMspBinGroup(binGroupId);
                        }
                        result = ApprovalRequestDAO.approve(approvalDto);
                    } else {
                    String merchantId = approvalConvertDto.getCondition().get("merchantId").toString();
                    String merchantName = approvalConvertDto.getNewValue() != null ? approvalConvertDto.getNewValue().get("merchant_name").toString() : "";
                    String clientMerchants = approvalConvertDto.getNewValue() != null ? approvalConvertDto.getNewValue().get("client_merchants").toString() : "";
                    Integer countMerchantName = MerchantMspDao.checkMerchantByName(merchantId, merchantName);
                    Integer countTransaction = MerchantMspDao.checkTransNotVIETINQR(merchantId);
                    if (approval.getType().contains("INSERT") && countMerchantName > 0 && clientMerchants.contains("client_id=VIETINQR")) {
                        throw new ErrorException(500, "Lỗi:", "Merchant Name này đã tồn tại và được sử dụng dịch vụ Vietin QR tĩnh", "", "Merchant Name này đã tồn tại và được sử dụng dịch vụ Vietin QR tĩnh");
                    }
                    if (approval.getType().contains("INSERT") && countTransaction > 0 && clientMerchants.contains("client_id=VIETINQR")) {
                        throw new ErrorException(500, "Lỗi:", "Không thể lưu do Merchant đã có giao dịch phát sinh", "", "Không thể lưu do Merchant đã có giao dịch phát sinh");
                    }
                    if(approval.getType().contains("INSERT")){
                        MerchantMspDto merchantMspDto = new Gson().fromJson(approval.getNewValue(), MerchantMspDto.class);
                        result = MerchantMspDao.upsertMerchant(merchantMspDto);
                    } else if(approval.getType().contains("DELETE")){
                        result = MerchantMspDao.deleteMerchantById(merchantId);
                    } else if (approval.getType().contains("CHANGE_STATE")) {
                        result = MerchantMspDao.changeStateMerchantById(merchantId, approvalConvertDto.getCondition().get("state").toString());
                    }
                    if (result == 1) {
                        // ghi đè accept instrument của QR nếu có setting qr_version2
                        if (StringUtils.isNotBlank(acceptInstrument)) {
                            ApprovalRequestDAO.overwriteQrAcceptInstrument(acceptInstrument, merchantId);
                        }
                        result = ApprovalRequestDAO.approve(approvalDto);
                    }

                    }
                } else if (action.equals("/reject")) {
                    result = ApprovalRequestDAO.reject(approvalDto);
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject res = new JsonObject();
                int statusCode = result == 200 ? 200 : 500;
                String statusMessage = result == 200 ? "Successful" : "Failed";
                res.put("status", statusMessage);
                sendResponse(ctx, statusCode, res);
            } catch (Exception e) {
                ctx.fail(e);
                throw IErrors.VALIDATION_ERROR;
            }
        }, false, null);
    }

    public static void getAprovalById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam(ID);
            try {
                ApprovalDto result = ApprovalRequestDAO.getApprovalById(Integer.parseInt(id));

                sendResponse(ctx, 200, new ApprovalConvertDto(result));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * PUT /msp/merchant/:id
     *
     * @param ctx
     */
    public static void updateMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();
            String id = request.getParam(ID);
            MerchantMspDto merchantDto = Util.gson.fromJson(body, MerchantMspDto.class);
            if (id == null
                    || merchantDto == null
                    || merchantDto.getMerchant_id() == null
                    || merchantDto.getMerchant_name() == null) {
                throw IErrors.VALIDATION_ERROR;
            }

            try {
                MerchantMspDao.upsertMerchant(merchantDto);
                JsonObject res = new JsonObject();
                res.put("status", "Successful");
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * DELETE /msp/merchant/:id
     *
     * @param ctx
     */
    public static void deleteMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String body = ctx.getBodyAsString();

            String id = request.getParam(ID);
            if (id == null) {
                throw IErrors.VALIDATION_ERROR;
            }

            try {
                MerchantMspDao.deleteMerchantById(id);
                JsonObject res = new JsonObject();
                res.put("status", "Successful");
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static Logger logger = Logger.getLogger(MerchantMspHandler.class.getName());

}
