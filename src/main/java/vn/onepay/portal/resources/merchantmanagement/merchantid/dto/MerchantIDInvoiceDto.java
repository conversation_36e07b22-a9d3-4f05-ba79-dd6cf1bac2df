package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)

public class MerchantIDInvoiceDto {
  private String onecredit_merchant_id;
  private String invoice_merchant_id;
  private String merchant_name;
  private String currency_code;
  private String active;

  public MerchantIDInvoiceDto() {
  }

  public MerchantIDInvoiceDto(String onecredit_merchant_id, String invoice_merchant_id, String merchant_name, String currency_code, String active) {
    this.onecredit_merchant_id = onecredit_merchant_id;
    this.invoice_merchant_id = invoice_merchant_id;
    this.merchant_name = merchant_name;
    this.currency_code = currency_code;
    this.active = active;
  }

  public String getOnecredit_merchant_id() {
    return this.onecredit_merchant_id;
  }

  public void setOnecredit_merchant_id(String onecredit_merchant_id) {
    this.onecredit_merchant_id = onecredit_merchant_id;
  }

  public String getInvoice_merchant_id() {
    return this.invoice_merchant_id;
  }

  public void setInvoice_merchant_id(String invoice_merchant_id) {
    this.invoice_merchant_id = invoice_merchant_id;
  }

  public String getMerchant_name() {
    return this.merchant_name;
  }

  public void setMerchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
  }

  public String getCurrency_code() {
    return this.currency_code;
  }

  public void setCurrency_code(String currency_code) {
    this.currency_code = currency_code;
  }

  public String getActive() {
    return this.active;
  }

  public void setActive(String active) {
    this.active = active;
  }

  public MerchantIDInvoiceDto onecredit_merchant_id(String onecredit_merchant_id) {
    this.onecredit_merchant_id = onecredit_merchant_id;
    return this;
  }

  public MerchantIDInvoiceDto invoice_merchant_id(String invoice_merchant_id) {
    this.invoice_merchant_id = invoice_merchant_id;
    return this;
  }

  public MerchantIDInvoiceDto merchant_name(String merchant_name) {
    this.merchant_name = merchant_name;
    return this;
  }

  public MerchantIDInvoiceDto currency_code(String currency_code) {
    this.currency_code = currency_code;
    return this;
  }

  public MerchantIDInvoiceDto active(String active) {
    this.active = active;
    return this;
  }

  @Override
  public String toString() {
    return "{" +
      " onecredit_merchant_id='" + getOnecredit_merchant_id() + "'" +
      ", invoice_merchant_id='" + getInvoice_merchant_id() + "'" +
      ", merchant_name='" + getMerchant_name() + "'" +
      ", currency_code='" + getCurrency_code() + "'" +
      ", active='" + getActive() + "'" +
      "}";
  }
}
