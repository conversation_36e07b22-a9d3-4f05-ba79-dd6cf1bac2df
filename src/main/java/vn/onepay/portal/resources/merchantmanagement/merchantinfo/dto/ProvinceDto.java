/*
 * Created by IntelliJ IDEA.
 * Author: Duong dai k
 * Date: 3/28/19 9:26 AM
 */

package vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto;

//import lombok.Builder;
//import lombok.Data;
//import lombok.ToString;

//@Data
//@Builder
//@ToString(includeFieldNames = true)
public class ProvinceDto {
    private int id;
    private String tinh;
    private String trungTam;

    public ProvinceDto() {
    }

    public ProvinceDto(int id, String tinh, String trungTam) {
        this.id = id;
        this.tinh = tinh;
        this.trungTam = trungTam;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTinh() {
        return this.tinh;
    }

    public void setTinh(String tinh) {
        this.tinh = tinh;
    }

    public String getTrungTam() {
        return this.trungTam;
    }

    public void setTrungTam(String trungTam) {
        this.trungTam = trungTam;
    }

    public ProvinceDto id(int id) {
        this.id = id;
        return this;
    }

    public ProvinceDto tinh(String tinh) {
        this.tinh = tinh;
        return this;
    }

    public ProvinceDto trungTam(String trungTam) {
        this.trungTam = trungTam;
        return this;
    }

    @Override
    public String toString() {
        return "{" + " id='" + getId() + "'" + ", tinh='" + getTinh() + "'" + ", trungTam='" + getTrungTam() + "'"
                + "}";
    }
}
