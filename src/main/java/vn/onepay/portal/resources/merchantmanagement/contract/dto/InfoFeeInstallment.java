package vn.onepay.portal.resources.merchantmanagement.contract.dto;

public class InfoFeeInstallment {

    private int id;
    private int order;
    private String bankName;
    private String shortName;
    private String threeMonths;
    private String sixMonths;
    private String nineMonths;
    private String twelveMonths;
    private String fifteenMonths;
    private String eighteenMonths;
    private String twentyFourMonths;
    private String thirtySixMonths;
    private int state;
    private int feeType;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getThreeMonths() {
        return threeMonths;
    }

    public void setThreeMonths(String threeMonths) {
        this.threeMonths = threeMonths;
    }

    public String getSixMonths() {
        return sixMonths;
    }

    public void setSixMonths(String sixMonths) {
        this.sixMonths = sixMonths;
    }

    public String getNineMonths() {
        return nineMonths;
    }

    public void setNineMonths(String nineMonths) {
        this.nineMonths = nineMonths;
    }

    public String getTwelveMonths() {
        return twelveMonths;
    }

    public void setTwelveMonths(String twelveMonths) {
        this.twelveMonths = twelveMonths;
    }

    public String getFifteenMonths() {
        return fifteenMonths;
    }

    public void setFifteenMonths(String fifteenMonths) {
        this.fifteenMonths = fifteenMonths;
    }

    public String getEighteenMonths() {
        return eighteenMonths;
    }

    public void setEighteenMonths(String eighteenMonths) {
        this.eighteenMonths = eighteenMonths;
    }

    public String getTwentyFourMonths() {
        return twentyFourMonths;
    }

    public void setTwentyFourMonths(String twentyFourMonths) {
        this.twentyFourMonths = twentyFourMonths;
    }

    public String getThirtySixMonths() {
        return thirtySixMonths;
    }

    public void setThirtySixMonths(String thirtySixMonths) {
        this.thirtySixMonths = thirtySixMonths;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getFeeType() {
        return feeType;
    }

    public void setFeeType(int feeType) {
        this.feeType = feeType;
    }
    public InfoFeeInstallment(int id, int order, String bankName, String shortName, String threeMonths,
            String sixMonths, String nineMonths, String twelveMonths, String fifteenMonths, String eighteenMonths, String twentyFourMonths,
            String thirtySixMonths,
            int state, int feeType) {
        this.id = id;
        this.order = order;
        this.bankName = bankName;
        this.shortName = shortName;
        this.threeMonths = threeMonths;
        this.sixMonths = sixMonths;
        this.nineMonths = nineMonths;
        this.twelveMonths = twelveMonths;
        this.fifteenMonths = fifteenMonths;
        this.eighteenMonths = eighteenMonths;
        this.twentyFourMonths = twentyFourMonths;
        this.thirtySixMonths = thirtySixMonths;
        this.state = state;
        this.feeType = feeType;
    }

}
