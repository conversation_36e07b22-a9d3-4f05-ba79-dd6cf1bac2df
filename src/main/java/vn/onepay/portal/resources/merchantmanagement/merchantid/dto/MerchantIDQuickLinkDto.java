package vn.onepay.portal.resources.merchantmanagement.merchantid.dto;

public class MerchantIDQuickLinkDto {
  private String merchantId;
  private String merchantName;
  private String paymentMethod;
  private String active;
  private String merchantProfileName;

  public MerchantIDQuickLinkDto() {
  }

  public MerchantIDQuickLinkDto(String merchantId, String merchantName, String paymentMethod, String active,
      String merchantProfileName) {
    this.merchantId = merchantId;
    this.merchantName = merchantName;
    this.paymentMethod = paymentMethod;
    this.active = active;
    this.merchantProfileName = merchantProfileName;
  }

  public String getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(String merchantId) {
    this.merchantId = merchantId;
  }

  public String getMerchantName() {
    return merchantName;
  }

  public void setMerchantName(String merchantName) {
    this.merchantName = merchantName;
  }

  public String getPaymentMethod() {
    return paymentMethod;
  }

  public void setPaymentMethod(String paymentMethod) {
    this.paymentMethod = paymentMethod;
  }

  public String getActive() {
    return active;
  }

  public void setActive(String active) {
    this.active = active;
  }

  public String getMerchantProfileName() {
    return merchantProfileName;
  }

  public void setMerchantProfileName(String merchantProfileName) {
    this.merchantProfileName = merchantProfileName;
  }

  public MerchantIDQuickLinkDto merchantId(String merchantId) {
    this.merchantId = merchantId;
    return this;
  }

  public MerchantIDQuickLinkDto merchantName(String merchantName) {
    this.merchantName = merchantName;
    return this;
  }

  public MerchantIDQuickLinkDto paymentMethod(String paymentMethod) {
    this.paymentMethod = paymentMethod;
    return this;
  }

  public MerchantIDQuickLinkDto active(String active) {
    this.active = active;
    return this;
  }

  public MerchantIDQuickLinkDto merchantProfileName(String merchantProfileName) {
    this.merchantProfileName = merchantProfileName;
    return this;
  }

  // @Override
  // public String toString() {
  // return "{" +
  // " onecredit_merchant_id='" + getOnecredit_merchant_id() + "'" +
  // ", invoice_merchant_id='" + getInvoice_merchant_id() + "'" +
  // ", merchant_name='" + getMerchant_name() + "'" +
  // ", currency_code='" + getCurrency_code() + "'" +
  // ", active='" + getActive() + "'" +
  // "}";
  // }
}
