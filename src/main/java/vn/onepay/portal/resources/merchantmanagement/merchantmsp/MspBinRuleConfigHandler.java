package vn.onepay.portal.resources.merchantmanagement.merchantmsp;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.MspBinGroupExtendDto;

import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MspBinRuleConfigHandler implements IConstants {

    public static void getRuleGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MspBinRuleConfigDao.getAllRuleGroupList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addEditRuleGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            String body = ctx.getBodyAsString();

            MspBinGroupExtendDto binGroupDto = Util.gson.fromJson(body, MspBinGroupExtendDto.class);
            if (binGroupDto == null
                    || binGroupDto.getGroupId() == null
                    || binGroupDto.getGroupName() == null) {
                throw IErrors.VALIDATION_ERROR;
            }
            int check = MspBinRuleConfigDao.validateDublicate(binGroupDto.getGroupId());

            try {
                if("addNew".equalsIgnoreCase(binGroupDto.getAction())){
                    if(check > 0){
                        throw IErrors.DUPLICATE;
                    }
                    // MspBinRuleConfigDao.addMspBinGroup(binGroupDto);
                    MerchantMspHandler.insertAprovalBinGroup(ctx);
                } else if ("edit".equalsIgnoreCase(binGroupDto.getAction())) {
                    if(check > 0 && !binGroupDto.getGroupId().equals(binGroupDto.getGroupIdOld())){
                        throw IErrors.DUPLICATE;
                    }
                    // MspBinRuleConfigDao.updateMspBinGroup(binGroupDto);
                    MerchantMspHandler.editAprovalBinGroup(ctx);
                }
                    JsonObject res = new JsonObject();
                    res.put("status", "Successful");
                    sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteRuleGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
                HttpServerRequest request = ctx.request();
                String groupId = request.getParam(ID) == null ? BLANK : request.getParam(ID);
            int checkUsingAvailable = MspBinRuleConfigDao.checkUseBinGroup(groupId);
            try {
                if (checkUsingAvailable > 0){
                    throw IErrors.VALIDATION_ERROR;
                }
                // MspBinRuleConfigDao.deleteMspBinGroup(groupId);
                MerchantMspHandler.deleteRequestBinGroup(ctx);
                JsonObject res = new JsonObject();
                res.put("status", "Successful");
                sendResponse(ctx, 200, res);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBinList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MspBinRuleConfigDao.getBinList());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static Logger logger = Logger.getLogger(InstallmentMspHandler.class.getName());

}
