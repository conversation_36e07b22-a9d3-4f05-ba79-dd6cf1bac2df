package vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto;

import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import vn.onepay.portal.IConstants;

public class IpnDto {
    private static Logger logger = Logger.getLogger(IpnDto.class.getName());

    private String rowNum;
    private String merchantId;
    private String merchantName;
    private String service;
    private String ndAuth;
    private String state;
    private String desc;
    private String createDate;
    private String updateDate;
    private String configType;
    private String config;
    private String transactionType;
    private boolean isPending;

    public IpnDto() {
        this.merchantId = "";
        this.merchantName = "";
        this.service = "";
        this.ndAuth = "";
        this.state = "";
        this.desc = "";
        this.createDate = "";
        this.updateDate = "";
        this.configType = "";
        this.config = "";
    }

    public IpnDto(IpnDto ipn) {
        rowNum = ipn.rowNum;
        merchantId = ipn.merchantId;
        merchantName = ipn.merchantName;
        service = ipn.service;
        state = ipn.state;
        desc = ipn.desc;
        createDate = ipn.createDate;
        updateDate = ipn.updateDate;
        configType = ipn.configType;
        config = ipn.config;
    }

    public String getRowNum() {
        return rowNum;
    }

    public void setRowNum(String rowNum) {
        this.rowNum = rowNum;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getNdAuth() {
        return ndAuth;
    }

    public void setNdAuth(String ndAuth) {
        this.ndAuth = ndAuth;
    }

    public String getTransactionType() {
        return this.transactionType;
    }

    public void setTransactionType(String config) {
        String transacRegex = "";
        List<String> list = new ArrayList<String>();
        try {
            if ("Advanced".equals(this.configType)) {
                JsonParser jsonParser = new JsonParser();
                JsonObject configJson = jsonParser.parse(config.substring(1, config.length() - 1)).getAsJsonObject();
                String filter = configJson.get("filter").getAsString();
                String regexpString = "\\(([a-z|]+)\\)";
                Pattern pattern = Pattern.compile(regexpString);
                Matcher matcher = pattern.matcher(filter);
                while (matcher.find()) {
                    transacRegex = matcher.group();
                    transacRegex = transacRegex.substring(1, transacRegex.length() - 1);
                }
            } else {
                transacRegex = getValueEelementConfig(config,IConstants.INDEX_STATES);
            }
            
            String[] arrOfStr = transacRegex.split("\\|");
            if (arrOfStr.length > 1 ) {
                this.transactionType = "All";
            } else {
                this.transactionType = "Successfull";
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
        }
    }

    private static String getValueEelementConfig(String config, int index){
        String[] element = config.split(" ");
        if (element.length == 1 && index == 1) {
            return element[0];
        } else if (element.length == 1 && index != 1) {
            return "";
        } else if (element.length >= index + 1){
            return element[index];
        }
        return "";
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getConfigInsert() {
        return this.config;
    }

    public boolean isPending() {
        return isPending;
    }

    public void setPending(boolean isPending) {
        this.isPending = isPending;
    }

    public IpnDto(IpnSimpleDto simple) {
        this.merchantId = simple.getMerchantId();
        this.merchantName = simple.getMerchantName();
        this.service = simple.getService();
        this.ndAuth = simple.getNdAuth();
        this.state = simple.getState();
        this.desc = simple.getDesc();
        this.createDate = simple.getCreateDate();
        this.updateDate = simple.getUpdateDate();
        this.configType = simple.getConfigType();
        this.config = simple.getConfigInsert();
    }

    public IpnDto(IpnAdvancedDto advanced) {
        this.merchantId = advanced.getMerchantId();
        this.merchantName = advanced.getMerchantName();
        this.service = advanced.getService();
        this.ndAuth = advanced.getNdAuth();
        this.state = advanced.getState();
        this.desc = advanced.getDesc();
        this.createDate = advanced.getCreateDate();
        this.updateDate = advanced.getUpdateDate();
        this.configType = advanced.getConfigType();
        this.config = advanced.getConfig();
    }

    public Date getSqlDate(String value) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy hh:mm aa");
        java.util.Date date = sdf.parse(value); 
        java.sql.Date sqlDate = new Date(date.getTime());
        return sqlDate;
    }
}
