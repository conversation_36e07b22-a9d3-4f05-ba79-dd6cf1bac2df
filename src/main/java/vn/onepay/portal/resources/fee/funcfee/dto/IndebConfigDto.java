package vn.onepay.portal.resources.fee.funcfee.dto;

import java.util.List;

public class IndebConfigDto {
    private String contractCode;
    private String contractType;
    private String merchantName;
    private String merchantId;
    private String paygate;
    private String midId;
    private String categoryCode;
    private List<FeeDetailDto> listFeeDetail;
    private List<FeeViewDto> feeViewDtoList;

    public List<FeeViewDto> getFeeViewDtoList() {
        return feeViewDtoList;
    }

    public void setFeeViewDtoList(List<FeeViewDto> feeViewDtoList) {
        this.feeViewDtoList = feeViewDtoList;
    }

    public List<FeeDetailDto> getListFeeDetail() {
        return listFeeDetail;
    }

    public void setListFeeDetail(List<FeeDetailDto> listFeeDetail) {
        this.listFeeDetail = listFeeDetail;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getMidId() {
        return midId;
    }

    public void setMidId(String midId) {
        this.midId = midId;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }
}
