package vn.onepay.portal.resources.fee.config.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.fee.config.dao.PartnerFeeConfigDAO;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class PartnerFeeConfigHandler implements IConstants {
    private PartnerFeeConfigHandler() {

    }

    private static Logger logger = Logger.getLogger(PartnerFeeConfigHandler.class.getName());

    public static void getDetailPartnerFeeConfigById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer partnerFeeConfigId = Integer.valueOf(ctx.request().getParam("partnerFeeConfigId"));
                sendResponse(ctx, 200, PartnerFeeConfigDAO.getDetailPartnerFeeConfigById(partnerFeeConfigId));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListPartnerFeeConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("partnerName", Util.handleHttpRequest(request, "partnerName", "").toString());
                mIn.put("partnerType", Util.handleHttpRequest(request, "partnerType", "").toString());
                mIn.put("partnerContractType", Util.handleHttpRequest(request, "partnerContractType", "").toString());
                mIn.put("partnerService", Util.handleHttpRequest(request, "partnerService", "").toString());
                mIn.put(PAGE, Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGESIZE, Integer.parseInt(Util.handleHttpRequest(request, PAGESIZE, "25").toString()));
                sendResponse(ctx, 200, PartnerFeeConfigDAO.getListPartnerFeeConfig(mIn));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantFee ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListPartnerFeeServiceByPartnerFeeConfigId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerFeeConfigId = Integer.parseInt(request.getParam("partnerFeeConfigId"));
                sendResponse(ctx, 200, PartnerFeeConfigDAO.getListPartnerFeeServiceByPartnerFeeConfigId(partnerFeeConfigId));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantFee ]  => ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
