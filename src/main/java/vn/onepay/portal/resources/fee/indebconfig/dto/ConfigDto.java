package vn.onepay.portal.resources.fee.indebconfig.dto;

import java.sql.Timestamp;
import java.util.Map;

public class ConfigDto {
    private Long id;
    private String partnerName;
    private String shortName;
    private String lastMerchantName;
    private String tenDkkd;
    private String contractCode;
    private Long contractDate;
    private Long effectiveDate;
    private String contractType;
    private String service;
    private String paymentPeriod;
    private String hourPayment;
    private String advanceBank;
    private String branchName;
    private String advanceAccount;
    private String merchantId;
    private String state;
    private String effectiveState;
    private Integer parentId;
    private String taxCode;
    private int action;
    private Integer cutOffTime;
    private Double advancePercent;
    private String dayOfWeek;
    private String dayOfMonth;
    private String advanceAmount;
    private String bank;
    private String accountName;
    private String formula;

    /**
     * @deprecated bỏ đi vì trùng với người được ủy quyền
     */
    @Deprecated(since="3.0", forRemoval=true)
    private String accountType;
    /**
     * @deprecated fd đang khoanh giữ ra 1 chỗ riêng và không có số tài khoản
     */
    @Deprecated(since="3.0", forRemoval=true)
    private String accountNumber;
    private double guaranteeAmount;
    private String guaranteeHoldingType;
    private Integer partnerId;
    private Integer flowType;
    private Integer templateId;
    private String templateName;
    private Timestamp fromDate;
    private Timestamp toDate;
    private Timestamp createdDate;
    private Timestamp updateDate;
    private String notes;
    private Double minAdvanceAmount;
    private String conditions;
    private String advanceVCB;
    private String advanceTransFailed;
    private Integer checkEnoughGuarantee;
    private String accountingEntry;
    private Integer templateFeeMonth;
    private String separateType;
    private String attachFile;
    private String status;
    /**
     * @deprecated bỏ đi vì nhận diện gd trả góp tự động
     */
    @Deprecated(since="3.0", forRemoval=true)
    private boolean itaType;
    private String emailFeeMonth;
    private boolean dividePCRF;
    private String authorizedPerson;
    private Long authorizedExpirationDate;
    private Integer parentConfig;
    private boolean grabPromotion;
    private boolean autoHold;
    private int cutoffFeeMonth;

    private String paymentPeriodND;
    private String paymentPeriodQR;
    private String paymentPeriodBNPL;
    private String paymentPeriodUPOS;
    private String paymentPeriodPayCollect;
    private String paymentPeriodPayOut;
    private String paymentPeriodVietQR;
    private String paymentPeriodDD;
    private String objectFee;

    private String cashFlow;
    private String invoiceType;
    private String invoiceInterval;
    private Map<String, Object> advanceAccountPayout;

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceInterval() {
        return invoiceInterval;
    }

    public void setInvoiceInterval(String invoiceInterval) {
        this.invoiceInterval = invoiceInterval;
    }

    public String getCashFlow() {
        return cashFlow;
    }

    public void setCashFlow(String cashFlow) {
        this.cashFlow = cashFlow;
    }

    public Long getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Long expirationDate) {
        this.expirationDate = expirationDate;
    }

    private Long expirationDate;



    public String getObjectFee() {
        return objectFee;
    }

    public void setObjectFee(String objectFee) {
        this.objectFee = objectFee;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public boolean isAutoHold() {
        return autoHold;
    }

    public void setAutoHold(boolean autoHold) {
        this.autoHold = autoHold;
    }

    public String getPaymentPeriodPayCollect() {
        return paymentPeriodPayCollect;
    }

    public void setPaymentPeriodPayCollect(String paymentPeriodPayCollect) {
        this.paymentPeriodPayCollect = paymentPeriodPayCollect;
    }

    public String getHourPaymentPayCollect() {
        return hourPaymentPayCollect;
    }

    public void setHourPaymentPayCollect(String hourPaymentPayCollect) {
        this.hourPaymentPayCollect = hourPaymentPayCollect;
    }

    public Integer getCutOffTimePayCollect() {
        return cutOffTimePayCollect;
    }

    public void setCutOffTimePayCollect(Integer cutOffTimePayCollect) {
        this.cutOffTimePayCollect = cutOffTimePayCollect;
    }

    private String hourPaymentND;
    private String hourPaymentQR;
    private String hourPaymentBNPL;
    private String hourPaymentUPOS;
    private String hourPaymentPayCollect;
    private String hourPaymentPayOut;
    private String hourPaymentVietQR;
    private String hourPaymentDD;

    private Integer cutOffTimeND;
    private Integer cutOffTimeQR;
    private Integer cutOffTimeBNPL;
    private Integer cutOffTimeUPOS;
    private Integer cutOffTimePayCollect;
    private Integer cutOffTimePayOut;
    private Integer cutOffTimeVietQR;
    private Integer cutOffTimeDD;



    private boolean advTranFailQT;
    private Integer functionCreateAdv;
    private String functionCreateAdvParam;
    private String functionCreateAdvDesc;
    private Integer functionCreateFeeMonth;
    private String functionCreateFeeMonthParam;
    private String lbfunctionCreateFeeMonthDesc;

    private Integer holdDay;

    private String onepayBankId;
    private String jQT;
    private String jGuaranteeTimeHold;
    private String jND;
    private String jQR;
    private String jSMS;
    private Map<String, Object> jAdvanceAccount;
    private String AdvanceTime;
    private String jMonthlyFee;
    private String jHold;
    private String jDataStr;
    private long idFeeMonth;
    private String transactionType;
    private String notifyMethod;
    private String receiptType;
    private String mergeMonthlyReport;
    private String addendum;
    private String controlMinutes;
    
    private String insertAdvTxnFuncQT;
    private String getAdvTxnFuncQT;
    private String insertAdvTxnFuncND;
    private String getAdvTxnFuncND;
    private String insertAdvTxnFuncQR;
    private String getAdvTxnFuncQR;
    private String insertAdvTxnFuncBNPL;
    private String insertAdvTxnFuncUPOS;
    private String insertAdvTxnFuncPayCollect;
    private String insertAdvTxnFuncPayOut;
    private String insertAdvTxnFuncVietQR;
    private String insertAdvTxnFuncDD;

    private String getAdvTxnFuncBNPL;
    private String getAdvTxnFuncUPOS;
    private String getAdvTxnFuncPayCollect;
    private String getAdvTxnFuncPayOut;
    private String getAdvTxnFuncVietQR;
    private String getAdvTxnFuncDD;

    private String insertAdvTxnFuncSMS;
    private String getAdvTxnFuncSMS;
    private String insertHoldFdFunc;
    private String insertHoldFunc;

    private ConfigDto sourceConfig;

    private String trantypeConfig;

    private String jBilling;
    private String paymentPeriodBilling;
    private Integer cutOffTimeBilling;
    private String insertAdvTxnFuncBilling;
    private String getAdvTxnFuncBilling;
    private String jBNPL;
    private String jUPOS;
    private String jPayCollect;
    private String jPayOut;
    private String jVietQR;
    private String jDD;
    private String jNotification;
    private String file;
    private String zip;
    private String autoSendMail;
    private String authorzedPerson;
    private String jData;
    private String uposType;

    // Auto configuration properties
    private Integer isAutoEnabled;           // '0' or '1'
    private String autoDays;                // "MON,TUE,SAT,SUN"
    private String autoStepLevel;           // "CHECK_ONLY", "ADVANCE", "STATEMENT", "FULL"
    private String emailNotifyPayment;      // "<EMAIL>,<EMAIL>"
    private String emailNotifyAccount;      // "<EMAIL>,<EMAIL>"
    private String emailNotifyBod;          // "<EMAIL>,<EMAIL>"

    public String getGetAdvTxnFuncVietQR() {
        return getAdvTxnFuncVietQR;
    }

    public void setGetAdvTxnFuncVietQR(String getAdvTxnFuncVietQR) {
        this.getAdvTxnFuncVietQR = getAdvTxnFuncVietQR;
    }

    public String getInsertAdvTxnFuncVietQR() {
        return insertAdvTxnFuncVietQR;
    }

    public void setInsertAdvTxnFuncVietQR(String insertAdvTxnFuncVietQR) {
        this.insertAdvTxnFuncVietQR = insertAdvTxnFuncVietQR;
    }

    public String getPaymentPeriodVietQR() {
        return paymentPeriodVietQR;
    }

    public void setPaymentPeriodVietQR(String paymentPeriodVietQR) {
        this.paymentPeriodVietQR = paymentPeriodVietQR;
    }

    public String getHourPaymentVietQR() {
        return hourPaymentVietQR;
    }

    public void setHourPaymentVietQR(String hourPaymentVietQR) {
        this.hourPaymentVietQR = hourPaymentVietQR;
    }

    public Integer getCutOffTimeVietQR() {
        return cutOffTimeVietQR;
    }

    public void setCutOffTimeVietQR(Integer cutOffTimeVietQR) {
        this.cutOffTimeVietQR = cutOffTimeVietQR;
    }

    public String getjVietQR() {
        return jVietQR;
    }

    public void setjVietQR(String jVietQR) {
        this.jVietQR = jVietQR;
    }

    public String getjDataStr() {
        return jDataStr;
    }

    public void setjDataStr(String jDataStr) {
        this.jDataStr = jDataStr;
    }

    public String getjData() {
        return jData;
    }

    public String getInsertAdvTxnFuncPayCollect() {
        return insertAdvTxnFuncPayCollect;
    }

    public void setInsertAdvTxnFuncPayCollect(String insertAdvTxnFuncPayCollect) {
        this.insertAdvTxnFuncPayCollect = insertAdvTxnFuncPayCollect;
    }

    public String getGetAdvTxnFuncPayCollect() {
        return getAdvTxnFuncPayCollect;
    }

    public void setGetAdvTxnFuncPayCollect(String getAdvTxnFuncPayCollect) {
        this.getAdvTxnFuncPayCollect = getAdvTxnFuncPayCollect;
    }

    public void setjData(String jData) {
        this.jData = jData;
    }

    public String getAuthorzedPerson() {
        return authorzedPerson;
    }

    public void setAuthorzedPerson(String authorzedPerson) {
        this.authorzedPerson = authorzedPerson;
    }

    public String getAutoSendMail() {
        return autoSendMail;
    }

    public void setAutoSendMail(String autoSendMail) {
        this.autoSendMail = autoSendMail;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getZip() {
        return zip;
    }

    public String getjPayCollect() {
        return jPayCollect;
    }

    public void setjPayCollect(String jPayCollect) {
        this.jPayCollect = jPayCollect;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getjNotification() {
        return jNotification;
    }

    public void setjNotification(String jNotification) {
        this.jNotification = jNotification;
    }

    public int getCutoffFeeMonth() {
        return this.cutoffFeeMonth;
    }

    public void setCutoffFeeMonth(int cutoffFeeMonth) {
        this.cutoffFeeMonth = cutoffFeeMonth;
    }

    public String getOnepayBankId() {
        return this.onepayBankId;
    }

    public void setOnepayBankId(String onepayBankId) {
        this.onepayBankId = onepayBankId;
    }

    public boolean isGrabPromotion() {
        return this.grabPromotion;
    }

    public void setGrabPromotion(boolean grabPromotion) {
        this.grabPromotion = grabPromotion;
    }

    public Integer getParentConfig() {
        return this.parentConfig;
    }

    public void setParentConfig(Integer parentConfig) {
        this.parentConfig = parentConfig;
    }

    public String getAuthorizedPerson() {
        return this.authorizedPerson;
    }

    public void setAuthorizedPerson(String authorizedPerson) {
        this.authorizedPerson = authorizedPerson;
    }

    public Long getAuthorizedExpirationDate() {
        return this.authorizedExpirationDate;
    }

    public void setAuthorizedExpirationDate(Long authorizedExpirationDate) {
        this.authorizedExpirationDate = authorizedExpirationDate;
    }

    public boolean isDividePCRF() {
        return this.dividePCRF;
    }

    public void setDividePCRF(boolean dividePCRF) {
        this.dividePCRF = dividePCRF;
    }

    public String getEmailFeeMonth() {
        return this.emailFeeMonth;
    }

    public void setEmailFeeMonth(String emailFeeMonth) {
        this.emailFeeMonth = emailFeeMonth;
    }

    /**
     * @deprecated bỏ đi vì nhận diện gd trả góp tự động
     */
    @Deprecated(since="3.0", forRemoval=true)
    public boolean getItaType() {
        return this.itaType;
    }

    /**
     * @deprecated bỏ đi vì nhận diện gd trả góp tự động
     */
    @Deprecated(since="3.0", forRemoval=true)
    public void setItaType(boolean itaType) {
        this.itaType = itaType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getAttachFile() {
        return attachFile;
    }

    public void setAttachFile(String attachFile) {
        this.attachFile = attachFile;
    }

    public String getEffectiveState() {
        return effectiveState;
    }

    public void setEffectiveState(String effectiveState) {
        this.effectiveState = effectiveState;
    }

    public String getSeparateType() {
        return separateType;
    }

    public void setSeparateType(String separateType) {
        this.separateType = separateType;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getFlowType() {
        return flowType;
    }

    public void setFlowType(Integer flowType) {
        this.flowType = flowType;
    }

    /**
     * @deprecated bỏ đi vì trùng với người được ủy quyền
     */
    @Deprecated(since="3.0", forRemoval=true)
    public String getAccountType() {
        return accountType;
    }

    /**
     * @deprecated bỏ đi vì trùng với người được ủy quyền
     */
    @Deprecated(since="3.0", forRemoval=true)
    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public Integer getCutOffTime() {
        return cutOffTime;
    }

    public void setCutOffTime(Integer cutOffTime) {
        this.cutOffTime = cutOffTime;
    }

    public Double getAdvancePercent() {
        return advancePercent;
    }

    public void setAdvancePercent(Double advancePercent) {
        this.advancePercent = advancePercent;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(String dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public String getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    /**
     * @deprecated fd đang khoanh giữ ra 1 chỗ riêng và không có số tài khoản
     */
    @Deprecated(since="3.0", forRemoval=true)
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * @deprecated fd đang khoanh giữ ra 1 chỗ riêng và không có số tài khoản
     */
    @Deprecated(since="3.0", forRemoval=true)
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }


    public double getGuaranteeAmount() {
        return this.guaranteeAmount;
    }

    public void setGuaranteeAmount(double guaranteeAmount) {
        this.guaranteeAmount = guaranteeAmount;
    }
    

    public String getGuaranteeHoldingType() {
        return guaranteeHoldingType;
    }

    public void setGuaranteeHoldingType(String guaranteeHoldingType) {
        this.guaranteeHoldingType = guaranteeHoldingType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getLastMerchantName() {
        return lastMerchantName;
    }

    public void setLastMerchantName(String lastMerchantName) {
        this.lastMerchantName = lastMerchantName;
    }

    public String getTenDkkd() {
        return tenDkkd;
    }

    public void setTenDkkd(String tenDkkd) {
        this.tenDkkd = tenDkkd;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getPaymentPeriod() {
        return paymentPeriod;
    }

    public void setPaymentPeriod(String paymentPeriod) {
        this.paymentPeriod = paymentPeriod;
    }

    public String getHourPayment() {
        return hourPayment;
    }

    public void setHourPayment(String hourPayment) {
        this.hourPayment = hourPayment;
    }

    public String getAdvanceBank() {
        return advanceBank;
    }

    public void setAdvanceBank(String advanceBank) {
        this.advanceBank = advanceBank;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getAdvanceAccount() {
        return advanceAccount;
    }

    public void setAdvanceAccount(String advanceAccount) {
        this.advanceAccount = advanceAccount;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Timestamp getFromDate() {
        return fromDate;
    }

    public void setFromDate(Timestamp fromDate) {
        this.fromDate = fromDate;
    }

    public Timestamp getToDate() {
        return toDate;
    }

    public void setToDate(Timestamp toDate) {
        this.toDate = toDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getConditions() {
        return conditions;
    }

    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    public Integer getCheckEnoughGuarantee() {
        return checkEnoughGuarantee;
    }

    public void setCheckEnoughGuarantee(Integer checkEnoughGuarantee) {
        this.checkEnoughGuarantee = checkEnoughGuarantee;
    }

    public String getAccountingEntry() {
        return accountingEntry;
    }

    public void setAccountingEntry(String accountingEntry) {
        this.accountingEntry = accountingEntry;
    }

    public Double getMinAdvanceAmount() {
        return minAdvanceAmount;
    }

    public void setMinAdvanceAmount(Double minAdvanceAmount) {
        this.minAdvanceAmount = minAdvanceAmount;
    }

    public String getAdvanceVCB() {
        return advanceVCB;
    }

    public void setAdvanceVCB(String advanceVCB) {
        this.advanceVCB = advanceVCB;
    }

    public String getAdvanceTransFailed() {
        return advanceTransFailed;
    }

    public void setAdvanceTransFailed(String advanceTransFailed) {
        this.advanceTransFailed = advanceTransFailed;
    }

    public Integer getTemplateFeeMonth() {
        return templateFeeMonth;
    }

    public void setTemplateFeeMonth(Integer templateFeeMonth) {
        this.templateFeeMonth = templateFeeMonth;
    }


    public Long getContractDate() {
        return this.contractDate;
    }

    public void setContractDate(Long contractDate) {
        this.contractDate = contractDate;
    }

    public Long getEffectiveDate() {
        return this.effectiveDate;
    }

    public void setEffectiveDate(Long effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getPaymentPeriodDD() {
        return paymentPeriodDD;
    }

    public void setPaymentPeriodDD(String paymentPeriodDD) {
        this.paymentPeriodDD = paymentPeriodDD;
    }

    public String getHourPaymentDD() {
        return hourPaymentDD;
    }

    public void setHourPaymentDD(String hourPaymentDD) {
        this.hourPaymentDD = hourPaymentDD;
    }

    public Integer getCutOffTimeDD() {
        return cutOffTimeDD;
    }

    public void setCutOffTimeDD(Integer cutOffTimeDD) {
        this.cutOffTimeDD = cutOffTimeDD;
    }

    public String getInsertAdvTxnFuncDD() {
        return insertAdvTxnFuncDD;
    }

    public void setInsertAdvTxnFuncDD(String insertAdvTxnFuncDD) {
        this.insertAdvTxnFuncDD = insertAdvTxnFuncDD;
    }

    public String getGetAdvTxnFuncDD() {
        return getAdvTxnFuncDD;
    }

    public void setGetAdvTxnFuncDD(String getAdvTxnFuncDD) {
        this.getAdvTxnFuncDD = getAdvTxnFuncDD;
    }

    public String getjDD() {
        return jDD;
    }

    public void setjDD(String jDD) {
        this.jDD = jDD;
    }

    /**
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(String status) {
        this.status = status;
    }



    //auto gen

    public boolean isItaType() {
        return this.itaType;
    }

    public boolean getDividePCRF() {
        return this.dividePCRF;
    }


    public boolean getGrabPromotion() {
        return this.grabPromotion;
    }


    public String getJQT() {
        return this.jQT;
    }

    public void setJQT(String jQT) {
        this.jQT = jQT;
    }

    public String getJGuaranteeTimeHold() {
        return this.jGuaranteeTimeHold;
    }

    public void setJGuaranteeTimeHold(String jGuaranteeTimeHold) {
        this.jGuaranteeTimeHold = jGuaranteeTimeHold;
    }

    public String getJND() {
        return this.jND;
    }

    public void setJND(String jND) {
        this.jND = jND;
    }

    public String getJQR() {
        return this.jQR;
    }

    public void setJQR(String jQR) {
        this.jQR = jQR;
    }

    public String getJSMS() {
        return this.jSMS;
    }

    public void setJSMS(String jSMS) {
        this.jSMS = jSMS;
    }

    public Map<String, Object> getJAdvanceAccount() {
        return this.jAdvanceAccount;
    }

    public void setJAdvanceAccount(Map<String, Object> jAdvanceAccount) {
        this.jAdvanceAccount = jAdvanceAccount;
    }

    public String getAdvanceTime() {
        return this.AdvanceTime;
    }

    public void setAdvanceTime(String AdvanceTime) {
        this.AdvanceTime = AdvanceTime;
    }

    public String getJMonthlyFee() {
        return this.jMonthlyFee;
    }

    public void setJMonthlyFee(String jMonthlyFee) {
        this.jMonthlyFee = jMonthlyFee;
    }

    public String getJHold() {
        return this.jHold;
    }

    public void setJHold(String jHold) {
        this.jHold = jHold;
    }


    public long getIdFeeMonth() {
        return this.idFeeMonth;
    }

    public void setIdFeeMonth(long idFeeMonth) {
        this.idFeeMonth = idFeeMonth;
    }
    

    public String getTransactionType() {
        return this.transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getNotifyMethod() {
        return this.notifyMethod;
    }

    public void setNotifyMethod(String notifyMethod) {
        this.notifyMethod = notifyMethod;
    }


//auto gen 2

    public String getPaymentPeriodND() {
        return this.paymentPeriodND;
    }

    public void setPaymentPeriodND(String paymentPeriodND) {
        this.paymentPeriodND = paymentPeriodND;
    }

    public String getPaymentPeriodQR() {
        return this.paymentPeriodQR;
    }

    public void setPaymentPeriodQR(String paymentPeriodQR) {
        this.paymentPeriodQR = paymentPeriodQR;
    }
    public String getPaymentPeriodBNPL() {
        return this.paymentPeriodBNPL;
    }
    public void setPaymentPeriodBNPL(String paymentPeriodBNPL) {
        this.paymentPeriodBNPL = paymentPeriodBNPL;
    }
    public String getPaymentPeriodUPOS() {
        return this.paymentPeriodUPOS;
    }
    public void setPaymentPeriodUPOS(String paymentPeriodUPOS) {
        this.paymentPeriodUPOS = paymentPeriodUPOS;
    }

    // public String getHourPayment() {
    //     return this.hourPayment;
    // }

    // public void setHourPayment(String hourPayment) {
    //     this.hourPayment = hourPayment;
    // }

    public String getHourPaymentND() {
        return this.hourPaymentND;
    }

    public void setHourPaymentND(String hourPaymentND) {
        this.hourPaymentND = hourPaymentND;
    }

    public String getHourPaymentQR() {
        return this.hourPaymentQR;
    }

    public void setHourPaymentQR(String hourPaymentQR) {
        this.hourPaymentQR = hourPaymentQR;
    }
    
    public String getHourPaymentBNPL() {
        return this.hourPaymentBNPL;
    }

    public void setHourPaymentBNPL(String hourPaymentBNPL) {
        this.hourPaymentBNPL = hourPaymentBNPL;
    }

    public String getHourPaymentUPOS() {
        return this.hourPaymentUPOS;
    }

    public void setHourPaymentUPOS(String hourPaymentUPOS) {
        this.hourPaymentUPOS = hourPaymentUPOS;
    }

    public Integer getCutOffTimeND() {
        return this.cutOffTimeND;
    }

    public void setCutOffTimeND(Integer cutOffTimeND) {
        this.cutOffTimeND = cutOffTimeND;
    }

    public Integer getCutOffTimeQR() {
        return this.cutOffTimeQR;
    }

    public void setCutOffTimeQR(Integer cutOffTimeQR) {
        this.cutOffTimeQR = cutOffTimeQR;
    }
    
    public Integer getCutOffTimeBNPL() {
        return this.cutOffTimeBNPL;
    }

    public void setCutOffTimeBNPL(Integer cutOffTimeBNPL) {
        this.cutOffTimeBNPL = cutOffTimeBNPL;
    }
    public Integer getCutOffTimeUPOS() {
        return this.cutOffTimeUPOS;
    }

    public void setCutOffTimeUPOS(Integer cutOffTimeUPOS) {
        this.cutOffTimeUPOS = cutOffTimeUPOS;
    }

    public boolean isAdvTranFailQT() {
        return this.advTranFailQT;
    }

    public boolean getAdvTranFailQT() {
        return this.advTranFailQT;
    }

    public void setAdvTranFailQT(boolean advTranFailQT) {
        this.advTranFailQT = advTranFailQT;
    }
    


    public Integer getFunctionCreateAdv() {
        return this.functionCreateAdv;
    }

    public void setFunctionCreateAdv(Integer functionCreateAdv) {
        this.functionCreateAdv = functionCreateAdv;
    }

    public String getFunctionCreateAdvParam() {
        return this.functionCreateAdvParam;
    }

    public void setFunctionCreateAdvParam(String functionCreateAdvParam) {
        this.functionCreateAdvParam = functionCreateAdvParam;
    }

    public String getFunctionCreateAdvDesc() {
        return this.functionCreateAdvDesc;
    }

    public void setFunctionCreateAdvDesc(String functionCreateAdvDesc) {
        this.functionCreateAdvDesc = functionCreateAdvDesc;
    }

    public Integer getFunctionCreateFeeMonth() {
        return this.functionCreateFeeMonth;
    }

    public void setFunctionCreateFeeMonth(Integer functionCreateFeeMonth) {
        this.functionCreateFeeMonth = functionCreateFeeMonth;
    }

    public String getFunctionCreateFeeMonthParam() {
        return this.functionCreateFeeMonthParam;
    }

    public void setFunctionCreateFeeMonthParam(String functionCreateFeeMonthParam) {
        this.functionCreateFeeMonthParam = functionCreateFeeMonthParam;
    }

    public String getLbfunctionCreateFeeMonthDesc() {
        return this.lbfunctionCreateFeeMonthDesc;
    }

    public void setLbfunctionCreateFeeMonthDesc(String lbfunctionCreateFeeMonthDesc) {
        this.lbfunctionCreateFeeMonthDesc = lbfunctionCreateFeeMonthDesc;
    }
    

    public String getReceiptType() {
        return this.receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }


    public String getMergeMonthlyReport() {
        return this.mergeMonthlyReport;
    }

    public void setMergeMonthlyReport(String mergeMonthlyReport) {
        this.mergeMonthlyReport = mergeMonthlyReport;
    }


    public Integer getHoldDay() {
        return this.holdDay;
    }

    public void setHoldDay(Integer holdDay) {
        this.holdDay = holdDay;
    }


    public String getAddendum() {
        return this.addendum;
    }

    public void setAddendum(String addendum) {
        this.addendum = addendum;
    }

    public String getControlMinutes() {
        return this.controlMinutes;
    }

    public void setControlMinutes(String controlMinutes) {
        this.controlMinutes = controlMinutes;
    }



    public String getInsertAdvTxnFuncQT() {
        return this.insertAdvTxnFuncQT;
    }

    public void setInsertAdvTxnFuncQT(String insertAdvTxnFuncQT) {
        this.insertAdvTxnFuncQT = insertAdvTxnFuncQT;
    }

    public String getGetAdvTxnFuncQT() {
        return this.getAdvTxnFuncQT;
    }

    public void setGetAdvTxnFuncQT(String getAdvTxnFuncQT) {
        this.getAdvTxnFuncQT = getAdvTxnFuncQT;
    }

    public String getInsertAdvTxnFuncND() {
        return this.insertAdvTxnFuncND;
    }

    public void setInsertAdvTxnFuncND(String insertAdvTxnFuncND) {
        this.insertAdvTxnFuncND = insertAdvTxnFuncND;
    }

    public String getGetAdvTxnFuncND() {
        return this.getAdvTxnFuncND;
    }

    public void setGetAdvTxnFuncND(String getAdvTxnFuncND) {
        this.getAdvTxnFuncND = getAdvTxnFuncND;
    }

    public String getInsertAdvTxnFuncQR() {
        return this.insertAdvTxnFuncQR;
    }

    public void setInsertAdvTxnFuncQR(String insertAdvTxnFuncQR) {
        this.insertAdvTxnFuncQR = insertAdvTxnFuncQR;
    }

    public String getInsertAdvTxnFuncBNPL() {
        return this.insertAdvTxnFuncBNPL;
    }

    public void setInsertAdvTxnFuncBNPL(String insertAdvTxnFuncBNPL) {
        this.insertAdvTxnFuncBNPL = insertAdvTxnFuncBNPL;
    }

    public String getInsertAdvTxnFuncUPOS() {
        return this.insertAdvTxnFuncUPOS;
    }

    public void setInsertAdvTxnFuncUPOS(String insertAdvTxnFuncUPOS) {
        this.insertAdvTxnFuncUPOS = insertAdvTxnFuncUPOS;
    }

    public String getGetAdvTxnFuncQR() {
        return this.getAdvTxnFuncQR;
    }

    public void setGetAdvTxnFuncQR(String getAdvTxnFuncQR) {
        this.getAdvTxnFuncQR = getAdvTxnFuncQR;
    }
    public String getGetAdvTxnFuncBNPL() {
        return this.getAdvTxnFuncBNPL;
    }

    public void setGetAdvTxnFuncBNPL(String getAdvTxnFuncBNPL) {
        this.getAdvTxnFuncBNPL = getAdvTxnFuncBNPL;
    }

    public String getGetAdvTxnFuncUPOS() {
        return this.getAdvTxnFuncUPOS;
    }

    public void setGetAdvTxnFuncUPOS(String getAdvTxnFuncUPOS) {
        this.getAdvTxnFuncUPOS = getAdvTxnFuncUPOS;
    }

    public String getInsertAdvTxnFuncSMS() {
        return this.insertAdvTxnFuncSMS;
    }

    public void setInsertAdvTxnFuncSMS(String insertAdvTxnFuncSMS) {
        this.insertAdvTxnFuncSMS = insertAdvTxnFuncSMS;
    }

    public String getGetAdvTxnFuncSMS() {
        return this.getAdvTxnFuncSMS;
    }

    public void setGetAdvTxnFuncSMS(String getAdvTxnFuncSMS) {
        this.getAdvTxnFuncSMS = getAdvTxnFuncSMS;
    }
    

    public String getInsertHoldFdFunc() {
        return this.insertHoldFdFunc;
    }

    public void setInsertHoldFdFunc(String insertHoldFdFunc) {
        this.insertHoldFdFunc = insertHoldFdFunc;
    }

    public String getInsertHoldFunc() {
        return this.insertHoldFunc;
    }

    public void setInsertHoldFunc(String insertHoldFunc) {
        this.insertHoldFunc = insertHoldFunc;
    }


    public ConfigDto getSourceConfig() {
        return this.sourceConfig;
    }

    public void setSourceConfig(ConfigDto sourceConfig) {
        this.sourceConfig = sourceConfig;
    }

    public String getTrantypeConfig() {
        return this.trantypeConfig;
    }

    public void setTrantypeConfig(String trantypeConfig) {
        this.trantypeConfig = trantypeConfig;
    }

    public String getJBilling() {
        return this.jBilling;
    }

    public void setJBilling(String jBilling) {
        this.jBilling = jBilling;
    }

    public String getJBNPL() {
        return this.jBNPL;
    }

    public void setJBNPL(String jBNPL) {
        this.jBNPL = jBNPL;
    }
    public String getJUPOS() {
        return this.jUPOS;
    }

    public void setJUPOS(String jUPOS) {
        this.jUPOS = jUPOS;
    }

    public String getPaymentPeriodBilling() {
        return this.paymentPeriodBilling;
    }

    public void setPaymentPeriodBilling(String paymentPeriodBilling) {
        this.paymentPeriodBilling = paymentPeriodBilling;
    }
 
    public Integer getCutOffTimeBilling() {
        return this.cutOffTimeBilling;
    }

    public void setCutOffTimeBilling(Integer cutOffTimeBilling) {
        this.cutOffTimeBilling = cutOffTimeBilling;
    }

    public String getInsertAdvTxnFuncBilling() {
        return this.insertAdvTxnFuncBilling;
    }

    public void setInsertAdvTxnFuncBilling(String insertAdvTxnFuncBilling) {
        this.insertAdvTxnFuncBilling = insertAdvTxnFuncBilling;
    }

    public String getGetAdvTxnFuncBilling() {
        return this.getAdvTxnFuncBilling;
    }

    public void setGetAdvTxnFuncBilling(String getAdvTxnFuncBilling) {
        this.getAdvTxnFuncBilling = getAdvTxnFuncBilling;
    }

    public Timestamp getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getPaymentPeriodPayOut() {
        return paymentPeriodPayOut;
    }

    public void setPaymentPeriodPayOut(String paymentPeriodPayOut) {
        this.paymentPeriodPayOut = paymentPeriodPayOut;
    }

    public String getHourPaymentPayOut() {
        return hourPaymentPayOut;
    }

    public void setHourPaymentPayOut(String hourPaymentPayOut) {
        this.hourPaymentPayOut = hourPaymentPayOut;
    }

    public Integer getCutOffTimePayOut() {
        return cutOffTimePayOut;
    }

    public void setCutOffTimePayOut(Integer cutOffTimePayOut) {
        this.cutOffTimePayOut = cutOffTimePayOut;
    }

    public String getInsertAdvTxnFuncPayOut() {
        return insertAdvTxnFuncPayOut;
    }

    public void setInsertAdvTxnFuncPayOut(String insertAdvTxnFuncPayOut) {
        this.insertAdvTxnFuncPayOut = insertAdvTxnFuncPayOut;
    }

    public String getGetAdvTxnFuncPayOut() {
        return getAdvTxnFuncPayOut;
    }

    public void setGetAdvTxnFuncPayOut(String getAdvTxnFuncPayOut) {
        this.getAdvTxnFuncPayOut = getAdvTxnFuncPayOut;
    }

    public String getjPayOut() {
        return jPayOut;
    }

    public void setjPayOut(String jPayOut) {
        this.jPayOut = jPayOut;
    }

    public Map<String, Object> getAdvanceAccountPayout() {
        return advanceAccountPayout;
    }

    public void setAdvanceAccountPayout(Map<String, Object> advanceAccountPayout) {
        this.advanceAccountPayout = advanceAccountPayout;
    }
    
    public String getUposType() {
        return uposType;
    }

    public void setUposType(String uposType) {
        this.uposType = uposType;
    }

    // Auto configuration getters and setters
    public Integer getIsAutoEnabled() { 
        return isAutoEnabled; 
    }
    
    public void setIsAutoEnabled(Integer isAutoEnabled) { 
        this.isAutoEnabled = isAutoEnabled; 
    }
    
    public String getAutoDays() { 
        return autoDays; 
    }
    
    public void setAutoDays(String autoDays) { 
        this.autoDays = autoDays; 
    }
    
    public String getAutoStepLevel() { 
        return autoStepLevel; 
    }
    
    public void setAutoStepLevel(String autoStepLevel) { 
        this.autoStepLevel = autoStepLevel; 
    }
    
    public String getEmailNotifyPayment() { 
        return emailNotifyPayment; 
    }
    
    public void setEmailNotifyPayment(String emailNotifyPayment) { 
        this.emailNotifyPayment = emailNotifyPayment; 
    }
    
    public String getEmailNotifyAccount() { 
        return emailNotifyAccount; 
    }
    
    public void setEmailNotifyAccount(String emailNotifyAccount) { 
        this.emailNotifyAccount = emailNotifyAccount; 
    }
    
    public String getEmailNotifyBod() { 
        return emailNotifyBod; 
    }
    
    public void setEmailNotifyBod(String emailNotifyBod) { 
        this.emailNotifyBod = emailNotifyBod; 
    }

}
