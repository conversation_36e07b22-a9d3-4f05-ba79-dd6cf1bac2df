package vn.onepay.portal.resources.fee.group_config.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PartnerGroupDAO extends Db {
    private static final Logger _LOGGER = Logger.getLogger(PartnerGroupDAO.class.getName());
    private static final String GET_LIST_PARTNER_GROUP_BY_PARTNER_FEE_CONFIG_ID = "{call ONEFIN.PKG_PARTNER_GROUP.GET_PARTNER_GROUP_BY_PARTNER_FEE_CONFIG_ID(?,?,?,?,?)}";
    private static final String INSERT_PARTNER_GROUP = "{call ONEFIN.PKG_PARTNER_GROUP.INSERT_PARTNER_GROUP(?,?,?,?,?,?,?)}";
    private static final String DELETE_PARTNER_GROUP_BY_ID = "{call ONEFIN.PKG_PARTNER_GROUP.DELETE_PARTNER_GROUP_BY_ID(?,?,?,?)}";
    private static final String UPDATE_PARTNER_GROUP_BY_ID = "{call ONEFIN.PKG_PARTNER_GROUP.UPDATE_PARTNER_GROUP_BY_ID(?,?,?,?,?,?,?)}";

    public static BaseList<Map<String, Object>> getListPartnerGroupBYPartnerFeeConfigId(Integer partnerFeeConfigId) throws SQLException {
        SQLException exception = null;
        BaseList<Map<String, Object>> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_PARTNER_GROUP_BY_PARTNER_FEE_CONFIG_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, partnerFeeConfigId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get partner group : " + error);
            } else {
                if (rs != null) {
                    result = new BaseList<>(Util.resultSetToList(rs), cs.getInt(4));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * @param mIn data
     * @return Thêm mới cấu hình group
     * @throws SQLException error
     */
    public static Map<String, Object> insertPartnerGroup(JsonObject mIn) throws SQLException {
        SQLException exception = null;
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(INSERT_PARTNER_GROUP);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3,mIn.getInteger(PARTNER_FEE_CONFIG_ID,0));
            cs.setInt(4,mIn.getInteger(PARTNER_FEE_SERVICE_ID,0));
            cs.setString(5,mIn.getString(PARTNER_GROUP_NAME,BLANK));
            cs.setString(6,mIn.getString(TYPE,BLANK));
            cs.setString(7,mIn.getString(USER_UPDATE,BLANK));
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 400) {
                result.put("status", nError);
                result.put("message", error);
            } else if (nError == 201) {
                result.put("status", nError);
                result.put("message", error);
            } else {
                throw new SQLException("DB insert part group : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> deletePartnerGroupById(Integer partnerGroupId) throws SQLException {
        SQLException exception = null;
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(DELETE_PARTNER_GROUP_BY_ID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, partnerGroupId);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 400) {
                result.put("status", nError);
                result.put("message", error);
            } else if (nError == 200) {
                result.put("status", nError);
                result.put("message", error);
            } else {
                throw new SQLException("DB insert part group : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * @param mIn data
     * @return Cập nhật thông tin partner group
     * @throws SQLException error
     */
    public static Map<String, Object> updatePartnerGroupById(JsonObject mIn) throws SQLException {
        SQLException exception = null;
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(UPDATE_PARTNER_GROUP_BY_ID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, mIn.getInteger(PARTNER_GROUP_ID));
            cs.setString(5, mIn.getString(PARTNER_GROUP_NAME));
            cs.setInt(6, mIn.getInteger(PARTNER_FEE_SERVICE_ID));
            cs.setString(7, mIn.getString(USER_UPDATE));
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 400) {
                result.put("status", nError);
                result.put("message", error);
            } else if (nError == 200) {
                result.put("status", nError);
                result.put("message", error);
            } else {
                throw new SQLException("DB insert part group : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
