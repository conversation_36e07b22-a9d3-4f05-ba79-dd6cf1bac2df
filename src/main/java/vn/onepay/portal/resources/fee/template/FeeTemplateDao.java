package vn.onepay.portal.resources.fee.template;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.template.dto.TemplateFeeInputDto;
import vn.onepay.portal.resources.fee.template.dto.TemplateDto;
import vn.onepay.portal.resources.fee.template.dto.TemplateFeeDto;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
public class FeeTemplateDao extends Db implements IConstants {

    public static int insertTemplateFee(TemplateFeeInputDto tempFee, String userId, int templateId) throws  Exception{
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.INSERT_FEE_TEMPLATE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1,OracleTypes.NUMBER);
            cs.registerOutParameter(2,OracleTypes.VARCHAR);
            cs.setObject(3, templateId);
            cs.setString(4, tempFee.getService());
            cs.setString(5, tempFee.getAcquirer());
            cs.setString(6, tempFee.getPaygate());
            cs.setObject(7, tempFee.getBinGroupId());
            cs.setString(8, tempFee.getTransactionType());
            cs.setObject(9, tempFee.getFixFee());
            cs.setObject(10, tempFee.getPercentFee());
            cs.setString(11, tempFee.getFromDate());
            cs.setString(12, tempFee.getToDate());
            cs.setString(13, tempFee.getFuncId());
            cs.setString(14, tempFee.getFuncInput());
            cs.setString(15, tempFee.getType());
            cs.setObject(16, tempFee.getOrder());
            cs.setString(17, tempFee.getCurrency());
            cs.setString(18, userId);
            cs.setString (19,tempFee.getContractType());
            cs.executeQuery();
            String error = cs.getString(2);
             nerror = cs.getInt(1);
            if (nerror == 500) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }
    public static int deleteFeeTemplate(TemplateFeeInputDto tempFee, String userId, int templateId) throws  Exception{
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.DELETE_FEE_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1,OracleTypes.NUMBER);
            cs.registerOutParameter(2,OracleTypes.VARCHAR);
            cs.setObject(3, templateId);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }
    public static int insertTemplate(TemplateDto template) throws  Exception{
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int templateId = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.INSERT_TEMPLATE(?,?,?,?,?,?)}");
            cs.setString(1, template.getName());
            cs.setString(2, template.getDesc());
            cs.setString(3, template.getState());
            cs.registerOutParameter(4,OracleTypes.NUMBER);
            cs.registerOutParameter(5,OracleTypes.VARCHAR);
            cs.registerOutParameter(6,OracleTypes.NUMBER);
            cs.executeQuery();
            String error = cs.getString(5);
            int nerror = cs.getInt(4);
            templateId = cs.getInt(6);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return templateId;
    }
    public static int updateTemplate(TemplateDto template) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_TEMPLATE(?,?,?,?,?)}");
            cs.setInt(1, template.getId());
            cs.setString(2, template.getName());
            cs.setString(3, template.getDesc());
            cs.registerOutParameter(4,OracleTypes.NUMBER);
            cs.registerOutParameter(5,OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(5);
            nerror = cs.getInt(4);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }

    public static int deleteTempaltefee(int templateID) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.DELETE_TEMPLATE_FEE(?,?,?)}");
            cs.setInt(1,templateID);
            cs.registerOutParameter(2,OracleTypes.NUMBER);
            cs.registerOutParameter(3,OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            nerror = cs.getInt(2);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }
    public static int deleteTemplate(int templateID) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try{
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.DELETE_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1,OracleTypes.NUMBER);
            cs.registerOutParameter(2,OracleTypes.VARCHAR);
            cs.setInt(3,templateID);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }
    public static Map<String,Object> getTemplate(String keyword,int page_size,int page) throws Exception {
        Exception exception = null;
        List<TemplateDto> template = new ArrayList<>();
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_TEMPLATE_BY_KEY(?,?,?,?,?,?,?)}");
            cs.setString(1, keyword);
            cs.setInt(2, page);
            cs.setInt(3, page_size);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.registerOutParameter(7, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(7);
            int nerror = cs.getInt(6);
            rs = (ResultSet) cs.getObject(4);
            total = cs.getInt(5);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    template.add(blindTemplate(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("template",template);
        data.put("total",total);
        return data;
    }
    public static Map<String,Object> getAlltemplate() throws Exception {
        Exception exception = null;
        List<TemplateDto> template = new ArrayList<>();
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    template.add(blindTemplate(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("template",template);
        return data;
    }
    public static TemplateDto getTemplatebyID(int id) throws Exception {
        Exception exception = null;
        TemplateDto template = new TemplateDto();;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_TEMPLATE_BY_ID(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    template.setId(rs.getInt("N_ID"));
                    template.setName(rs.getString("S_NAME"));
                    template.setDesc(rs.getString("S_DESC"));;
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return template;
    }

    public static List<TemplateFeeDto> getTemplateDetail(int template_id) throws Exception {
        Exception exception = null;
        List<TemplateFeeDto> template = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_TEMPLATE_DETAIL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, template_id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get template detail error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    template.add(bindFeeTemplate(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return template;
    }
    private static TemplateFeeDto bindFeeTemplate(ResultSet rs) throws Exception{
        TemplateFeeDto temp = new TemplateFeeDto();
        temp.setId(rs.getInt("N_ID"));
        temp.setTemplateId(rs.getInt("N_TEMPLATE_ID"));
        temp.setService(rs.getString("S_SERVICE"));
        temp.setPaygate(rs.getString("S_PAYGATE"));
        temp.setAcquirer(rs.getString("S_ACQUIRER"));
        temp.setBinGroupId(rs.getInt("N_BIN_GROUP_ID"));
        temp.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        temp.setFixFee(rs.getDouble("N_FIX_FEE") == 0 ? null : rs.getDouble("N_FIX_FEE"));
        temp.setPercentFee(rs.getDouble("N_PERCENT_FEE") == 0 ? null : rs.getDouble("N_PERCENT_FEE"));
        temp.setFromDate(rs.getTimestamp("D_FROM_DATE"));
        temp.setToDate(rs.getTimestamp("D_TO_DATE"));
        temp.setType(rs.getString("S_TYPE"));
        temp.setFuncId(rs.getString("S_FUNC_ID"));
        temp.setFuncInput(rs.getString("S_FUNC_INPUT"));
        temp.setCreateDate(rs.getTimestamp("D_CREATE"));
        temp.setUpdateDate(rs.getTimestamp("D_UPDATE"));
        temp.setOrder(rs.getInt("N_ORDER"));
        temp.setCurrency(rs.getString("S_CURRENCY"));
        temp.setContractType(rs.getString("S_CONTRACT_TYPE"));
        return temp;
    }
    private static TemplateDto blindTemplate(ResultSet rs) throws Exception{
        TemplateDto temp = new TemplateDto();
        temp.setId(rs.getInt("N_ID"));
        temp.setName(rs.getString("S_NAME"));
        temp.setDesc(rs.getString("S_DESC"));
        return temp;
    }

}
