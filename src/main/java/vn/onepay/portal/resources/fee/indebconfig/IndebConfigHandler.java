package vn.onepay.portal.resources.fee.indebconfig;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.IConstants.*;
import static vn.onepay.portal.Util.sendResponse;

public class IndebConfigHandler {

    private static Logger logger = Logger.getLogger(IndebConfigHandler.class.getName());

    public static void getListTemplateMail(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("templateList",IndebConfigDao.getTemplateEmail());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    public static void getListTemplateFeeMonth(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("templateList",IndebConfigDao.getTemplateFeeMonth());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    public static void getListConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
            HttpServerRequest request = ctx.request();
            ConfigQueryDto config = new ConfigQueryDto();
//            String fromDate = request.getParam("fromDate").length() == 0 ? null:request.getParam("fromDate");
//            String toDate = request.getParam("toDate").length() == 0 ? null:request.getParam("toDate");
            config.setKeyword(request.getParam("keyword").length() == 0 ? null:request.getParam("keyword"));
            config.setPageSize(request.getParam("page_size").length() == 0 ? 50 : Integer.parseInt(request.getParam("page_size")));
            config.setPage(request.getParam("page").length() == 0 ? 0 :Integer.parseInt(request.getParam("page")));
            if(request.getParam("paymentPeriod").length() == 0 || request.getParam("paymentPeriod").equalsIgnoreCase("")) {
                config.setPaymentPeriod(null);
            } else if(request.getParam("paymentPeriod").equalsIgnoreCase("T 1")) {
                config.setPaymentPeriod("T+1");
            } else if(request.getParam("paymentPeriod").equalsIgnoreCase("T 2")) {
                config.setPaymentPeriod("T+2");
            } else if(request.getParam("paymentPeriod").equalsIgnoreCase("T 7")) {
                config.setPaymentPeriod("T+7");
            } else {
                config.setPaymentPeriod(request.getParam("paymentPeriod"));
            }
            config.setAdvanceBank(request.getParam("advanceBank").length() == 0 ? null:request.getParam("advanceBank"));
            config.setState(request.getParam("state").length() == 0 ? 0:Integer.parseInt(request.getParam("state")));
            config.setService(request.getParam("service").length() == 0 ? null:request.getParam("service"));
            config.setContractType(request.getParam("contractType").length() == 0 ? null:request.getParam("contractType"));
            if(request.getParam("guaranteeAmount").length() == 0 || request.getParam("guaranteeAmount").equalsIgnoreCase("")) {
                config.setGuaranteeAmount(null);
            } else if(request.getParam("guaranteeAmount").equalsIgnoreCase("Percent")){
                config.setGuaranteeAmount("1,5000,000 + 15%");
            } else {
                config.setGuaranteeAmount(request.getParam("guaranteeAmount"));
            }
//            config.setGuaranteeAmount(request.getParam("guaranteeAmount").length() == 0 ? null:request.getParam("guaranteeAmount"));
            config.setGuaranteeHoldingType(request.getParam("guaranteeHoldingType").length() == 0 ? null:request.getParam("guaranteeHoldingType"));
            config.setWithoutMerchant(request.getParam("withoutMerchant").length() == 0 ? null:request.getParam("withoutMerchant"));
            config.setStatusConfig(request.getParam("statusConfig").length() == 0 ? null:request.getParam("statusConfig"));
            config.setStateOfEffective(request.getParam("stateOfEffective").length() == 0 ? "":request.getParam("stateOfEffective"));
            config.setFromDate(request.getParam("fromDate").length() == 0 ? "":request.getParam("fromDate"));
            config.setToDate(request.getParam("toDate").length() == 0 ? "":request.getParam("toDate"));
            Map<String,Object> temp = new HashMap<>();
            
                temp = IndebConfigDao.getListConfig(config);
                temp.put("total", IndebConfigDao.getTotal(config));
                sendResponse(ctx, 200,temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
        }, false, null);
    }
    public static void dowloadConfig(RoutingContext ctx){
        ctx.vertx().executeBlocking(future->{
          try {
              JsonObject request = ctx.getBodyAsJson();
              ConfigQueryDto config = new ConfigQueryDto();
            config.setKeyword(request.getString("keyword") == null ? "":request.getString("keyword"));
            config.setPageSize(request.getInteger("page_size")== null ? 50 : request.getInteger("page_size"));
            config.setPage(request.getInteger("page")== null ? 0 :request.getInteger("page"));
            if(request.getString("paymentPeriod")== null || request.getString("paymentPeriod").equalsIgnoreCase("")) {
                config.setPaymentPeriod("");
            } else if(request.getString("paymentPeriod").equalsIgnoreCase("T 1")) {
                config.setPaymentPeriod("T+1");
            } else if(request.getString("paymentPeriod").equalsIgnoreCase("T 2")) {
                config.setPaymentPeriod("T+2");
            } else if(request.getString("paymentPeriod").equalsIgnoreCase("T 7")) {
                config.setPaymentPeriod("T+7");
            } else {
                config.setPaymentPeriod(request.getString("paymentPeriod"));
            }
            config.setAdvanceBank(request.getString("advanceBank")== null ? "":request.getString("advanceBank"));
            config.setState(request.getString("state")== null ? 0:Integer.parseInt(request.getString("state")));
            config.setService(request.getString("service")== null ? "":request.getString("service"));
            config.setContractType(request.getString("contractType")== null ? "":request.getString("contractType"));
            if(request.getString("guaranteeAmount")== null || request.getString("guaranteeAmount").equalsIgnoreCase("")) {
                config.setGuaranteeAmount(null);
            } else if(request.getString("guaranteeAmount").equalsIgnoreCase("Percent")){
                config.setGuaranteeAmount("1,5000,000 + 15%");
            } else {
                config.setGuaranteeAmount(request.getString("guaranteeAmount"));
            }
            config.setGuaranteeHoldingType(request.getString("guaranteeHoldingType")== null ? "":request.getString("guaranteeHoldingType"));
            config.setWithoutMerchant(request.getString("withoutMerchant")== null ? "":request.getString("withoutMerchant"));
            config.setStatusConfig(request.getString("statusConfig")== null ? "":request.getString("statusConfig"));
            config.setStateOfEffective(request.getString("stateOfEffective")== null ? "":request.getString("stateOfEffective"));
            config.setFromDate(request.getString("fromDate")== null ? "":request.getString("fromDate"));
            config.setToDate(request.getString("toDate")== null ? "":request.getString("toDate"));
              Map<String, Object> requestData = new HashMap<>();
              requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
              requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
              requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
              int totalRows =IndebConfigDao.getTotal(config);
              if (totalRows == 0) {
                  throw IErrors.NO_DATA_FOUND;
              }
              long date = new java.util.Date().getTime();
              String fileName = "Indebtebness_Config" + date;
              String fileHashName = "";
              Map<String, Object> data = new HashMap<>();
              data.put("parameter", config);
              data.put("file_name", fileName);
              data.put("row", totalRows);
              try {
                  fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
              } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                  ctx.fail(e);
              }
              requestData.put(FILE_HASH_NAME, fileHashName);
              requestData.put(FILE_NAME, fileName);
              FileDownloadDto fileDownloadDto = new FileDownloadDto();
              fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
              fileDownloadDto.setFile_type("Indebtebness_Config");
              fileDownloadDto.setFile_name(fileName);
              fileDownloadDto.setFile_hash_name(fileHashName);
              fileDownloadDto.setConditions(gson.toJson(config));
              if (totalRows <= Config.getFileRowLevel()) {
                  fileDownloadDto.setExt("xlsx");
              }else{
                  fileDownloadDto.setExt("zip");
              }
              FileDownloadDao.insert(fileDownloadDto);

              if (totalRows <= Config.getFileRowLevel()) {
                  //fileDownload.setExt("csv");
                  QueueProducer.sendMessage(new Message(config, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
              } else {
                  //fileDownload.setExt("zip");
                  QueueProducer.sendMessage(new Message(config, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
              }

              sendResponse(ctx, 200, fileDownloadDto);
          } catch (Exception e) {
              logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
              ctx.fail(e);
          }
        }, false, null);
    }
    public static void getDetailConfig(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int id = Integer.parseInt(request.getParam("id"));
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("configDetail",IndebConfigDao.getConfigDetail(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void updateInsertConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
            Map<String,Object> temp = new HashMap<>();
            JsonObject bodyJson = ctx.getBodyAsJson();
            ConfigDto config = new ConfigDto();
            config.setParentId((bodyJson.getValue("parentId") == null || bodyJson.getValue("parentId").toString().isEmpty()) ? null : bodyJson.getInteger("parentId"));
            config.setPartnerId(bodyJson.getString("partnerId").length()==0?null:Integer.parseInt(bodyJson.getString("partnerId")));
            config.setContractCode(bodyJson.getString("contractCode").length()==0?null:bodyJson.getString("contractCode"));
            config.setTaxCode(bodyJson.getString("taxCode").length()==0?null:bodyJson.getString("taxCode"));
            config.setAction(bodyJson.getString("action").length() == 0 ?0:Integer.parseInt(bodyJson.getString("action")));
            config.setPaymentPeriod(bodyJson.getString("paymentPeriod").length()==0?null:bodyJson.getString("paymentPeriod"));
            config.setCutOffTime(bodyJson.getString("cutOffTime")== null || bodyJson.getString("cutOffTime").equals("") ?null:Integer.parseInt(bodyJson.getString("cutOffTime")));
            String percentConfig = bodyJson.getString("advancePercent");
            String percent = percentConfig.replace("%","");
            if ( percent.equals("")) {
                config.setAdvancePercent(null);
            } else {
                config.setAdvancePercent(Double.parseDouble(percent));
            }
            JsonArray arrayDayOfWeek = bodyJson.getJsonArray("dayOfWeek");
            if (arrayDayOfWeek != null) {
                String dayOfWeek = String.join(",", arrayDayOfWeek.getList());
                config.setDayOfWeek(dayOfWeek);
            } else {
                config.setDayOfWeek(null);
            }
            JsonArray arrayDayOfMonth = bodyJson.getJsonArray("dayOfMonth");
            if (arrayDayOfMonth != null) {
                if (arrayDayOfMonth.size() > 0) {
                    String dayOfMonth = String.join(",", arrayDayOfMonth.getList());
                    config.setDayOfMonth(dayOfMonth);
                } else {
                    config.setDayOfMonth(null);
                }
            }
            config.setAdvanceAmount(bodyJson.getString("advanceAmount").length()==0?null:bodyJson.getString("advanceAmount"));
            config.setLastMerchantName(bodyJson.getString("lastMerchantName").length()==0?null:bodyJson.getString("lastMerchantName"));
            config.setTenDkkd(bodyJson.getString("tenDkkd").length()==0?null:bodyJson.getString("tenDkkd"));
            config.setBank(bodyJson.getString("advanceBank").length()==0?null:bodyJson.getString("advanceBank"));
            config.setAdvanceAccount(bodyJson.getString("advanceAccount").length()==0?null:bodyJson.getString("advanceAccount"));
            config.setBranchName(bodyJson.getString("branchName").length()==0?null:bodyJson.getString("branchName"));
            config.setAccountName(bodyJson.getString("accountName").length()==0?null:bodyJson.getString("accountName"));
            config.setAccountType(bodyJson.getString("accountType").length()==0?null:bodyJson.getString("accountType"));
            config.setAccountNumber(bodyJson.getString("accountNumber").length()==0?null:bodyJson.getString("accountNumber").trim());
            config.setGuaranteeAmount(bodyJson.getString("guaranteeAmount").length()==0?null:Convert.parseDouble(bodyJson.getString("guaranteeAmount"), 0) );
            config.setGuaranteeHoldingType(bodyJson.getString("guaranteeHoldingType").length()==0?null:bodyJson.getString("guaranteeHoldingType"));
            config.setFlowType(bodyJson.getString("flowType").length()==0?null:Integer.parseInt(bodyJson.getString("flowType")));
            config.setTemplateId(bodyJson.getString("templateId").length()==0?null:Integer.parseInt(bodyJson.getString("templateId")));
            String fromDate = bodyJson.getString("fromDate");
            String toDate = bodyJson.getString("toDate");
            config.setNotes(bodyJson.getString("notes"));
            JsonArray conditions = bodyJson.getJsonArray("conditions");
                if (conditions != null) {
                    if (conditions.size() > 0) {
                        for (Object element: conditions) {
                            if (element.equals("0")) {
                                config.setAdvanceVCB(element.toString());
                            } else if (element.equals("1")) {
                                config.setAdvanceTransFailed(element.toString());
                            }
                        }
                    }
                }
            config.setCheckEnoughGuarantee(!bodyJson.getBoolean("checkEnoughGuarantee") ? 0 : 1);
            config.setAccountingEntry(bodyJson.getString("accountingEntry"));
            config.setMinAdvanceAmount(bodyJson.getString("minAdvanceAmount").length() == 0 ? 0 : Double.parseDouble(bodyJson.getString("minAdvanceAmount")));
            config.setTemplateFeeMonth(bodyJson.getString("templateFeeMonth").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateFeeMonth")));
            config.setSeparateType(bodyJson.getString("separateType").length() == 0 ? null : bodyJson.getString("separateType"));
            config.setAttachFile(bodyJson.getString("attachFile").length() == 0 ? null : bodyJson.getString("attachFile"));
            config.setService(bodyJson.getString("service").length() == 0 ? null : bodyJson.getString("service"));
            String contractDate = bodyJson.getString("contractDate");

            int drafId = 0;
            if (config.getParentId()!= null){
                drafId = IndebConfigDao.getIdDrafConfig(config.getParentId());
            }
             //check draft
                if (drafId ==0){
                    temp.put("id",IndebConfigDao.updateInsertConfig(config, fromDate, toDate, contractDate));

                    // clone merchant id

                    temp.put("error",200);
                }else{
                    temp.put("id",drafId);
                    temp.put("error",IndebConfigDao.updateDrafConfig(config,drafId, fromDate, toDate, contractDate));
                }
                sendResponse(ctx, 200,temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on  inser config", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    public static void removeList(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
            JsonObject body = ctx.getBodyAsJson();
            JsonArray approvalId = body.getJsonArray("id");
            for (Object id : approvalId.getList()) {
                IndebConfigDao.deleteConfig(Integer.parseInt(id.toString()));
            }
            
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void approvalList(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
            JsonObject body = ctx.getBodyAsJson();
            List<ConfigDto> listConfig = IndebConfigDao.getApproveWaiting();
            JsonArray approvalId = body.getJsonArray("id");
            for (int i = 0;approvalId.size()>i;i++){
                for (int y = 0;listConfig.size()>y;y++){
                    ConfigDto conf = listConfig.get(y);
                    if (Integer.parseInt(approvalId.getString(i))== conf.getId() && (conf.getParentId() == null || conf.getParentId() == 0)){
                        if (conf.getState().equals("wait_for_approve")){
                            IndebConfigDao.approvalConfig(conf.getId(),"approved");
                        }else
                            IndebConfigDao.approvalDeleteConfig(conf.getId());
                        break;
                    }else if(Integer.parseInt(approvalId.getString(i))== conf.getId() && conf.getParentId() > 0){
                            IndebConfigDao.approvalUpdateConfig(conf.getId(),conf.getParentId());
                            break;
                    }
                }
            }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void getFlowType(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("listFlowType",IndebConfigDao.getListFlowType());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void getListPartner(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("listPartner",IndebConfigDao.getListPartner());
                sendResponse(ctx, 200,temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }

        }, false, null);
    }

    public static void getListContractCode(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            HttpServerRequest request = ctx.request();
            try {
                temp.put("listContractCode",IndebConfigDao.getListContractCode(request.getParam("partner_id"), request.getParam("config_id"), request.getParam("type_get")));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    public static void getListContractDate(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            HttpServerRequest request = ctx.request();
            try {
                temp.put("listContractDate",IndebConfigDao.getListContractDate(request.getParam("partner_id")
                        , request.getParam("config_id"), request.getParam("contract_code"), request.getParam("type_get")));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on : " , e);
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    private static final Gson gson = new Gson();

}
