package vn.onepay.portal.resources.fee.funcfee;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.funcfee.dto.*;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class FuncFeeDao extends Db implements IConstants {
    public static List<FuncFeedto> getFuncFee() throws Exception {
        Exception exception = null;
        List<FuncFeedto> func = new ArrayList<>();;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_FUNCTION_FEE_BY_SERVICE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    func.add(blindFunc(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return func;
    }
    public static List<IndebConfigDto> getIndebConfig(String contractCode) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<IndebConfigDto> indebConfig = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_INDEBTEBNESS_CONFIG(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4,contractCode);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    indebConfig.add(blindIndebConfig(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return indebConfig;
    }
    public static Map<String,Object> getFeeData(String keyword,String contract_type,String service,String paygate,int page,int page_size) throws Exception {
        Exception exception = null;
        List<FeeDataDto> func = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_LIST_FEE_VIEW(?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, page);
            cs.setInt(6, page_size);
            cs.setString(7, keyword);
            cs.setString(8, contract_type);
            cs.setString(9, service);
            cs.setString(10, paygate);
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            total = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    func.add(blindFeeViewData(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        HashMap<String,Object> temp = new HashMap<>();
        temp.put("total",func.size() );
        temp.put("feedata",func);
        return temp;
    }
//    public static List<FeeDetailDto> getFeebyContract(String contractCode) throws Exception {
//        Exception exception = null;
//        Connection con = null;
//        CallableStatement cs = null;
//        ResultSet rs = null;
//        List<FeeDetailDto> feedt = new ArrayList<>();
//        try {
//            con = getConnection114();
//            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_FEE_DATA_BY_CONTRACT_CODE(?,?,?,?)}");
//            cs.registerOutParameter(1, OracleTypes.CURSOR);
//            cs.registerOutParameter(2, OracleTypes.NUMBER);
//            cs.registerOutParameter(3, OracleTypes.VARCHAR);
//            cs.setString(4,contractCode);
//            cs.executeQuery();
//            String error = cs.getString(3);
//            int nerror = cs.getInt(2);
//            rs = (ResultSet) cs.getObject(1);
//            if (nerror == 0) {
//                logger.severe("DB get domestic transaction error: " + error);
//            } else {
//                while (rs != null && rs.next()) {
//                    feedt.add(blindFeeDetail(rs));
//                }
//            }
//        } catch (Exception e) {
//            logger.log(Level.SEVERE, "", e);
//            exception = e;
//        } finally {
//            closeConnectionDB(rs, null, cs, con);
//        }
//        if (exception != null)
//            throw exception;
//        return feedt;
//    }

    public static List<FeeViewDto> getFeeViewbyContract(String contractCode) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<FeeViewDto> feedt = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_FEE_VIEW_BY_CONTRACT_CODE(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4,contractCode);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    feedt.add(blindFeeView(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return feedt;
    }

//    private static FeeDataDto blindFeeData(ResultSet rs) throws Exception{
//        FeeDataDto func = new FeeDataDto();
//        func.setMerchantId(rs.getString("S_MERCHANT_ID"));
//        func.setPartnerName(rs.getString("S_TEN_DV"));
//        func.setRegisterName(rs.getString("S_TEN_DKKD"));
//        func.setContractCode(rs.getString("S_CONTRACT_CODE"));
//        func.setContractType(rs.getString("S_CONTRACT_TYPE"));
//        func.setService(rs.getString("S_PAY_CHANNEL"));
//        func.setAcquirer(rs.getString("S_ACQUIRER"));
//        func.setPaygate(rs.getString("S_PAYGATE"));
//        func.setMerchantNumber(rs.getString("S_CAIC"));
//        return func;
//    }

    private static FeeDataDto blindFeeViewData(ResultSet rs) throws Exception{
        FeeDataDto func = new FeeDataDto();
//        func.setMerchantId(rs.getString("S_MERCHANT_ID"));
        func.setPartnerName(rs.getString("S_TEN_DV"));
        func.setRegisterName(rs.getString("S_TEN_DKKD"));
        func.setContractCode(rs.getString("S_CONTRACT_CODE"));
        func.setContractType(rs.getString("S_CONTRACT_TYPE"));
        func.setService(rs.getString("S_SERVICE"));
        func.setAcquirer(rs.getString("S_ACQUIRER"));
        func.setPayGate(rs.getString("S_PAYGATE"));
        func.setMerchantNumber(rs.getString("S_MERCHANT_NO"));
        func.setCurrency(rs.getString("CURRENCY"));
        func.setMcc(rs.getString("MCC"));
        return func;
    }

    private static FuncFeedto blindFunc(ResultSet rs) throws Exception{
        FuncFeedto func = new FuncFeedto();
        func.setId(rs.getString("S_ID"));
        func.setName(rs.getString("S_NAME"));
        func.setDesc(rs.getString("S_DESC"));
        func.setParamDesc(rs.getString("S_PARAM_DESC"));
        func.setAcquirerName(rs.getString("S_ACQ"));
        func.setService(rs.getString("S_SERVICE"));
        func.setContractType(rs.getString("S_CONTRACT_TYPE"));
        func.setFeeType(rs.getString("S_FEE_TYPE"));
        return func;
    }
    private static IndebConfigDto blindIndebConfig(ResultSet rs) throws Exception{
        IndebConfigDto Config = new IndebConfigDto();
        Config.setContractCode(rs.getString("S_CONTRACT_CODE"));
        Config.setMerchantId(rs.getString("S_MERCHANT_ID"));
//        Config.setPaygate(rs.getString("S_PAYGATE"));
//        if(Config.getPaygate().toLowerCase().equals("nd")){
//            Config.setMerchantName(rs.getString("MERCHANT_ND_NAME"));
//            Config.setMidId(rs.getString("MID_ND_ID"));
//            Config.setCategoryCode(rs.getString("CATEGORY_ND_CODE"));
//        }else{
//            Config.setMerchantName(rs.getString("MERCHANT_QT_NAME"));
//            Config.setMidId(rs.getString("MID_QT_ID"));
//            Config.setCategoryCode(rs.getString("CATEGORY_QT_CODE"));
//        }
        return Config;
    }
//    private static FeeDetailDto blindFeeDetail(ResultSet rs) throws Exception{
//        FeeDetailDto feedt = new FeeDetailDto();
//        feedt.setAcquirer(rs.getString("S_ACQUIRER"));
//        feedt.setBinGroup(rs.getInt("N_BIN_GROUP_ID"));
//        feedt.setContractType(rs.getString("S_CONTRACT_TYPE"));
//        feedt.setPaygate(rs.getString("S_PAYGATE"));
//        feedt.setMerchant_id(rs.getString("S_MERCHANT_ID"));
//        feedt.setService(rs.getString("S_SERVICE"));
//        feedt.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
//        FeeType feeMer = new FeeType();
//        feeMer.setFixFee(rs.getDouble("FIX_FEE_MER"));
//        feeMer.setPercentFee(rs.getDouble("PERCENT_FEE_MER"));
//        feeMer.setFuncName(rs.getString("FUNC_NAME_MER"));
//        feeMer.setFuncInput(rs.getString("FUNC_INPUT_MER"));
//        if (feeMer!= null)feedt.setFeeMerchant(feeMer);
//        FeeType feeIss = new FeeType();
//        feeIss.setFixFee(rs.getDouble("FIX_FEE_ISS"));
//        feeIss.setPercentFee(rs.getDouble("PERCENT_FEE_ISS"));
//        feeIss.setFuncName(rs.getString("FUNC_NAME_ISS"));
//        feeIss.setFuncInput(rs.getString("FUNC_INPUT_ISS"));
//        if (feeIss!= null)feedt.setFeeIss(feeIss);
//        FeeType feeAcq = new FeeType();
//        feeAcq.setFixFee(rs.getDouble("FIX_FEE_ISS"));
//        feeAcq.setPercentFee(rs.getDouble("PERCENT_FEE_ISS"));
//        feeAcq.setFuncName(rs.getString("FUNC_NAME_ISS"));
//        feeAcq.setFuncInput(rs.getString("FUNC_INPUT_ISS"));
//        if (feeAcq!= null)feedt.setFeeAcq(feeAcq);
//        FeeType feeIns = new FeeType();
//        feeIns.setFixFee(rs.getDouble("FIX_FEE_INS"));
//        feeIns.setPercentFee(rs.getDouble("PERCENT_FEE_INS"));
//        feeIns.setFuncName(rs.getString("FUNC_NAME_INS"));
//        feeIns.setFuncInput(rs.getString("FUNC_INPUT_INS"));
//        if (feeIns!= null)feedt.setFeeInstallmentbank(feeIns);
//        return feedt;
//    }

    private static FeeViewDto blindFeeView(ResultSet rs) throws Exception{
        FeeViewDto feedt = new FeeViewDto();
        feedt.setMerchantId(rs.getString("S_MERCHANT_ID"));
//        feedt.setMerchantName(rs.getString("s_merchant_name"));
        feedt.setContractType(rs.getString("s_contract_type"));
        feedt.setAcquirer(rs.getString("S_ACQUIRER"));
        feedt.setBinGroupName(rs.getString("bingroup_name"));
        feedt.setContractType(rs.getString("s_contract_type"));
        feedt.setPayGate(rs.getString("S_PAYGATE"));
        feedt.setService(rs.getString("S_SERVICE"));
        feedt.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        feedt.setFeeType(rs.getString("s_type"));
        if (feedt.getFeeType().toUpperCase().equals("MERCHANT")) {
            FeeTypeMerchant feeTypeMerchant = new FeeTypeMerchant();
            feeTypeMerchant.setMerchantFixFee(rs.getDouble("n_fix_fee"));
            feeTypeMerchant.setMerchantPercentFee(rs.getDouble("n_percent_fee"));
            feeTypeMerchant.setMerchantFunctionName(rs.getString("function_name"));
            feeTypeMerchant.setMerchantFunctionInput(rs.getString("s_func_input"));
            feedt.setFeeTypeMerchant(feeTypeMerchant);
        } else if (feedt.getFeeType().toUpperCase().equals("ACQ")) {
            FeeTypeAcq feeTypeAcq = new FeeTypeAcq();
            feeTypeAcq.setAcqFixFee(rs.getDouble("n_fix_fee"));
            feeTypeAcq.setAcqPercentFee(rs.getDouble("n_percent_fee"));
            feeTypeAcq.setAcqFunctionName(rs.getString("function_name"));
            feeTypeAcq.setAcqFunctionInput(rs.getString("s_func_input"));
            feedt.setFeeTypeAcq(feeTypeAcq);
        } else if (feedt.getFeeType().toUpperCase().equals("ISS")) {
            FeeTypeIss feeTypeIss = new FeeTypeIss();
            feeTypeIss.setIssFixFee(rs.getDouble("n_fix_fee"));
            feeTypeIss.setIssPercentFee(rs.getDouble("n_percent_fee"));
            feeTypeIss.setIssFunctionName(rs.getString("function_name"));
            feeTypeIss.setIssFunctionInput(rs.getString("s_func_input"));
            feedt.setFeeTypeIss(feeTypeIss);
        } else if (feedt.getFeeType().toUpperCase().equals("INSTALLMENT")) {
            FeeTypeInstallment feeTypeInstallment = new FeeTypeInstallment();
            feeTypeInstallment.setIbFixFee(rs.getDouble("n_fix_fee"));
            feeTypeInstallment.setIbPercentFee(rs.getDouble("n_percent_fee"));
            feeTypeInstallment.setIbFunctionName(rs.getString("function_name"));
            feeTypeInstallment.setIbFunctionInput(rs.getString("s_func_input"));
            feedt.setFeeTypeInstallment(feeTypeInstallment);
        }

        return feedt;
    }

}
