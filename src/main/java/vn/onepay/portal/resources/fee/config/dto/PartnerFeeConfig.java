package vn.onepay.portal.resources.fee.config.dto;

import java.io.Serializable;

public class PartnerFeeConfig implements Serializable {
    private Integer id;
    private String partnerName;
    private String partnerType;
    private String partnerService;
    private String partnerContractType;
    private String partnerFeeType;
    private String updater;
    private String updateAt;
    private String partnerId;

    public PartnerFeeConfig() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType;
    }

    public String getPartnerService() {
        return partnerService;
    }

    public void setPartnerService(String partnerService) {
        this.partnerService = partnerService;
    }

    public String getPartnerContractType() {
        return partnerContractType;
    }

    public void setPartnerContractType(String partnerContractType) {
        this.partnerContractType = partnerContractType;
    }

    public String getPartnerFeeType() {
        return partnerFeeType;
    }

    public void setPartnerFeeType(String partnerFeeType) {
        this.partnerFeeType = partnerFeeType;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(String updateAt) {
        this.updateAt = updateAt;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }
}
