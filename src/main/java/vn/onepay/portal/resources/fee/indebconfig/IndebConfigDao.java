package vn.onepay.portal.resources.fee.indebconfig;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.FlowTypeDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.PartnerConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class IndebConfigDao extends Db {
    private static final String SEARCH_CONFIG = "{ call PKG_ONEFIN2.search_config_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    public static Map<String,Object> getListConfig(ConfigQueryDto queryDto) throws Exception {
        Exception exception = null;
        List<ConfigDto> config = new ArrayList<>();
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.name());
            cs.setString(5, queryDto.getFromDate());
            cs.setString(6, queryDto.getToDate());
            cs.setString(7, queryDto.getKeyword());
            cs.setInt(8, queryDto.getPage());
            cs.setInt(9, queryDto.getPageSize());
            cs.setString(10, queryDto.getPaymentPeriod());
            cs.setString(11, queryDto.getAdvanceBank());
            cs.setInt(12, queryDto.getState());
            cs.setString(13, queryDto.getService());
            cs.setString(14, queryDto.getContractType());
            cs.setString(15, queryDto.getGuaranteeAmount());
            cs.setString(16, queryDto.getGuaranteeHoldingType());
            cs.setString(17, queryDto.getWithoutMerchant());
            cs.setString(18, queryDto.getStatusConfig());
            cs.setString(19, queryDto.getStateOfEffective());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror ==500 ) {
                logger.log(Level.SEVERE, "DB get config list error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    config.add(blindConfig(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("intedConfig",config);
        return data;
    }

    public static List<ConfigDto> searchConfig(ConfigQueryDto queryDto) throws Exception {
        Exception exception = null;
        List<ConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.name());
            cs.setString(5, queryDto.getFromDate());
            cs.setString(6, queryDto.getToDate());
            cs.setString(7, queryDto.getKeyword());
            cs.setInt(8, queryDto.getPage());
            cs.setInt(9, queryDto.getPageSize());
            cs.setString(10, queryDto.getPaymentPeriod());
            cs.setString(11, queryDto.getAdvanceBank());
            cs.setInt(12, queryDto.getState());
            cs.setString(13, queryDto.getService());
            cs.setString(14, queryDto.getContractType());
            cs.setString(15, queryDto.getGuaranteeAmount());
            cs.setString(16, queryDto.getGuaranteeHoldingType());
            cs.setString(17, queryDto.getWithoutMerchant());
            cs.setString(18, queryDto.getStatusConfig());
            cs.setString(19, queryDto.getStateOfEffective());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror ==500 ) {
                logger.log(Level.SEVERE, "DB get config list error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    config.add(blindConfig(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return config;
    }

    public static int getTotal(ConfigQueryDto queryDto) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.name());
            cs.setString(5, queryDto.getFromDate());
            cs.setString(6, queryDto.getToDate());
            cs.setString(7, queryDto.getKeyword());
            cs.setInt(8, queryDto.getPage());
            cs.setInt(9, queryDto.getPageSize());
            cs.setString(10, queryDto.getPaymentPeriod());
            cs.setString(11, queryDto.getAdvanceBank());
            cs.setInt(12, queryDto.getState());
            cs.setString(13, queryDto.getService());
            cs.setString(14, queryDto.getContractType());
            cs.setString(15, queryDto.getGuaranteeAmount());
            cs.setString(16, queryDto.getGuaranteeHoldingType());
            cs.setString(17, queryDto.getWithoutMerchant());
            cs.setString(18, queryDto.getStatusConfig());
            cs.setString(19, queryDto.getStateOfEffective());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror ==500 ) {
                logger.log(Level.SEVERE, "DB get total config error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static int getIdDrafConfig(int id) throws Exception {
        Exception exception = null;
        int drafId = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_PARENTID_CONFIG(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            drafId = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return drafId;
    }
    public static List<FlowTypeDto> getListFlowType() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<FlowTypeDto> listfl = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_FLOW_TYPE(?,?,?)}");
            cs.registerOutParameter(1,OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    FlowTypeDto fl = new FlowTypeDto();
                    fl.setId(rs.getInt("N_ID"));
                    fl.setName(rs.getString("S_NAME"));
                    fl.setDesc(rs.getString("s_DESC"));
                    listfl.add(fl);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return listfl;
    }

    public static List<Map> getTemplateEmail() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map> listfl = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_EMAIL_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1,OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> fl = new HashMap<>();
                    fl.put("id",rs.getInt("N_ID"));
                    fl.put("name",rs.getString("S_NAME"));
                    fl.put("detail",rs.getString("S_DETAIL"));
                    listfl.add(fl);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return listfl;
    }

    public static List<Map> getTemplateFeeMonth() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map> listTemplate = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_FEE_MONTH_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB GET LIST TEMPLATE ERROR: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> fl = new HashMap<>();
                    fl.put("id",rs.getInt("N_ID"));
                    fl.put("name",rs.getString("S_NAME"));
                    listTemplate.add(fl);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return listTemplate;
    }

    public static void deleteConfig(int id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.delete_config(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.executeQuery();
            String error = cs.getString(2);
            int nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB delete config error: {0}", error);
            } 
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }


    public static List<ConfigDto> getConfigDetail(int id) throws Exception {
        Exception exception = null;
        List<ConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_INDEBCONFIG_DETAIL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get detail config error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    config.add(blindConfigdetail(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return config;
    }

    public static List<PartnerConfigDto> getListPartner() throws Exception {
        Exception exception = null;
        List<PartnerConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_LIST_PARTNER(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    PartnerConfigDto cf = new PartnerConfigDto();
                    cf.setPartnerId(rs.getInt("N_ID"));
                    cf.setPartnerName(rs.getString("S_PARTNER_NAME"));
                    cf.setShortName(rs.getString("S_SHORT_NAME"));
                    config.add(cf);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return config;
    }

    public static List<String> getListContractCode(String partner_id, String config_id, String type_get) throws Exception {
        Exception exception = null;
        List<String> contractCodeList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_LIST_CONTRACT_CODE(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Integer.parseInt(partner_id));
            cs.setInt(5, Integer.parseInt(config_id));
            cs.setString(6, type_get);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get CONTRACT CODE error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    contractCodeList.add(rs.getString("S_CONTRACT_CODE"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return contractCodeList;
    }

    public static List<Timestamp> getListContractDate(String partner_id, String config_id, String contract_code, String type_get) throws Exception {
        Exception exception = null;
        List<Timestamp> contractDateList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_LIST_CONTRACT_DATE(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Integer.parseInt(partner_id));
            cs.setInt(5, Integer.parseInt(config_id));
            cs.setString(6, contract_code);
            cs.setString(7, type_get);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get CONTRACT DATE error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    contractDateList.add(rs.getString("D_CONTRACT_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CONTRACT_DATE")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return contractDateList;
    }

    public static List<ConfigDto> getApproveWaiting() throws Exception {
        Exception exception = null;
        List<ConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_APPROVE_WAITING_CONFIG(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    ConfigDto cf = new ConfigDto();
                    cf.setId(rs.getLong("N_ID"));
                    cf.setParentId(rs.getInt("N_PARENT_ID"));
                    cf.setState(rs.getString("s_approve"));
                    config.add(cf);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return config;
    }
    public static Map<String,Object> approvalConfig(long id,String status) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 500;
        String error = "";
        Map<String,Object> temp = new HashMap<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.APPROVAL_INDEB_CONFIG(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.setString(4, status);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        temp.put("error",nerror + " :"+error);
        return temp;
    }
    public static Map<String,Object> approvalUpdateConfig(long id,int parentId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 500;
        String error = "";
        Map<String,Object> temp = new HashMap<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.APPROVAL_UPDATE_INDEB_CONFIG(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.setInt(4, parentId);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        temp.put("error",nerror + " :"+error);
        return temp;
    }
    public static Map<String,Object> approvalDeleteConfig(long id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 500;
        String error = "";
        Map<String,Object> temp = new HashMap<>();
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.APPROVAL_DELETE_INDEB_CONFIG(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, id);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        temp.put("error",nerror + " :"+error);
        return temp;
    }

    public static int updateInsertConfig(ConfigDto config, String fromDate, String toDate, String contractDate) throws Exception {
        Exception exception = null;
        int nerror = 500;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int id = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.INSERT_CONFIG(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setObject(4,config.getParentId());
            cs.setObject(5, config.getPartnerId());
            cs.setString(6, config.getContractCode());
            cs.setString(7, config.getTaxCode());
            cs.setInt(8, config.getAction());
            cs.setString(9, config.getPaymentPeriod());
            cs.setObject(10, config.getCutOffTime());
            cs.setObject(11, config.getAdvancePercent());
            cs.setString(12, config.getDayOfWeek());
            cs.setString(13, config.getDayOfMonth());
            cs.setString(14, config.getAdvanceAmount());
            cs.setString(15, config.getLastMerchantName());
            cs.setString(16, config.getTenDkkd());
            cs.setString(17, config.getBank());
            cs.setString(18, config.getAdvanceAccount());
            cs.setString(19, config.getBranchName());
            cs.setString(20, config.getAccountName());
            cs.setString(21, config.getAccountType());
            cs.setObject(22, config.getAccountNumber());
            cs.setObject(23, config.getGuaranteeAmount());
            cs.setObject(24, config.getGuaranteeHoldingType());
            cs.setObject(25, config.getFlowType());
            cs.setObject(26, config.getTemplateId());
            cs.setString(27, fromDate);
            cs.setString(28, toDate);
            cs.setString(29, config.getNotes());
            cs.setObject(30, config.getCheckEnoughGuarantee());
            cs.setString(31, config.getAccountingEntry());
            cs.setObject(32, config.getMinAdvanceAmount());
            cs.setString(33, config.getAdvanceVCB());
            cs.setString(34, config.getAdvanceTransFailed());
            cs.setInt(35, config.getTemplateFeeMonth());
            cs.setString(36, contractDate);
            cs.setString(37, config.getSeparateType());
            cs.setString(38, config.getAttachFile());
            cs.setString(39, config.getService() == null ? "" : config.getService());
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            id = cs.getInt(3);
            if (nerror == 0) {
                logger.severe("DB update config error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return id;
    }
    public static int updateDrafConfig(ConfigDto config,int drafId, String fromDate, String toDate, String contractDate) throws Exception {
        Exception exception = null;
        int nerror = 500;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_INDEB_CONFIG(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3,config.getParentId());
            cs.setObject(4, config.getPartnerId());
            cs.setString(5, config.getContractCode());
            cs.setString(6, config.getTaxCode());
            cs.setInt(7, config.getAction());
            cs.setString(8, config.getPaymentPeriod());
            cs.setObject(9, config.getCutOffTime());
            cs.setDouble(10, config.getAdvancePercent());
            cs.setString(11, config.getDayOfWeek());
            cs.setString(12, config.getDayOfMonth());
            cs.setString(13, config.getAdvanceAmount());
            cs.setString(14, config.getLastMerchantName());
            cs.setString(15, config.getTenDkkd());
            cs.setString(16, config.getBank());
            cs.setString(17, config.getAdvanceAccount());
            cs.setString(18, config.getBranchName());
            cs.setString(19, config.getAccountName());
            cs.setString(20, config.getAccountType());
            cs.setObject(21, config.getAccountNumber());
            cs.setObject(22, config.getGuaranteeAmount());
            cs.setObject(23, config.getGuaranteeHoldingType());
            cs.setInt(24, drafId);
            cs.setObject(25,config.getFlowType());
            cs.setObject(26,config.getTemplateId());
            cs.setString(27, fromDate);
            cs.setString(28, toDate);
            cs.setString(29, config.getNotes());
            cs.setInt(30, config.getCheckEnoughGuarantee());
            cs.setString(31, config.getAccountingEntry());
            cs.setDouble(32, config.getMinAdvanceAmount());
            cs.setString(33, config.getAdvanceVCB());
            cs.setString(34, config.getAdvanceTransFailed());
            cs.setInt(35, config.getTemplateFeeMonth());
            cs.setString(36, contractDate);
            cs.setString(37, config.getSeparateType());
            cs.setString(38, config.getAttachFile());
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB update config error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nerror;
    }
    private static ConfigDto blindConfig(ResultSet rs) throws Exception{
        ConfigDto config = new ConfigDto();
        config.setId(rs.getLong("N_ID"));
        config.setAdvanceAccount(rs.getString("s_advance_account")==null?"":rs.getString("s_advance_account"));
        config.setContractCode(rs.getString("s_contract_code"));
        config.setContractType(rs.getString("s_contract_type"));
        config.setLastMerchantName(rs.getString("S_TEN_DV"));
        config.setMerchantId(rs.getString("s_merchant_id")==null?"":rs.getString("s_merchant_id"));
        config.setPartnerName(rs.getString("s_partner_name"));
        config.setService(rs.getString("s_pay_channel"));
        config.setTenDkkd(rs.getString("s_ten_dkkd"));
        config.setPartnerId(rs.getInt("n_partner_id"));
        config.setDayOfWeek(rs.getString("S_DAY_OF_WEEK"));
        config.setDayOfMonth(rs.getString("S_DAY_OF_MONTH"));
        config.setCutOffTime(rs.getInt("N_HOUR"));
        config.setGuaranteeAmount(rs.getDouble("n_guarantee_destination"));
        config.setPaymentPeriod(rs.getString("s_type_advance"));
        config.setAdvancePercent(rs.getDouble("N_ADVANCE_PERCENT"));
        config.setAdvanceBank(rs.getString("S_ADV_ACC_BANK_ID"));
        config.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        config.setBranchName(rs.getString("S_BRANCH_NAME"));
        config.setParentId(rs.getInt("N_PARENT_ID"));
        config.setTaxCode(rs.getString("S_TAX_CODE"));
        config.setAccountType(rs.getString("S_ACCOUNT_TYPE"));
        config.setGuaranteeHoldingType(rs.getString("S_GUARANTEE_HOLDING_TYPE"));
        config.setAccountNumber(rs.getString("n_guarantee_account"));
        config.setState(rs.getString("s_approve")==null?"":rs.getString("s_approve"));
        config.setAction(rs.getObject("n_active")==null?2:rs.getInt("n_active"));
        config.setTemplateId(rs.getObject("N_TEMPLATE_EMAIL_ID")==null?0:rs.getInt("N_TEMPLATE_EMAIL_ID"));
        config.setTemplateName(rs.getString("N_TEMPLATE_NAME")==null?"":rs.getString("N_TEMPLATE_NAME"));
        config.setShortName(rs.getString("S_SHORT_NAME")==null?"":rs.getString("S_SHORT_NAME"));
        config.setEffectiveState(rs.getString("S_EFFECTIVE_STATE")==null?"":rs.getString("S_EFFECTIVE_STATE"));
        config.setFromDate(rs.getString("D_FROM") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_FROM")));
        config.setToDate(rs.getString("D_TO") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_TO")));
        config.setCreatedDate(rs.getString("D_CREATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CREATE")));
        config.setStatus(rs.getString("s_state"));
        return config;
    }
    private static ConfigDto blindConfigdetail(ResultSet rs) throws Exception{
        ConfigDto config = new ConfigDto();
        config.setId(rs.getLong("N_ID"));
        config.setAdvanceAccount(rs.getString("s_advance_account")==null?"":rs.getString("s_advance_account"));
        config.setContractCode(rs.getString("s_contract_code"));
        // config.setContractDate(rs.getString("D_CONTRACT_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_CONTRACT_DATE")));
        config.setContractDate(rs.getDate("D_CONTRACT_DATE") == null ? null : rs.getDate("D_CONTRACT_DATE").getTime());
        config.setContractType(rs.getString("s_contract_type"));
        config.setLastMerchantName(rs.getString("S_TEN_DV"));
        config.setService(rs.getString("s_pay_channel"));
        config.setTenDkkd(rs.getString("s_ten_dkkd"));
        config.setPartnerId(rs.getInt("n_partner_id"));
        config.setDayOfWeek(rs.getString("S_DAY_OF_WEEK"));
        config.setDayOfMonth(rs.getString("S_DAY_OF_MONTH"));
        config.setAdvanceAmount(rs.getString("n_advance_amount"));
        config.setCutOffTime(rs.getInt("N_HOUR"));
        config.setGuaranteeAmount(rs.getDouble("n_guarantee_destination"));
        config.setPaymentPeriod(rs.getString("s_type_advance"));
        config.setAdvancePercent(rs.getDouble("N_ADVANCE_PERCENT"));
        config.setAdvanceBank(rs.getString("S_ADV_ACC_BANK_ID"));
        config.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        config.setBranchName(rs.getString("S_BRANCH_NAME"));
        config.setParentId(rs.getInt("N_PARENT_ID"));
        config.setAction(rs.getInt("n_active"));
        config.setTaxCode(rs.getString("S_TAX_CODE"));
        config.setAccountType(rs.getString("S_ACCOUNT_TYPE"));
        config.setGuaranteeHoldingType(rs.getString("S_GUARANTEE_HOLDING_TYPE"));
        config.setAccountNumber(rs.getString("n_guarantee_account"));
        config.setFlowType(rs.getInt("n_flow_type_id"));
        config.setState(rs.getString("s_approve"));
        config.setTemplateId(rs.getObject("N_TEMPLATE_EMAIL_ID")==null?0:rs.getInt("N_TEMPLATE_EMAIL_ID"));
        config.setTemplateName(rs.getString("N_TEMPLATE_NAME")==null?"":rs.getString("N_TEMPLATE_NAME"));
        config.setFromDate(rs.getString("D_FROM") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_FROM")));
        config.setToDate(rs.getString("D_TO") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_TO")));
        config.setNotes(rs.getString("S_NOTES"));
        StringBuilder conditions = new StringBuilder();
        if (rs.getString("S_ADVANCE_VCB") != null && rs.getString("S_ADVANCE_TRANS_FAILED") != null) {
            conditions.append(rs.getString("S_ADVANCE_VCB"));
            conditions.append(",");
            conditions.append(rs.getString("S_ADVANCE_TRANS_FAILED"));
        } else if (rs.getString("S_ADVANCE_TRANS_FAILED") != null) {
            conditions.append(rs.getString("S_ADVANCE_TRANS_FAILED"));
        } else if (rs.getString("S_ADVANCE_VCB") != null) {
            conditions.append(rs.getString("S_ADVANCE_VCB"));
        }
        config.setConditions(conditions.toString());
        config.setCheckEnoughGuarantee(rs.getInt("N_ENOUGH_GUARANTEE"));
        config.setAccountingEntry(rs.getString("S_ACCOUNTING_ENTRY") == null ? "" : rs.getString("S_ACCOUNTING_ENTRY"));
        config.setMinAdvanceAmount(rs.getDouble("N_MIN_AMOUNT_ADV"));
        config.setTemplateFeeMonth(rs.getInt("N_TEMPLATE_FEE_MONTH"));
        config.setSeparateType(rs.getString("S_SEPARATE_TYPE"));
        config.setAttachFile(rs.getString("S_ATTACH_FILE"));
        return config;
    }
}
