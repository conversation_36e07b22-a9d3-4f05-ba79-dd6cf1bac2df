package vn.onepay.portal.resources.fee.acquirer;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.acquirer.dto.AcquirerDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class AcquirerDao extends Db implements IConstants {
    public static List<AcquirerDto> getAcquirer() throws Exception {
        Exception exception = null;
        List<AcquirerDto> accquirer = new ArrayList<>();;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_ACQUIRER(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    accquirer.add(blindAcquirer(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return accquirer;
    }
    private static AcquirerDto blindAcquirer(ResultSet rs) throws Exception{
        AcquirerDto acq = new AcquirerDto();
        acq.setAcquirerId(rs.getInt("N_ACQUIRER_ID"));
        acq.setAcquirerName(rs.getString("S_ACQUIRER_NAME"));
        acq.setDesciption(rs.getString("S_DESCRIPTION"));
        acq.setAcquirerShortName(rs.getString("S_ACQUIRER_SHORT_NAME"));
        acq.setAcquirerCode(rs.getString("S_ACQUIRER_CODE"));
        return acq;
    }
}
