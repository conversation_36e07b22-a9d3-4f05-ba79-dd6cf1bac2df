package vn.onepay.portal.resources.fee.merchantfee;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantFeeDto;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantFeeInputDto;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantOnePartnerDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MerchantFeeDao extends Db implements IConstants {
    public static Map<String,Object> getMerchantOnePartner(String keyword, int page, int page_size) throws Exception {
        Exception exception = null;
        Map<String,Object> data = new HashMap<>();
        List<MerchantOnePartnerDto> merchant = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_LIST_FEE_CONFIG(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, keyword);
            cs.setInt(6, page);
            cs.setInt(7, page_size);
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(2);
            if (nerror == 0) {
                logger.severe("DB get Fee Config List error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    merchant.add(bindMerchantfee(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("merchant",merchant);
        data.put("total",total);
        return data;
    }
    public static void updateMerchantFeeData(MerchantFeeInputDto feedata, String UserName,String contractCode) throws Exception{
        iMerchantFeeData(feedata,UserName,"wait_approval_for_update", contractCode);
    }
    public static void insertMerchantFeeData(MerchantFeeInputDto feedata,String UserName, String contractCode) throws Exception{
        iMerchantFeeData(feedata,UserName,"wait_approval_for_insert", contractCode);
    }

    public static void putMerchantFeeData(MerchantFeeInputDto feeData,String UserName,String contractCode) throws Exception{
        putMerchantFeeDataImpl(feeData, UserName, contractCode);
    }

    private static void putMerchantFeeDataImpl(MerchantFeeInputDto feedata,String UserName, String contractCode) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;

        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.PUT_MERCHANT_FEE_DATA(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, feedata.getId());
            cs.setString(4, feedata.getMerchantId());
            cs.setString(5, feedata.getService());
            cs.setString(6, feedata.getAcquirer());
            cs.setString(7, feedata.getPaygate());
            cs.setObject(8, feedata.getBinGroupId());
            cs.setString(9, feedata.getTransactionType());
            cs.setDouble(10, feedata.getFixFee() == null ? 0 : feedata.getFixFee());
            cs.setDouble(11, feedata.getPercentFee() == null ? 0 :feedata.getPercentFee());
            cs.setString(12, feedata.getFromDate());
            cs.setString(13, feedata.getToDate());
            cs.setString(14, feedata.getFuncId());
            cs.setString(15, feedata.getFuncInput());
            cs.setString(16, feedata.getType());
            cs.setObject(17, feedata.getOrder());
            cs.setString(18, feedata.getCurrency());
            cs.setString(19, feedata.getContractType());
            cs.setString(20, UserName);
            cs.setObject(21, feedata.getParentId());
            cs.setString(22, contractCode == null ? feedata.getContractCode() : contractCode);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB PUT merchant fee error: " + error);
            } else {
                logger.severe("Success");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    private static void iMerchantFeeData(MerchantFeeInputDto feedata,String UserName,String state, String contractCode) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;

        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.INSERT_MERCHANT_FEE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, feedata.getMerchantId());
            cs.setString(5, feedata.getService());
            cs.setString(6, feedata.getAcquirer());
            cs.setString(7, feedata.getPaygate());
            cs.setObject(8, feedata.getBinGroupId());
            cs.setString(9, feedata.getTransactionType());
            cs.setDouble(10, feedata.getFixFee() == null ? 0 : feedata.getFixFee());
            cs.setDouble(11, feedata.getPercentFee() == null ? 0 :feedata.getPercentFee());
            cs.setString(12, feedata.getFromDate());
            cs.setString(13, feedata.getToDate());
            cs.setString(14, feedata.getFuncId());
            cs.setString(15, feedata.getFuncInput());
            cs.setString(16, feedata.getType());
            cs.setObject(17, feedata.getOrder());
            cs.setString(18, feedata.getCurrency());
            cs.setString(19, feedata.getContractType());
            cs.setString(20, UserName);
            cs.setString(21, UserName);
            cs.setObject(22, feedata.getId());
            cs.setString(23, state);
            cs.setString(24, contractCode == null ? feedata.getContractCode() : contractCode);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB insert merchant fee error: " + error);
            } else {
                logger.severe("Success");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static void removeMerchantFee(int id) throws Exception {
        updateMerchantState(id, "wait_for_delete");
    }

    private static void updateMerchantState(int Id,String state) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_MERCHANT_FEE(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, Id);
            cs.setString(4, state);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static List<MerchantFeeDto> getMerchantFeeDetail(String templateId, String service) throws Exception {
        Exception exception = null;
        List<MerchantFeeDto> feedata = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_MERCHANT_FEE_DATA(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, templateId);
            cs.setString(5, service);
            cs.executeQuery();
            String error = cs.getString(3);
            int n_error = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (n_error == 0) {
                logger.severe("DB get Fee config error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    feedata.add(bindfeedata(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return feedata;
    }


    public static String updateContractCodeForMerchant(String merchant_id, String contractCode, String contractDate, String service) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result_update = null;
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.PUT_MERCHANT_CONTRACT_CODE(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, merchant_id);
            cs.setString(4, contractCode);
            cs.setString(5, contractDate);
            cs.setString(6, service);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("Update contract code error: " + error);
            } else {
                logger.severe("Susscess");
                result_update = "OK";
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result_update;
    }

    private static MerchantFeeDto bindfeedata(ResultSet rs) throws Exception{
        MerchantFeeDto mer = new MerchantFeeDto();
        mer.setId(rs.getInt("N_ID"));
        mer.setMerchantId(rs.getString("S_MERCHANT_ID"));
        mer.setService(rs.getString("S_SERVICE"));
        mer.setPaygate(rs.getString("S_PAYGATE"));
        mer.setAcquirer(rs.getString("S_ACQUIRER"));
        mer.setBinGroupId(rs.getInt("N_BIN_GROUP_ID"));
        mer.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        mer.setFixFee(rs.getDouble("N_FIX_FEE"));
        mer.setPercentFee(rs.getDouble("N_PERCENT_FEE"));
        mer.setFromDate(rs.getTimestamp("D_FROM_DATE"));
        mer.setToDate(rs.getTimestamp("D_TO_DATE"));
        mer.setFuncId(rs.getString("S_FUNC_ID"));
        mer.setFuncInput(rs.getString("S_FUNC_INPUT"));
        mer.setType(rs.getString("S_TYPE"));
        mer.setContractCode(rs.getString("S_CONTRACT_CODE"));
        mer.setCreateDate(rs.getTimestamp("D_CREATE"));
        mer.setUpdateDate(rs.getTimestamp("D_UPDATE"));
        mer.setUpdateByUserId(rs.getString("S_UPDATE"));
        mer.setCreateByUserId(rs.getString("S_CREATE"));
        mer.setOrder(rs.getInt("N_ORDER"));
        mer.setContractType(rs.getString("S_CONTRACT_TYPE"));
        mer.setCurrency(rs.getString("S_CURRENCY"));
        mer.setState(rs.getString("S_STATE"));
        mer.setParentId(rs.getString("N_PARENT_ID"));
        return mer;
    }
    private static MerchantOnePartnerDto bindMerchantfee(ResultSet rs) throws Exception{
        MerchantOnePartnerDto mer = new MerchantOnePartnerDto();
        mer.setMerchantId(rs.getString("S_MERCHANT_ID"));
        mer.setShortName(rs.getString("S_SHORT_NAME"));
        mer.setPaygate(rs.getString("S_PAYGATE"));
        mer.setContractCode(rs.getString("S_CONTRACT_CODE"));
        mer.setContractDate(rs.getTimestamp("D_CONTRACT_DATE"));
        mer.setMerchantNumber(rs.getString("S_MERCHANT_NO"));
        mer.setCurrency(rs.getString("CURRENCY"));
        mer.setMcc(rs.getString("MCC"));
        return mer;
    }
}
