package vn.onepay.portal.resources.fee.funcfee.dto;

public class FeeViewDto {

    private String merchantId;
    private String merchantName;
    private String feeType;
    private String binGroupName;
    private String contractType;
    private String payGate;
    private String acquirer;
    private String service;
    private String transactionType;
    private FeeTypeMerchant feeTypeMerchant;
    private FeeTypeAcq feeTypeAcq;
    private FeeTypeIss feeTypeIss;
    private FeeTypeInstallment feeTypeInstallment;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getBinGroupName() {
        return binGroupName;
    }

    public void setBinGroupName(String binGroupName) {
        this.binGroupName = binGroupName;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getPayGate() {
        return payGate;
    }

    public void setPayGate(String payGate) {
        this.payGate = payGate;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public FeeTypeMerchant getFeeTypeMerchant() {
        return feeTypeMerchant;
    }

    public void setFeeTypeMerchant(FeeTypeMerchant feeTypeMerchant) {
        this.feeTypeMerchant = feeTypeMerchant;
    }

    public FeeTypeAcq getFeeTypeAcq() {
        return feeTypeAcq;
    }

    public void setFeeTypeAcq(FeeTypeAcq feeTypeAcq) {
        this.feeTypeAcq = feeTypeAcq;
    }

    public FeeTypeIss getFeeTypeIss() {
        return feeTypeIss;
    }

    public void setFeeTypeIss(FeeTypeIss feeTypeIss) {
        this.feeTypeIss = feeTypeIss;
    }

    public FeeTypeInstallment getFeeTypeInstallment() {
        return feeTypeInstallment;
    }

    public void setFeeTypeInstallment(FeeTypeInstallment feeTypeInstallment) {
        this.feeTypeInstallment = feeTypeInstallment;
    }
}
