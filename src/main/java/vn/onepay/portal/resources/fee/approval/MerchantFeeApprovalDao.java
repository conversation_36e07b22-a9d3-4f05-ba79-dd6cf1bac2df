package vn.onepay.portal.resources.fee.approval;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantFeeDto;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MerchantFeeApprovalDao extends Db implements IConstants {
    public static Map<String,Object> getApprovalFeeMerchant(String contractType, String contractCode, String merchantId, String partnerName, String service, String acquirer, String payGate, Integer bin_group, String transactionType,int page,int page_size) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String,Object> data = new HashMap<>();
        List<MerchantFeeDto> merchantFeeApproval = new ArrayList<>();
        int total = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ONEFIN2.SEARCH_FEE_APPROVAL(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, page);
            cs.setInt(6, page_size);
            cs.setString(7, contractType);
            cs.setString(8, contractCode);
            cs.setString(9, merchantId);
            cs.setString(10, partnerName);
            cs.setString(11, service);
            cs.setString(12, acquirer);
            cs.setString(13, payGate);
            cs.setObject(14, bin_group);
            cs.setString(15, transactionType);
            cs.executeQuery();
            String error = cs.getString(4);
            total = cs.getInt(2);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get approve fee list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    merchantFeeApproval.add(blidApprovalMerchantfee(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("approval",merchantFeeApproval);
        data.put("total",total);
        return data;
    }
    public static void approvalMerchantState(String Id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_LIST_STATE_MERCHANT_FEE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, Id);
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                logger.severe("Susscess");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }
    private static MerchantFeeDto blidApprovalMerchantfee(ResultSet rs) throws Exception{
        MerchantFeeDto mer = new MerchantFeeDto();
        mer.setId(rs.getInt("N_ID"));
        mer.setMerchantId(rs.getString("S_MERCHANT_ID"));
        mer.setPartnerName(rs.getString("S_SHORT_NAME"));
        mer.setService(rs.getString("S_SERVICE"));
        mer.setPaygate(rs.getString("S_PAYGATE"));
        mer.setAcquirer(rs.getString("S_ACQUIRER"));
        mer.setBinGroupId(rs.getInt("N_BIN_GROUP_ID"));
        mer.setTransactionType(rs.getString("S_TRANSACTION_TYPE") == null ? "" : rs.getString("S_TRANSACTION_TYPE"));
        mer.setFixFee(rs.getDouble("N_FIX_FEE"));
        mer.setPercentFee(rs.getDouble("N_PERCENT_FEE"));
        mer.setFromDate(rs.getTimestamp("D_FROM_DATE"));
        mer.setToDate(rs.getTimestamp("D_TO_DATE"));
        mer.setFuncId(rs.getString("S_FUNC_ID"));
        mer.setFuncInput(rs.getString("S_FUNC_INPUT"));
        mer.setType(rs.getString("S_TYPE"));
        mer.setContractCode(rs.getString("CONTRACT_CODE"));
        mer.setCreateDate(rs.getTimestamp("D_CREATE"));
        mer.setUpdateDate(rs.getTimestamp("D_UPDATE"));
        mer.setUpdateByUserId(rs.getString("S_UPDATE"));
        mer.setCreateByUserId(rs.getString("S_CREATE"));
        mer.setOrder(rs.getInt("N_ORDER"));
        mer.setContractType(rs.getString("S_CONTRACT_TYPE"));
        mer.setCurrency(rs.getString("S_CURRENCY"));
        mer.setState(rs.getString("S_STATE"));
        mer.setParentId(rs.getString("N_PARENT_ID"));
        return mer;
    }
}
