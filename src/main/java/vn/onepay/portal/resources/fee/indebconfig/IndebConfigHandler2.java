package vn.onepay.portal.resources.fee.indebconfig;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import java.util.regex.Pattern;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.MerchantInfoDao;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportHandler;
import vn.onepay.portal.resources.payout.merchant_config.PayoutMerchantConfigDao;
import vn.onepay.portal.resources.user.UserDao;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import vn.onepay.portal.Util;
import static vn.onepay.portal.IConstants.*;
import static vn.onepay.portal.Util.sendResponse;

public class IndebConfigHandler2 {
    public static final String ID = "id";

    private IndebConfigHandler2() {}

    private static Logger logger = Logger.getLogger(IndebConfigHandler2.class.getName());
    private static final Gson gson = new Gson();

    public static void removeList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            SimpleDateFormat sm = new SimpleDateFormat("mm-dd-yyyy");
            try {
                JsonObject body = ctx.getBodyAsJson();
                JsonArray approvalId = body.getJsonArray("id");
                // Info
                String userId = ctx.get(S_USER_ID);
                String UserName = "";// UserDao.get(userId).getName();
                logger.log(Level.SEVERE, "Approve adv config ids:" + approvalId + ",user approve:" + UserName);
                for (Object id : approvalId.getList()) {
                    IndebConfigDao2.deleteConfig(Integer.parseInt(id.toString()));
                    logger.log(Level.SEVERE, "adv config ids deleted:" + approvalId + sm.format(new Date()).toString());
                }

                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    // @SuppressWarnings("unchecked")
    // public static void approvalList(RoutingContext ctx) {
    // ctx.vertx().executeBlocking(future -> {
    // Map<String, Object> temp = new HashMap<>();
    // try {
    // JsonObject body = ctx.getBodyAsJson();
    // List<String> approvalId = body.getJsonArray("id").getList();
    // for (String id : approvalId) {
    // IndebConfigDao2.approvalConfig(Integer.parseInt(id));
    // }
    // sendResponse(ctx, 200, temp);
    // } catch (Exception e) {
    // logger.log(Level.SEVERE, "", e);
    // ctx.fail(e);
    // }
    // }, false, null);
    // }

    @SuppressWarnings("unchecked")
    public static void approvalList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                JsonObject body = ctx.getBodyAsJson();
                List<String> approvalId = body.getJsonArray("id").getList();
                List<Map<String, Object>> parentConfigs = new ArrayList<>();
                for (String id : approvalId) {
                    int idInput = Integer.parseInt(id);
                    IndebConfigDao2.approvalConfig(idInput);
                    var check = IndebConfigDao2.checkIndebConfig(idInput);
                    if (check > 0) {
                        var parentConfig = IndebConfigDao2.getIndebConfig(idInput);
                        parentConfigs.add(parentConfig);
                    }
                }
                logger.info("list data: " + parentConfigs.size());
                if (!parentConfigs.isEmpty()) {
                    alertIndebConfigChangeInformation(parentConfigs);
                }
                sendResponse(ctx, 200, temp);

                // Tạo request tạo BBĐS có cấu hình tạm ứng tương ứng
                String configIds = approvalId.stream().collect(Collectors.joining(","));
                MonthlyFeeReportHandler.createMonthlyFeeReportRequest(configIds);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void alertIndebConfigChangeInformation(List<Map<String, Object>> listIndebtednessConfig) {
        DateFormat dateFormat;
        java.util.Date now = new java.util.Date();
        dateFormat = new SimpleDateFormat("HH:mm");
        String currentTime = dateFormat.format(now);
        dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        String currentDate = dateFormat.format(now);
        StringBuilder rows = new StringBuilder();
        String[] arrColumn = new String[] {"N_ID", "S_TEN_DKKD", "S_TEN_DKKD_OLD", "S_MERCHANT_IDS", "S_ADV_ACC_BANK_ID",
                "S_ADV_ACC_BANK_ID_OLD", "S_PAY_CHANNEL", "S_ADVANCE_ACCOUNT", "S_CONTRACT_CODE",
                "S_TYPE_ADVANCE"};
        int count = 0;
        for (Map<String, Object> m : listIndebtednessConfig) {
            rows.append("<tr>");
            rows.append("<td>" + count++ + "</td>");
            for (String column : arrColumn) {
                rows.append("<td>" + m.get(column) + "</td>");
            }
            rows.append("</tr>");
        }
        String contentHtml = "Dear OnePay Accountant team, " +
                "<br><br>Vào lúc " +
                currentTime +
                " ngày " +
                currentDate +
                " có cấu hình tạm ứng đã duyệt bị thay đổi thông tin tên đăng ký kinh doanh, ngân hàng chuyển." +
                "<br><br>Danh sách cụ thể như sau:<br><br>" +
                "<table border='1' cellspacing='3' cellpadding='5' style='border-collapse:collapse; table-layout:fixed; max-width:none; width:auto; min-width:72%;'>" +
                "<tr style='height:33px; text-align:center; color:black; font-weight:bold;'>" +
                "<th>STT</th>" +
                "<th>ID Config</th>" +
                "<th>Tên đăng ký kinh doanh</th>" +
                "<th>Tên đăng ký kinh doanh cũ</th>" +
                "<th>Merchant Ids</th>" +
                "<th>Ngân hàng nhận</th>" +
                "<th>Ngân hàng nhận cũ</th>" +
                "<th>Loại dịch vụ</th>" +
                "<th>Số tài khoản</th>" +
                "<th>Số hợp đồng</th>" +
                "<th>Hình thức tạm ứng</th>" +
                "</tr>"
                + rows +
                "</table>" +
                "<br><br>Thanks & Best Regards,<br>" +
                "--------------------------------<br>" +
                "<font color='#00549E'><b>OnePay System</b></font>";
        String host = Config.getString("advance-config.email.host", "");
        Integer port = Config.getInteger("advance-config.email.port", 25);
        String username = Config.getString("advance-config.email.username", "");
        String password = Config.getString("advance-config.email.password", "");
        String fromEmail = Config.getString("advance-config.email.from", "");
        String toEmail = Config.getString("advance-config.email.to", "");
        String ccEmail = Config.getString("advance-config.email.cc", "");
        try {
            MailUtil.sendMailFD(host, port, username, password, fromEmail,
                    // to
                    toEmail, ccEmail, "",
                    // subject
                    "Danh sách cấu hình tạm ứng thay đổi thông tin : " + listIndebtednessConfig.size() + " Accounts",
                    // content
                    contentHtml);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "error check missing account:", e);
        }
    }

    public static void getListConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                ConfigQueryDto config = new ConfigQueryDto();
                config.setKeyword(request.getParam(KEYWORD).length() == 0 ? null : request.getParam(KEYWORD));
                config.setPageSize(request.getParam(PAGE_SIZE).length() == 0 ? 50 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                config.setPage(request.getParam(PAGE).length() == 0 ? 0 : Integer.parseInt(request.getParam(PAGE)));
                String paymentPeriod = request.getParam("paymentPeriod");
                config.setPaymentPeriod(paymentPeriod.replaceAll("T 1", "T+1").replaceAll("T 2", "T+2").replaceAll("T 7", "T+7"));
                String advanceAccountTypes = Optional.ofNullable(request.getParam("advanceAccountTypes")).orElse("");
                config.setAdvanceAccountTypes(advanceAccountTypes);
                config.setAdvanceBank(request.getParam("advanceBank").length() == 0 ? null : Util.appendSearchOldName(request.getParam("advanceBank")));
                config.setOnepayBankId(request.getParam("onepayBankId").length() == 0 ? null : Util.appendSearchOldName(request.getParam("onepayBankId")));
                config.setState(request.getParam(STATE).length() == 0 ? null : Integer.parseInt(request.getParam(STATE)));
                config.setService(request.getParam(SERVICE).length() == 0 ? null : request.getParam(SERVICE));
                config.setFormula(request.getParam(FORMULA).length() == 0 ? null : request.getParam(FORMULA));
                config.setAdvanceAmount(request.getParam("advanceAmount").length() == 0 ? null : request.getParam("advanceAmount"));
                config.setContractType(request.getParam("contractType").length() == 0 ? null : request.getParam("contractType"));
                if (request.getParam("guaranteeAmount").length() == 0
                        || request.getParam("guaranteeAmount").equalsIgnoreCase("")
                        || request.getParam("guaranteeAmount").equalsIgnoreCase("NaN")) {
                    config.setGuaranteeAmount(null);
                } else if (request.getParam("guaranteeAmount").equalsIgnoreCase("Percent")) {
                    config.setGuaranteeAmount("1,5000,000 + 15%");
                } else {
                    config.setGuaranteeAmount(request.getParam("guaranteeAmount"));
                }
                config.setGuaranteeHoldingType(request.getParam("guaranteeHoldingType").length() == 0 ? null : request.getParam("guaranteeHoldingType"));
                config.setWithoutMerchant(request.getParam("withoutMerchant").length() == 0 ? null : request.getParam("withoutMerchant"));
                config.setMerchantIDWaitForApprove(request.getParam("merchantIDWaitForApprove").length() == 0 ? null : request.getParam("merchantIDWaitForApprove"));
                config.setStatusConfig(request.getParam("statusConfig").length() == 0 ? null : request.getParam("statusConfig"));
                config.setFromDate(request.getParam("fromdateF").length() == 0 ? null : request.getParam("fromdateF"));
                config.setToDate(request.getParam("todateF").length() == 0 ? null : request.getParam("todateF"));
                Map<String, Object> temp = new HashMap<>();

                temp = IndebConfigDao2.getListConfig(config);
                temp.put("total", IndebConfigDao2.getTotal(config));
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    @SuppressWarnings("unchecked")
    public static void getListAdvanceConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                ConfigQueryDto config = new ConfigQueryDto();
                Integer userId = Integer.parseInt(request.getParam(USER_ID));
                config.setKeyword(request.getParam(KEYWORD).length() == 0 ? null : request.getParam(KEYWORD));
                config.setPageSize(request.getParam(PAGE_SIZE).length() == 0 ? 50 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                config.setPage(request.getParam(PAGE).length() == 0 ? 0 : Integer.parseInt(request.getParam(PAGE)));
                Map<String, Object> temp = new HashMap<>();
                Integer totalPartnerId = 0;
                String provinceIds = MerchantInfoDao.getProvinceIdByUserId(userId);
                Map<String, Object> mapData = IndebConfigDao2.getPartnerIdByUserId(userId, provinceIds);
                List<Integer> listPartnerId = (List<Integer>) mapData.get("list");
                totalPartnerId = (Integer) mapData.get("total");
                config.setListPartnerId(listPartnerId);
                config.setTotalPartnerId(totalPartnerId);
                temp = IndebConfigDao2.getListAdvanceConfig(config);
                temp.put("total", IndebConfigDao2.getTotalAdvanceConfig(config));
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void dowloadConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject request = ctx.getBodyAsJson();


                ConfigQueryDto config = new ConfigQueryDto();
                config.setKeyword(request.getString(KEYWORD));
                // config.setPageSize(request.getInteger(PAGE_SIZE) == null ? 50 : request.getInteger(PAGE_SIZE));
                config.setPageSize(*********);
                // config.setPage(request.getInteger(PAGE) == null ? 0 : request.getInteger(PAGE));
                config.setPage(0);
                String paymentPeriod = request.getString("paymentPeriod");
                if (paymentPeriod == null || paymentPeriod.equalsIgnoreCase("")) {
                    config.setPaymentPeriod("");
                } else if (paymentPeriod.equalsIgnoreCase("T 1")) {
                    config.setPaymentPeriod("T+1");
                } else if (paymentPeriod.equalsIgnoreCase("T 2")) {
                    config.setPaymentPeriod("T+2");
                } else if (paymentPeriod.equalsIgnoreCase("T 7")) {
                    config.setPaymentPeriod("T+7");
                } else {
                    config.setPaymentPeriod(paymentPeriod);
                }

                StringJoiner advanceBank = new StringJoiner(",");
                JsonArray advanceBankArray = request.getJsonArray("advanceBank");
                if (advanceBankArray != null && advanceBankArray.size() > 0) {
                    for (int i = 0; i < advanceBankArray.size(); i++) {
                        advanceBank.add(advanceBankArray.getString(i));
                    }
                }
                StringJoiner onepayBankId = new StringJoiner(",");
                JsonArray onepayBankIdArray = request.getJsonArray("onepayBankId");
                if (onepayBankIdArray != null && onepayBankIdArray.size() > 0) {
                    for (int i = 0; i < onepayBankIdArray.size(); i++) {
                        onepayBankId.add(onepayBankIdArray.getString(i));
                    }
                }

                String advanceAccountTypes = request.getJsonArray("advanceAccountTypes", new JsonArray()).stream().map(Object::toString).collect(Collectors.joining(","));
                config.setAdvanceAccountTypes(advanceAccountTypes);
                config.setAdvanceBank(advanceBank == null ? "" : Util.appendSearchOldName(advanceBank.toString()));
                config.setOnepayBankId(onepayBankId == null ? "" : Util.appendSearchOldName(onepayBankId.toString()));
                config.setState(request.getString(STATE) == null ? null : Integer.parseInt(request.getString(STATE)));
                config.setService(request.getString(SERVICE) == null ? "" : request.getString(SERVICE));
                config.setContractType(request.getString("contractType") == null ? "" : request.getString("contractType"));
                if (request.getString("guaranteeAmount") == null || request.getString("guaranteeAmount").length() == 0
                        || request.getString("guaranteeAmount").equalsIgnoreCase("")
                        || request.getString("guaranteeAmount").equalsIgnoreCase("NaN")) {
                    config.setGuaranteeAmount(null);
                } else if (request.getString("guaranteeAmount").equalsIgnoreCase("Percent")) {
                    config.setGuaranteeAmount("1,5000,000 + 15%");
                } else {
                    config.setGuaranteeAmount(request.getString("guaranteeAmount"));
                }
                config.setGuaranteeHoldingType(request.getString("guaranteeHoldingType") == null ? "" : request.getString("guaranteeHoldingType"));
                config.setWithoutMerchant(request.getString("withoutMerchant") == null ? "" : request.getString("withoutMerchant"));
                config.setStatusConfig(request.getString("statusConfig") == null ? "" : request.getString("statusConfig"));
                config.setFromDate(request.getString("fromdateF").length() == 0 ? null : request.getString("fromdateF"));
                config.setToDate(request.getString("todateF").length() == 0 ? null : request.getString("todateF"));
                JsonArray formulaArray = new JsonArray();
                formulaArray = request.getJsonArray("formula");
                String formula = null;
                if (formulaArray != null && formulaArray.size() > 0) {
                    if (formulaArray.size() == 1 && formulaArray != null) {
                        formula = formulaArray.getString(0).toString();
                    } else if (formulaArray.size() == 2 && formulaArray != null) {
                        formula = formulaArray.getString(0).toString() + "," + formulaArray.getString(1).toString();
                    }
                }
                config.setFormula(formula == null ? null : formula);
                JsonArray advanceAmountArray = new JsonArray();
                advanceAmountArray = request.getJsonArray("advanceAmount");
                String advanceAmount = null;
                if (advanceAmountArray != null && advanceAmountArray.size() > 0) {
                    if (advanceAmountArray.size() == 1) {
                        advanceAmount = advanceAmountArray.getString(0).toString();
                    } else if (advanceAmountArray.size() == 2) {
                        advanceAmount = advanceAmountArray.getString(0).toString() + "," + advanceAmountArray.getString(1).toString();
                    } else if (advanceAmountArray.size() == 3) {
                        advanceAmount = advanceAmountArray.getString(0).toString() + "," + advanceAmountArray.getString(1).toString() + "," + advanceAmountArray.getString(2).toString();
                    }
                }
                config.setAdvanceAmount(advanceAmount);
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                int totalRows = IndebConfigDao2.getTotal(config);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                long date = new java.util.Date().getTime();
                String fileName = "Indebtebness_Config" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", config);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("Indebtebness_Config");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(config));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                    requestData.put(FILE_EXT, "xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                    requestData.put(FILE_EXT, ".xlsx");
                }
                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(config, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(config, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD Advance config Error: ", e);
                ctx.fail(e);
            }
        }, false, null);

    }

    public static void getDetailConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int id = Integer.parseInt(request.getParam("id"));
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("configDetail", IndebConfigDao2.getConfigDetail(id));
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void countWaitForApproveConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int id = Convert.parseInt(request.getParam("id"), 0);
            Map<String, Object> temp = new HashMap<>();
            try {
                Map<String, Object> res = IndebConfigDao2.countWaitForApproveConfig(id);
                temp.put("id", 0);
                temp.put("map_res", res);
                if (res != null && res.get("n_result") != null) {
                    logger.log(Level.SEVERE, "res:" + res);
                }
                temp.put("error", 200);
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListTemplateMail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("templateList", IndebConfigDao2.getTemplateEmail());
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListTemplateFeeMonth(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("templateList", IndebConfigDao2.getTemplateFeeMonth());
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getFlowType(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("listFlowType", IndebConfigDao2.getListFlowType());
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("listPartner", IndebConfigDao2.getListPartner());
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }

        }, false, null);
    }

    @SuppressWarnings("unchecked")
    public static void updateInsertConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> temp = new HashMap<>();
                JsonObject bodyJson = ctx.getBodyAsJson();
                ConfigDto config = new ConfigDto();
                config.setId(StringUtils.isBlank(bodyJson.getString("id")) ? 0 : Long.parseLong(bodyJson.getString("id")));
                config.setParentConfig(Integer.parseInt(bodyJson.getString("parentConfig")));
                config.setPartnerId(bodyJson.getString("partnerId").length() == 0 ? null : Integer.parseInt(bodyJson.getString("partnerId")));
                config.setContractCode(bodyJson.getString("contractCode").length() == 0 ? null : bodyJson.getString("contractCode"));
                config.setTaxCode(bodyJson.getString("taxCode").length() == 0 ? null : bodyJson.getString("taxCode"));
                config.setFormula(bodyJson.getString("formula").length() == 0 ? null
                        : bodyJson.getString(
                                "formula"));
                config.setAction(bodyJson.getString("action").length() == 0 ? 0 : Integer.parseInt(bodyJson.getString("action")));
                config.setCutOffTime(Convert.parseInt(bodyJson.getString("cutOffTime"), 0));
                String percentConfig = bodyJson.getString("advancePercent");
                String percent = percentConfig.replace("%", "");
                if (!percent.equals("") && !percent.equals("NaN")) {
                    config.setAdvancePercent(Double.parseDouble(percent));
                } else {
                    config.setAdvancePercent(100.0);
                }
                JsonArray arrayDayOfWeek = bodyJson.getJsonArray("dayOfWeek");
                if (arrayDayOfWeek != null) {
                    String dayOfWeek = String.join(",", arrayDayOfWeek.getList());
                    config.setDayOfWeek(dayOfWeek);
                }
                JsonArray arrayDayOfMonth = bodyJson.getJsonArray("dayOfMonth");
                if (arrayDayOfMonth != null && arrayDayOfMonth.size() > 0) {
                    String dayOfMonth = String.join(",", arrayDayOfMonth.getList());
                    config.setDayOfMonth(dayOfMonth);
                }
                String wayChargeFee = bodyJson.getString("advanceAmount").length() == 0 ? null : bodyJson.getString("advanceAmount");
                config.setAdvanceAmount(wayChargeFee);
                config.setLastMerchantName(bodyJson.getString("lastMerchantName").length() == 0 ? null : bodyJson.getString("lastMerchantName"));
                config.setTenDkkd(bodyJson.getString("tenDkkd").length() == 0 ? null : bodyJson.getString("tenDkkd"));
                config.setBank(bodyJson.getString("advanceBank").length() == 0 ? null : bodyJson.getString("advanceBank"));
                config.setAdvanceAccount(bodyJson.getString("advanceAccount").length() == 0 ? null : bodyJson.getString("advanceAccount"));
                config.setBranchName(bodyJson.getString("branchName").length() == 0 ? null : bodyJson.getString("branchName"));
                config.setAccountName(bodyJson.getString("accountName").length() == 0 ? null : bodyJson.getString("accountName"));
                config.setGuaranteeAmount(Convert.parseDouble(bodyJson.getString("guaranteeAmount").replaceAll(",", ""), 0));
                config.setGuaranteeHoldingType(bodyJson.getString("guaranteeHoldingType").length() == 0 ? null : bodyJson.getString("guaranteeHoldingType"));
                config.setFlowType(bodyJson.getString("flowType").length() == 0 ? null : Integer.parseInt(bodyJson.getString("flowType")));
                config.setTemplateId(bodyJson.getString("templateId").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateId")));
                config.setNotes(bodyJson.getString("notes"));
                config.setObjectFee(bodyJson.getString("objectFee"));
                // JsonArray conditions = bodyJson.getJsonArray("conditions");
                // if (conditions != null && conditions.size() > 0) {
                // for (Object element : conditions) {
                // if (element.equals("0")) {
                // config.setAdvanceVCB(element.toString());
                // } else if (element.equals("1")) {
                // config.setAdvanceTransFailed("YES");
                // }
                // }
                // }
                config.setCheckEnoughGuarantee(Boolean.FALSE.equals(bodyJson.getBoolean("checkEnoughGuarantee")) ? 0 : 1);
                config.setAccountingEntry(bodyJson.getString("accountingEntry"));
                config.setMinAdvanceAmount(bodyJson.getString("minAdvanceAmount").length() == 0 ? 0 : Double.parseDouble(bodyJson.getString("minAdvanceAmount")));
                config.setTemplateFeeMonth(bodyJson.getString("templateFeeMonth").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateFeeMonth")));
                config.setSeparateType(bodyJson.getString("separateType").length() == 0 ? null : bodyJson.getString("separateType"));
                config.setAttachFile(bodyJson.getString("attachFile").length() == 0 ? null : bodyJson.getString("attachFile"));
                config.setEmailFeeMonth(bodyJson.getString("emailFeeMonth"));
                config.setDividePCRF(bodyJson.getBoolean("dividePCRF", false));
                config.setAuthorizedPerson(bodyJson.getString("authorizedPerson"));
                config.setAuthorizedExpirationDate(bodyJson.getString("authorizedExpirationDate") == null ? null : Long.parseLong(bodyJson.getString("authorizedExpirationDate")));
                config.setGrabPromotion(bodyJson.getBoolean("grabPromotion", false));
                config.setCutoffFeeMonth(bodyJson.getInteger("cutoffFeeMonth"));
                config.setOnepayBankId(bodyJson.getString("onepayBankId"));

                JsonArray arrayTranType = bodyJson.getJsonArray("transactionType");
                String transactionType = "";
                if (arrayDayOfWeek != null) {
                    transactionType = String.join(",", arrayTranType.getList());
                }

                // build json data
                // json qt
                JsonObject jQTOld = null;
                if (bodyJson.getString("jQT") != null && bodyJson.getString("jQT").length() > 0)
                    jQTOld = new JsonObject(bodyJson.getString("jQT"));
                int activeQT = "inactive".equals(bodyJson.getString("paymentPeriod")) ? 0 : 1;
                JsonObject jQT = new JsonObject();
                jQT.put("active", activeQT);
                jQT.put("hour", Convert.parseInt(bodyJson.getString("cutOffTime"), 17));
                jQT.put("period", bodyJson.getString("hourPayment"));
                jQT.put("advance_type", activeQT == 1 ? bodyJson.getString("paymentPeriod") : "");
                jQT.put("txn_failed", bodyJson.getBoolean("advTranFailQT", false) == true ? "YES" : "NO");
                jQT.put("txn_types", transactionType);
                jQT.put("insert_adv_txn_func", getValueFromJson(jQTOld, "insert_adv_txn_func"));
                jQT.put("get_adv_txn_func", getValueFromJson(jQTOld, "get_adv_txn_func"));
                config.setJQT(jQT.toString());
                config.setAdvanceTransFailed(bodyJson.getBoolean("advTranFailQT", false) == true ? "YES" : "NO");
                // json nd
                JsonObject jNDOld = null;
                if (bodyJson.getString("jND") != null && bodyJson.getString("jND").length() > 0)
                    jNDOld = new JsonObject(bodyJson.getString("jND"));
                int activeND = "inactive".equals(bodyJson.getString("paymentPeriodND")) ? 0 : 1;
                JsonObject jND = new JsonObject();
                jND.put("active", activeND);
                jND.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeND"), 24));
                jND.put("period", bodyJson.getString("hourPaymentND"));
                jND.put("advance_type", activeND == 1 ? bodyJson.getString("paymentPeriodND") : "");
                jND.put("txn_types", transactionType);
                jND.put("insert_adv_txn_func", getValueFromJson(jNDOld, "insert_adv_txn_func"));
                jND.put("get_adv_txn_func", getValueFromJson(jNDOld, "get_adv_txn_func"));
                config.setJND(jND.toString());
                // json nd
                JsonObject jQROld = null;
                if (bodyJson.getString("jQR") != null && bodyJson.getString("jQR").length() > 0)
                    jQROld = new JsonObject(bodyJson.getString("jQR"));
                int activeQR = "inactive".equals(bodyJson.getString("paymentPeriodQR")) ? 0 : 1;
                JsonObject jQR = new JsonObject();
                jQR.put("active", activeQR);
                jQR.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeQR"), 24));
                jQR.put("period", bodyJson.getString("hourPaymentQR"));
                jQR.put("advance_type", activeQR == 1 ? bodyJson.getString("paymentPeriodQR") : "");
                jQR.put("txn_types", transactionType);
                jQR.put("insert_adv_txn_func", getValueFromJson(jQROld, "insert_adv_txn_func"));
                jQR.put("get_adv_txn_func", getValueFromJson(jQROld, "get_adv_txn_func"));
                config.setJQR(jQR.toString());
                // Billing
                JsonObject jBillingOld = null;
                if (bodyJson.getString("jBilling") != null && bodyJson.getString("jBilling").length() > 0)
                    jBillingOld = new JsonObject(bodyJson.getString("jBilling"));
                int activeBilling = "inactive".equals(bodyJson.getString("paymentPeriodBilling")) ? 0 : 1;
                JsonObject jBilling = new JsonObject();
                jBilling.put("active", activeBilling);
                jBilling.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeBilling"), 24));
                jBilling.put("period", bodyJson.getString("hourPaymentBilling"));
                jBilling.put("advance_type", activeBilling == 1 ? bodyJson.getString("paymentPeriodBilling") : "");
                jBilling.put("txn_types", transactionType);
                jBilling.put("insert_adv_txn_func", getValueFromJson(jBillingOld, "insert_adv_txn_func"));
                jBilling.put("get_adv_txn_func", getValueFromJson(jBillingOld, "get_adv_txn_func"));
                config.setJBilling(jBilling.toString());

                // jsonBNPL
                JsonObject jBNPLOld = null;
                if (bodyJson.getString("jBNPL") != null && bodyJson.getString("jBNPL").length() > 0)
                    jBNPLOld = new JsonObject(bodyJson.getString("jBNPL"));
                int activeBNPL = "inactive".equals(bodyJson.getString("paymentPeriodBNPL")) ? 0 : 1;
                JsonObject jBNPL = new JsonObject();
                jBNPL.put("active", activeBNPL);
                jBNPL.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeBNPL"), 24));
                jBNPL.put("period", bodyJson.getString("hourPaymentBNPL"));
                jBNPL.put("advance_type", activeBNPL == 1 ? bodyJson.getString("paymentPeriodBNPL") : "");
                jBNPL.put("txn_types", transactionType);
                jBNPL.put("insert_adv_txn_func", getValueFromJson(jBNPLOld, "insert_adv_txn_func"));
                jBNPL.put("get_adv_txn_func", getValueFromJson(jBNPLOld, "get_adv_txn_func"));
                config.setJBNPL(jBNPL.toString());
                JsonObject jUPOSOld = null;
                if (bodyJson.getString("jUPOS") != null && bodyJson.getString("jUPOS").length() > 0)
                    jUPOSOld = new JsonObject(bodyJson.getString("jUPOS"));
                int activeUPOS = "inactive".equals(bodyJson.getString("paymentPeriodUPOS")) ? 0 : 1;
                JsonObject jUPOS = new JsonObject();
                jUPOS.put("active", activeUPOS);
                jUPOS.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeUPOS"), 24));
                jUPOS.put("period", bodyJson.getString("hourPaymentUPOS"));
                jUPOS.put("advance_type", activeUPOS == 1 ? bodyJson.getString("paymentPeriodUPOS") : "");
                jUPOS.put("txn_types", transactionType);
                jUPOS.put("insert_adv_txn_func", getValueFromJson(jUPOSOld, "insert_adv_txn_func"));
                jUPOS.put("get_adv_txn_func", getValueFromJson(jUPOSOld, "get_adv_txn_func"));
                jUPOS.put("upos_type", bodyJson.getString("uposType"));
                config.setJUPOS(jUPOS.toString());

                JsonObject jPayCollectOld = null;
                if (bodyJson.getString("jPayCollect") != null && bodyJson.getString("jPayCollect").length() > 0)
                    jPayCollectOld = new JsonObject(bodyJson.getString("jPayCollect"));
                int activePayCollect = "inactive".equals(bodyJson.getString("paymentPeriodPayCollect")) ? 0 : 1;
                JsonObject jPayCollect = new JsonObject();
                jPayCollect.put("active", activePayCollect);
                jPayCollect.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimePayCollect"), 24));
                jPayCollect.put("period", bodyJson.getString("hourPaymentPayCollect"));
                jPayCollect.put("advance_type", activePayCollect == 1 ? bodyJson.getString("paymentPeriodPayCollect") : "");
                jPayCollect.put("txn_types", transactionType);
                jPayCollect.put("insert_adv_txn_func", getValueFromJson(jPayCollectOld, "insert_adv_txn_func"));
                jPayCollect.put("get_adv_txn_func", getValueFromJson(jPayCollectOld, "get_adv_txn_func"));
                config.setjPayCollect(jPayCollect.toString());
                // JsonObject jPayOut
                JsonObject jPayOutOld = null;
                if (bodyJson.getString("jPayOut") != null && bodyJson.getString("jPayOut").length() > 0)
                    jPayOutOld = new JsonObject(bodyJson.getString("jPayOut"));
                int activePayOut = "inactive".equals(bodyJson.getString("paymentPeriodPayOut")) ? 0 : 1;
                JsonObject jPayOut = new JsonObject();
                jPayOut.put("active", activePayOut);
                jPayOut.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimePayOut"), 24));
                jPayOut.put("period", bodyJson.getString("hourPaymentPayOut"));
                jPayOut.put("advance_type", activePayOut == 1 ? bodyJson.getString("paymentPeriodPayOut") : "");
                jPayOut.put("txn_types", transactionType);
                jPayOut.put("insert_adv_txn_func", getValueFromJson(jPayOutOld, "insert_adv_txn_func"));
                jPayOut.put("get_adv_txn_func", getValueFromJson(jPayOutOld, "get_adv_txn_func"));
                config.setjPayOut(jPayOut.toString());

                // JsonObject jVietQR
                JsonObject jVietQROld = null;
                if (bodyJson.getString("jVietQR") != null && bodyJson.getString("jVietQR").length() > 0)
                    jVietQROld = new JsonObject(bodyJson.getString("jVietQR"));
                int activeVietQR = "inactive".equals(bodyJson.getString("paymentPeriodVietQR")) ? 0 : 1;
                JsonObject jVietQR = new JsonObject();
                jVietQR.put("active", activeVietQR);
                jVietQR.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeVietQR"), 24));
                jVietQR.put("period", bodyJson.getString("hourPaymentVietQR"));
                jVietQR.put("advance_type", activeVietQR == 1 ? bodyJson.getString("paymentPeriodVietQR") : "");
                jVietQR.put("txn_types", transactionType);
                jVietQR.put("insert_adv_txn_func", getValueFromJson(jVietQROld, "insert_adv_txn_func"));
                jVietQR.put("get_adv_txn_func", getValueFromJson(jVietQROld, "get_adv_txn_func"));
                config.setjVietQR(jVietQR.toString());

                // JsonObject Direct Debit
                JsonObject jDDOld = null;
                if (bodyJson.getString("jDD") != null && bodyJson.getString("jDD").length() > 0)
                    jDDOld = new JsonObject(bodyJson.getString("jDD"));
                int activeDD = "inactive".equals(bodyJson.getString("paymentPeriodDD")) ? 0 : 1;
                JsonObject jDD = new JsonObject();
                jDD.put("active", activeDD);
                jDD.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeDD"), 24));
                jDD.put("period", bodyJson.getString("hourPaymentDD"));
                jDD.put("advance_type", activeDD == 1 ? bodyJson.getString("paymentPeriodDD") : "");
                jDD.put("txn_types", transactionType);
                jDD.put("insert_adv_txn_func", getValueFromJson(jDDOld, "insert_adv_txn_func"));
                jDD.put("get_adv_txn_func", getValueFromJson(jDDOld, "get_adv_txn_func"));
                config.setjDD(jDD.toString());

                JsonObject jSMSOld = null;
                if (bodyJson.getString("jSMS") != null && bodyJson.getString("jSMS").length() > 0)
                    jSMSOld = new JsonObject(bodyJson.getString("jSMS"));
                JsonObject jSMS = new JsonObject();
                jSMS.put("insert_adv_txn_func", getValueFromJson(jSMSOld, "insert_adv_txn_func"));
                jSMS.put("get_adv_txn_func", getValueFromJson(jSMSOld, "get_adv_txn_func"));
                config.setJSMS(jSMS.toString());

                String fileOld = "";
                String zipOld = "";
                String autoSendMail = "";
                if (bodyJson.getString("file") != null && bodyJson.getString("file").length() > 0)
                    fileOld = bodyJson.getString("file");
                if (bodyJson.getString("zip") != null && bodyJson.getString("zip").length() > 0)
                    zipOld = bodyJson.getString("zip");
                if (bodyJson.getString("autoSendMail") != null && bodyJson.getString("autoSendMail").length() > 0)
                    autoSendMail = bodyJson.getString("autoSendMail");

                JsonObject jNotification = new JsonObject();
                jNotification.put("file", fileOld);
                jNotification.put("zip", zipOld);
                jNotification.put("auto_send_mail", autoSendMail);
                config.setjNotification(jNotification.toString());
                config.setAutoHold(bodyJson.getBoolean("autoHold", true));
                // JsonObject jData = new JsonObject();
                // jData.put("auto_hold", config.isAutoHold() ? 1 : 0);
                String jDataStr = bodyJson.getString("jData");
                JsonObject jData = null;
                if (jDataStr != null) {
                    jData = new JsonObject(jDataStr);
                } else {
                    jData = new JsonObject();
                }
                jData.put("auto_hold", config.isAutoHold() ? 1 : 0);
                config.setjDataStr(jData.toString());


                String JMonthlyFee = "";
                if (bodyJson.getString("jMonthlyFee") != null && bodyJson.getString("jMonthlyFee").length() > 0)
                    JMonthlyFee = bodyJson.getString("jMonthlyFee");
                config.setJMonthlyFee(JMonthlyFee);
                // kỳ tạm ứng
                String typeAdvance = "";
                Map<String, String> mType = new HashMap<>();
                if (activeQT == 1)
                    mType.put(bodyJson.getString("paymentPeriod"), "1");
                if (activeND == 1)
                    mType.put(bodyJson.getString("paymentPeriodND"), "1");
                if (activeQR == 1)
                    mType.put(bodyJson.getString("paymentPeriodQR"), "1");
                if (activeBilling == 1)
                    mType.put(bodyJson.getString("paymentPeriodBilling"), "1");
                if (activeBNPL == 1)
                    mType.put(bodyJson.getString("paymentPeriodBNPL"), "1");
                if (activeUPOS == 1)
                    mType.put(bodyJson.getString("paymentPeriodUPOS"), "1");
                if (activePayCollect == 1)
                    mType.put(bodyJson.getString("paymentPeriodPayCollect"), "1");
                if (activePayOut == 1)
                    mType.put(bodyJson.getString("paymentPeriodPayOut"), "1");
                if (activeVietQR == 1)
                    mType.put(bodyJson.getString("paymentPeriodVietQR"), "1");
                if (activeDD == 1)
                    mType.put(bodyJson.getString("paymentPeriodDD"), "1");
                for (String key : mType.keySet()) {
                    if (typeAdvance.length() > 0)
                        typeAdvance += ",";
                    typeAdvance += key;
                }
                config.setPaymentPeriod(typeAdvance);
                String payChannel = "";
                if (activeQT == 1)
                    payChannel = "QT";
                if (activeND == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "ND";
                }
                if (activeQR == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "QR";
                }
                if (activeBilling == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "BL";
                }
                if (activeBNPL == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "BNPL";
                }
                if (activeUPOS == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "UPOS";
                }
                if (activePayCollect == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "PC";
                }
                if (activePayOut == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "PO";
                }
                if (activeVietQR == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "VIETQR";
                }
                if (activeDD == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "DD";
                }
                config.setService(payChannel);
                // json advance account
                Map<String, Object> map = Optional.ofNullable(bodyJson.getJsonObject("jAdvanceAccount")).orElse(new JsonObject()).getMap();
                map.put("id_bank_advance", bodyJson.getString("advanceBank"));
                map.put("bank_account_advance", bodyJson.getString("advanceAccount"));
                config.setJAdvanceAccount(map);
                // json hold
                JsonObject jHoldOld = null;
                if (bodyJson.getString("jHold") != null && bodyJson.getString("jHold").length() > 0)
                    jHoldOld = new JsonObject(bodyJson.getString("jHold"));
                JsonObject jHold = new JsonObject();
                jHold.put("active", config.getCheckEnoughGuarantee());
                // String type = config.getCheckEnoughGuarantee() == 1 &&
                // !bodyJson.getString("guaranteeHoldingType").equals("0") &&
                // !bodyJson.getString("guaranteeHoldingType").equals("101") ? "FD" : config.getAdvancePercent() <
                // 100 ? "HOLD" : "";
                String type = bodyJson.getString("rhold", "");
                jHold.put("type", type);
                int pecentAdv = type.equals("HOLD") ? Convert.parseInt(percent, 100) : 100 - Convert.parseInt(bodyJson.getString("guaranteeHoldingType"), 0);
                jHold.put("hold_percent", 100 - pecentAdv);
                jHold.put("advance_percent", pecentAdv);
                int activeHold = !"100".equals(percent) ? 1 : 0;
                jHold.put("hold_day", activeHold == 1 ? bodyJson.getInteger("holdDay") : 0);
                jHold.put("total_hold_amount", Convert.parseDouble(bodyJson.getString("guaranteeAmount").replaceAll(",", ""), 0));
                jHold.put("insert_hold_fd_func", getValueFromJson(jHoldOld, "insert_hold_fd_func"));
                jHold.put("insert_hold_func", getValueFromJson(jHoldOld, "insert_hold_func"));
                config.setJHold(jHold.toString());

                String advTime = "";
                if ((activeQT == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "DAILY";
                else if ((activeQT == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "WEEKLY";
                else if ((activeQT == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "HOURLY";
                config.setAdvanceTime(advTime);
                long idFeeMonth = 0;
                if (bodyJson.getString("idFeeMonth") != null && !"".equals(bodyJson.getString("idFeeMonth")))
                    idFeeMonth = Long.parseLong(bodyJson.getString("idFeeMonth"));
                config.setIdFeeMonth(idFeeMonth);
                config.setTransactionType(transactionType);
                config.setNotifyMethod(bodyJson.getString("notifyMethod"));
                int funcCreateAdvID = 2000;
                if ("Full amount - fixfee".equalsIgnoreCase(wayChargeFee))
                    funcCreateAdvID = 2001;
                if ("Full amount".equalsIgnoreCase(wayChargeFee))
                    funcCreateAdvID = 2002;
                config.setFunctionCreateAdv(funcCreateAdvID);// functionCreateAdv
                config.setFunctionCreateAdvParam(bodyJson.getString("functionCreateAdvParam"));
                config.setFunctionCreateFeeMonth(Integer.parseInt(bodyJson.getString("functionCreateFeeMonth")));
                config.setFunctionCreateFeeMonthParam(bodyJson.getString("functionCreateFeeMonthParam"));
                config.setReceiptType(bodyJson.getString("receiptType"));
                config.setMergeMonthlyReport(bodyJson.getString("mergeMonthlyReport"));
                config.setAddendum(bodyJson.getString("addendum").length() == 0 ? null : bodyJson.getString("addendum"));
                config.setControlMinutes(bodyJson.getString("controlMinutes").length() == 0 ? null : bodyJson.getString("controlMinutes"));

                // String contractDate = bodyJson.getString("contractDate");
                config.setContractDate(bodyJson.getString("contractDate") == null ? null : Long.parseLong(bodyJson.getString("contractDate")));
                config.setEffectiveDate(bodyJson.getString("effectiveDate") == null ? null : Long.parseLong(bodyJson.getString("effectiveDate")));
                config.setExpirationDate(bodyJson.getString("expirationDate") == null ? null : Long.parseLong(bodyJson.getString("expirationDate")));
                config.setCashFlow(bodyJson.getString("cashFlow"));
                config.setInvoiceType(bodyJson.getString("invoiceType"));
                config.setInvoiceInterval(bodyJson.getString("invoiceInterval"));
                
                // Auto configuration mapping
                config.setIsAutoEnabled(Integer.parseInt(getValueFromJson(bodyJson, "isAutoEnabled")));
                config.setAutoDays(getValueFromJson(bodyJson, "autoDays"));
                config.setAutoStepLevel(getValueFromJson(bodyJson, "autoStepLevel"));
                config.setEmailNotifyPayment(getValueFromJson(bodyJson, "emailNotifyPayment"));
                config.setEmailNotifyAccount(getValueFromJson(bodyJson, "emailNotifyAccount"));
                config.setEmailNotifyBod(getValueFromJson(bodyJson, "emailNotifyBod"));
                
                // Validate auto configuration if enabled
                if (config.getIsAutoEnabled() != null && "1".equals(config.getIsAutoEnabled())) {
                    if (config.getAutoDays() == null || config.getAutoDays().trim().isEmpty()) {
                        sendResponse(ctx, 400, "Ngày tự động tạm ứng không được để trống");
                        return;
                    }
                    if (config.getAutoStepLevel() == null || config.getAutoStepLevel().trim().isEmpty()) {
                        sendResponse(ctx, 400, "Bước tự động tạm ứng không được để trống");
                        return;
                    }
                    if (config.getEmailNotifyPayment() == null || config.getEmailNotifyPayment().trim().isEmpty()) {
                        sendResponse(ctx, 400, "Email thông báo payment không được để trống");
                        return;
                    }
                    if (config.getEmailNotifyAccount() == null || config.getEmailNotifyAccount().trim().isEmpty()) {
                        sendResponse(ctx, 400, "Email thông báo kế toán không được để trống");
                        return;
                    }
                    if (config.getEmailNotifyBod() == null || config.getEmailNotifyBod().trim().isEmpty()) {
                        sendResponse(ctx, 400, "Email thông báo BOD không được để trống");
                        return;
                    }
                    
                    // Validate email format
                    if (!isValidEmailList(config.getEmailNotifyPayment())) {
                        sendResponse(ctx, 400, "Email thông báo payment không đúng định dạng");
                        return;
                    }
                    if (!isValidEmailList(config.getEmailNotifyAccount())) {
                        sendResponse(ctx, 400, "Email thông báo kế toán không đúng định dạng");
                        return;
                    }
                    if (!isValidEmailList(config.getEmailNotifyBod())) {
                        sendResponse(ctx, 400, "Email thông báo BOD không đúng định dạng");
                        return;
                    }
                }
                
                // Info
                String userId = ctx.get(S_USER_ID);
                String UserName = UserDao.get(userId).getName();
                logger.log(Level.SEVERE, "update adv config id:" + config.getId() + ",user update:" + UserName);
                // Lấy id cấu hình đang chờ duyệt nếu có
                long drafId = 0;
                if (config.getId() != null) {
                    drafId = IndebConfigDao2.getIdDrafConfig(config.getId());
                }
                // check có merchant id nào giống merchant id cấu hình này, thuộc cấu hình khác, đã approved, có
                // transaction type
                // giống transaction type đang được đẩy vào DB (ko được phép như vậy). Số lượng>0 là sai
                Integer check = IndebConfigDao2.checkConflicIndebtednessConfig(config.getId().intValue(), config.getTransactionType());
                // Check merchant id đã cài đặt là 1 dịch vụ cụ thể (ví dụ QT) mà trên cấu hình ko active dịch vụ
                // tương ứng (QT) thì là sai checkServiceActive ra bằng 1
                Integer checkServiceActive = 0;
                List<MerchantConfigDto> merchantEmail = IndebConfigDao2.getListEmail(config.getParentConfig());
                for (MerchantConfigDto merchantConfigDto : merchantEmail) {
                    if (checkServiceActive == 0) {
                        if (merchantConfigDto.getService().equals("QT") && activeQT != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("ND") && activeND != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("QR") && activeQR != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("BNPL") && activeBNPL != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("UPOS") && activeUPOS != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("BL") && activeBilling != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("PC") && activePayCollect != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("PO") && activePayOut != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("VIETQR") && activeVietQR != 1) {
                            checkServiceActive = 1;
                        } else if (merchantConfigDto.getService().equals("DD") && activeDD != 1) {
                            checkServiceActive = 1;
                        }
                    }
                }
                if (check == 0) {
                    if (checkServiceActive == 0) {
                        // check draft
                        if (drafId == 0) {// ko có bản ghi chờ duyệt-> insert 1 bản ghi chờ duyệt
                            Map<String, Object> res = IndebConfigDao2.updateInsertConfig(config);
                            temp.put("id", 0);
                            temp.put("map_res", res);
                            // clone merchant id
                            if (res != null && res.get("n_result") != null) {
                                logger.log(Level.SEVERE, "res:" + res);
                            }
                            temp.put("error", 200);
                        } else {// có bản ghi chờ duyệt-> update bản ghi chờ duyệt
                            logger.log(Level.INFO, "config.getjVietQR()", config.getjVietQR());
                            Map<String, Object> res = IndebConfigDao2.updateDrafConfig(config, drafId);
                            temp.put("id", drafId);
                            temp.put("error", 200);
                            temp.put("map_res", res);
                            if (res != null && res.get("n_result") != null) {
                                logger.log(Level.SEVERE, "res:" + res);
                            }
                        }
                        sendResponse(ctx, 200, temp);
                    } else {
                        Map<String, Object> res = new HashMap<>();
                        res.put("n_result", 502);
                        temp.put("error", 200);
                        temp.put("map_res", res);
                        sendResponse(ctx, 200, temp);
                    }
                } else {
                    Map<String, Object> res = new HashMap<>();
                    res.put("n_result", 501);
                    temp.put("error", 200);
                    temp.put("map_res", res);
                    sendResponse(ctx, 200, temp);
                }

            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on  inser config", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateConfigApproved(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> temp = new HashMap<>();
                JsonObject bodyJson = ctx.getBodyAsJson();
                ConfigDto config = new ConfigDto();
                config.setId(StringUtils.isBlank(bodyJson.getString("id")) ? 0 : Long.parseLong(bodyJson.getString("id")));
                config.setParentConfig(Integer.parseInt(bodyJson.getString("parentConfig")));
                config.setTemplateId(bodyJson.getString("templateId").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateId")));
                config.setCutOffTime(Convert.parseInt(bodyJson.getString("cutOffTime"), 0));
                config.setEmailFeeMonth(bodyJson.getString("emailFeeMonth"));
                config.setTemplateFeeMonth(bodyJson.getString("templateFeeMonth").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateFeeMonth")));
                long idFeeMonth = 0;
                if (bodyJson.getString("idFeeMonth") != null && !"".equals(bodyJson.getString("idFeeMonth")))
                    idFeeMonth = Long.parseLong(bodyJson.getString("idFeeMonth"));
                config.setIdFeeMonth(idFeeMonth);
                config.setCutoffFeeMonth(bodyJson.getInteger("cutoffFeeMonth"));
                config.setNotifyMethod(bodyJson.getString("notifyMethod"));
                config.setFormula(bodyJson.getString("formula"));
                config.setReceiptType(bodyJson.getString("receiptType"));
                config.setMergeMonthlyReport(bodyJson.getString("mergeMonthlyReport"));
                config.setControlMinutes(bodyJson.getString("controlMinutes").length() == 0 ? null : bodyJson.getString("controlMinutes"));
                config.setExpirationDate(bodyJson.getString("expirationDate") == null ? null : Long.parseLong(bodyJson.getString("expirationDate")));
                String fileOld = "";
                String zipOld = "";
                String autoSendMail = "";
                if (bodyJson.getString("file") != null && bodyJson.getString("file").length() > 0)
                    fileOld = bodyJson.getString("file");
                if (bodyJson.getString("zip") != null && bodyJson.getString("zip").length() > 0)
                    zipOld = bodyJson.getString("zip");
                if (bodyJson.getString("autoSendMail") != null && bodyJson.getString("autoSendMail").length() > 0)
                    autoSendMail = bodyJson.getString("autoSendMail");
                JsonObject jNotification = new JsonObject();
                jNotification.put("file", fileOld);
                jNotification.put("zip", zipOld);
                jNotification.put("auto_send_mail", autoSendMail);
                config.setjNotification(jNotification.toString());
                // Info
                String userId = ctx.get(S_USER_ID);
                String UserName = UserDao.get(userId).getName();
                logger.log(Level.SEVERE, "update adv config id:" + config.getId() + ",user update:" + UserName);
                Map<String, Object> res = IndebConfigDao2.updateConfigApproved(config);
                temp.put("map_res", res);
                if (res != null && res.get("n_result") != null) {
                    logger.log(Level.SEVERE, "res:" + res);
                }
                temp.put("error", 200);
                sendResponse(ctx, 200, temp);

                // Tạo request tạo BBĐS có cấu hình tạm ứng tương ứng
                MonthlyFeeReportHandler.createMonthlyFeeReportRequest(config.getId().toString());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error update config approved", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * API check thay đổi cấu hình tạm ứng
     */
    @SuppressWarnings("unchecked")
    public static void checkChangeApprovedConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> temp = new HashMap<>();
                JsonObject bodyJson = ctx.getBodyAsJson();
                ConfigDto config = new ConfigDto();
                config.setId(StringUtils.isBlank(bodyJson.getString("id")) ? 0 : Long.parseLong(bodyJson.getString("id")));
                config.setParentConfig(Integer.parseInt(bodyJson.getString("parentConfig")));
                config.setPartnerId(bodyJson.getString("partnerId").length() == 0 ? null : Integer.parseInt(bodyJson.getString("partnerId")));
                config.setContractCode(bodyJson.getString("contractCode").length() == 0 ? null : bodyJson.getString("contractCode"));
                config.setTaxCode(bodyJson.getString("taxCode").length() == 0 ? null : bodyJson.getString("taxCode"));
                config.setFormula(bodyJson.getString("formula").length() == 0 ? null
                        : bodyJson.getString(
                                "formula"));
                config.setAction(bodyJson.getString("action").length() == 0 ? 0 : Integer.parseInt(bodyJson.getString("action")));
                config.setCutOffTime(Convert.parseInt(bodyJson.getString("cutOffTime"), 0));
                String percentConfig = bodyJson.getString("advancePercent");
                String percent = percentConfig.replace("%", "");
                if (!percent.equals("") && !percent.equals("NaN")) {
                    config.setAdvancePercent(Double.parseDouble(percent));
                } else {
                    config.setAdvancePercent(100.0);
                }
                JsonArray arrayDayOfWeek = bodyJson.getJsonArray("dayOfWeek");
                if (arrayDayOfWeek != null) {
                    String dayOfWeek = String.join(",", arrayDayOfWeek.getList());
                    config.setDayOfWeek(dayOfWeek);
                }
                JsonArray arrayDayOfMonth = bodyJson.getJsonArray("dayOfMonth");
                if (arrayDayOfMonth != null && arrayDayOfMonth.size() > 0) {
                    String dayOfMonth = String.join(",", arrayDayOfMonth.getList());
                    config.setDayOfMonth(dayOfMonth);
                }
                String wayChargeFee = bodyJson.getString("advanceAmount").length() == 0 ? null : bodyJson.getString("advanceAmount");
                config.setAdvanceAmount(wayChargeFee);
                config.setLastMerchantName(bodyJson.getString("lastMerchantName").length() == 0 ? null : bodyJson.getString("lastMerchantName"));
                config.setTenDkkd(bodyJson.getString("tenDkkd").length() == 0 ? null : bodyJson.getString("tenDkkd"));
                config.setBank(bodyJson.getString("advanceBank").length() == 0 ? null : bodyJson.getString("advanceBank"));
                config.setAdvanceAccount(bodyJson.getString("advanceAccount").length() == 0 ? null : bodyJson.getString("advanceAccount"));
                config.setBranchName(bodyJson.getString("branchName").length() == 0 ? null : bodyJson.getString("branchName"));
                config.setAccountName(bodyJson.getString("accountName").length() == 0 ? null : bodyJson.getString("accountName"));
                config.setGuaranteeAmount(Convert.parseDouble(bodyJson.getString("guaranteeAmount").replaceAll(",", ""), 0));
                config.setGuaranteeHoldingType(bodyJson.getString("guaranteeHoldingType").length() == 0 ? null : bodyJson.getString("guaranteeHoldingType"));
                config.setFlowType(bodyJson.getString("flowType").length() == 0 ? null : Integer.parseInt(bodyJson.getString("flowType")));
                config.setTemplateId(bodyJson.getString("templateId").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateId")));
                config.setNotes(bodyJson.getString("notes"));
                config.setCheckEnoughGuarantee(Boolean.FALSE.equals(bodyJson.getBoolean("checkEnoughGuarantee")) ? 0 : 1);
                config.setAccountingEntry(bodyJson.getString("accountingEntry"));
                config.setMinAdvanceAmount(bodyJson.getString("minAdvanceAmount").length() == 0 ? 0 : Double.parseDouble(bodyJson.getString("minAdvanceAmount")));
                config.setTemplateFeeMonth(bodyJson.getString("templateFeeMonth").length() == 0 ? null : Integer.parseInt(bodyJson.getString("templateFeeMonth")));
                config.setSeparateType(bodyJson.getString("separateType").length() == 0 ? null : bodyJson.getString("separateType"));
                config.setAttachFile(bodyJson.getString("attachFile").length() == 0 ? "" : bodyJson.getString("attachFile"));
                config.setEmailFeeMonth(bodyJson.getString("emailFeeMonth"));
                config.setDividePCRF(bodyJson.getBoolean("dividePCRF", false));
                config.setAuthorizedPerson(bodyJson.getString("authorizedPerson"));
                config.setAuthorizedExpirationDate(bodyJson.getString("authorizedExpirationDate") == null ? null : Long.parseLong(bodyJson.getString("authorizedExpirationDate")));
                config.setGrabPromotion(bodyJson.getBoolean("grabPromotion", false));
                config.setAutoHold(bodyJson.getBoolean("autoHold", true));
                config.setCutoffFeeMonth(bodyJson.getInteger("cutoffFeeMonth"));
                config.setOnepayBankId(bodyJson.getString("onepayBankId"));

                JsonArray arrayTranType = bodyJson.getJsonArray("transactionType");
                String transactionType = "";
                if (arrayDayOfWeek != null) {
                    transactionType = String.join(",", arrayTranType.getList());
                }

                // build json data
                // json qt
                JsonObject jQTOld = null;
                if (bodyJson.getString("jQT") != null && bodyJson.getString("jQT").length() > 0)
                    jQTOld = new JsonObject(bodyJson.getString("jQT"));
                int activeQT = "inactive".equals(bodyJson.getString("paymentPeriod")) ? 0 : 1;
                JsonObject jQT = new JsonObject();
                jQT.put("active", activeQT);
                jQT.put("hour", Convert.parseInt(bodyJson.getString("cutOffTime"), 17));
                jQT.put("period", bodyJson.getString("hourPayment"));
                jQT.put("advance_type", activeQT == 1 ? bodyJson.getString("paymentPeriod") : "");
                jQT.put("txn_failed", bodyJson.getBoolean("advTranFailQT", false) == true ? "YES" : "NO");
                jQT.put("txn_types", transactionType);
                jQT.put("insert_adv_txn_func", getValueFromJson(jQTOld, "insert_adv_txn_func"));
                jQT.put("get_adv_txn_func", getValueFromJson(jQTOld, "get_adv_txn_func"));
                config.setJQT(jQT.toString());
                config.setAdvanceTransFailed(bodyJson.getBoolean("advTranFailQT", false) == true ? "YES" : "NO");
                // json nd
                JsonObject jNDOld = null;
                if (bodyJson.getString("jND") != null && bodyJson.getString("jND").length() > 0)
                    jNDOld = new JsonObject(bodyJson.getString("jND"));
                int activeND = "inactive".equals(bodyJson.getString("paymentPeriodND")) ? 0 : 1;
                JsonObject jND = new JsonObject();
                jND.put("active", activeND);
                jND.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeND"), 24));
                jND.put("period", bodyJson.getString("hourPaymentND"));
                jND.put("advance_type", activeND == 1 ? bodyJson.getString("paymentPeriodND") : "");
                jND.put("txn_types", transactionType);
                jND.put("insert_adv_txn_func", getValueFromJson(jNDOld, "insert_adv_txn_func"));
                jND.put("get_adv_txn_func", getValueFromJson(jNDOld, "get_adv_txn_func"));
                config.setJND(jND.toString());
                // json nd
                JsonObject jQROld = null;
                if (bodyJson.getString("jQR") != null && bodyJson.getString("jQR").length() > 0)
                    jQROld = new JsonObject(bodyJson.getString("jQR"));
                int activeQR = "inactive".equals(bodyJson.getString("paymentPeriodQR")) ? 0 : 1;
                JsonObject jQR = new JsonObject();
                jQR.put("active", activeQR);
                jQR.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeQR"), 24));
                jQR.put("period", bodyJson.getString("hourPaymentQR"));
                jQR.put("advance_type", activeQR == 1 ? bodyJson.getString("paymentPeriodQR") : "");
                jQR.put("txn_types", transactionType);
                jQR.put("insert_adv_txn_func", getValueFromJson(jQROld, "insert_adv_txn_func"));
                jQR.put("get_adv_txn_func", getValueFromJson(jQROld, "get_adv_txn_func"));
                config.setJQR(jQR.toString());
                // Billing
                JsonObject jBillingOld = null;
                if (bodyJson.getString("jBilling") != null && bodyJson.getString("jBilling").length() > 0)
                    jBillingOld = new JsonObject(bodyJson.getString("jBilling"));
                int activeBilling = "inactive".equals(bodyJson.getString("paymentPeriodBilling")) ? 0 : 1;
                JsonObject jBilling = new JsonObject();
                jBilling.put("active", activeBilling);
                jBilling.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeBilling"), 24));
                jBilling.put("period", bodyJson.getString("hourPaymentBilling"));
                jBilling.put("advance_type", activeBilling == 1 ? bodyJson.getString("paymentPeriodBilling") : "");
                jBilling.put("txn_types", transactionType);
                jBilling.put("insert_adv_txn_func", getValueFromJson(jBillingOld, "insert_adv_txn_func"));
                jBilling.put("get_adv_txn_func", getValueFromJson(jBillingOld, "get_adv_txn_func"));
                config.setJBilling(jBilling.toString());

                // jsonBNPL
                JsonObject jBNPLOld = null;
                if (bodyJson.getString("jBNPL") != null && bodyJson.getString("jBNPL").length() > 0)
                    jBNPLOld = new JsonObject(bodyJson.getString("jBNPL"));
                int activeBNPL = "inactive".equals(bodyJson.getString("paymentPeriodBNPL")) ? 0 : 1;
                JsonObject jBNPL = new JsonObject();
                jBNPL.put("active", activeBNPL);
                jBNPL.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeBNPL"), 24));
                jBNPL.put("period", bodyJson.getString("hourPaymentBNPL"));
                jBNPL.put("advance_type", activeBNPL == 1 ? bodyJson.getString("paymentPeriodBNPL") : "");
                jBNPL.put("txn_types", transactionType);
                jBNPL.put("insert_adv_txn_func", getValueFromJson(jBNPLOld, "insert_adv_txn_func"));
                jBNPL.put("get_adv_txn_func", getValueFromJson(jBNPLOld, "get_adv_txn_func"));
                config.setJBNPL(jBNPL.toString());
                JsonObject jUPOSOld = null;
                if (bodyJson.getString("jUPOS") != null && bodyJson.getString("jUPOS").length() > 0)
                    jUPOSOld = new JsonObject(bodyJson.getString("jUPOS"));
                int activeUPOS = "inactive".equals(bodyJson.getString("paymentPeriodUPOS")) ? 0 : 1;
                JsonObject jUPOS = new JsonObject();
                jUPOS.put("active", activeUPOS);
                jUPOS.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeUPOS"), 24));
                jUPOS.put("period", bodyJson.getString("hourPaymentUPOS"));
                jUPOS.put("advance_type", activeUPOS == 1 ? bodyJson.getString("paymentPeriodUPOS") : "");
                jUPOS.put("txn_types", transactionType);
                jUPOS.put("insert_adv_txn_func", getValueFromJson(jUPOSOld, "insert_adv_txn_func"));
                jUPOS.put("get_adv_txn_func", getValueFromJson(jUPOSOld, "get_adv_txn_func"));
                jUPOS.put("upos_type", bodyJson.getString("uposType"));
                config.setJUPOS(jUPOS.toString());

                JsonObject jPayCollectOld = null;
                if (bodyJson.getString("jPayCollect") != null && bodyJson.getString("jPayCollect").length() > 0)
                    jPayCollectOld = new JsonObject(bodyJson.getString("jPayCollect"));
                int activePayCollect = "inactive".equals(bodyJson.getString("paymentPeriodPayCollect")) ? 0 : 1;
                JsonObject jPayCollect = new JsonObject();
                jPayCollect.put("active", activePayCollect);
                jPayCollect.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimePayCollect"), 24));
                jPayCollect.put("period", bodyJson.getString("hourPaymentPayCollect"));
                jPayCollect.put("advance_type", activePayCollect == 1 ? bodyJson.getString("paymentPeriodPayCollect") : "");
                jPayCollect.put("txn_types", transactionType);
                jPayCollect.put("insert_adv_txn_func", getValueFromJson(jPayCollectOld, "insert_adv_txn_func"));
                jPayCollect.put("get_adv_txn_func", getValueFromJson(jPayCollectOld, "get_adv_txn_func"));
                config.setjPayCollect(jPayCollect.toString());

                // VIETQR
                JsonObject jVietQROld = null;
                if (bodyJson.getString("jVietQR") != null && bodyJson.getString("jVietQR").length() > 0)
                    jVietQROld = new JsonObject(bodyJson.getString("jVietQR"));
                int activeVietQR = "inactive".equals(bodyJson.getString("paymentPeriodVietQR")) ? 0 : 1;
                JsonObject jVietQR = new JsonObject();
                jVietQR.put("active", activeVietQR);
                jVietQR.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeVietQR"), 24));
                jVietQR.put("period", bodyJson.getString("hourPaymentVietQR"));
                jVietQR.put("advance_type", activeVietQR == 1 ? bodyJson.getString("paymentPeriodVietQR") : "");
                jVietQR.put("txn_types", transactionType);
                jVietQR.put("insert_adv_txn_func", getValueFromJson(jVietQROld, "insert_adv_txn_func"));
                jVietQR.put("get_adv_txn_func", getValueFromJson(jVietQROld, "get_adv_txn_func"));
                config.setjVietQR(jVietQR.toString());

                // Direct Debit
                JsonObject jDDOld = null;
                if (bodyJson.getString("jDD") != null && bodyJson.getString("jDD").length() > 0)
                    jDDOld = new JsonObject(bodyJson.getString("jDD"));
                int activeDD = "inactive".equals(bodyJson.getString("paymentPeriodDD")) ? 0 : 1;
                JsonObject jDD = new JsonObject();
                jDD.put("active", activeDD);
                jDD.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimeDD"), 24));
                jDD.put("period", bodyJson.getString("hourPaymentDD"));
                jDD.put("advance_type", activeDD == 1 ? bodyJson.getString("paymentPeriodDD") : "");
                jDD.put("txn_types", transactionType);
                jDD.put("insert_adv_txn_func", getValueFromJson(jDDOld, "insert_adv_txn_func"));
                jDD.put("get_adv_txn_func", getValueFromJson(jDDOld, "get_adv_txn_func"));
                config.setjDD(jDD.toString());

                // payout
                JsonObject jPayOutOld = null;
                if (bodyJson.getString("jPayOut") != null && bodyJson.getString("jPayOut").length() > 0)
                    jPayOutOld = new JsonObject(bodyJson.getString("jPayOut"));
                int activePayOut = "inactive".equals(bodyJson.getString("paymentPeriodPayOut")) ? 0 : 1;
                JsonObject jPayOut = new JsonObject();
                jPayOut.put("active", activePayOut);
                jPayOut.put("hour", Convert.parseInt(bodyJson.getString("cutOffTimePayOut"), 24));
                jPayOut.put("period", bodyJson.getString("hourPaymentPayOut"));
                jPayOut.put("advance_type", activeUPOS == 1 ? bodyJson.getString("paymentPeriodPayOut") : "");
                jPayOut.put("txn_types", transactionType);
                jPayOut.put("insert_adv_txn_func", getValueFromJson(jPayOutOld, "insert_adv_txn_func"));
                jPayOut.put("get_adv_txn_func", getValueFromJson(jPayOutOld, "get_adv_txn_func"));
                config.setjPayCollect(jPayOut.toString());

                JsonObject jSMSOld = null;
                if (bodyJson.getString("jSMS") != null && bodyJson.getString("jSMS").length() > 0)
                    jSMSOld = new JsonObject(bodyJson.getString("jSMS"));
                JsonObject jSMS = new JsonObject();
                jSMS.put("insert_adv_txn_func", getValueFromJson(jSMSOld, "insert_adv_txn_func"));
                jSMS.put("get_adv_txn_func", getValueFromJson(jSMSOld, "get_adv_txn_func"));
                config.setJSMS(jSMS.toString());

                String fileOld = "";
                String zipOld = "";
                String autoSendMail = "";
                if (bodyJson.getString("file") != null && bodyJson.getString("file").length() > 0)
                    fileOld = bodyJson.getString("file");
                if (bodyJson.getString("zip") != null && bodyJson.getString("zip").length() > 0)
                    zipOld = bodyJson.getString("zip");
                if (bodyJson.getString("autoSendMail") != null && bodyJson.getString("autoSendMail").length() > 0)
                    autoSendMail = bodyJson.getString("autoSendMail");

                JsonObject jNotification = new JsonObject();
                jNotification.put("file", fileOld);
                jNotification.put("zip", zipOld);
                jNotification.put("auto_send_mail", autoSendMail);
                config.setjNotification(jNotification.toString());
                // kỳ tạm ứng
                String typeAdvance = "";
                Map<String, String> mType = new HashMap<>();
                if (activeQT == 1)
                    mType.put(bodyJson.getString("paymentPeriod"), "1");
                if (activeND == 1)
                    mType.put(bodyJson.getString("paymentPeriodND"), "1");
                if (activeQR == 1)
                    mType.put(bodyJson.getString("paymentPeriodQR"), "1");
                if (activeBilling == 1)
                    mType.put(bodyJson.getString("paymentPeriodBilling"), "1");
                if (activeBNPL == 1)
                    mType.put(bodyJson.getString("paymentPeriodBNPL"), "1");
                if (activeUPOS == 1)
                    mType.put(bodyJson.getString("paymentPeriodUPOS"), "1");
                if (activePayCollect == 1)
                    mType.put(bodyJson.getString("paymentPeriodPayCollect"), "1");
                if (activePayOut == 1)
                    mType.put(bodyJson.getString("paymentPeriodPayOut"), "1");
                if (activeVietQR == 1)
                    mType.put(bodyJson.getString("paymentPeriodVietQR"), "1");
                if (activeDD == 1)
                    mType.put(bodyJson.getString("paymentPeriodDD"), "1");
                for (String key : mType.keySet()) {
                    if (typeAdvance.length() > 0)
                        typeAdvance += ",";
                    typeAdvance += key;
                }
                config.setPaymentPeriod(typeAdvance);
                String payChannel = "";
                if (activeQT == 1)
                    payChannel = "QT";
                if (activeND == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "ND";
                }
                if (activeQR == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "QR";
                }
                if (activeBilling == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "BL";
                }
                if (activeBNPL == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "BNPL";
                }
                if (activeUPOS == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "UPOS";
                }
                if (activePayCollect == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "PC";
                }
                if (activePayOut == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "PO";
                }
                if (activeVietQR == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "VIETQR";
                }
                if (activeDD == 1) {
                    if (payChannel.length() > 0)
                        payChannel += ",";
                    payChannel += "DD";
                }
                config.setService(payChannel);
                // json advance account
                Map<String, Object> map = Optional.ofNullable(bodyJson.getJsonObject("jAdvanceAccount")).orElse(new JsonObject()).getMap();
                map.put("id_bank_advance", bodyJson.getString("advanceBank"));
                map.put("bank_account_advance", bodyJson.getString("advanceAccount"));
                config.setJAdvanceAccount(map);
                // json hold
                JsonObject jHoldOld = null;
                if (bodyJson.getString("jHold") != null && bodyJson.getString("jHold").length() > 0)
                    jHoldOld = new JsonObject(bodyJson.getString("jHold"));
                JsonObject jHold = new JsonObject();
                jHold.put("active", config.getCheckEnoughGuarantee());
                String type = config.getCheckEnoughGuarantee() == 1 && !bodyJson.getString("guaranteeHoldingType").equals("0") && !bodyJson.getString("guaranteeHoldingType").equals("101") ? "FD" : config.getAdvancePercent() < 100 ? "HOLD" : "";
                jHold.put("type", type);
                int pecentAdv = type.equals("HOLD") ? Convert.parseInt(percent, 100) : 100 - Convert.parseInt(bodyJson.getString("guaranteeHoldingType"), 0);
                jHold.put("hold_percent", 100 - pecentAdv);
                jHold.put("advance_percent", pecentAdv);
                int activeHold = !"100".equals(percent) ? 1 : 0;
                jHold.put("hold_day", activeHold == 1 ? bodyJson.getInteger("holdDay") : 0);
                jHold.put("total_hold_amount", Convert.parseDouble(bodyJson.getString("guaranteeAmount").replaceAll(",", ""), 0));
                jHold.put("insert_hold_fd_func", getValueFromJson(jHoldOld, "insert_hold_fd_func"));
                jHold.put("insert_hold_func", getValueFromJson(jHoldOld, "insert_hold_func"));
                config.setJHold(jHold.toString());

                String advTime = "";
                if ((activeQT == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",T+1,T+2,T+7,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "DAILY";
                else if ((activeQT == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",Hàng tuần,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "WEEKLY";
                else if ((activeQT == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriod") + ",") >= 0)
                        || (activeND == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodND") + ",") >= 0)
                        || (activeBNPL == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodBNPL") + ",") >= 0)
                        || (activeUPOS == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodUPOS") + ",") >= 0)
                        || (activeQR == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodQR") + ",") >= 0)
                        || (activeBilling == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodBilling") + ",") >= 0)
                        || (activePayCollect == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodPayCollect") + ",") >= 0)
                        || (activePayOut == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodPayOut") + ",") >= 0)
                        || (activeVietQR == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodVietQR") + ",") >= 0)
                        || (activeDD == 1 && (",T,").indexOf("," + bodyJson.getString("paymentPeriodDD") + ",") >= 0))
                    advTime = "HOURLY";
                config.setAdvanceTime(advTime);
                long idFeeMonth = 0;
                if (bodyJson.getString("idFeeMonth") != null && !"".equals(bodyJson.getString("idFeeMonth")))
                    idFeeMonth = Long.parseLong(bodyJson.getString("idFeeMonth"));
                config.setIdFeeMonth(idFeeMonth);
                config.setTransactionType(transactionType);
                config.setNotifyMethod(bodyJson.getString("notifyMethod"));
                int funcCreateAdvID = 2000;
                if ("Full amount - fixfee".equalsIgnoreCase(wayChargeFee))
                    funcCreateAdvID = 2001;
                if ("Full amount".equalsIgnoreCase(wayChargeFee))
                    funcCreateAdvID = 2002;
                config.setFunctionCreateAdv(funcCreateAdvID);
                config.setFunctionCreateAdvParam(bodyJson.getString("functionCreateAdvParam"));
                config.setFunctionCreateFeeMonth(Integer.parseInt(bodyJson.getString("functionCreateFeeMonth")));
                config.setFunctionCreateFeeMonthParam(bodyJson.getString("functionCreateFeeMonthParam"));
                config.setReceiptType(bodyJson.getString("receiptType"));
                config.setMergeMonthlyReport(bodyJson.getString("mergeMonthlyReport"));
                config.setAddendum(bodyJson.getString("addendum").length() == 0 ? null : bodyJson.getString("addendum"));
                config.setControlMinutes(bodyJson.getString("controlMinutes").length() == 0 ? null : bodyJson.getString("controlMinutes"));

                config.setContractDate(bodyJson.getString("contractDate") == null ? null : Long.parseLong(bodyJson.getString("contractDate")));
                config.setEffectiveDate(bodyJson.getString("effectiveDate") == null ? null : Long.parseLong(bodyJson.getString("effectiveDate")));
                config.setExpirationDate(bodyJson.getString("expirationDate") == null ? null : Long.parseLong(bodyJson.getString("expirationDate")));
                config.setInvoiceType(bodyJson.getString("invoiceType") == null ? BLANK : bodyJson.getString("invoiceType"));
                config.setInvoiceInterval(bodyJson.getString("invoiceInterval") == null ? BLANK : bodyJson.getString("invoiceInterval"));
                Map<String, Object> res = IndebConfigDao2.checkChangeApprovedConfig(config);
                temp.put("map_res", res);
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error on  check change approved config", e);
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void getListContractCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            HttpServerRequest request = ctx.request();
            try {
                temp.put("listContractCode", IndebConfigDao2.getListContractCode(request.getParam("partner_id"), request.getParam("config_id"), request.getParam("type_get")));
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantIdNotInConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int partnerId = Integer.parseInt(request.getParam("id"));
                int indebtednessConfigId = Integer.parseInt(request.getParam("indebtednessId"));
                String service = request.getParam(SERVICE);
                sendResponse(ctx, 200, Map.of("list", IndebConfigDao2.listMerchantIdNotInConfig(partnerId, service, indebtednessConfigId)));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getConfigMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int id = Integer.parseInt(request.getParam("id"));
            Map<String, Object> temp = new HashMap<>();
            try {
                List<MerchantConfigDto> config = IndebConfigDao2.getListEmail(id);
                temp.put("ListEmail", config);
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    @SuppressWarnings("unchecked")
    public static void updateInsertMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                String userId = ctx.get(S_USER_ID);
                String userEmail = UserDao.get(userId).getEmail();
                // List<MerchantConfigDto> configs = new ArrayList<>();
                List<MerchantConfigDto> configEmailAlerts = new ArrayList<>();
                Map<String, List<MerchantConfigDto>> emailAlertMap = new HashMap<>();
                MerchantConfigDto config = new MerchantConfigDto();
                config.setMerchantIds(body.getJsonArray(MERCHANT_IDS).getList());
                // config.setMerchantId(body.getString(MERCHANTID));
                config.setEmail(body.getString(EMAIL));
                config.setActive(body.getString(ACTIVE));
                config.setState(body.getString(STATE));
                config.setService(body.getString(SERVICE));
                int id = Integer.parseInt(request.getParam("id"));
                logger.log(Level.INFO, config.getState());
                logger.log(Level.INFO, config.getService());
                Integer check = 0;
                Integer checkNotChange = 0;
                Integer checkUpdateConfig = 0;
                String action = "INSERT-UPDATE";

                for (String merchantIdService : config.getMerchantIds()) {
                    config.setMerchantId(merchantIdService.split("-")[0]);
                    config.setService(merchantIdService.split("-")[1].replace("Direct Debit", "DD"));
                    MerchantConfigDto configDtoApproved = IndebConfigDao2.getConfigMerchantApproved(config, id);
                    if (configDtoApproved.getEmail() != null
                            && configDtoApproved.getEmail().equals(config.getEmail())
                            && configDtoApproved.getActive() != null
                            && configDtoApproved.getActive().equals(config.getActive())
                            && configDtoApproved.getMerchantId() != null
                            && configDtoApproved.getMerchantId().equals(config.getMerchantId())
                            && configDtoApproved.getService() != null
                            && configDtoApproved.getService().equals(config.getService())) {
                        temp.put("code", 201);
                        temp.put("mess", "Không có gì thay đổi");
                        sendResponse(ctx, 200, temp);
                    } else {

                        checkNotChange = 1;
                        configEmailAlerts.add(configDtoApproved);
                        configEmailAlerts.add(config);
                        checkUpdateConfig = IndebConfigDao2.checkUpdateConfigMerchant(id, config);
                        if (body.getString("action").equals("UPDATE")) {
                            action = "UPDATE";
                        } else {
                            action = "INSERT";
                        }
                        if (checkUpdateConfig == 0) {
                            check = IndebConfigDao2.insertUpdateConfigMerchant(config, id, action);
                            if (check == 0) {
                                sendResponse(ctx, 500, temp);
                            } else if (check == 502) {
                                temp.put("code", 502);
                                sendResponse(ctx, 200, temp);
                            }
                            emailAlertMap.put(config.getMerchantId() + "/" + id + ";" + config.getService(), configEmailAlerts);
                        } else {
                            temp.put("code", 501);
                            temp.put("mess", "trung merchantId");
                            sendResponse(ctx, 200, temp);
                        }
                    }
                }

                if (check != 0 && check != 502 && checkNotChange != 0) {
                    temp.put("code", 200);
                    temp.put("mess", "OK");
                    sendResponse(ctx, 200, temp);
                    sendEmailWhenActionConfigMerchant(userEmail, emailAlertMap, action);
                    insertConfigMerchantLog(userEmail, emailAlertMap, "INSERT-UPDATE");
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                String userId = ctx.get(S_USER_ID);
                String userEmail = UserDao.get(userId).getEmail();
                List<MerchantConfigDto> configs = new ArrayList<>();
                List<MerchantConfigDto> configEmailAlerts = new ArrayList<>();
                Map<String, List<MerchantConfigDto>> emailAlertMap = new HashMap<>();
                JsonArray a = body.getJsonArray("list");
                for (Object b : a) {
                    JsonObject data = (JsonObject) b;
                    MerchantConfigDto config = new MerchantConfigDto();
                    config.setMerchantId(data.getString(MERCHANTID));
                    config.setEmail(data.getString(EMAIL));
                    config.setActive(data.getString(ACTIVE));
                    config.setState("wait_for_approve_delete");
                    config.setService(data.getString(SERVICE).replace("Direct Debit", "DD"));
                    config.setCurrency(data.getString(CURRENCY));
                    configs.add(config);
                }
                int id = Integer.parseInt(request.getParam("id"));
                Integer check = 0;
                for (MerchantConfigDto configDto : configs) {
                    check = IndebConfigDao2.insertUpdateConfigMerchant(configDto, id, "DELETE");
                    if (check == 0) {
                        sendResponse(ctx, 500, temp);
                    } else {
                        configEmailAlerts.add(IndebConfigDao2.getConfigMerchantApproved(configDto, id));
                        configEmailAlerts.add(configDto);
                        emailAlertMap.put(configDto.getMerchantId() + "/" + id + ";" + configDto.getService(), configEmailAlerts);
                    }
                }
                if (check == 0) {
                    sendResponse(ctx, 500, temp);
                } else {
                    sendResponse(ctx, 200, temp);
                    sendEmailWhenActionConfigMerchant(userEmail, emailAlertMap, "DELETE");
                    insertConfigMerchantLog(userEmail, emailAlertMap, "DELETE");
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approvalMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                String userId = ctx.get(S_USER_ID);
                String userEmail = UserDao.get(userId).getEmail();
                List<MerchantConfigDto> configs = new ArrayList<>();
                List<MerchantConfigDto> configEmailAlerts = new ArrayList<>();
                Map<String, List<MerchantConfigDto>> emailAlertMap = new HashMap<>();
                JsonArray a = body.getJsonArray("list");
                String approvedStatus = body.getString(APPROVE);
                for (Object b : a) {
                    JsonObject data = (JsonObject) b;
                    MerchantConfigDto config = new MerchantConfigDto();
                    config.setMerchantId(data.getString(MERCHANTID));
                    config.setEmail(data.getString(EMAIL));
                    config.setActive(data.getString(ACTIVE));
                    config.setState(data.getString(STATE));
                    config.setService(data.getString(SERVICE).replace("Direct Debit", "DD"));
                    config.setCurrency(data.getString(CURRENCY));
                    configs.add(config);
                }
                int id = Integer.parseInt(request.getParam("id"));
                Integer check = 0;
                if (approvedStatus.toUpperCase().equals("APPROVE")) {
                    check = IndebConfigDao2.checkConflicConfigMerchant(id);
                }
                if (check == 0) {
                    for (MerchantConfigDto configDto : configs) {
                        configEmailAlerts.add(IndebConfigDao2.getConfigMerchantApproved(configDto, id));
                        configEmailAlerts.add(configDto);
                        emailAlertMap.put(configDto.getMerchantId() + "/" + id + ";" + configDto.getService(), configEmailAlerts);

                        IndebConfigDao2.approvedConfigMerchant(configDto, id, approvedStatus);
                    }
                    temp.put("code", 200);
                    temp.put("mess", "ok");
                    sendResponse(ctx, 200, temp);
                    sendEmailWhenActionConfigMerchant(userEmail, emailAlertMap, approvedStatus.toUpperCase());
                    insertConfigMerchantLog(userEmail, emailAlertMap, approvedStatus.toUpperCase());
                } else {
                    temp.put("code", 501);
                    temp.put("mess", "trung merchantId");
                    sendResponse(ctx, 200, temp);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void sendEmailWhenActionConfigMerchant(String userEmail, Map<String, List<MerchantConfigDto>> emailAlert, String action) {
        try {
            String tittleContent = "Điều chỉnh";
            if (action.equals("INSERT")) {
                tittleContent = "Thêm mới";
            } else if (action.equals("DELETE")) {
                tittleContent = "Xóa";
            } else {
                tittleContent = "Cập nhật";
            }
            String tittle = tittleContent + " email thông báo tạm ứng";
            String content = "Dear All,\n\n";
            content += "Tài khoản thao tác: " + userEmail + "\n";
            content += "Thao tác: " + action + "\n";
            content += "Thông báo chi tiết: " + "\n";
            content += "\n";
            List<String> a = new ArrayList<>();
            emailAlert.forEach((k, v) -> {
                a.add(" Merchant ID: " + k.split("/")[0] + "\n");
                a.add(" Dịch vụ: " + k.split(";")[1] + "\n");
                if (v.size() > 1) {
                    a.add(" Email: " + v.get(0).getEmail() + " -> " + v.get(1).getEmail() + " \n");
                } else if (v.size() == 1) {
                    a.add(" Email: " + v.get(0).getEmail() + " \n");
                }

                if (v.size() > 1) {
                    a.add(" Trạng thái: " + v.get(0).getActive() + " -> " + v.get(1).getActive() + "\n");
                } else if (v.size() == 1) {
                    a.add(" Trạng thái: " + v.get(0).getActive() + "\n");
                }


                if (v.size() > 1) {
                    a.add(" Tiền tệ: " + v.get(0).getCurrency() + " -> " + v.get(1).getCurrency() + "\n");
                } else if (v.size() == 1) {
                    a.add(" Tiền tệ: " + v.get(0).getCurrency() + "\n");
                }


                if (v.size() > 1) {
                    if (action.equals("APPROVE")) {
                        a.add(" State: " + v.get(1).getState() + " -> " + v.get(0).getState() + "\n");
                    } else if (action.equals("REJECT")) {
                        a.add(" State: " + v.get(1).getState() + " -> reject" + "\n");
                    } else {
                        a.add(" State: " + v.get(0).getState() + " -> " + v.get(1).getState() + "\n");
                    }

                } else if (v.size() == 1) {
                    a.add(" State: " + v.get(0).getState() + "\n");
                }
                a.add("\n");
            });
            for (String ct : a) {
                content += ct;
            }
            content += "\n";
            content += "Iportal System" + "\n";
            logger.severe("send email to " + Config.getString("email.toEmailConfigMerchant118", "<EMAIL>"));
            logger.severe(": " + "mail title: " + tittle);
            logger.severe(": " + "mail content: " + content);
            MailUtil.sendMail(Config.getString("email.toEmailConfigMerchant118", "<EMAIL>"), tittle, content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void insertConfigMerchantLog(String userEmail, Map<String, List<MerchantConfigDto>> emailAlert, String action) {
        try {
            emailAlert.forEach((k, v) -> {
                try {
                    logger.severe("=============START INSERT TB_HISTORY_LOG=============");
                    MerchantConfigDto dtoNew = new MerchantConfigDto();
                    MerchantConfigDto dtoOld = new MerchantConfigDto();
                    String dataNew = "";
                    String dataOld = "";
                    dtoOld.setMerchantId(k.split("/")[0]);
                    dtoOld.setService(k.split(";")[1]);
                    dtoOld.setEmail(v.get(0).getEmail());
                    dtoOld.setActive(v.get(0).getActive());
                    if (v.size() > 1) {
                        if (action.equals("APPROVE")) {
                            dtoOld.setState(v.get(1).getState());
                        } else if (action.equals("REJECT")) {
                            dtoOld.setState(v.get(1).getState());
                        } else {
                            dtoOld.setState(v.get(0).getState());
                        }
                    } else if (v.size() == 1) {
                        dtoOld.setState(v.get(0).getState());
                    }
                    dataOld = dtoOld.toString();
                    dtoNew.setMerchantId(k.split("/")[0]);
                    dtoNew.setService(k.split(";")[1]);
                    dtoNew.setEmail(v.get(1).getEmail());
                    dtoNew.setActive(v.get(1).getActive());
                    if (v.size() > 1) {
                        if (action.equals("APPROVE")) {
                            dtoNew.setState("approved");
                        } else if (action.equals("REJECT")) {
                            dtoNew.setState("rejected");
                        } else {
                            dtoNew.setState(v.get(1).getState());
                        }
                    } else if (v.size() == 1) {
                        dtoNew.setState(v.get(1).getState());
                    }
                    dataNew = dtoNew.toString();
                    logger.severe("--INSERT" + k.split("/")[0] + " - " + k.split(";")[1]);
                    IndebConfigDao2.insertConfigMerchantLog(dataNew, dataOld, userEmail, action, "IPORTAL-SERVICE");
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
                logger.severe("=============END INSERT TB_HISTORY_LOG=============");
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void getListOnepayBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, IndebConfigDao2.getListOnepayBank());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                if (!ctx.response().ended()) {
                    ctx.fail(e);
                }
            }
        }, false, null);
    }

    public static void getListFuncFeemonth(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, IndebConfigDao2.getListFuncFeeMonth());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                if (!ctx.response().ended()) {
                    ctx.fail(e);
                }
            }
        }, false, null);
    }

    private static String getValueFromJson(JsonObject j, String key) {
        String rt = null;
        if (j != null && key != null) {
            String val = j.getString(key);
            if (val != null && !"null".equalsIgnoreCase(val) && !"".equalsIgnoreCase(val))
                rt = val;
        }
        if (rt == null && "insert_adv_txn_func".equals(key))
            rt = "i_adv_txn_by_pay_channel";
        if (rt == null && "get_adv_txn_func".equals(key))
            rt = "g_advance_transaction";
        if (rt == null && "insert_hold_fd_func".equals(key))
            rt = "i_auto_hold_fd";
        if (rt == null && "insert_hold_func".equals(key))
            rt = "i_auto_hold";
        return rt;
    }

    // Thêm method validate email
    private static boolean isValidEmailList(String emailList) {
        if (emailList == null || emailList.trim().isEmpty()) {
            return false;
        }
        String[] emails = emailList.split(",");
        String emailRegex = "^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\\s*,\\s*)*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})$";
        Pattern pattern = Pattern.compile(emailRegex);
        
        for (String email : emails) {
            if (!pattern.matcher(email.trim()).matches()) {
                return false;
            }
        }
        return true;
    }

    public static void getPayoutMerchantAccounts(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, IndebConfigDao2.getPayoutMerchantAccounts());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantIdsByPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String partnerId = StringUtils.defaultString(request.getParam("partner_id"));
                Map<String, Object> mInPartner = new HashMap<>();
                mInPartner.put(KEYWORD, "");
                mInPartner.put(PARTNER_ID, partnerId);
                List<String> merchantIds = PayoutMerchantConfigDao.listPartnersWithMerchant(mInPartner).stream().map(x -> x.getMerchantId()).collect(Collectors.toList());
                sendResponse(ctx, 200, merchantIds);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
