package vn.onepay.portal.resources.fee.bingroup;

import io.vertx.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class BinGroupHandler {
    private static Logger logger = Logger.getLogger(BinGroupHandler.class.getName());
    public static void getBinGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("bin_group", BinGroupDao.getBinGroup());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
}
