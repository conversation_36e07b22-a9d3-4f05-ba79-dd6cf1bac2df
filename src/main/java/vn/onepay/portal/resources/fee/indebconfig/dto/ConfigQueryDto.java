package vn.onepay.portal.resources.fee.indebconfig.dto;

import java.io.Serializable;
import java.util.List;

public class ConfigQueryDto implements Serializable {
    private String fromDate;
    private String toDate;
    private String keyword;
    private String paymentPeriod;
    private String advanceAccountTypes;
    private String advanceBank;
    private String onepayBankId;
    private Integer state;
    private String service;
    private String formula;
    private String advanceAmount;
    private String contractType;
    private String guaranteeAmount;
    private String guaranteeHoldingType;
    private String withoutMerchant;
    private String statusConfig;
    private String stateOfEffective;
    private String merchantIDWaitForApprove;
    private List<Integer> listPartnerId;
    private Integer totalPartnerId;
    int page;
    int pageSize;

    public String getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getWithoutMerchant() {
        return withoutMerchant;
    }

    public String getStateOfEffective() {
        return stateOfEffective;
    }

    public void setStateOfEffective(String stateOfEffective) {
        this.stateOfEffective = stateOfEffective;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public void setWithoutMerchant(String withoutMerchant) {
        this.withoutMerchant = withoutMerchant;
    }

    public String getGuaranteeAmount() {
        return guaranteeAmount;
    }

    public void setGuaranteeAmount(String guaranteeAmount) {
        this.guaranteeAmount = guaranteeAmount;
    }

    public String getGuaranteeHoldingType() {
        return guaranteeHoldingType;
    }

    public void setGuaranteeHoldingType(String guaranteeHoldingType) {
        this.guaranteeHoldingType = guaranteeHoldingType;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPaymentPeriod() {
        return paymentPeriod;
    }

    public void setPaymentPeriod(String paymentPeriod) {
        this.paymentPeriod = paymentPeriod;
    }

    public String getAdvanceAccountTypes() {
        return advanceAccountTypes;
    }

    public void setAdvanceAccountTypes(String advanceAccountTypes) {
        this.advanceAccountTypes = advanceAccountTypes;
    }

    public String getAdvanceBank() {
        return advanceBank;
    }

    public void setAdvanceBank(String advanceBank) {
        this.advanceBank = advanceBank;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getStatusConfig() {
        return statusConfig;
    }

    public void setStatusConfig(String statusConfig) {
        this.statusConfig = statusConfig;
    }

    public String getMerchantIDWaitForApprove() {
        return this.merchantIDWaitForApprove;
    }

    public void setMerchantIDWaitForApprove(String merchantIDWaitForApprove) {
        this.merchantIDWaitForApprove = merchantIDWaitForApprove;
    }

    public List<Integer> getListPartnerId() {
        return listPartnerId;
    }

    public void setListPartnerId(List<Integer> listPartnerId) {
        this.listPartnerId = listPartnerId;
    }

    public Integer getTotalPartnerId() {
        return totalPartnerId;
    }

    public void setTotalPartnerId(Integer totalPartnerId) {
        this.totalPartnerId = totalPartnerId;
    }

    public String getOnepayBankId() {
        return onepayBankId;
    }

    public void setOnepayBankId(String onepayBankId) {
        this.onepayBankId = onepayBankId;
    }



}
