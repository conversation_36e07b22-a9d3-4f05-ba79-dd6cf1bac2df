package vn.onepay.portal.resources.fee.funcfee.dto;

public class FeeDetailDto {
    private Integer binGroup;
    private String contractType;
    private String paygate;
    private String acquirer;
    private String service;
    private String transactionType;
    private String merchant_id;
    private FeeType feeMerchant;
    private FeeType feeIss;
    private FeeType feeAcq;
    private FeeType feeInstallmentbank;

    public FeeType getFeeMerchant() {
        return feeMerchant;
    }

    public void setFeeMerchant(FeeType feeMerchant) {
        this.feeMerchant = feeMerchant;
    }

    public FeeType getFeeIss() {
        return feeIss;
    }

    public void setFeeIss(FeeType feeIss) {
        this.feeIss = feeIss;
    }

    public FeeType getFeeAcq() {
        return feeAcq;
    }

    public void setFeeAcq(FeeType feeAcq) {
        this.feeAcq = feeAcq;
    }

    public FeeType getFeeInstallmentbank() {
        return feeInstallmentbank;
    }

    public void setFeeInstallmentbank(FeeType feeInstallmentbank) {
        this.feeInstallmentbank = feeInstallmentbank;
    }

    public Integer getBinGroup() {
        return binGroup;
    }

    public void setBinGroup(Integer binGroup) {
        this.binGroup = binGroup;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getPaygate() {
        return paygate;
    }

    public void setPaygate(String paygate) {
        this.paygate = paygate;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }
}
