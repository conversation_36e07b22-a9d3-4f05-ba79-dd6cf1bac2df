package vn.onepay.portal.resources.fee.merchantfee;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantFeeDto;
import vn.onepay.portal.resources.fee.merchantfee.dto.MerchantFeeInputDto;
import vn.onepay.portal.resources.user.UserDao;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.IConstants.X_USER_ID;
import static vn.onepay.portal.Util.sendResponse;

public class MerchantFeeHandler {
    private static Logger logger = Logger.getLogger(MerchantFeeHandler.class.getName());

    public static void getMerchantOnePartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String keyword = request.getParam("keyword") == null ? "":request.getParam("keyword");
            int page = request.getParam("page") == null ? 0:Integer.parseInt(request.getParam("page"));
            int page_size = request.getParam("page_size") == null ? 50:Integer.parseInt(request.getParam("page_size"));
            Map<String,Object> temp = new HashMap<>();
            try {
                temp = MerchantFeeDao.getMerchantOnePartner(keyword,page,page_size);
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);

    }
    public static void getfeeData(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String id = request.getParam("id") == null ? "" : request.getParam("id");
            String service = request.getParam("service") == null ? "" : request.getParam("service");
            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("merchant", MerchantFeeDao.getMerchantFeeDetail(id, service));
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);

    }
    public static void createFeeData(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
            Integer userId = ctx.get(X_USER_ID);
            if (userId == null) {
                logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String UserName = UserDao.get(userId).getName();

            HttpServerRequest request = ctx.request();
            String id = request.getParam("id") == null ? "" : request.getParam("id");
            JsonObject body = ctx.getBodyAsJson();
            String service = body.getString("service") == null ? "" :  body.getString("service");
            Map<Integer, MerchantFeeDto> foundedFeeDatas  = MerchantFeeDao.getMerchantFeeDetail(id, service).stream().collect(Collectors.toMap(MerchantFeeDto::getId, Function.identity()));

            String contractCode = body.getString("contractCode") == null ? "" :  body.getString("contractCode");
//            String contractDate = body.getString("contractDate") == null ? "" :  body.getString("contractDate");
            JsonArray uploadFeeData = body.getJsonArray("merchantFee");
            for(int i = 0;uploadFeeData.size()>i;i++){
                JsonObject obj = uploadFeeData.getJsonObject(i);
                MerchantFeeInputDto uploadDto = bindFeeTemplate(obj, contractCode);
                if(uploadDto.getId()==null){  // case insert new Fee
                        uploadDto.setMerchantId(id);
                        MerchantFeeDao.insertMerchantFeeData(uploadDto,UserName, contractCode);
                }else{
                    MerchantFeeDao.putMerchantFeeData(uploadDto,UserName, contractCode);
                    MerchantFeeDto foundedDto = foundedFeeDatas.get(uploadDto.getId());
//                    String fromDate =   new SimpleDateFormat("dd/MM/yyyy hh:mm:ss a").format(foundedDto.getFromDate());
//                    String toDate =   new SimpleDateFormat("dd/MM/yyyy hh:mm:ss a").format(foundedDto.getToDate());
                    if(foundedDto == null) continue;
                    if (!foundedDto.getAcquirer().equals(uploadDto.getAcquirer())){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                    }else if(!foundedDto.getBinGroupId().equals(uploadDto.getBinGroupId())){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getContractType() != null && !foundedDto.getContractType().equals(uploadDto.getContractType()))||(foundedDto.getContractType() == null && uploadDto.getContractType() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if( (foundedDto.getCurrency() !=null && !foundedDto.getCurrency().equals(uploadDto.getCurrency()))||(foundedDto.getCurrency()==null && uploadDto.getCurrency() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if(foundedDto.getFixFee().compareTo(uploadDto.getFixFee())!=0){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if(foundedDto.getPercentFee().compareTo(uploadDto.getPercentFee())!=0){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getFuncId() != null && !foundedDto.getFuncId().equals(uploadDto.getFuncId()))||(foundedDto.getFuncId()==null && uploadDto.getFuncId() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getFuncInput() != null && !foundedDto.getFuncInput().equals(uploadDto.getFuncInput()))||(foundedDto.getFuncInput() == null && uploadDto.getFuncInput()!= "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if(foundedDto.getOrder() != uploadDto.getOrder()){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if(!foundedDto.getFromDate().equals(uploadDto.getFromDate())){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getToDate() != null && !foundedDto.getToDate().equals(uploadDto.getToDate()))||(foundedDto.getToDate() ==null && uploadDto.getToDate() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getPaygate() != null && !foundedDto.getPaygate().equals(uploadDto.getPaygate()))||(foundedDto.getPaygate() == null && uploadDto.getPaygate() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if( ( foundedDto.getService() != null && !foundedDto.getService().equals(uploadDto.getService()))||(foundedDto.getService()== null && uploadDto.getService() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getType() != null && !foundedDto.getType().equals(uploadDto.getType()))||(foundedDto.getType() == null & uploadDto.getType() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getContractCode() != null && !foundedDto.getContractCode().equals(uploadDto.getContractCode()))||(foundedDto.getContractCode() == null && uploadDto.getContractCode() != "" )){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }else if((foundedDto.getTransactionType() != null && !foundedDto.getTransactionType().equals(uploadDto.getTransactionType()))||(foundedDto.getTransactionType() == null && uploadDto.getTransactionType() != "")){
                        MerchantFeeDao.updateMerchantFeeData(uploadDto, UserName, contractCode);
                        continue;
                    }
                    foundedFeeDatas.remove(foundedDto.getId());
                    }

                }
            if (foundedFeeDatas.size()>0){

                foundedFeeDatas.keySet().forEach(integer -> {
                    try {
                        MerchantFeeDao.removeMerchantFee(integer);
                    } catch (Exception e) {
                        ctx.fail(e);
                    }
                });
            }
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("return_url", Config.getString("Success", "Success"));
            sendResponse(ctx, 201, returnMap);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    private static MerchantFeeInputDto bindFeeTemplate(JsonObject rs, String contractCode){
        MerchantFeeInputDto mer = new MerchantFeeInputDto();
        mer.setId(rs.getString("id").length()==0?null:Integer.parseInt(rs.getString("id")));
        mer.setMerchantId(rs.getString("merchantId"));
        mer.setPaygate(rs.getString("paygate"));
        mer.setService(rs.getString("service"));
        mer.setAcquirer(rs.getString("acquirer"));
        mer.setBinGroupId(rs.getString("binGroupId").length()==0?null:Integer.parseInt(rs.getString("binGroupId")));
        mer.setTransactionType(rs.getString("transactionType"));
        mer.setFixFee(rs.getDouble("fixFee") == null || rs.getDouble("fixFee").toString().equals("0") ? null : rs.getDouble("fixFee"));
        mer.setPercentFee(rs.getDouble("percentFee") == null  || rs.getDouble("percentFee").toString().equals("0") ? null : rs.getDouble("percentFee"));
//        mer.setFixFee(rs.getInteger("fixFee")==0?null:Double.parseDouble(rs.getInteger("fixFee").toString()));
//        mer.setPercentFee(rs.getInteger("percentFee")==0?null:Double.parseDouble(rs.getInteger("percentFee").toString()));
        mer.setFuncId(rs.getString("funcId"));
        mer.setFuncInput(rs.getString("funcInput"));
        mer.setType(rs.getString("type"));
        mer.setContractCode(contractCode);
        mer.setUpdateByUserId(rs.getString("updateByUserId"));
        mer.setCreateByUserId(rs.getString("createByUserId"));
        mer.setOrder(rs.getString("order").length()==0?null:Integer.parseInt(rs.getString("order")));
        mer.setContractType(rs.getString("contractType"));
        mer.setCurrency(rs.getString("currency"));

        SimpleDateFormat  inputDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.sss'Z'");
        SimpleDateFormat   outputDate = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss aa");
        Date fromDate = null, toDate = null;
        try {
            fromDate = inputDate.parse(rs.getString("fromDate"));
            if (fromDate != null) {
                String fromDates = outputDate.format(fromDate);
                mer.setFromDate(fromDates);
            } else {
                mer.setFromDate(null);
            }
            toDate = inputDate.parse(rs.getString("toDate"));
            if (toDate != null) {
                String toDates = outputDate.format(toDate);
                mer.setToDate(toDates);
            } else {
                mer.setToDate(null);
            }
        } catch (Exception ex) {
            logger.severe("error: " + ex);
        }
//        mer.setToDate(rs.getString("toDate"));
//        mer.setFromDate(rs.getString("fromDate"));
        return mer;
    }

    public static void updateContractCodeForMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            String merchant_id = body.getString("merchant_id") == null ? "" : body.getString("merchant_id");
            String contract_code = body.getString("contract_code") == null ? "" : body.getString("contract_code");
            String contract_date = body.getString("contract_date") == null ? "" : body.getString("contract_date");
            String service = body.getString("service") == null ? "" : body.getString("service");
            Map<String, Object> returnMap = new HashMap<>();
            try {
                returnMap.put("result", MerchantFeeDao.updateContractCodeForMerchant(merchant_id, contract_code, contract_date, service));
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,returnMap);
        }, false, null);

    }

    private final static Gson gson = new Gson();
}
