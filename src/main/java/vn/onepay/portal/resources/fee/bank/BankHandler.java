package vn.onepay.portal.resources.fee.bank;

import io.vertx.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class BankHandler {
    private static Logger logger = Logger.getLogger(BankHandler.class.getName());
    public static void getListBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("Bank", BankDao.getbank());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, temp);
        }, false, null);

    }
}
