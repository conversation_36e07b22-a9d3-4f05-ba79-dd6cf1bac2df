package vn.onepay.portal.resources.fee.group_config.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.fee.group_config.dao.PartnerGroupDAO;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class PartnerGroupHandler implements IConstants {
    private PartnerGroupHandler() {
    }

    private static Logger logger = Logger.getLogger(PartnerGroupHandler.class.getName());

    public static void getListPartnerGroupByPartnerFeeConfigId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerFeeConfigId = Integer.parseInt(request.getParam("partnerFeeConfigId").toString());
                sendResponse(ctx, 200, PartnerGroupDAO.getListPartnerGroupBYPartnerFeeConfigId(partnerFeeConfigId));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ PARTNER GROUP ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insertPartnerGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                JsonObject mIn  = new JsonObject();
                int partnerFeeConfigId = ctx.request().getParam(PARTNER_FEE_CONFIG_ID) != null ? Integer.parseInt(ctx.request().getParam(PARTNER_FEE_CONFIG_ID)) : 0;
                mIn.put(PARTNER_FEE_CONFIG_ID, partnerFeeConfigId);
                int partnerFeeServiceId = body.getInteger(PARTNER_FEE_SERVICE_ID,0);
                mIn.put(PARTNER_FEE_SERVICE_ID, partnerFeeServiceId);
                String partnerGroupName = body.getString(PARTNER_GROUP_NAME,BLANK);
                mIn.put(PARTNER_GROUP_NAME, partnerGroupName);
                String userUpdate  = body.getString(USER_UPDATE);
                mIn.put(USER_UPDATE, userUpdate);
                String type  = body.getString(TYPE);
                mIn.put(TYPE, type);
                var result = PartnerGroupDAO.insertPartnerGroup(mIn);
                if (result.get("status").equals(400)) {
                    sendResponse(ctx, 400, result);
                } else {
                    sendResponse(ctx, 201, result);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "insert: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deletePartnerGroupById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer partnerGroupId = Integer.parseInt(request.getParam("partnerGroupId"));
                var result = PartnerGroupDAO.deletePartnerGroupById(partnerGroupId);
                if (result.get("status").equals(400)) {
                    sendResponse(ctx, 400, result);
                } else {
                    sendResponse(ctx, 200, result);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "delete: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updatePartnerGroupById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int partnerGroupId = request.getParam(PARTNER_GROUP_ID) != null ? Integer.parseInt(request.getParam(PARTNER_GROUP_ID)) : 0;
                JsonObject body = ctx.getBodyAsJson();
                String partnerGroupName = body.getString(PARTNER_GROUP_NAME) != null ? body.getString(PARTNER_GROUP_NAME) : BLANK;
                int partnerFeeServiceId = body.getInteger(PARTNER_FEE_SERVICE_ID, 0);
                String userUpdate = body.getString(USER_UPDATE, BLANK);
                JsonObject mIn  = new JsonObject();
                mIn.put(PARTNER_GROUP_ID, partnerGroupId);
                mIn.put(PARTNER_GROUP_NAME, partnerGroupName);
                mIn.put(PARTNER_FEE_SERVICE_ID, partnerFeeServiceId);
                mIn.put(USER_UPDATE, userUpdate);
                var result = PartnerGroupDAO.updatePartnerGroupById(mIn);
                if (result.get("status").equals(400)) {
                    sendResponse(ctx, 400, result);
                } else {
                    sendResponse(ctx, 200, result);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "delete: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
