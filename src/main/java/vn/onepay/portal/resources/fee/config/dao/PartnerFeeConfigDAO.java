package vn.onepay.portal.resources.fee.config.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.fee.config.dto.PartnerFeeConfig;
import vn.onepay.portal.resources.fee.config.dto.PartnerFeeServiceFunc;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PartnerFeeConfigDAO extends Db {
    private static final Logger _LOGGER = Logger.getLogger(PartnerFeeConfigDAO.class.getName());
    private static final String GET_LIST_PARTNER_FEE_CONFIG = "{call ONEFIN.PKG_PARTNER_FEE_CONFIG.SEARCH_PARTNER_FEE_CONFIG(?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_DETAIL_PARTNER_FEE_CONFIG_BY_ID = "{call ONEFIN.PKG_PARTNER_FEE_CONFIG.GET_DETAIL_PARTNER_FEE_CONFIG_BY_ID(?,?,?,?)}";
    private static final String GET_LIST_PARTNER_FEE_SERVICE = "{call ONEFIN.PKG_PARTNER_FEE_SERVICE.SEARCH_PARTNER_FEE_SERVICE_BY_PARTNER_FEE_CONFIG_ID(?,?,?,?,?)}";
    private static final String GET_PARTNER_FEE_CONFIG = "{call ONEFIN.PKG_PARTNER_FEE_CONFIG.get_partner_fee_config(?,?,?,?,?,?)}";
    private static final String INSERT_PARTNER_FEE_SERVICE = "{call ONEFIN.PKG_PARTNER_FEE_SERVICE.insert_partner_fee_service(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_PARTNER_FEE_SERVICE_FUNC = "{call ONEFIN.PKG_PARTNER_FEE_SERVICE.g_func(?,?,?,?)}";
    private static final String UPDATE_PARTNER_FEE_SERVICE = "{call ONEFIN.PKG_PARTNER_FEE_SERVICE.update_partner_fee_service(?,?,?,?,?,?)}";

    public static Map<String, Object> getDetailPartnerFeeConfigById(Integer id) throws SQLException {
        SQLException exception = null;
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_DETAIL_PARTNER_FEE_CONFIG_BY_ID);
            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 1) {
                throw new SQLException("DB get partner fee config : " + error);
            } else {
                if (rs.next()) {
                    result.put("id", id);
                    result.put("partnerName", rs.getString("S_PARTNER_NAME"));
                    result.put("partnerType", rs.getString("S_PARTNER_TYPE"));
                    result.put("partnerService", rs.getString("S_PARTNER_SERVICE"));
                    result.put("partnerContractType", rs.getString("S_PARTNER_CONTRACT_TYPE"));
                    result.put("partnerFeeType", rs.getString("S_PARTNER_FEE_TYPE"));
                    result.put("updater", rs.getString("S_UPDATE"));
                    result.put("updatedAt", rs.getString("D_UPDATE"));
                    result.put("partnerID", rs.getString("S_PARTNER_ID"));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<Map<String, Object>> getListPartnerFeeConfig(Map<String, Object> map) throws SQLException {
        SQLException exception = null;
        BaseList<Map<String, Object>> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_PARTNER_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, map.get("partnerName").toString());
            cs.setString(6, map.get("partnerType").toString());
            cs.setString(7, map.get("partnerContractType").toString());
            cs.setString(8, map.get("partnerService").toString());
            cs.setInt(9, Integer.parseInt(map.get("page").toString()));
            cs.setInt(10, Integer.parseInt(map.get("pageSize").toString()));
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB get partner fee config : " + error);
            } else {
                if (rs != null) {
                    result = new BaseList<>(Util.resultSetToList(rs), cs.getInt(2));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<Map<String, Object>> getListPartnerFeeServiceByPartnerFeeConfigId(Integer partnerFeeConfigId) throws SQLException {
        SQLException exception = null;
        BaseList<Map<String, Object>> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_PARTNER_FEE_SERVICE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, partnerFeeConfigId);
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB get partner fee service : " + error);
            } else {
                if (rs != null) {
                    result = new BaseList<>(Util.resultSetToList(rs), cs.getInt(2));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, PartnerFeeConfig> getPartnerFeeConfig(String contractType, String service, String partnerId) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, PartnerFeeConfig> result = new HashMap<>();
        PartnerFeeConfig partnerFeeConfig = new PartnerFeeConfig();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_PARTNER_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, contractType);
            cs.setString(5, service);
            cs.setString(6, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB get partner fee config : " + error);
            } else {
                if (rs.next()) {
                    partnerFeeConfig = convertPartnerFeeConfig(rs);
                    result.put("200", partnerFeeConfig);
                }
                result.put("400", partnerFeeConfig);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, String> insertPartnerFeeService(Integer partnerFeeConfigId, String currency,
                                                              String bankMerchantId, Integer func1,
                                                              Integer func2, String state, Integer acquirerId,
                                                              String funcParams, String funcParams2,
                                                              String ciac) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, String> result = new HashMap<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(INSERT_PARTNER_FEE_SERVICE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, partnerFeeConfigId);
            cs.setString(4, currency);
            cs.setString(5, bankMerchantId);
            cs.setInt(6, func1);
            cs.setInt(7, func2);
            cs.setString(8, state);
            cs.setInt(9, acquirerId);
            cs.setString(10, funcParams);
            cs.setString(11, funcParams2);
            cs.setString(12, ciac);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result.put("500", "Error");
                throw new SQLException("DB insert partner fee service : " + error);
            } else {
                result.put("200", "Success");
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static PartnerFeeServiceFunc getFuncParams(Integer func) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        PartnerFeeServiceFunc feeServiceFunc = new PartnerFeeServiceFunc();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_PARTNER_FEE_SERVICE_FUNC);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, func);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB get partner fee config : " + error);
            } else {
                if (rs.next()) {
                    feeServiceFunc = convertServiceFunc(rs);
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return feeServiceFunc;
    }

    public static Map<String, String> updatePartnerFeeService(Integer partnerFeeConfig, String merchantBankIdPrev,
                                                              String merchantBankIdNext, String ciac) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, String> result = new HashMap<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(UPDATE_PARTNER_FEE_SERVICE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, partnerFeeConfig);
            cs.setString(4, merchantBankIdPrev);
            cs.setString(5, merchantBankIdNext);
            cs.setString(6, ciac);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result.put("500", "Error");
                throw new SQLException("DB update partner fee service : " + error);
            } else {
                result.put("200", "Success");
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    private static PartnerFeeConfig convertPartnerFeeConfig(ResultSet rs) throws SQLException {
        PartnerFeeConfig result = new PartnerFeeConfig();
        result.setId(rs.getInt(N_ID));
        result.setPartnerName(rs.getString("S_PARTNER_NAME"));
        result.setPartnerType(rs.getString("S_PARTNER_TYPE"));
        result.setPartnerService(rs.getString("S_PARTNER_SERVICE"));
        result.setPartnerContractType(rs.getString("S_PARTNER_CONTRACT_TYPE"));
        result.setPartnerFeeType(rs.getString("S_PARTNER_FEE_TYPE"));
        result.setUpdater(rs.getString("S_UPDATE"));
        result.setUpdateAt(rs.getString("D_UPDATE"));
        result.setPartnerId(rs.getString("S_PARTNER_ID"));
        return result;
    }

    private static PartnerFeeServiceFunc convertServiceFunc(ResultSet rs) throws SQLException {
        PartnerFeeServiceFunc result = new PartnerFeeServiceFunc();
        result.setFunc1(rs.getInt("N_FUNC_ID"));
        result.setFunc2(rs.getInt("N_FUNC_ID_2"));
        result.setFuncParams1(rs.getString("S_FUNC_PARAMS"));
        result.setFuncParams2(rs.getString("S_FUNC_PARAMS_2"));
        return result;
    }

}
