package vn.onepay.portal.resources.fee.indebconfig;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.FlowTypeDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.PartnerConfigDto;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;

@SuppressWarnings("deprecation")
public class IndebConfigDao2 extends Db {
    private static final String SEARCH_CONFIG = "{ call ONEFIN.search_config_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String SEARCH_CONFIG_MM = "{ call ONEFIN.search_config_mm(?,?,?,?,?,?,?,?,?)}";
    private static final String GET_PARTNER_ID_BY_USER_ID = "{call get_partner_id_by_user_id(?,?,?,?,?) }";
    private static final String CHECK_INDEB_CONFIG = "{call ONEFIN.pkg_indeb_config.c_indeb_config(?,?,?,?) }";
    private static final String GET_INDEB_CONFIG = "{call ONEFIN.pkg_indeb_config.g_indeb_config(?,?,?,?) }";

    public static void deleteConfig(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.delete_config(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.executeQuery();
            String error = cs.getString(2);
            int nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB delete config error: {0}", error);
            }
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
    }

    public static Map<String, Object> getIndebConfig(int id)  throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        int nerror = 500;
        String error = "";
        ResultSet rs = null;
        Map<String, Object>  result = new HashMap<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_INDEB_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            if (nerror == 500) {
                logger.log(Level.SEVERE, "DB get indebtedness config error: {0}", error);
            }else{
                rs = (ResultSet) cs.getObject(1);
               if (rs != null && rs.next()){
                   result.put("N_ID", rs.getInt("N_ID"));
                   result.put("S_TEN_DKKD", rs.getString("S_TEN_DKKD"));
                   result.put("S_TEN_DKKD_OLD", rs.getString("S_TEN_DKKD_OLD"));
                   result.put("S_MERCHANT_IDS", rs.getString("S_MERCHANT_IDS"));
                   result.put("N_PARENT_CONFIG", rs.getInt("N_PARENT_CONFIG"));
                   result.put("S_ADV_ACC_BANK_ID", rs.getString("S_ADV_ACC_BANK_ID"));
                   result.put("S_ADV_ACC_BANK_ID_OLD", rs.getString("S_ADV_ACC_BANK_ID_OLD"));
                   result.put("N_PARTNER_ID", rs.getInt("N_PARTNER_ID"));
                   result.put("S_APPROVE", rs.getString("S_APPROVE"));
                   result.put("N_ACTIVE", rs.getInt("N_ACTIVE"));
                   result.put("S_PAY_CHANNEL", rs.getString("S_PAY_CHANNEL"));
                   result.put("S_ADVANCE_ACCOUNT", rs.getString("S_ADVANCE_ACCOUNT"));
                   result.put("S_CONTRACT_CODE", rs.getString("S_CONTRACT_CODE"));
                   result.put("S_TYPE_ADVANCE", rs.getString("S_TYPE_ADVANCE"));
               }
            }
            return result;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }

    }

    public static int checkIndebConfig(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        int nerror = 500;
        String error = "";
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(CHECK_INDEB_CONFIG);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            int result = 0;
            result = cs.getInt(3);
            if (nerror == 500) {
                logger.log(Level.SEVERE, "DB check indebtedness config error: {0}", error);
            }
            return result;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
    }

    public static void approvalConfig(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        int nerror = 500;
        String error = "";
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.APPROVAL_INDEB_CONFIG(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB approval config error: {0}", error);
            }
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
    }

    public static Map<String, Object> getListConfig(ConfigQueryDto queryDto) throws SQLException {
        List<ConfigDto> config = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.name());
            cs.setString(5, queryDto.getKeyword());
            cs.setInt(6, queryDto.getPage());
            cs.setInt(7, queryDto.getPageSize());
            cs.setString(8, queryDto.getPaymentPeriod());
            cs.setString(9, queryDto.getAdvanceBank());
            cs.setObject(10, queryDto.getState());
            cs.setString(11, queryDto.getService());
            cs.setString(12, queryDto.getFormula());
            cs.setString(13, queryDto.getContractType());
            cs.setString(14, queryDto.getGuaranteeAmount());
            cs.setString(15, queryDto.getGuaranteeHoldingType());
            cs.setString(16, queryDto.getWithoutMerchant());
            cs.setString(17, queryDto.getStatusConfig());
            cs.setString(18, queryDto.getStateOfEffective());
            cs.setString(19, queryDto.getFromDate());
            cs.setString(20, queryDto.getToDate());
            cs.setString(21, queryDto.getMerchantIDWaitForApprove());
            cs.setString(22, queryDto.getAdvanceAmount());
            cs.setString(23, queryDto.getOnepayBankId());
            cs.setString(24, queryDto.getAdvanceAccountTypes());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 500) {
                logger.log(Level.SEVERE, "DB get config list error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    config.add(bindConfig(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        data.put("intedConfig", config);
        return data;
    }

    public static Map<String, Object> getPartnerIdByUserId(Integer userId, String provinceIds) throws Exception {
        Map<String, Object> dataList = new HashMap<>();
        List<Integer> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer count = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_ID_BY_USER_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, userId);
            cs.setString(5, provinceIds);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET PARTNER BY USER ID: " + error);
            } else {

                while (rs != null && rs.next()) {
                    count++;
                    result.add(rs.getInt("N_PARTNER_ID"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        dataList.put("list", result);
        dataList.put("total", count);
        return dataList;
    }

    public static Map<String, Object> getListAdvanceConfig(ConfigQueryDto queryDto) throws Exception {
        CallableStatement cs = null;
        OracleConnection connection = null;
        ResultSet rs = null;
        List<ConfigDto> config = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        int userIdCheck = 1;
        try (Connection conn = getPds118().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);

                Integer[] N_PARTNER_ID = new Integer[queryDto.getTotalPartnerId()];
                List<Integer> list = (List<Integer>) queryDto.getListPartnerId();
                for (int i = 0; i < list.size(); i++) {
                    N_PARTNER_ID[i] = list.get(i);
                    if (N_PARTNER_ID[i] == 0) {
                        userIdCheck = 0;
                    }
                }
                ArrayDescriptor arrDesc = ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, N_PARTNER_ID);

                cs = connection.prepareCall(SEARCH_CONFIG_MM);
                cs.registerOutParameter(1, OracleTypes.CURSOR);
                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.setString(4, QueryMethod.SELECT.name());
                cs.setString(5, queryDto.getKeyword());
                cs.setInt(6, queryDto.getPage());
                cs.setInt(7, queryDto.getPageSize());
                cs.setArray(8, array);
                cs.setInt(9, userIdCheck);
                cs.executeQuery();
                String error = cs.getString(3);
                int nerror = cs.getInt(2);
                rs = (ResultSet) cs.getObject(1);
                if (nerror == 500) {
                    logger.log(Level.SEVERE, "DB get config list error: {0}", error);
                } else {
                    while (rs != null && rs.next()) {
                        // TODO: change search config MM
                        config.add(bindConfig(rs));
                    }
                }
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in get list advance config", ex);
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        data.put("intedConfig", config);
        return data;

    }

    private static ConfigDto bindConfig(ResultSet rs) throws SQLException {
        ConfigDto config = new ConfigDto();
        config.setId(rs.getLong("N_ID"));
        config.setAdvanceAccount(rs.getString("s_advance_account"));
        config.setContractCode(rs.getString("s_contract_code"));
        config.setContractType(rs.getString("s_contract_type"));
        config.setLastMerchantName(rs.getString("S_TEN_DV"));
        config.setMerchantId(rs.getString("s_merchant_id") == null ? "" : rs.getString("s_merchant_id"));
        config.setPartnerName(rs.getString("s_partner_name"));
        // Giữ nguyên giá trị gốc từ DB, không thay đổi DD
        config.setService(rs.getString("s_pay_channel"));
        config.setTenDkkd(rs.getString("s_ten_dkkd"));
        config.setPartnerId(rs.getInt("n_partner_id"));
        config.setDayOfWeek(rs.getString("S_DAY_OF_WEEK"));
        config.setDayOfMonth(rs.getString("S_DAY_OF_MONTH"));
        config.setCutOffTime(rs.getInt("N_HOUR"));
        config.setGuaranteeAmount(rs.getDouble("n_guarantee_destination"));
        config.setPaymentPeriod(rs.getString("s_type_advance"));
        config.setAdvancePercent(rs.getDouble("N_ADVANCE_PERCENT"));
        config.setAdvanceBank(rs.getString("S_ADV_ACC_BANK_ID"));
        config.setOnepayBankId(rs.getString("S_ONEPAY_ACC_BANK_ID"));
        config.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        config.setBranchName(rs.getString("S_BRANCH_NAME"));
        config.setParentConfig(rs.getInt("N_PARENT_CONFIG"));
        config.setTaxCode(rs.getString("S_TAX_CODE"));
        config.setGuaranteeHoldingType(rs.getString("S_GUARANTEE_HOLDING_TYPE"));
        config.setState(rs.getString("s_approve"));
        config.setAction(rs.getInt("N_ACTIVE"));
        config.setTemplateId(rs.getInt("N_TEMPLATE_EMAIL_ID"));
        config.setTemplateName(rs.getString("N_TEMPLATE_NAME") == null ? "" : rs.getString("N_TEMPLATE_NAME"));
        config.setShortName(rs.getString("S_SHORT_NAME") == null ? "" : rs.getString("S_SHORT_NAME"));
        config.setCreatedDate(new Timestamp(rs.getDate("D_CREATE").getTime()));
        config.setStatus(rs.getString("s_state"));
        config.setAddendum(rs.getString("s_addendum"));
        config.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        config.setUpdateDate(new Timestamp(rs.getDate("D_UPDATE").getTime()));
        config.setJHold(rs.getString("J_HOLD"));
        config.setReceiptType(rs.getString("S_TYPE_RECEIPT"));
        config.setAuthorizedExpirationDate(rs.getDate("D_AUTHORIZED_EXPIRATION_DATE") == null ? null : rs.getDate("D_AUTHORIZED_EXPIRATION_DATE").getTime());
        config.setAuthorizedPerson(rs.getString("S_AUTHORIZED_PERSON"));
        String trantype = "";
        if (("," + config.getTransactionType() + ",").indexOf(",REFUND,") >= 0) {
            trantype += "Refund";
        }
        if (("," + config.getTransactionType() + ",").indexOf(",PURCHASE,") >= 0) {
            if (trantype.length() > 1)
                trantype += ",";
            trantype += "Purchase";
        }
        config.setTrantypeConfig(trantype);
        config.setAdvanceAmount(rs.getString("S_ADVANCE_AMOUNT"));
        config.setAdvanceAccountPayout(new Gson().fromJson(rs.getString("J_ADVANCE_ACCOUNT"), Util.MAP_STRING_OBJECT));
        return config;
    }

    public static int getTotalAdvanceConfig(ConfigQueryDto queryDto) throws SQLException {
        CallableStatement cs = null;
        OracleConnection connection = null;
        ResultSet rs = null;
        int total = 0;
        int userIdCheck = 1;
        try (Connection conn = getPds118().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);

                Integer[] N_PARTNER_ID = new Integer[queryDto.getTotalPartnerId()];
                List<Integer> list = (List<Integer>) queryDto.getListPartnerId();

                for (int i = 0; i < list.size(); i++) {
                    N_PARTNER_ID[i] = list.get(i);
                    if (N_PARTNER_ID[i] == 0) {
                        userIdCheck = 0;
                    }
                }

                ArrayDescriptor arrDesc = ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, N_PARTNER_ID);

                cs = connection.prepareCall(SEARCH_CONFIG_MM);
                cs.registerOutParameter(1, OracleTypes.CURSOR);
                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.setString(4, QueryMethod.TOTAL.name());
                cs.setString(5, queryDto.getKeyword());
                cs.setInt(6, queryDto.getPage());
                cs.setInt(7, queryDto.getPageSize());
                cs.setArray(8, array);
                cs.setInt(9, userIdCheck);
                cs.executeQuery();
                String error = cs.getString(3);
                int nerror = cs.getInt(2);
                rs = (ResultSet) cs.getObject(1);
                if (nerror == 500) {
                    logger.log(Level.SEVERE, "DB get total config error: {0}", error);
                } else {
                    while (rs != null && rs.next()) {
                        total = rs.getInt("N_TOTAL");
                    }
                }
            }
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        return total;
    }

    public static int getTotal(ConfigQueryDto queryDto) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.name());
            cs.setString(5, queryDto.getKeyword());
            cs.setInt(6, queryDto.getPage());
            cs.setInt(7, queryDto.getPageSize());
            cs.setString(8, queryDto.getPaymentPeriod());
            cs.setString(9, queryDto.getAdvanceBank());
            cs.setObject(10, queryDto.getState());
            cs.setString(11, queryDto.getService());
            cs.setString(12, queryDto.getFormula());
            cs.setString(13, queryDto.getContractType());
            cs.setString(14, queryDto.getGuaranteeAmount());
            cs.setString(15, queryDto.getGuaranteeHoldingType());
            cs.setString(16, queryDto.getWithoutMerchant());
            cs.setString(17, queryDto.getStatusConfig());
            cs.setString(18, queryDto.getStateOfEffective());
            cs.setString(19, queryDto.getFromDate());
            cs.setString(20, queryDto.getToDate());
            cs.setString(21, queryDto.getMerchantIDWaitForApprove());
            cs.setString(22, queryDto.getAdvanceAmount());
            cs.setString(23, queryDto.getOnepayBankId());
            cs.setString(24, queryDto.getAdvanceAccountTypes());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 500) {
                logger.log(Level.SEVERE, "DB get total config error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt("N_TOTAL");
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return total;
    }

    public static ConfigDto getConfigDetail(int id) throws SQLException {
        ConfigDto config = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsSource = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_INDEBCONFIG_DETAIL(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, id);
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            rsSource = (ResultSet) cs.getObject(2);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get detail config error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    config = bindConfigdetail(rs, rsSource != null && rsSource.next() ? rsSource : null);
                }
            }
        } finally {
            Db.closeObjects(new Object[]{rs, rsSource, cs, con});
        }
        return config;
    }

    private static ConfigDto bindConfigdetail(ResultSet rs, ResultSet rsSource) throws SQLException {
        ConfigDto config = new ConfigDto();
        config.setId(rs.getLong("N_ID"));
        config.setAdvanceAccount(rs.getString("s_advance_account"));
        config.setContractCode(rs.getString("s_contract_code"));
        config.setFormula(rs.getString("s_formula"));
        // config.setContractDate(rs.getString("D_CONTRACT_DATE") == null ? null :
        // java.sql.Timestamp.valueOf(rs.getString("D_CONTRACT_DATE")));
        config.setContractDate(rs.getDate("D_CONTRACT_DATE") == null ? null : rs.getDate("D_CONTRACT_DATE").getTime());
        config.setEffectiveDate(rs.getDate("D_EFFECTIVE_DATE") == null ? null : rs.getDate("D_EFFECTIVE_DATE").getTime());
        config.setExpirationDate(rs.getDate("D_EXPIRATION_DATE") == null ? null : rs.getDate("D_EXPIRATION_DATE").getTime());
        config.setContractType(rs.getString("s_contract_type"));
        config.setLastMerchantName(rs.getString("S_TEN_DV"));
        config.setService(rs.getString("s_pay_channel"));
        config.setTenDkkd(rs.getString("s_ten_dkkd"));
        config.setPartnerId(rs.getInt("n_partner_id"));
        config.setDayOfWeek(rs.getString("S_DAY_OF_WEEK"));
        config.setDayOfMonth(rs.getString("S_DAY_OF_MONTH"));
        config.setAdvanceAmount(rs.getString("S_ADVANCE_AMOUNT"));
        config.setObjectFee(rs.getString("S_OBJECT_FEE"));
        // config.setCutOffTime(rs.getInt("N_HOUR"));
        config.setGuaranteeAmount(rs.getDouble("n_guarantee_destination"));
        // config.setPaymentPeriod(rs.getString("s_type_advance"));
        config.setAdvancePercent(rs.getDouble("N_ADVANCE_PERCENT"));
        config.setAdvanceBank(rs.getString("S_ADV_ACC_BANK_ID"));
        config.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        config.setBranchName(rs.getString("S_BRANCH_NAME"));
        config.setParentConfig(rs.getInt("N_PARENT_CONFIG"));
        config.setAction(rs.getInt("n_active"));
        config.setTaxCode(rs.getString("S_TAX_CODE"));
        config.setGuaranteeHoldingType(rs.getString("S_GUARANTEE_HOLDING_TYPE"));
        config.setFlowType(rs.getInt("n_flow_type_id"));
        config.setState(rs.getString("s_approve"));
        config.setTemplateId(rs.getInt("N_TEMPLATE_EMAIL_ID"));
        config.setTemplateName(rs.getString("N_TEMPLATE_NAME") == null ? "" : rs.getString("N_TEMPLATE_NAME"));
        config.setNotes(rs.getString("S_NOTES"));
        StringBuilder conditions = new StringBuilder();
        String advanceTranFailed = rs.getString("S_ADVANCE_TRANS_FAILED");
        String advanceVCB = rs.getString("S_ADVANCE_VCB");
        if (advanceVCB != null && "YES".equals(advanceTranFailed)) {
            conditions.append(advanceVCB);
            conditions.append(",");
            conditions.append("1");
        } else if ("YES".equals(advanceTranFailed)) {
            conditions.append("1");
        } else if (advanceVCB != null) {
            conditions.append(advanceVCB);
        }
        config.setAdvTranFailQT(advanceTranFailed.equals("YES") ? true : false);
        config.setAdvanceTransFailed(advanceTranFailed);
        config.setConditions(conditions.toString());
        config.setCheckEnoughGuarantee(rs.getInt("N_ENOUGH_GUARANTEE"));
        config.setAccountingEntry(rs.getString("S_ACCOUNTING_ENTRY") == null ? "" : rs.getString("S_ACCOUNTING_ENTRY"));
        config.setMinAdvanceAmount(rs.getDouble("N_MIN_AMOUNT_ADV"));
        config.setTemplateFeeMonth(rs.getInt("N_TEMPLATE_FEE_MONTH"));
        config.setAttachFile(rs.getString("S_ATTACH_FILE"));
        config.setEmailFeeMonth(rs.getString("s_email_fee_month"));
        config.setDividePCRF("YES".equals(rs.getString("S_DIVIDE_PC_RF")));
        config.setAuthorizedPerson(rs.getString("S_AUTHORIZED_PERSON"));
        config.setAuthorizedExpirationDate(rs.getDate("D_AUTHORIZED_EXPIRATION_DATE") == null ? null : rs.getDate("D_AUTHORIZED_EXPIRATION_DATE").getTime());
        config.setGrabPromotion("YES".equals(rs.getString("S_GRAB_PROMOTION")));
        config.setCutoffFeeMonth(rs.getInt("N_DAY_CUTOFF_MONTH"));
        config.setOnepayBankId(rs.getString("S_ONEPAY_ACC_BANK_ID"));

        config.setFunctionCreateAdv(rs.getInt("N_FUNC_CREATE_ADV"));
        config.setFunctionCreateAdvParam(rs.getString("s_func_create_adv_param"));
        config.setFunctionCreateAdvDesc(rs.getString("s_func_create_adv_desc"));
        config.setFunctionCreateFeeMonth(rs.getString("N_FUNC_CREATE_REPORT_MONTH") == null ? 0 : rs.getInt("N_FUNC_CREATE_REPORT_MONTH"));
        config.setFunctionCreateFeeMonthParam(rs.getString("s_func_fee_month_param"));
        config.setLbfunctionCreateFeeMonthDesc(rs.getString("s_func_fee_month_desc"));

        config.setJQT(rs.getString("J_QT"));
        if (rs.getString("J_QT") != null) {
            JsonObject jQT = new JsonObject(rs.getString("J_QT"));
            String paymentPeriodQT = jQT.getInteger("active") == 0 ? "inactive" : jQT.getString("advance_type");
            config.setPaymentPeriod(paymentPeriodQT);
            config.setCutOffTime(jQT.getInteger("active") == 0 ? 17 : jQT.getInteger("hour"));
            config.setHourPayment(jQT.getString("period"));
            config.setInsertAdvTxnFuncQT(jQT.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncQT(jQT.getString("get_adv_txn_func"));
        }
        config.setJGuaranteeTimeHold(rs.getString("S_GUARANTEE_TIME_HOLD"));
        config.setJND(rs.getString("J_ND"));
        if (rs.getString("J_ND") != null) {
            JsonObject jND = new JsonObject(rs.getString("J_ND"));
            String paymentPeriodND = jND.getInteger("active") == 0 ? "inactive" : jND.getString("advance_type");
            config.setPaymentPeriodND(paymentPeriodND);
            config.setCutOffTimeND(jND.getInteger("active") == 0 ? 24 : jND.getInteger("hour"));
            config.setHourPaymentND(jND.getString("period"));
            config.setInsertAdvTxnFuncND(jND.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncND(jND.getString("get_adv_txn_func"));
        }
        config.setJQR(rs.getString("J_QR"));
        if (rs.getString("J_QR") != null) {
            JsonObject jQR = new JsonObject(rs.getString("J_QR"));
            String paymentPeriodQR = jQR.getInteger("active") == 0 ? "inactive" : jQR.getString("advance_type");
            config.setPaymentPeriodQR(paymentPeriodQR);
            config.setCutOffTimeQR(jQR.getInteger("active") == 0 ? 24 : jQR.getInteger("hour"));
            config.setHourPaymentQR(jQR.getString("period"));
            config.setInsertAdvTxnFuncQR(jQR.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncQR(jQR.getString("get_adv_txn_func"));
        }
        config.setJBNPL(rs.getString("j_bnpl"));
        if (rs.getString("j_bnpl") != null) {
            JsonObject jBNPL = new JsonObject(rs.getString("j_bnpl"));
            String paymentPeriodBNPL = jBNPL.getInteger("active") == 0 ? "inactive" : jBNPL.getString("advance_type");
            config.setPaymentPeriodBNPL(paymentPeriodBNPL);
            config.setCutOffTimeBNPL(jBNPL.getInteger("active") == 0 ? 24 : jBNPL.getInteger("hour"));
            config.setHourPaymentBNPL(jBNPL.getString("period"));
            config.setInsertAdvTxnFuncBNPL(jBNPL.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncBNPL(jBNPL.getString("get_adv_txn_func"));
        }
        config.setJUPOS(rs.getString("j_upos"));
        if (rs.getString("j_upos") != null) {
            JsonObject jUPOS = new JsonObject(rs.getString("j_upos"));
            String paymentPeriodUPOS = jUPOS.getInteger("active") == 0 ? "inactive" : jUPOS.getString("advance_type");
            config.setPaymentPeriodUPOS(paymentPeriodUPOS);
            config.setCutOffTimeUPOS(jUPOS.getInteger("active") == 0 ? 24 : jUPOS.getInteger("hour"));
            config.setHourPaymentUPOS(jUPOS.getString("period"));
            config.setInsertAdvTxnFuncUPOS(jUPOS.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncUPOS(jUPOS.getString("get_adv_txn_func"));
            config.setUposType(jUPOS.getString("upos_type"));
        }

        config.setjPayCollect(rs.getString("j_paycollect"));
        if (rs.getString("j_paycollect") != null) {
            JsonObject jPayCollect = new JsonObject(rs.getString("j_paycollect"));
            String paymentPeriodPayCollect = jPayCollect.getInteger("active") == 0 ? "inactive" : jPayCollect.getString("advance_type");
            config.setPaymentPeriodPayCollect(paymentPeriodPayCollect);
            config.setCutOffTimePayCollect(jPayCollect.getInteger("active") == 0 ? 24 : jPayCollect.getInteger("hour"));
            config.setHourPaymentPayCollect(jPayCollect.getString("period"));
            config.setInsertAdvTxnFuncPayCollect(jPayCollect.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncPayCollect(jPayCollect.getString("get_adv_txn_func"));
        }
        config.setCashFlow(rs.getString("S_CASH_FLOW"));
        config.setInvoiceType(rs.getString("S_INVOICE_TYPE"));
        config.setInvoiceInterval(rs.getString("S_INVOICE_INTERVAL"));

        config.setjPayOut(rs.getString("j_payout"));
        if (rs.getString("j_payout") != null) {
            JsonObject jPayOut = new JsonObject(rs.getString("j_payout"));
            String paymentPeriodPayOut = jPayOut.getInteger("active") == 0 ? "inactive" : jPayOut.getString("advance_type");
            config.setPaymentPeriodPayOut(paymentPeriodPayOut);
            config.setCutOffTimePayOut(jPayOut.getInteger("active") == 0 ? 24 : jPayOut.getInteger("hour"));
            config.setHourPaymentPayOut(jPayOut.getString("period"));
            config.setInsertAdvTxnFuncPayOut(jPayOut.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncPayOut(jPayOut.getString("get_adv_txn_func"));
        }

        config.setjVietQR(rs.getString("j_vietqr"));
        if (rs.getString("j_vietqr") != null) {
            JsonObject jVietQR = new JsonObject(rs.getString("j_vietqr"));
            String paymentPeriodVietQR = jVietQR.getInteger("active") == 0 ? "inactive" : jVietQR.getString("advance_type");
            config.setPaymentPeriodVietQR(paymentPeriodVietQR);
            config.setCutOffTimeVietQR(jVietQR.getInteger("active") == 0 ? 24 : jVietQR.getInteger("hour"));
            config.setHourPaymentVietQR(jVietQR.getString("period"));
            config.setInsertAdvTxnFuncVietQR(jVietQR.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncVietQR(jVietQR.getString("get_adv_txn_func"));
        }

        config.setjDD(rs.getString("j_dd"));
        if (rs.getString("j_dd") != null) {
            JsonObject jDD = new JsonObject(rs.getString("j_dd"));
            String paymentPeriodDD = jDD.getInteger("active") == 0 ? "inactive" : jDD.getString("advance_type");
            config.setPaymentPeriodDD(paymentPeriodDD);
            config.setCutOffTimeDD(jDD.getInteger("active") == 0 ? 24 : jDD.getInteger("hour"));
            config.setHourPaymentDD(jDD.getString("period"));
            config.setInsertAdvTxnFuncDD(jDD.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncDD(jDD.getString("get_adv_txn_func"));
        }

        config.setJSMS(rs.getString("J_SMS"));
        if (rs.getString("J_SMS") != null) {
            JsonObject jSMS = new JsonObject(rs.getString("J_SMS"));
            config.setInsertAdvTxnFuncSMS(jSMS.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncSMS(jSMS.getString("get_adv_txn_func"));
        }
        config.setjNotification(rs.getString("J_NOTIFICATION"));

        if (rs.getString("J_NOTIFICATION") != null) {
            JsonObject jNotification = new JsonObject(rs.getString("J_NOTIFICATION"));
            config.setFile(jNotification.getString("file"));
            config.setZip(jNotification.getString("zip"));
            config.setAutoSendMail(jNotification.getString("auto_send_mail"));
        }
        config.setJAdvanceAccount(new Gson().fromJson(rs.getString("J_ADVANCE_ACCOUNT"), Util.MAP_STRING_OBJECT));
        config.setAdvanceTime(rs.getString("S_ADVANCE_TIME"));
        config.setJMonthlyFee(rs.getString("J_MONTHLY_FEE"));
        config.setJHold(rs.getString("J_HOLD"));
        if (rs.getString("J_HOLD") != null) {
            JsonObject jHold = new JsonObject(rs.getString("J_HOLD"));
            config.setHoldDay(jHold.getInteger("active") == 0 ? 0 : jHold.getInteger("hold_day"));
            config.setInsertHoldFdFunc(jHold.getString("insert_hold_fd_func"));
            config.setInsertHoldFunc(jHold.getString("insert_hold_func"));
        }

        config.setIdFeeMonth(rs.getLong("N_FEEMONTH_CONFIG_ID"));
        config.setTransactionType(rs.getString("S_TRANSACTION_TYPE"));
        config.setNotifyMethod(rs.getString("S_NOTIFY_METHOD"));
        config.setReceiptType(rs.getString("S_TYPE_RECEIPT"));
        config.setMergeMonthlyReport(rs.getString("S_MERGE_MONTHLY_REPORT"));
        config.setAddendum(rs.getString("S_ADDENDUM"));
        config.setControlMinutes(rs.getString("S_CONTROL_MINUTES"));
        config.setJBilling(rs.getString("J_BILLING"));
        if (rs.getString("J_BILLING") != null) {
            JsonObject jBilling = new JsonObject(rs.getString("J_BILLING"));
            String paymentPeriodBilling = jBilling.getInteger("active") == 0 ? "inactive" : jBilling.getString("advance_type");
            config.setPaymentPeriodBilling(paymentPeriodBilling);
            config.setCutOffTimeBilling(jBilling.getInteger("active") == 0 ? 24 : jBilling.getInteger("hour"));
            config.setHourPaymentBNPL(jBilling.getString("period"));
            config.setInsertAdvTxnFuncBilling(jBilling.getString("insert_adv_txn_func"));
            config.setGetAdvTxnFuncBilling(jBilling.getString("get_adv_txn_func"));
        }
        config.setJBNPL(rs.getString("J_BNPL"));
        String jData = rs.getString("J_DATA");
        Map<String, Object> map = new HashMap<>();
        if (jData != null) {
            map = new Gson().fromJson(jData, Util.MAP_STRING_OBJECT);
        }
        if (map.get("merchant_type") == null) {
            map.put("merchant_type", "");
        }

        if (map.get("auto_hold") != null) {
            config.setAutoHold(map.get("auto_hold").equals(1.0));
        } else {
            map.put("auto_hold", 1);
            config.setAutoHold(true);
        }
        JsonObject jsonData = new JsonObject(jData);
        if (jsonData.getString("merchant_type") == null) {
            jsonData.put("merchant_type", "");
        }
        config.setjData(jsonData.toString());
        config.setSourceConfig(null);
        if (rsSource != null) {
            ConfigDto configSource = new ConfigDto();
            configSource.setId(rsSource.getLong("N_ID"));
            configSource.setAdvanceAccount(rsSource.getString("s_advance_account"));
            configSource.setContractCode(rsSource.getString("s_contract_code"));
            // configSource.setContractDate(rsSource.getString("D_CONTRACT_DATE") == null ? null :
            // java.sql.Timestamp.valueOf(rsSource.getString("D_CONTRACT_DATE")));
            configSource.setContractDate(rsSource.getDate("D_CONTRACT_DATE") == null ? null : rsSource.getDate("D_CONTRACT_DATE").getTime());
            configSource.setEffectiveDate(rsSource.getDate("D_EFFECTIVE_DATE") == null ? null : rsSource.getDate("D_EFFECTIVE_DATE").getTime());
            configSource.setExpirationDate(rsSource.getDate("D_EXPIRATION_DATE") == null ? null : rsSource.getDate("D_EXPIRATION_DATE").getTime());
            configSource.setContractType(rsSource.getString("s_contract_type"));
            configSource.setLastMerchantName(rsSource.getString("S_TEN_DV"));
            configSource.setService(rsSource.getString("s_pay_channel"));
            configSource.setTenDkkd(rsSource.getString("s_ten_dkkd"));
            configSource.setPartnerId(rsSource.getInt("n_partner_id"));
            configSource.setDayOfWeek(rsSource.getString("S_DAY_OF_WEEK"));
            configSource.setDayOfMonth(rsSource.getString("S_DAY_OF_MONTH"));
            configSource.setAdvanceAmount(rsSource.getString("S_ADVANCE_AMOUNT"));
            // configSource.setCutOffTime(rsSource.getInt("N_HOUR"));
            configSource.setGuaranteeAmount(rsSource.getDouble("n_guarantee_destination"));
            configSource.setPaymentPeriod(rsSource.getString("s_type_advance"));
            configSource.setAdvancePercent(rsSource.getDouble("N_ADVANCE_PERCENT"));
            configSource.setAdvanceBank(rsSource.getString("S_ADV_ACC_BANK_ID"));
            configSource.setAccountName(rsSource.getString("S_ACCOUNT_NAME"));
            configSource.setBranchName(rsSource.getString("S_BRANCH_NAME"));
            configSource.setParentConfig(rsSource.getInt("N_PARENT_CONFIG"));
            configSource.setAction(rsSource.getInt("n_active"));
            configSource.setTaxCode(rsSource.getString("S_TAX_CODE"));
            configSource.setGuaranteeHoldingType(rsSource.getString("S_GUARANTEE_HOLDING_TYPE"));
            configSource.setFlowType(rsSource.getInt("n_flow_type_id"));
            configSource.setState(rsSource.getString("s_approve"));
            configSource.setTemplateId(rsSource.getInt("N_TEMPLATE_EMAIL_ID"));
            configSource.setTemplateName(rsSource.getString("N_TEMPLATE_NAME") == null ? "" : rsSource.getString("N_TEMPLATE_NAME"));
            configSource.setNotes(rsSource.getString("S_NOTES"));
            StringBuilder conditions2 = new StringBuilder();
            String advanceTranFailed2 = rsSource.getString("S_ADVANCE_TRANS_FAILED");
            String advanceVCB2 = rsSource.getString("S_ADVANCE_VCB");
            if (advanceVCB2 != null && "YES".equals(advanceTranFailed2)) {
                conditions2.append(advanceVCB2);
                conditions2.append(",");
                conditions2.append("1");
            } else if ("YES".equals(advanceTranFailed2)) {
                conditions2.append("1");
            } else if (advanceVCB2 != null) {
                conditions2.append(advanceVCB2);
            }
            configSource.setAdvTranFailQT(advanceTranFailed2.equals("YES") ? true : false);
            configSource.setAdvanceTransFailed(advanceTranFailed2);
            configSource.setConditions(conditions2.toString());
            configSource.setCheckEnoughGuarantee(rsSource.getInt("N_ENOUGH_GUARANTEE"));
            configSource.setAccountingEntry(rsSource.getString("S_ACCOUNTING_ENTRY") == null ? "" : rsSource.getString("S_ACCOUNTING_ENTRY"));
            configSource.setMinAdvanceAmount(rsSource.getDouble("N_MIN_AMOUNT_ADV"));
            configSource.setTemplateFeeMonth(rsSource.getInt("N_TEMPLATE_FEE_MONTH"));
            configSource.setAttachFile(rsSource.getString("S_ATTACH_FILE"));
            configSource.setEmailFeeMonth(rsSource.getString("s_email_fee_month"));
            configSource.setDividePCRF("YES".equals(rsSource.getString("S_DIVIDE_PC_RF")));
            configSource.setAuthorizedPerson(rsSource.getString("S_AUTHORIZED_PERSON"));
            configSource.setAuthorizedExpirationDate(rsSource.getDate("D_AUTHORIZED_EXPIRATION_DATE") == null ? null : rsSource.getDate("D_AUTHORIZED_EXPIRATION_DATE").getTime());
            configSource.setGrabPromotion("YES".equals(rsSource.getString("S_GRAB_PROMOTION")));
            configSource.setCutoffFeeMonth(rsSource.getInt("N_DAY_CUTOFF_MONTH"));
            configSource.setOnepayBankId(rsSource.getString("S_ONEPAY_ACC_BANK_ID"));

            configSource.setFunctionCreateAdv(rsSource.getInt("N_FUNC_CREATE_ADV"));
            configSource.setFunctionCreateAdvParam(rsSource.getString("s_func_create_adv_param"));
            configSource.setFunctionCreateAdvDesc(rsSource.getString("s_func_create_adv_desc"));
            configSource.setFunctionCreateFeeMonth(rsSource.getString("N_FUNC_CREATE_REPORT_MONTH") == null ? 0 : rsSource.getInt("N_FUNC_CREATE_REPORT_MONTH"));
            configSource.setFunctionCreateFeeMonthParam(rsSource.getString("s_func_fee_month_param"));
            configSource.setLbfunctionCreateFeeMonthDesc(rsSource.getString("s_func_fee_month_desc"));

            configSource.setJQT(rsSource.getString("J_QT"));
            if (rsSource.getString("J_QT") != null) {
                JsonObject jQT = new JsonObject(rsSource.getString("J_QT"));
                String paymentPeriodQT = jQT.getInteger("active") == 0 ? "inactive" : jQT.getString("advance_type");
                configSource.setPaymentPeriod(paymentPeriodQT);
                configSource.setCutOffTime(jQT.getInteger("active") == 0 ? 17 : jQT.getInteger("hour"));
                configSource.setHourPayment(jQT.getString("period"));
                configSource.setInsertAdvTxnFuncQT(jQT.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncQT(jQT.getString("get_adv_txn_func"));
            }
            configSource.setJGuaranteeTimeHold(rsSource.getString("S_GUARANTEE_TIME_HOLD"));
            configSource.setJND(rsSource.getString("J_ND"));
            if (rsSource.getString("J_ND") != null) {
                JsonObject jND = new JsonObject(rsSource.getString("J_ND"));
                String paymentPeriodND = jND.getInteger("active") == 0 ? "inactive" : jND.getString("advance_type");
                configSource.setPaymentPeriodND(paymentPeriodND);
                configSource.setCutOffTimeND(jND.getInteger("active") == 0 ? 24 : jND.getInteger("hour"));
                configSource.setHourPaymentND(jND.getString("period"));
                configSource.setInsertAdvTxnFuncND(jND.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncND(jND.getString("get_adv_txn_func"));
            }
            configSource.setJQR(rsSource.getString("J_QR"));
            if (rsSource.getString("J_QR") != null) {
                JsonObject jQR = new JsonObject(rsSource.getString("J_QR"));
                String paymentPeriodQR = jQR.getInteger("active") == 0 ? "inactive" : jQR.getString("advance_type");
                configSource.setPaymentPeriodQR(paymentPeriodQR);
                configSource.setCutOffTimeQR(jQR.getInteger("active") == 0 ? 24 : jQR.getInteger("hour"));
                configSource.setHourPaymentQR(jQR.getString("period"));
                configSource.setInsertAdvTxnFuncQR(jQR.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncQR(jQR.getString("get_adv_txn_func"));
            }
            configSource.setJBNPL(rsSource.getString("J_BNPL"));
            if (rsSource.getString("J_BNPL") != null) {
                JsonObject jBNPL = new JsonObject(rsSource.getString("J_BNPL"));
                String paymentPeriodBNPL = jBNPL.getInteger("active") == 0 ? "inactive" : jBNPL.getString("advance_type");
                configSource.setPaymentPeriodBNPL(paymentPeriodBNPL);
                configSource.setCutOffTimeBNPL(jBNPL.getInteger("active") == 0 ? 24 : jBNPL.getInteger("hour"));
                configSource.setHourPaymentBNPL(jBNPL.getString("period"));
                configSource.setInsertAdvTxnFuncBNPL(jBNPL.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncBNPL(jBNPL.getString("get_adv_txn_func"));
            }

            configSource.setjPayCollect(rs.getString("j_paycollect"));
            if (rs.getString("j_paycollect") != null) {
                JsonObject jPayCollect = new JsonObject(rs.getString("j_paycollect"));
                String paymentPeriodPayCollect = jPayCollect.getInteger("active") == 0 ? "inactive" : jPayCollect.getString("advance_type");
                configSource.setPaymentPeriodPayCollect(paymentPeriodPayCollect);
                configSource.setCutOffTimePayCollect(jPayCollect.getInteger("active") == 0 ? 24 : jPayCollect.getInteger("hour"));
                configSource.setHourPaymentPayCollect(jPayCollect.getString("period"));
                configSource.setInsertAdvTxnFuncPayCollect(jPayCollect.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncPayCollect(jPayCollect.getString("get_adv_txn_func"));
            }
            configSource.setCashFlow(rs.getString("S_CASH_FLOW"));
            configSource.setInvoiceType(rs.getString("S_INVOICE_TYPE"));
            configSource.setInvoiceInterval(rs.getString("S_INVOICE_INTERVAL"));

            configSource.setjPayOut(rs.getString("j_payout"));
            if (rs.getString("j_payout") != null) {
                JsonObject jPayOut = new JsonObject(rs.getString("j_payout"));
                String paymentPeriodPayOut = jPayOut.getInteger("active") == 0 ? "inactive" : jPayOut.getString("advance_type");
                configSource.setPaymentPeriodPayOut(paymentPeriodPayOut);
                configSource.setCutOffTimePayOut(jPayOut.getInteger("active") == 0 ? 24 : jPayOut.getInteger("hour"));
                configSource.setHourPaymentPayOut(jPayOut.getString("period"));
                configSource.setInsertAdvTxnFuncPayOut(jPayOut.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncPayOut(jPayOut.getString("get_adv_txn_func"));
            }

            configSource.setjVietQR(rs.getString("j_vietqr"));
            if (rs.getString("j_vietqr") != null) {
                JsonObject jVietQR = new JsonObject(rs.getString("j_vietqr"));
                String paymentPeriodVietQR = jVietQR.getInteger("active") == 0 ? "inactive" : jVietQR.getString("advance_type");
                configSource.setPaymentPeriodVietQR(paymentPeriodVietQR);
                configSource.setCutOffTimeVietQR(jVietQR.getInteger("active") == 0 ? 24 : jVietQR.getInteger("hour"));
                configSource.setHourPaymentVietQR(jVietQR.getString("period"));
                configSource.setInsertAdvTxnFuncVietQR(jVietQR.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncVietQR(jVietQR.getString("get_adv_txn_func"));
            }
            configSource.setjDD(rs.getString("j_dd"));
            if (rs.getString("j_dd") != null) {
                JsonObject jDD = new JsonObject(rs.getString("j_dd"));
                String paymentPeriodDD = jDD.getInteger("active") == 0 ? "inactive" : jDD.getString("advance_type");
                configSource.setPaymentPeriodDD(paymentPeriodDD);
                configSource.setCutOffTimeDD(jDD.getInteger("active") == 0 ? 24 : jDD.getInteger("hour"));
                configSource.setHourPaymentDD(jDD.getString("period"));
                configSource.setInsertAdvTxnFuncDD(jDD.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncDD(jDD.getString("get_adv_txn_func"));
            }

            configSource.setJSMS(rsSource.getString("J_SMS"));
            if (rsSource.getString("J_SMS") != null) {
                JsonObject jSMS = new JsonObject(rsSource.getString("J_SMS"));
                configSource.setInsertAdvTxnFuncSMS(jSMS.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncSMS(jSMS.getString("get_adv_txn_func"));
            }


            configSource.setjNotification(rsSource.getString("J_NOTIFICATION"));
            if (rsSource.getString("J_NOTIFICATION") != null) {
                JsonObject jNotification = new JsonObject(rsSource.getString("J_NOTIFICATION"));
                configSource.setFile(jNotification.getString("file"));
                configSource.setZip(jNotification.getString("zip"));
                configSource.setAutoSendMail(jNotification.getString("auto_send_mail"));
            }
            configSource.setJAdvanceAccount(new Gson().fromJson(rsSource.getString("J_ADVANCE_ACCOUNT"), Util.MAP_STRING_OBJECT));
            configSource.setAdvanceTime(rsSource.getString("S_ADVANCE_TIME"));
            configSource.setJMonthlyFee(rsSource.getString("J_MONTHLY_FEE"));
            configSource.setJHold(rsSource.getString("J_HOLD"));
            if (rsSource.getString("J_HOLD") != null) {
                JsonObject jHold = new JsonObject(rsSource.getString("J_HOLD"));
                configSource.setHoldDay(jHold.getInteger("active") == 0 ? 0 : jHold.getInteger("hold_day"));
                configSource.setInsertHoldFdFunc(jHold.getString("insert_hold_fd_func"));
                configSource.setInsertHoldFunc(jHold.getString("insert_hold_func"));
            }

            configSource.setIdFeeMonth(rsSource.getLong("N_FEEMONTH_CONFIG_ID"));
            configSource.setTransactionType(rsSource.getString("S_TRANSACTION_TYPE"));
            configSource.setNotifyMethod(rsSource.getString("S_NOTIFY_METHOD"));
            configSource.setReceiptType(rsSource.getString("S_TYPE_RECEIPT"));
            configSource.setMergeMonthlyReport(rsSource.getString("S_MERGE_MONTHLY_REPORT"));
            configSource.setAddendum(rsSource.getString("S_ADDENDUM"));
            configSource.setControlMinutes(rsSource.getString("S_CONTROL_MINUTES"));
            configSource.setJBilling(rsSource.getString("J_BILLING"));
            if (rsSource.getString("J_BILLING") != null) {
                JsonObject jBilling = new JsonObject(rsSource.getString("J_BILLING"));
                String paymentPeriodBilling = jBilling.getInteger("active") == 0 ? "inactive" : jBilling.getString("advance_type");
                configSource.setPaymentPeriodBilling(paymentPeriodBilling);
                configSource.setCutOffTimeBilling(jBilling.getInteger("active") == 0 ? 24 : jBilling.getInteger("hour"));
                configSource.setInsertAdvTxnFuncBilling(jBilling.getString("insert_adv_txn_func"));
                configSource.setGetAdvTxnFuncBilling(jBilling.getString("get_adv_txn_func"));
            }
            configSource.setJBNPL(rsSource.getString("J_BNPL"));
            String rsSourceJData = rsSource.getString("J_DATA");
            Map<String, Object> mapSource = new HashMap<>();
            if (rsSourceJData != null) {
                mapSource = new Gson().fromJson(rsSourceJData, Util.MAP_STRING_OBJECT);
            }
            if (mapSource.get("merchant_type") == null) {
                mapSource.put("merchant_type", "");
            }
            configSource.setjData(jData);
            configSource.setObjectFee(rsSource.getString("S_OBJECT_FEE"));
            //giang comment lỗi code
//            if(mapSource.get("auto_hold") == null){
//                mapSource.put("merchant_type","");
//            }

            if (mapSource.get("auto_hold") != null) {
                configSource.setAutoHold(mapSource.get("auto_hold").equals(1.0));
            } else {
                mapSource.put("auto_hold", 1);
                configSource.setAutoHold(true);
            }
            configSource.setjData(rsSourceJData);
            config.setSourceConfig(configSource);
        }

        // Thêm binding cho auto configuration
        config.setIsAutoEnabled(rs.getInt("N_IS_AUTO_ENABLED"));
        config.setAutoDays(rs.getString("S_AUTO_DAYS"));
        config.setAutoStepLevel(rs.getString("S_AUTO_STEP_LEVEL"));
        config.setEmailNotifyPayment(rs.getString("S_EMAIL_NOTIFY_PAYMENT"));
        config.setEmailNotifyAccount(rs.getString("S_EMAIL_NOTIFY_ACCOUNT"));
        config.setEmailNotifyBod(rs.getString("S_EMAIL_NOTIFY_BOD"));

        return config;
    }

    public static List<Map<String, Object>> getTemplateEmail() throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> listfl = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_EMAIL_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get template email error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> fl = new HashMap<>();
                    fl.put("id", rs.getInt("N_ID"));
                    fl.put("name", rs.getString("S_NAME"));
                    fl.put("detail", rs.getString("S_DETAIL"));
                    listfl.add(fl);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return listfl;
    }

    public static List<Map<String, Object>> getTemplateFeeMonth() throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> listTemplate = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_FEE_MONTH_TEMPLATE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB GET LIST TEMPLATE ERROR: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> fl = new HashMap<>();
                    fl.put("id", rs.getInt("N_ID"));
                    fl.put("name", rs.getString("S_NAME"));
                    listTemplate.add(fl);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return listTemplate;
    }

    public static List<FlowTypeDto> getListFlowType() throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<FlowTypeDto> listfl = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_FLOW_TYPE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get list flow type error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    FlowTypeDto fl = new FlowTypeDto();
                    fl.setId(rs.getInt("N_ID"));
                    fl.setName(rs.getString("S_NAME"));
                    fl.setDesc(rs.getString("s_DESC"));
                    listfl.add(fl);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return listfl;
    }

    public static List<PartnerConfigDto> getListPartner() throws SQLException {
        List<PartnerConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_LIST_PARTNER(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get list partner error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    PartnerConfigDto cf = new PartnerConfigDto();
                    cf.setPartnerId(rs.getInt("N_ID"));
                    cf.setPartnerName(rs.getString("S_PARTNER_NAME"));
                    cf.setShortName(rs.getString("S_SHORT_NAME"));
                    config.add(cf);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return config;
    }

    public static long getIdDrafConfig(long id) throws SQLException {
        int drafId = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.check_draft_config(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setLong(4, id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            drafId = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get id draf config error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return drafId;
    }

    /**
     * check cấu hình tạm ứng gửi lên có thay đổi gì so với cấu hình tạm ứng gốc không?
     * @param config Cấu hình tạm ứng mới lấy từ request gửi lên
     * @return mRes Map chứa 4 key: check_change_update (check thay đổi những trường cần duyệt), check_change (check thay đổi những trường không cần duyệt), n_result (mã kết quả update), s_result (nội dung kết quả update)
     */
    public static Map<String, Object> checkChangeApprovedConfig(ConfigDto config) throws SQLException {
        Map<String, Object> mRes = new HashMap<>();
        mRes.put("check_change_update", 0);
        mRes.put("check_change", 0);
        mRes.put("n_result", 0);
        mRes.put("s_result", "");
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.CHECK_APPROVED_CONFIG_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");//73
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setObject(5, config.getParentConfig());
            cs.setObject(6, config.getPartnerId());
            cs.setString(7, config.getContractCode());
            cs.setString(8, config.getTaxCode());
            cs.setInt(9, config.getAction());
            cs.setString(10, config.getPaymentPeriod());
            cs.setObject(11, config.getCutOffTime());
            cs.setObject(12, config.getAdvancePercent());
            cs.setString(13, config.getDayOfWeek());
            cs.setString(14, config.getDayOfMonth());
            cs.setString(15, config.getAdvanceAmount());
            cs.setString(16, config.getLastMerchantName());
            cs.setString(17, config.getTenDkkd());
            cs.setString(18, config.getBank());
            cs.setString(19, config.getAdvanceAccount());
            cs.setString(20, config.getBranchName());
            cs.setString(21, config.getAccountName());
            cs.setObject(22, config.getGuaranteeAmount());
            cs.setObject(23, config.getGuaranteeHoldingType());
            cs.setObject(24, config.getFlowType());
            cs.setObject(25, config.getTemplateId());
            cs.setString(26, config.getNotes());
            cs.setObject(27, config.getCheckEnoughGuarantee());
            cs.setString(28, config.getAccountingEntry());
            cs.setObject(29, config.getMinAdvanceAmount());
            cs.setString(30, config.getAdvanceVCB());
            cs.setString(31, config.getAdvanceTransFailed());
            cs.setInt(32, config.getTemplateFeeMonth());
            cs.setObject(33, config.getContractDate() == null ? null : new Date(config.getContractDate()));
            cs.setString(34, config.getAttachFile());
            cs.setString(35, config.getService() == null ? null : config.getService());
            cs.setString(36, config.getEmailFeeMonth());
            cs.setString(37, config.isDividePCRF() ? null : null);
            cs.setString(38, config.getAuthorizedPerson());
            cs.setObject(39, config.getAuthorizedExpirationDate() == null ? null : new Date(config.getAuthorizedExpirationDate()));
            cs.setString(40, config.isGrabPromotion() ? null : null);
            cs.setInt(41, config.getCutoffFeeMonth());
            cs.setString(42, config.getOnepayBankId());
            cs.setString(43, config.getJQT());
            cs.setString(44, config.getJGuaranteeTimeHold());
            cs.setString(45, config.getJND());
            cs.setString(46, config.getJQR());
            cs.setString(47, config.getJSMS());
            cs.setString(48, config.getAdvanceTime());
            cs.setString(49, config.getJMonthlyFee());
            cs.setString(50, config.getJHold());
            cs.setString(51, JsonObject.mapFrom(config.getJAdvanceAccount()).encode());
            cs.setLong(52, config.getIdFeeMonth());
            cs.setString(53, config.getTransactionType());
            cs.setString(54, config.getNotifyMethod());
            cs.setInt(55, config.getFunctionCreateAdv());
            cs.setString(56, config.getFunctionCreateAdvParam());
            cs.setInt(57, config.getFunctionCreateFeeMonth());
            cs.setString(58, config.getFunctionCreateFeeMonthParam());
            cs.setString(59, config.getReceiptType());
            cs.setString(60, config.getMergeMonthlyReport());
            cs.setString(61, config.getAddendum());
            cs.setString(62, config.getControlMinutes());
            cs.setObject(63, config.getEffectiveDate() == null ? null : new Date(config.getEffectiveDate()));
            cs.setString(64, config.getJBilling());
            cs.setString(65, config.getJBNPL());
            cs.setString(66, config.getJUPOS());
            cs.setString(67, config.getjNotification());
            cs.setInt(68, !config.isAutoHold() ? 0 : 1);
            cs.setString(69, config.getObjectFee());
            cs.setString(70, config.getjPayCollect());
            cs.setString(71, config.getjPayOut());
            cs.setString(72, config.getFormula());
            cs.setObject(73, config.getExpirationDate() == null ? null : new Date(config.getExpirationDate()));
            cs.setString(74, config.getjVietQR());
            cs.setString(75, config.getjDD());
            cs.setString(76, config.getInvoiceType());
            cs.setString(77, config.getInvoiceInterval());
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            int countCheckChangeUpdate = cs.getInt(1);
            int countCheckChange = cs.getInt(2);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get approved config error: {0}", error);
            }
            mRes.put("check_change_update", countCheckChangeUpdate);
            mRes.put("check_change", countCheckChange);
            mRes.put("n_result", nerror);
            mRes.put("s_result", error);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mRes;
    }


    public static Map<String, Object> countWaitForApproveConfig(long id) throws SQLException {
        Map<String, Object> mRes = new HashMap<>();
        mRes.put("id", 0);
        mRes.put("n_result", 0);
        mRes.put("s_result", "");
        mRes.put("listConfigId", null);
        int vCheck = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<ConfigDto> listConfigId = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.COUNT_WAIT_FOR_APPROVE_CONFIG(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setLong(5, id);
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            vCheck = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB COUNT WAIT FOR APPROVE CONFIG: ", error);
            } else {
                while (rs != null && rs.next()) {
                    ConfigDto configId = new ConfigDto();
                    configId.setId(rs.getLong("N_ID"));
                    listConfigId.add(configId);
                }
            }
            mRes.put("count_config", vCheck);
            mRes.put("n_result", nerror);
            mRes.put("s_result", error);
            mRes.put("listConfigId", listConfigId);

            logger.log(Level.INFO, "LIST CONFIG ID WAIT FOR APPROVE: " + listConfigId.toString());
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mRes;
    }

    public static Map<String, Object> updateInsertConfig(ConfigDto config) throws SQLException {
        Map<String, Object> mRes = new HashMap<>();
        mRes.put("id", 0);
        mRes.put("n_result", 0);
        mRes.put("s_result", "");
        int nerror = 500;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int id = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.INSERT_CONFIG3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");//84
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setObject(4, config.getParentConfig());
            cs.setObject(5, config.getPartnerId());
            cs.setString(6, config.getContractCode());
            cs.setString(7, config.getTaxCode());
            cs.setInt(8, config.getAction());
            cs.setString(9, config.getPaymentPeriod());
            cs.setObject(10, config.getCutOffTime());
            cs.setObject(11, config.getAdvancePercent());
            cs.setString(12, config.getDayOfWeek());
            cs.setString(13, config.getDayOfMonth());
            cs.setString(14, config.getAdvanceAmount());
            cs.setString(15, config.getLastMerchantName());
            cs.setString(16, config.getTenDkkd());
            cs.setString(17, config.getBank());
            cs.setString(18, config.getAdvanceAccount());
            cs.setString(19, config.getBranchName());
            cs.setString(20, config.getAccountName());
            cs.setObject(21, config.getGuaranteeAmount());
            cs.setObject(22, config.getGuaranteeHoldingType());
            cs.setObject(23, config.getFlowType());
            cs.setObject(24, config.getTemplateId());
            cs.setString(25, config.getNotes());
            cs.setObject(26, config.getCheckEnoughGuarantee());
            cs.setString(27, config.getAccountingEntry());
            cs.setObject(28, config.getMinAdvanceAmount());
            cs.setString(29, config.getAdvanceVCB());
            cs.setString(30, config.getAdvanceTransFailed());
            cs.setInt(31, config.getTemplateFeeMonth());
            // cs.setString(32, contractDate);
            cs.setObject(32, config.getContractDate() == null ? null : new Date(config.getContractDate()));
            cs.setString(33, config.getAttachFile());
            cs.setString(34, config.getService() == null ? "" : config.getService());
            cs.setString(35, config.getEmailFeeMonth());
            cs.setString(36, config.isDividePCRF() ? "YES" : "NO");
            cs.setString(37, config.getAuthorizedPerson());
            cs.setObject(38, config.getAuthorizedExpirationDate() == null ? null : new Date(config.getAuthorizedExpirationDate()));
            cs.setString(39, config.isGrabPromotion() ? "YES" : "NO");
            cs.setInt(40, config.getCutoffFeeMonth());
            cs.setString(41, config.getOnepayBankId());
            cs.setString(42, config.getJQT());
            cs.setString(43, config.getJGuaranteeTimeHold());
            cs.setString(44, config.getJND());
            cs.setString(45, config.getJQR());
            cs.setString(46, config.getJSMS());
            cs.setString(47, config.getAdvanceTime());
            cs.setString(48, config.getJMonthlyFee());
            cs.setString(49, config.getJHold());
            cs.setString(50, JsonObject.mapFrom(config.getJAdvanceAccount()).encode());
            cs.setLong(51, config.getIdFeeMonth());
            cs.setString(52, config.getTransactionType());
            cs.setString(53, config.getNotifyMethod());
            cs.setInt(54, config.getFunctionCreateAdv());
            cs.setString(55, config.getFunctionCreateAdvParam());
            cs.setInt(56, config.getFunctionCreateFeeMonth());
            cs.setString(57, config.getFunctionCreateFeeMonthParam());
            cs.setString(58, config.getReceiptType());
            cs.setString(59, config.getMergeMonthlyReport());
            cs.setString(60, config.getAddendum());
            cs.setString(61, config.getControlMinutes());
            cs.setObject(62, config.getEffectiveDate() == null ? null : new Date(config.getEffectiveDate()));
            cs.setString(63, config.getJBilling());
            cs.setString(64, config.getJBNPL());
            cs.setString(65, config.getJUPOS());
            cs.setString(66, config.getjNotification());
            cs.setString(67, config.getjPayCollect());
            cs.setString(68, config.getjDataStr());
            cs.setInt(69, !config.isAutoHold() ? 0 : 1);
            cs.setString(70, config.getObjectFee());
            cs.setString(71, config.getjPayOut());
            cs.setString(72, config.getFormula());
            cs.setString(73, config.getjVietQR());
            cs.setString(74, config.getjDD());
            cs.setObject(75,  config.getExpirationDate() == null ? null : new Date(config.getExpirationDate()));
            cs.setString(76, config.getCashFlow());
            cs.setString(77, config.getInvoiceType());
            cs.setString(78, config.getInvoiceInterval());
            cs.setInt(79, config.getIsAutoEnabled());           // N_IS_AUTO_ENABLED
            cs.setString(80, config.getAutoDays());                // S_AUTO_DAYS
            cs.setString(81, config.getAutoStepLevel());           // S_AUTO_STEP_LEVEL
            cs.setString(82, config.getEmailNotifyPayment());      // S_EMAIL_NOTIFY_PAYMENT
            cs.setString(83, config.getEmailNotifyAccount());      // S_EMAIL_NOTIFY_ACCOUNT
            cs.setString(84, config.getEmailNotifyBod());          // S_EMAIL_NOTIFY_BOD
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            id = cs.getInt(3);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB update insert config error: {0}", error);
            }
            mRes.put("id", id);
            mRes.put("n_result", nerror);
            mRes.put("s_result", error);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mRes;
    }

    public static Map<String, Object> updateConfigApproved(ConfigDto config) throws SQLException {
        Map<String, Object> mRes = new HashMap<>();
        mRes.put("n_result", 0);
        mRes.put("s_result", "");
        int nerror = 500;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.UPDATE_CONFIG_APPROVED_2(?,?,?,?,?,?,?,?,?,?,?," +
                    "?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, config.getParentConfig());
            cs.setObject(4, config.getTemplateId());
            cs.setInt(5, config.getTemplateFeeMonth());
            cs.setString(6, config.getEmailFeeMonth());
            cs.setInt(7, config.getCutoffFeeMonth());
            cs.setLong(8, config.getIdFeeMonth());
            cs.setString(9, config.getNotifyMethod());
            cs.setString(10, config.getReceiptType());
            cs.setString(11, config.getMergeMonthlyReport());
            cs.setString(12, config.getControlMinutes());
            cs.setString(13, config.getjNotification());
            cs.setString(14, config.getFormula());
            cs.setDate(15,  config.getExpirationDate() == null ? null : new Date(config.getExpirationDate()));
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB update config approved error: {0}", error);
            }
            mRes.put("n_result", nerror);
            mRes.put("s_result", error);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mRes;
    }

    public static Map<String, Object> updateDrafConfig(ConfigDto config, long drafId) throws SQLException {
        Map<String, Object> mRes = new HashMap<>();
        mRes.put("id", 0);
        mRes.put("n_result", 0);
        mRes.put("s_result", "");
        int nerror = 500;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.UPDATE_INDEB_CONFIG2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}"); //81
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, config.getPartnerId());
            cs.setString(4, config.getContractCode());
            cs.setString(5, config.getTaxCode());
            cs.setInt(6, config.getAction());
            cs.setString(7, config.getPaymentPeriod());
            cs.setObject(8, config.getCutOffTime());
            cs.setDouble(9, config.getAdvancePercent());
            cs.setString(10, config.getDayOfWeek());
            cs.setString(11, config.getDayOfMonth());
            cs.setString(12, config.getAdvanceAmount());
            cs.setString(13, config.getLastMerchantName());
            cs.setString(14, config.getTenDkkd());
            cs.setString(15, config.getBank());
            cs.setString(16, config.getAdvanceAccount());
            cs.setString(17, config.getBranchName());
            cs.setString(18, config.getAccountName());
            cs.setObject(19, config.getGuaranteeAmount());
            cs.setObject(20, config.getGuaranteeHoldingType());
            cs.setLong(21, drafId);
            cs.setObject(22, config.getFlowType());
            cs.setObject(23, config.getTemplateId());
            cs.setString(24, config.getNotes());
            cs.setInt(25, config.getCheckEnoughGuarantee());
            cs.setString(26, config.getAccountingEntry());
            cs.setDouble(27, config.getMinAdvanceAmount());
            cs.setString(28, config.getAdvanceVCB());
            cs.setString(29, config.getAdvanceTransFailed());
            cs.setInt(30, config.getTemplateFeeMonth());
            // cs.setString(31, contractDate);
            cs.setObject(31, config.getContractDate() == null ? null : new Date(config.getContractDate()));
            cs.setString(32, config.getAttachFile());
            cs.setString(33, config.getEmailFeeMonth());
            cs.setString(34, config.isDividePCRF() ? "YES" : "NO");
            cs.setString(35, config.getAuthorizedPerson());
            cs.setObject(36, config.getAuthorizedExpirationDate() == null ? null : new Date(config.getAuthorizedExpirationDate()));
            cs.setString(37, config.isGrabPromotion() ? "YES" : "NO");
            cs.setInt(38, config.getCutoffFeeMonth());
            cs.setString(39, config.getOnepayBankId());

            cs.setString(40, config.getJQT());
            cs.setString(41, config.getJGuaranteeTimeHold());
            cs.setString(42, config.getJND());
            cs.setString(43, config.getJQR());
            cs.setString(44, config.getJSMS());
            cs.setString(45, config.getAdvanceTime());
            cs.setString(46, config.getJMonthlyFee());
            cs.setString(47, config.getJHold());

            cs.setString(48, JsonObject.mapFrom(config.getJAdvanceAccount()).encode());
            cs.setLong(49, config.getIdFeeMonth());
            cs.setString(50, config.getTransactionType());
            cs.setString(51, config.getNotifyMethod());
            cs.setInt(52, config.getFunctionCreateAdv());
            cs.setString(53, config.getFunctionCreateAdvParam());
            cs.setInt(54, config.getFunctionCreateFeeMonth());
            cs.setString(55, config.getFunctionCreateFeeMonthParam());
            cs.setString(56, config.getReceiptType());
            cs.setString(57, config.getMergeMonthlyReport());
            cs.setString(58, config.getService());
            cs.setString(59, config.getAddendum());
            cs.setString(60, config.getControlMinutes());
            cs.setObject(61, config.getEffectiveDate() == null ? null : new Date(config.getEffectiveDate()));
            cs.setString(62, config.getJBilling());
            cs.setString(63, config.getJBNPL());
            cs.setString(64, config.getJUPOS());
            cs.setString(65, config.getjNotification());
            cs.setString(66, config.getjPayCollect());
            cs.setString(67, config.getjDataStr());
            cs.setString(68, config.getObjectFee());
            cs.setString(69, config.getjPayOut());
            cs.setString(70, config.getjVietQR());
            cs.setString(71, config.getjDD());
            cs.setObject(72,config.getExpirationDate() == null ? null :  new Date(config.getExpirationDate()));
            cs.setString(73, config.getCashFlow());
            cs.setString(74, config.getInvoiceType());
            cs.setString(75, config.getInvoiceInterval());
            cs.setInt(76, config.getIsAutoEnabled());           // N_IS_AUTO_ENABLED
            cs.setString(77, config.getAutoDays());                // S_AUTO_DAYS
            cs.setString(78, config.getAutoStepLevel());           // S_AUTO_STEP_LEVEL
            cs.setString(79, config.getEmailNotifyPayment());      // S_EMAIL_NOTIFY_PAYMENT
            cs.setString(80, config.getEmailNotifyAccount());      // S_EMAIL_NOTIFY_ACCOUNT
            cs.setString(81, config.getEmailNotifyBod());          // S_EMAIL_NOTIFY_BOD
            cs.executeQuery();
            String error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB update config error: {0}", error);
            }
            mRes.put("id", 0);
            mRes.put("n_result", nerror);
            mRes.put("s_result", error);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mRes;
    }

    public static List<Map<String, Object>> getListContractCode(String partnerId, String configId, String typeGet) throws SQLException {
        List<Map<String, Object>> contractCodeList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_LIST_CONTRACT_CODE(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Integer.parseInt(partnerId));
            cs.setInt(5, Integer.parseInt(configId));
            cs.setString(6, typeGet);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get CONTRACT CODE error: {0}", error);
            } else {
                if (rs != null) {
                    contractCodeList = Util.resultSetToList(rs);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return contractCodeList;
    }

    public static List<Map<String, Object>> listMerchantIdNotInConfig(Integer partnerId, String service, Integer indebtednessConfigId) throws SQLException {
        List<Map<String, Object>> merchantIdList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.get_merchant_id_not_in_config_2(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.setString(5, service);
            cs.setInt(6, indebtednessConfigId);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get get_merchant_id_not_in_config error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> merchantcf = new HashMap<>();
                    merchantcf.put("merchantId", rs.getString("S_MERCHANT_ID"));
                    merchantcf.put("service", rs.getString("S_SERVICE_ID"));
                    merchantIdList.add(merchantcf);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return merchantIdList;
    }

    public static List<MerchantConfigDto> getListEmail(int id) throws SQLException {
        List<MerchantConfigDto> mailConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_EMAIL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get list email error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    mailConfig.add(bindEmail(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mailConfig;
    }

    private static MerchantConfigDto bindEmail(ResultSet rs) throws SQLException {
        MerchantConfigDto config = new MerchantConfigDto();
        config.setMerchantId(rs.getString("S_MERCHANT_ID"));
        config.setCurrency(rs.getString("S_CURRENCY"));
        config.setEmail(rs.getString("S_EMAIL") != null ? rs.getString("S_EMAIL") : "");
        config.setState(rs.getString("S_STATE"));
        config.setService(rs.getString("S_PAY_CHANNEL").replaceAll("DD","Direct Debit"));
        config.setActive(rs.getString("S_ACTIVE"));
        config.setApprovedDate(Util.getColumnTimeStamp(rs, "D_APPROVE"));
        config.setCreatedDate(Util.getColumnTimeStamp(rs, "D_CREATE"));
        config.setUpdatedDate(Util.getColumnTimeStamp(rs, "D_UPDATE"));
        return config;
    }

    public static List<MerchantConfigDto> getListEmailbyMerchant(int id, String merchantId) throws SQLException {
        List<MerchantConfigDto> mailConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_EMAIL_BY_MERCHANT(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.setString(5, merchantId);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get list email by merchant error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    mailConfig.add(bindEmail(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return mailConfig;
    }

    public static void updateConfig(MerchantConfigDto config, int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.UPDATE_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, config.getMerchantId());
            cs.setString(5, config.getEmail());
            cs.setString(6, config.getActive());
            cs.setString(7, config.getService());
            cs.setString(8, config.getState());
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB update config error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    public static void updatePaygate(String payGate, int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.UPDATE_INDEBTEDNESS_PAYGATE(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, payGate);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB get get_merchant_id_not_in_config error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    public static void insertConfig(MerchantConfigDto config, int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.INSERT_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, config.getMerchantId());
            cs.setString(5, config.getEmail());
            cs.setString(6, config.getActive());
            cs.setString(7, config.getService());
            cs.setString(8, config.getState());
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB insert config error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    public static Integer insertUpdateConfigMerchant(MerchantConfigDto config, int id, String action) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        Integer result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.INSERT_UPDATE_CONFIGMERCHANT(?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, config.getMerchantId());
            cs.setString(5, config.getEmail());
            cs.setString(6, config.getActive());
            cs.setString(7, config.getService());
            cs.setString(8, config.getState());
            cs.setString(9, action);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 502) {
                logger.log(Level.SEVERE, "DB PROC INSERT_UPDATE_CONFIGMERCHANT " + error);
                return 502;
            } else if (nerror != 200 && nerror != 502) {
                logger.log(Level.SEVERE, "DB PROC INSERT_UPDATE_CONFIGMERCHANT " + error);
                return 0;
            }
            return 1;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static MerchantConfigDto getConfigMerchantApproved(MerchantConfigDto config, int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        MerchantConfigDto dto = new MerchantConfigDto();
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.GET_CONFIGMERCHANT_APPROVE(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, config.getMerchantId());
            cs.setInt(5, id);
            cs.setString(6, config.getService());

            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB PROC GET_CONFIGMERCHANT_APPROVE error:", error);
            } else {
                while (rs != null && rs.next()) {
                    dto = bindEmail(rs);
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return dto;
    }

    public static void approvedConfigMerchant(MerchantConfigDto config, int id, String approveStatus) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.APPROVED_CONFIGMERCHANT(?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, config.getMerchantId());
            cs.setString(5, config.getEmail());
            cs.setString(6, config.getActive());
            cs.setString(7, config.getService().replaceAll("Direct Debit","DD"));
            cs.setString(8, config.getState());
            cs.setString(9, approveStatus);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB PROC APPROVED_CONFIGMERCHANT error:", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    public static Integer checkConflicConfigMerchant(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 1;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.CHECK_APPROVED_CONFIGMERCHANT(?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, id);
            cs.executeQuery();
            result = cs.getInt(1);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static Integer checkUpdateConfigMerchant(int id, MerchantConfigDto config) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 1;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.CHECK_UPDATE_CONFIG_MERCHANT(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, id);
            cs.setString(3, config.getMerchantId());
            cs.setString(4, config.getService());
            cs.setString(5, config.getState());
            cs.setString(6, config.getActive());
            cs.executeQuery();
            result = cs.getInt(1);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static Integer checkConflicIndebtednessConfig(int id, String transactionTypes) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Integer result = 1;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.CHECK_UPDATE_INDEBTEDNESS_CONFIG(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, id);
            cs.setString(3, transactionTypes);
            cs.executeQuery();
            result = cs.getInt(1);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static void insertConfigMerchantLog(String dataNew, String dataOld, String user, String action, String system) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.INSERT_TB_HISTORY_LOG(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, dataNew);
            cs.setString(4, dataOld);
            cs.setString(5, user);
            cs.setString(6, action);
            cs.setString(7, system);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB PROC INSERT_TB_HISTORY_LOG error:", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    public static Map<String, Object> updateStateConfig(int id, String merchantId, String state, String stateChange, String email, String service) throws SQLException {
        Map<String, Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.APPROVAL_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, state);
            cs.setInt(4, id);
            cs.setString(5, merchantId);
            cs.setString(6, stateChange);
            cs.setString(7, email);
            cs.setString(8, service);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB update state config error: {0}", error);
            }
            data.put("n_result", nerror);
            data.put("s_result", error);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        data.put("error", error + nerror);
        return data;
    }

    public static Map<String, Object> deleteConfigDraf(int id, String merchantId, String email, String state) throws SQLException {
        Map<String, Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.DELETE_EMAIL_DRAF(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, merchantId);
            cs.setString(5, email);
            cs.setString(6, state);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB delete draf config error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        data.put("error", error + nerror);
        return data;
    }

    public static List<ConfigDto> searchConfig(ConfigQueryDto queryDto) throws SQLException {
        List<ConfigDto> config = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.name());
            cs.setString(5, queryDto.getKeyword());
            cs.setInt(6, queryDto.getPage());
            cs.setInt(7, queryDto.getPageSize());
            cs.setString(8, queryDto.getPaymentPeriod());
            cs.setString(9, queryDto.getAdvanceBank());
            cs.setObject(10, queryDto.getState());
            cs.setString(11, queryDto.getService());
            cs.setString(12, queryDto.getFormula());
            cs.setString(13, queryDto.getContractType());
            cs.setString(14, queryDto.getGuaranteeAmount());
            cs.setString(15, queryDto.getGuaranteeHoldingType());
            cs.setString(16, queryDto.getWithoutMerchant());
            cs.setString(17, queryDto.getStatusConfig());
            cs.setString(18, queryDto.getStateOfEffective());
            cs.setString(19, queryDto.getFromDate());
            cs.setString(20, queryDto.getToDate());
            cs.setString(21, queryDto.getMerchantIDWaitForApprove());
            cs.setString(22, queryDto.getAdvanceAmount());
            cs.setString(23, queryDto.getOnepayBankId());
            cs.setString(24, queryDto.getAdvanceAccountTypes());
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 500) {
                logger.log(Level.SEVERE, "DB get config list error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    config.add(bindConfig(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return config;
    }

    public static List<Map<String, Object>> getListOnepayBank() throws SQLException {
        List<Map<String, Object>> list = new ArrayList<>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            String query = "select * from ( SELECT \n" +
                    "t.*,decode(t.s_bank_id,'VIETCOMBANK',1,'TECHCOMBANK',2,'VIETINBANK',3) n_order \n" +
                    "FROM onedata.tbl_bank t where t.n_nd_bank_id is not null and t.S_SHORT_NAME not like '%-NP') \n" +
                    "order by n_order,s_bank_id";
            ps = con.prepareStatement(query);
            rs = ps.executeQuery();
            if (rs != null) {
                list = Util.resultSetToList(rs);
            }
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        return list;
    }

    public static Map<String, Object> checkExistMerchantConfig(long idParentConfig, String merchantId, String service) throws SQLException {
        Map<String, Object> data = new HashMap<>();
        data.put("n_result", 0);
        data.put("s_result", "do_nothing");
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.CHECK_EXIST_MERCHANTID(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, idParentConfig);
            cs.setString(4, merchantId);
            cs.setString(5, service);

            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            data.put("n_result", nerror);
            data.put("s_result", error);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB CHECK_EXIST_MERCHANTID error: {0}", error);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        data.put("error", error + nerror);
        return data;
    }

    public static List<Map<String, Object>> getListFuncFeeMonth() throws SQLException {
        List<Map<String, Object>> list = new ArrayList<>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            String query = "select * \n" +
                    "FROM onefin.tb_func t where t.s_type='calculate_monthly_fee' \n" +
                    "order by n_id";
            ps = con.prepareStatement(query);
            rs = ps.executeQuery();
            if (rs != null) {
                list = Util.resultSetToList(rs);
            }
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        return list;
    }

    public static List<Map<String, Object>> getPayoutMerchantAccounts() throws SQLException {
        List<Map<String, Object>> payoutAccounts = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{? = call ONEFIN.get_payout_merchant_account()}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.executeQuery();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                Map<String, Object> dto = new HashMap<String, Object>();
                dto.put("payout_merchant_account_number", rs.getString("payout_merchant_account_number"));
                dto.put("payout_merchant_account_name", rs.getString("payout_merchant_account_name"));
                dto.put("payout_merchant_id", rs.getString("payout_merchant_id"));
                payoutAccounts.add(dto);
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return payoutAccounts;
    }
}
