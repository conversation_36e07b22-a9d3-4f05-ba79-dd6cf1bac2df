package vn.onepay.portal.resources.fee.funcfee;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.fee.funcfee.dto.FeeViewDto;
import vn.onepay.portal.resources.fee.funcfee.dto.IndebConfigDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class FuncFeeHandler {
    private static Logger logger = Logger.getLogger(FuncFeeHandler.class.getName());

    public static void getFuncFee(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                temp.put("funcfee", FuncFeeDao.getFuncFee());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, temp);
        }, false, null);

    }

    public static void getFeeData(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String keyword = request.getParam("keyword").length() == 0?"":request.getParam("keyword");
            int page = request.getParam("page").length()==0 ? 0:Integer.parseInt(request.getParam("page"));
            int page_size = request.getParam("page_size").length() == 0 ? 50:Integer.parseInt(request.getParam("page_size"));
            String contract_type = request.getParam("contract_type").length() == 0?"":request.getParam("contract_type");
            String service_type = request.getParam("service_type").length() == 0?"":request.getParam("service_type");
            String paygate = request.getParam("paygate").length() == 0?"":request.getParam("paygate");
            Map<String, Object> temp = new HashMap<>();
            try {
                temp = FuncFeeDao.getFeeData(keyword,contract_type,service_type,paygate,page,page_size);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, temp);
        }, false, null);
    }
//    public static void getFeeDataDetail(RoutingContext ctx) {
//        ctx.vertx().executeBlocking(future -> {
//            HttpServerRequest request = ctx.request();
//            String contractCode = request.getParam("contractCode");
//            Map<String, Object> temp = new HashMap<>();
//            try {
//                List<IndebConfigDto> listConfig =  FuncFeeDao.getIndebConfig(contractCode);
//                List<FeeDetailDto> listFeeDetail = FuncFeeDao.getFeebyContract(contractCode);
//                for (int i = 0;listConfig.size()>i;i++){
//                    IndebConfigDto config = listConfig.get(i);
//                    ArrayList<FeeDetailDto>listFeeDetailbyMerchant = new ArrayList<>();
//                    for(int y = 0;listFeeDetail.size()>y;y++){
//                        FeeDetailDto feeDetail = listFeeDetail.get(y);
//                        if(feeDetail.getMerchant_id().equals(config.getMerchantId())){
//                            listFeeDetailbyMerchant.add(feeDetail);
//                        }
//                    }
//                    config.setListFeeDetail(listFeeDetailbyMerchant);
//                }
//                temp.put("data",listConfig);
//
//            } catch (Exception e) {
//                
//            }
//            sendResponse(ctx, 200, temp);
//        }, false, null);
//    }

    public static void getFeeViewDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String contractCode = request.getParam("contractCode");
            Map<String, Object> temp = new HashMap<>();
            try {
                List<IndebConfigDto> listConfig =  FuncFeeDao.getIndebConfig(contractCode);
                List<FeeViewDto> feeViewDtoList = FuncFeeDao.getFeeViewbyContract(contractCode);
                for (int i = 0;listConfig.size()>i;i++){
                    IndebConfigDto config = listConfig.get(i);
                    ArrayList<FeeViewDto>listFeeDetailbyMerchant = new ArrayList<>();
                    for(int y = 0;feeViewDtoList.size()>y;y++){
                        FeeViewDto feeDetail = feeViewDtoList.get(y);
                        if(feeDetail.getMerchantId().equals(config.getMerchantId())){
                            listFeeDetailbyMerchant.add(feeDetail);
                        }
                    }
                    config.setFeeViewDtoList(listFeeDetailbyMerchant);
                }
                temp.put("data",listConfig);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, temp);
        }, false, null);
    }
}
