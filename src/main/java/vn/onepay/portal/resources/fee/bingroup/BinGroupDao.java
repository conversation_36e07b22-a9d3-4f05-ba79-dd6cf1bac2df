package vn.onepay.portal.resources.fee.bingroup;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.fee.bingroup.dto.BinGroupDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class BinGroupDao extends Db implements IConstants {
    public static List<BinGroupDto> getBinGroup() throws Exception {
        Exception exception = null;
        List<BinGroupDto> binGroup = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_BIN_GROUP(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get bin group list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    binGroup.add(blindbinGroup(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return binGroup;
    }
    private static BinGroupDto blindbinGroup(ResultSet rs) throws Exception{
        BinGroupDto temp = new BinGroupDto();
        temp.setId(rs.getInt("N_ID"));
        temp.setName(rs.getString("S_NAME"));
        temp.setDesc(rs.getString("S_DESC"));
        temp.setPriority(rs.getString("N_PRIORITY"));
        temp.setBankId(rs.getString("N_BANK_ID"));
        temp.setService(rs.getString("S_SERVICE"));
        temp.setCardType(rs.getString("S_CARD_TYPE"));
        return temp;
    }
}
