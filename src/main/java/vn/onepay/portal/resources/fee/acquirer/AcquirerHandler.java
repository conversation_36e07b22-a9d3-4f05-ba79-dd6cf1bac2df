package vn.onepay.portal.resources.fee.acquirer;

import io.vertx.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class AcquirerHandler {
    private static Logger logger = Logger.getLogger(AcquirerHandler.class.getName());
    public static void getAcquirer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {

            Map<String,Object> temp = new HashMap<>();
            try {
                temp.put("acquirer", AcquirerDao.getAcquirer());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ERROR: ", e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);

    }
}
