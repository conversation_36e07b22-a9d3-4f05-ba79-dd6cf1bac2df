package vn.onepay.portal.resources.fee.template;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.template.dto.TemplateDto;
import vn.onepay.portal.resources.fee.template.dto.TemplateFeeInputDto;
import vn.onepay.portal.resources.user.UserDao;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import static vn.onepay.portal.IConstants.X_USER_ID;
import static vn.onepay.portal.Util.sendResponse;


public class FeeTemplateHandler {
    private static Logger logger = Logger.getLogger(FeeTemplateHandler.class.getName());
    public static void getAllTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                temp = FeeTemplateDao.getAlltemplate();
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void getTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String keyword = request.getParam("keyword") == null ? "":request.getParam("keyword");
            int page_size = request.getParam("page_size") == null ? 50 : Integer.parseInt(request.getParam("page_size"));
            int page = request.getParam("page") == null ? 0 :Integer.parseInt(request.getParam("page"));
            Map<String,Object> temp = new HashMap<>();
            try {
                temp = FeeTemplateDao.getTemplate(keyword,page_size,page);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);

    }

    public static void getTemplateFee(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> data = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                int id = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                TemplateDto template = FeeTemplateDao.getTemplatebyID(id);
                data.put("id", template.getId());
                data.put("name", template.getName());
                data.put("desc", template.getDesc());
                data.put("state", template.getState());
                data.put("feedata",FeeTemplateDao.getTemplateDetail(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, data);
        }, false, null);
    }

    public static void getTemplateFeeConfig(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> data = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                int template_id = request.getParam("template_id") == null ? 0 : Integer.parseInt(request.getParam("template_id"));
                TemplateDto template = FeeTemplateDao.getTemplatebyID(template_id);
                data.put("id", template.getId());
                data.put("name", template.getName());
                data.put("desc", template.getDesc());
                data.put("state", template.getState());
                data.put("feedata",FeeTemplateDao.getTemplateDetail(template_id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200, data);
        }, false, null);
    }

    public static void insertTemplate(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            int templateId = 0;
            Integer userId = ctx.get(X_USER_ID);
            if (userId == null) {
                logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String userName = "";
            try {
                userName = UserDao.get(userId).getName();
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            JsonObject body = ctx.getBodyAsJson();
            TemplateDto templ = new TemplateDto();
            templ.setName(body.getString("name"));
            templ.setDesc(body.getString("desc"));
            try {
                templateId =  FeeTemplateDao.insertTemplate(templ);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            JsonArray feetempl = body.getJsonArray("feedata");
            int error = 0;
            if (feetempl.size()>0){
                List<TemplateFeeInputDto> l = new ArrayList<>();
                feetempl.forEach(o->l.add(bindFeeTemplate((JsonObject) o)));
                for(TemplateFeeInputDto template : l){
                    try {
                        error = FeeTemplateDao.insertTemplateFee(template,userName,templateId);
                    } catch (Exception e) {
                        logger.log(Level.SEVERE, "Error: ", e);
                    }
                }
            }
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("error", error);
            sendResponse(ctx, 200,returnMap);
        }, false, null);
    }
    public static void deleteTemplate(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            int templateId = 0;
            Integer userId = ctx.get(X_USER_ID);
            if (userId == null) {
                logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            HttpServerRequest request = ctx.request();
            int id = Integer.parseInt(request.getParam("id"));
            try {
                templateId =  FeeTemplateDao.deleteTemplate(id);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("error", templateId);
            sendResponse(ctx, templateId,returnMap);
        }, false, null);
    }

    public static void updateTemplate(RoutingContext ctx){
        ctx.vertx().executeBlocking(future -> {
            Integer userId = ctx.get(X_USER_ID);
            if (userId == null) {
                logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                throw IErrors.VALIDATION_ERROR;
            }
            String userName = "";
            try {
                userName = UserDao.get(userId).getName();
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            JsonObject body = ctx.getBodyAsJson();
            TemplateDto templ = new TemplateDto();
            templ.setId(Integer.parseInt(body.getString("id")));
            templ.setName(body.getString("name"));
            templ.setDesc(body.getString("desc"));
            try {
                FeeTemplateDao.updateTemplate(templ);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
//          TemplateFeeDto[] data = gson.fromJson(feetempl.toString(),TemplateFeeDto[].class);
            JsonArray feetempl = body.getJsonArray("feedata");
            List<TemplateFeeInputDto> l = new ArrayList<>();
            feetempl.forEach(o->l.add(bindFeeTemplate((JsonObject) o)));

            if (l != null) {
                try {
                    FeeTemplateDao.deleteTempaltefee(templ.getId());
                } catch (Exception e) {
                    logger.severe("delete template error: " + e.getMessage());
                    logger.log(Level.SEVERE, "Error: ", e);
                }
            }
            int error = 0;
            for(TemplateFeeInputDto template : l){
                try {
                    error = FeeTemplateDao.insertTemplateFee(template,userName,templ.getId());
                } catch (Exception e) {
                    logger.severe("delete template error: " + e.getMessage());
                    logger.log(Level.SEVERE, "Error: ", e);
                }
            }
            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("error", error);
            sendResponse(ctx, error,returnMap);
        }, false, null);
    }

    private static TemplateFeeInputDto bindFeeTemplate(JsonObject rs){
        TemplateFeeInputDto temp = new TemplateFeeInputDto();
        temp.setService(rs.getString("service"));
        temp.setPaygate(rs.getString("paygate"));
        temp.setAcquirer(rs.getString("acquirer"));
        temp.setBinGroupId(rs.getString("binGroupId").length()==0?null:Integer.parseInt(rs.getString("binGroupId")));
        temp.setTransactionType(rs.getString("transactionType"));

        temp.setFixFee(rs.getDouble("fixFee") == null || rs.getDouble("fixFee").toString().equals("0") ? null : rs.getDouble("fixFee"));
        temp.setPercentFee(rs.getDouble("percentFee") == null  || rs.getDouble("percentFee").toString().equals("0") ? null : rs.getDouble("percentFee"));
        SimpleDateFormat  inputDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        SimpleDateFormat   outputDate = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss aa");
        Date fromDate = null, toDate = null;
        try {
            fromDate = inputDate.parse(rs.getString("fromDate"));
            if (fromDate != null) {
                String fromDates = outputDate.format(fromDate);
                temp.setFromDate(fromDates);
            } else {
                temp.setFromDate(null);
            }
            toDate = inputDate.parse(rs.getString("toDate"));
            if (toDate != null) {
                String toDates = outputDate.format(toDate);
                temp.setToDate(toDates);
            } else {
                temp.setToDate(null);
            }
        } catch (Exception ex) {
            logger.severe("error: " + ex);
        }
        temp.setType(rs.getString("type"));
        temp.setFuncId(rs.getString("funcId"));
        temp.setFuncInput(rs.getString("funcInput"));
        temp.setOrder(rs.getString("order").length()==0?null:Integer.parseInt(rs.getString("order")));
        temp.setCurrency(rs.getString("currency"));
        temp.setContractType(rs.getString("contractType"));
        return temp;
    }
    private final static Gson gson = new Gson();
}
