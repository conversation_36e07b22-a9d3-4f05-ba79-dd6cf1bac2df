package vn.onepay.portal.resources.fee.approval;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantFeeApprovalHandler {
    private static Logger logger = Logger.getLogger(MerchantFeeApprovalHandler.class.getName());
    public static void getApprovalMerchantFee(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String contractType = request.getParam("contractType").length() == 0 ? null:request.getParam("contractType");
            String contractCode = request.getParam("contractCode").length() == 0 ? null:request.getParam("contractCode");
            String merchantId = request.getParam("merchantId").length() == 0 ? null:request.getParam("merchantId");
            String partnerName = request.getParam("partnerName").length() == 0 ? null:request.getParam("partnerName");
            String service = request.getParam("service").length() == 0 ? null:request.getParam("service");
            String acquirer = request.getParam("acquirer").length() == 0 ? null:request.getParam("acquirer");
            String payGate = request.getParam("payGate").length() == 0 ? null:request.getParam("payGate");
            int page = request.getParam("page").length()==0 ? 0:Integer.parseInt(request.getParam("page"));
            int page_size = request.getParam("page_size").length()==0? 50:Integer.parseInt(request.getParam("page_size"));
            Integer bin_group = request.getParam("bin_group").length() == 0 ? null: Integer.parseInt(request.getParam("bin_group"));
            String transactionType = request.getParam("transactionType").length() == 0 ? null:request.getParam("transactionType");
            Map<String,Object> temp = new HashMap<>();
            try {
                temp = MerchantFeeApprovalDao.getApprovalFeeMerchant(contractType,contractCode,merchantId,partnerName,service,acquirer,payGate,bin_group,transactionType,page,page_size);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error: ", e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);

    }
    public static void approvalMerchantFee(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject body = ctx.getBodyAsJson();
            JsonArray uploadFeeData = body.getJsonArray("approval");
            if (uploadFeeData != null) {
                String listId = uploadFeeData.getString(0);

                for (int i = 1; i < uploadFeeData.size(); i++) {
                    listId += ',' + uploadFeeData.getString(i);
                }
                Map<String,Object> temp = new HashMap<>();
                try {
                    MerchantFeeApprovalDao.approvalMerchantState(listId);
                    temp.put("approval", "Approval Success");
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Error: ", e);
                }
                sendResponse(ctx, 200, temp);
            }
        }, false, null);

    }
}
