package vn.onepay.portal.resources.on_off_banks;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.ExportReceiptDAO;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.on_off_banks.dto.MspEmail;
import vn.onepay.portal.resources.on_off_banks.dto.OnOffBank;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.IConstants.*;
import static vn.onepay.portal.Util.sendResponse;

public class OnOffBanksHandler {
    public static final Logger logger = Logger.getLogger(OnOffBanksHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void onOffBanksSearch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Map mIn = new HashMap();
                mIn.put("keyWord", request.getParam("keyWord"));
                mIn.put(PAGE, request.getParam(PAGE));
                mIn.put(PAGESIZE, request.getParam(PAGESIZE));

                Util.sendResponse(ctx, 200, OnOffBankDao.onOffBanksSearch(mIn));
            } catch (Exception ex) {
                logger.log(Level.INFO, "onOffBanksSearch error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void activeOrInactiveBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();

                if (!body.containsKey(BANKID) || !body.containsKey(CURRENT_STATE))
                    throw IErrors.VALIDATION_ERROR;
                OnOffBank onOffBank = gson.fromJson(body.encode(), OnOffBank.class);

                Util.sendResponse(ctx, 200, OnOffBankDao.activeOrInactiveBank(onOffBank));
            } catch (Exception ex) {
                logger.log(Level.INFO, "activeOrInactiveBank error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void onOffBanksApprovalSearch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Map mIn = new HashMap();
                mIn.put("keyWord", request.getParam("keyWord"));
                mIn.put(PAGE, request.getParam(PAGE));
                mIn.put(PAGESIZE, request.getParam(PAGESIZE));

                Util.sendResponse(ctx, 200, OnOffBankDao.onOffBanksApprovalSearch(mIn));
            } catch (Exception ex) {
                logger.log(Level.INFO, "disableBank error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void approveOrReject(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                if (!body.containsKey(NID) || !body.containsKey(USER_NAME))
                    throw IErrors.VALIDATION_ERROR;
                OnOffBank onOffBank = gson.fromJson(body.encode(), OnOffBank.class);

                Util.sendResponse(ctx, 200, OnOffBankDao.approveOrReject(onOffBank));
            } catch (Exception ex) {
                logger.log(Level.FINE, "onOffBanksApprove error ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void onOffApprovalHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Map mIn = new HashMap();
                mIn.put("keyWord", request.getParam("keyWord"));
                mIn.put(PAGE, request.getParam(PAGE));
                mIn.put(PAGESIZE, request.getParam(PAGESIZE));
                mIn.put(FROMDATE, request.getParam(FROMDATE) == "" ? null : request.getParam(FROMDATE));
                mIn.put(TODATE, request.getParam(TODATE) == "" ? null : request.getParam(TODATE));

                Util.sendResponse(ctx, 200, OnOffBankDao.onOffBanksApprovalHistory(mIn));
            } catch (Exception ex) {
                logger.log(Level.FINE, "onOffBanksReject error ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void onOffBanksDownload(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                Map<String, Object> mIn = new HashMap<>();
                mIn.put("keyWord", request.getParam("keyWord"));
                mIn.put(FROMDATE, request.getParam(FROMDATE) == "" ? null : request.getParam(FROMDATE));
                mIn.put(TODATE, request.getParam(TODATE) == "" ? null : request.getParam(TODATE));
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "On_Off_Bank_Approval_History_" + date;
                String fileHashName = "";
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("on_off_banks_approval_history");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                int totalRows = OnOffBankDao.getTotalApprovalHistory(mIn);
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.FINE, "onOffBanksDownload error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    // on off email
    public static void sendOnEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                HttpServerRequest request = ctx.request();
                int bank_id = request.getParam("bank_id") == null ? 0 : Integer.parseInt(request.getParam("bank_id"));
                String bank_name = body.getString("bank_name") == null ? "" : body.getString("bank_name");
                // list email user
                List<MspEmail> mspEmailList = OnOffBankDao.getMspEmailList(bank_id);
                if (mspEmailList.size() > 0) {
                    String bcc = mspEmailList.stream().map(MspEmail::getEmail).collect(Collectors.joining(","));

                    String tittleUser = "OnePay - Thông báo Ngân hàng " + bank_name + " đã hoạt động bình thường / " + bank_name + " system is back on";

                    String contentUser = "Kính gửi: Quý khách hàng,<br/><br/>"
                            + "Công ty CP TM&DV Trực tuyến OnePay (OnePay) xin thông báo hệ thống thanh toán trực tuyến của Ngân hàng " + bank_name + " đã hoạt động trở lại bình thường.<br/><br/>"
                            + "Hiện tại, Quý khách hàng đã có thể thực hiện giao dịch thanh toán trực tuyến qua hệ thống Ngân hàng " + bank_name + ".<br/><br/>"
                            + "<i>Đây là Email được gửi tự động, vui lòng không trả lời Email này.</i><br/><br/>"
                            + "Mọi yêu cầu hỗ trợ khác, vui lòng liên hệ:<br/>"
                            + "Bộ phận hỗ trợ khách hàng<br/>"
                            + "Tòa nhà BIDV, 194 Trần Quang Khải, Hoàn Kiếm, Hà Nội<br/>"
                            + "Tel: (84) 24 ********<br/><br/>" + "Email: <EMAIL><br/>"
                            + "Trân trọng thông báo!<br/><br/>"
                            + "                     ----------------------                  <br/><br/>"
                            + "To: Customer,<br/><br/>"
                            + "OnePay would like to inform you that " + bank_name + " system is back on.<br/><br/>"
                            + "Currently, you can make online payment transactions via " + bank_name + " system.<br/><br/>"
                            + "<i>This is an email sent automatically, please do not reply to this email.</i><br/><br/>"
                            + "For any other support requirements, please contact the following information:<br/>"
                            + "Service Support Department<br/>"
                            + "BIDV tower, 194 Tran Quang Khai, Hoan Kiem, Hanoi<br/>"
                            + "Tel: (84) 24 ********<br/>"
                            + "Email: <a href='mailto:<EMAIL>'><EMAIL></a> | <a href='www.onepay.vn'>www.onepay.vn</a><br/><br/>"
                            + "Thanks,<br/>"
                            + "OnePay JSC<br/>"
                            + "<div style='text-align: left; padding: 15px 0px;'><img src='https://www.onepay.vn/home/<USER>/onepay/images/logo-onoff.png' height=60><div><br/>";

                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank id: " + bank_id);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank name: " + bank_name);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Send vi email bcc" + bcc);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "mail title: " + tittleUser);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "mail content: " + contentUser);
                    MailUtil.sendMailWithBCC(Config.getString("", ""), "", bcc, tittleUser, contentUser,"html");
                } else {
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "No email user found");
                }

                // list email merchant
                String merchantBcc = OnOffBankDao.getOnePartnerEmailList(bank_id);
                // Mail for merchant
                String tittleMerchant = "OnePay – Thông báo Ngân hàng " + bank_name + " đã hoạt động bình thường / " + bank_name + " system is back on";

                String contentMerchant = "Kính gửi: Quý đơn vị,<br/><br/>"
                        + "Công ty CP TM &DV Trực tuyến OnePay (OnePay) xin thông báo hệ thống thanh toán trực tuyến của Ngân hàng " + bank_name + " đã hoạt động trở lại bình thường.<br/><br/>"
                        + "Hiện tại, Quý khách hàng đã có thể thực hiện giao dịch thanh toán trực tuyến qua hệ thống Ngân hàng " + bank_name + ".<br/><br/>"
                        + "<i>Đây là Email được gửi tự động, vui lòng không trả lời Email này.</i><br/><br/>"
                        + "Mọi yêu cầu hỗ trợ khác, vui lòng liên hệ:<br/>"
                        + "Bộ phận hỗ trợ khách hàng<br/>"
                        + "Tòa nhà BIDV, 194 Trần Quang Khải, Hoàn Kiếm, Hà Nội<br/>"
                        + "Tel: (84) 24 ********<br/><br/>" + "Email: <a href='mailto:<EMAIL>'><EMAIL></a> | <a href='www.onepay.vn '>www.onepay.vn </a><br/><br/>"
                        + "Trân trọng thông báo!<br/><br/>"
                        + "                     ---------------------------                 <br/><br/>"
                        + "To: Merchant, <br/><br/>"
                        + "OnePay would like to inform you that "+bank_name+" system is back on. <br/><br/>"
                        + "Currently, the customers can make online payment transactions via SHB system. <br/><br/>"
                        + "<i>This is an email sent automatically, please do not reply to this email.</i> <br/><br/>"
                        + "For any other support requirements, please contact the following information:<br/>"
                        + "Service Support Department <br/>"
                        + "BIDV tower, 194 Tran Quang Khai, Hoan Kiem, Hanoi<br/>"
                        + "Tel: (84) 24 ********<br/>"
                        + "Email: <a href='mailto:<EMAIL>'><EMAIL></a> | <a href='www.onepay.vn'>www.onepay.vn</a><br/><br/>"
                        + "Thanks,<br/>"
                        + "OnePay JSC <br/><br/>"
                        + "<div style='text-align: left; padding: 15px 0px;'><img src='https://www.onepay.vn/home/<USER>/onepay/images/logo-onoff.png' height=60><div><br/>";

                logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank id: " + bank_id);
                logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank name: " + bank_name);
                logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank name: " + bank_name);
                logger.info(ctx.get(REQUEST_UUID) + ": " + "Send en email bcc" + merchantBcc);
                logger.info(ctx.get(REQUEST_UUID) + ": " + "mail title: " + tittleMerchant);
                logger.info(ctx.get(REQUEST_UUID) + ": " + "mail content: " + contentMerchant);
                if (!"".equals(merchantBcc)) {
                    MailUtil.sendMailWithBCC(Config.getString("", ""), "", merchantBcc, tittleMerchant, contentMerchant, "html");
                }
                sendResponse(ctx, 200, OnOffBankDao.updateMspEmail(bank_id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "UPDATE ONSITE ERROR: ", e);
                ctx.fail(e);
            }

        }, false, null);
    }

    public static void sendOffEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                HttpServerRequest request = ctx.request();
                int bank_id = request.getParam("bank_id") == null ? 0 : Integer.parseInt(request.getParam("bank_id"));
                String onePartnerEmailList = OnOffBankDao.getOnePartnerEmailList(bank_id);
                if (onePartnerEmailList != null) {
                    String bank_name = body.getString("bank_name") == null ? "" : body.getString("bank_name");

                    String tittle = "OnePay – Thông báo gián đoạn dịch vụ của Ngân hàng " + bank_name + " / " + bank_name + " service interruption";

                    String content = "Kính gửi: Quý đơn vị,<br/><br/>"
                            + "Công ty CP TM &DV Trực tuyến OnePay (OnePay) xin thông báo hệ thống thanh toán trực tuyến của Ngân hàng " + bank_name + " đang có sự cố gián đoạn dịch vụ.<br/><br/>"
                            + "Hiện tại, khách hàng không thể thực hiện giao dịch thanh toán trực tuyến qua hệ thống Ngân hàng " + bank_name + ".<br/><br/>"
                            + "OnePay đã thông báo tới Ngân hàng " + bank_name + " để kiểm tra và khắc phục tình trạng này.<br/><br/>"
                            + "OnePay sẽ cập nhật tới Quý đơn vị khi hệ thống thanh toán trực tuyến của Ngân hàng " + bank_name + " hoạt động bình thường trở lại.<br/><br/>"
                            + "<i>Đây là Email được gửi tự động, vui lòng không trả lời Email này.</i><br/><br/>"
                            + "Mọi yêu cầu hỗ trợ khác, vui lòng liên hệ:<br/>"
                            + "Bộ phận hỗ trợ khách hàng<br/>"
                            + "Tòa nhà BIDV, 194 Trần Quang Khải, Hoàn Kiếm, Hà Nội<br/>"
                            + "Tel: (84) 24 ********<br/>"
                            + "Email: <a href='mailto:<EMAIL>'><EMAIL></a> | <a href='www.onepay.vn'>www.onepay.vn </a><br/><br/>"
                            + "Trân trọng thông báo!<br/><br/>"
                            + "                     --------------------------                       <br/><br/>"
                            + "To: Merchants,<br/><br/>"
                            + "OnePay would like to inform you that " + bank_name + " system is interrupted.<br/><br/>"
                            + "Currently, the customers can not make online payment transactions via " + bank_name + " system.<br/><br/>"
                            + "OnePay has notified " + bank_name + " to check and fix this situation.<br/><br/>"
                            + "OnePay will update to you when " + bank_name + " online payment system is back on.<br/><br/>"
                            + "<i>This is an email sent automatically, please do not reply to this email.</i><br/><br/>"
                            + "For any other support requirements, please contact the following information: <br/>"
                            + "Service Support Department<br/>"
                            + "BIDV tower, 194 Tran Quang Khai, Hoan Kiem, Hanoi<br/>"
                            + "Tel: (84) 24 ********<br/>"
                            + "Email: <a href='mailto:<EMAIL>'><EMAIL></a> | <a href='www.onepay.vn'>www.onepay.vn </a><br/><br/>"
                            + "Thanks,<br/>"
                            + "OnePay JSC<br/><br/>"
                            + "<div style='text-align: left; padding: 15px 0px;'><img src='https://www.onepay.vn/home/<USER>/onepay/images/logo-onoff.png' height=60><div><br/>";

                    String cc = body.getString(CC) == null ? BLANK : body.getString(CC);
                    String bcc = onePartnerEmailList;
                    content += body.getString(CONTENT) == null ? BLANK : body.getString(CONTENT);

                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank id: " + bank_id);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Bank name: " + bank_name);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "send email to "
                            + Config.getString("email.enable_bank", ""));
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "send email cc " + cc);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "Send email bcc" + bcc);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "mail title: " + tittle);
                    logger.info(ctx.get(REQUEST_UUID) + ": " + "mail content: " + content);
                    MailUtil.sendMailWithBCC(Config.getString("", ""), cc, bcc, tittle, content, "html");
                    sendResponse(ctx, 200, new JsonObject());
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "UPDATE ONSITE ERROR: ", e);
                ctx.fail(e);
            }

        }, false, null);
    }

}
