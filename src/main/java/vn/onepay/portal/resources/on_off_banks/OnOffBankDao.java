package vn.onepay.portal.resources.on_off_banks;

import com.google.gson.Gson;
import oracle.jdbc.OracleTypes;
import io.vertx.core.json.JsonObject;

import oracle.jdbc.OracleTypes;
import org.exolab.castor.types.Date;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.on_off_banks.dto.MspEmail;
import vn.onepay.portal.resources.on_off_banks.dto.OnOffBank;
import vn.onepay.portal.resources.on_off_banks.dto.OnOffBankApproval;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class OnOffBankDao extends Db implements IConstants {
    private static final Logger logger = Logger.getLogger(OnOffBanksHandler.class.getName());
    private static final Gson gson = new Gson();
    private static SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

    private static final String ON_OFF_BANKS_SEARCH = "{call PKG_ONOFF_BANK.onoff_banks_search(?,?,?,?,?,?,?)}";
    private static final String ACTIVE_INACTIVE_BANK = "{call PKG_ONOFF_BANK.active_inactive_bank(?,?,?,?,?,?,?,?)}";
    private static final String ON_OFF_BANKS_APPROVAL_SEARCH = "{call PKG_ONOFF_BANK.onoff_banks_approval_search(?,?,?,?,?,?,?)}";
    private static final String REJECT_APPROVE_ON_OFF_BANK = "{call PKG_ONOFF_BANK.reject_approve_onoff_bank(?,?,?,?,?,?,?,?,?)}";
    private static final String ON_OFF_BANKS_APPROVAL_HISTORY = "{call PKG_ONOFF_BANK.onoff_banks_approval_history(?,?,?,?,?,?,?,?,?)}";
    private static final String MSP_EMAIL_LIST = "{call PKG_ONOFF_BANK.onoff_banks_email_list(?,?,?,?)}";
    private static final String UPDATE_CUSTOMER_EMAIL_STATE = "{call PKG_ONOFF_BANK.update_customer_email_state(?,?,?,?)}";
    
    public static BaseList<OnOffBank> onOffBanksSearch(Map mIn) throws Exception {
        BaseList<OnOffBank> results = new BaseList<>();
        List<OnOffBank> onOffBanks = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ON_OFF_BANKS_SEARCH);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString().trim());
            cs.setString(5, QUERY_SELECT);
            cs.setInt(6, Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setInt(7, Integer.parseInt(mIn.get(PAGESIZE).toString()));
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                onOffBanks.add(bindOnOffBank(rs));

            int totalItems = getTotalItems(conn, mIn);

            results.setTotalItems(totalItems);
            results.setList(onOffBanks);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return results;
    }

    private static int getTotalItems(Connection conn, Map mIn) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int totalRows = 0;
        try {
            cs = conn.prepareCall(ON_OFF_BANKS_SEARCH);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString());
            cs.setString(5, QUERY_TOTAL);
            cs.setNull(6, OracleTypes.NULL);
            cs.setNull(7, OracleTypes.NULL);
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                totalRows = Util.getColumnInteger(rs, "N_TOTAL");
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return totalRows;
    }

    private static OnOffBank bindOnOffBank(ResultSet rs) throws SQLException {
        OnOffBank onOffBank = new OnOffBank();

        onOffBank.setnId(Util.getColumnInteger(rs, "N_ID"));
        onOffBank.setBankId(Util.getColumnInteger(rs, "N_BANK_ID"));
        onOffBank.setDisableType(Util.getColumnString(rs, "S_DISABLE_TYPE"));
        onOffBank.setNote(Util.getColumnString(rs, "S_NOTE"));
        onOffBank.setBankName(Util.getColumnString(rs, "S_BANK_NAME"));
        onOffBank.setShortBankName(Util.getColumnString(rs, "S_SHORT_BANK_NAME"));
        if (!ENABLE.equals(onOffBank.getState()))
            onOffBank.setState(Util.getColumnString(rs, "S_STATE"));
        onOffBank.setLastUpdated(Util.getColumnTimeStamp(rs, "D_UPDATE"));

        return onOffBank;
    }

    public static ActionDto activeOrInactiveBank(OnOffBank onOffBank) throws Exception {
        ActionDto actionDto = new ActionDto();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ACTIVE_INACTIVE_BANK);
            cs.registerOutParameter(1, OracleTypes.INTEGER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, onOffBank.getBankId());
            if (ENABLE.equals(onOffBank.getState())) {
                cs.setString(4, ENABLE);
                cs.setString(5, WAIT_FOR_DISABLE);
            }
            if (DISABLE.equals(onOffBank.getState())) {
                cs.setString(4, DISABLE);
                cs.setString(5, WAIT_FOR_ENABLE);
            }
            cs.setString(6, onOffBank.getUserName());
            cs.setString(7, onOffBank.getBankName());
            cs.setString(8, gson.toJson(onOffBank));
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB active_inactive_bank error: " + sResult);

            actionDto.setSResult("OK");
            actionDto.setNResult(200);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return actionDto;
    }

    public static BaseList<OnOffBankApproval> onOffBanksApprovalSearch(Map mIn) throws Exception {
        BaseList<OnOffBankApproval> results = new BaseList<>();
        List<OnOffBankApproval> onOffBankApprovals = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ON_OFF_BANKS_APPROVAL_SEARCH);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString().trim());
            cs.setString(5, QUERY_SELECT);
            cs.setInt(6, Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setInt(7, Integer.parseInt(mIn.get(PAGESIZE).toString()));
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                onOffBankApprovals.add(bindOnOffApprovalBank(rs));

            int totalItems = getTotalApprovalItems(conn, mIn);

            results.setTotalItems(totalItems);
            results.setList(onOffBankApprovals);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return results;
    }

    private static int getTotalApprovalItems(Connection conn, Map mIn) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int totalRows = 0;
        try {
            cs = conn.prepareCall(ON_OFF_BANKS_APPROVAL_SEARCH);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString());
            cs.setString(5, QUERY_TOTAL);
            cs.setNull(6, OracleTypes.NULL);
            cs.setNull(7, OracleTypes.NULL);
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                totalRows = Util.getColumnInteger(rs, "N_TOTAL");
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return totalRows;
    }

    private static OnOffBankApproval bindOnOffApprovalBank(ResultSet rs) throws Exception {
        OnOffBankApproval onOffBankApproval = new OnOffBankApproval();

        onOffBankApproval.setnId(Util.getColumnInteger(rs, "N_ID"));
        onOffBankApproval.setBankName(Util.getColumnString(rs, "S_BANK_NAME"));
        onOffBankApproval.setShortBankName(Util.getColumnString(rs, "S_SHORT_BANK_NAME"));
        onOffBankApproval.setStatus(Util.getColumnString(rs, "S_STATE"));
        onOffBankApproval.setRequestUser(Util.getColumnString(rs, "S_CREATOR"));
        onOffBankApproval.setCreateDate(Util.getColumnTimeStamp(rs, "D_CREATE"));
        onOffBankApproval.setType(Util.getColumnString(rs, "S_TYPE"));
        onOffBankApproval.setApproveUser(Util.getColumnString(rs, "S_APPROVER"));
        onOffBankApproval.setApproveDate(Util.getColumnTimeStamp(rs, "D_UPDATE"));
        String data = Util.getColumnString(rs, "S_DATA");
        if (data != null && !data.isEmpty()) {
            OnOffBank onOffBank = gson.fromJson(data, OnOffBank.class);
            onOffBankApproval.setData(onOffBank);
        }

        return onOffBankApproval;
    }

    public static ActionDto approveOrReject(OnOffBank onOffBank) throws Exception {
        ActionDto actionDto = new ActionDto();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(REJECT_APPROVE_ON_OFF_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, onOffBank.getBankId());
            cs.setString(4, onOffBank.getUserName());
            cs.setString(5, onOffBank.getState());
            cs.setString(6, onOffBank.getDisableType());
            cs.setString(7, onOffBank.getNote());
            cs.setString(8, onOffBank.getAction());
            cs.setInt(9, onOffBank.getnId());
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB reject_approve_onoff_bank error: " + sResult);
            actionDto.setSResult("OK");
            actionDto.setNResult(200);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static BaseList<OnOffBankApproval> onOffBanksApprovalHistory(Map mIn) throws Exception {
        BaseList<OnOffBankApproval> results = new BaseList<>();
        List<OnOffBankApproval> onOffBankApprovalHis = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ON_OFF_BANKS_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString().trim());
            cs.setString(5, QUERY_SELECT);
            cs.setString(6, mIn.get(FROMDATE) == null ? "" : mIn.get(FROMDATE).toString());
            cs.setString(7, mIn.get(TODATE) == null ? "" : mIn.get(TODATE).toString());
            cs.setInt(8, Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setInt(9, Integer.parseInt(mIn.get(PAGESIZE).toString()));
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_approval_history error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                onOffBankApprovalHis.add(bindOnOffApprovalBank(rs));
            int totalItems = getTotalApprovalHistory(mIn);

            results.setTotalItems(totalItems);
            results.setList(onOffBankApprovalHis);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return results;
    }

    public static int getTotalApprovalHistory(Map mIn) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int totalRows = 0;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ON_OFF_BANKS_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString().trim());
            cs.setString(5, QUERY_TOTAL);
            cs.setString(6, mIn.get(FROMDATE) == null ? "" : mIn.get(FROMDATE).toString());
            cs.setString(7, mIn.get(TODATE) == null ? "" : mIn.get(TODATE).toString());
            cs.setNull(8, OracleTypes.NULL);
            cs.setNull(9, OracleTypes.NULL);
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                totalRows = Util.getColumnInteger(rs, "N_TOTAL");
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return totalRows;
    }

    public static List<OnOffBankApproval> approvalHistoryFile(Map<String, Object> mIn) throws Exception {
        List<OnOffBankApproval> onOffBankApprovals = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(ON_OFF_BANKS_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, mIn.get("keyWord") == null ? "" : mIn.get("keyWord").toString().trim());
            cs.setString(5, QUERY_DOWNLOAD);
            cs.setString(6, mIn.get(FROMDATE) == null ? "" : mIn.get(FROMDATE).toString());
            cs.setString(7, mIn.get(TODATE) == null ? "" : mIn.get(TODATE).toString());
            cs.setNull(8, OracleTypes.NULL);
            cs.setNull(9, OracleTypes.NULL);
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB onoff_banks_search error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                onOffBankApprovals.add(bindOnOffApprovalBank(rs));
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return onOffBankApprovals;
    }

    public static List<MspEmail> getMspEmailList(int bankId) throws Exception {
        List<MspEmail> mspEmailList = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(MSP_EMAIL_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankId);
            cs.execute();

            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("DB email list error: " + sResult);
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next())
                mspEmailList.add(bindMspEmailList(rs));
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return mspEmailList;
    }

    private static MspEmail bindMspEmailList(ResultSet rs) throws Exception {
        MspEmail mspEmailList = new MspEmail();
        mspEmailList.setEmail(Util.getColumnString(rs, "S_EMAIL"));
        mspEmailList.setLocale(Util.getColumnString(rs, "S_LOCALE"));
        return mspEmailList;
    }

    public static String getOnePartnerEmailList(int bankId) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String result = "";
        try {
            conn = getConnection114();
            cs = conn.prepareCall("{call " + Config.getOnepartnerSchema() + ".P_MERCHANT_MANAGEMENT.onoff_banks_email_list(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankId);
            cs.execute();

            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("DB get registerd email error: " + sResult);
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                if (Util.getColumnString(rs, "S_EMAIL") != null) {
                    if (result == "") {
                        result = Util.getColumnString(rs, "S_EMAIL");
                    } else {
                        result = result + "," + Util.getColumnString(rs, "S_EMAIL");
                    }
                }
                
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static JsonObject updateMspEmail(int bank_id) throws Exception {
        JsonObject result = null;
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(UPDATE_CUSTOMER_EMAIL_STATE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setInt(4, bank_id);
            cs.execute();

            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            result = new JsonObject().put(DATA, nResult).put("MESSAGE", sResult);
            if (nResult != 200)
                throw new Exception("DB UPDATE_CUSTOMER_EMAIL_STATE error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next()) {
                result.put("email", rs.getString("S_EMAIL"));
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
