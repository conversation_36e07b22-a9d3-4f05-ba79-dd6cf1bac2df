package vn.onepay.portal.resources.on_off_banks.dto;

import java.util.Date;

public class OnOffBank {
    private Integer nId;
    private String bankName;
    private String shortBankName;
    private Integer bankId;
    private String state;
    private Date lastUpdated;
    private String disableType;
    private String note;
    private String userName;
    private String action;

    public Integer getnId() {
        return nId;
    }

    public void setnId(Integer nId) {
        this.nId = nId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getShortBankName() {
        return shortBankName;
    }

    public void setShortBankName(String shortBankName) {
        this.shortBankName = shortBankName;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getDisableType() {
        return disableType;
    }

    public void setDisableType(String disableType) {
        this.disableType = disableType;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
