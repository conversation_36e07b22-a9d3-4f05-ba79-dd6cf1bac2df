package vn.onepay.portal.resources.on_off_banks.dto;

import java.util.Date;

public class OnOffBankApproval {
    private Integer nId;
    private String bankName;
    private String shortBankName;
    private String status;
    private String requestUser;
    private String approveUser;
    private Date createDate;
    private Date approveDate;
    private OnOffBank data;
    private String type;

    public Integer getnId() {
        return nId;
    }

    public void setnId(Integer nId) {
        this.nId = nId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getShortBankName() {
        return shortBankName;
    }

    public void setShortBankName(String shortBankName) {
        this.shortBankName = shortBankName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRequestUser() {
        return requestUser;
    }

    public void setRequestUser(String requestUser) {
        this.requestUser = requestUser;
    }

    public String getApproveUser() {
        return approveUser;
    }

    public void setApproveUser(String approveUser) {
        this.approveUser = approveUser;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public OnOffBank getData() {
        return data;
    }

    public void setData(OnOffBank data) {
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
