package vn.onepay.portal.resources.lead_provider.dto;

import java.sql.Timestamp;

public class ConsumerDto {

    private String id;
    private String uuid;
    private String partner;
    private String merchantName;
    private String merchantId;
    private String personalId;
    private String fullName;
    private String birthday;
    private String email;
    private String income;
    private String currentProvince;
    private String currentDistrict;
    private String currentAddress;
    private String mobileNumber;
    private String bankResCode;
    private String bankResContent;
    private String des;
    private Timestamp createdDate;
    private Timestamp updatedDate;
    private Timestamp approvedDate;
    private String bankCode;
    private String cardType;
    private String approveState;
    private String signUpState;

    public ConsumerDto() {
        
    }

    /**
     * @param id
     * @param uuid
     * @param partner
     * @param merchantName
     * @param merchantId
     * @param personalId
     * @param fullName
     * @param birthday
     * @param email
     * @param income
     * @param currentProvince
     * @param currentDistrict
     * @param currentAddress
     * @param mobileNumber
     * @param bankResCode
     * @param bankResContent
     * @param des
     * @param createdDate
     * @param updatedDate
     * @param approvedDate
     * @param bankCode
     * @param cardType
     * @param approveState
     * @param signUpState
     */
    public ConsumerDto(String id, String uuid, String partner, String merchantName, String merchantId, String personalId,
            String fullName, String birthday, String email, String income, String currentProvince,
            String currentDistrict, String currentAddress, String mobileNumber, String bankResCode,
            String bankResContent, String des, Timestamp createdDate, Timestamp updatedDate, Timestamp approvedDate,
            String bankCode, String cardType, String approveState, String signUpState) {
        this.id = id;
        this.uuid = uuid;
        this.partner = partner;
        this.merchantName = merchantName;
        this.merchantId = merchantId;
        this.personalId = personalId;
        this.fullName = fullName;
        this.birthday = birthday;
        this.email = email;
        this.income = income;
        this.currentProvince = currentProvince;
        this.currentDistrict = currentDistrict;
        this.currentAddress = currentAddress;
        this.mobileNumber = mobileNumber;
        this.bankResCode = bankResCode;
        this.bankResContent = bankResContent;
        this.des = des;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.approvedDate = approvedDate;
        this.bankCode = bankCode;
        this.cardType = cardType;
        this.approveState = approveState;
        this.signUpState = signUpState;
    }

    /**
     * @return the id
     */
    public String getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return the uuid
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * @param uuid the uuid to set
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * @return the partner
     */
    public String getPartner() {
        return partner;
    }

    /**
     * @param partner the partner to set
     */
    public void setPartner(String partner) {
        this.partner = partner;
    }

    /**
     * @return the merchantName
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * @param merchantName the merchantName to set
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * @return the merchantId
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * @param merchantId the merchantId to set
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * @return the personalId
     */
    public String getPersonalId() {
        return personalId;
    }

    /**
     * @param personalId the personalId to set
     */
    public void setPersonalId(String personalId) {
        this.personalId = personalId;
    }

    /**
     * @return the fullName
     */
    public String getFullName() {
        return fullName;
    }

    /**
     * @param fullName the fullName to set
     */
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    /**
     * @return the birthday
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * @param birthday the birthday to set
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the income
     */
    public String getIncome() {
        return income;
    }

    /**
     * @param income the income to set
     */
    public void setIncome(String income) {
        this.income = income;
    }

    /**
     * @return the currentProvince
     */
    public String getCurrentProvince() {
        return currentProvince;
    }

    /**
     * @param currentProvince the currentProvince to set
     */
    public void setCurrentProvince(String currentProvince) {
        this.currentProvince = currentProvince;
    }

    /**
     * @return the currentDistrict
     */
    public String getCurrentDistrict() {
        return currentDistrict;
    }

    /**
     * @param currentDistrict the currentDistrict to set
     */
    public void setCurrentDistrict(String currentDistrict) {
        this.currentDistrict = currentDistrict;
    }

    /**
     * @return the currentAddress
     */
    public String getCurrentAddress() {
        return currentAddress;
    }

    /**
     * @param currentAddress the currentAddress to set
     */
    public void setCurrentAddress(String currentAddress) {
        this.currentAddress = currentAddress;
    }

    /**
     * @return the mobileNumber
     */
    public String getMobileNumber() {
        return mobileNumber;
    }

    /**
     * @param mobileNumber the mobileNumber to set
     */
    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    /**
     * @return the bankResCode
     */
    public String getBankResCode() {
        return bankResCode;
    }

    /**
     * @param bankResCode the bankResCode to set
     */
    public void setBankResCode(String bankResCode) {
        this.bankResCode = bankResCode;
    }

    /**
     * @return the bankResContent
     */
    public String getBankResContent() {
        return bankResContent;
    }

    /**
     * @param bankResContent the bankResContent to set
     */
    public void setBankResContent(String bankResContent) {
        this.bankResContent = bankResContent;
    }

    /**
     * @return the des
     */
    public String getDes() {
        return des;
    }

    /**
     * @param des the des to set
     */
    public void setDes(String des) {
        this.des = des;
    }

    /**
     * @return the createdDate
     */
    public Timestamp getCreatedDate() {
        return createdDate;
    }

    /**
     * @param createdDate the createdDate to set
     */
    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * @return the updatedDate
     */
    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    /**
     * @param updatedDate the updatedDate to set
     */
    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    /**
     * @return the approvedDate
     */
    public Timestamp getApprovedDate() {
        return approvedDate;
    }

    /**
     * @param approvedDate the approvedDate to set
     */
    public void setApprovedDate(Timestamp approvedDate) {
        this.approvedDate = approvedDate;
    }

    /**
     * @return the bankCode
     */
    public String getBankCode() {
        return bankCode;
    }

    /**
     * @param bankCode the bankCode to set
     */
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * @return the cardType
     */
    public String getCardType() {
        return cardType;
    }

    /**
     * @param cardType the cardType to set
     */
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * @return the approveState
     */
    public String getApproveState() {
        return approveState;
    }

    /**
     * @param approveState the approveState to set
     */
    public void setApproveState(String approveState) {
        this.approveState = approveState;
    }

    /**
     * @return the signUpState
     */
    public String getSignUpState() {
        return signUpState;
    }

    /**
     * @param signUpState the signUpState to set
     */
    public void setSignUpState(String signUpState) {
        this.signUpState = signUpState;
    }
    
}
