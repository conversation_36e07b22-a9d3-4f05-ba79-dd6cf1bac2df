package vn.onepay.portal.resources.lead_provider.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.lead_provider.dto.ConsumerDto;
import vn.onepay.portal.resources.lead_provider.dto.DropDownDto;
import vn.onepay.portal.resources.lead_provider.dto.MerchantLinkDto;
import vn.onepay.portal.resources.lead_provider.dto.ReportConsumerDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Author: Tiennv
 * Date: 15/04/2021
 */
public class LeadProviderDao extends Db implements IConstants {
    private static final Logger logger = Logger.getLogger(LeadProviderDao.class.getName());

    private static final String GET_CONSUMER = "{call CCI.PKG_LEAD_PROVIDER.GET_CONSUMER(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String REPORT_CONSUMER = "{call CCI.PKG_LEAD_PROVIDER.REPORT_CONSUMER(?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String GET_MERCHANT_LINK = "{call CCI.PKG_LEAD_PROVIDER.GET_MERCHANT_LINK(?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String GET_DATA = "{call CCI.PKG_LEAD_PROVIDER.GET_DATA(?,?,?,?) }";
    private static final String GET_PARTNER = "{call PKG_LEAD_PROVIDER.GET_PARTNER(?,?,?) }";
    private static final String GET_MERCHANT_NAME = "{call PKG_LEAD_PROVIDER.GET_MERCHANT_NAME(?,?,?,?) }";
    private static final String GET_MERCHANT_ID = "{call PKG_LEAD_PROVIDER.GET_MERCHANT_ID(?,?,?,?,?) }";
    private static final String PUSH_MERCHANT = "{call CCI.PKG_LEAD_PROVIDER.PUSH_MERCHANT(?,?,?,?,?,?,?,?,?,?) }";
    private static final String APPROVE_CONSUMER = "{call CCI.PKG_LEAD_PROVIDER.APPROVE_CONSUMER(?,?,?,?,?) }";

    public static List<ConsumerDto> getListConsumer(Map mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<ConsumerDto> list = new ArrayList<>();

        try {
            con = getConnection112();
            cs = con.prepareCall(GET_CONSUMER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_SELECT);
            cs.setInt(5, Integer.valueOf(mIn.get(TYPE_FILTER).toString()));
            cs.setInt(6, Integer.valueOf(mIn.get(PARTNER_ID).toString()));
            cs.setInt(7, Integer.valueOf(mIn.get(MERCHANT_NAME_ID).toString()));
            cs.setString(8, mIn.get(PERSONAL_ID).toString());
            cs.setString(9, mIn.get(ID).toString());
            cs.setString(10, mIn.get(FROM_DATE).toString());
            cs.setString(11, mIn.get(TO_DATE).toString());
            cs.setString(12, mIn.get(MERCHANT_ID).toString());
            cs.setString(13, mIn.get(MOBILE_NUMBER).toString());
            cs.setString(14, mIn.get(FULL_NAME).toString());
            cs.setString(15, mIn.get(BANK_CODE).toString());
            cs.setString(16, mIn.get(CARD_TYPE).toString());
            cs.setString(17, mIn.get(STATE).toString());
            cs.setInt(18, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));
            cs.setInt(19, Integer.valueOf(mIn.get(PAGE).toString()));

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add(handleConsumer(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static int getTotalConsumer(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;

        try {
            con = getConnection112();
            cs = con.prepareCall(GET_CONSUMER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_TOTAL);
            cs.setInt(5, Integer.valueOf(mIn.get(TYPE_FILTER).toString()));
            cs.setInt(6, Integer.valueOf(mIn.get(PARTNER_ID).toString()));
            cs.setInt(7, Integer.valueOf(mIn.get(MERCHANT_NAME_ID).toString()));
            cs.setString(8, mIn.get(PERSONAL_ID).toString());
            cs.setString(9, mIn.get(ID).toString());
            cs.setString(10, mIn.get(FROM_DATE).toString());
            cs.setString(11, mIn.get(TO_DATE).toString());
            cs.setString(12, mIn.get(MERCHANT_ID).toString());
            cs.setString(13, mIn.get(MOBILE_NUMBER).toString());
            cs.setString(14, mIn.get(FULL_NAME).toString());
            cs.setString(15, mIn.get(BANK_CODE).toString());
            cs.setString(16, mIn.get(CARD_TYPE).toString());
            cs.setString(17, mIn.get(STATE).toString());
            cs.setInt(18, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));
            cs.setInt(19, Integer.valueOf(mIn.get(PAGE).toString()));

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt(N_TOTAL);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static List<ReportConsumerDto> getListReportConsumer(Map mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<ReportConsumerDto> list = new ArrayList<>();

        try {
            con = getConnection112();
            cs = con.prepareCall(REPORT_CONSUMER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_SELECT);
            cs.setInt(5, Integer.valueOf(mIn.get(TYPE_FILTER).toString()));
            cs.setString(6, mIn.get(FROM_DATE).toString());
            cs.setString(7, mIn.get(TO_DATE).toString());
            cs.setInt(8, Integer.valueOf(mIn.get(PARTNER_ID).toString()));
            cs.setInt(9, Integer.valueOf(mIn.get(MERCHANT_NAME_ID).toString()));
            cs.setString(10, mIn.get(MERCHANT_ID).toString());
            cs.setString(11, mIn.get(BANK_CODE).toString());
            cs.setString(12, mIn.get(CARD_TYPE).toString());
            cs.setString(13, mIn.get(STATE).toString());
            cs.setString(14, mIn.get(TIME_INTERVAL).toString());

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add(handleReportConsumer(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static int getTotalReportConsumer(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;

        try {
            con = getConnection112();
            cs = con.prepareCall(REPORT_CONSUMER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_TOTAL);
            cs.setInt(5, Integer.valueOf(mIn.get(TYPE_FILTER).toString()));
            cs.setString(6, mIn.get(FROM_DATE).toString());
            cs.setString(7, mIn.get(TO_DATE).toString());
            cs.setInt(8, Integer.valueOf(mIn.get(PARTNER_ID).toString()));
            cs.setInt(9, Integer.valueOf(mIn.get(MERCHANT_NAME_ID).toString()));
            cs.setString(10, mIn.get(MERCHANT_ID).toString());
            cs.setString(11, mIn.get(BANK_CODE).toString());
            cs.setString(12, mIn.get(CARD_TYPE).toString());
            cs.setString(13, mIn.get(STATE).toString());
            cs.setString(14, mIn.get(TIME_INTERVAL).toString());

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt(N_TOTAL);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static List<MerchantLinkDto> getListMerchantLink(Map mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<MerchantLinkDto> list = new ArrayList<>();

        try {
            con = getConnection112();
            cs = con.prepareCall(GET_MERCHANT_LINK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_SELECT);
            cs.setString(5, mIn.get(FROM_DATE).toString());
            cs.setString(6, mIn.get(TO_DATE).toString());
            cs.setString(7, mIn.get(MERCHANT_ID).toString());
            cs.setString(8, mIn.get(PARTNER_ID).toString());
            cs.setString(9, mIn.get(MERCHANT_NAME_ID).toString());
            cs.setInt(10, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));
            cs.setInt(11, Integer.valueOf(mIn.get(PAGE).toString()));

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add(handleMerchantLink(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static int getTotalMerchantLink(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int total = 0;

        try {
            con = getConnection112();
            cs = con.prepareCall(GET_MERCHANT_LINK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QUERY_TOTAL);
            cs.setString(5, mIn.get(FROM_DATE).toString());
            cs.setString(6, mIn.get(TO_DATE).toString());
            cs.setString(7, mIn.get(MERCHANT_ID).toString());
            cs.setString(8, mIn.get(PARTNER_ID).toString());
            cs.setString(9, mIn.get(MERCHANT_NAME_ID).toString());
            cs.setInt(10, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));
            cs.setInt(11, Integer.valueOf(mIn.get(PAGE).toString()));

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt(N_TOTAL);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static List<DropDownDto> getListPartner() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<DropDownDto> list = new ArrayList<>();

        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                logger.info("ERROR getListPartner: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add( new DropDownDto(rs.getInt(MY_ID), rs.getString(MY_NAME)));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<DropDownDto> getListMerchantName(int partnerId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<DropDownDto> list = new ArrayList<>();

        try {
            con = getConnection114();
            cs = con.prepareCall(GET_MERCHANT_NAME);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                logger.info("ERROR getListMerchantName " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add( new DropDownDto(rs.getInt(MY_ID), rs.getString(MY_NAME)));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<String> getListMerchantId(int partnerId, int merchantNameId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();

        try {
            con = getConnection114();
            cs = con.prepareCall(GET_MERCHANT_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.setInt(5, merchantNameId);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                logger.info("ERROR " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add(rs.getString(MY_NAME));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static List<String> getListData(String type) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();

        try {
            con = getConnection112();
            cs = con.prepareCall(GET_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String sResult = cs.getString(3);
            int nResult = cs.getInt(2);
            if (nResult != 200) {
                logger.info("ERROR " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    list.add(rs.getString("S_DATA"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static Integer pushMerchant(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        int nResult = 500;
        try {
            con = getConnection112();
            cs = con.prepareCall(PUSH_MERCHANT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.get("merchantId").toString());
            cs.setString(4, mIn.get("uId").toString());
            cs.setInt(5, Integer.valueOf(mIn.get("partnerId").toString()));
            cs.setInt(6, Integer.valueOf(mIn.get("merchantNameId").toString()));
            cs.setString(7, mIn.get("partnerName").toString());
            cs.setString(8, mIn.get("merchantName").toString());
            cs.setString(9, mIn.get("link").toString());
            cs.setString(10, mIn.get("user").toString());

            cs.execute();
            String sResult = cs.getString(2);
            logger.info("pushMerchant Result " + sResult);
            nResult = cs.getInt(1);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

    public static Integer approveConsumer(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        int nResult = 500;
        try {
            con = getConnection112();
            cs = con.prepareCall(APPROVE_CONSUMER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.get(ID).toString());
            cs.setString(4, mIn.get("approveState").toString());
            cs.setString(5, mIn.get("approveDate").toString());

            cs.execute();
            String sResult = cs.getString(2);
            logger.info("approveConsumer Result " + sResult);
            nResult = cs.getInt(1);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

    public static MerchantLinkDto handleMerchantLink(ResultSet rs) throws SQLException {  
        MerchantLinkDto merchantLinkDto = new MerchantLinkDto();
        merchantLinkDto.setPartnerId(rs.getInt("n_partner_id"));
        merchantLinkDto.setMerchantNameId(rs.getInt("n_merchant_name_id"));
        merchantLinkDto.setPartnerName(rs.getString(S_PARTNER));
        merchantLinkDto.setMerchantName(rs.getString(S_MERCHANT_NAME));
        merchantLinkDto.setMerchantId(rs.getString("S_ID"));
        merchantLinkDto.setuId(rs.getString("S_UID"));
        merchantLinkDto.setLink(rs.getString("S_LINK"));
        merchantLinkDto.setCreatedDate(rs.getTimestamp(D_CREATED));
        merchantLinkDto.setUser(rs.getString("S_USER"));
        
        return merchantLinkDto;
    }

    public static ConsumerDto handleConsumer(ResultSet rs) throws SQLException {
        ConsumerDto consumerDto = new ConsumerDto();
        consumerDto.setId(rs.getString("S_ID"));
        consumerDto.setUuid(rs.getString("S_UUID"));
        consumerDto.setPartner(rs.getString(S_PARTNER));
        consumerDto.setMerchantName(rs.getString(S_MERCHANT_NAME));
        consumerDto.setMerchantId(rs.getString("S_MERCHANT_ID"));
        consumerDto.setPersonalId(rs.getString("S_PERSONAL_ID"));
        consumerDto.setFullName(rs.getString("S_FULLNAME"));
        consumerDto.setBirthday(rs.getString("S_BIRTHDAY"));
        consumerDto.setEmail(rs.getString("S_EMAIL"));
        consumerDto.setCurrentProvince(rs.getString("S_CURRENT_PROVINCE"));
        consumerDto.setCurrentDistrict(rs.getString("S_CURRENT_DISTRICT"));
        consumerDto.setCurrentAddress(rs.getString("S_CURRENT_ADDRESS"));
        consumerDto.setMobileNumber(rs.getString("S_MOBILE_NUMBER"));
        consumerDto.setBankResCode(rs.getString("S_BANK_RES_CODE"));
        consumerDto.setBankResContent(rs.getString("S_BANK_RES_CONTENT"));
        consumerDto.setDes(rs.getString("S_DESC"));
        consumerDto.setCreatedDate(rs.getString(D_CREATED) == null ? null : java.sql.Timestamp.valueOf(rs.getString(D_CREATED)));
        consumerDto.setUpdatedDate(rs.getString("D_UPDATED") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_UPDATED")));
        consumerDto.setApprovedDate(rs.getString("D_APPROVED") == null ? null : java.sql.Timestamp.valueOf(rs.getString("D_APPROVED")));
        consumerDto.setBankCode(rs.getString("S_BANK_CODE"));
        consumerDto.setCardType(rs.getString("S_CARD_TYPE"));
        consumerDto.setApproveState(rs.getString("S_APPROVE_STATE"));
        consumerDto.setSignUpState(rs.getString("S_SIGN_UP_STATE"));
        
        return consumerDto;
    }

    public static ReportConsumerDto handleReportConsumer(ResultSet rs) throws SQLException {
        ReportConsumerDto reportConsumerDto = new ReportConsumerDto();
        reportConsumerDto.settDate(rs.getString("T_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("T_DATE")));
        reportConsumerDto.settBank(rs.getString("T_BANK"));
        reportConsumerDto.settPending(rs.getInt("T_PENDING"));
        reportConsumerDto.settRejected(rs.getInt("T_REJECTED"));
        reportConsumerDto.settApproved(rs.getInt("T_APPROVED"));
        
        return reportConsumerDto;
    }
    
}
