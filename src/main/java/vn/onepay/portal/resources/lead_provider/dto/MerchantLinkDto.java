package vn.onepay.portal.resources.lead_provider.dto;

import java.sql.Timestamp;

public class MerchantLinkDto {

    private String merchantId;
    private int partnerId;
    private int merchantNameId;
    private String partnerName;
    private String merchantName;
    private String uId;
    private String link;
    private Timestamp createdDate;
    private String user;

    public MerchantLinkDto(){

    }

    /**
     * @param merchantId
     * @param partnerId
     * @param merchantNameId
     * @param partnerName
     * @param merchantName
     * @param uId
     * @param link
     * @param createdDate
     * @param user
     */
    public MerchantLinkDto(String merchantId, int partnerId, int merchantNameId, String partnerName, String merchantName, String uId, String link,
        Timestamp createdDate, String user) {
        this.merchantId = merchantId;
        this.partnerId = partnerId;
        this.merchantNameId = merchantNameId;
        this.partnerName = partnerName;
        this.merchantName = merchantName;
        this.uId = uId;
        this.link = link;
        this.createdDate = createdDate;
        this.user = user;
    }

    /**
     * @return the merchantId
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * @param merchantId the merchantId to set
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * @return the partnerId
     */
    public int getPartnerId() {
        return partnerId;
    }

    /**
     * @param partnerId the partnerId to set
     */
    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }

    /**
     * @return the merchantNameId
     */
    public int getMerchantNameId() {
        return merchantNameId;
    }

    /**
     * @param merchantNameId the merchantNameId to set
     */
    public void setMerchantNameId(int merchantNameId) {
        this.merchantNameId = merchantNameId;
    }

    /**
     * @return the link
     */
    public String getLink() {
        return link;
    }

    /**
     * @param link the link to set
     */
    public void setLink(String link) {
        this.link = link;
    }

    /**
     * @return the createdDate
     */
    public Timestamp getCreatedDate() {
        return createdDate;
    }

    /**
     * @param createdDate the createdDate to set
     */
    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * @return the user
     */
    public String getUser() {
        return user;
    }

    /**
     * @param user the user to set
     */
    public void setUser(String user) {
        this.user = user;
    }

    /**
     * @return the uId
     */
    public String getuId() {
        return uId;
    }

    /**
     * @param uId the uId to set
     */
    public void setuId(String uId) {
        this.uId = uId;
    }

    /**
     * @return the partnerName
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     * @param partnerName the partnerName to set
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * @return the merchantName
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * @param merchantName the merchantName to set
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    
}
