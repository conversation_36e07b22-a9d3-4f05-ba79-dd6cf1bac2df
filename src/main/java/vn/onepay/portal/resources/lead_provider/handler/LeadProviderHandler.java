package vn.onepay.portal.resources.lead_provider.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.lead_provider.dao.LeadProviderDao;
import vn.onepay.portal.resources.lead_provider.dto.ConsumerDto;
import vn.onepay.portal.resources.lead_provider.dto.DropDownDto;
import vn.onepay.portal.resources.lead_provider.dto.MerchantLinkDto;
import vn.onepay.portal.resources.lead_provider.dto.ReportConsumerDto;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.apache.commons.lang3.time.DateUtils;

import static vn.onepay.portal.Util.sendResponse;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/*
 * Author: Tiennv
 * Date: 15/04/2021
 */
public class LeadProviderHandler implements IConstants {

    private LeadProviderHandler() {

    }
    private static Logger logger = Logger.getLogger(LeadProviderHandler.class.getName());

    public static void getConsumerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, Util.handleHttpRequest(request, FROM_DATE, "").toString());
                mIn.put(TO_DATE, Util.handleHttpRequest(request, TO_DATE, "").toString());
                mIn.put(ID, Util.handleHttpRequest(request, ID, "").toString());
                mIn.put(TYPE_FILTER, Integer.parseInt(Util.handleHttpRequest(request, TYPE_FILTER, "0").toString()));
                mIn.put(PARTNER_ID, Util.handleHttpRequest(request, PARTNER_ID, "-1").toString());
                mIn.put(MERCHANT_NAME_ID, Util.handleHttpRequest(request, MERCHANT_NAME_ID, "-1").toString());
                mIn.put(MERCHANT_ID, Util.handleHttpRequest(request, MERCHANT_ID, "").toString());
                mIn.put(PERSONAL_ID, Util.handleHttpRequest(request, PERSONAL_ID, "").toString());
                mIn.put(MOBILE_NUMBER, Util.handleHttpRequest(request, MOBILE_NUMBER, "").toString());
                mIn.put(FULL_NAME, Util.handleHttpRequest(request, FULL_NAME, "").toString());
                mIn.put(BANK_CODE, Util.handleHttpRequest(request, BANK_CODE, "").toString());
                mIn.put(CARD_TYPE, Util.handleHttpRequest(request, CARD_TYPE, "").toString());
                mIn.put(STATE, Util.handleHttpRequest(request, STATE, "").toString());
                mIn.put(PAGE, Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer.parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "100").toString()));
                
                List<ConsumerDto> list = LeadProviderDao.getListConsumer(mIn);
                int total = LeadProviderDao.getTotalConsumer(mIn);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(TOTAL, String.valueOf(total));
                responseData.put(DATA, list);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE Consumer DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getConsumer ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getReportConsumerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, Util.handleHttpRequest(request, FROM_DATE, "").toString());
                mIn.put(TO_DATE, Util.handleHttpRequest(request, TO_DATE, "").toString());
                mIn.put(TYPE_FILTER, Util.handleHttpRequest(request, TYPE_FILTER, "0").toString());
                mIn.put(PARTNER_ID, Util.handleHttpRequest(request, PARTNER_ID, "-1").toString());
                mIn.put(MERCHANT_NAME_ID, Util.handleHttpRequest(request, MERCHANT_NAME_ID, "-1").toString());
                mIn.put(MERCHANT_ID, Util.handleHttpRequest(request, MERCHANT_ID, "").toString());
                mIn.put(BANK_CODE, Util.handleHttpRequest(request, BANK_CODE, "").toString());
                mIn.put(CARD_TYPE, Util.handleHttpRequest(request, CARD_TYPE, "").toString());
                mIn.put(STATE, Util.handleHttpRequest(request, STATE, "").toString());
                mIn.put(TIME_INTERVAL, Util.handleHttpRequest(request, TIME_INTERVAL, "DD").toString());
                
                List<ReportConsumerDto> list = LeadProviderDao.getListReportConsumer(mIn);
                int total = LeadProviderDao.getTotalReportConsumer(mIn);
                int pendingTotal = 0;
                int rejectedTotal = 0;
                int approvedTotal = 0;
                for(ReportConsumerDto element: list) {
                    pendingTotal = element.gettPending() + pendingTotal;
                    rejectedTotal = element.gettRejected() + rejectedTotal;
                    approvedTotal = element.gettApproved() + approvedTotal;
                }
                responseData.put(STATE, SUCCESS);
                responseData.put(TOTAL, String.valueOf(total));
                responseData.put("pendingTotal", String.valueOf(pendingTotal));
                responseData.put("rejectedTotal", String.valueOf(rejectedTotal));
                responseData.put("approvedTotal", String.valueOf(approvedTotal));
                responseData.put(DATA, list);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE REPORT Consumer DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getReportConsumerList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put("pendingTotal", "0");
                responseData.put("rejectedTotal", "0");
                responseData.put("approvedTotal", "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantLinkList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, Util.handleHttpRequest(request, FROM_DATE, "").toString());
                mIn.put(TO_DATE, Util.handleHttpRequest(request, TO_DATE, "").toString());
                mIn.put(PARTNER_ID, Util.handleHttpRequest(request, PARTNER_ID, "-1").toString());
                mIn.put(MERCHANT_NAME_ID, Util.handleHttpRequest(request, MERCHANT_NAME_ID, "-1").toString());
                mIn.put(MERCHANT_ID, Util.handleHttpRequest(request, MERCHANT_ID, "").toString());
                mIn.put(PAGE, Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer.parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "100").toString()));
                
                List<MerchantLinkDto> list = LeadProviderDao.getListMerchantLink(mIn);
                int total = LeadProviderDao.getTotalMerchantLink(mIn);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(TOTAL, String.valueOf(total));
                responseData.put(DATA, list);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE Merchant Link DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantLinkList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartnerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {                    
                List<DropDownDto> res = LeadProviderDao.getListPartner();
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, res);
                response.put(RESPONSE, responseData);
                // logger.info("RESPONSE Partner DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getPartnerList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantNameList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                int partnerId = Integer.parseInt(request.getParam(PARTNER_ID));                      
                List<DropDownDto> res = LeadProviderDao.getListMerchantName(partnerId);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, res);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE MerchantName DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantNameList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantIdList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                int partnerId = Integer.parseInt(request.getParam("partner_id"));
                int merchantNameId = Integer.parseInt(request.getParam("merchant_name_id"));                      
                List<String> list = LeadProviderDao.getListMerchantId(partnerId, merchantNameId);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, list);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE MerchantId DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantIdList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDataList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                String typeData = request.getParam("type_data");                      
                List<String> list = LeadProviderDao.getListData(typeData);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, list);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE DATA: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getDataList ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void pushMerchantLink(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            int statusCode = 200;
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                String partnerId = bodyJson.getString(PARTNER_ID);
                String merchantNameId = bodyJson.getString(MERCHANT_NAME_ID);
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String partnerName = bodyJson.getString(PARTNER_NAME);
                String merchantName = bodyJson.getString(MERCHANT_NAME);
                String uId = bodyJson.getString("u_id");
                String link = bodyJson.getString("link");
                String user = bodyJson.getString("user");
                if (partnerId == null || merchantNameId == null || partnerName == null || merchantName == null || merchantId == null 
                 || uId == null || link == null || user == null
                 || partnerId.isEmpty() || partnerName.isEmpty() || merchantName.isEmpty() || merchantNameId.isEmpty() || merchantId.isEmpty()
                 || uId.isEmpty() || link.isEmpty() || user.isEmpty()) {
                    statusCode = 400;                                   
                } else {
                    mIn.put("partnerId", partnerId);
                    mIn.put("merchantNameId", merchantNameId);
                    mIn.put("merchantId", merchantId);
                    mIn.put("partnerName", partnerName);
                    mIn.put("merchantName", merchantName);
                    mIn.put("uId", uId);
                    mIn.put("link", link);
                    mIn.put("user", user);                      
                    statusCode = LeadProviderDao.pushMerchant(mIn);
                    if (statusCode == 500) statusCode = 401;                                      
                }
                logger.info("RESPONSE PUSH MERCHANT: " + statusCode);
                if (statusCode == 200) responseData.put(STATE, SUCCESS);
                else responseData.put(STATE, FAILED);
                responseData.put(DATA, statusCode);
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                statusCode = 500;
                logger.log(Level.SEVERE, "[ pushMerchantLink ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, statusCode);
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 200, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveConsumer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            int statusCode = 200;
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                String id = bodyJson.getString(ID);
                String approveState = bodyJson.getString("approve_state");
                String approveDate = bodyJson.getString("approve_date");
                if (id == null || approveState == null || approveDate == null
                 || id.isEmpty() || approveState.isEmpty() || approveDate.isEmpty()) {
                    statusCode = 400;                                   
                } else {
                    mIn.put(ID, id);
                    mIn.put("approveState", approveState);
                    mIn.put("approveDate", approveDate);                      
                    statusCode = LeadProviderDao.approveConsumer(mIn);                                    
                }
                logger.info("STATUS APPROVE CONSUMER: " + statusCode);
                if (statusCode == 200) responseData.put(STATE, SUCCESS);
                else responseData.put(STATE, FAILED);
                responseData.put(DATA, statusCode);
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                statusCode = 500;
                logger.log(Level.SEVERE, "[ approveConsumer ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, statusCode);
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 200, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadConsumer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, Util.handleJsonObject(bodyJson, FROM_DATE, ""));
                mIn.put(TO_DATE, Util.handleJsonObject(bodyJson, TO_DATE, ""));
                mIn.put(ID,  Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(TYPE_FILTER, Util.handleJsonObject(bodyJson, ID, "0"));
                mIn.put(PARTNER_ID, Util.handleJsonObject(bodyJson, ID, "-1"));
                mIn.put(MERCHANT_NAME_ID, Util.handleJsonObject(bodyJson, ID, "-1"));
                mIn.put(MERCHANT_ID, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(PERSONAL_ID, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(MOBILE_NUMBER, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(FULL_NAME, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(BANK_CODE, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(CARD_TYPE, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(STATE, Util.handleJsonObject(bodyJson, STATE, ""));
                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                HttpServerRequest request = ctx.request();
                requestData.put(X_USER_ID, Util.handleHeaderHttpRequest(request, X_USER_ID, ""));
                requestData.put(X_REQUEST_ID, Util.handleHeaderHttpRequest(request, X_REQUEST_ID, ""));
                requestData.put(X_REAL_IP, Util.handleHeaderHttpRequest(request, X_REAL_IP, ""));

                int totalRows = LeadProviderDao.getTotalConsumer(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = dateFormat.format(new Date());
                String fileName = "LeadProvider_Consumer_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("lead_provider_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                }else{
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "CONSUMER DOWNLOAD ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(FROM_DATE, Util.handleJsonObject(bodyJson, FROM_DATE, ""));
                mIn.put(TO_DATE, Util.handleJsonObject(bodyJson, TO_DATE, ""));
                mIn.put(TYPE_FILTER, Util.handleJsonObject(bodyJson, ID, "0"));
                mIn.put(PARTNER_ID, Util.handleJsonObject(bodyJson, ID, "-1"));
                mIn.put(MERCHANT_NAME_ID, Util.handleJsonObject(bodyJson, ID, "-1"));
                mIn.put(MERCHANT_ID, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(BANK_CODE, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(CARD_TYPE, Util.handleJsonObject(bodyJson, ID, ""));
                mIn.put(STATE, Util.handleJsonObject(bodyJson, STATE, ""));
                mIn.put(TIME_INTERVAL, Util.handleJsonObject(bodyJson, TIME_INTERVAL, "DD"));

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                HttpServerRequest request = ctx.request();
                requestData.put(X_USER_ID, Util.handleHeaderHttpRequest(request, X_USER_ID, ""));
                requestData.put(X_REQUEST_ID, Util.handleHeaderHttpRequest(request, X_REQUEST_ID, ""));
                requestData.put(X_REAL_IP, Util.handleHeaderHttpRequest(request, X_REAL_IP, ""));

                int totalRows = LeadProviderDao.getTotalReportConsumer(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = dateFormat.format(new Date());
                String fileName = "LeadProvider_Report_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("lead_provider_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                }else{
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "REPORT DOWNLOAD ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}
