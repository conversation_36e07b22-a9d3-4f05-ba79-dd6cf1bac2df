package vn.onepay.portal.resources.lead_provider.dto;

import java.sql.Timestamp;

public class ReportConsumerDto {

    private Timestamp tDate;
    private String tBank;
    private int tPending;
    private int tRejected;
    private int tApproved;

    public ReportConsumerDto() {

    }

    /**
     * @param tDate
     * @param tBank
     * @param tPending
     * @param tRejected
     * @param tApproved
     */
    public ReportConsumerDto(Timestamp tDate, String tBank, int tPending, int tRejected, int tApproved) {
        this.tDate = tDate;
        this.tBank = tBank;
        this.tPending = tPending;
        this.tRejected = tRejected;
        this.tApproved = tApproved;
    }

    /**
     * @return the tDate
     */
    public Timestamp gettDate() {
        return tDate;
    }

    /**
     * @param tDate the tDate to set
     */
    public void settDate(Timestamp tDate) {
        this.tDate = tDate;
    }

    /**
     * @return the tBank
     */
    public String gettBank() {
        return tBank;
    }

    /**
     * @param tBank the tBank to set
     */
    public void settBank(String tBank) {
        this.tBank = tBank;
    }

    /**
     * @return the tPending
     */
    public int gettPending() {
        return tPending;
    }

    /**
     * @param tPending the tPending to set
     */
    public void settPending(int tPending) {
        this.tPending = tPending;
    }

    /**
     * @return the tRejected
     */
    public int gettRejected() {
        return tRejected;
    }

    /**
     * @param tRejected the tRejected to set
     */
    public void settRejected(int tRejected) {
        this.tRejected = tRejected;
    }

    /**
     * @return the tApproved
     */
    public int gettApproved() {
        return tApproved;
    }

    /**
     * @param tApproved the tApproved to set
     */
    public void settApproved(int tApproved) {
        this.tApproved = tApproved;
    }

    
    
}
