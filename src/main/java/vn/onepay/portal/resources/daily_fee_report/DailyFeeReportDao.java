package vn.onepay.portal.resources.daily_fee_report;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;

public class DailyFeeReportDao extends Db {
    private static final String DAILY_FEE_REPORT_SEARCH = "{ call ONEFIN.PKG_DAILY_FEE_REPORT.SEARCH_DAILY_ADV_PAYMENTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_TOTAL_ROW_DAILY_FEE_REPORT = "{ call ONEFIN.PKG_DAILY_FEE_REPORT.GET_TOTAL_ROW_DAILY_FEE_REPORT(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(DailyFeeReportDao.class.getName());

    public static Map<String, Object> list(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<DailyFeeReportDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        Integer nTotal = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(DAILY_FEE_REPORT_SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, (String) mIn.get(MFR_PARTNER_IDS));
            cs.setString(6, (String) mIn.get(MFR_MERCHANT_IDS));
            cs.setString(7, (String) mIn.get(MFR_TAX_CODE));
            cs.setString(8, (String) mIn.get(MFR_FROM_DATE));
            cs.setString(9, (String) mIn.get(MFR_TO_DATE));
            cs.setInt(10, mIn.get(MFR_PAGE_ACTIVE) == null ? 0 : (Integer) mIn.get(MFR_PAGE_ACTIVE));
            cs.setInt(11, mIn.get(MFR_PAGE_SIZE) == null ? Integer.MAX_VALUE : (Integer) mIn.get(MFR_PAGE_SIZE));
            cs.setString(12, (String) mIn.get(MFR_TYPE_ADVANCES));
            cs.setString(13, (String) mIn.get(MFR_TIME_ADVANCES));
            cs.setString(14, (String) mIn.get(MFR_PAY_CHANNELS));
            cs.setString(15, (String) mIn.get(MFR_RECEIPT_TYPES));
            cs.setString(16, (String) mIn.get(MFR_CONTROL_MINUTES));
            cs.setInt(17, (Integer) mIn.get(MFR_TIME_INTERVAL));
            cs.execute();

            sResult = cs.getString(4);
            nResult = cs.getInt(3);
            nTotal = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nResult != 1) {
                throw new Exception("DB PKG_DAILY_FEE_REPORT.SEARCH_DAILY_ADV_PAYMENTS: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    DailyFeeReportDto mfrd = new DailyFeeReportDto();
                    mfrd.setIndex(rs.getInt("RNUM"));
                    mfrd.setReportId(rs.getString("REPORT_ID"));
                    mfrd.setPartnerName(rs.getString("PARTNER_NAME"));
                    mfrd.setBusinessName(rs.getString("BUSINESS_NAME"));
                    mfrd.setPartnerId(rs.getInt("PARTNER_ID"));
                    mfrd.setMerchantIds(rs.getString("MERCHANT_IDS"));
                    mfrd.setContractCode(rs.getString("CONTRACT_CODE"));
                    mfrd.setContractDateTime(rs.getDate("CONTRACT_DATE") == null ? 0 : rs.getDate("CONTRACT_DATE").getTime());
                    mfrd.setContractId(rs.getInt("CONTRACT_ID"));
                    mfrd.setAddendum(rs.getString("ADDENDUM"));
                    mfrd.setControlMinutes(rs.getString("CONTROL_MINUTES"));
                    mfrd.setMerchantName(rs.getString("MERCHANT_NAME"));
                    mfrd.setDateReport(rs.getTimestamp("D_REPORT"));

                    // JsonObject jAdvance = new JsonObject(rs.getString("J_ADVANCE") == null ? "{}" : rs.getString("J_ADVANCE"));
                    // String countSuccess = filterStringNullPointer(jAdvance.getValue("total_txn_success"));
                    mfrd.setCountSuccess(filterStringNullPointer(rs.getString("TOTAL_TXN_SUCCESS")));
                    // String countFailed = filterStringNullPointer(jAdvance.getValue("total_txn_failed"));
                    mfrd.setCountFailed(filterStringNullPointer(rs.getString("TOTAL_TXN_FAILED")));
                    // String originAmountUsd = filterStringNullPointer(jAdvance.getValue("total_usd"));
                    mfrd.setOriginAmountUsd(filterStringNullPointer(rs.getString("TOTAL_USD")));
                    // String originAmountVnd = filterStringNullPointer(jAdvance.getValue("total_vnd"));
                    mfrd.setOriginAmountVnd(filterStringNullPointer(rs.getString("TOTAL_VND")));
                    // String partnerDiscountAmount = filterStringNullPointer(jAdvance.getValue("total_partner_discount_amount"));
                    mfrd.setPartnerDiscountAmount(filterStringNullPointer(rs.getString("TOTAL_PARTNER_DISCOUNT_AMOUNT")));
                    // String merchantDiscountAmount = filterStringNullPointer(jAdvance.getValue("total_merchant_discount_amount"));
                    mfrd.setMerchantDiscountAmount(filterStringNullPointer(rs.getString("TOTAL_MERCHANT_DISCOUNT_AMOUNT")));
                    // String amountTotalVND = filterStringNullPointer(jAdvance.getValue("total_change_to_vnd"));
                    mfrd.setAmountTotalVnd(filterStringNullPointer(rs.getString("TOTAL_CHANGE_TO_VND")));
                    // String feeSuccess = filterStringNullPointer(jAdvance.getValue("total_fee_txn_success"));
                    mfrd.setFeeSuccess(filterStringNullPointer(rs.getString("TOTAL_FEE_TXN_SUCCESS")));
                    // String feeFailed = filterStringNullPointer(jAdvance.getValue("total_fee_txn_failed"));
                    mfrd.setFeeFailed(filterStringNullPointer(rs.getString("TOTAL_FEE_TXN_FAILED")));
                    // String feeIta = filterStringNullPointer(jAdvance.getValue("total_fee_ita"));
                    mfrd.setFeeIta(filterStringNullPointer(rs.getString("TOTAL_FEE_ITA")));
                    // String feeEcom = filterStringNullPointer(jAdvance.getValue("total_fee_ecom"));
                    mfrd.setFeeEcom(filterStringNullPointer(rs.getString("TOTAL_FEE_ECOM")));
                    // String totalFee = filterStringNullPointer(jAdvance.getValue("total_fee_inc_vat"));
                    mfrd.setTotalFee(filterStringNullPointer(rs.getString("TOTAL_FEE_INC_VAT")));
                    // String totalVAT = filterStringNullPointer(jAdvance.getValue("total_vat"));
                    mfrd.setTotalVAT(filterStringNullPointer(rs.getString("TOTAL_VAT")));
                    // String advanceAmount = filterStringNullPointer(jAdvance.getValue("total_advance"));
                    mfrd.setAdvanceAmount(filterStringNullPointer(rs.getString("TOTAL_ADVANCE")));
                    // TODO: sua lai
                    mfrd.setDiscountFee("0");
                    mfrd.setTotalFeeCollected("0");
                    mfrd.setTotalFeeReceivable("0");

                    SimpleDateFormat sdfHHMMSSMMDDYYYY = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy");

                    JsonObject jFrom = new JsonObject(rs.getString("J_FROM") == null ? "{}" : rs.getString("J_FROM"));
                    JsonObject jTo = new JsonObject(rs.getString("J_TO") == null ? "{}" : rs.getString("J_TO"));

                    // QT
                    java.util.Date fromDateQT = null;
                    java.util.Date toDateQT = null;
                    // ND
                    java.util.Date fromDateND = null;
                    java.util.Date toDateND = null;
                    // QR
                    java.util.Date fromDateQR = null;
                    java.util.Date toDateQR = null;
                    // SMS
                    java.util.Date fromDateSMS = null;
                    java.util.Date toDateSMS = null;
                    // BL
                    java.util.Date fromDateBL = null;
                    java.util.Date toDateBL = null;
                    // BNPL
                    java.util.Date fromDateBNPL = null;
                    java.util.Date toDateBNPL = null;

                    if (!jFrom.equals("{}")) {
                        if (jFrom.containsKey("qt")) {
                            fromDateQT = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qt"));
                        }
                        if (jFrom.containsKey("nd")) {
                            fromDateND = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("nd"));
                        }
                        if (jFrom.containsKey("qr")) {
                            fromDateQR = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qr"));
                        }
                        if (jFrom.containsKey("sms")) {
                            fromDateSMS = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("sms"));
                        }
                        if (jFrom.containsKey("bl")) {
                            fromDateBL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bl"));
                        }
                        if (jFrom.containsKey("bnpl")) {
                            fromDateBNPL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bnpl"));
                        }
                    }
                    if (!jTo.equals("{}")) {
                        if (jTo.containsKey("qt")) {
                            toDateQT = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qt"));
                        }
                        if (jTo.containsKey("nd")) {
                            toDateND = sdfHHMMSSMMDDYYYY.parse(jTo.getString("nd"));
                        }
                        if (jTo.containsKey("qr")) {
                            toDateQR = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qr"));
                        }
                        if (jTo.containsKey("sms")) {
                            toDateSMS = sdfHHMMSSMMDDYYYY.parse(jTo.getString("sms"));
                        }
                        if (jTo.containsKey("bl")) {
                            toDateBL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bl"));
                        }
                        if (jTo.containsKey("bnpl")) {
                            toDateBNPL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bnpl"));
                        }
                    }
                    mfrd.setFromDateQT(fromDateQT);
                    mfrd.setFromDateQTTime(fromDateQT == null ? 0 : fromDateQT.getTime());
                    mfrd.setFromDateND(fromDateND);
                    mfrd.setFromDateNDTime(fromDateND == null ? 0 : fromDateND.getTime());
                    mfrd.setFromDateQR(fromDateQR);
                    mfrd.setFromDateQRTime(fromDateQR == null ? 0 : fromDateQR.getTime());
                    mfrd.setFromDateSMS(fromDateSMS);
                    mfrd.setFromDateSMSTime(fromDateSMS == null ? 0 : fromDateSMS.getTime());
                    mfrd.setFromDateBL(fromDateBL);
                    mfrd.setFromDateBLTime(fromDateBL == null ? 0 : fromDateBL.getTime());
                    mfrd.setFromDateBNPL(fromDateBNPL);
                    mfrd.setFromDateBNPLTime(fromDateBNPL == null ? 0 : fromDateBNPL.getTime());

                    mfrd.setToDateQT(toDateQT);
                    mfrd.setToDateQTTime(toDateQT == null ? 0 : toDateQT.getTime());
                    mfrd.setToDateND(toDateND);
                    mfrd.setToDateNDTime(toDateND == null ? 0 : toDateND.getTime());
                    mfrd.setToDateQR(toDateQR);
                    mfrd.setToDateQRTime(toDateQR == null ? 0 : toDateQR.getTime());
                    mfrd.setToDateSMS(toDateSMS);
                    mfrd.setToDateSMSTime(toDateSMS == null ? 0 : toDateSMS.getTime());
                    mfrd.setToDateBL(toDateBL);
                    mfrd.setToDateBLTime(toDateBL == null ? 0 : toDateBL.getTime());
                    mfrd.setToDateBNPL(toDateBNPL);
                    mfrd.setToDateBNPLTime(toDateBNPL == null ? 0 : toDateBNPL.getTime());

                    mfrd.setPayChannels(rs.getString("PAY_CHANNELS"));
                    mfrd.setTimeAdvance(rs.getString("TIME_ADVANCE"));
                    mfrd.setTypeAdvance(rs.getString("TYPE_ADVANCE"));
                    mfrd.setReceiptType(rs.getString("RECEIPT_TYPE"));
                    // mfrd.setReceiptState(rs.getString("RECEIPT_STATE"));
                    mfrd.setIdConfig(rs.getInt("ID_CONFIG"));
                    mfrd.setTaxCode(rs.getString("TAX_CODE"));
                    mfrd.setAccountNumbers(rs.getString("ACCOUNT_NUMBERS"));
                    // mfrd.setNumberEmailSent(rs.getLong("NUMBER_EMAIL_SENT"));
                    result.add(mfrd);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("data", result);
        dataList.put("total", nTotal);
        return dataList;
    }

    public static Integer getTotalRowMonthlyFeeReport(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;

        ResultSet rs = null;
        Integer nTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_TOTAL_ROW_DAILY_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setString(2, (String) mIn.get(MFR_PARTNER_IDS));
            cs.setString(3, (String) mIn.get(MFR_MERCHANT_IDS));
            cs.setString(4, (String) mIn.get(MFR_TAX_CODE));
            cs.setString(5, (String) mIn.get(MFR_FROM_DATE));
            cs.setString(6, (String) mIn.get(MFR_TO_DATE));
            cs.setInt(7, mIn.get(MFR_PAGE_ACTIVE) == null ? 0 : (Integer) mIn.get(MFR_PAGE_ACTIVE));
            cs.setInt(8, mIn.get(MFR_PAGE_SIZE) == null ? Integer.MAX_VALUE : (Integer) mIn.get(MFR_PAGE_SIZE));
            cs.setString(9, (String) mIn.get(MFR_TYPE_ADVANCES));
            cs.setString(10, (String) mIn.get(MFR_TIME_ADVANCES));
            cs.setString(11, (String) mIn.get(MFR_PAY_CHANNELS));
            cs.setString(12, (String) mIn.get(MFR_RECEIPT_TYPES));
            cs.setString(13, (String) mIn.get(MFR_CONTROL_MINUTES));
            cs.setInt(14, (Integer) mIn.get(MFR_TIME_INTERVAL));
            cs.execute();

            nTotal = cs.getInt(1);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nTotal;
    }


    public static String filterStringNullPointer(Object object) {
        return object == null ? "" : object.toString();
    }

    public static boolean isEmptyString(String value) {
        if ("".equals(value) || value == null) {
            return true;
        }
        return false;
    }

    private static Double parseDouble(Double amount) {
        if (amount == null)
            return 0.0;
        return amount;
    }
}
