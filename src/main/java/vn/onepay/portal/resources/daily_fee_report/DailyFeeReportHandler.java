package vn.onepay.portal.resources.daily_fee_report;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.impl.URIDecoder;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.RoutePool;

import static vn.onepay.portal.Util.sendResponse;
import static vn.onepay.portal.Util.gson;

public class DailyFeeReportHandler implements IConstants {
    private static final Logger logger = Logger
            .getLogger(DailyFeeReportHandler.class.getName());

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();

                String fromDate = isNull(params.get("fromDate")) ? "" : params.get("fromDate");
                String toDate = isNull(params.get("toDate")) ? "" : params.get("toDate");
                JsonArray partners = new JsonArray(params.get("partners"));
                String partnerIds = partners.stream().map(partner -> ((JsonObject) partner).getValue("partnerId").toString()).collect(Collectors.joining(","));
                JsonArray merchants = new JsonArray(params.get("merchants"));
                String merchantIds = merchants.stream().map(merchant -> ((JsonObject) merchant).getValue("merchantId").toString()).collect(Collectors.joining(","));
                String taxCode = isNull(params.get("taxCode")) ? "" : params.get("taxCode");
                String encodedTypeAdvances = isNull(params.get("typeAdvances")) ? "" : params.get("typeAdvances"); // Chua ki tu '+' -> encode tren FE
                String typeAdvances = URIDecoder.decodeURIComponent(encodedTypeAdvances);
                String timeAdvanes = isNull(params.get("timeAdvances")) ? "" : params.get("timeAdvances");
                String payChannels = isNull(params.get("payChannels")) ? "" : params.get("payChannels");
                String receiptTypes = isNull(params.get("receiptTypes")) ? "" : params.get("receiptTypes");
                String controlMinutes = isNull(params.get("controlMinutes")) ? "" : params.get("controlMinutes");
                Integer timeInterval = Integer.parseInt(params.get("timeInterval"));
                Integer pageActive = Integer.parseInt(params.get("pageActive"));
                Integer pageSize = Integer.parseInt(params.get("pageSize"));

                Map<String, Object> mIn = new HashMap();
                mIn.put(MFR_FROM_DATE, fromDate);
                mIn.put(MFR_TO_DATE, toDate);
                mIn.put(MFR_PARTNER_IDS, partnerIds);
                mIn.put(MFR_MERCHANT_IDS, merchantIds);
                mIn.put(MFR_TAX_CODE, taxCode);
                mIn.put(MFR_TYPE_ADVANCES, typeAdvances);
                mIn.put(MFR_TIME_ADVANCES, timeAdvanes);
                mIn.put(MFR_PAY_CHANNELS, payChannels);
                mIn.put(MFR_RECEIPT_TYPES, receiptTypes);
                mIn.put(MFR_CONTROL_MINUTES, controlMinutes);
                mIn.put(MFR_TIME_INTERVAL, timeInterval);
                mIn.put(MFR_PAGE_ACTIVE, pageActive);
                mIn.put(MFR_PAGE_SIZE, pageSize);
                logger.info("MONTHLY FEE REPORT SEARCH REQUEST PARAMS | " + mIn);

                sendResponse(ctx, 200, DailyFeeReportDao.list(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH MONTHLY FEE REPORT ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportExcel(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String fromDate = isNull(bodyJson.getString("fromDate")) ? "" : bodyJson.getString("fromDate");
                String toDate = isNull(bodyJson.getString("toDate")) ? "" : bodyJson.getString("toDate");
                JsonArray partners = new JsonArray(bodyJson.getString("partners"));
                String partnerIds = partners.stream().map(partner -> ((JsonObject) partner).getValue("partnerId").toString()).collect(Collectors.joining(","));
                JsonArray merchants = new JsonArray(bodyJson.getString("merchants"));
                String merchantIds = merchants.stream().map(merchant -> ((JsonObject) merchant).getValue("merchantId").toString()).collect(Collectors.joining(","));
                String taxCode = isNull(bodyJson.getString("taxCode")) ? "" : bodyJson.getString("taxCode");
                String encodedTypeAdvances = isNull(bodyJson.getString("typeAdvances")) ? "" : bodyJson.getString("typeAdvances"); //Chua ki tu '+' -> encode tren FE
                String typeAdvances = URIDecoder.decodeURIComponent(encodedTypeAdvances);
                String timeAdvanes = isNull(bodyJson.getString("timeAdvances")) ? "" : bodyJson.getString("timeAdvances");
                String payChannels = isNull(bodyJson.getString("payChannels")) ? "" : bodyJson.getString("payChannels");
                String receiptTypes = isNull(bodyJson.getString("receiptTypes")) ? "" : bodyJson.getString("receiptTypes");
                // String receiptState = isNull(bodyJson.getString("receiptState")) ? "" : bodyJson.getString("receiptState");
                // String notifyState = isNull(bodyJson.getString("notifyState")) ? "" : bodyJson.getString("notifyState");
                Integer timeInterval = bodyJson.getInteger("timeInterval");
                Integer pageActive = 0;
                Integer pageSize = Integer.MAX_VALUE;
                
                Map<String, Object> mIn = new HashMap();
                mIn.put(MFR_FROM_DATE, fromDate);
                mIn.put(MFR_TO_DATE, toDate);
                mIn.put(MFR_PARTNER_IDS, partnerIds);
                mIn.put(MFR_MERCHANT_IDS, merchantIds);
                mIn.put(MFR_TAX_CODE, taxCode);
                mIn.put(MFR_TYPE_ADVANCES, typeAdvances);
                mIn.put(MFR_TIME_ADVANCES, timeAdvanes);
                mIn.put(MFR_PAY_CHANNELS, payChannels);
                mIn.put(MFR_RECEIPT_TYPES, receiptTypes);
                mIn.put(MFR_TIME_INTERVAL, timeInterval);
                mIn.put(MFR_PAGE_ACTIVE, pageActive);
                mIn.put(MFR_PAGE_SIZE, pageSize);
                logger.info("DAILY FEE REPORT EXPORT REQUEST PARAMS | " + mIn);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = DailyFeeReportDao.getTotalRowMonthlyFeeReport(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "monthly_fee_report_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("monthly_fee_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                    QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                    QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch(Exception e) {
                logger.log(Level.WARNING, "EXPORT EXCEL MONTHLY FEE REPORT Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
        
    }

    public static boolean isNull(Object obj) {
        return obj == null ? true : false;
    }
}
