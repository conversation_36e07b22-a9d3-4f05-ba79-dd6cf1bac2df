package vn.onepay.portal.resources.daily_fee_report;

import java.util.Date;
import java.text.SimpleDateFormat;

public class DailyFeeReportDto {
    private SimpleDateFormat sdf = null;

    private Integer index;
    private String reportId;
    private String partnerName;
    private String businessName;
    private Integer partnerId;
    private String merchantIds;
    private String contractCode;
    private Long contractDateTime;
    private Integer contractId;
    private String addendum;
    private String countSuccess;
    private String countFailed;
    private String originAmountUsd;
    private String originAmountVnd;
    private String partnerDiscountAmount;
    private String merchantDiscountAmount;
    private String amountTotalVnd;
    private String feeSuccess;
    private String feeFailed;
    private String feeEcom;
    private String feeIta;
    private String totalFee;
    private String advanceAmount;
    private String payChannels;
    private String timeAdvance;
    private String typeAdvance;
    private String receiptType;
    private String receiptState;
    private Integer idConfig;
    private String taxCode;
    private String accountNumbers;
    private String totalVAT;
    private Date dateReport;
    private Date fromDate;
    private Date toDate;
    private Date fromDateQT;
    private Long fromDateQTTime;
    private Date toDateQT;
    private Long toDateQTTime;
    private Date fromDateND;
    private Long fromDateNDTime;
    private Date toDateND;
    private Long toDateNDTime;
    private Date fromDateQR;
    private Long fromDateQRTime;
    private Date toDateQR;
    private Long toDateQRTime;
    private Date fromDateSMS;
    private Long fromDateSMSTime;
    private Date toDateSMS;
    private Long toDateSMSTime;
    private Date fromDateBL;
    private Long fromDateBLTime;
    private Date toDateBL;
    private Long toDateBLTime;
    private Date fromDateBNPL;
    private Long fromDateBNPLTime;
    private Date toDateBNPL;
    private Long toDateBNPLTime;
    private Long numberEmailSent;
    private String controlMinutes;
    private String merchantName;
    private String discountFee;
    private String totalFeeCollected;
    private String totalFeeReceivable;

    public Date getFromDate() {
        return this.fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return this.toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public String getDiscountFee() {
        return this.discountFee;
    }

    public void setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
    }

    public String getTotalFeeCollected() {
        return this.totalFeeCollected;
    }

    public void setTotalFeeCollected(String totalFeeCollected) {
        this.totalFeeCollected = totalFeeCollected;
    }

    public String getTotalFeeReceivable() {
        return this.totalFeeReceivable;
    }

    public void setTotalFeeReceivable(String totalFeeReceivable) {
        this.totalFeeReceivable = totalFeeReceivable;
    }

    public Integer getIndex() {
        return this.index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getReportId() {
        return this.reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getBusinessName() {
        return this.businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Date getDateReport() {
        return this.dateReport;
    }

    public void setDateReport(Date dateReport) {
        this.dateReport = dateReport;
    }

    public Integer getPartnerId() {
        return this.partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getMerchantIds() {
        return this.merchantIds;
    }

    public void setMerchantIds(String merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getContractCode() {
        return this.contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Long getContractDateTime() {
        return this.contractDateTime;
    }

    public void setContractDateTime(Long contractDateTime) {
        this.contractDateTime = contractDateTime;
    }

    public Integer getContractId() {
        return this.contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public String getCountSuccess() {
        return this.countSuccess;
    }

    public void setCountSuccess(String countSuccess) {
        this.countSuccess = countSuccess;
    }

    public String getCountFailed() {
        return this.countFailed;
    }

    public void setCountFailed(String countFailed) {
        this.countFailed = countFailed;
    }

    public String getPartnerDiscountAmount() {
        return this.partnerDiscountAmount;
    }

    public void setPartnerDiscountAmount(String partnerDiscountAmount) {
        this.partnerDiscountAmount = partnerDiscountAmount;
    }

    public String getMerchantDiscountAmount() {
        return this.merchantDiscountAmount;
    }

    public void setMerchantDiscountAmount(String merchantDiscountAmount) {
        this.merchantDiscountAmount = merchantDiscountAmount;
    }

    public String getAmountTotalVnd() {
        return this.amountTotalVnd;
    }

    public void setAmountTotalVnd(String amountTotalVnd) {
        this.amountTotalVnd = amountTotalVnd;
    }

    public String getFeeSuccess() {
        return this.feeSuccess;
    }

    public void setFeeSuccess(String feeSuccess) {
        this.feeSuccess = feeSuccess;
    }

    public String getFeeFailed() {
        return this.feeFailed;
    }

    public void setFeeFailed(String feeFailed) {
        this.feeFailed = feeFailed;
    }

    public String getFeeIta() {
        return this.feeIta;
    }

    public void setFeeIta(String feeIta) {
        this.feeIta = feeIta;
    }

    public String getTotalFee() {
        return this.totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getAdvanceAmount() {
        return this.advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getPayChannels() {
        return this.payChannels;
    }

    public void setPayChannels(String payChannels) {
        this.payChannels = payChannels;
    }

    public String getTypeAdvance() {
        return this.typeAdvance;
    }

    public void setTypeAdvance(String typeAdvance) {
        this.typeAdvance = typeAdvance;
    }

    public String getReceiptType() {
        return this.receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getReceiptState() {
        return this.receiptState;
    }

    public void setReceiptState(String receiptState) {
        this.receiptState = receiptState;
    }

    public SimpleDateFormat getSdf() {
        return this.sdf;
    }

    public void setSdf(SimpleDateFormat sdf) {
        this.sdf = sdf;
    }

    public String getFeeEcom() {
        return this.feeEcom;
    }

    public void setFeeEcom(String feeEcom) {
        this.feeEcom = feeEcom;
    }

    public Integer getIdConfig() {
        return this.idConfig;
    }

    public void setIdConfig(Integer idConfig) {
        this.idConfig = idConfig;
    }

    public String getTaxCode() {
        return this.taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getTotalVAT() {
        return this.totalVAT;
    }

    public void setTotalVAT(String totalVAT) {
        this.totalVAT = totalVAT;
    }

    public String getTimeAdvance() {
        return this.timeAdvance;
    }

    public void setTimeAdvance(String timeAdvance) {
        this.timeAdvance = timeAdvance;
    }

    public String getAccountNumbers() {
        return this.accountNumbers;
    }

    public void setAccountNumbers(String accountNumbers) {
        this.accountNumbers = accountNumbers;
    }

    public String getOriginAmountUsd() {
        return this.originAmountUsd;
    }

    public void setOriginAmountUsd(String originAmountUsd) {
        this.originAmountUsd = originAmountUsd;
    }

    public String getOriginAmountVnd() {
        return this.originAmountVnd;
    }

    public void setOriginAmountVnd(String originAmountVnd) {
        this.originAmountVnd = originAmountVnd;
    }

    public Date getFromDateQT() {
        return this.fromDateQT;
    }

    public void setFromDateQT(Date fromDateQT) {
        this.fromDateQT = fromDateQT;
    }

    public Date getToDateQT() {
        return this.toDateQT;
    }

    public void setToDateQT(Date toDateQT) {
        this.toDateQT = toDateQT;
    }

    public Date getFromDateND() {
        return this.fromDateND;
    }

    public void setFromDateND(Date fromDateND) {
        this.fromDateND = fromDateND;
    }

    public Date getToDateND() {
        return this.toDateND;
    }

    public void setToDateND(Date toDateND) {
        this.toDateND = toDateND;
    }

    public Date getFromDateQR() {
        return this.fromDateQR;
    }

    public void setFromDateQR(Date fromDateQR) {
        this.fromDateQR = fromDateQR;
    }

    public Date getToDateQR() {
        return this.toDateQR;
    }

    public void setToDateQR(Date toDateQR) {
        this.toDateQR = toDateQR;
    }

    public Date getFromDateSMS() {
        return this.fromDateSMS;
    }

    public void setFromDateSMS(Date fromDateSMS) {
        this.fromDateSMS = fromDateSMS;
    }

    public Date getToDateSMS() {
        return this.toDateSMS;
    }

    public void setToDateSMS(Date toDateSMS) {
        this.toDateSMS = toDateSMS;
    }

    public Long getFromDateQTTime() {
        return this.fromDateQTTime;
    }

    public void setFromDateQTTime(Long fromDateQTTime) {
        this.fromDateQTTime = fromDateQTTime;
    }

    public Long getToDateQTTime() {
        return this.toDateQTTime;
    }

    public void setToDateQTTime(Long toDateQTTime) {
        this.toDateQTTime = toDateQTTime;
    }

    public Long getFromDateNDTime() {
        return this.fromDateNDTime;
    }

    public void setFromDateNDTime(Long fromDateNDTime) {
        this.fromDateNDTime = fromDateNDTime;
    }

    public Long getToDateNDTime() {
        return this.toDateNDTime;
    }

    public void setToDateNDTime(Long toDateNDTime) {
        this.toDateNDTime = toDateNDTime;
    }

    public Long getFromDateQRTime() {
        return this.fromDateQRTime;
    }

    public void setFromDateQRTime(Long fromDateQRTime) {
        this.fromDateQRTime = fromDateQRTime;
    }

    public Long getToDateQRTime() {
        return this.toDateQRTime;
    }

    public void setToDateQRTime(Long toDateQRTime) {
        this.toDateQRTime = toDateQRTime;
    }

    public Long getFromDateSMSTime() {
        return this.fromDateSMSTime;
    }

    public void setFromDateSMSTime(Long fromDateSMSTime) {
        this.fromDateSMSTime = fromDateSMSTime;
    }

    public Long getToDateSMSTime() {
        return this.toDateSMSTime;
    }

    public void setToDateSMSTime(Long toDateSMSTime) {
        this.toDateSMSTime = toDateSMSTime;
    }

    public Date getFromDateBL() {
        return this.fromDateBL;
    }

    public void setFromDateBL(Date fromDateBL) {
        this.fromDateBL = fromDateBL;
    }

    public Long getFromDateBLTime() {
        return this.fromDateBLTime;
    }

    public void setFromDateBLTime(Long fromDateBLTime) {
        this.fromDateBLTime = fromDateBLTime;
    }

    public Date getToDateBL() {
        return this.toDateBL;
    }

    public void setToDateBL(Date toDateBL) {
        this.toDateBL = toDateBL;
    }

    public Long getToDateBLTime() {
        return this.toDateBLTime;
    }

    public void setToDateBLTime(Long toDateBLTime) {
        this.toDateBLTime = toDateBLTime;
    }

    public Date getFromDateBNPL() {
        return this.fromDateBNPL;
    }

    public void setFromDateBNPL(Date fromDateBNPL) {
        this.fromDateBNPL = fromDateBNPL;
    }

    public Long getFromDateBNPLTime() {
        return this.fromDateBNPLTime;
    }

    public void setFromDateBNPLTime(Long fromDateBNPLTime) {
        this.fromDateBNPLTime = fromDateBNPLTime;
    }

    public Date getToDateBNPL() {
        return this.toDateBNPL;
    }

    public void setToDateBNPL(Date toDateBNPL) {
        this.toDateBNPL = toDateBNPL;
    }

    public Long getToDateBNPLTime() {
        return this.toDateBNPLTime;
    }

    public void setToDateBNPLTime(Long toDateBNPLTime) {
        this.toDateBNPLTime = toDateBNPLTime;
    }

    public Long getNumberEmailSent() {
        return this.numberEmailSent;
    }

    public void setNumberEmailSent(Long numberEmailSent) {
        this.numberEmailSent = numberEmailSent;
    }

    public String getAddendum() {
        return this.addendum;
    }

    public void setAddendum(String addendum) {
        this.addendum = addendum;
    }

    public String getControlMinutes() {
        return this.controlMinutes;
    }

    public void setControlMinutes(String controlMinutes) {
        this.controlMinutes = controlMinutes;
    }

    public String getMerchantName() {
        return this.merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
}
