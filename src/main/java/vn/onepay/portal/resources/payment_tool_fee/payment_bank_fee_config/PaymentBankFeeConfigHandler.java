package vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config;

import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dao.PaymentBankFeeConfigDao;

import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class PaymentBankFeeConfigHandler implements IConstants {
    public PaymentBankFeeConfigHandler() {
    }

    private static Logger logger = Logger.getLogger(PaymentBankFeeConfigHandler.class.getName());

    public static void getListFeeData(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int id = Convert.parseInt(ctx.request().getParam("id"), 0);
                String feeType = ctx.request().getParam("feeType");
                sendResponse(ctx, 200, PaymentBankFeeConfigDao.getListBankFeeConfig(id, feeType));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}
