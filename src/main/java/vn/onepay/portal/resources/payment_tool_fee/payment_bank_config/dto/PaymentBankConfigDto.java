package vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dto;

import java.io.Serializable;

public class PaymentBankConfigDto implements Serializable {
    private int id;
    private String sId;


    private String shortName;
    private String fullName;
    private String payChannel;
    public PaymentBankConfigDto() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getsId() {
        return sId;
    }

    public void setsId(String sId) {
        this.sId = sId;
    }



    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }
    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

}
