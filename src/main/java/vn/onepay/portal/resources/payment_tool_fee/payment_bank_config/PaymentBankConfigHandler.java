package vn.onepay.portal.resources.payment_tool_fee.payment_bank_config;

import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dao.PaymentBankConfigDao;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dto.PaymentFeeDataItaDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class PaymentBankConfigHandler implements IConstants {
    private static Logger logger = Logger.getLogger(PaymentBankConfigHandler.class.getName());

    public static void getListBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, PaymentBankConfigDao.getListBank());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "get list bank: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int bankConfigId = ctx.getBodyAsJson().getInteger("bankConfigId", 0);
                String advanceType = ctx.getBodyAsJson().getString("advanceType", "");
                int bankMerchantConfigId = ctx.getBodyAsJson().getInteger("bankMerchantId", 0);
                String state = ctx.getBodyAsJson().getString(STATE, "");
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                List<PaymentFeeDataItaDto> listData;
                if (state.equalsIgnoreCase("NEW")) {
                    listData = PaymentBankConfigDao
                            .getListMerchantWithAdvanceType(bankConfigId, advanceType);
                } else if (state.equalsIgnoreCase("COMPLETED")) {
                    listData = PaymentBankConfigDao
                            .getListMerchantLogWithAdvanceType(bankMerchantConfigId, advanceType);
                } else {
                    listData = new ArrayList<>();
                }
                int totalRows;
                totalRows = listData.size();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }
                String advanceFile = "";
                if (Objects.equals(advanceType, "T+1")) {
                    advanceFile = "t_1_";
                } else if (Objects.equals(advanceType, "T+2")) {
                    advanceFile = "t_2_";
                } else {
                    advanceFile = "t_";
                }
                long date = new Date().getTime();
                String fileName = "Payment_Fee_" + advanceFile + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("file_name", fileName);
                data.put("row", totalRows);
                data.put("data", listData);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("payment_fee_ita");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setExt("xls");
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD FEE DATA ITA: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}
