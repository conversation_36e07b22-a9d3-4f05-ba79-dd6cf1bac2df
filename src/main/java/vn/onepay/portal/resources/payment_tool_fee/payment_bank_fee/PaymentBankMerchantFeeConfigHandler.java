package vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee.dao.PaymentBankMerchantFeeConfigDao;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dao.PaymentBankFeeConfigDao;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dto.PaymentBankFeeConfigCommand;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class PaymentBankMerchantFeeConfigHandler implements IConstants {
    public PaymentBankMerchantFeeConfigHandler() {
    }

    private static Logger logger = Logger.getLogger(PaymentBankMerchantFeeConfigHandler.class.getName());

    public static void getDetailBankFeeMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int id = Convert.parseInt(ctx.request().getParam("id"), 0);
                sendResponse(ctx, 200, PaymentBankMerchantFeeConfigDao.getDetail(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void delete(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int id = Convert.parseInt(ctx.request().getParam("id"), 0);
                sendResponse(ctx, 200, PaymentBankMerchantFeeConfigDao.delete(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> mIn = new HashMap<>();
                int page = Convert.parseInt(ctx.request().getParam("page"), 0);
                mIn.put("page", page);
                int size = Convert.parseInt(ctx.request().getParam("size"), 10);
                mIn.put("size", size);
                String type = ctx.request().getParam("type");
                mIn.put("type", type);
                String keyWord = ctx.request().getParam("keyWord") == null ? BLANK : ctx.request().getParam("keyWord");
                mIn.put("keyWord", keyWord);
                String fromDate = ctx.request().getParam("fromDate") == null ? BLANK : ctx.request().getParam("fromDate");
                mIn.put("fromDate", fromDate);
                String toDate = ctx.request().getParam("toDate") == null ? BLANK : ctx.request().getParam("toDate");
                mIn.put("toDate", toDate);
                String status = ctx.request().getParam("status") == null ? BLANK : ctx.request().getParam("status");
                mIn.put("status", status);
                sendResponse(ctx, 200, PaymentBankMerchantFeeConfigDao.search(mIn));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void pushBankMerchantFeeConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("id", ctx.getBodyAsJson().getInteger("id"));
                mIn.put("bankId", ctx.getBodyAsJson().getString("bankId"));
                mIn.put("bankName", ctx.getBodyAsJson().getString("bankName"));
                mIn.put("active", ctx.getBodyAsJson().getString("active"));
                mIn.put("status", ctx.getBodyAsJson().getString("status"));
                mIn.put("startDate", ctx.getBodyAsJson().getString("startDate"));
                mIn.put("applyId", ctx.getBodyAsJson().getString("applyId"));
                mIn.put("applyDate", ctx.getBodyAsJson().getString("applyDate"));
                mIn.put("key", ctx.getBodyAsJson().getString("key"));
                mIn.put("actor", ctx.getBodyAsJson().getString("actor"));
                mIn.put("type", ctx.getBodyAsJson().getString("type"));
                mIn.put("bankMerchantFeeNew", ctx.getBodyAsJson().getJsonArray("bankMerchantFeeNew"));
                var result = PaymentBankMerchantFeeConfigDao.pushBankMerchantFeeConfig(mIn);
                int bankMerchantId = Convert.parseInt(result.get("nId").toString(), 0);
                String feeType = "";
                String bankId = ctx.getBodyAsJson().getString("bankId");
                String bankName = ctx.getBodyAsJson().getString("bankName");
                String status = "NEW";
                String actor = ctx.getBodyAsJson().getString("actor");
                String startDate = ctx.getBodyAsJson().getString("startDate");
                int idInput = Convert.parseInt(ctx.getBodyAsJson().getInteger("id").toString(), 0);
                if (Convert.parseInt(result.get("status").toString(), 500) == 200) {
                    System.out.println("status == 200");
                    if (idInput != 0) {
                        PaymentBankFeeConfigDao.delete(idInput);
                    }
                    var newData = ctx.getBodyAsJson().getJsonArray("bankMerchantFeeNew");
                    for (var data : newData) {
                        feeType = "new";
                        PaymentBankFeeConfigCommand command = getPaymentBankFeeConfigCommand(data);
                        PaymentBankFeeConfigDao.pushBankFeeConfig(bankMerchantId, feeType, bankId, bankName,
                                status, actor, startDate, command);
                    }
                    if (Objects.equals(ctx.getBodyAsJson().getString("type",""), "port")){
                        var oldData = ctx.getBodyAsJson().getJsonArray("bankMerchantFeeOld");
                        for (var data : oldData) {
                            feeType = "old";
                            PaymentBankFeeConfigCommand command = getPaymentBankFeeConfigCommand(data);
                            PaymentBankFeeConfigDao.pushBankFeeConfig(bankMerchantId, feeType, bankId, bankName,
                                    status, actor, startDate, command);
                        }
                    }
                }
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static PaymentBankFeeConfigCommand getPaymentBankFeeConfigCommand(Object data) {
        JsonObject object = new JsonObject(String.valueOf(data));
        PaymentBankFeeConfigCommand command = new PaymentBankFeeConfigCommand();
        command.setS3(object.getString("s3"));
        command.setS6(object.getString("s6"));
        command.setS9(object.getString("s9"));
        command.setS12(object.getString("s12"));
        command.setS15(object.getString("s15"));
        command.setS18(object.getString("s18"));
        command.setS24(object.getString("s24"));
        command.setS36(object.getString("s36"));
        command.setPosition(object.getString("position"));
        command.setType(object.getString("type"));
        return command;
    }
}
