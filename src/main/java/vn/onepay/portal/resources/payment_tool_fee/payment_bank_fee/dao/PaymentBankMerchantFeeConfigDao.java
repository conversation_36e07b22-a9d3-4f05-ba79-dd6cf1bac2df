package vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.dto.MspBankDto;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee.dto.PaymentBankMerchantFeeConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentBankMerchantFeeConfigDao extends Db {
    private static final Logger _LOGGER = Logger.getLogger(PaymentBankMerchantFeeConfigDao.class.getName());

    private static final String PUSH_BANK_MERCHANT_FEE_CONFIG = "{call onefin.pkg_bank_merchant_fee_config.push_bank_merchant_fee(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_DETAIL_BANK_MERCHANT_FEE_CONFIG = "{call onefin.pkg_bank_merchant_fee_config.get_detail(?,?,?,?)}";
    private static final String SEARCH_BANK_MERCHANT_FEE_CONFIG = "{call onefin.pkg_bank_merchant_fee_config.search_bank_merchant_fee(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DELETE_BANK_MERCHANT_FEE_CONFIG = "{call onefin.pkg_bank_merchant_fee_config.delete_bank_merchant_fee(?,?,?)}";

    public static Map<String, Object> pushBankMerchantFeeConfig(Map<String, Object> mIn) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        int ntotal = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(PUSH_BANK_MERCHANT_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get("id").toString(), 0));
            cs.setString(5, Convert.toString(mIn.getOrDefault("bankId", "")));
            cs.setString(6, Convert.toString(mIn.getOrDefault("bankName", "")));
            cs.setString(7, Convert.toString(mIn.getOrDefault("active", "")));
            cs.setString(8, Convert.toString(mIn.getOrDefault("status", "")));
            cs.setString(9, Convert.toString(mIn.getOrDefault("startDate", "")));
            cs.setInt(10, Convert.parseInt(mIn.getOrDefault("applyId", "0").toString(), 0));
            cs.setString(11, Convert.toString(mIn.getOrDefault("applyDate", "")));
            cs.setInt(12, Convert.parseInt(mIn.getOrDefault("key", "0").toString(), 0));
            cs.setString(13, Convert.toString(mIn.getOrDefault("actor", "")));
            cs.setString(14, Convert.toString(mIn.getOrDefault("type", "")));
            cs.execute();
            error = cs.getString(2);
            nError = cs.getInt(1);
            ntotal = cs.getInt(3);
            if (nError == 500) {
                throw new SQLException("DB push bank merchant fee config : " + error);
            }
            result.put("status", nError);
            result.put("message", error);
            result.put("nId", ntotal);
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getDetail(int id) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_DETAIL_BANK_MERCHANT_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 500) {
                throw new SQLException("DB get_detail_bank_merchant_fee_config : " + error);
            } else {
                if (rs != null && rs.next()) {
                    result.put("data", convertData(rs));
                    result.put("status", nError);
                    result.put("message", error);
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> delete(int id) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(DELETE_BANK_MERCHANT_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.execute();
            error = cs.getString(2);
            nError = cs.getInt(1);
            if (nError == 500) {
                throw new SQLException("DB delete_bank_merchant_fee_config : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.put("status", nError);
        result.put("message", error);
        return result;
    }

    public static Map<String, Object> search(Map<String, Object> mIn) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        List<PaymentBankMerchantFeeConfigDto> data = new ArrayList<>();
        int nError = 500;
        String error = "";
        int total = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH_BANK_MERCHANT_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, Convert.parseInt(mIn.get("page").toString(), 0));
            cs.setInt(6, Convert.parseInt(mIn.get("size").toString(), 0));
            cs.setString(7, Convert.toString(mIn.getOrDefault("fromDate", "").toString()));
            cs.setString(8, Convert.toString(mIn.getOrDefault("toDate", "").toString()));
            cs.setString(9, Convert.toString(mIn.getOrDefault("keyWord", "").toString()));
            cs.setString(10, Convert.toString(mIn.getOrDefault("status", "").toString()));
            cs.setString(11, Convert.toString(mIn.getOrDefault("type", "").toString()));
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            total = cs.getInt(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB MSP get list bank : " + error);
            } else {
                while (rs != null && rs.next()) {
                    data.add(convertData(rs));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.put("list", data);
        result.put("status", nError);
        result.put("message", nError);
        result.put("total", total);
        return result;
    }

    private static PaymentBankMerchantFeeConfigDto convertData(ResultSet rs) throws SQLException {
        PaymentBankMerchantFeeConfigDto result = new PaymentBankMerchantFeeConfigDto();
        result.setId(rs.getInt("N_ID"));
        result.setBankId(rs.getString("S_BANK_ID"));
        result.setStatus(rs.getString("S_STATUS"));
        result.setActive(rs.getString("S_ACTIVE"));
        result.setStartDate(rs.getString("D_START"));
        result.setApplyDate(rs.getString("D_APPLY"));
        result.setApply(rs.getInt("N_APPLY"));
        result.setKey(rs.getInt("N_KEY"));
        result.setCreatedAt(rs.getString("D_CREATE"));
        result.setCreatedBy(rs.getString("D_CREATE"));
        result.setModifiedAt(rs.getString("D_UPDATE"));
        result.setModifiedBy(rs.getString("S_UPDATE"));
        result.setBankName(rs.getString("S_BANK_NAME"));
        result.setType(rs.getString("S_TYPE"));
        return result;
    }
}
