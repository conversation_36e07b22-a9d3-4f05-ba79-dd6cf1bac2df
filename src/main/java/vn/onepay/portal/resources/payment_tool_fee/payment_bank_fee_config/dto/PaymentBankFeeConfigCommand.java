package vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dto;

import java.io.Serializable;

public class PaymentBankFeeConfigCommand implements Serializable {
    private String type;
    private String s3;
    private String s6;
    private String s9;
    private String s12;
    private String s15;
    private String s18;
    private String s24;
    private String s36;
    private String position;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getS3() {
        return s3;
    }

    public void setS3(String s3) {
        this.s3 = s3;
    }

    public String getS6() {
        return s6;
    }

    public void setS6(String s6) {
        this.s6 = s6;
    }

    public String getS9() {
        return s9;
    }

    public void setS9(String s9) {
        this.s9 = s9;
    }

    public String getS12() {
        return s12;
    }

    public void setS12(String s12) {
        this.s12 = s12;
    }

    public String getS15() {
        return s15;
    }

    public void setS15(String s15) {
        this.s15 = s15;
    }

    public String getS18() {
        return s18;
    }

    public void setS18(String s18) {
        this.s18 = s18;
    }

    public String getS24() {
        return s24;
    }

    public void setS24(String s24) {
        this.s24 = s24;
    }

    public String getS36() {
        return s36;
    }

    public void setS36(String s36) {
        this.s36 = s36;
    }

    public String getPosition() {
        return position;
    }


    public void setPosition(String position) {
        this.position = position;
    }

    public PaymentBankFeeConfigCommand() {
    }

    public PaymentBankFeeConfigCommand(String type, String s3, String s6, String s9, String s12, String s15, String s18, String s24, String s36, String position) {
        this.type = type;
        this.s3 = s3;
        this.s6 = s6;
        this.s9 = s9;
        this.s12 = s12;
        this.s15 = s15;
        this.s18 = s18;
        this.s24 = s24;
        this.s36 = s36;
        this.position = position;
    }
}
