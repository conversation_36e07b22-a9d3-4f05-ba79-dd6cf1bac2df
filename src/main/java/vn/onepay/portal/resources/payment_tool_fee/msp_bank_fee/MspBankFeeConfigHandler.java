package vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.dao.MspBankFeeConfigDao;
import vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.query.MspInstallmentMerchantQuery;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.Util.sendResponse;


public class MspBankFeeConfigHandler implements IConstants {
    public MspBankFeeConfigHandler() {
    }

    private static Logger logger = Logger.getLogger(MspBankFeeConfigHandler.class.getName());

    public static void getListBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MspBankFeeConfigDao.getListBank());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadMerchantEquals(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String installmentId = ctx.getBodyAsJson().getString("installmentId", "");
                String status = ctx.getBodyAsJson().getString("status", "");
                int bankMerchantFeeConfigId = ctx.getBodyAsJson().getInteger("bankMerchantFeeConfigId", 0);
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                List<JsonObject> installmentMerchantAll = MspBankFeeConfigDao.getListInstallmentMerchant(installmentId);
                List<JsonObject> lists;
                if (installmentMerchantAll.isEmpty()) {
                    lists = new ArrayList<>();
                } else if (status.equalsIgnoreCase("NEW")) {
                    lists = getInstallmentMerchantEquals(installmentMerchantAll);
                } else if (status.equalsIgnoreCase("COMPLETED")) {
                    lists = MspBankFeeConfigDao.getListInstallmentMerchantLog(bankMerchantFeeConfigId, "EQ_MATCH");
                } else {
                    lists = new ArrayList<>();
                }
                int totalRows = 0;
                totalRows = lists.size();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }
                var dataMap = lists.stream().map(item -> {
                    Map result = new HashMap();
                    result.put("installmentId", item.getString("installmentId", ""));
                    result.put("merchantId", item.getString("merchantId", ""));
                    result.put("createdAt", item.getString("createdAt", ""));
                    result.put("modifiedAt", item.getString("modifiedAt", ""));
                    result.put("state", item.getString("state", ""));
                    result.put("description", item.getString("description", ""));
                    result.put("times", item.getString("times", ""));
                    result.put("cancelDays", item.getInteger("cancelDays"));
                    result.put("minAmount", item.getDouble("minAmount"));
                    result.put("maxAmount", item.getDouble("maxAmount"));
                    result.put("itaFee", item.getDouble("itaFee"));
                    result.put("bankMerchantId", item.getString("bankMerchantId", ""));
                    result.put("listFee", item.getString("listFee", ""));
                    return result;
                }).collect(Collectors.toList());
                long date = new Date().getTime();
                String fileName = "installment_merchant_equals_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("file_name", fileName);
                data.put("row", totalRows);
                data.put("data", dataMap);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("installment_merchant_equals");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setExt("xls");
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD INSTALLMENT MERCHANT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadMerchantMatch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String installmentId = ctx.getBodyAsJson().getString("installmentId");
                String status = ctx.getBodyAsJson().getString("status");
                int bankMerchantFeeConfigId = ctx.getBodyAsJson().getInteger("bankMerchantFeeConfigId", 0);
                JsonArray queryOld = ctx.getBodyAsJson().getJsonArray("feeOld");
                JsonArray queryNew = ctx.getBodyAsJson().getJsonArray("feeNew");
                List<JsonObject> oldData = queryOld.stream().map(i -> new JsonObject(String.valueOf(i))).collect(Collectors.toList());
                List<JsonObject> newData = queryNew.stream().map(i -> new JsonObject(String.valueOf(i))).collect(Collectors.toList());
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                List<JsonObject> dataInput;
                if (status.equals("NEW")) {
                    var installmentMerchantAll = MspBankFeeConfigDao.getListInstallmentMerchant(installmentId);
                    dataInput = getInstallmentMerchantMatch(installmentMerchantAll, oldData, newData);
                } else if (status.equalsIgnoreCase("COMPLETED")) {
                    dataInput = MspBankFeeConfigDao.getListInstallmentMerchantLog(bankMerchantFeeConfigId, "MATCH");
                } else {
                    dataInput = new ArrayList<>();
                }
                int totalRows;
                totalRows = dataInput.size();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }
                var dataMap = dataInput.stream().map(item -> {
                    Map result = new HashMap();
                    result.put("installmentId", item.getString("installmentId", ""));
                    result.put("merchantId", item.getString("merchantId", ""));
                    result.put("createdAt", item.getString("createdAt", ""));
                    result.put("modifiedAt", item.getString("modifiedAt", ""));
                    result.put("state", item.getString("state", ""));
                    result.put("description", item.getString("description", ""));
                    result.put("times", item.getString("times", ""));
                    result.put("cancelDays", item.getInteger("cancelDays"));
                    result.put("minAmount", item.getDouble("minAmount"));
                    result.put("maxAmount", item.getDouble("maxAmount"));
                    result.put("itaFee", item.getDouble("itaFee"));
                    result.put("bankMerchantId", item.getString("bankMerchantId", ""));
                    result.put("listFee", item.getString("listFee", ""));
                    return result;
                }).collect(Collectors.toList());

                long date = new Date().getTime();
                String fileName = "installment_merchant_match" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("file_name", fileName);
                data.put("row", totalRows);
                data.put("data", dataMap);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("installment_merchant_match");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setExt("xls");
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD INSTALLMENT MERCHANT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadMerchantNotMatch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String installmentId = ctx.getBodyAsJson().getString("installmentId");
                String status = ctx.getBodyAsJson().getString("status");
                int bankMerchantFeeConfigId = ctx.getBodyAsJson().getInteger("bankMerchantFeeConfigId", 0);
                JsonArray feeOld = ctx.getBodyAsJson().getJsonArray("feeOld");
                JsonArray feeNew = ctx.getBodyAsJson().getJsonArray("feeNew");
                List<JsonObject> queryOld = feeOld.stream().map(i -> new JsonObject(String.valueOf(i))).collect(Collectors.toList());
                List<JsonObject> queryNew = feeNew.stream().map(i -> new JsonObject(String.valueOf(i))).collect(Collectors.toList());
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                List<JsonObject> installmentMerchantNotMatch;
                if (status.equalsIgnoreCase("NEW")) {
                    List<JsonObject> installmentMerchantAll = MspBankFeeConfigDao.getListInstallmentMerchant(installmentId);
                    List<JsonObject> installmentMatch = getInstallmentMerchantMatch(installmentMerchantAll, queryOld, queryNew);
                    installmentMerchantNotMatch = getInstallmentMerchantNotMatch(installmentMerchantAll, installmentMatch);
                } else if (status.equalsIgnoreCase("COMPLETED")) {
                    installmentMerchantNotMatch = MspBankFeeConfigDao.getListInstallmentMerchantLog(bankMerchantFeeConfigId, "NOT_MATCH");
                } else {
                    installmentMerchantNotMatch = new ArrayList<>();
                }
                int totalRows = 0;
                totalRows = installmentMerchantNotMatch.size();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }
                long date = new Date().getTime();
                String fileName = "installment_merchant_not_match_" + date;
                String fileHashName = "";
                var dataMap = installmentMerchantNotMatch.stream().map(item -> {
                    Map result = new HashMap();
                    result.put("installmentId", item.getString("installmentId", ""));
                    result.put("merchantId", item.getString("merchantId", ""));
                    result.put("createdAt", item.getString("createdAt", ""));
                    result.put("modifiedAt", item.getString("modifiedAt", ""));
                    result.put("state", item.getString("state", ""));
                    result.put("description", item.getString("description", ""));
                    result.put("times", item.getString("times", ""));
                    result.put("cancelDays", item.getInteger("cancelDays"));
                    result.put("minAmount", item.getDouble("minAmount"));
                    result.put("maxAmount", item.getDouble("maxAmount"));
                    result.put("itaFee", item.getDouble("itaFee"));
                    result.put("bankMerchantId", item.getString("bankMerchantId", ""));
                    result.put("listFee", item.getString("listFee", ""));
                    return result;
                }).collect(Collectors.toList());
                Map<String, Object> data = new HashMap<>();
                data.put("file_name", fileName);
                data.put("row", totalRows);
                data.put("data", dataMap);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("installment_merchant_not_match");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setExt("xls");
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(data, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD INSTALLMENT MERCHANT: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static MspInstallmentMerchantQuery convertMspInstallmentMerchantQuery(Object data) {
        JsonObject object = new JsonObject(String.valueOf(data));
        MspInstallmentMerchantQuery command = new MspInstallmentMerchantQuery();
        command.setT3(object.getString("s3"));
        command.setT6(object.getString("s6"));
        command.setT9(object.getString("s9"));
        command.setT12(object.getString("s12"));
        command.setT15(object.getString("s15"));
        command.setT18(object.getString("s18"));
        command.setT24(object.getString("s24"));
        command.setT36(object.getString("s36"));
        return command;
    }

    public static void getListInstallmentMerchantEqual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String installmentId = ctx.request().getParam("installmentId");
                var listInstallmentMerchant = MspBankFeeConfigDao.getListInstallmentMerchant(installmentId);
//                List<MspInstallmentMerchant> result = getInstallmentMerchantEquals(listInstallmentMerchant);
//                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * @param listInstallmentMerchant Danh sách tất cả bộ phi đang được cấu hình
     * @return Danh sách bộ phí có tất cả phí bằng không hoặc không được cấu hình.
     */
    private static List<JsonObject> getInstallmentMerchantEquals(List<JsonObject> listInstallmentMerchant) {
        List<JsonObject> result = new ArrayList<>();
        for (var data : listInstallmentMerchant) {
            String strFees = data.getString("listFee");
            if (strFees == null || strFees.isBlank() || strFees.isEmpty()) {
                result.add(data);
            } else {
                logger.info("check fee with bank id " + data.getString("installmentId"));
                List<JsonObject> fees = data.getJsonArray("fees").stream().map(i -> new JsonObject(i.toString()))
                        .collect(Collectors.toList());
                var listT3 = fees.stream().filter(i -> i.getInteger("t", 0) == 3).collect(Collectors.toList());
                var listT6 = fees.stream().filter(i -> i.getInteger("t", 0) == 6).collect(Collectors.toList());
                var listT9 = fees.stream().filter(i -> i.getInteger("t", 0) == 9).collect(Collectors.toList());
                var listT12 = fees.stream().filter(i -> i.getInteger("t", 0) == 12).collect(Collectors.toList());
                var listT15 = fees.stream().filter(i -> i.getInteger("t", 0) == 15).collect(Collectors.toList());
                var listT18 = fees.stream().filter(i -> i.getInteger("t", 0) == 18).collect(Collectors.toList());
                var listT24 = fees.stream().filter(i -> i.getInteger("t", 0) == 24).collect(Collectors.toList());
                var listT36 = fees.stream().filter(i -> i.getInteger("t", 0) == 36).collect(Collectors.toList());
                boolean checkT3 = listT3.isEmpty() || listT3.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT6 = listT6.isEmpty() || listT6.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT9 = listT9.isEmpty() || listT9.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT12 = listT12.isEmpty() || listT12.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT15 = listT15.isEmpty() || listT15.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT18 = listT18.isEmpty() || listT18.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT24 = listT24.isEmpty() || listT24.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                boolean checkT36 = listT36.isEmpty() || listT36.stream().anyMatch(i -> i.getDouble("pf", 0.0) == 0);
                if (checkT3 && checkT6 && checkT9 && checkT12 && checkT15 && checkT18 && checkT24 && checkT36) {
                    result.add(data);
                }
            }
        }
        return result;
    }

    /**
     * @return Danh sách merchant không match với bộ phí được cấu hình.
     */
    private static List<JsonObject> getInstallmentMerchantNotMatch(List<JsonObject> listInstallmentMerchant,
                                                                   List<JsonObject> listInstallmentMerchantMatch
    ) {
        var listE = getInstallmentMerchantEquals(listInstallmentMerchant);
        Map<String, JsonObject> result = new HashMap<>();
        listInstallmentMerchant.forEach(i -> {
            result.put(convertKey(i.getString("installmentId"), i.getString("merchantId")), i);
        });

        for (JsonObject e : listE
        ) {
            String key = convertKey(e.getString("installmentId"), e.getString("merchantId"));
            if (result.containsKey(key)) {
                result.remove(key);
            }
        }

        for (JsonObject e : listInstallmentMerchantMatch
        ) {
            String key = convertKey(e.getString("installmentId"), e.getString("merchantId"));
            if (result.containsKey(key)) {
                result.remove(key);
            }
        }
        return new ArrayList<>(result.values());
    }

    private static String convertKey(String installmentId, String merchantId) {
        return installmentId.concat(":").concat(merchantId);
    }

    /**
     * @param listData Danh sách tất cả bộ phis đang được cấu hình cho bank đó.
     * @return Danh sách bộ phí match với bộ phí cũ đang được cấu hình.
     */
    private static List<JsonObject> getInstallmentMerchantMatch(List<JsonObject> listData, List<JsonObject>
            listQueryOld, List<JsonObject>
                                                                        listQueryNew) {
        List<JsonObject> result = new ArrayList<>();
        for (JsonObject query : listQueryOld) {
            for (JsonObject data : listData) {
                List<JsonObject> fees = data.getJsonArray("fees").stream().map(i -> new JsonObject(i.toString()))
                        .collect(Collectors.toList());
                if (!fees.isEmpty()) {
                    List<JsonObject> listT3 = new ArrayList<>();
                    boolean existsQueryT3 = false;
                    List<JsonObject> listT6 = new ArrayList<>();
                    boolean existsQueryT6 = false;
                    List<JsonObject> listT9 = new ArrayList<>();
                    boolean existsQueryT9 = false;
                    List<JsonObject> listT12 = new ArrayList<>();
                    boolean existsQueryT12 = false;
                    List<JsonObject> listT15 = new ArrayList<>();
                    boolean existsQueryT15 = false;
                    List<JsonObject> listT18 = new ArrayList<>();
                    boolean existsQueryT18 = false;
                    List<JsonObject> listT24 = new ArrayList<>();
                    boolean existsQueryT24 = false;
                    List<JsonObject> listT36 = new ArrayList<>();
                    boolean existsQueryT36 = false;
                    boolean checkFormQuery = false;
                    if (query.getString("s3") != null && !query.getString("s3").isBlank()) {
                        listT3 = fees.stream().filter(i -> i.getInteger("t", 0) == 3).collect(Collectors.toList());
                        existsQueryT3 = true;
                    }
                    if (query.getString("s6") != null && !query.getString("s6").isBlank()) {
                        listT6 = fees.stream().filter(i -> i.getInteger("t", 0) == 6).collect(Collectors.toList());
                        existsQueryT6 = true;
                    }
                    if (query.getString("s9") != null && !query.getString("s9").isBlank()) {
                        listT9 = fees.stream().filter(i -> i.getInteger("t", 0) == 9).collect(Collectors.toList());
                        existsQueryT9 = true;
                    }
                    if (query.getString("s12") != null && !query.getString("s12").isBlank()) {
                        listT12 = fees.stream().filter(i -> i.getInteger("t", 0) == 12).collect(Collectors.toList());
                        existsQueryT12 = true;
                    }
                    if (query.getString("s15") != null && !query.getString("s15").isBlank()) {
                        listT15 = fees.stream().filter(i -> i.getInteger("t", 0) == 15).collect(Collectors.toList());
                        existsQueryT15 = true;
                    }
                    if (query.getString("s18") != null && !query.getString("s18").isBlank()) {
                        listT18 = fees.stream().filter(i -> i.getInteger("t", 0) == 18).collect(Collectors.toList());
                        existsQueryT18 = true;
                    }
                    if (query.getString("s24") != null && !query.getString("s24").isBlank()) {
                        listT24 = fees.stream().filter(i -> i.getInteger("t", 0) == 24).collect(Collectors.toList());
                        existsQueryT24 = true;
                    }
                    if (query.getString("s36") != null && !query.getString("s36").isBlank()) {
                        listT36 = fees.stream().filter(i -> i.getInteger("t", 0) == 36).collect(Collectors.toList());
                        existsQueryT36 = true;
                    }
                    boolean checkT3;
                    if (existsQueryT3) {
                        checkFormQuery = true;
                        if (listT3.isEmpty()) {
                            checkT3 = false;
                        } else {
                            checkT3 = listT3.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s3")));
                        }
                    } else {
                        checkT3 = true;
                    }

                    boolean checkT6;
                    if (existsQueryT6) {
                        checkFormQuery = true;
                        if (listT6.isEmpty()) {
                            checkT6 = false;
                        } else {
                            checkT6 = listT6.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s6")));
                        }
                    } else {
                        checkT6 = true;
                    }
                    boolean checkT9;
                    if (existsQueryT9) {
                        checkFormQuery = true;
                        if (listT9.isEmpty()) {
                            checkT9 = false;
                        } else {
                            checkT9 = listT9.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s9")));
                        }
                    } else {
                        checkT9 = true;
                    }

                    boolean checkT12;
                    if (existsQueryT12) {
                        checkFormQuery = true;
                        if (listT12.isEmpty()) {
                            checkT12 = false;
                        } else {
                            checkT12 = listT12.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s12")));
                        }
                    } else {
                        checkT12 = true;
                    }

                    boolean checkT15;
                    if (existsQueryT15) {
                        checkFormQuery = true;
                        if (listT15.isEmpty()) {
                            checkT15 = false;
                        } else {
                            checkT15 = listT15.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s15")));
                        }
                    } else {
                        checkT15 = true;
                    }

                    boolean checkT18;
                    if (existsQueryT18) {
                        checkFormQuery = true;
                        if (listT18.isEmpty()) {
                            checkT18 = false;
                        } else {
                            checkT18 = listT18.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s18")));
                        }
                    } else {
                        checkT18 = true;
                    }
                    boolean checkT24;
                    if (existsQueryT24) {
                        checkFormQuery = true;
                        if (listT24.isEmpty()) {
                            checkT24 = false;
                        } else {
                            checkT24 = listT24.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s24")));
                        }
                    } else {
                        checkT24 = true;
                    }

                    boolean checkT36;
                    if (existsQueryT36) {
                        checkFormQuery = true;
                        if (listT36.isEmpty()) {
                            checkT36 = false;
                        } else {
                            checkT36 = listT36.stream().allMatch(i -> i.getDouble("pf") ==
                                    Double.parseDouble(query.getString("s36")));
                        }
                    } else {
                        checkT36 = true;
                    }
                    if (checkT3 && checkT6 && checkT9 && checkT12 && checkT15 && checkT18 && checkT24 && checkT36 && checkFormQuery) {
                        data.put("queryOld", query);
                        for (JsonObject newObject : listQueryNew) {
                            if (newObject.getString("position").equals(query.getString("position"))) {
                                data.put("queryNew", newObject);
                            }
                        }
                        result.add(data);
                    }
                }
            }
        }
        return result;
    }
}
