package vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dto;

import java.io.Serializable;

public class PaymentFeeDataItaDto implements Serializable {
    private int id;
    private String merchantId;
    private int bankConfigId;
    private Double n3;
    private Double n6;
    private Double n9;
    private Double n12;
    private Double n15;
    private Double n18;
    private Double n24;
    private Double n36;
    private String approvedAt;
    private String createdAt;
    private String modifiedAt;
    private String active;
    private String state;

    public PaymentFeeDataItaDto() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public int getBankConfigId() {
        return bankConfigId;
    }

    public void setBankConfigId(int bankConfigId) {
        this.bankConfigId = bankConfigId;
    }

    public Double getN3() {
        return n3;
    }

    public void setN3(Double n3) {
        this.n3 = n3;
    }

    public Double getN6() {
        return n6;
    }

    public void setN6(Double n6) {
        this.n6 = n6;
    }

    public Double getN9() {
        return n9;
    }

    public void setN9(Double n9) {
        this.n9 = n9;
    }

    public Double getN12() {
        return n12;
    }

    public void setN12(Double n12) {
        this.n12 = n12;
    }

    public Double getN15() {
        return n15;
    }

    public void setN15(Double n15) {
        this.n15 = n15;
    }

    public Double getN18() {
        return n18;
    }

    public void setN18(Double n18) {
        this.n18 = n18;
    }

    public Double getN24() {
        return n24;
    }

    public void setN24(Double n24) {
        this.n24 = n24;
    }

    public Double getN36() {
        return n36;
    }

    public void setN36(Double n36) {
        this.n36 = n36;
    }

    public String getApprovedAt() {
        return approvedAt;
    }

    public void setApprovedAt(String approvedAt) {
        this.approvedAt = approvedAt;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getModifiedAt() {
        return modifiedAt;
    }

    public void setModifiedAt(String modifiedAt) {
        this.modifiedAt = modifiedAt;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
