package vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dto.PaymentBankConfigDto;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_config.dto.PaymentFeeDataItaDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentBankConfigDao extends Db {
    private static final Logger _LOGGER = Logger.getLogger(PaymentBankConfigDao.class.getName());

    private static final String GET_LIST_BANK = "{call onefin.pkg_bank_config.get_list_bank_config_ita(?,?,?)}";
    private static final String GET_LIST_MERCHANT_WITH_ADVANCE_TYPE = "{call onefin.pkg_bank_config.get_list_merchant_with_advance_type(?,?,?,?,?)}";
    private static final String GET_LIST_MERCHANT_LOG_WITH_ADVANCE_TYPE = "{call onefin.pkg_fee_data_ita_log.g_fee_ita_log_with_advance_type(?,?,?,?,?)}";

    public static List<PaymentBankConfigDto> getListBank() throws SQLException { 
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<PaymentBankConfigDto> result = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_BANK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB ONEFIN get list bank : " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(convertPaymentBank(rs));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<PaymentFeeDataItaDto> getListMerchantWithAdvanceType(int bankConfigId, String advanceType) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<PaymentFeeDataItaDto> result = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_MERCHANT_WITH_ADVANCE_TYPE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankConfigId);
            cs.setString(5, advanceType);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB ONEFIN get list merchant with advance type : " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(convertPaymentFeeDataIta(rs));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<PaymentFeeDataItaDto> getListMerchantLogWithAdvanceType(int bankConfigId, String advanceType) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<PaymentFeeDataItaDto> result = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_MERCHANT_LOG_WITH_ADVANCE_TYPE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankConfigId);
            cs.setString(5, advanceType);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB ONEFIN get list merchant with advance type : " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(convertPaymentFeeDataItaLog(rs));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static PaymentFeeDataItaDto convertPaymentFeeDataIta(ResultSet rs) throws SQLException {
        PaymentFeeDataItaDto result = new PaymentFeeDataItaDto();
        result.setId(rs.getInt("N_ID"));
        result.setMerchantId(rs.getString("S_MERCHANT_ID"));
        result.setBankConfigId(rs.getInt("N_BANK_CONFIG_ID"));
        result.setN3(rs.getDouble("N_3"));
        result.setN6(rs.getDouble("N_6"));
        result.setN9(rs.getDouble("N_9"));
        result.setN12(rs.getDouble("N_12"));
        result.setN15(rs.getDouble("N_15"));
        result.setN18(rs.getDouble("N_18"));
        result.setN24(rs.getDouble("N_24"));
        result.setN36(rs.getDouble("N_36"));
        result.setApprovedAt(rs.getString("D_APPROVE"));
        result.setCreatedAt(rs.getString("D_CREATE"));
        result.setModifiedAt(rs.getString("D_UPDATE"));
        result.setActive(rs.getString("S_ACTIVE"));
        result.setState(rs.getString("S_STATE"));
        return result;
    }

    private static PaymentFeeDataItaDto convertPaymentFeeDataItaLog(ResultSet rs) throws SQLException {
        PaymentFeeDataItaDto result = new PaymentFeeDataItaDto();
        result.setId(rs.getInt("N_PARENT_ID"));
        result.setMerchantId(rs.getString("S_MERCHANT_ID"));
        result.setBankConfigId(rs.getInt("N_BANK_CONFIG_ID"));
        result.setN3(rs.getDouble("N_3"));
        result.setN6(rs.getDouble("N_6"));
        result.setN9(rs.getDouble("N_9"));
        result.setN12(rs.getDouble("N_12"));
        result.setN15(rs.getDouble("N_15"));
        result.setN18(rs.getDouble("N_18"));
        result.setN24(rs.getDouble("N_24"));
        result.setN36(rs.getDouble("N_36"));
        result.setApprovedAt(rs.getString("D_APPROVE"));
        result.setCreatedAt(rs.getString("D_CREATE"));
        result.setModifiedAt(rs.getString("D_UPDATE"));
        result.setActive(rs.getString("S_ACTIVE"));
        result.setState(rs.getString("S_STATE"));
        return result;
    }

    private static PaymentBankConfigDto convertPaymentBank(ResultSet rs) throws SQLException {
        PaymentBankConfigDto result = new PaymentBankConfigDto();
        result.setId(rs.getInt("N_ID"));
        result.setsId(rs.getString("S_ID"));
        result.setShortName(rs.getString("S_SHORT_NAME"));
        result.setFullName(rs.getString("S_FULL_NAME"));
        result.setPayChannel(rs.getString("S_PAYCHANNEL"));
        return result;
    }
}
