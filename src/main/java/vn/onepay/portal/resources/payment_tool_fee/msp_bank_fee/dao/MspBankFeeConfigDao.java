package vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.dao;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.dto.MspBankDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MspBankFeeConfigDao extends Db {
    private static final Logger _LOGGER = Logger.getLogger(MspBankFeeConfigDao.class.getName());

    private static final String GET_LIST_BANK = "{call MSP.pkg_bank_merchant_fee_config.g_list_bank(?,?,?)}";
    private static final String GET_INSTALLMENT_MERCHANT = "{call MSP.pkg_bank_merchant_fee_config.dl_installment_merchant(?,?,?,?)}";

    private static final String GET_INSTALLMENT_MERCHANT_LOG = "{call MSP.pkg_installment_merchant_log.get_list_ita_merchant_log(?,?,?,?,?)}";

    public static List<MspBankDto> getListBank() throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<MspBankDto> result = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_BANK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB MSP get list bank : " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(convertMspBank(rs));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<JsonObject> getListInstallmentMerchantLog(int bankMerchantFeeConfigId, String status) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<JsonObject> result = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_INSTALLMENT_MERCHANT_LOG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankMerchantFeeConfigId);
            cs.setString(5, status);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB MSP get installment merchant : " + error);
            } else {
                while (rs != null && rs.next()) {
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.put("id", rs.getInt("N_ID"));
                    jsonObject.put("installmentId", rs.getString("S_INSTALLMENT_ID"));
                    jsonObject.put("merchantId", rs.getString("S_MERCHANT_ID"));
                    jsonObject.put("createdAt", rs.getString("D_CREATE"));
                    jsonObject.put("modifiedAt", rs.getString("D_UPDATE"));
                    jsonObject.put("state", rs.getString("S_STATE"));
                    jsonObject.put("times", rs.getString("S_TIMES"));
                    jsonObject.put("cancelDays", rs.getInt("N_CANCEL_DAYS"));
                    jsonObject.put("minAmount", rs.getDouble("N_MIN_AMOUNT"));
                    jsonObject.put("maxAmount", rs.getDouble("N_MAX_AMOUNT"));
                    jsonObject.put("itaFee", rs.getDouble("N_ITA_FEE"));
                    jsonObject.put("bankMerchantId", rs.getString("S_BANK_MERCHANT_ID"));
                    jsonObject.put("feesOld", rs.getString("S_FEES_OLD"));
                    jsonObject.put("feeNew", rs.getString("S_FEES_NEW"));
                    jsonObject.put("listFee", rs.getString("S_FEES_NEW"));
                    jsonObject.put("startDate", rs.getString("D_START"));
                    jsonObject.put("startDate", rs.getString("D_START"));
                    result.add(jsonObject);
                }
            }
        } catch (SQLException e) {
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<JsonObject> getListInstallmentMerchant(String installmentId) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<JsonObject> result = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_INSTALLMENT_MERCHANT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, installmentId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB MSP get installment merchant : " + error);
            } else {
                while (rs != null && rs.next()) {
                    while (rs != null && rs.next()) {
                        JsonObject data = convertInstallmentMerchant(rs);
                        result.add(data);
                    }
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static JsonObject convertInstallmentMerchant(ResultSet rs) throws SQLException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.put("installmentId", Util.getColumnString(rs, "S_INSTALLMENT_ID"));
        jsonObject.put("merchantId", Util.getColumnString(rs, "S_MERCHANT_ID"));
        jsonObject.put("createdAt", Util.getColumnString(rs, "D_CREATE"));
        jsonObject.put("modifiedAt", Util.getColumnString(rs, "D_UPDATE"));
        jsonObject.put("state", Util.getColumnString(rs, "S_STATE"));
        jsonObject.put("description", Util.getColumnString(rs, "S_DESC"));
        jsonObject.put("times", Util.getColumnString(rs, "S_TIMES"));
        jsonObject.put("cancelDays", Util.getColumnInteger(rs, "N_CANCEL_DAYS"));
        jsonObject.put("minAmount", Util.getColumnDouble(rs, "N_MIN_AMOUNT"));
        jsonObject.put("maxAmount", Util.getColumnDouble(rs, "N_MAX_AMOUNT"));
        jsonObject.put("itaFee", Util.getColumnDouble(rs, "N_ITA_FEE"));
        jsonObject.put("listFee", Util.getColumnString(rs, "S_FEES"));
        JsonArray fees;
        if (jsonObject.getString("listFee") != null) {
            fees = new JsonArray(rs.getString("S_FEES"));
        } else {
            fees = new JsonArray();
        }
        jsonObject.put("fees", fees);
        return jsonObject;
    }

    private static MspBankDto convertMspBank(ResultSet resultSet) throws SQLException {
        MspBankDto result = new MspBankDto();
        result.setId(resultSet.getString("S_ID"));
        result.setName(resultSet.getString("S_NAME"));
        result.setSwiftCode(resultSet.getString("S_SWIFT_CODE"));
        return result;
    }
}
