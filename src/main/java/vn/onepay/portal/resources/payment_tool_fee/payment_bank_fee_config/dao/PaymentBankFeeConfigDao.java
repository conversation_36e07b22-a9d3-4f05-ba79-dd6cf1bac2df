package vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee.dao.PaymentBankMerchantFeeConfigDao;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dto.PaymentBankFeeConfigCommand;
import vn.onepay.portal.resources.payment_tool_fee.payment_bank_fee_config.dto.PaymentBankFeeConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentBankFeeConfigDao extends Db {
    private static final Logger _LOGGER = Logger.getLogger(PaymentBankFeeConfigDao.class.getName());

    private static final String PUSH_BANK_FEE_CONFIG = "{call onefin.pkg_bank_fee_config.push_data(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DELETE_BANK_FEE_CONFIG = "{call onefin.pkg_bank_fee_config.delete_by_bank_merchant_id(?,?,?)}";
    private static final String GET_LIST_BANK_FEE_CONFIG = "{call onefin.pkg_bank_fee_config.get_list_data_fee(?,?,?,?,?)}";


    public static Map<String, Object> getListBankFeeConfig(int bankMerchantFeeConfigId, String feeType)
            throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        List<PaymentBankFeeConfigDto> data = new ArrayList<>();
        String error = "";
        int nError = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_BANK_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankMerchantFeeConfigId);
            cs.setString(5, feeType);
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 500) {
                throw new SQLException("DB push bank merchant fee config : " + error);
            } else {
                while (rs != null && rs.next()) {
                    data.add(convertData(rs));
                }
            }
            result.put("status", nError);
            result.put("message", error);
            result.put("list", data);
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    public static Map<String, Object> pushBankFeeConfig(int bankMerchantId, String feeType, String bankId,
                                                        String bankName, String status, String actor,
                                                        String startDate, PaymentBankFeeConfigCommand command)
            throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        int ntotal = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(PUSH_BANK_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, bankMerchantId);
            cs.setString(4, command.getType());
            cs.setString(5, feeType);
            cs.setString(6, bankId);
            cs.setString(7, bankName);
            cs.setString(8, command.getS3());
            cs.setString(9, command.getS6());
            cs.setString(10, command.getS9());
            cs.setString(11, command.getS12());
            cs.setString(12, command.getS15());
            cs.setString(13, command.getS18());
            cs.setString(14, command.getS24());
            cs.setString(15, command.getS36());
            cs.setString(16, status);
            cs.setString(17, actor);
            cs.setString(18, command.getPosition());
            cs.setString(19, startDate);
            cs.execute();
            error = cs.getString(2);
            nError = cs.getInt(1);
            if (nError == 500) {
                throw new SQLException("DB push bank merchant fee config : " + error);
            }
            result.put("status", nError);
            result.put("message", error);
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> delete(int bankMerchantFeeConfigId)
            throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        int ntotal = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(DELETE_BANK_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, bankMerchantFeeConfigId);
            cs.execute();
            error = cs.getString(2);
            nError = cs.getInt(1);
            if (nError == 500) {
                throw new SQLException("DB delete bank merchant fee config : " + error);
            }
            result.put("status", nError);
            result.put("message", error);
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static PaymentBankFeeConfigDto convertData(ResultSet rs) throws SQLException {
        PaymentBankFeeConfigDto result = new PaymentBankFeeConfigDto();
        result.setId(rs.getInt("N_ID"));
        result.setBankMerchantId(rs.getInt("N_BANK_MERCHANT_FEE_ID"));
        result.setType(rs.getString("S_TYPE"));
        result.setFeeType(rs.getString("S_FEE_TYPE"));
        result.setBankId(rs.getString("S_BANK_ID"));
        result.setBankName(rs.getString("S_BANK_NAME"));
        result.setS3(rs.getString("S_3"));
        result.setS6(rs.getString("S_6"));
        result.setS9(rs.getString("S_9"));
        result.setS12(rs.getString("S_12"));
        result.setS15(rs.getString("S_15"));
        result.setS18(rs.getString("S_18"));
        result.setS24(rs.getString("S_24"));
        result.setS36(rs.getString("S_36"));
        result.setStatus(rs.getString("S_STATUS"));
        result.setActive(rs.getString("S_ACTIVE"));
        result.setUpdatedAt(rs.getString("D_UPDATE"));
        result.setUpdatedBy(rs.getString("S_UPDATE"));
        result.setPosition(rs.getString("S_POSITION"));
        result.setStartDate(rs.getString("D_START"));
        return result;
    }
}
