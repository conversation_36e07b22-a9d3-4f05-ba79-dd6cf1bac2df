package vn.onepay.portal.resources.payment_tool_fee.msp_bank_fee.dto;

import java.io.Serializable;
import java.util.List;

public class MspInstallmentMerchant implements Serializable {
    private String installmentId;
    private String merchantId;
    private String createdAt;
    private String modifiedAt;
    private String state;
    private String description;
    private int cancelDays;
    private String times;
    private List<MspInstallmentMerchantFee> fees;

    private String listFee;
    private Double minAmount;
    private Double maxAmount;

    public Double getMinAmount() {
        return minAmount;
    }

    private Double itaFee;
    private String bankMerchantId;

    public String getListFee() {
        return listFee;
    }

    public void setListFee(String listFee) {
        this.listFee = listFee;
    }

    public MspInstallmentMerchant() {
    }

    public String getInstallmentId() {
        return installmentId;
    }

    public void setInstallmentId(String installmentId) {
        this.installmentId = installmentId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMinAmount(Double minAmount) {
        this.minAmount = minAmount;
    }

    public Double getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(Double maxAmount) {
        this.maxAmount = maxAmount;
    }

    public Double getItaFee() {
        return itaFee;
    }

    public void setItaFee(Double itaFee) {
        this.itaFee = itaFee;
    }

    public List<MspInstallmentMerchantFee> getFees() {
        return fees;
    }

    public void setFees(List<MspInstallmentMerchantFee> fees) {
        this.fees = fees;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getModifiedAt() {
        return modifiedAt;
    }

    public void setModifiedAt(String modifiedAt) {
        this.modifiedAt = modifiedAt;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCancelDays() {
        return cancelDays;
    }

    public void setCancelDays(int cancelDays) {
        this.cancelDays = cancelDays;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }


    public String getBankMerchantId() {
        return bankMerchantId;
    }

    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }

    public static class MspInstallmentMerchantFee implements Serializable {
        public MspInstallmentMerchantFee() {
        }

        private int t;
        private String c;
        private double ff;
        private double pf;
        private double mina;
        private double maxa;
        private String collect;

        public int getT() {
            return t;
        }

        public void setT(int t) {
            this.t = t;
        }

        public String getC() {
            return c;
        }

        public void setC(String c) {
            this.c = c;
        }

        public double getFf() {
            return ff;
        }

        public void setFf(double ff) {
            this.ff = ff;
        }

        public double getPf() {
            return pf;
        }

        public void setPf(double pf) {
            this.pf = pf;
        }

        public double getMina() {
            return mina;
        }

        public void setMina(double mina) {
            this.mina = mina;
        }

        public double getMaxa() {
            return maxa;
        }

        public void setMaxa(double maxa) {
            this.maxa = maxa;
        }

        public String getCollect() {
            return collect;
        }

        public void setCollect(String collect) {
            this.collect = collect;
        }
    }
}
