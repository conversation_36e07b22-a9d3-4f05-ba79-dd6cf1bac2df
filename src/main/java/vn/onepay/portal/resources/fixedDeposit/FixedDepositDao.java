package vn.onepay.portal.resources.fixedDeposit;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.fixedDeposit.dto.ContractDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;
import vn.onepay.portal.resources.fixedDeposit.dto.PartnerDto;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class FixedDepositDao extends Db implements IConstants {

    private static SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

    public static FixedDepositDto get(String contractCode, String fd) throws Exception {
        Exception exception = null;
        FixedDepositDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_get(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, contractCode);
            cs.setString(5, fd);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bind(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return result;
    }

    public static Integer approve(Long id) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_approve(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setLong(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
            result = nError;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return result;
    }

    public static Integer ignore(Long id) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_ignore(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setLong(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
            result = nError;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return result;
    }

    public static Integer delete(Long id) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_delete(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setLong(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
            result = nError;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return result;
    }

    // Exception exception = null;
    public static List<FixedDepositDto> search(FixedDepositQueryDto query) throws Exception {
        List<FixedDepositDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_list(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, query.getPage());
            cs.setInt(5, query.getPageSize());
            cs.setString(6, query.getFromDate());
            cs.setString(7, query.getToDate());
            cs.setString(8, query.getFd());
            cs.setString(9, query.getContractCode());
            cs.setString(10, query.getBusinessRegName());
            cs.setString(11, query.getMerchantId());
            cs.setString(12, query.getAdvAccount());
            cs.setString(13, query.getBank());
            cs.setString(14, query.getState());
            cs.setString(15, query.getSource());
            cs.setString(16, query.getId());
            cs.setString(17, query.getRollPrincipal());
            cs.setString(18, query.getTaxNumber());
            cs.setInt(19, query.getMaturityPeriod());
            cs.setString(20, query.getApproval());
            cs.setString(21, query.getIdFd());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(bind(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            // exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        // if (exception != null)
        return list;
    }

    public static BaseList<FixedDepositDto> getByids(FixedDepositQueryDto query) throws Exception {
        Exception exception = null;
        BaseList<FixedDepositDto> result = new BaseList<>();
        List<FixedDepositDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        logger.severe("DB getByids input: " + query.getIds());
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_by_ids(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, query.getIds());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB fixed_deposit_by_ids: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(bind(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    public static Integer total(FixedDepositQueryDto query) throws Exception {
        Exception exception = null;
        Integer total = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_total(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, query.getFromDate());
            cs.setString(5, query.getToDate());
            cs.setString(6, query.getFd());
            cs.setString(7, query.getContractCode());
            cs.setString(8, query.getBusinessRegName());
            cs.setString(9, query.getMerchantId());
            cs.setString(10, query.getAdvAccount());
            cs.setString(11, query.getBank());
            cs.setString(12, query.getState());
            cs.setString(13, query.getSource());
            cs.setString(14, query.getId());
            cs.setString(15, query.getRollPrincipal());
            cs.setString(16, query.getTaxNumber());
            cs.setInt(17, query.getMaturityPeriod());
            cs.setString(18, query.getApproval());
            cs.setString(19, query.getIdFd());
            logger.info("getFromDate: " + query.getFromDate() + " " +"getToDate: " + query.getToDate() + " "
            +"getFd: " + query.getFd() + " " +"getContractCode: " + query.getContractCode() + " "
            +"getBusinessRegName: " + query.getBusinessRegName() + " "+"getMerchantId: " + query.getMerchantId() + " "
            +"getAdvAccount: " + query.getAdvAccount() + " "+"getBank: " + query.getBank() + " "
            +"getState: " + query.getState() + " " +"getSource: " + query.getSource() + " "
            +"getId: " + query.getId() + " " +"getRollPrincipal: " + query.getRollPrincipal() + " "
            +"getTaxNumber: " + query.getTaxNumber() + " "+"getMaturityPeriod: " + query.getMaturityPeriod() + " "
            +"getApproval: " + query.getApproval() + " "+"getIdFd: " + query.getIdFd() + " ");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            total = cs.getInt(1);
            logger.info("total: " + total + " nError: " +nError + " error: " +error);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static String totalBalance(FixedDepositQueryDto query) throws Exception {
        Exception exception = null;
        String total = "";
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fd_total_balance(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, query.getFromDate());
            cs.setString(5, query.getToDate());
            cs.setString(6, query.getFd());
            cs.setString(7, query.getContractCode());
            cs.setString(8, query.getBusinessRegName());
            cs.setString(9, query.getMerchantId());
            cs.setString(10, query.getAdvAccount());
            cs.setString(11, query.getBank());
            cs.setString(12, query.getState());
            cs.setString(13, query.getSource());
            cs.setString(14, query.getId());
            cs.setString(15, query.getRollPrincipal());
            cs.setString(16, query.getTaxNumber());
            cs.setInt(17, query.getMaturityPeriod());
            cs.setString(18, query.getApproval());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            total = cs.getString(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static Integer insert(FixedDepositDto fixedDepositDto) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_insert(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, fixedDepositDto.getContractCode());
            cs.setString(4, fixedDepositDto.getFd());
            cs.setString(5, fixedDepositDto.getCurrency());
            cs.setDouble(6, fixedDepositDto.getBalance());
            cs.setString(7, fixedDepositDto.getOpenDate());
            cs.setString(8, fixedDepositDto.getCloseDate());
            cs.setString(9, fixedDepositDto.getSignedDate());
            cs.setString(10, fixedDepositDto.getMaturityDate());
            cs.setInt(11, fixedDepositDto.getMaturityPeriod());
            cs.setFloat(12, fixedDepositDto.getInterestRate());
            cs.setString(13, fixedDepositDto.getBank());
            cs.setString(14, fixedDepositDto.getState());
            cs.setString(15, fixedDepositDto.getNote());
            cs.setString(16, fixedDepositDto.getTaxNumber());
            cs.setString(17, fixedDepositDto.getSource());
            cs.setString(18, fixedDepositDto.getRollPrincipal());
            cs.setString(19, fixedDepositDto.getAccSavingNum());
            cs.setString(20, fixedDepositDto.getAccSavingName());
            cs.setString(21, fixedDepositDto.getAccSavingAddress());
            cs.setString(22, fixedDepositDto.getAccSavingBank());
            cs.setString(23, fixedDepositDto.getAccSavingBranch());
            cs.setString(24, fixedDepositDto.getAccRefNum());
            cs.setString(25, fixedDepositDto.getAccRefName());
            cs.setString(26, fixedDepositDto.getAccRefAddress());
            cs.setString(27, fixedDepositDto.getAccRefBank());
            cs.setString(28, fixedDepositDto.getAccRefBranch());
            cs.setString(29, fixedDepositDto.getMerchantName());
            cs.setString(30, fixedDepositDto.getIdFd());
            cs.setString(31, fixedDepositDto.getPartnerId());
            cs.setString(32, null);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB fixed_deposit_insert error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer update(FixedDepositDto fixedDepositDto) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_update(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, Long.parseLong(fixedDepositDto.getId()));
            cs.setString(4, fixedDepositDto.getContractCode());
            cs.setString(5, fixedDepositDto.getFd());
            cs.setString(6, fixedDepositDto.getCurrency());
            cs.setDouble(7, fixedDepositDto.getBalance());
            cs.setString(8, fixedDepositDto.getOpenDate());
            cs.setString(9, fixedDepositDto.getCloseDate());
            cs.setString(10, fixedDepositDto.getSignedDate());
            cs.setString(11, fixedDepositDto.getMaturityDate());
            cs.setInt(12, fixedDepositDto.getMaturityPeriod());
            cs.setFloat(13, fixedDepositDto.getInterestRate());
            cs.setString(14, fixedDepositDto.getBank());
            cs.setString(15, fixedDepositDto.getState());
            cs.setString(16, fixedDepositDto.getNote());
            cs.setString(17, fixedDepositDto.getTaxNumber());
            cs.setString(18, fixedDepositDto.getSource());
            cs.setString(19, fixedDepositDto.getRollPrincipal());
            cs.setString(20, fixedDepositDto.getAccSavingNum());
            cs.setString(21, fixedDepositDto.getAccSavingName());
            cs.setString(22, fixedDepositDto.getAccSavingAddress());
            cs.setString(23, fixedDepositDto.getAccSavingBank());
            cs.setString(24, fixedDepositDto.getAccSavingBranch());
            cs.setString(25, fixedDepositDto.getAccRefNum());
            cs.setString(26, fixedDepositDto.getAccRefName());
            cs.setString(27, fixedDepositDto.getAccRefAddress());
            cs.setString(28, fixedDepositDto.getAccRefBank());
            cs.setString(29, fixedDepositDto.getAccRefBranch());
            cs.setString(30, fixedDepositDto.getMerchantName());
            cs.setString(31, fixedDepositDto.getApproval());
            cs.setString(32, fixedDepositDto.getParentId());
            cs.setString(33, fixedDepositDto.getIdFd());
            cs.setString(34, fixedDepositDto.getPartnerId());
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB ONEFIN.PKG_FIXED_DEPOSIT.fixed_deposit_update error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer updateInterestRate(FixedDepositQueryDto depositQueryDto) throws Exception {
        Exception exception = null;
        Integer result = 1;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        logger.severe("DB update_interest_rate input: " + depositQueryDto.getIds());
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.update_interest_rate(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, depositQueryDto.getIds());
            cs.setFloat(4, depositQueryDto.getInterestRate());
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                result = 0;
                logger.severe("DB update_interest_rate error: " + error);
                throw new Exception("" + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ContractDto> getContract(String contractCode) throws Exception {
        Exception exception = null;
        List<ContractDto> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.CONTRACT_GET(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, contractCode);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                result = new ArrayList<>();
                while (rs != null && rs.next()) {
                    ContractDto contractDto = new ContractDto();
                    contractDto.setId(rs.getInt("N_ID"));
                    contractDto.setBusinessRegName(rs.getString("S_TEN_DKKD"));
                    contractDto.setType(rs.getString("S_TYPE"));
                    contractDto.setGuaranteeType(rs.getString("S_GUARANTEE_TYPE"));
                    contractDto.setGuaranteeDetail(rs.getString("S_GUARANTEE_DETAIL"));
                    contractDto.setStatus(rs.getString("S_STATUS"));
                    result.add(contractDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map getContract(int id) throws Exception {
        Map map = new HashMap();
        Exception exception = null;
        List<ContractDto> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.GET_CONTRACT_BY_ID(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                result = new ArrayList<>();
                while (rs != null && rs.next()) {
                    ContractDto contractDto = new ContractDto();
                    contractDto.setId(rs.getInt("N_ID"));
                    contractDto.setContractCode(rs.getString("S_CONTRACT_CODE"));
                    result.add(contractDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        map.put("list", result);
        return map;
    }

    public static Map getBalance(int id) throws Exception {
        Map map = new HashMap();
        Exception exception = null;
        List<ContractDto> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String khoanDamBao = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.GET_BALANCE_BY_CONTRACT(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB load Acceptance error: " + error);
                throw new Exception("" + error);
            } else {
                while (rs != null && rs.next()) {
                    String json = rs.getString("JSON_CONTRACT_DETAIL");
                    JsonParser parser = new JsonParser();
                    JsonObject jsonObject = (JsonObject) parser.parse(json);
                    khoanDamBao = jsonObject.get("khoanDamBaoInput") != null ? jsonObject.get("khoanDamBaoInput").getAsString()
                    : jsonObject.get("inputKyQuyKhac") != null ? jsonObject.get("inputKyQuyKhac").getAsString().replaceAll("[A-Za-z\\.\\,]", "") : null;
                    if(khoanDamBao != null) break;
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        map.put("balance", khoanDamBao != null ? khoanDamBao : "");
        return map;
    }

    public static List getPartner() throws Exception {
        Exception exception = null;
        List<PartnerDto> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEFIN.PKG_FIXED_DEPOSIT.GET_PARTNER(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("DB get partner error: " + error);
                throw new Exception("" + error);
            } else {
                result = new ArrayList<>();
                while (rs != null && rs.next()) {
                    PartnerDto partnerDto = new PartnerDto();
                    partnerDto.setPartnerId(rs.getInt("N_ID"));
                    partnerDto.setShortName(rs.getString("S_PARTNER_NAME"));
                    result.add(partnerDto);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static String handleDuplicateData(String inputData){
        List<String> dataList = null;
        String dataArray[] = null;
        String outputData = null;
        if(inputData != null){
            dataArray = inputData.split(",");
            if(dataArray != null && dataArray.length > 0){
                dataList = Arrays.asList(dataArray);
                dataList = dataList.stream().distinct().collect(Collectors.toList());
                outputData = dataList.stream().collect(Collectors.joining(","));
            }
        }
        if(outputData == null) outputData = "";
        return outputData;
    }

    public static FixedDepositDto bind(ResultSet rs) throws SQLException {
        Timestamp openDate = rs.getObject("D_OPEN") != null ? rs.getTimestamp("D_OPEN") : null;
        Timestamp maturityDate = rs.getObject("D_MATURITY") != null ? rs.getTimestamp("D_MATURITY") : null;
        Timestamp closeDate = rs.getObject("D_BREAK_DATE") != null ? rs.getTimestamp("D_BREAK_DATE") : null;
        long days = 0;
        if (openDate != null && maturityDate != null) {
            days = Duration.between(openDate.toLocalDateTime().toLocalDate().atStartOfDay(), maturityDate.toLocalDateTime().toLocalDate().atStartOfDay()).toDays();
        }
        FixedDepositDto dto = new FixedDepositDto();
        dto.setId(rs.getString("N_ID"));
        dto.setParentId(rs.getString("N_PARENT_ID"));
        dto.setApproval(rs.getString("S_APPROVAL"));
        dto.setContractCode(rs.getString("S_CONTRACT_CODE"));
        dto.setFd(rs.getString("S_FD"));
        dto.setBank(rs.getString("S_BANK"));
        dto.setCurrency(rs.getString("S_CURRENCY"));
        dto.setBalance(rs.getObject("N_BALANCE") != null ? rs.getDouble("N_BALANCE"): 0);
        dto.setOpenDate(openDate != null ? sdf.format(openDate) : "");
        dto.setSignedDate(rs.getObject("D_SIGNED") != null ? sdf.format(rs.getTimestamp("D_SIGNED")) : "");
        dto.setMaturityDate(maturityDate != null ? sdf.format(maturityDate) : "");
        dto.setCloseDate(closeDate != null ? sdf.format(closeDate) : "");
        dto.setMaturityPeriod(rs.getInt("N_MATURITY_PERIOD"));
        dto.setInterestRate(rs.getObject("N_INTEREST_RATE") != null ? rs.getFloat("N_INTEREST_RATE") : 0);
        dto.setState(rs.getString("S_STATE"));
        dto.setPartnerId(rs.getString("N_PARTNER_ID"));
        dto.setBusinessRegName(rs.getString("S_TEN_DKKD"));
        dto.setMerchantIds(handleDuplicateData(rs.getString("S_MERCHANT_IDS")));
        dto.setMerchantName(rs.getString("S_MERCHANT_NAME") != null ? rs.getString("S_MERCHANT_NAME") : "");
        dto.setAdvAccounts(handleDuplicateData(rs.getString("S_ADV_ACCOUNTS")));
        dto.setNote(rs.getString("S_NOTE"));
        dto.setTaxNumber(rs.getString("S_TAX_NUMBER") != null ? rs.getString("S_TAX_NUMBER"): "");
        dto.setSource(rs.getString("S_SOURCE") != null ? rs.getString("S_SOURCE"): "");
        dto.setRollPrincipal(rs.getString("S_ROLL_PRINCIPAL") != null ? rs.getString("S_ROLL_PRINCIPAL"): "");
        dto.setAccSavingNum(rs.getString("S_ACC_SAVING_NUM") != null ? rs.getString("S_ACC_SAVING_NUM"): "");
        dto.setAccSavingName(rs.getString("S_ACC_SAVING_NAME") != null ? rs.getString("S_ACC_SAVING_NAME") : "");
        dto.setAccSavingAddress(rs.getString("S_ACC_SAVING_ADDRESS") != null ? rs.getString("S_ACC_SAVING_ADDRESS") : "");
        dto.setAccSavingBank(rs.getString("S_ACC_SAVING_BANK") != null && rs.getString("S_ACC_SAVING_BANK").length() > 0 ? rs.getString("S_ACC_SAVING_BANK") : "");
        dto.setAccSavingBranch(rs.getString("S_ACC_SAVING_BRANCH") != null ? rs.getString("S_ACC_SAVING_BRANCH") : "");
        dto.setAccRefNum(rs.getString("S_ACC_REF_NUM") != null ? rs.getString("S_ACC_REF_NUM"): "");
        dto.setAccRefName(rs.getString("S_ACC_REF_NAME") != null ? rs.getString("S_ACC_REF_NAME") : "");
        dto.setAccRefAddress(rs.getString("S_ACC_REF_ADDRESS") != null ? rs.getString("S_ACC_REF_ADDRESS") : "");
        dto.setAccRefBank(rs.getString("S_ACC_REF_BANK") != null ? rs.getString("S_ACC_REF_BANK") : "");
        dto.setAccRefBranch(rs.getString("S_ACC_REF_BRANCH") != null ? rs.getString("S_ACC_REF_BRANCH") : "");
        dto.setBalanceAfterMaturity(dto.getBalance() + dto.getBalance() * dto.getInterestRate() * days / (365 * 100));
        dto.setIdFd(rs.getString("N_ID_FD"));
        dto.setPartnerName(rs.getString("S_PARTNER_NAME"));
        dto.setEmail(rs.getString("S_EMAIL") != null ? rs.getString("S_EMAIL") : "");
        dto.setContractId(rs.getString("N_CONTRACT_ID") != null ? rs.getString("N_CONTRACT_ID") : "");
        dto.setShortName(rs.getString("S_SHORT_NAME") != null ? rs.getString("S_SHORT_NAME") : "");
        return dto;
    }

}
