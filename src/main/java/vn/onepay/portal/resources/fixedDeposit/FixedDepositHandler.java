package vn.onepay.portal.resources.fixedDeposit;

import com.google.gson.Gson;
import org.apache.axis.encoding.ser.CalendarDeserializerFactory;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fixedDeposit.dto.ContractDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class FixedDepositHandler implements IConstants {

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> response = new HashMap<>();
                FixedDepositQueryDto query = new FixedDepositQueryDto();
                query.setPage(request.getParam("page") == null ? 0 : Integer.parseInt(request.getParam("page")));
                query.setPageSize(request.getParam("pageSize") == null ? 0 : Integer.parseInt(request.getParam("pageSize")));
                query.setFromDate(request.getParam("fromDate") == null ? BLANK : request.getParam("fromDate"));
                query.setToDate(request.getParam("toDate") == null ? BLANK : request.getParam("toDate"));
                query.setContractCode(request.getParam("contractCode") == null ? BLANK : request.getParam("contractCode"));
                query.setFd(request.getParam("fd") == null ? BLANK : request.getParam("fd"));
                query.setBank(request.getParam("bank") == null ? BLANK : request.getParam("bank"));
                query.setBusinessRegName(request.getParam("businessRegName") == null ? BLANK : request.getParam("businessRegName"));
                query.setAdvAccount(request.getParam("advAccount") == null ? BLANK : request.getParam("advAccount"));
                query.setMerchantId(request.getParam("merchantId") == null ? BLANK : request.getParam("merchantId"));
                query.setState(request.getParam("state") == null ? BLANK : request.getParam("state"));
                query.setSource(request.getParam("source") == null ? BLANK : request.getParam("source"));
                query.setId(request.getParam("id") == null ? BLANK : request.getParam("id"));
                query.setIdFd(request.getParam("idFd") == null ? BLANK : request.getParam("idFd"));
                query.setRollPrincipal(request.getParam("rollPrincipal") == null ? BLANK : request.getParam("rollPrincipal"));
                query.setTaxNumber(request.getParam("taxNumber") == null ? BLANK : request.getParam("taxNumber"));
                query.setMaturityPeriod(request.getParam("maturityPeriod") == null ? 0 : Integer.parseInt((request.getParam("maturityPeriod"))));
                query.setApproval(request.getParam("approval") == null ? "WAITING_FOR_APPROVAL" : request.getParam("approval"));
                List<FixedDepositDto> list = FixedDepositDao.search(query);
                response.put("data_list", list);
                response.put("total_quantity", FixedDepositDao.total(query));
                response.put("total_balance", list.isEmpty() ? "0" : FixedDepositDao.totalBalance(query));
                response.put("partner_list", FixedDepositDao.getPartner());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "search: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insert(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                FixedDepositDto fixedDepositDto = gson.fromJson(ctx.getBodyAsString(), FixedDepositDto.class);
                List<ContractDto> contracts = FixedDepositDao.getContract(fixedDepositDto.getContractCode());
                if (contracts == null || contracts.size() == 0) {
                    throw IErrors.INVALID_CONTRACT;
                }

                FixedDepositDto foundFD = FixedDepositDao.get(fixedDepositDto.getContractCode().toString(), fixedDepositDto.getFd());
                if (foundFD != null) {
                    throw IErrors.FIXED_DEPOSIT_EXISTED;
                }
                fixedDepositDto.setCurrency("VND");
                fixedDepositDto.setMaturityDate(fixedDepositDto.getMaturityDate() != null ? fixedDepositDto.getMaturityDate() : "");
                FixedDepositDao.insert(fixedDepositDto);
                sendResponse(ctx, 200, fixedDepositDto);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "insert: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void update(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                FixedDepositDto fixedDepositDto = gson.fromJson(ctx.getBodyAsString(), FixedDepositDto.class);
                List<ContractDto> contracts = FixedDepositDao.getContract(fixedDepositDto.getContractCode());
                if (contracts == null || contracts.size() == 0) {
                    throw IErrors.INVALID_CONTRACT;
                }
                fixedDepositDto.setCurrency("VND");
                fixedDepositDto.setMaturityDate(fixedDepositDto.getMaturityDate() != null ? fixedDepositDto.getMaturityDate() : "");
                FixedDepositDao.update(fixedDepositDto);
                sendResponse(ctx, 200, fixedDepositDto);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "update: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateInterestRate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject dt = ctx.getBodyAsJson();
                JsonArray jsonArray = dt.getJsonArray("listData");
                Float interestRate = Float.parseFloat(dt.getString("interestRate"));
                logger.log(Level.INFO, "LIST ID: " + jsonArray.getList());
                FixedDepositQueryDto query = new FixedDepositQueryDto();
                query.setIds(String.join(",", jsonArray.getList()));
                query.setInterestRate(interestRate);
                FixedDepositDao.updateInterestRate(query);
                sendResponse(ctx, 200, query);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "updateInterestRate: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void delete(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject dt = ctx.getBodyAsJson();
                JsonArray jsonArray = dt.getJsonArray("idList");
                Map<String, Object> response = new HashMap<>();
                int countSuccess = 0;
                int countFail = 0;
                if (jsonArray.isEmpty()) {
                    throw IErrors.NO_DATA_FOUND;
                } else {
                    for (Object ob : jsonArray) {
                        Integer result = FixedDepositDao.delete(Long.parseLong(String.valueOf(ob)));
                        if (result == 200)
                            countSuccess += 1;
                        else
                            countFail += 1;
                    }
                }
                response.put("countSuccess", countSuccess);
                response.put("countFail", countFail);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "delete: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approve(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject dt = ctx.getBodyAsJson();
                JsonArray jsonArray = dt.getJsonArray("idList");
                Map<String, Object> response = new HashMap<>();
                int countSuccess = 0;
                int countFail = 0;
                if (jsonArray.isEmpty()) {
                    throw IErrors.NO_DATA_FOUND;
                } else {
                    for (Object ob : jsonArray) {
                        Integer result = FixedDepositDao.approve(Long.parseLong(String.valueOf(ob)));
                        if (result == 200)
                            countSuccess += 1;
                        else
                            countFail += 1;
                    }
                }
                response.put("countSuccess", countSuccess);
                response.put("countFail", countFail);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "approve: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void ignore(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject dt = ctx.getBodyAsJson();
                JsonArray jsonArray = dt.getJsonArray("idList");
                Map<String, Object> response = new HashMap<>();
                int countSuccess = 0;
                int countFail = 0;
                if (jsonArray.isEmpty()) {
                    throw IErrors.NO_DATA_FOUND;
                } else {
                    for (Object ob : jsonArray) {
                        Integer result = FixedDepositDao.ignore(Long.parseLong(String.valueOf(ob)));
                        if (result == 200)
                            countSuccess += 1;
                        else
                            countFail += 1;
                    }
                }
                response.put("countSuccess", countSuccess);
                response.put("countFail", countFail);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ignore: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                FixedDepositQueryDto query = gson.fromJson(ctx.getBodyAsString(), FixedDepositQueryDto.class);
                query.setPage(0);
                query.setPageSize(Integer.MAX_VALUE);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                int totalRows = FixedDepositDao.total(query);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "fixed_deposit_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", query);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("fixed_deposit");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(query));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD MERCHANT TRANSFER Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadPdf(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject dt = ctx.getBodyAsJson();
                JsonArray jsonArray = dt.getJsonArray("listData");
                String type = dt.getString("type");
                logger.log(Level.INFO, "LIST ID: " + jsonArray.getList());
                FixedDepositQueryDto query = new FixedDepositQueryDto();
                query.setIds(String.join(",", jsonArray.getList()));
                query.setType(type);
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                long date = new Date().getTime();
                String fileName = "UNC_" + type.toUpperCase() + " FD " + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", query);
                data.put("file_name", fileName);
                data.put("row", jsonArray.getList().size());
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("UNC_" + type.toUpperCase() + " FD");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(query));
                String ext = "pdf";
                fileDownloadDto.setExt(ext);
                FileDownloadDao.insert(fileDownloadDto);

                if (jsonArray.getList().size() <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD MERCHANT TRANSFER Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, FixedDepositDao.getPartner());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "approve: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int id = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                sendResponse(ctx, 200, FixedDepositDao.getContract(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "approve: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBalance(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int id = request.getParam("id") == null ? 0 : Integer.parseInt(request.getParam("id"));
                sendResponse(ctx, 200, FixedDepositDao.getBalance(id));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "approve: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void sendMail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String pattern = "###,###";
                DecimalFormat decimalFormat = new DecimalFormat(pattern);
                HttpServerRequest request = ctx.request();
                JsonObject obj = ctx.getBodyAsJson();
                JsonArray jsonArray = obj.getJsonArray("list");
                String option = obj.getString("option");
                String cc = obj.getString("cc") != null ? obj.getString("cc") : "";
                String bcc = obj.getString("bcc") != null ? obj.getString("bcc") : "";
                // loc dau ;
                cc = cc.replaceAll(";", ",");
                bcc = bcc.replaceAll(";", ",");

                String desc = obj.getString("desc");
                String subject = obj.getString("subject");
                desc.replaceAll("\n", "<br>");
                Date today = new Date();
                Calendar cal = Calendar.getInstance();
                cal.setTime(today);
                String host = Config.getString("email.host", "");
                Integer port = Config.getInteger("email.port", 25);
                String username = Config.getString("email.username", "");
                String password = Config.getString("email.password", "");
                String fromEmail = Config.getString("email.address", "");
                String toEmail = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    String descTemp = desc;
                    String subjectTemp = subject;
                    JsonObject object = jsonArray.getJsonObject(i);
                    String time = cal.get(Calendar.MONTH) + 1 + "/" + cal.get(Calendar.YEAR);
                    String partnerName = object.getString("businessRegName");
                    String contractCode = object.getString("contractCode");
                    String signedDate = object.getString("signedDate");
                    Double balance = object.getDouble("balance");
                    Integer period = object.getInteger("maturityPeriod");
                    String roll = object.getString("rollPrincipal");
                    toEmail = "";
                    if (option.equals("config")) {
                        toEmail = object.getString("email");
                    }
                    subjectTemp = subjectTemp.replaceAll("TEN_DON_VI", partnerName).replaceAll("TODAY", time);
                    descTemp = descTemp.replaceAll("TEN_DON_VI", partnerName).replaceAll("CONTRACT_CODE", contractCode).replaceAll("TODAY", time)
                            .replaceAll("SIGNED_DATE", signedDate).replaceAll("PERIOD", String.format("%02d", period))
                            .replaceAll("ROLL_PRINCIPAL", getRollPrincipal(roll)).replace("BALANCE", decimalFormat.format(balance));
                    MailUtil.sendMailFD(host, port, username, password, fromEmail, toEmail, cc, bcc, subjectTemp, descTemp);
                    // if (cc == null && bcc == null) {
                    // MailUtil.sendMail(host, port, username, password, fromEmail, toEmail, toEmail, subjectTemp,
                    // descTemp);
                    // } else if (cc != null & bcc == null) {
                    // MailUtil.sendMailWithCC(toEmail, cc, subjectTemp, descTemp, "text");
                    // } else {
                    // MailUtil.sendMailWithBCC(toEmail, cc, bcc, subjectTemp, descTemp, "text");
                    // }
                }
                Map<String, String> map = new HashMap<>();
                map.put("result", "success");
                sendResponse(ctx, 200, map);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "approve: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static String getRollPrincipal(String roll) {
        switch (roll) {
            case "BOTH":
                return "Hình thức tự động chuyển lãi và gốc sang kỳ hạn mới";
            case "PRINCIPAL":
                return "Hình thức gốc chuyển sang kỳ hạn mới";
            case "NONE":
                return "Hình thức đóng tài khoản";
            default:
                return "";
        }
    }

    private static final Logger logger = Logger.getLogger(FixedDepositHandler.class.getName());

    private static final Gson gson = new Gson();

}
