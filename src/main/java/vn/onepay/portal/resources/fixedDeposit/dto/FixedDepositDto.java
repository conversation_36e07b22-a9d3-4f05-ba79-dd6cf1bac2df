package vn.onepay.portal.resources.fixedDeposit.dto;

public class FixedDepositDto {
    private String id;
    private String contractCode;
    private String fd;
    private String idFd;
    private String parentId;
    private String approval;
    private String currency;
    private Double balance;
    private String openDate;
    private String signedDate;
    private String maturityDate;
    private String closeDate;
    private Integer maturityPeriod;
    private Float interestRate;
    private String bank;
    private String state;
    private String note;
    private String taxNumber;
    private String source;
    private String rollPrincipal;
    private String accSavingNum;
    private String accSavingName;
    private String accSavingAddress;
    private String accSavingBank;
    private String accSavingBranch;
    private String accRefNum;
    private String accRefName;
    private String accRefAddress;
    private String accRefBank;
    private String accRefBranch;

    // Extra
    private String partnerId;
    private String businessRegName;
    private String advAccounts;
    private String merchantIds;
    private String merchantName;
    private Double balanceAfterMaturity;
    private String partnerName;
    private String email;
    private String contractId;
    private String shortName;

    public String getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(String closeDate) {
        this.closeDate = closeDate;
    }

    public String getShortName() {
        return this.shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getContractId() {
        return this.contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdFd() {
        return idFd;
    }

    public void setIdFd(String idFd) {
        this.idFd = idFd;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getFd() {
        return fd;
    }

    public void setFd(String fd) {
        this.fd = fd;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public String getOpenDate() {
        return openDate;
    }

    public void setOpenDate(String openDate) {
        this.openDate = openDate;
    }

    public String getSignedDate() {
        return signedDate;
    }

    public void setSignedDate(String signedDate) {
        this.signedDate = signedDate;
    }

    public String getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(String maturityDate) {
        this.maturityDate = maturityDate;
    }

    public Integer getMaturityPeriod() {
        return maturityPeriod;
    }

    public void setMaturityPeriod(Integer maturityPeriod) {
        this.maturityPeriod = maturityPeriod;
    }

    public Float getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(Float interestRate) {
        this.interestRate = interestRate;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getBusinessRegName() {
        return businessRegName;
    }

    public void setBusinessRegName(String businessRegName) {
        this.businessRegName = businessRegName;
    }

    public String getAdvAccounts() {
        return advAccounts;
    }

    public void setAdvAccounts(String advAccounts) {
        this.advAccounts = advAccounts;
    }

    public String getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(String merchantIds) {
        this.merchantIds = merchantIds;
    }

    public Double getBalanceAfterMaturity() {
        return balanceAfterMaturity;
    }

    public void setBalanceAfterMaturity(Double balanceAfterMaturity) {
        this.balanceAfterMaturity = balanceAfterMaturity;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRollPrincipal() {
        return rollPrincipal;
    }

    public void setRollPrincipal(String rollPrincipal) {
        this.rollPrincipal = rollPrincipal;
    }

    public String getAccSavingNum() {
        return accSavingNum;
    }

    public void setAccSavingNum(String accSavingNum) {
        this.accSavingNum = accSavingNum;
    }

    public String getAccSavingName() {
        return accSavingName;
    }

    public void setAccSavingName(String accSavingName) {
        this.accSavingName = accSavingName;
    }

    public String getAccSavingBank() {
        return accSavingBank;
    }

    public void setAccSavingBank(String accSavingBank) {
        this.accSavingBank = accSavingBank;
    }

    public String getAccRefNum() {
        return accRefNum;
    }

    public void setAccRefNum(String accRefNum) {
        this.accRefNum = accRefNum;
    }

    public String getAccRefName() {
        return accRefName;
    }

    public void setAccRefName(String accRefName) {
        this.accRefName = accRefName;
    }

    public String getAccRefBank() {
        return accRefBank;
    }

    public void setAccRefBank(String accRefBank) {
        this.accRefBank = accRefBank;
    }

    /**
     * @return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }
    

    /**
     * @return String return the accSavingAddress
     */
    public String getAccSavingAddress() {
        return accSavingAddress;
    }

    /**
     * @param accSavingAddress the accSavingAddress to set
     */
    public void setAccSavingAddress(String accSavingAddress) {
        this.accSavingAddress = accSavingAddress;
    }

    /**
     * @return String return the accRefAddress
     */
    public String getAccRefAddress() {
        return accRefAddress;
    }

    /**
     * @param accRefAddress the accRefAddress to set
     */
    public void setAccRefAddress(String accRefAddress) {
        this.accRefAddress = accRefAddress;
    }


    /**
     * @return String return the accSavingBranch
     */
    public String getAccSavingBranch() {
        return accSavingBranch;
    }

    /**
     * @param accSavingBranch the accSavingBranch to set
     */
    public void setAccSavingBranch(String accSavingBranch) {
        this.accSavingBranch = accSavingBranch;
    }

    /**
     * @return String return the accRefBranch
     */
    public String getAccRefBranch() {
        return accRefBranch;
    }

    /**
     * @param accRefBranch the accRefBranch to set
     */
    public void setAccRefBranch(String accRefBranch) {
        this.accRefBranch = accRefBranch;
    }


    /**
     * @return String return the merchantName
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * @param merchantName the merchantName to set
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * @return the parentId
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * @param parentId the parentId to set
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * @return the approval
     */
    public String getApproval() {
        return approval;
    }

    /**
     * @param approval the approval to set
     */
    public void setApproval(String approval) {
        this.approval = approval;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}
