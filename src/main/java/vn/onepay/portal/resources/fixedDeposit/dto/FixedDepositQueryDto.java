package vn.onepay.portal.resources.fixedDeposit.dto;

import java.io.Serializable;

public class FixedDepositQueryDto implements Serializable {
    private Integer page;
    private Integer pageSize;
    private String fromDate;
    private String toDate;
    private String fd;
    private String contractCode;
    private String businessRegName;
    private String merchantId;
    private String advAccount;
    private String bank;
    private String state;
    private String source;
    private String id;
    private String rollPrincipal;
    private String ids;
    private String type;
    private String taxNumber;
    private Float interestRate;
    private Integer maturityPeriod;
    private String approval;
    private String idFd;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getId() {
        return id;
    }

    public String getIdFd() {
        return idFd;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setIdFd(String idFd) {
        this.idFd = idFd;
    }

    public String getRollPrincipal() {
        return rollPrincipal;
    }

    public void setRollPrincipal(String rollPrincipal) {
        this.rollPrincipal = rollPrincipal;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getFd() {
        return fd;
    }

    public void setFd(String fd) {
        this.fd = fd;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getBusinessRegName() {
        return businessRegName;
    }

    public void setBusinessRegName(String businessRegName) {
        this.businessRegName = businessRegName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getAdvAccount() {
        return advAccount;
    }

    public void setAdvAccount(String advAccount) {
        this.advAccount = advAccount;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    /**
     * @return String return the ids
     */
    public String getIds() {
        return ids;
    }

    /**
     * @param ids the ids to set
     */
    public void setIds(String ids) {
        this.ids = ids;
    }

    /**
     * @return String return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }


    /**
     * @return String return the taxNumber
     */
    public String getTaxNumber() {
        return taxNumber;
    }

    /**
     * @param taxNumber the taxNumber to set
     */
    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }


    /**
     * @return Float return the interestRate
     */
    public Float getInterestRate() {
        return interestRate;
    }

    /**
     * @param interestRate the interestRate to set
     */
    public void setInterestRate(Float interestRate) {
        this.interestRate = interestRate;
    }


    /**
     * @return String return the maturityPeriod
     */
    public Integer getMaturityPeriod() {
        return maturityPeriod;
    }

    /**
     * @param maturityPeriod the maturityPeriod to set
     */
    public void setMaturityPeriod(Integer maturityPeriod) {
        this.maturityPeriod = maturityPeriod;
    }

    /**
     * @return the approval
     */
    public String getApproval() {
        return approval;
    }

    /**
     * @param approval the approval to set
     */
    public void setApproval(String approval) {
        this.approval = approval;
    }
    
}
