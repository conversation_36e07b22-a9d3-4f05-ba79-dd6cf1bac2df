package vn.onepay.portal.resources.File;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

public class FileDownloadDao extends Db implements IConstants {

    public static void updateStatusFile(String statusFile, String fileNameHash) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.UPDATE_STATUS(?,?,?,?,?) }");
            cs.setString(1, fileNameHash);
            cs.setString(2, statusFile);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(4);
            String sError = cs.getString(5);
            rs = (ResultSet) cs.getObject(3);
            if (nError != 200) {
                logger.severe("UPDATE STATUS DOWNLOAD error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

    }
    /**
     *
     * @param fileHashName
     * @return
     */

    public static List<FileDownloadDto> getFilesByType(String typeName, int userId) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<FileDownloadDto> files  = new ArrayList<>();

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.GET_FILES_BY_TYPE(?,?,?,?,?) }");
            cs.setString(1, typeName);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, userId);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET FILES BY TYPE DOWNLOAD error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    FileDownloadDto fileDownloadDto = null;
                    fileDownloadDto = bindFileDownload(rs);
                    files.add(fileDownloadDto);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return files;
    }
    public static FileDownloadDto get(String fileHashName) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        FileDownloadDto fileDownloadDto = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.GET_FILE(?,?,?,?) }");
            cs.setString(1, fileHashName);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET FILE DOWNLOAD error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    fileDownloadDto = bindFileDownload(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return fileDownloadDto;
    }
    
    public static FileDownloadDto get2(String fileHashName) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        FileDownloadDto fileDownloadDto = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.GET_FILE(?,?,?,?) }");
            cs.setString(1, fileHashName);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                logger.severe("GET FILE DOWNLOAD error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    fileDownloadDto = bindFileDownload(rs);
                }
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return fileDownloadDto;
    }

    /**
     *
     * @param fileDownloadDto
     * @return
     */
    public static FileDownloadDto update(FileDownloadDto fileDownloadDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        if (fileDownloadDto == null) {
            return null;
        }

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.UPDATE_FILE(?,?,?,?,?) }");
            cs.setString(1, fileDownloadDto.getFile_hash_name());
            cs.setLong(2, fileDownloadDto.getFile_size());
            cs.setString(3, fileDownloadDto.getStatus());
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(4);
            String sError = cs.getString(5);
            if (nError != 200 && nError != 201) {
                logger.severe("INSERT FILE DOWNLOAD Error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return fileDownloadDto;
    }

    /**
     *
     * @param fileDownloadDto
     * @return
     */
    public static FileDownloadDto insert (FileDownloadDto fileDownloadDto) throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        logger.log(Level.INFO, "FileDownloadDto data: ",fileDownloadDto);
        if (fileDownloadDto == null) {
            return null;
        }

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_FILE_DOWNLOAD.INSERT_FILE(?,?,?,?,?,?,?,?) }");
            cs.setString(1, fileDownloadDto.getFile_hash_name());
            cs.setString(2, fileDownloadDto.getUser());
            cs.setString(3, fileDownloadDto.getFile_name());
            cs.setString(4, fileDownloadDto.getExt());
            cs.setString(5, fileDownloadDto.getFile_type());
            cs.setString(6, "");
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(7);
            String sError = cs.getString(8);
            if (nError != 200 && nError != 201) {
                logger.severe("INSERT FILE DOWNLOAD Error: " + nError + " - " + sError);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return fileDownloadDto;
    }

    private static FileDownloadDto bindFileDownload(ResultSet rs) throws Exception {
        String fileHashName = rs.getString("S_FILE_HASH_NAME");
        String fileName = rs.getString("S_FILE_ORG_NAME");
        String type = rs.getString("S_TYPE");
        String userId = rs.getString("S_USER_ID");
        String extension = rs.getString("S_EXTENSION");
        Timestamp createDate = Timestamp.valueOf(rs.getString("D_CREATE_DATE"));
        Timestamp updateDate = Timestamp.valueOf(rs.getString("D_UPDATED_DATE"));
        Timestamp expiredDate = null;
        if(rs.getString("D_EXP_DATE") != null){
            expiredDate = Timestamp.valueOf(rs.getString("D_EXP_DATE"));
        }
        String status = rs.getString("S_STATUS");
        String condition = rs.getString("S_CONDITIONS");
        long fileSize = 0;
        if(rs.getString("N_FILE_SIZE") != null){
            fileSize =  rs.getLong("N_FILE_SIZE");
        }

        FileDownloadDto fileDownload = new FileDownloadDto();
        fileDownload.setConditions(condition);
        fileDownload.setCreate_date(createDate);
        fileDownload.setUpdate_date(updateDate);
        fileDownload.setExpired_date(expiredDate);
        fileDownload.setExt(extension);
        fileDownload.setUser(userId);
        fileDownload.setFile_hash_name(fileHashName);
        fileDownload.setFile_name(fileName);
        fileDownload.setFile_type(type);
        fileDownload.setStatus(status);
        fileDownload.setFile_size(fileSize);

        return fileDownload;
    }
}
