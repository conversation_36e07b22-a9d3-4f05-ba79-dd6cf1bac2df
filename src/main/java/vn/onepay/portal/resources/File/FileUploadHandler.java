package vn.onepay.portal.resources.File;

import io.vertx.core.file.FileSystem;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.File.dto.FileUploadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.google.gson.Gson;
import org.apache.commons.lang3.RandomStringUtils;
import static vn.onepay.portal.Util.sendResponse;

public class FileUploadHandler  implements IConstants  {
    private static final Logger LOGGER = Logger.getLogger(FileUploadHandler.class.getName());


    public static void post (RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    throw IErrors.VALIDATION_ERROR;
                }

                    // rc.fileUploads()
                    // .stream()
                    // .map(fileUpload -> {
                    // })
                    // .collect(Collectors.toList())
                    // ;
                    BaseList<FileUploadDto> r = new BaseList<>();
                    List<FileUploadDto> rl = new ArrayList<>();
                    LOGGER.info("file-upload: "+ ctx.fileUploads());
                    for (FileUpload f : ctx.fileUploads()) {
                        FileUploadDto fu = new FileUploadDto();
                        String fileName = f.fileName();
                        int extLocation = f.fileName().lastIndexOf(".");
                        String ext = f.fileName().substring(extLocation+1);
                        String newName = f.fileName().substring(0,extLocation)+"_"+RandomStringUtils.randomAlphabetic(10) + "." + ext;
                        fu.setOriginalName(fileName);
                        fu.setExt(ext);
                        fu.setName(newName);
                        fu.setUserId(userId);
                        LOGGER.info("fu: "+ fu);
                        FileUploadDao.insert(fu);
                        rl.add(fu);


                        // copy to other directory
                        Path srcPath = FileSystems.getDefault().getPath(f.uploadedFileName()).normalize();
                        Path desPath = FileSystems.getDefault().getPath(Config.getFileUploadLocation()+"/"+newName);
                        LOGGER.info("srcPath: "+ srcPath);
                        LOGGER.info("desPath: "+ desPath);
                        FileSystem fs = ctx.vertx().fileSystem();
                        fs.exists(srcPath.toString(), (isExist) -> {
                            if (isExist.result()) {
                                try {
                                    Files.copy(srcPath, desPath);
                                } catch (IOException e) {
                                    LOGGER.log(Level.SEVERE, "error on move file from "+srcPath.toString() + " to " +desPath.toString(), e);
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                            } else {
                                LOGGER.log(Level.SEVERE, "srcPath not found: " + srcPath.toString() );
                                throw IErrors.INTERNAL_SERVER_ERROR;
                            }
                        });
                    }
                    r.setList(rl);
                    sendResponse(ctx, 200, r);

            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "Upload FILE ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
