package vn.onepay.portal.resources.File;

import com.google.gson.Gson;
import io.vertx.core.file.AsyncFile;
import io.vertx.core.file.FileSystem;
import io.vertx.core.file.OpenOptions;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.streams.Pump;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class FileDownloadHandler {
    private static final Logger LOGGER = Logger.getLogger(FileDownloadHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void get(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String fileNameHash = request.getParam(IConstants.FILE_HASH_NAME);
                if (fileNameHash == null) {
                    throw IErrors.VALIDATION_ERROR;
                }
                FileDownloadDto fileDownloadDto = FileDownloadDao.get(fileNameHash);
                LOGGER.log(Level.INFO, "fileDownloadDto: " + fileDownloadDto.toString());
                if (fileDownloadDto != null) {
                    LOGGER.log(Level.INFO, "FileDownloadDto != null");
                    Path requestPath = FileSystems.getDefault().getPath(Config.getFileExportLocation() + "/" + fileNameHash, "/").normalize();
                    LOGGER.log(Level.INFO, "Request Path: " + requestPath);
                    FileSystem fs = ctx.vertx().fileSystem();
                    fs.exists(requestPath.toString(), (isExist) -> {
                        if (isExist.result()) {
                            LOGGER.log(Level.INFO, "Exist result!");
                            Map data = new HashMap();
                            data.put(ParamsPool.FILE_HASH_NAME, fileNameHash);
                            data.put(ParamsPool.PATH_FILE, requestPath.toString());
                            data.put(ParamsPool.FILE_NAME, fileDownloadDto.getFile_name());
                            data.put(ParamsPool.FILE_EXT, fileDownloadDto.getExt());
                            try {
                                data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                            } catch (IOException e) {
                                ctx.fail(e);
                            }
                            ctx.response().setChunked(true);
                            String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                            String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                            String contentType = "application/octet-stream";
                            if (extFile.equals("zip")) {
                                contentType = "application/zip";
                            }
                            ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                            ctx.response().putHeader("Content-Disposition", "attachment; filename=\"" + fileOrg + "." + extFile + "\"");

                            ctx.response().sendFile(requestPath.toString(), (result) -> {
                                if (result.succeeded()) {
                                    try {
                                        Files.deleteIfExists(Paths.get(String.valueOf(data.get(ParamsPool.PATH_FILE))));
                                        // if(fileDownloadDto.getFile_type().equals("statistic")){
                                            try {
                                                FileDownloadDao.updateStatusFile("downloaded", fileNameHash);
                                            } catch (Exception e) {
                                                LOGGER.log(Level.SEVERE, "Update Status File DB Error : ", e);
                                            }
                                        // }
                                    } catch (IOException e) {
                                        LOGGER.log(Level.SEVERE, "Remove File Error : ", e);
                                    }
                                }
                            });
                        } else {
                            LOGGER.log(Level.INFO, "Not exist result!");
                            throw IErrors.RESOURCE_NOT_FOUND;
                        }
                    });

                    // sendResponse(ctx, 200, data);
                } else {
                    LOGGER.log(Level.INFO, "FileDownloadDto = null");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "DOWNLOAD FILE ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
