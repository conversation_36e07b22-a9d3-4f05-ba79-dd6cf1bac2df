package vn.onepay.portal.resources.auto_confirm_refund.exchange_rate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.time.format.DateTimeFormatter;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.auto_confirm_refund.report.ReportConfirmRefundDao;

public class ExchangeRateDao extends Db {
    
    private static final Logger LOGGER = LogManager.getLogger(ReportConfirmRefundDao.class);
    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    private static final String SEARCH = "{ call ONEFIN.PKG_REQUEST_REFUND_EXCHANGE_RATE.SEARCH(?,?,?,?,?,?,?)}";

    public static JsonObject search(JsonObject params) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setDate(3, params.getString("applyDate") == null ? null : new Date(Long.parseLong(params.getString("applyDate"))));
            cs.setString(4, params.getString("session"));
            cs.setString(5, params.getString("exchangeRate"));
            if (params.getString("pageActive").isEmpty()) {
                cs.setNull(6, OracleTypes.NUMBER);
            } else {
                cs.setInt(6, Integer.parseInt(params.getString("pageActive")));
            }
            if (params.getString("pageSize").isEmpty()) {
                cs.setNull(7, OracleTypes.NUMBER);
            } else {
                cs.setInt(7, Integer.parseInt(params.getString("pageSize")));
            }
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("id", rs.getLong("N_ID"));
                item.put("rowNum", rs.getString("RNUM"));
                item.put("exchangeRate", rs.getString("N_EXCHANGE_RATE_AMOUNT"));
                item.put("applyDate", rs.getTimestamp("D_APPLY") == null ? null : dtf.format(rs.getTimestamp("D_APPLY").toLocalDateTime()));
                item.put("session", rs.getString("N_SESSION"));
                list.add(item);
            }
            result.put("list", list);

            rsTotal = (ResultSet) cs.getObject(2);
            while (rsTotal != null && rsTotal.next()) {
                JsonObject item = new JsonObject();
                item.put("totalRows", rsTotal.getLong("N_TOTAL_ROWS"));
                result.put("total", item);
            }
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }
}
