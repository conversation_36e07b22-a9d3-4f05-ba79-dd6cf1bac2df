package vn.onepay.portal.resources.auto_confirm_refund.config_refund_detail;

import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;

public class ConfigRefundDetailHandler implements IConstants {
    private static final Logger logger = Logger
            .getLogger(ConfigRefundDetailHandler.class.getName());
    private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");
    
    public static void getDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
           try {
                HttpServerRequest request = ctx.request();
                Long configRefundId = Long.parseLong(request.getParam("configRefundId"));
                Util.sendResponse(ctx, 200, ConfigRefundDetailDao.getDetail(configRefundId));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
           }

        }, false, null);
    }

    public static void save(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/save";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "SAVE CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListAddableOffsetRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Long configRefundId = Long.parseLong(request.getParam("configRefundId"));
                Util.sendResponse(ctx, 200, ConfigRefundDetailDao.getListAddableOffsetRefund(configRefundId));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET OFFSET REFUND ADDABLE INDEBT CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addOffsetRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/add-offset-refund";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "ADD CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getListEditableOffsetRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Long offsetRefundId = Long.parseLong(request.getParam("offsetRefundId"));
                Long configRefundId = Long.parseLong(request.getParam("configRefundId"));                
                Util.sendResponse(ctx, 200, ConfigRefundDetailDao.getListEditableOffsetRefund(offsetRefundId, configRefundId));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST EDITABLE OFFSET REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateOffsetRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/update-offset-refund";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "UPDATE CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteOffsetRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/delete-offset-refund";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DELETE CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
