package vn.onepay.portal.resources.auto_confirm_refund.config_refund_detail;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;

public class ConfigRefundDetailDao extends Db {

    private static final Logger LOGGER = Logger.getLogger(ConfigRefundDetailDao.class.getName());

    private static final String SEARCH = "{ call ONEFIN.PKG_CONFIG_REFUND.SEARCH_CONFIG_REFUND(?,?,?,?,?,?,?,?)}";
    private static final String GET_DETAIL = "{ call ONEFIN.PKG_CONFIG_REFUND_DETAIL.GET_DETAIL_CONFIG_REFUND(?,?,?,?,?)}";
    private static final String GET_LIST_ADDABLE_OFFSET_REFUND = "{ call ONEFIN.PKG_CONFIG_REFUND_DETAIL.GET_LIST_ADDABLE_OFFSET_REFUND(?,?,?)}";
    private static final String GET_LIST_EDITABLE_OFFSET_REFUND = "{ call ONEFIN.PKG_CONFIG_REFUND_DETAIL.GET_LIST_EDITABLE_OFFSET_REFUND(?,?,?,?)}";

    public static JsonObject search(MultiMap params) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, params.get("keyword").trim());
            cs.setString(5, params.get("activeState"));
            cs.setString(6, params.get("approveState"));
            cs.setLong(7, Long.parseLong(params.get("pageActive")));
            cs.setLong(8, Long.parseLong(params.get("pageSize")));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nTotal = cs.getInt(2);
            String sResult = cs.getString(3);

            if (sResult != null) {
                throw new Exception("DB PKG_CONFIG_REFUND: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    JsonObject item = new JsonObject();
                    item.put("rowNum", rs.getString("RNUM"));
                    item.put("configRefundId", rs.getString("N_CONFIG_REFUND_ID"));
                    item.put("partnerId", rs.getString("N_PARTNER_ID"));
                    item.put("partnerShortName", rs.getString("S_SHORT_NAME"));
                    item.put("partnerFullName", rs.getString("S_PARTNER_NAME"));
                    item.put("overdraft", rs.getString("N_OVERDRAFT"));
                    item.put("approveState", rs.getString("S_STATE"));
                    list.add(item);              
                }
            }

            result.put("list", list);
            result.put("total", nTotal);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static JsonObject getDetail(Long configRefundId) throws Exception {
        JsonObject result = new JsonObject();
        Connection con = null;
        CallableStatement cs = null;

        ResultSet rsConfigRefund = null;
        JsonObject configRefund = new JsonObject();

        ResultSet rsOffsetRefund = null;
        JsonArray listOffsetRefund = new JsonArray();

        ResultSet rsIndebtConfigRefund = null;
        JsonArray listIndebtConfigRefund = new JsonArray();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_DETAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setLong(5, configRefundId);
            cs.execute();

            String sResult = cs.getString(4);
            if (sResult != null) {
                throw new Exception("DB PKG_CONFIG_REFUND: " + sResult);
            } else {
                rsConfigRefund = (ResultSet) cs.getObject(1);
                configRefund = bindDataConfigRefund(rsConfigRefund);

                rsOffsetRefund = (ResultSet) cs.getObject(2);
                listOffsetRefund = bindDataListOffsetRefund(rsOffsetRefund);

                rsIndebtConfigRefund = (ResultSet) cs.getObject(3);
                listIndebtConfigRefund = bindDataListIndebtConfigRefund(rsIndebtConfigRefund);
            }

            result.put("configRefund", configRefund);
            result.put("listOffsetRefund", listOffsetRefund);
            result.put("listIndebtConfigRefund", listIndebtConfigRefund);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            throw e;
        } finally {
            closeConnectionDB(rsConfigRefund, null, cs, con);
            closeConnectionDB(rsOffsetRefund, null, null, null);
            closeConnectionDB(rsIndebtConfigRefund, null, null, null);
        }
        return result;
    }

    public static JsonObject bindDataConfigRefund(ResultSet rs) throws Exception {
        JsonObject result = new JsonObject();
        try {
            if (rs != null && rs.next()) {
                result.put("id", rs.getLong("N_CONFIG_REFUND_ID"));
                result.put("partnerId", rs.getLong("N_PARTNER_ID"));
                result.put("shortName", rs.getString("S_SHORT_NAME"));
                result.put("fullName", rs.getString("S_FULL_NAME"));
                result.put("active", rs.getInt("N_ACTIVE"));
                result.put("overdraft", rs.getInt("N_OVERDRAFT"));
                result.put("dateEndOverdraft", rs.getDate("D_DATE_END_OVERDRAFT") != null ? rs.getDate("D_DATE_END_OVERDRAFT").getTime() : null);
                result.put("autoMailNotifyDebt", rs.getInt("N_AUTO_MAIL_NOTIFY_DEBT"));
                result.put("emailNotifyDebt", rs.getString("S_EMAIL_NOTIFY_DEBT"));
                result.put("description", rs.getString("S_DESCRIPTION"));
                result.put("state", rs.getString("S_STATE"));
            }
        } catch (Exception ex) {
            throw ex;
        }
        return result;
    }

    public static JsonArray bindDataListOffsetRefund(ResultSet rs) throws Exception {
        JsonArray result = new JsonArray();
        try {
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("offsetRefundId", rs.getLong("N_OFFSET_REFUND_ID"));
                item.put("rowNum", rs.getInt("ROWNUM"));
                item.put("indebtednessConfigIds", rs.getString("S_INDEBTEDNESS_CONFIG_IDS"));
                item.put("parentConfigIds", rs.getString("S_PARENT_CONFIG_IDS"));
                item.put("payChannels", rs.getString("S_PAY_CHANNELS"));
                item.put("advanceAccounts", rs.getString("S_ADVANCE_ACCOUNTS"));
                item.put("contractCodes", rs.getString("S_CONTRACT_CODES"));
                result.add(item);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return result;
    }

    public static JsonArray bindDataListIndebtConfigRefund(ResultSet rs) throws Exception {
        JsonArray result = new JsonArray();
        try {
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("rowNum", rs.getInt("ROWNUM"));
                item.put("indebtnessConfigId", rs.getLong("N_INDEBTEDNESS_CONFIG_ID"));
                item.put("lastMerchantName", rs.getString("S_LAST_MERCHANT_NAME"));
                item.put("merchantIds", rs.getString("S_MERCHANT_IDS"));
                item.put("contractCode", rs.getString("S_CONTRACT_CODE"));
                item.put("payChannels", rs.getString("S_PAY_CHANNELS"));
                item.put("advanceAccount", rs.getString("S_ADVANCE_ACCOUNT"));
                item.put("transactionType", rs.getString("S_TRANSACTION_TYPE"));
                item.put("offsetMerchant", rs.getInt("N_OFFSET_MERCHANT"));
                item.put("offsetTopup", rs.getInt("N_OFFSET_TOPUP"));
                item.put("holdRefund", rs.getInt("N_HOLd_REFUND"));
                item.put("overdraftActivate", rs.getInt("N_OVERDRAFT_ACTIVATE"));
                item.put("state", rs.getString("S_STATE"));
                item.put("partnerId", rs.getLong("N_PARTNER_ID"));
                item.put("parentConfig", rs.getLong("N_PARENT_CONFIG_ID"));
                item.put("offsetRefundId", rs.getLong("N_OFFSET_REFUND_ID"));
                item.put("id", rs.getLong("N_ID"));
                result.add(item);
            }
        } catch (Exception ex) {
            throw ex;
        }
        return result;
    }

    public static JsonObject getListAddableOffsetRefund(Long configRefundId) throws Exception {
        JsonObject dbReturnData = new JsonObject();
        List<JsonObject> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_ADDABLE_OFFSET_REFUND);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, configRefundId);
            cs.execute();

            String error = cs.getString(2);
            if (error != null) {
                throw new SQLException(error);
            } else {
                rs = (ResultSet) cs.getObject(1);
                while (rs != null && rs.next()) {
                    JsonObject item = new JsonObject();
                    item.put("id", rs.getLong("N_ID"));
                    item.put("indebtednessConfigId", rs.getLong("N_INDEBTEDNESS_CONFIG_ID"));
                    item.put("merchantIds", rs.getString("S_MERCHANT_IDS"));
                    item.put("payChannels", rs.getString("S_PAY_CHANNELS"));
                    item.put("advanceAccount", rs.getString("S_ADVANCE_ACCOUNT"));
                    item.put("contractCode", rs.getString("S_CONTRACT_CODE"));
                    list.add(item);
                }
            }
            dbReturnData.put("list", list);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "GET LIST ADDABLE OFFSET REFUND", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return dbReturnData;
    }

    public static JsonObject getListEditableOffsetRefund(Long offsetRefundId, Long configRefundId) throws Exception {
        JsonObject dbReturnData = new JsonObject();
        List<JsonObject> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LIST_EDITABLE_OFFSET_REFUND);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setLong(3, offsetRefundId);
            cs.setLong(4, configRefundId);
            cs.execute();

            String error = cs.getString(2);
            if (error != null) {
                throw new SQLException(error);
            } else {
                rs = (ResultSet) cs.getObject(1);
                while (rs != null && rs.next()) {
                    JsonObject item = new JsonObject();
                    item.put("id", rs.getLong("N_ID"));
                    item.put("indebtednessConfigId", rs.getLong("N_INDEBTEDNESS_CONFIG_ID"));
                    item.put("merchantIds", rs.getString("S_MERCHANT_IDS"));
                    item.put("payChannels", rs.getString("S_PAY_CHANNELS"));
                    item.put("advanceAccount", rs.getString("S_ADVANCE_ACCOUNT"));
                    item.put("contractCode", rs.getString("S_CONTRACT_CODE"));
                    item.put("active", rs.getInt("N_ACTIVE"));
                    list.add(item);
                }
            }
            dbReturnData.put("list", list);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "GET LIST EDITABLE OFFSET REFUND", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) throw exception;
        return dbReturnData;
    }
}
