package vn.onepay.portal.resources.auto_confirm_refund.report;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;

public class ReportConfirmRefundDao extends Db {
    
    private static final Logger LOGGER = LogManager.getLogger(ReportConfirmRefundDao.class);
    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");

    private static final String SEARCH = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.SEARCH_REPORT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_MERCHANTS = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_MERCHANTS(?)}";
    private static final String GET_PARTNERS = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_PARTNERS(?)}";
    private static final String GET_LASTEST_REPORT_DATE = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_LASTEST_REPORT_DATE(?)}";    
    private static final String GET_REQUEST_REFUND_TRANSACTION = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_REQUEST_REFUND_TRANSACTION(?,?,?,?,?,?,?)}";
    private static final String GET_DETAIL_ACCOUNT_BALANCE = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_DETAIL_ACCOUNT_BALANCE(?,?,?)}";
    private static final String GET_REPORT_BY_IDS = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_REPORT_BY_IDS(?,?)}";
    private static final String GET_REFUND_PENDING_EXCHANGE_RATE = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_REFUND_PENDING_EXCHANGE_RATE(?,?,?,?)}";
    private static final String GET_REPORT_BUILDING_STATE = "{ call ONEFIN.PKG_REPORT_CONFIRM_REFUND.GET_REPORT_BUILDING_STATE(?,?,?,?)}";

    public static JsonObject search(JsonObject params) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setDate(3, (params.getString("fromDate") == null || "".equals(params.getString("fromDate"))) ? null : new Date(Long.parseLong(params.getString("fromDate"))));
            cs.setDate(4, (params.getString("toDate") == null || "".equals(params.getString("toDate"))) ? null : new Date(Long.parseLong(params.getString("toDate"))));
            cs.setString(5, params.getString("session"));
            cs.setString(6, params.getString("partnerId"));
            cs.setString(7, params.getString("merchantId"));
            cs.setString(8, params.getString("contractCode"));
            cs.setString(9, params.getString("bankAccountAdvance"));
            cs.setString(10, params.getString("payChannel"));
            cs.setString(11, params.getString("currency"));
            cs.setString(12, params.getString("other"));
            cs.setString(13, params.getString("tranSendingType"));
            if (params.getString("pageActive").isEmpty()) {
                cs.setNull(14, OracleTypes.NUMBER);
            } else {
                cs.setInt(14, Integer.parseInt(params.getString("pageActive")));
            }
            if (params.getString("pageSize").isEmpty()) {
                cs.setNull(15, OracleTypes.NUMBER);
            } else {
                cs.setInt(15, Integer.parseInt(params.getString("pageSize")));
            }
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("reportId", rs.getLong("N_ID"));
                item.put("configRefundId", rs.getString("S_CONFIG_REFUND_ID"));
                item.put("partnerId", rs.getString("S_PARTNER_ID"));
                item.put("rowNum", rs.getString("RNUM"));
                item.put("partnerName", rs.getString("S_PARTNER_NAME"));
                item.put("merchantName", rs.getString("S_MERCHANT_NAME"));
                item.put("merchantId", rs.getString("S_MERCHANT_ID"));
                item.put("contractCode", rs.getString("S_CONTRACT_CODE"));
                item.put("bankAccountAdvance", rs.getString("S_BANK_ACCOUNT_ADVANCE"));
                item.put("payChannel", rs.getString("S_PAY_CHANNEL"));
                item.put("description", rs.getString("S_DESCRIPTION"));
                item.put("tranSendingType", rs.getString("S_TRAN_SENDING_TYPE"));
                item.put("mailDate", rs.getTimestamp("D_SEND_MAIL_LASTEST") == null ? null : dtf.format(rs.getTimestamp("D_SEND_MAIL_LASTEST").toLocalDateTime()));
                JsonObject sData = new JsonObject(rs.getString("S_DATA"));
                // request refund - yêu cầu hoàn tiền
                item.put("reqRefTransQuantity", sData.getJsonObject("requestRefund").getLong("totalTransaction"));
                item.put("reqRefTransAmountUsd", sData.getJsonObject("requestRefund").getDouble("totalUsd"));
                item.put("reqRefTransAmountVnd", sData.getJsonObject("requestRefund").getDouble("totalVnd"));
                item.put("reqRefTransAmountToVnd", sData.getJsonObject("requestRefund").getDouble("totalChangeToVnd"));
                // account balance - doanh số
                item.put("accountBalance", sData.getJsonObject("accountBalance").getDouble("totalAmount"));
                item.put("topupRefundAmount", sData.getJsonObject("accountBalance").getDouble("topupRefund"));
                item.put("createdAdvanceAmount", sData.getJsonObject("accountBalance").getDouble("createdAdvance"));
                item.put("unsettleTransactionAmount", sData.getJsonObject("accountBalance").getDouble("unsettleTransaction"));
                item.put("unholdRefundAmount", sData.getJsonObject("accountBalance").getDouble("unholdRefund"));
                item.put("overdraftAmount", sData.getJsonObject("accountBalance").getDouble("overdraft"));
                item.put("notAdvanceYetAmount", sData.getJsonObject("accountBalance").getDouble("notAdvanceYet"));
                // confirmable - đủ đk hoàn tiền
                item.put("confirmableReqRefTransQuantity", sData.getJsonObject("confirmableRequestRefund").getLong("totalTransaction"));
                item.put("confirmableReqRefTransAmountToVnd", sData.getJsonObject("confirmableRequestRefund").getDouble("totalChangeToVnd"));
                // unconfirmable - không đủ đk hoàn tiền
                item.put("unconfirmableReqRefTransQuantity", sData.getJsonObject("unconfirmableRequestRefund").getLong("totalTransaction"));
                item.put("unconfirmableReqRefTransAmountToVnd", sData.getJsonObject("unconfirmableRequestRefund").getDouble("totalChangeToVnd"));
                // account debit - số tiền còn phải chuyển
                item.put("accountDebit", sData.getDouble("accountDebit"));
                // used overdraft - thấu chi đã sử dụng
                item.put("usedOverdraft", sData.getJsonObject("accountBalance").getDouble("usedOverdraft"));
                list.add(item);              
            }
            result.put("list", list);

            rsTotal = (ResultSet) cs.getObject(2);
            while (rsTotal != null && rsTotal.next()) {
                JsonObject item = new JsonObject();
                item.put("totalRows", rsTotal.getLong("N_TOTAL_ROWS"));
                item.put("totalReqRefTrans", rsTotal.getLong("N_TOTAL_REQ_REF_TRANS"));
                item.put("totalReqRefAmount", rsTotal.getDouble("N_TOTAL_REQ_REF_AMOUNT"));
                item.put("totalConfirmableReqRefTrans", rsTotal.getLong("N_TOTAL_CONFIRMABLE_TRANS"));    
                item.put("totalConfirmableReqRefAmount", rsTotal.getDouble("N_TOTAL_CONFIRMABLE_AMOUNT"));
                item.put("totalUnconfirmableReqRefTrans", rsTotal.getLong("N_TOTAL_UNCONFIRMABLE_TRANS"));
                item.put("totalUnconfirmableReqRefAmount", rsTotal.getDouble("N_TOTAL_UNCONFIRMABLE_AMOUNT"));
                item.put("totalAccountDebitAmount", rsTotal.getDouble("N_TOTAL_ACCOUNT_DEBIT_AMOUNT"));
                result.put("total", item);
            }
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static JsonObject getMerchants() throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_MERCHANTS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("merchantId", rs.getString("S_MERCHANT_ID"));
                list.add(item);
            }
            result.put("list", list);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static JsonObject getPartners() throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_PARTNERS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("partnerId", rs.getString("N_ID"));
                item.put("shortName", rs.getString("S_SHORT_NAME"));
                item.put("fullName", rs.getString("S_PARTNER_NAME"));
                list.add(item);
            }
            result.put("list", list);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static JsonObject getLastestReportDate() throws Exception {
        JsonObject result = new JsonObject();
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_LASTEST_REPORT_DATE);
            cs.registerOutParameter(1, OracleTypes.DATE);
            cs.execute();
            Long lastestReportDate = cs.getDate(1) == null ? null : cs.getDate(1).getTime();
            result.put("lastestReportDate", lastestReportDate);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        return result;
    }

    public static JsonObject getRequestRefundTransaction(String reportId, String tranIds, String type, Long pageActive, Long pageSize) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        JsonObject total = new JsonObject();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_REQUEST_REFUND_TRANSACTION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setString(3, reportId);
            cs.setString(4, tranIds);
            cs.setString(5, type);
            if (pageActive == null) {
                cs.setNull(6, OracleTypes.NUMBER);
            } else {
                cs.setLong(6, pageActive);
            }
            if (pageSize == null) {
                cs.setNull(7, OracleTypes.NUMBER);
            } else {
                cs.setLong(7, pageSize);
            }
            cs.execute();
            
            // list trans
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("id", rs.getLong("N_ID"));
                item.put("rowNum", rs.getLong("RNUM"));
                item.put("confirmState", rs.getString("S_CONFIRM_STATE"));
                item.put("acquirer", rs.getString("S_ACQUIRER"));
                item.put("groupBankIdAndAppNameAndCardType", rs.getString("S_GROUP_BANK_ID_AND_APP_NAME_AND_CARD_TYPE"));
                item.put("merchantId", rs.getString("S_MERCHANT_ID"));
                item.put("payChannel", rs.getString("S_PAY_CHANNEL"));
                item.put("ciac", rs.getString("S_CIAC"));
                item.put("originalTransactionId", rs.getString("S_ORIGINAL_TRANSACTION_ID"));
                item.put("transactionId", rs.getString("S_TRANSACTION_ID"));
                item.put("bankTransactionId", rs.getString("S_BANK_TRANSACTION_ID"));
                item.put("orderInfo", rs.getString("S_ORDER_INFO"));
                item.put("merchantTransactionId", rs.getString("S_MERCHANT_TRANSACTION_ID"));
                item.put("transactionRef", rs.getString("S_MERCHANT_TRANSACTION_REFERENCE"));
                item.put("cardType", rs.getString("S_CARD_TYPE"));
                item.put("cardNo", rs.getString("S_CARD_NO"));
                item.put("authCode", rs.getString("S_AUTH_CODE"));
                item.put("binCountry", rs.getString("S_BIN_COUNTRY"));
                item.put("binBank", rs.getString("S_BIN_BANK"));
                item.put("itaBank", rs.getString("S_ITA_BANK"));
                item.put("itaTerm", rs.getString("S_ITA_TIME"));
                item.put("refundAmount", rs.getDouble("N_REFUND_AMOUNT"));
                item.put("currency", rs.getString("S_CURRENCY"));
                item.put("exchangRate", rs.getDouble("N_EXCHANGE_RATE"));
                item.put("refundAmountVnd", rs.getDouble("N_REFUND_AMOUNT_CHANGE_TO_VND"));
                item.put("fixFee", rs.getDouble("N_FIX_FEE"));
                item.put("percentFee", rs.getDouble("N_PERCENT_FEE"));
                item.put("itaPercentFee", rs.getDouble("N_ITA_PERCENT_FEE"));
                item.put("totalFee", rs.getDouble("N_TOTAL_FEE"));
                item.put("amountAdvance", rs.getDouble("N_AMOUNT_ADVANCE"));
                item.put("refundDate", rs.getTimestamp("D_DATE_REFUND") == null ? null : dtf.format(rs.getTimestamp("D_DATE_REFUND").toLocalDateTime()));
                item.put("status", rs.getString("S_STATUS"));
                item.put("purchaseAmount", rs.getDouble("N_PURCHASE_AMOUNT"));
                item.put("purchaseDate", rs.getTimestamp("D_DATE_PURCHASE") == null ? null : dtf.format(rs.getTimestamp("D_DATE_PURCHASE").toLocalDateTime()));
                item.put("bnplDate", rs.getTimestamp("D_DATE_BNPL") == null ? null : dtf.format(rs.getTimestamp("D_DATE_BNPL").toLocalDateTime()));
                item.put("advType", rs.getString("S_TYPE_ADVANCE"));
                item.put("refundType", rs.getString("S_REFUND_TYPE"));
                item.put("platform", rs.getString("S_PLATFORM"));
                item.put("clientId", rs.getString("S_CLIENT_ID"));

                // biến đổi thêm các thông tin
                transformTransaction(item);

                list.add(item);
            }
            result.put("list", list);

            // total
            rsTotal = (ResultSet) cs.getObject(2);
            while (rsTotal != null && rsTotal.next()) {
                total.put("totalRows", rsTotal.getLong("N_TOTAL_ROWS"));
                total.put("dateRefundFrom", rsTotal.getTimestamp("D_DATE_REFUND_FROM") == null ? null : dtf.format(rsTotal.getTimestamp("D_DATE_REFUND_FROM").toLocalDateTime()));
                total.put("dateRefundTo", rsTotal.getTimestamp("D_DATE_REFUND_TO") == null ? null : dtf.format(rsTotal.getTimestamp("D_DATE_REFUND_TO").toLocalDateTime()));
            }
            result.put("total", total);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rsTotal, null, null, null);
        }
        return result;
    }

    public static JsonObject getDetailAccountBalance(Long reportId) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        JsonObject total = new JsonObject();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_DETAIL_ACCOUNT_BALANCE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setLong(3, reportId);
            cs.execute();
            
            // list trans
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("merchantId", rs.getString("S_MERCHANT_ID"));
                item.put("payChannel", rs.getString("S_PAY_CHANNEL"));
                item.put("transactionType", rs.getString("S_TRANSACTION_TYPE"));
                item.put("transactionState", rs.getString("S_TRANSACTION_STATE"));
                item.put("amountAdv", rs.getDouble("N_AMOUNT_ADV"));
                item.put("amountVnd", rs.getDouble("N_AMOUNT_VND"));
                item.put("totalFee", rs.getDouble("N_TOTAL_FEE"));
                list.add(item);
            }
            result.put("list", list);

            // total
            rsTotal = (ResultSet) cs.getObject(2);
            while (rsTotal != null && rsTotal.next()) {
                total.put("totalAmount", rsTotal.getDouble("N_TOTAL_AMOUNT"));
                total.put("topupRefundAmount", rsTotal.getDouble("N_TOPUP_REFUND"));
                total.put("createdAdvanceAmount", rsTotal.getDouble("N_CREATED_ADVANCE"));
                total.put("unsettleTransactionAmount", rsTotal.getDouble("N_UNSETTLE_TRANSACTION"));
                total.put("unholdRefundAmount", rsTotal.getDouble("N_UNHOLD_REFUND"));
                total.put("overdraftAmount", rsTotal.getDouble("N_OVERDRAFT"));
                total.put("usedOverdraftAmount", rsTotal.getDouble("N_USED_OVERDRAFT"));
                total.put("notInTermAdvanceAmount", rsTotal.getDouble("N_NOT_ADVANCE_YET"));
            }
            result.put("total", total);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rsTotal, null, null, null);
        }
        return result;
    }

    public static List<JsonObject> getRequestPendingExchangeRate(LocalDate dateFrom, LocalDate dateTo, String sessionHours) throws Exception {
        List<JsonObject> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_REFUND_PENDING_EXCHANGE_RATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.setObject(2, dateFrom);
            cs.setObject(3, dateTo);
            cs.setString(4, sessionHours);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("session", rs.getString("N_SESSION"));
                item.put("applyDate", rs.getDate("D_APPLY").getTime());
                result.add(item);
            }
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    /**
     * Lấy danh sách thông tin job tạo báo cáo y/c hoàn trả theo thời gian và phiên
     * @param dateFrom ngày chặn trên ngày báo cáo
     * @param dateTo ngày chặn dưới ngày báo cáo
     * @param sessionHours phiên báo cáo
     * @return danh sách job tạo báo cáo y/c hoàn trả
     * @throws Exception
     */
    public static List<JsonObject> getRequestPendingBuildingState(LocalDate dateFrom, LocalDate dateTo, String sessionHours) throws Exception {
        List<JsonObject> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_REPORT_BUILDING_STATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.setObject(2, dateFrom);
            cs.setObject(3, dateTo);
            cs.setString(4, sessionHours);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                JsonObject item = new JsonObject();
                item.put("id", rs.getLong("N_ID"));
                item.put("jobId", rs.getLong("N_JOB_ID"));
                item.put("state", rs.getString("S_STATE"));
                item.put("session", rs.getString("N_SESSION"));
                item.put("type", rs.getString("S_TYPE"));
                item.put("dateProcess", rs.getDate("D_PROCESS").getTime());
                result.add(item);
            }
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    /**
     * Biến đổi các thông tin thêm của giao dịch
     * @param tran
     */
    public static void transformTransaction(JsonObject tran) {
        String clientId = tran.getString("clientId");
        String payChannel = tran.getString("payChannel");
        String channel = transformChannel(payChannel, clientId);
        tran.put("qrChannel", channel);
    }

    public static String transformChannel(String payChannel, String clientId) {
        String channel = "";
        if (QR.equalsIgnoreCase(payChannel)) {
            if (clientId.equals("DSP")) {
                channel = "mPayVN";
            } else if (clientId.equals("VIETINQR")) {
                channel = "Vietin";
            } else if (clientId.equals("DSP_VRBANK")) {
                channel = "VRB";
            } else if (clientId.equals("MSBQR")) {
                channel = "MSB";
            } else if (clientId.equals("VCBQR")) {
                channel = "VIETCOMBANK";
            } else {
                channel = clientId;
            }
        }
        return channel;
    }

}
