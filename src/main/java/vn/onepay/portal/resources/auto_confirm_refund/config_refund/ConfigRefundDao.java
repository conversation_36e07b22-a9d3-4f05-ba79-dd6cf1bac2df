package vn.onepay.portal.resources.auto_confirm_refund.config_refund;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.resources.Db;

public class ConfigRefundDao extends Db {

    private static final Logger LOGGER = Logger.getLogger(ConfigRefundDao.class.getName());
    private static final DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");

    private static final String SEARCH = "{ call ONEFIN.PKG_CONFIG_REFUND.SEARCH_CONFIG_REFUND(?,?,?,?,?,?,?,?)}";
    private static final String GET_DETAIL = "{ call ONEFIN.PKG_CONFIG_REFUND.GET_DETAIL_CONFIG_REFUND(?,?,?,?,?)}";

    public static JsonObject search(MultiMap params) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, params.get("keyword").trim());
            cs.setString(5, params.get("activeState"));
            cs.setString(6, params.get("approveState"));
            cs.setLong(7, Long.parseLong(params.get("pageActive")));
            cs.setLong(8, Long.parseLong(params.get("pageSize")));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nTotal = cs.getInt(2);
            String sResult = cs.getString(3);

            if (sResult != null) {
                throw new Exception("DB PKG_CONFIG_REFUND: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    JsonObject item = new JsonObject();
                    item.put("rowNum", rs.getString("RNUM"));
                    item.put("configRefundId", rs.getString("N_CONFIG_REFUND_ID"));
                    item.put("partnerId", rs.getString("N_PARTNER_ID"));
                    item.put("partnerShortName", rs.getString("S_SHORT_NAME"));
                    item.put("partnerFullName", rs.getString("S_PARTNER_NAME"));
                    item.put("overdraft", rs.getString("N_OVERDRAFT"));
                    item.put("approveState", rs.getString("S_STATE"));
                    item.put("dateUpdate", rs.getDate("D_UPDATE") == null ? "" : dateFormat.format(rs.getDate("D_UPDATE")));
                    list.add(item);              
                }
            }

            result.put("list", list);
            result.put("total", nTotal);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }
}
