package vn.onepay.portal.resources.auto_confirm_refund.report;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.IConstants;

public class ReportConfirmRefundService implements IConstants {
    
    private static final Logger logger = Logger.getLogger(ReportConfirmRefundHandler.class.getName());
    private static final String GROUP_BY_SPLITOR = "-";
    private static final List<String> PURCHASE_TYPES = Arrays.asList(new String[]{"PURCHASE","CAPTURE","VOICE_REFUND_CAPTURE","AUTO_HOLD_FD"});
    private static final List<String> REFUND_TYPES = Arrays.asList(new String[]{"REFUND","REFUND_CAPTURE","VOID_CAPTURE","VOID_PURCHASE"});
    private static DateTimeFormatter DTF = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    public static JsonObject getDetailAccountBalance(Long reportId) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray resultList = new JsonArray();
        
        JsonObject detailAccountBalance = ReportConfirmRefundDao.getDetailAccountBalance(reportId);
        List<JsonObject> listTrans = detailAccountBalance.getJsonArray("list").getList();
        JsonObject total = detailAccountBalance.getJsonObject("total");

        // Tính tổng trên tổng giao dịch
        double purchaseAmountSum = 0;
        double refundAmountSum = 0;
        double balanceAmountSum = 0;
        double totalFeeSum = 0;
        double amountAdvSum = 0;
        long successTransQuantitySum = 0;

        // Tính tổng trên từng group
        Map<String, List<JsonObject>> listGroupedTrans = listTrans.stream().collect(Collectors.groupingBy(item->{
            return item.getString("merchantId") + GROUP_BY_SPLITOR + item.getString("payChannel");
        }));
        for (int i = 0; i < listGroupedTrans.keySet().size(); i++) {
            String key = (String) listGroupedTrans.keySet().toArray()[i];
            String[] keys = key.split(GROUP_BY_SPLITOR);
            String merchantId = keys[0];
            String payChannel = keys[1];
            List<JsonObject> listGroupedTransItem = listGroupedTrans.get(key);
            long successTransQuantity = calculateSuccessTransQuantity(listGroupedTransItem);
            Object[] calculatedObj = calculateDetailAccountBalance(listGroupedTransItem);
            double purchaseAmount = (double) calculatedObj[0];
            double refundAmount = (double) calculatedObj[1];
            double balanceAmount = (double) calculatedObj[2];
            double totalFee = (double) calculatedObj[3];
            double amountAdv = (double) calculatedObj[4];

            JsonObject resultItem = new JsonObject();
            resultItem.put("rowNum", i+1);
            resultItem.put("merchantId", merchantId);
            resultItem.put("payChannel", payChannel);
            resultItem.put("successTransQuantity", successTransQuantity);
            resultItem.put("purchaseAmount", purchaseAmount);
            resultItem.put("refundAmount", refundAmount);
            resultItem.put("balanceAmount", balanceAmount);
            resultItem.put("totalFee", totalFee);
            resultItem.put("amountAdv", amountAdv);
            resultList.add(resultItem);

            // sum tổng cuối
            successTransQuantitySum += successTransQuantity;
            purchaseAmountSum += purchaseAmount;
            refundAmountSum += refundAmount;
            balanceAmountSum += balanceAmount;
            totalFeeSum += totalFee;
            amountAdvSum += amountAdv;
        }
        JsonObject listTotal = new JsonObject();
        listTotal.put("successTransQuantitySum", successTransQuantitySum);
        listTotal.put("purchaseAmountSum", purchaseAmountSum);
        listTotal.put("refundAmountSum", refundAmountSum);
        listTotal.put("balanceAmountSum", balanceAmountSum);
        listTotal.put("totalFeeSum", totalFeeSum);
        listTotal.put("amountAdvSum", amountAdvSum);

        result.put("list", resultList);
        result.put("listTotal", listTotal);
        result.put("total", total);

        return result;
    }

    public static Object[] calculateDetailAccountBalance(List<JsonObject> listGroupedTransItem) {
        double sumPurchaseAmountVnd = 0, sumRefundAmountVnd = 0, sumTotalAmountVnd = 0, sumTotalFee = 0, sumTotalAmountAdv = 0;
        for (JsonObject trans : listGroupedTransItem) {
            String transactionType = trans.getString("transactionType");
            String transactionState = trans.getString("transactionState");
            double amountAdv = trans.getDouble("amountAdv");
            double amountVnd = trans.getDouble("amountVnd");
            double totalFee = trans.getDouble("totalFee");
            if (SUCCESS.equalsIgnoreCase(transactionState)) {
                if (PURCHASE_TYPES.contains(transactionType)) {
                    sumPurchaseAmountVnd += amountVnd;
                }
                if (REFUND_TYPES.contains(transactionType)) {
                    sumRefundAmountVnd += amountVnd;
                }
            }
            sumTotalFee += totalFee;
            sumTotalAmountAdv += amountAdv;
        }
        sumTotalAmountVnd = sumPurchaseAmountVnd - sumRefundAmountVnd;
        return new Object[]{sumPurchaseAmountVnd, sumRefundAmountVnd, sumTotalAmountVnd, sumTotalFee, sumTotalAmountAdv};
    }

    public static long calculateSuccessTransQuantity(List<JsonObject> listGroupedTransItem) {
        long quantity = 0;
        for (JsonObject transItem : listGroupedTransItem) {
            String transactionState = transItem.getString("transactionState");
            if ("SUCCESS".equals(transactionState)) quantity += 1;
        }
        return quantity;
    }

    public static JsonObject alertRefundPendingExchangeRate(LocalDate fromDate, LocalDate toDate, String sessionHours) throws Exception {
        JsonObject result = new JsonObject();
        String[] sessionHoursParts = sessionHours.split(",");
        List<JsonObject> listRefundPendingExcRateJson = ReportConfirmRefundDao.getRequestPendingExchangeRate(fromDate, toDate, sessionHours);

        JsonObject notRefundPendingExcRateJson = new JsonObject();
        List<LocalDate> listRequestDate = fromDate.datesUntil(toDate.plusDays(1)).collect(Collectors.toList());
        for (String session : sessionHoursParts) {
            List<LocalDate> listNotRefundPendingExcRateDate = new ArrayList<>();
            List<JsonObject> listRefPendingExcRate = listRefundPendingExcRateJson.stream().filter(item->session.equals(item.getString("session"))).collect(Collectors.toList());
            if (listRefPendingExcRate.isEmpty()) {
                listNotRefundPendingExcRateDate.addAll(listRequestDate);
            } else {
                List<LocalDate> listRefPendingExcRateDate = listRefPendingExcRate.stream().map(item->{
                    return Instant.ofEpochMilli(item.getLong("applyDate")).atZone(ZoneId.systemDefault()).toLocalDate();
                }).collect(Collectors.toList());
                for (LocalDate requestDate : listRequestDate) {
                    if (!listRefPendingExcRateDate.contains(requestDate)) {
                        listNotRefundPendingExcRateDate.add(requestDate);
                    }
                }
            }
            if (!listNotRefundPendingExcRateDate.isEmpty())
                notRefundPendingExcRateJson.put(session.toString(), listNotRefundPendingExcRateDate.stream().map(item->DTF.format(item)).collect(Collectors.joining(",")));
        }
        if (!notRefundPendingExcRateJson.isEmpty())
            result.put("warn",  notRefundPendingExcRateJson);
        return result;
    }

    /**
     * Kiểm tra trạng thái báo cáo hoàn trả đang chờ xử lý trong một khoảng thời gian và các phiên làm việc cụ thể.
     * 
     * @param fromDate Ngày bắt đầu kiểm tra trạng thái báo cáo.
     * @param toDate Ngày kết thúc kiểm tra trạng thái báo cáo.
     * @param sessionHours Chuỗi chứa các phiên làm việc cần kiểm tra, được phân cách bởi dấu phẩy (","). Mỗi phiên đại diện cho một khung giờ cụ thể.
     * 
     * @return Một đối tượng JsonObject chứa các thông báo:
     *         - "danger": Danh sách các phiên và ngày chưa chạy báo cáo.
     *         - "warn": Danh sách các phiên và ngày đang chạy báo cáo.
     * 
     * @throws Exception Nếu có lỗi xảy ra trong quá trình lấy dữ liệu hoặc xử lý logic.
     * 
     * <p><b>Chi tiết xử lý:</b></p>
     * <ul>
     *     <li>Truy xuất danh sách các yêu cầu báo cáo đang chờ xử lý từ cơ sở dữ liệu dựa trên khoảng thời gian và các phiên làm việc.</li>
     *     <li>Duyệt qua từng phiên và từng ngày trong khoảng thời gian được cung cấp để kiểm tra trạng thái:</li>
     *     <ul>
     *         <li>Nếu không tìm thấy công việc tương ứng, đánh dấu phiên và ngày đó là "chưa chạy báo cáo".</li>
     *         <li>Nếu trạng thái công việc là "running", đánh dấu phiên và ngày đó là "đang chạy báo cáo".</li>
     *     </ul>
     *     <li>Tạo thông báo cảnh báo cho từng phiên làm việc và trạng thái tương ứng.</li>
     * </ul>
     */
    public static JsonObject alertRefundPendingBuildingState(LocalDate fromDate, LocalDate toDate, String sessionHours) throws Exception {
        JsonObject result = new JsonObject();
        String[] sessionHoursParts = sessionHours.split(",");
        List<JsonObject> listJob = ReportConfirmRefundDao.getRequestPendingBuildingState(fromDate, toDate, sessionHours);
        List<LocalDate> listDate = fromDate.datesUntil(toDate.plusDays(1)).collect(Collectors.toList());
        List<String> listNotRunning = new ArrayList<>();
        List<String> listRunning = new ArrayList<>();
        for (String session : sessionHoursParts) {
            List<String> listNotValidDateNotRunning = new ArrayList<>();
            List<String> listNotValidDateRunning = new ArrayList<>();
            for (LocalDate date : listDate) {
                // Trường hợp chưa đến kỳ chạy job
                JsonObject job = listJob.stream().filter(item->{
                    LocalDate dateProcessJob = Instant.ofEpochMilli(item.getLong("dateProcess")).atZone(ZoneId.systemDefault()).toLocalDate();
                    String sessionJob = item.getString("session");
                    return date.equals(dateProcessJob) && session.equals(sessionJob);
                }).findAny().orElse(null);
                if (job == null) {
                    listNotValidDateNotRunning.add(date.format(DTF));
                    continue;
                }
                // Trường hợp đã chạy job và đang running
                List<JsonObject> listRunningJob = listJob.stream().filter(item->{
                    LocalDate dateProcessJob = Instant.ofEpochMilli(item.getLong("dateProcess")).atZone(ZoneId.systemDefault()).toLocalDate();
                    String sessionJob = item.getString("session");
                    String stateJob = item.getString("state");
                    return date.equals(dateProcessJob) && session.equals(sessionJob) && "running".equals(stateJob);
                }).collect(Collectors.toList());
                if (!listRunningJob.isEmpty()) {
                    listNotValidDateRunning.add(date.format(DTF));
                    continue;
                }
            }
            if (!listNotValidDateNotRunning.isEmpty()) {
                String dates = listNotValidDateNotRunning.stream().collect(Collectors.joining(","));
                String message = "Phiên " + session + " giờ, ngày " + dates + " chưa chạy báo cáo";
                listNotRunning.add(message);
            }
            if (!listNotValidDateRunning.isEmpty()) {
                String dates = listNotValidDateRunning.stream().collect(Collectors.joining(","));
                String message = "Phiên " + session + " giờ, ngày " + dates + " đang chạy báo cáo";
                listRunning.add(message);
            }
        }
        if (!listNotRunning.isEmpty()) result.put("danger", listNotRunning);
        if (!listRunning.isEmpty()) result.put("warn", listRunning);
        return result;
    }
}
