package vn.onepay.portal.resources.auto_confirm_refund.exchange_rate;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;

public class ExchangeRateHandler implements IConstants {
    
    private static final Logger logger = LogManager.getLogger(ExchangeRateHandler.class);
    private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");

    public static void search(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				JsonObject parseParams = Util.parseJsonObject(params);
				Util.sendResponse(ctx, 200, ExchangeRateDao.search(parseParams));
			} catch (Exception e) {
				logger.log(Level.ERROR, "SEARCH REQUEST REFUND EXCHANGE RATE: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void add(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL
						+ "/auto-confirm-refund/exchange-rate/add";
				String requestMethod = "POST";
				Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
			} catch (Exception e) {
				logger.log(Level.ERROR, "ADD REQUEST REFUND EXCHANGE RATE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	public static void update(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL
						+ "/auto-confirm-refund/exchange-rate/update";
				String requestMethod = "POST";
				Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
			} catch (Exception e) {
				logger.log(Level.ERROR, "UPDATE REQUEST REFUND EXCHANGE RATE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	public static void delete(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL
						+ "/auto-confirm-refund/exchange-rate/delete";
				String requestMethod = "POST";
				Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
			} catch (Exception e) {
				logger.log(Level.ERROR, "DELETE REQUEST REFUND EXCHANGE RATE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}
}
