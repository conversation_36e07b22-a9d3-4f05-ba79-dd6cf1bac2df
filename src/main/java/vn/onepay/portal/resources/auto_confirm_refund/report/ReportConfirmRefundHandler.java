package vn.onepay.portal.resources.auto_confirm_refund.report;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.utils.RoutePool;

public class ReportConfirmRefundHandler implements IConstants {

	private static final Logger logger = Logger.getLogger(ReportConfirmRefundHandler.class.getName());
	private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");

	public static void search(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				JsonObject parseParams = Util.parseJsonObject(params);
				Util.sendResponse(ctx, 200, ReportConfirmRefundDao.search(parseParams));
			} catch (Exception e) {
				logger.log(Level.WARNING, "SEARCH REPORT CONFIRM REFUND ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void exportExcelReport(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				Map<String, Object> parseParams = Util.parseJsonObject(params).getMap();

				// init file download object
				FileDownloadDto fileDownloadDto = new FileDownloadDto();
				fileDownloadDto.setUser(ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                fileDownloadDto.setFile_type("report_confirm_refund");
				fileDownloadDto.setExt("xls");
				String fileName = "REPORT_CONFIRM_REFUND_" + System.currentTimeMillis();
				String fileHashName = Convert.hash(fileName);
				fileDownloadDto.setFile_name(fileName);
				fileDownloadDto.setFile_hash_name(fileHashName);
				FileDownloadDao.insert(fileDownloadDto);

				// requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

				String requestPath = RoutePool.EXPORT_EXCEL_REPORT_CONFIRM_REFUND;
                QueueProducer.sendMessage(new Message(parseParams, JMSMessageCreator.MEDIUM_PRIORITY, 
                        QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), requestPath, requestData));
				Util.sendResponse(ctx, 200, fileDownloadDto);
			} catch (Exception e) {
				logger.log(Level.WARNING, "EXPORT EXCEL REPORT CONFIRM REFUND ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void exportExcelDetailTrans(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				String type = params.get("type");
				Map<String, Object> parseParams = Util.parseJsonObject(params).getMap();

				// init file download object
				FileDownloadDto fileDownloadDto = new FileDownloadDto();
				fileDownloadDto.setUser(ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                fileDownloadDto.setFile_type("report_confirm_refund_detail_trans");
				fileDownloadDto.setExt("xls");
				String fileName = "REPORT_CONFIRM_REFUND_DETAIL_TRANS_" + type.toUpperCase() + "_" + System.currentTimeMillis();
				String fileHashName = Convert.hash(fileName);
				fileDownloadDto.setFile_name(fileName);
				fileDownloadDto.setFile_hash_name(fileHashName);
				FileDownloadDao.insert(fileDownloadDto);

				// requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

				String requestPath = RoutePool.EXPORT_EXCEL_REPORT_CONFIRM_REFUND_DETAIL_TRANS;
                QueueProducer.sendMessage(new Message(parseParams, JMSMessageCreator.MEDIUM_PRIORITY, 
                        QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), requestPath, requestData));
				Util.sendResponse(ctx, 200, fileDownloadDto);
			} catch (Exception e) {
				logger.log(Level.WARNING, "EXPORT EXCEL REPORT CONFIRM REFUND DETAIL TRANS ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void getMerchants(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				Util.sendResponse(ctx, 200, ReportConfirmRefundDao.getMerchants());
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET MERCHANTS ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void getPartners(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				Util.sendResponse(ctx, 200, ReportConfirmRefundDao.getPartners());
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET MERCHANTS ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void getLastestReportDate(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				Util.sendResponse(ctx, 200, ReportConfirmRefundDao.getLastestReportDate());
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET LASTEST REPORT DATE: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void getRequestRefundTransaction(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				String reportId = params.get("reportId");
				String type = params.get("type");
				Long pageActive = Long.parseLong(params.get("pageActive"));
				Long pageSize = Long.parseLong(params.get("pageSize"));
				Util.sendResponse(ctx, 200,
						ReportConfirmRefundDao.getRequestRefundTransaction(reportId, null, type, pageActive, pageSize));
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET REQUEST REFUND TRANSACTION ERROR: ", e);
				ctx.fail(e);
			}

		}, false, null);
	}

	public static void getDetailAccountBalance(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				Long reportId = Long.parseLong(params.get("reportId"));
				Util.sendResponse(ctx, 200, ReportConfirmRefundService.getDetailAccountBalance(reportId));
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET DETAIL ACCOUNT BALANCE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	public static void getInfoMailSending(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/report/get-info-mail-sending";
				String requestMethod = "GET";
				Util.sendResponseFromOtherServiceVer2(ctx, requestURI, requestMethod);
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET INFO MAIL SENDING ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	public static void createMailRequest(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL
						+ "/auto-confirm-refund/report/create_mail_request";
				String requestMethod = "POST";
				Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET INFO MAIL SENDING ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	public static void alertRefundPendingExchangeRateDate(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				Long fromDateMils = Long.parseLong(params.get("fromDate"));
				Long toDateMils = Long.parseLong(params.get("toDate"));
				String session = params.get("session");
				Instant instantfromDate = Instant.ofEpochMilli(fromDateMils);
				Instant instanttoDate = Instant.ofEpochMilli(toDateMils);
				LocalDate fromDate = instantfromDate.atZone(ZoneId.systemDefault()).toLocalDate();
				LocalDate toDate = instanttoDate.atZone(ZoneId.systemDefault()).toLocalDate();
				Util.sendResponse(ctx, 200, ReportConfirmRefundService.alertRefundPendingExchangeRate(fromDate, toDate, session));
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET ALERT REFUND PENDING EXCHANGE RATE DATE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}

	/**
	 * Xử lý và trả kết quả khi lấy thông tin build báo cáo y/c hoàn trả
	 * @param ctx
	 */
	public static void alertRefundPendingBuildingState(RoutingContext ctx) {
		ctx.vertx().executeBlocking(future -> {
			try {
				HttpServerRequest request = ctx.request();
				MultiMap params = request.params();
				Long fromDateMils = Long.parseLong(params.get("fromDate"));
				Long toDateMils = Long.parseLong(params.get("toDate"));
				String session = params.get("session");
				Instant instantfromDate = Instant.ofEpochMilli(fromDateMils);
				Instant instanttoDate = Instant.ofEpochMilli(toDateMils);
				LocalDate fromDate = instantfromDate.atZone(ZoneId.systemDefault()).toLocalDate();
				LocalDate toDate = instanttoDate.atZone(ZoneId.systemDefault()).toLocalDate();
				Util.sendResponse(ctx, 200, ReportConfirmRefundService.alertRefundPendingBuildingState(fromDate, toDate, session));
			} catch (Exception e) {
				logger.log(Level.WARNING, "GET ALERT REFUND PENDING EXCHANGE RATE DATE ERROR: ", e);
				ctx.fail(e);
			}
		}, false, null);
	}
}
