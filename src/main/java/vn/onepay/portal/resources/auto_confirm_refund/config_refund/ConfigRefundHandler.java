package vn.onepay.portal.resources.auto_confirm_refund.config_refund;

import java.util.logging.Level;
import java.util.logging.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;

public class ConfigRefund<PERSON>andler implements IConstants {
    private static final Logger logger = Logger
            .getLogger(ConfigRefundHandler.class.getName());
    private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
           try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                Util.sendResponse(ctx, 200, ConfigRefundDao.search(params));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
           }

        }, false, null);
    }

    public static void add(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/add";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "ADD CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/auto-confirm-refund/config-refund/update-state";
                String requestMethod = "POST";
                Util.sendResponseFromOtherService(ctx, requestURI, requestMethod);
            } catch (Exception e) {
                logger.log(Level.WARNING, "UPDATE STATE CONFIG REFUND ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
