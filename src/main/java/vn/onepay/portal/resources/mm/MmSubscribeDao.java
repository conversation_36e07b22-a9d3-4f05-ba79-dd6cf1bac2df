package vn.onepay.portal.resources.mm;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.mm.dto.MmSubscribeDto;
import vn.onepay.portal.resources.role_management.RoleManagementDao;
import vn.onepay.portal.resources.sale.dto.SaleSubscribeDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.stream.Collectors;
import java.util.*;

public class MmSubscribeDao extends Db implements IConstants {
    // Thanhnv update API gan don vi
    private static final String GET_MM_NAME = "{call PKG_MM_SUBSCRIBE.get_mm_name(?,?,?)}";
    private static final String INSERT_USER_MM_BY_UID_AND_MMID = "{call PKG_MM_SUBSCRIBE.insert_mm_by_user_id(?,?)}";
    private static final String GET_MM_BY_USER_ID = "{call PKG_MM_SUBSCRIBE.get_mm_by_user_id(?,?,?,?)}";

    public static Map getMMName() throws Exception {
        Map<String, Object> mapMerchant = new HashMap<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall(GET_MM_NAME);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);

            if (nResult != 200)
                throw new Exception("DB get_mm_name error: " + sResult);
            while (rs != null && rs.next()) {
                if (Objects.nonNull(Util.getColumnString(rs, "S_EMAIL")))
                    mapMerchant.put(Util.getColumnString(rs, "S_EMAIL"), Util.getColumnInteger(rs, "N_ID"));
            }

        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        if (exception != null)
            throw exception;

        return mapMerchant;
    }

    public static ActionDto insertByUserIdAndMMIds(Integer userId, List<Integer> mmIds) throws Exception {
        Exception exception = null;
        ActionDto actionDto = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(INSERT_USER_MM_BY_UID_AND_MMID);
            for (Integer mmId : mmIds) {
                cs.setInt(1, userId);
                cs.setInt(2, mmId);
                cs.addBatch();
            }
            cs.executeBatch();

            actionDto = new ActionDto().sResult("OK").nResult(200);
        } catch (Exception e) {
            logger.log(Level.FINE, "INSERT PARTNER BY USER ID AND MM ID ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static List<MmSubscribeDto> getMMByUserId(Integer userId) throws Exception {
        List<MmSubscribeDto> mmSubscribeDtos = new ArrayList<>();
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(GET_MM_BY_USER_ID);
            cs.setInt(1, userId);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);

            cs.execute();
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            rs = (ResultSet) cs.getObject(4);

            if (nResult != 200)
                throw new Exception("getMMByUserId error: " + sResult);
            MmSubscribeDto mmSubscribeDto;
            while (rs != null && rs.next()) {
                mmSubscribeDto = new MmSubscribeDto();
                mmSubscribeDto.setId(Util.getColumnInteger(rs, "N_MM_ID"));
                mmSubscribeDto.setEmail(Util.getColumnString(rs, "S_EMAIL"));
                mmSubscribeDtos.add(mmSubscribeDto);
            }

        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        if (exception != null)
            throw exception;

        return mmSubscribeDtos;
    }
}
