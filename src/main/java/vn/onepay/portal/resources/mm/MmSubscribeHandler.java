package vn.onepay.portal.resources.mm;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.mm.dto.MmSubscribeDto;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import static vn.onepay.portal.Util.sendResponse;

public class MmSubscribeHandler implements IConstants {
    private static Logger logger = Logger.getLogger(MmSubscribeHandler.class.getName());

    public static void loadAllMM(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> mapResult = MmSubscribeDao.getMMName();
                sendResponse(rc, 200, mapResult);
            } catch (Exception e) {
                logger.log(Level.FINE, "Role Management - Load all mm error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

    // TBL_MM_SUBSCRIBE
    public static void insertUserMM(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = rc.getBodyAsJson();
                Integer userId = bodyJson.getInteger("user_id");
                List<Integer> mmIds = (List<Integer>) bodyJson.getJsonArray("mm_id").getList();
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;
                sendResponse(rc, 200, MmSubscribeDao.insertByUserIdAndMMIds(userId, mmIds));
            } catch (Exception e) {
                logger.log(Level.FINE, "Insert User MM With User Id And List MM Id Error: ", e);
                rc.fail(e);
            }
        }, false, null);
    }

     // Thanhnv update api don vi

     public static void getMMByUserId(RoutingContext rc) {
        rc.vertx().executeBlocking(future -> {
            try {
                String userId = rc.request().getParam(("id"));
                if (userId == null || userId.equals(""))
                    throw IErrors.VALIDATION_ERROR;
                List<MmSubscribeDto> mmSubscribeDtos = MmSubscribeDao.getMMByUserId(Integer.parseInt(userId));
                BaseList<MmSubscribeDto> resultList = new BaseList<>();
                resultList.setList(mmSubscribeDtos);
                resultList.setTotalItems(mmSubscribeDtos.size());
                sendResponse(rc, 200, resultList);
            } catch (Exception ex) {
                logger.log(Level.FINE, "Get list mms by user id error ", ex);
                rc.fail(ex);
            }
        }, false, null);
    }
}
