package vn.onepay.portal.resources.exchange_rate;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.exchange_rate.dto.ExchangeRateQueryDto;
import vn.onepay.portal.resources.exchange_rate.dto.PartnerDto;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExchangeRateDAO extends Db {
    private static final Logger _LOGGER = Logger.getLogger(ExchangeRateDAO.class.getName());
    private static String LIST_PARTNER = "SELECT N_ID, S_SHORT_NAME FROM ONEDATA.TB_PN_PARTNER WHERE N_ACTIVE = 1 ORDER BY S_SHORT_NAME";
    private static BaseList<String> setList;
    //check commit
    public static BaseList<Map<String, Object>> getListExchangeRate(Date from, Date to, String page, String pageSize,String selectedPartner,String selectedtype) throws SQLException {
        SQLException exception = null;
        BaseList<Map<String, Object>> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.get_exchange_rate(?,?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setDate(4, new  java.sql.Date(from.getTime()));
            cs.setDate(5, new  java.sql.Date(to.getTime()));
            cs.setString(6, page);
            cs.setString(7, pageSize);
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.setString(9,selectedPartner);
            cs.setString(10,"%"+selectedtype+"%");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                if (rs != null) {
                    result = new BaseList<>(Util.resultSetToList(rs), cs.getInt(8));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<Map> download(ExchangeRateQueryDto query) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map> result=new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.get_download_exchange_rate(?,?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString (4,query.getFromDate());
            cs.setString(5, query.getToDate());
            cs.setString(6, query.getPage().toString());
            cs.setString(7, query.getPageSize().toString());
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.setInt(9,query.getpartnerId());
            cs.setString(10,"%"+query.getType()+"%");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate download: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bind(rs));
                }
                // if (rs != null) {
                //     result.add(bind(rs));
                // }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map bind(ResultSet rs) throws SQLException {

        Map<String, Object> map = new HashMap<>();
        map.put("N_ID",rs.getString("N_ID"));
        map.put("S_CURRENCY_CODE",rs.getString("S_CURRENCY_CODE"));
        map.put("D_ADVANCE",rs.getString("D_ADVANCE"));
        map.put("N_ADV_SELLING_RATE",rs.getString("N_ADV_SELLING_RATE"));
        map.put("N_ADV_BUYING_RATE",rs.getString("N_ADV_BUYING_RATE"));
        map.put("S_ADVANCE_TYPE",rs.getString("S_ADVANCE_TYPE"));
        map.put("S_SHORT_NAME",rs.getString("S_SHORT_NAME"));
        map.put("RNUM",rs.getString("RNUM"));
        return map;
    }
    public static Integer total(ExchangeRateQueryDto query) throws SQLException, ParseException {
        SQLException exception = null;
        Integer total = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null ;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.get_download_exchange_rate(?,?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString (4,query.getFromDate());
            cs.setString(5, query.getToDate());
            cs.setString(6, query.getPage().toString());
            cs.setString(7, query.getPageSize().toString());
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.setInt(9,query.getpartnerId());
            cs.setString(10,"%"+query.getType()+"%");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate download: " + error);
            } else {
                if (rs != null) {
                    total = cs.getInt(8);
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }
    public static BaseList<PartnerDto> getPartners() throws SQLException {
        BaseList<PartnerDto> baseList = new BaseList<>();
        List<PartnerDto> lstResult = new ArrayList<>();
        SQLException exception = null;
        PartnerDto partnerDto;
        PartnerDto partnerDtoinit;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            ps = con.prepareStatement(LIST_PARTNER);
            rs = ps.executeQuery();
            partnerDtoinit = new PartnerDto();
            partnerDtoinit.setPartnerId(0);
            partnerDtoinit.setShortName("All Partner");
            lstResult.add(partnerDtoinit);
            while (rs != null && rs.next()) {
                partnerDto = new PartnerDto();
                partnerDto.setPartnerId(rs.getInt(1));
                partnerDto.setShortName(rs.getString(2));

                lstResult.add(partnerDto);
            }
            baseList.setList(lstResult);
            baseList.setTotalItems(lstResult.size());
        } catch (SQLException ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        if (exception != null)
            throw exception;

        return baseList;
    }
    public static BaseList<Integer> getExchangeStatus() throws SQLException {
        List<Integer> lstResult = new ArrayList<>();
        BaseList<Integer> result =new BaseList<>();
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        logger.info(" get exchange status ");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.GET_EXCHANGE_RATE_STATUS(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                while (rs != null && rs.next()) {
                    lstResult.add(Util.getColumnInteger(rs, "n_partner_id"));
                }
            }
            result.setList(lstResult);
            result.setTotalItems(lstResult.size());
        } catch (SQLException ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return result;
    }

    public static BaseList<Integer> getExchangeSpecial() throws Exception {
        List<Integer> lstResult = new ArrayList<>();
        BaseList<Integer> result =new BaseList<>();
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        logger.info(" get exchange status ");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.GET_SPECIAL_PARTNER_ID(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                while (rs != null && rs.next()) {
                    lstResult.add(Util.getColumnInteger(rs, "n_partner_id"));
                }
            }
            result.setList(lstResult);
            result.setTotalItems(lstResult.size());
        } catch (SQLException ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return result;
    }
    public static boolean createUpdateExchangeRate(JsonObject body) throws SQLException {
        SQLException exception = null;
        boolean result = false;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.create_update_exchange_rate(?,?,?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, body.getString("advanceType"));
            cs.setDouble(4, body.getDouble("advBuying"));
            cs.setDouble(5, body.getDouble("advSelling"));
            cs.setString(6, "USD");
            cs.setDate(7,  new java.sql.Date(body.getLong("dateAdvance")));
            cs.setInt(8, body.getInteger("id"));
            cs.setDate(9, new java.sql.Date(body.getLong("d_Advance")));
            cs.setInt(10, body.getInteger("partnerId"));
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                result = true;
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
