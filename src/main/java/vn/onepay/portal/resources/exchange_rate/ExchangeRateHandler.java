package vn.onepay.portal.resources.exchange_rate;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.AdvanceServiceClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.exchange_rate.dto.ExchangeRateQueryDto;
import vn.onepay.portal.resources.exchange_rate.dto.PartnerDto;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class ExchangeRateHandler  implements IConstants{
    private ExchangeRateHandler() {}

    private static final Logger _LOGGER = Logger.getLogger(ExchangeRateHandler.class.getName());

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> request = Util.convertRequestParamsToMap(ctx);
                Date from = new Date(Long.parseLong(request.get("from")));
                Date to = new Date(Long.parseLong(request.get("to")));
                Util.sendResponse(ctx, 200, ExchangeRateDAO.getListExchangeRate(from, to, request.get("page"), request.get("pageSize"),request.get("selectedPartner"),request.get("selectedType")));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    public static void getPartners(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                BaseList<PartnerDto> lstResult = ExchangeRateDAO.getPartners();
                Util.sendResponse(ctx, 200, lstResult);
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "Exchange rate - Error get list partners", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
    public static void getPartnersStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                BaseList<Integer> lstResult = ExchangeRateDAO.getExchangeStatus();
                Util.sendResponse(ctx, 200, lstResult);
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "Exchange rate - Error get list status", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
    public static void getSpecial(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                BaseList<Integer> lstResult = ExchangeRateDAO.getExchangeSpecial();
                Util.sendResponse(ctx, 200, lstResult);
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "Exchange rate - Error get list partners", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
    public static void createUpdateExchangeRate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject request = ctx.getBodyAsJson();
                double advBuying = request.getDouble("advBuying");
                double advSelling = request.getDouble("advSelling");
                // check if advBuying and advSelling is between 18000 and 30000
                if (advBuying < 18000 || advBuying > 30000 || advSelling < 18000 || advSelling > 30000) {
                    throw new IllegalArgumentException("advBuying and advSelling must be between 18000 and 30000");
                }
                int resultCode = ExchangeRateDAO.createUpdateExchangeRate(request) ? 200 : 400;
                // if(resultCode== 200){
                //     AdvanceServiceClient.updateExchangeRate();
                // }
                Util.sendResponse(ctx, 200, Map.of("status", resultCode));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "createUpdateExchangeRate: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                ExchangeRateQueryDto query = gson.fromJson(ctx.getBodyAsString(), ExchangeRateQueryDto.class);
                query.setPage(0);
                query.setPageSize(Integer.MAX_VALUE);
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                // int totalRows = ExchangeRateDAO.total(query);

                Date from = new SimpleDateFormat("dd/MM/yyyy").parse(query.getFromDate());
                Date to = new SimpleDateFormat("dd/MM/yyyy").parse(query.getToDate());
                BaseList<Map<String, Object>> listB = ExchangeRateDAO.getListExchangeRate(from,to,query.getPage().toString(),query.getPageSize().toString(),query.getpartnerId().toString(),query.getType());
                int totalRows = listB.getTotalItems();

                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "exchange-rate" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", query);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("exchange-rate");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(query));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(query, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "download: exchange - rate", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    private static final Gson gson = new Gson();
}

