package vn.onepay.portal.resources.exchange_rate.dto;

import java.io.Serializable;

public class ExchangeRateQueryDto implements Serializable {

    private Integer page;
    private Integer pageSize;
    private String fromDate;
    private String toDate;
    private String id;
    private String type;
    private String currencyCode;
    private String currencyName;
    private Integer active;
    private Integer advSellingRate;
    private Integer advBuyingRate;
    private Integer partnerId;
    private String partnerName;



    public ExchangeRateQueryDto() {}
    public ExchangeRateQueryDto(Integer page, Integer pageSize, String fromDate, String toDate, String id, String type, String currencyCode, String currencyName, Integer active, Integer advSellingRate, Integer advBuyingRate, Integer partnerId, String partnerName) {
        this.page = page;
        this.pageSize = pageSize;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.id = id;
        this.type = type;
        this.currencyCode = currencyCode;
        this.currencyName = currencyName;
        this.active = active;
        this.advSellingRate = advSellingRate;
        this.advBuyingRate = advBuyingRate;
        this.partnerId = partnerId;
        this.partnerName = partnerName;
    }
    public Integer getPage() {
        return page;
    }
    public String getpartnerName() {
        return partnerName;
    }
    public void setpartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
    public Integer getpartnerId() {
        return partnerId;
    }
    public void setpartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }
    public Integer getadvBuyingRate() {
        return advBuyingRate;
    }
    public void setadvBuyingRate(Integer advBuyingRate) {
        this.advBuyingRate = advBuyingRate;
    }
    public Integer getadvSellingRate() {
        return advSellingRate;
    }
    public void setadvSellingRate(Integer advSellingRate) {
        this.advSellingRate = advSellingRate;
    }
    public Integer getActive() {
        return active;
    }
    public void setActive(Integer active) {
        this.active = active;
    }
    public String getcurrencyName() {
        return currencyName;
    }
    public void setcurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }
    public String getcurrencyCode() {
        return currencyCode;
    }
    public void setcurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getToDate() {
        return toDate;
    }
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }
    public String getFromDate() {
        return fromDate;
    }
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }
    public Integer getPageSize() {
        return pageSize;
    }
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    public void setPage(Integer page) {
        this.page = page;
    }



}
