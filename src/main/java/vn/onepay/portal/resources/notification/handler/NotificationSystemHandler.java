package vn.onepay.portal.resources.notification.handler;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantTransfer.MerchantTransferDao;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantTransferDto;
import vn.onepay.portal.resources.notification.dao.NotificationSystemDao;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.ReadExcel;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.commons.util.Convert;
import static vn.onepay.portal.Util.sendResponse;
import static java.nio.charset.StandardCharsets.UTF_8;
public class NotificationSystemHandler implements IConstants {
    private static final Logger _LOGGER = Logger.getLogger(NotificationSystemHandler.class.getName());

    public static void loadService(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            try {
                List<Map> data = NotificationSystemDao.loadService();
                response.put(DATA, data);
                sendResponse(ctx, OK, response);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "loadService error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    //load notiType
    public static void loadNotiTypeName(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            try {
                List<Map> data = NotificationSystemDao.loadNotiTypeName();
                response.put(DATA, data);
                sendResponse(ctx, OK, response);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "loadNotiType error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    //load notitype by n_id
    public static void loadNotiTypeByNId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                int nId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Map data = NotificationSystemDao.loadNotiTypeById(nId);
                response.put(DATA, data);
                sendResponse(ctx, OK, response);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "loadNotiType error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    //load email group
    public static void loadEmailGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            try {
                List<Map> data = NotificationSystemDao.loadEmailGroup();
                response.put(DATA, data);
                sendResponse(ctx, OK, response);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "loadEmailGroup error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
    //function search noti
    public static void searchNotification(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String fromDate = request.getParam(FROM_DATE) == null ? BLANK : request.getParam(FROM_DATE);
                String toDate = request.getParam(TO_DATE) == null ? BLANK : request.getParam(TO_DATE);

                JsonObject mIn = new JsonObject();
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(N_ID_BATCH, request.getParam(N_ID_BATCH) == null ? BLANK : Convert.parseInt(request.getParam(N_ID_BATCH),0));
                mIn.put(NOTI_TYPE, request.getParam(NOTI_TYPE) == null ? BLANK : request.getParam(NOTI_TYPE));
                mIn.put(SERVICE, request.getParam(SERVICE) == null ? BLANK : request.getParam(SERVICE));
                mIn.put(STATE, request.getParam(STATE) == null ? BLANK : request.getParam(STATE));
                mIn.put(GROUP, request.getParam(GROUP) == null ? BLANK : request.getParam(GROUP));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID) == null ? BLANK : request.getParam(MERCHANT_ID));
                mIn.put(EMAIL, request.getParam(EMAIL) == null ? BLANK : request.getParam(EMAIL));
                mIn.put(PARTNER_NAME, request.getParam(PARTNER_NAME) == null ? BLANK : request.getParam(PARTNER_NAME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> notiResult = NotificationSystemDao.searchNotification(mIn);
                sendResponse(ctx, 200, notiResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    //function load noti by n_id
    public static void loadNotificationById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int nId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Map<String, Object> notiResult = NotificationSystemDao.loadNotificationById(nId);
                sendResponse(ctx, 200, notiResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    //function update batch email
    public static void updateBatchEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> resultReturn = new HashMap<>();

                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                JsonObject mIn = new JsonObject();
                int idBatchEmail = body.getInteger(N_ID) == null ? 0 : body.getInteger(N_ID);
                boolean isUpdateGroup = false;
                // String services = request.getParam(SERVICE) == null ? BLANK : request.getParam(SERVICE);
                
                mIn.put(N_ID, idBatchEmail);
                mIn.put(N_ID_TEMPLATE, body.getString(N_ID_TEMPLATE) == null ? BLANK : Convert.parseInt(body.getString(N_ID_TEMPLATE), 0) );
                int idGroup = body.getValue(N_ID_GROUP) == null ? 0 : Convert.parseInt(body.getValue(N_ID_GROUP)+"", 0);
                int idLastGroup = body.getValue(LAST_GROUP) == null ? 0 : Convert.parseInt(body.getValue(LAST_GROUP)+"", 0);
                mIn.put(N_ID_GROUP, idGroup);
                mIn.put(S_SUBJECT, body.getString(S_SUBJECT) == null ? BLANK : body.getString(S_SUBJECT));
                mIn.put(S_CONTENT, body.getString(S_CONTENT) == null ? BLANK : body.getString(S_CONTENT));
                mIn.put(D_START, body.getString(D_START) == null ? BLANK : body.getString(D_START));
                mIn.put(S_FILE_NAME, body.getString(S_FILE_NAME) == null ? BLANK : body.getString(S_FILE_NAME));
                    
                _LOGGER.log(Level.INFO, "Before update id group: " + idLastGroup + " After update group: " + idGroup);
                //todo: build lại list contact theo group
                if (idGroup!=0 && (idGroup!=idLastGroup || idBatchEmail == 0)){
                    //delete email log
                    if (idBatchEmail != 0){
                        Map<String, Object> deleteEmailLogResult = NotificationSystemDao.deleteEmailLog(idBatchEmail);
                        resultReturn.putAll(deleteEmailLogResult);
                    }
                    isUpdateGroup=true;
                }
                
                Map<String, Object> notiResult = NotificationSystemDao.updateBatchEmail(mIn);
                //check response notiResult if success then update email log
                if (notiResult.get("nerror").equals(200) && isUpdateGroup){
                    //insert email log get from group 
                    Map<String, Object> insertEmailLogResult = NotificationSystemDao.insertEmailLogFromGroup(idBatchEmail!=0?idBatchEmail:(int)notiResult.get("idBatch"), idGroup);
                    resultReturn.putAll(insertEmailLogResult);
                }

                notiResult.putAll(resultReturn);
                sendResponse(ctx, 200, notiResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* Sync email log by batch id */
    public static void syncEmailLog(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idBatchEmail = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idBatchEmail!=0) {
                    Map<String, Object> result = NotificationSystemDao.syncEmailLog(idBatchEmail);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idBatchEmail is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    //upload file attach
    public static void uploadFileAttach(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                for (FileUpload fileUpload : fileUploadSet) {
                    Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                    byte[] bArray = uploadedFile.getBytes();
                    String fileName = URLDecoder.decode(fileUpload.fileName(), UTF_8.toString());
                    // String idEmailBatch = request.getParam(ID_EMAIL_BATCH) == null ? BLANK : request.getParam(ID_EMAIL_BATCH);
                    String idEmailBatch = ctx.pathParam(ID) == null ? BLANK : ctx.pathParam(ID);
                    int idEmailBatchInt = Convert.parseInt(idEmailBatch, 0);
                    if (idEmailBatchInt!=0) {
                        Map<String, Object> result = NotificationSystemDao.updateAttachBatchEmail(idEmailBatchInt,bArray, fileName);
                        sendResponse(ctx, 200, result);
                    } else {
                        sendResponse(ctx, 400, "idEmailBatch is null");
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* update state batch email */
    public static void updateStateBatchEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idBatchEmail = body.getString(N_ID_BATCH) == null ? 0 : Convert.parseInt(body.getString(N_ID_BATCH),0);
                String state = body.getString(S_STATE) == null ? "" : body.getString(S_STATE);
                if (idBatchEmail!=0 && !state.equals("")) {
                    Map<String, Object> result = NotificationSystemDao.updateStateBatchEmail(idBatchEmail, state);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idBatchEmail or state is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    //hàm so sánh dữ liệu giữa serviceList và dataServicesBatch nếu cùng số lượng và giá trị item thì trả về true
    public static boolean compareList(String[] serviceList, List<Integer> dataServicesBatch) {
        if(serviceList == null && dataServicesBatch == null){
            return true;
        }
        if(serviceList == null || dataServicesBatch == null){
            return false;
        }
        if(serviceList.length != dataServicesBatch.size()){
            return false;
        }
        for (int i = 0; i < serviceList.length; i++) {
            if(!dataServicesBatch.contains(Integer.parseInt(serviceList[i]))){
                return false;
            }
        }
        return true;
    }
    /* search email log */
    public static void searchEmailLog(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject mIn = new JsonObject();
                mIn.put(N_ID_BATCH, request.getParam(N_ID_BATCH) == null ? BLANK : Convert.parseInt(request.getParam(N_ID_BATCH),0));
                mIn.put(EMAIL, request.getParam(S_EMAIL) == null ? BLANK : request.getParam(S_EMAIL));
                mIn.put(PARTNER_NAME, request.getParam(S_PARTNER) == null ? BLANK : request.getParam(S_PARTNER));
                mIn.put(MERCHANT_ID, request.getParam(S_MERCHANT_ID) == null ? BLANK : request.getParam(S_MERCHANT_ID));
                mIn.put(STATE, request.getParam(S_STATE) == null ? BLANK : request.getParam(S_STATE));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> emailLogResult = NotificationSystemDao.searchEmailLog(mIn);
                sendResponse(ctx, 200, emailLogResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* search group email by name or state */
    public static void searchGroupEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject mIn = new JsonObject();
                mIn.put(S_NAME, request.getParam(S_NAME) == null ? BLANK : request.getParam(S_NAME));
                mIn.put(S_STATE, request.getParam(S_STATE) == null ? BLANK : request.getParam(S_STATE));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> groupEmailResult = NotificationSystemDao.searchGroupEmail(mIn);
                sendResponse(ctx, 200, groupEmailResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* update group email state by id */
    public static void updateGroupEmailState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idGroup = body.getString(N_ID_GROUP) == null ? 0 : Convert.parseInt(body.getString(N_ID_GROUP),0);
                String state = body.getString(S_STATE) == null ? "" : body.getString(S_STATE);
                if (idGroup!=0 && !state.equals("")) {
                    Map<String, Object> result = NotificationSystemDao.updateGroupEmailState(idGroup, state);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idGroup or state is null");
                }
                
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* load group mail by id */
    public static void loadGroupEmailById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idGroup = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idGroup!=0) {
                    Map<String, Object> groupEmailResult = NotificationSystemDao.loadGroupEmailById(idGroup);
                    sendResponse(ctx, 200, groupEmailResult);
                } else {
                    sendResponse(ctx, 400, "idGroup is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* search list email in group */
    public static void searchEmailInGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject mIn = new JsonObject();
                mIn.put(N_ID_GROUP, request.getParam(N_ID_GROUP) == null ? BLANK : Convert.parseInt(request.getParam(N_ID_GROUP),0));
                mIn.put(S_EMAIL, request.getParam(S_EMAIL) == null ? BLANK : request.getParam(S_EMAIL));
                mIn.put(S_MERCHANT_ID, request.getParam(S_MERCHANT_ID) == null ? BLANK : request.getParam(S_MERCHANT_ID));
                mIn.put(S_PARTNER_NAME, request.getParam(S_PARTNER_NAME) == null ? BLANK : request.getParam(S_PARTNER_NAME));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> emailInGroupResult = NotificationSystemDao.searchEmailInGroup(mIn);
                sendResponse(ctx, 200, emailInGroupResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* update/insert group mail */
    public static void updateGroupEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                JsonObject mIn = new JsonObject();
                int idGroup = body.getInteger(N_ID_GROUP) == null ? 0 : body.getInteger(N_ID_GROUP);
                String services = body.getString(S_SERVICE) == null ? BLANK : body.getString(S_SERVICE);
                mIn.put(N_ID_GROUP, idGroup);
                mIn.put(S_NAME, body.getString(S_NAME) == null ? BLANK : body.getString(S_NAME));
                mIn.put(S_BUILD, "");
                String[] serviceList = null;
                if (services!=null && !services.trim().isEmpty()){
                    serviceList = services.split(",");
                }
                Map<String, Object> updateGroupResult = null;
                if(idGroup!=0){
                    //get service by batch email id
                    List<Integer> dataServicesBatch = NotificationSystemDao.getServiceByGroupEmailId(idGroup);
                    //so sánh dữ liệu giữa serviceList và dataServicesBatch nếu cùng số lượng và giá trị item thì trả về true
                    boolean check = compareList(serviceList, dataServicesBatch);
                    if(!check){
                        //print log before and after update services
                        _LOGGER.log(Level.INFO, "Before update services: " + dataServicesBatch+ " After update services: " + serviceList);
                        if(idGroup != 0){
                            //delete service on batch
                            Map<String, Object> deleteServiceResult = NotificationSystemDao.deleteServiceGroupEmail(idGroup);
                            //if service deleted then insert new service
                            if(deleteServiceResult.get("nerrorDeleteServiceGroup").equals(200)){    
                                //insert service
                                NotificationSystemDao.insertServiceGroupEmail(idGroup, serviceList);
                            }
                        }
                        mIn.put(S_BUILD, "wait");
                    }
                    updateGroupResult = NotificationSystemDao.updateGroupEmail(mIn);
                }else{
                    //insert new group
                    mIn.put(S_BUILD, "wait");
                    updateGroupResult = NotificationSystemDao.updateGroupEmail(mIn);
                    if(updateGroupResult.get("nerror").equals(200) && serviceList!=null && serviceList.length>0){
                        _LOGGER.log(Level.INFO, "Insert new group email: " + updateGroupResult.get("idGroup")+"|"+serviceList.length+"|"+services);
                        idGroup = (int)updateGroupResult.get("idGroup");
                        //insert service
                        NotificationSystemDao.insertServiceGroupEmail(idGroup, serviceList);
                    }
                }
                sendResponse(ctx, Integer.parseInt(updateGroupResult.get("nerror")+""), updateGroupResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* delete email in group by id */
    public static void deleteEmailInGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idEmail = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idEmail!=0) {
                    Map<String, Object> result = NotificationSystemDao.deleteEmailInGroup(idEmail);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idEmail is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* upload file contact import to group */
    public static void uploadFileContact(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                for (FileUpload fileUpload : fileUploadSet) {
                    _LOGGER.log(Level.INFO,"file upload: "+ fileUpload.uploadedFileName()+ "| file Type: " + fileUpload.contentType());
                    //read file upload as excel file and insert to database
                    Map<String, Object> mTemp = new HashMap<>();
                    mTemp.put("row_start", 1);
                    mTemp.put("sheet_number", 0);
                    List<String> columns = new ArrayList<>();
                    columns.add("merchantId");
                    columns.add("partnerName");
                    mTemp.put("list_column_name", columns);
                    List<Map<String, Object>> listImport = new ArrayList<>();
                    // if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(fileUpload.contentType())) {
                    //     listImport = ReadExcel.readExcelXlsx2(fileUpload.uploadedFileName(), mTemp);
                    // } else if ("application/vnd.ms-excel".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXls2(fileUpload.uploadedFileName(), mTemp);
                    // } else {
                    //     // sendResponse(ctx, 201, IErrors.INVALID_FILE_MUST_EXCEL);
                    //     throw IErrors.INVALID_FILE_MUST_EXCEL;
                    // }
                    if (listImport == null || listImport.isEmpty()) {
                        throw IErrors.VALIDATION_ERROR;
                        // sendResponse(ctx, 202, IErrors.VALIDATION_ERROR);
                    }
                    //tách listImport làm 2 listMerchantId và listPartner, với dòng có cả 2 giá trị thì lấy giá trị merchantId và không lấy giá trị partner
                    List<String> listMerchantId = new ArrayList<>();
                    List<String> listPartner = new ArrayList<>();
                    _LOGGER.log(Level.INFO,"list import: "+ listImport.size() + "|"+ listImport.toString());
                    for (Map<String, Object> map : listImport) {
                        String merchantId = map.get("merchantId") == null ? "" : map.get("merchantId").toString();
                        String partnerName = map.get("partnerName") == null ? "" : map.get("partnerName").toString();
                        if (!merchantId.equals("") && !merchantId.equalsIgnoreCase("NA") ) {
                            listMerchantId.add(merchantId);
                        } 
                        if (!partnerName.equals("") && !partnerName.equalsIgnoreCase("NA")) {
                            listPartner.add(partnerName);
                        }
                    }
                    //gọi đến loadEmailByListMerchantId truyền vào listMerchantId, nếu listMerchantId nhiều hơn 100 item chia làm nhiều lần gọi hàm, kết quả trả về gộp nhiều lần vào 1 list
                    List<String> listEmail = new ArrayList<>();
                    if (!listMerchantId.isEmpty()) {
                        for(int i = 0; i < listMerchantId.size(); i+=100){
                            List<String> listMerchantIdTemp = listMerchantId.subList(i, Math.min(listMerchantId.size(), i + 100));
                            //gọi đến loadEmailByListMerchantId truyền vào listMerchantIdTemp (max 100 item)
                            List<String> listEmailTemp = NotificationSystemDao.loadEmailByListMerchantId(listMerchantIdTemp);
                            listEmail.addAll(listEmailTemp);
                        }
                    }
                    //gọi đến loadEmailByListPartner truyền vào listPartner, nếu listPartner nhiều hơn 100 item chia làm nhiều lần gọi hàm, kết quả trả về gộp nhiều lần vào 1 list và check trùng lặp
                    if (!listPartner.isEmpty()) {
                        for(int i = 0; i < listPartner.size(); i+=100){
                            List<String> listPartnerTemp = listPartner.subList(i, Math.min(listPartner.size(), i + 100));
                            //gọi đến loadEmailByListPartner truyền vào listPartnerTemp (max 100 item)
                            List<String> listEmailTemp = NotificationSystemDao.loadEmailByListPartnerName(listPartnerTemp);
                            listEmail.addAll(listEmailTemp);
                        }
                    }
                    //loại bỏ email trùng lặp
                    List<String> listEmailDistinct = new ArrayList<>();
                    for (String email : listEmail) {
                        if (!listEmailDistinct.contains(email)) {
                            listEmailDistinct.add(email);
                        }
                    }
                    String idGroup = ctx.pathParam(ID) == null ? BLANK : ctx.pathParam(ID);
                    int idGroupInt = Convert.parseInt(idGroup, 0);
                    if (idGroupInt!=0){
                        //insert listEmailDistinct vào group qua hàm insertEmailToGroup
                        Map<String, Object> result = NotificationSystemDao.insertEmailToGroup(listEmailDistinct, idGroupInt);
                        //updateGroupEmailBuild
                        Map<String, Object> updateGroupResult = NotificationSystemDao.updateGroupEmailBuild(idGroupInt,"done");
                        sendResponse(ctx, 200, result);
                    }else{
                        sendResponse(ctx, 400, "idGroup is null");
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* update email in group by id email */
    public static void updateEmailInGroup(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idEmail = body.getValue(N_ID) == null ? 0 : Convert.parseInt(body.getValue(N_ID)+"",0);
                String email = body.getString(S_EMAIL) == null ? "" : body.getString(S_EMAIL);
                if (idEmail!=0 && !email.equals("")) {
                    Map<String, Object> result = NotificationSystemDao.updateEmailInGroup(idEmail, email);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idEmail or email is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /* search teamplate by input keyword */   
    public static void  searchTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject mIn = new JsonObject();
                mIn.put(S_KEYWORD, request.getParam(S_KEYWORD) == null ? BLANK : request.getParam(S_KEYWORD));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Convert.parseInt(request.getParam(PAGE),0));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : Convert.parseInt(request.getParam(PAGE_SIZE),0));
                
                Map<String, Object> templateResult = NotificationSystemDao.searchTemplate(mIn);
                sendResponse(ctx, 200, templateResult);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* update/insert teamplate */   
public static void updateTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                JsonObject mIn = new JsonObject();
                int idTemplate = body.getValue(N_ID) == null ? 0 : Convert.parseInt(body.getValue(N_ID)+"",0);

                mIn.put(N_ID_TEMPLATE, idTemplate);
                mIn.put(S_NAME, body.getString(S_NAME) == null ? BLANK : body.getString(S_NAME));
                mIn.put(S_CONTENT, body.getString(S_CONTENT) == null ? BLANK : body.getString(S_CONTENT));
                Map<String, Object> resultReturn = NotificationSystemDao.updateTemplate(mIn);
                sendResponse(ctx, Integer.parseInt(resultReturn.get("nerror")+""), resultReturn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* delete teamplate by id */ 
    public static void deleteTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idTemplate = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idTemplate!=0) {
                    Map<String, Object> result = NotificationSystemDao.deleteTemplate(idTemplate);
                    sendResponse(ctx, 200, result);
                } else {
                    sendResponse(ctx, 400, "idTemplate is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* load teamplate by id */
    public static void loadTemplateById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idTemplate = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                if (idTemplate!=0) {
                    Map<String, Object> templateResult = NotificationSystemDao.loadTemplateById(idTemplate);
                    sendResponse(ctx, 200, templateResult);
                } else {
                    sendResponse(ctx, 400, "idTemplate is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* approve teamplate by id */   
    public static void approveTemplate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                int idTemplate = body.getValue(N_ID) == null ? 0 : Convert.parseInt(body.getValue(N_ID)+"",0);
                String state = body.getString(S_STATE) == null ? "" : body.getString(S_STATE);
                Map<String, Object> result = NotificationSystemDao.approveTemplate(idTemplate,state);
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    private static final String TEMPLATE_FILE_PATH = Config.getString("notification.template_file_path", "");
    private static final String TEMPLATE_FILE_NAME = Config.getString("notification.template_merchant_group_file", "");
    public static void downloadTempFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String[] arr = TEMPLATE_FILE_NAME.split("\\.");
                String fileName = arr[0];
                String fileExt = arr[1];

                Path requestPath = FileSystems.getDefault().getPath(TEMPLATE_FILE_PATH + File.separator + TEMPLATE_FILE_NAME).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                        Map<String, Object> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                // logger.info("Download Success");
                            } else {
                                // logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception ex) {
                // logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
    /* load history log from tb_audit_trail by id template */
    public static void loadHistoryLog(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idTemplate = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID)+"");
                if (idTemplate!=0) {
                    Map<String, Object> historyLogResult = NotificationSystemDao.loadHistoryLog(idTemplate);
                    sendResponse(ctx, 200, historyLogResult);
                } else {
                    sendResponse(ctx, 400, "idTemplate is null");
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    /* download file b_lob from db by ud email batch */
    public static void downloadAttachFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int idEmailBatch = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                String fileNameFull = request.getParam(S_FILE_NAME) == null ? BLANK : request.getParam(S_FILE_NAME);
                if (idEmailBatch!=0) {
                    Map<String, Object> resultLoadFile = NotificationSystemDao.getFileAttachByIdBatch(idEmailBatch,fileNameFull);
                    if (resultLoadFile.get("nerror").equals(200)) {
                        String[] arr = fileNameFull.split("\\.");
                        String fileName = arr[0];
                        String fileExt = arr[1];

                        String filePath = resultLoadFile.get("filePath") == null ? BLANK : resultLoadFile.get("filePath").toString();
                        Path requestPath = FileSystems.getDefault().getPath(filePath).normalize();
                        FileSystem fs = ctx.vertx().fileSystem();
                        fs.exists(requestPath.toString(), ar -> {
                            if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                                Map<String, Object> data = new HashMap<>();
                                data.put(ParamsPool.PATH_FILE, requestPath.toString());
                                data.put(ParamsPool.FILE_NAME, fileName);
                                data.put(ParamsPool.FILE_EXT, fileExt);
                                try {
                                    data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                                } catch (IOException e) {
                                    ctx.fail(e);
                                }
                                ctx.response().setChunked(true);

                                String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                                String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                                String contentType = "application/octet-stream";
                                if (extFile.equals("zip")) {
                                    contentType = "application/zip";
                                }
                                ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                                ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                                ctx.response().sendFile(requestPath.toString(), result -> {
                                        if (result.succeeded()) {
                                            _LOGGER.log(Level.INFO,"Download Success");
                                        } else {
                                            _LOGGER.log(Level.INFO,"Download Fail");
                                        }
                                    });
                            } else {
                                ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                            }
                        });
                    }
                }
                        
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
    //upload file img to server
    public static void uploadFileIMG(RoutingContext ctx) {
        _LOGGER.log(Level.INFO,"upload IMG CKEDITOR ");
        ctx.vertx().executeBlocking(future -> {
            try {
                _LOGGER.log(Level.INFO,"upload IMG CKEDITOR "+ctx.fileUploads().size());
                HttpServerRequest request = ctx.request();
                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                for (FileUpload fileUpload : fileUploadSet) {
                    _LOGGER.log(Level.INFO,"file upload: "+ fileUpload.uploadedFileName()+ "| file Type: " + fileUpload.contentType());
                    Buffer uploadedFile = ctx.vertx().fileSystem().readFileBlocking(fileUpload.uploadedFileName());
                    byte[] bArray = uploadedFile.getBytes();
                    String fileName = URLDecoder.decode(fileUpload.fileName(), UTF_8.toString());
                    String idIMG = UUID.randomUUID().toString();
                    Map<String, Object> result = NotificationSystemDao.insertIMGAttachCKeditor(idIMG,bArray);
                    if(result.get("nerror").equals(200)){
                        JsonObject response = new JsonObject();
                        response.put("url", "https://secure.onepay.vn/iportal/api/v1/notification-system/img-file/"+idIMG);
                        sendResponse(ctx, OK, response);
                    }else{
                        sendResponse(ctx, 400, result);
                    }
                   
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
            // JsonObject response = new JsonObject();
            // response.put("url", "https://secure.onepay.vn/iportal/api/v1/notification-system/img-file/d8762552-3cae-4481-a859-904068a2f6b3");
            // sendResponse(ctx, OK, response);
        }, false, null);
    }
    //load file img from db
    public static void loadFileIMG(RoutingContext ctx) {
        _LOGGER.log(Level.INFO,"load IMG CKEDITOR ");
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String idIMG = request.getParam(ID) == null ? "" : request.getParam(ID);
                // String fileNameFull = request.getParam(S_FILE_NAME) == null ? BLANK : request.getParam(S_FILE_NAME);
                if (!idIMG.equals("")) {
                    Map<String, Object> resultLoadFile = NotificationSystemDao.getFileAttachByIdIMG(idIMG);
                    if (resultLoadFile.get("nerror").equals(200)) {
                        // String[] arr = fileNameFull.split("\\.");
                        // String fileName = arr[0];
                        // String fileExt = arr[1];

                        String filePath = resultLoadFile.get("filePath") == null ? BLANK : resultLoadFile.get("filePath").toString();
                        String fileExt = resultLoadFile.get("ext") == null ? BLANK : resultLoadFile.get("ext").toString();
                        Path requestPath = FileSystems.getDefault().getPath(filePath).normalize();
                        FileSystem fs = ctx.vertx().fileSystem();
                        fs.exists(requestPath.toString(), ar -> {
                            if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                                Map<String, Object> data = new HashMap<>();
                                data.put(ParamsPool.PATH_FILE, requestPath.toString());
                                data.put(ParamsPool.FILE_NAME, idIMG);
                                data.put(ParamsPool.FILE_EXT, fileExt);
                                try {
                                    data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                                } catch (IOException e) {
                                    ctx.fail(e);
                                }
                                ctx.response().setChunked(true);

                                String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                                String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                                String contentType = "application/octet-stream";
                                if (extFile.equals("zip")) {
                                    contentType = "application/zip";
                                }
                                ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                                ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                                ctx.response().sendFile(requestPath.toString(), result -> {
                                        if (result.succeeded()) {
                                            _LOGGER.log(Level.INFO,"Download Success");
                                        } else {
                                            _LOGGER.log(Level.INFO,"Download Fail");
                                        }
                                    });
                            } else {
                                ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                            }
                        });
                    }
                }
                        
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
