package vn.onepay.portal.resources.notification.dao;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.s;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;
import vn.onepay.portal.resources.audit_trail.response.AuditTrail;
import vn.onepay.portal.resources.bank_mid_management.dao.BankMidDao;
import vn.onepay.portal.resources.base.dto.IErrors;
import static vn.onepay.portal.utils.RoutePool.FILE_UPLOAD;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.io.*;

public class NotificationSystemDao extends Db implements IConstants {

    //Hàm load dữ liệu danh sách service
    public static List<Map> loadService() {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<Map> lists = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_service(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_service error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map m = new HashMap<>();
                    m.put("n_id", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("d_create", Util.getColumnDate(rs, "d_create"));
                    m.put("d_update", Util.getColumnDate(rs, "d_update"));
                    m.put("s_desc", Util.getColumnString(rs, "s_desc"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;

    }

    //hàm load noti type từ scheduler load_noti_type
    public static List<Map> loadNotiTypeName() {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<Map> lists = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_noti_type_name(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_noti_type error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map m = new HashMap<>();
                    m.put("n_id", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("d_create", Util.getColumnDate(rs, "d_create"));
                    m.put("d_update", Util.getColumnDate(rs, "d_update"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;

    }
    //hàm load noti type từ scheduler load_noti_type_by_id tham số truyền vào là n_id của noti type
    public static Map loadNotiTypeById(int id) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        Map m = new HashMap<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_noti_type_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_noti_type_by_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    m.put("n_id", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_content", Util.getColumnString(rs, "s_content"));
                    m.put("d_create", Util.getColumnDate(rs, "d_create"));
                    m.put("d_update", Util.getColumnDate(rs, "d_update"));
                    m.put("s_desc", Util.getColumnString(rs, "s_desc"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return m;

    }
    //hàm load email group từ procedure load_email_group
    public static List<Map> loadEmailGroup() {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<Map> lists = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_group(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_group error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map m = new HashMap<>();
                    m.put("n_id", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("d_create", Util.getColumnDate(rs, "d_create"));
                    m.put("d_update", Util.getColumnDate(rs, "d_update"));
                    m.put("s_desc", Util.getColumnString(rs, "s_desc"));
                    m.put("s_group_contact", Util.getColumnString(rs, "s_group_contact"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;

    }

    public static Map<String, Object> searchNotification(JsonObject mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.search_email_batch(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, mIn.getInteger(N_ID_BATCH));
            cs.setString(6, mIn.getString(FROMDATE));
            cs.setString(7, mIn.getString(TODATE));
            cs.setString(8, mIn.getString(NOTI_TYPE));
            cs.setString(9, mIn.getString(SERVICE));
            cs.setString(10, mIn.getString(STATE));
            cs.setString(11, mIn.getString(GROUP));
            cs.setString(12, mIn.getString(MERCHANT_ID));
            cs.setString(13, mIn.getString(EMAIL));
            cs.setString(14, mIn.getString(PARTNER_NAME));
            cs.setInt(15, mIn.getInteger(PAGE));
            cs.setInt(16, mIn.getInteger(PAGE_SIZE));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.search_email_batch error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>(); // Parameterize the Map object
                    m.put("n_id_batch", Util.getColumnInteger(rs, "n_id"));
                    m.put("d_start", Util.getColumnString(rs, "d_start"));
                    m.put("d_end", Util.getColumnDate(rs, "d_end"));
                    m.put("n_total", Util.getColumnInteger(rs, "n_total"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("n_sent", Util.getColumnInteger(rs, "n_sent"));
                    m.put("n_fail", Util.getColumnInteger(rs, "n_fail"));
                    m.put("s_subject", Util.getColumnString(rs, "s_subject"));
                    m.put("s_content", Util.getColumnString(rs, "s_content"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    m.put("s_file_name", Util.getColumnString(rs, "s_file_name"));
                    m.put("n_id_template", Util.getColumnInteger(rs, "n_id_template"));
                    m.put("s_template_name", Util.getColumnString(rs, "s_template_name"));
                    m.put("n_id_group", Util.getColumnInteger(rs, "n_id_group"));
                    m.put("s_group_name", Util.getColumnString(rs, "s_group_name"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }
    /* load Notification by n_id*/
    public static Map loadNotificationById(int id) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        Map m = new HashMap<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_batch_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_batch_by_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    m.put("n_id_batch_mail", Util.getColumnInteger(rs, "n_id"));
                    m.put("d_start", Util.getColumnString(rs, "d_start"));
                    m.put("d_end", Util.getColumnString(rs, "d_end"));
                    m.put("n_total", Util.getColumnInteger(rs, "n_total"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("n_sent", Util.getColumnInteger(rs, "n_sent"));
                    m.put("n_fail", Util.getColumnInteger(rs, "n_fail"));
                    m.put("s_subject", Util.getColumnString(rs, "s_subject"));
                    m.put("s_content", Util.getColumnString(rs, "s_content"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    m.put("d_approved", Util.getColumnString(rs, "d_approved"));
                    m.put("s_file_name", Util.getColumnString(rs, "s_file_name"));
                    m.put("n_id_template", Util.getColumnInteger(rs, "n_id_template"));
                    m.put("s_template_name", Util.getColumnString(rs, "s_template_name"));
                    m.put("n_id_group", Util.getColumnInteger(rs, "n_id_group"));
                    m.put("s_group_name", Util.getColumnString(rs, "s_group_name"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        System.out.println(m);
        return m;
    }

    /* function update batch email*/   
    public static Map<String, Object> updateBatchEmail(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        int idBatch = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_batch(?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, mIn.getInteger(N_ID));
            cs.setInt(4, mIn.getInteger(N_ID_TEMPLATE));
            cs.setString(5, mIn.getString(S_FILE_NAME));
            cs.setInt(6, mIn.getInteger(N_ID_GROUP));
            cs.setString(7, mIn.getString(S_SUBJECT));
            cs.setString(8, mIn.getString(S_CONTENT));
            cs.setString(9, mIn.getString(D_START));
            cs.registerOutParameter(10, OracleTypes.NUMBER);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            idBatch = cs.getInt(10);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_batch error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        result.put("idBatch", idBatch);
        return result;
    }
    /* function update filed BLOB file attachment on batch email*/
    public static Map<String, Object> updateAttachBatchEmail(int idBatchEmail,byte[] bArray, String fileName) {
        logger.log(Level.INFO, "updateAttachBatchEmail idBatchEmail="+idBatchEmail+"|fileName="+fileName);
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            InputStream inputStream = new ByteArrayInputStream(bArray);
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_batch_blob(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idBatchEmail);
            cs.setBinaryStream(4, inputStream, bArray.length);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            logger.log(Level.INFO, "updateAttachBatchEmail error="+error+"|nerror="+nerror);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_batch_blob error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* function update state batch mail */
    public static Map<String, Object> updateStateBatchEmail(int idBatchEmail, String state) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_state_email_batch(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idBatchEmail);
            cs.setString(4, state);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_state_email_batch error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error_state", error);
        result.put("nerror", nerror);
        return result;
    }
    /* function delete email log by batch id*/
    public static Map<String, Object> deleteEmailLog(int idBatchEmail) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.delete_log_mail_by_batch(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idBatchEmail);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.delete_log_mail_by_batch error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error_delete_email_log", error);
        result.put("nerror_delete_email_log", nerror);
        return result;
    }
    /* insert email log get from list email in a group, param input p_n_id_batch and p_n_id_group call procedure insert_log_mail_from_group */
    public static Map<String, Object> insertEmailLogFromGroup(int idBatchEmail, int idGroup) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.insert_log_mail_from_group(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idBatchEmail);
            cs.setInt(4, idGroup);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.insert_log_mail_from_group error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error_insert_mail_from_group", error);
        result.put("nerror_insert_mail_from_group", nerror);
        return result;
    }
    /* Sync email log by batch id */
    public static Map<String, Object> syncEmailLog(int idBatchEmail) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.sync_email_by_batch(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idBatchEmail);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.sync_email_by_batch error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* function lấy danh sách email từ procedure load_email_by_service_qt*/
    public static List<Map> loadEmailByServiceQt(int idService) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<Map> lists = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_by_service_qt(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_by_service_qt error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map m = new HashMap<>();
                    m.put("n_id", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_email", Util.getColumnString(rs, "s_email"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_phone", Util.getColumnString(rs, "s_phone"));
                    m.put("s_address", Util.getColumnString(rs, "s_address"));
                    m.put("s_group_contact", Util.getColumnString(rs, "s_group_contact"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }
    /* function search email log */
    public static Map<String, Object> searchEmailLog(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        int totalEmail = 0;
        int emailSuccess = 0;
        int emailFail = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.search_email_log(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, mIn.getInteger(N_ID_BATCH));
            cs.setString(6, mIn.getString(PARTNER_NAME));
            cs.setString(7, mIn.getString(MERCHANT_ID));
            cs.setString(8, mIn.getString(EMAIL));
            cs.setString(9, mIn.getString(STATE));
            cs.setInt(10, mIn.getInteger(PAGE));
            cs.setInt(11, mIn.getInteger(PAGE_SIZE));
            cs.registerOutParameter(12, OracleTypes.NUMBER);
            cs.registerOutParameter(13, OracleTypes.NUMBER);
            cs.registerOutParameter(14, OracleTypes.NUMBER);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            totalEmail = cs.getInt(12);
            emailSuccess = cs.getInt(13);
            emailFail = cs.getInt(14);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.search_email_log error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("n_id_email", Util.getColumnInteger(rs, "n_id"));
                    m.put("n_id_batch", Util.getColumnInteger(rs, "n_id_batch"));
                    m.put("s_email", Util.getColumnString(rs, "s_email"));
                    m.put("s_merchant_id", Util.getColumnString(rs, "s_merchant_id"));
                    m.put("s_partner_name", Util.getColumnString(rs, "s_partner_name"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        result.put("totalEmail", totalEmail);
        result.put("emailSuccess", emailSuccess);
        result.put("emailFail", emailFail);
        return result;
    }
    /* function search group email by name or state */
    public static Map<String, Object> searchGroupEmail(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.search_email_group(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, mIn.getString(S_NAME));
            cs.setString(6, mIn.getString(S_STATE));
            cs.setInt(7, mIn.getInteger(PAGE));
            cs.setInt(8, mIn.getInteger(PAGE_SIZE));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.search_email_group error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("n_id_group", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("s_build", Util.getColumnString(rs, "s_build"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    m.put("s_desc", Util.getColumnString(rs, "s_desc"));
                    m.put("s_group_contact", Util.getColumnString(rs, "s_group_contact"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }
    /* function update group email state by id*/
    public static Map<String, Object> updateGroupEmailState(int idGroup, String state) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_group_state(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idGroup);
            cs.setString(4, state);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_group_state error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* load group mail by id */
    public static Map loadGroupEmailById(int id) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsService = null;
        String error = "";
        int nerror = 0;
        Map m = new HashMap<>();
        List<Integer> listService = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_group_by_id(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.registerOutParameter(5, OracleTypes.CURSOR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_group_by_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    m.put("n_id_group", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("s_build", Util.getColumnString(rs, "s_build"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    m.put("s_desc", Util.getColumnString(rs, "s_desc"));
                    m.put("s_group_contact", Util.getColumnString(rs, "s_group_contact"));
                }
            }
            rsService = (ResultSet) cs.getObject(5);
            if (rsService != null) {
                while (rsService.next()) {
                    listService.add(Util.getColumnInteger(rsService, "n_id_service"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        m.put("serviceList", listService);
        return m;
    }

    /* function delete service on group*/   
    public static Map<String, Object> deleteServiceGroupEmail(int idGroupEmail) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.delete_service_on_group(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idGroupEmail);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.delete_service_on_group error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("errorDeleteServiceGroup", error);
        result.put("nerrorDeleteServiceGroup", nerror);
        return result;
    }

    /* function insert list service assigned on a group mail*/   
    public static void insertServiceGroupEmail(int idGroupEmail, String[] serviceList) {
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            con.setAutoCommit(false);
            for (String service : serviceList) {
                if(service!=null){
                    cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.insert_service_on_group(?,?,?,?)}");
                    cs.registerOutParameter(1, OracleTypes.NUMBER);
                    cs.registerOutParameter(2, OracleTypes.VARCHAR);
                    cs.setInt(3, idGroupEmail);
                    cs.setInt(4,  Integer.parseInt(service));
                    cs.executeQuery();
                    error = cs.getString(2);
                    nerror = cs.getInt(1);
                    if (nerror != 200) {
                        logger.log(Level.SEVERE, "P_NOTI_SYSTEM.insert_service_group_email error: {0}", error);
                    }
                    cs.close();
                }
            }
            con.commit();
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            if (con!=null) {
                try {
                    con.setAutoCommit(true);
                } catch (SQLException e) {
                    logger.log(Level.SEVERE, "Failed to set auto commit: ", e);
                }
            }
            closeConnectionDB(null, null, cs, con);
            
        }
    }
    

    /* get list service by id group email*/   
    public static List<Integer> getServiceByGroupEmailId(int idGroupEmail) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<Integer> lists = new ArrayList<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.get_service_by_group_email_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, idGroupEmail);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.get_service_by_group_email_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(Util.getColumnInteger(rs, "N_ID_SERVICE"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }
    /* search list email in group */
    public static Map<String, Object> searchEmailInGroup(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.search_email_in_group(?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, mIn.getInteger(N_ID_GROUP));
            cs.setString(6, mIn.getString(S_EMAIL));
            cs.setString(7, mIn.getString(S_MERCHANT_ID));
            cs.setString(8, mIn.getString(S_PARTNER_NAME));
            cs.setInt(9, mIn.getInteger(PAGE));
            cs.setInt(10, mIn.getInteger(PAGE_SIZE));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.search_email_in_group error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("n_id_email_group", Util.getColumnInteger(rs, "n_id"));
                    m.put("n_id_group", Util.getColumnInteger(rs, "n_id_group"));
                    m.put("s_email", Util.getColumnString(rs, "s_email"));
                    m.put("s_merchant_id", Util.getColumnString(rs, "s_merchant_id"));
                    m.put("s_partner_name", Util.getColumnString(rs, "s_partner_name"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }
    /* update/insert group mail */
    public static Map<String, Object> updateGroupEmail(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        int idGroup = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_group(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, mIn.getInteger(N_ID_GROUP));
            cs.setString(4, mIn.getString(S_NAME));
            cs.setString(5, mIn.getString(S_BUILD));
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            idGroup = cs.getInt(6);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_group error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        result.put("idGroup", idGroup);
        return result;
    }
    /* delete email in group by id */
    public static Map<String, Object> deleteEmailInGroup(int idEmailGroup) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.delete_email_in_group(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idEmailGroup);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.delete_email_in_group error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* load list email by list merchantid input dạng table, đầu vào hàm là ArrayList<String> listMerchantId */
    public static List<String> loadEmailByListMerchantId(List<String> listMerchantId) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<String> lists = new ArrayList<>();
        try {
            con = getConnection118();
            String listMerchantIdString = String.join(",", listMerchantId);
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_by_list_merchantid(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, listMerchantIdString);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_by_list_merchantid error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(Util.getColumnString(rs, "s_email"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }
    /* load list email by list partnerName input dạng table, đầu vào hàm là ArrayList<String> listPartnerName */
    public static List<String> loadEmailByListPartnerName(List<String> listPartnerName) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        List<String> lists = new ArrayList<>();
        try {
            con = getConnection118();
            String listPartnerString = String.join(",", listPartnerName);
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_by_list_partnername(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, listPartnerString);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_by_list_partnername error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(Util.getColumnString(rs, "s_email"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }
    /* function insert list email to group,input là List<String> listEmail, auto commit false, commit mỗi 100 email */
    public static Map<String, Object> insertEmailToGroup(List<String> listEmail, int idGroup) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            con.setAutoCommit(false);
            for (int i = 0; i < listEmail.size(); i++) {
                cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.insert_email_to_group(?,?,?,?)}");
                cs.registerOutParameter(1, OracleTypes.NUMBER);
                cs.registerOutParameter(2, OracleTypes.VARCHAR);
                cs.setInt(3, idGroup);
                cs.setString(4, listEmail.get(i));
                cs.executeQuery();
                error = cs.getString(2);
                nerror = cs.getInt(1);
                if (nerror != 200) {
                    logger.log(Level.SEVERE, "P_NOTI_SYSTEM.insert_email_to_group error: {0}", error);
                }
                if (i % 100 == 0) {
                    con.commit();
                }
                cs.close();
            }
            con.commit();
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            if (con!=null) {
                try {
                    con.setAutoCommit(true);
                } catch (SQLException e) {
                    logger.log(Level.SEVERE, "Failed to set auto commit: ", e);
                }
            }
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }  
    /* update group email s_build */
    public static Map<String, Object> updateGroupEmailBuild(int idGroup, String build) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_group_build(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idGroup);
            cs.setString(4, build);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_group_build error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* update email in group by id email */
    public static Map<String, Object> updateEmailInGroup(int idEmailGroup, String email) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_in_group(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idEmailGroup);
            cs.setString(4, email);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_in_group error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* search teamplate by input keyword */   
    public static Map<String, Object> searchTemplate(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total = 0;
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.search_email_template(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, mIn.getString(S_KEYWORD));
            cs.setInt(6, mIn.getInteger(PAGE));
            cs.setInt(7, mIn.getInteger(PAGE_SIZE));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.search_email_template error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("n_id_template", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("s_content", Util.getColumnString(rs, "s_content"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }
    /* update/insert teamplate */   
    public static Map<String, Object> updateTemplate(JsonObject mIn) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        int idTemplate = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.update_email_template(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, mIn.getInteger(N_ID_TEMPLATE));
            cs.setString(4, mIn.getString(S_NAME));
            cs.setString(5, mIn.getString(S_CONTENT));
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            idTemplate = cs.getInt(6);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_template error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        result.put("idTemplate", idTemplate);
        return result;
    }
    /* delete teamplate by id */ 
    public static Map<String, Object> deleteTemplate(int idTemplate) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.delete_email_template(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idTemplate);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.delete_email_template error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* load teamplate by id */
    public static Map loadTemplateById(int id) {
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        Map m = new HashMap<>();
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_email_template_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.load_email_template_by_id error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    m.put("n_id_template", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_name", Util.getColumnString(rs, "s_name"));
                    m.put("s_state", Util.getColumnString(rs, "s_state"));
                    m.put("s_content", Util.getColumnString(rs, "s_content"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    m.put("d_update", Util.getColumnString(rs, "d_update"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return m;
    }
    /* approve teamplate by id */   
    public static Map<String, Object> approveTemplate(int idTemplate,String state) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.approve_email_template(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, idTemplate);
            cs.setString(4, state);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.approve_email_template error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }
    /* load history log from tb_audit_trail by id template */
    public static Map<String, Object> loadHistoryLog(int idTemplate) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.load_history_log_by_id_template(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, idTemplate);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB P_NOTI_SYSTEM.load_history_log_by_id_template error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("n_id_audit", Util.getColumnInteger(rs, "n_id"));
                    m.put("s_user", Util.getColumnString(rs, "s_user"));
                    m.put("s_action", Util.getColumnString(rs, "s_action"));
                    m.put("d_create", Util.getColumnString(rs, "d_create"));
                    lists.add(m);
                }
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        return result;
    }

    /* get the file attach in b_attached blob from DB by id batch and write to file */
    public static Map<String, Object> getFileAttachByIdBatch(int idBatch,String fileName) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            cs = con.prepareCall("select b_attached from TB_EMAIL_BATCH where n_id=?");
            cs.setInt(1, idBatch);
            rs = cs.executeQuery();
            Blob blob = null;
            while (rs.next()) {
                blob = rs.getBlob("b_attached");
            }
            logger.info("lob=" + blob);
            InputStream in = blob.getBinaryStream();
            // Define output file
            File file = new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName);
            OutputStream outputStream = new FileOutputStream(new File(Config.getString("file.export_location","/opt/iportal-service/exports") + fileName));
            result.put("filePath",file.getAbsolutePath());
            logger.info("filePath=" + file.getAbsolutePath());
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int bytesRead;
            byte[] buffer = new byte[4096];
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.writeTo(outputStream);

            in.close();
            out.close();
            outputStream.close();
            error="OK";
            nerror=200;
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error=e.getMessage();
            nerror=300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* get the file MIG attach in b_attached blob from DB by id batch and write to file */
    public static Map<String, Object> getFileAttachByIdIMG(String idIMG) {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection118();
            //query get S_EXT từ table onemon.TB_IMG theo id idIMG
            cs = con.prepareCall("select s_ext from onemon.TB_IMG where s_id=?");
            cs.setString(1, idIMG);
            rs = cs.executeQuery();
            String ext = "";
            while (rs.next()) {
                ext = rs.getString("s_ext");
                result.put("ext",ext);
            }
            rs.close();
            cs.close();
            if(!ext.equals("")){
                File file = new File(Config.getString("file.export_location_img","/opt/iportal-service/exports/img") + "/"+idIMG+"."+ext);
                if(!file.exists()){
                    cs = con.prepareCall("select b_attached from onemon.TB_IMG where s_id=?");
                    cs.setString(1, idIMG);
                    rs = cs.executeQuery();
                    Blob blob = null;
                    while (rs.next()) {
                        blob = rs.getBlob("b_attached");
                    }
                    logger.info("lob=" + blob);
                    InputStream in = blob.getBinaryStream();
                    // Define output file
                    file = new File(Config.getString("file.export_location_img","/opt/iportal-service/exports/img/") + idIMG+"."+ext);
                    OutputStream outputStream = new FileOutputStream(new File(Config.getString("file.export_location_img","/opt/iportal-service/exports/img/") + "/"+ idIMG+"."+ext));
                    result.put("filePath",file.getAbsolutePath());
                    logger.info("filePath=" + file.getAbsolutePath());
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    int bytesRead;
                    byte[] buffer = new byte[4096];
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    out.writeTo(outputStream);

                    in.close();
                    out.close();
                    outputStream.close();
                }else{
                    result.put("filePath",file.getAbsolutePath());
                }
            }
            
            error="OK";
            nerror=200;
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
            error=e.getMessage();
            nerror=300;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

    /* function update filed BLOB file IMG CKEDITOR attachment*/
    public static Map<String, Object> insertIMGAttachCKeditor(String idIMG,byte[] bArray) {
        logger.log(Level.INFO, "updateIMGAttachCKeditor id="+idIMG);
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        String error = "";
        int nerror = 0;
        try {
            InputStream inputStream = new ByteArrayInputStream(bArray);
            con = getConnection118();
            cs = con.prepareCall("{ call ONEMON.P_NOTI_SYSTEM.insert_img_ckeditor_blob(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, idIMG);
            cs.setBinaryStream(4, inputStream, bArray.length);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            logger.log(Level.INFO, "updateAttachBatchEmail error="+error+"|nerror="+nerror);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "P_NOTI_SYSTEM.update_email_batch_blob error: {0}", error);
            }
        } catch (Exception e) {
            logger.log(Level.FINE, "", e);
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        result.put("error", error);
        result.put("nerror", nerror);
        return result;
    }

}
