package vn.onepay.portal.resources.exchange_rate_admin.dto;

import java.sql.Timestamp;

public class ExchangeRate {

    private String currencyCode;
    private String currencyName;
    private Timestamp create;
    private Timestamp update;
    private Double buyingRate;
    private Double sellingRate;
    private String operator;
    
    public String getOperator() {
        return operator;
    }
    public void setOperator(String operator) {
        this.operator = operator;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    public String getCurrencyName() {
        return currencyName;
    }
    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }
    public Timestamp getCreate() {
        return create;
    }
    public void setCreate(Timestamp create) {
        this.create = create;
    }
    public Timestamp getUpdate() {
        return update;
    }
    public void setUpdate(Timestamp update) {
        this.update = update;
    }
    public Double getBuyingRate() {
        return buyingRate;
    }
    public void setBuyingRate(Double buyingRate) {
        this.buyingRate = buyingRate;
    }
    public Double getSellingRate() {
        return sellingRate;
    }
    public void setSellingRate(Double sellingRate) {
        this.sellingRate = sellingRate;
    }

}
