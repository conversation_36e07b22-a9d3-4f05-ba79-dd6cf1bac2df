package vn.onepay.portal.resources.exchange_rate_admin.handler;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.exchange_rate_admin.dao.ExchangeRateAdminDao;
import vn.onepay.portal.resources.exchange_rate_admin.dto.ExchangeRate;
import vn.onepay.portal.resources.user.UserDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

/*
 * Author: HieuNT
 * Date: 15/11/2023
 */
public class ExchangeRateAdmin implements IConstants {

    private ExchangeRateAdmin() {
    }

    private static Logger logger = Logger.getLogger(ExchangeRateAdmin.class.getName());

    public static void updateExchangeRate (RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> map = new HashMap<>();
                String xUserId = ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID);
                String userEmail = UserDao.get(xUserId).getEmail() == null ? "" : UserDao.get(xUserId).getEmail();

                // map.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                // map.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                // map.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                JsonObject data = ctx.getBodyAsJson();
                Integer result = ExchangeRateAdminDao.updateExchangeRate(data,userEmail);
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "saveBankMid: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getExchangeRate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                List<ExchangeRate> data = ExchangeRateAdminDao.getExchangeRate();

                sendResponse(ctx, 200, data);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getExchangeRate ]  => ERROR: ", e);
                sendResponse(ctx, 500, new ArrayList());
                ctx.fail(e);
            }
        }, false, null);
    }

}
