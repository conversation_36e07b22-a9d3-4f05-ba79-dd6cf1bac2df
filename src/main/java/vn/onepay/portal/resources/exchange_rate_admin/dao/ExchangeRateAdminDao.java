package vn.onepay.portal.resources.exchange_rate_admin.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.exchange_rate_admin.dto.ExchangeRate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Author: HieuNT
 * Date: 15/11/2023
 */
public class ExchangeRateAdminDao extends Db implements IConstants {

    private static final Logger logger = Logger.getLogger(ExchangeRateAdminDao.class.getName());

    private static final String UPDATE_EXCHANGE_RATE = "{call LDP.PKG_LDP.UPDATE_EXCHANGE_RATE(?,?,?,?,?,?,?) }";
    private static final String GET_EXCHANGE_RATE = "{call LDP.PKG_LDP.GET_EXCHANGE_RATE(?,?,?) }";

    public static Integer updateExchangeRate(JsonObject data, String userEmail) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_EXCHANGE_RATE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, data.getString("currencyCode"));
            cs.setString(4, data.getString("currencyName"));
            cs.setDouble(5, Double.parseDouble(data.getString("buyingRate")));
            cs.setDouble(6, Double.parseDouble(data.getString("sellingRate")));
            cs.setString(7, userEmail);
            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateExchangeRate: "  + data.getString("currencyCode")+ " " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ExchangeRate> getExchangeRate() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<ExchangeRate> exchangeRateList = new ArrayList<>();
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_EXCHANGE_RATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    ExchangeRate exchangeRate = new ExchangeRate();
                    exchangeRate.setCurrencyCode(rs.getString("S_CURRENCY_CODE"));
                    exchangeRate.setCurrencyName(rs.getString("S_CURRENCY_NAME"));
                    exchangeRate.setBuyingRate(rs.getDouble("N_BUYING_RATE"));
                    exchangeRate.setSellingRate(rs.getDouble("N_SELLING_RATE"));
                    exchangeRate.setCreate(rs.getTimestamp("D_CREATE"));
                    exchangeRate.setUpdate(rs.getTimestamp("D_UPDATE"));
                    exchangeRate.setOperator(rs.getString("S_OPERATOR"));
                    exchangeRateList.add(exchangeRate);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return exchangeRateList;
    }

}
