package vn.onepay.portal.resources.accountantManagement.misaCode;

import com.google.gson.Gson;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.accountantManagement.misaCode.dto.PartnerContractDTO;
import vn.onepay.portal.resources.accountantManagement.misaCode.dto.MisaCodeDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.ReadExcel;

import java.io.IOException;
import java.io.File;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MisaCodeHandler {
    private final static Logger logger = Logger.getLogger(MisaCodeHandler.class.getName());
    private static String templateFilePath = Config.getString("accountant.template_file_path", "");
    private static String templateFileName = "temp_misa_code_upload.xls";

    public static void listMisaCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                String keywords = httpServerRequest.getParam(ParamsPool.KEY_WORDS) == null ? "" : httpServerRequest.getParam(ParamsPool.KEY_WORDS);
                String state = httpServerRequest.getParam(ParamsPool.STATE) == null ? "" : httpServerRequest.getParam(ParamsPool.STATE);
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0 : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
                BaseList<MisaCodeDTO> listMerchant = MisaCodeDAO.listMisaCode(keywords, state, page, pageSize);
                sendResponse(ctx, 200, listMerchant);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void listPartnerContract(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                String contractCode = httpServerRequest.getParam("contract_code") == null ? "" : httpServerRequest.getParam("contract_code");
                List<PartnerContractDTO> listMerchantContract = MisaCodeDAO.listPartnerContractByContractCode(contractCode);
                Map<String, Object> result = new HashMap<>();
                result.put("list", listMerchantContract);
                sendResponse(ctx, 200, result);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void addMisaCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String body = ctx.getBodyAsString();
                MisaCodeDTO merchantDTO = new Gson().fromJson(body, MisaCodeDTO.class);

                MisaCodeDAO.insertMisaCode(merchantDTO);
                sendResponse(ctx, 201);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateMisaCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int nId = ctx.request().getParam(ParamsPool.ID) == null ? 0 : Integer.parseInt(ctx.request().getParam(ParamsPool.ID));
                if (nId == 0)
                    throw IErrors.VALIDATION_ERROR;
                String body = ctx.getBodyAsString();
                MisaCodeDTO merchantDTO = new Gson().fromJson(body, MisaCodeDTO.class);
                merchantDTO.setId(nId);
                MisaCodeDAO.updateMisaCode(merchantDTO);

                sendResponse(ctx, 200);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void deleteMisaCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int nId = ctx.request().getParam(ParamsPool.ID) == null ? 0 : Integer.parseInt(ctx.request().getParam(ParamsPool.ID));
                if (nId == 0)
                    throw IErrors.VALIDATION_ERROR;
                boolean isDeleted = MisaCodeDAO.deleteMisaCode(nId);
                int statusRes = isDeleted ? 200 : 500;
                sendResponse(ctx, statusRes, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void uploadMisaCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Set<FileUpload> fileUploadSet = ctx.fileUploads();

                FileUpload fileUpload = fileUploadSet.stream().findFirst().get();
                if (fileUpload == null)
                    throw IErrors.VALIDATION_ERROR;
                Map<String, Object> mTemp = new HashMap<>();
                mTemp.put("row_start", 3);
                mTemp.put("sheet_number", 0);
                List<String> columns = new ArrayList<>();
                columns.add("contractCode");
                columns.add("merchantId");
                columns.add("misaCode");
                columns.add("misaName");
                columns.add("address");
                columns.add("taxNumber");
                columns.add("partnerName");
                columns.add("email");
                columns.add("state");
                mTemp.put("list_column_name", columns);

                List<Map> listImport;
                try {
                    logger.info("file Type: " + fileUpload.contentType());
                    if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXlsx2(fileUpload.uploadedFileName(), mTemp);
                    } else if ("application/vnd.ms-excel".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXls2(fileUpload.uploadedFileName(), mTemp);
                    } else {
                        throw IErrors.INVALID_FILE_MUST_EXCEL;
                    }
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "ERROR TO MERCHANT ACCOUNTANT IMPORT: ", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }

                if (listImport == null || listImport.isEmpty()) {
                    logger.info("No import file data found");
                    throw IErrors.VALIDATION_ERROR;
                }
                for (int i = 0; i < listImport.size(); i++) {
                    logger.info("value import:" + new JsonObject(listImport.get(i)));
                }
                List<String> listErrors = new ArrayList<>();
                List<Map<String, Object>> listPass = new ArrayList<>();
                precheckMisaCode(listImport, listErrors, listPass);
                if (!listPass.isEmpty()) {
                    MisaCodeDAO.importMisaCode(listPass);
                }
                Map<String, List<String>> result = new HashMap<>();
                result.put("error", listErrors);
                sendResponse(ctx, 200, result);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static void precheckMisaCode(List<Map> listImport, List<String> listErrors, List<Map<String, Object>> listPass) {
        for (int i = 0; i < listImport.size(); i++) {
            Map<String, Object> t = listImport.get(i);
            String error = "";
            if (t.getOrDefault("contractCode", "").toString().trim().isEmpty()) {
                error = "contractCode is blank.";
            }
            if (t.getOrDefault("misaCode", "").toString().trim().isEmpty()) {
                error = "misaCode is blank.";
            }
            if (t.getOrDefault("taxNumber", "").toString().trim().isEmpty()) {
                error = "taxNumber is blank.";
            }
            String state = t.getOrDefault("state", "").toString().trim();

            if (state.isEmpty() || (!state.equals("actived") && !state.equals("deleted") && !state.equals("disabled"))) {
                error = "State is blank or invalid.";
            }
            if (!error.isEmpty()) {
                error = "Import failed at row " + (i + 4) + ". " + error;
                listErrors.add(error);
            } else {
                listPass.add(t);
            }
        }
    }

    public static void downloadTempFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String[] arr = templateFileName.split("\\.");
                String fileName = arr[0];
                String fileExt = arr[1];

                Path requestPath = FileSystems.getDefault().getPath(templateFilePath + File.separator + templateFileName).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && ar.result().booleanValue()) {
                        Map<String, String> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            logger.log(Level.SEVERE, "[ERROR]", e);
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                logger.info("Download Success");
                            } else {
                                logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
