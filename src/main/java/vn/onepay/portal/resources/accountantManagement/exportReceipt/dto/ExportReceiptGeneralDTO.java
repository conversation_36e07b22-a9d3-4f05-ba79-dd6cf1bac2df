package vn.onepay.portal.resources.accountantManagement.exportReceipt.dto;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 12/13/2021
 * Time: 10:23 AM
 * To change this TOOL_PAYMENT.
 */

public class ExportReceiptGeneralDTO {
    public List<ExportReceiptDetailDTO> receiptDetails;
    public ExportReceiptDTO exportReceipt;

    public List<ExportReceiptDetailDTO> getReceiptDetails() {
        return receiptDetails;
    }

    public void setReceiptDetails(List<ExportReceiptDetailDTO> receiptDetails) {
        this.receiptDetails = receiptDetails;
    }

    public ExportReceiptDTO getExportReceipt() {
        return exportReceipt;
    }

    public void setExportReceipt(ExportReceiptDTO exportReceipt) {
        this.exportReceipt = exportReceipt;
    }
}
