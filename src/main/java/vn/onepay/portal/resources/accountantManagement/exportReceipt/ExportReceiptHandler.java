package vn.onepay.portal.resources.accountantManagement.exportReceipt;

import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.*;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;
import vn.onepay.portal.utils.ParamsPool;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class ExportReceiptHandler implements IConstants {
    private final static Logger logger = Logger.getLogger(ExportReceiptHandler.class.getName());

    private static String TEMPLATE_FILE_PATH = Config.getString("export-receipt.template_file_path", "");
    private static String TEMPLATE_FILE_NAME = Config.getString("export-receipt.template_statement_file", "");

    public static void getActivePartners(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                BaseList<PartnerDto> lstResult = ExportReceiptDAO.getActivePartners();
                Util.sendResponse(ctx, 200, lstResult);
            } catch (Exception ex) {
                logger.log(Level.FINE, "Export Receipt - Error get list partners", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void getExportReceipt(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                Map mIn = new HashMap();

                SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                String fDate = body.getString(FROMDATE);
                String tDate = body.getString(TODATE);
                Date fromDate;
                Date toDate;
                try {
                    fromDate = df.parse(fDate);
                    toDate = df.parse(tDate);
                } catch (Exception ex) {
                    logger.log(Level.FINE, "Error parse date in getExportReceipt", ex);
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(fromDate, toDate);
                if (months > 12)
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                //set params
                mIn.put(FROMDATE, body.getString(FROMDATE));
                mIn.put(TODATE, body.getString(TODATE));
                mIn.put(PARTNERID, body.getString(PARTNERID) != null ? body.getString(PARTNERID) : "");
                mIn.put(ADVANCETYPE, body.getString(ADVANCETYPE) != null ? body.getString(ADVANCETYPE) : "");
                mIn.put(PAYCHANNEL, body.getString(PAYCHANNEL) != null ? body.getString(PAYCHANNEL) : "");
                mIn.put(MERCHANTCLASSIFY, body.getString(MERCHANTCLASSIFY) != null ? body.getString(MERCHANTCLASSIFY) : "");
                mIn.put(STATUS, body.getString(STATUS) != null ? body.getString(STATUS) : "");
                mIn.put(STATE, body.getString(STATE) != null ? body.getString(STATE) : "");
                mIn.put(PAGE, body.getInteger(PAGE));
                mIn.put(PAGESIZE, body.getInteger(PAGESIZE) != null ? body.getInteger(PAGESIZE) : 0);
                mIn.put(OFFSET, 0);
                mIn.put(TRANS_VALUE, body.getString(TRANS_VALUE) != null ? body.getString(TRANS_VALUE) : "");

                Util.sendResponse(ctx, 200, ExportReceiptDAO.getExportReceiptList(mIn));
            } catch (Exception ex) {
                logger.log(Level.FINE, "Export Receipt - Error getExportReceipt", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateLastReceiptNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer lastReceiptNumber = ctx.getBodyAsJson().getInteger("receiptNumber");
                Util.sendResponse(ctx, 200, ExportReceiptDAO.updateLastReceiptNumber(lastReceiptNumber));
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateReceiptNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                if (body.getString("data") == null || body.getString("lastReceiptNumber") == null || body.getString("currentReceiptNumber") == null)
                    throw IErrors.VALIDATION_ERROR;
                Integer lastReceiptNumber = Integer.parseInt(body.getString("lastReceiptNumber"));
                Integer currentReceiptNumber = Integer.parseInt(body.getString("currentReceiptNumber"));
                List<String> listUpdate = new ArrayList<>(Arrays.asList(body.getString("data").trim().split(",")));

                Util.sendResponse(ctx, 200, ExportReceiptDAO.updateReceiptNumber(listUpdate, lastReceiptNumber, currentReceiptNumber));
            } catch (Exception ex) {
                logger.log(Level.FINE, "Export Receipt - Error updateReceiptNumber", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateSettlementDate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                if (body.getString("data") == null || body.getString("date") == null)
                    throw IErrors.VALIDATION_ERROR;
                String lstUpdate = body.getString("data");
                String date = body.getString("date");

                Util.sendResponse(ctx, 200, ExportReceiptDAO.updateSettleDate(date, new ArrayList<>(Arrays.asList(lstUpdate.split(",")))));
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadTemplateFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info("========downloadTemplateFile===========");
                String[] arr = TEMPLATE_FILE_NAME.split("\\.");
                String fileName = arr[0];
                String fileExt = arr[1];

                Path requestPath = FileSystems.getDefault().getPath(TEMPLATE_FILE_PATH + "/" + TEMPLATE_FILE_NAME).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && ar.result()) {
                        Map data = new HashMap();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            logger.log(Level.SEVERE, "[ERROR]", e);
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), (result) -> {
                            if (result.succeeded()) {
                                logger.info("Download Success");
                            } else {
                                logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception ex) {
                logger.log(Level.SEVERE, "[ERROR]", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }


    public static void exportReceiptDownload(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                try {
                    JsonObject body = ctx.getBodyAsJson();
                    if (body.getString("id") == null)
                        throw IErrors.VALIDATION_ERROR;
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("id", body.getString("id"));
                    mIn.put("password", body.getBoolean("password"));

                    // requestData in message
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    // Initial file information
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
                    String date = sdf.format(new Date());
                    Calendar c = Calendar.getInstance();
                    c.set(Calendar.DAY_OF_MONTH, 1);
                    String fileName = "Input Misa " + date + " DS " + new SimpleDateFormat("MM.dd").format(c.getTime())
                            + (!isEmpty(body.getString("payment_channel")) ? ("-" + body.getString("payment_channel")) : "")
                            + (!isEmpty(body.getString("advance_type")) ? ("-" + body.getString("advance_type")) : "")
                            + "-" + new SimpleDateFormat("yyyy.MM.dd").format(new Date());
                    String fileHashName = "";
                    try {
                        fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                    } catch (NoSuchAlgorithmException e) {
                        ctx.fail(e);
                    } catch (UnsupportedEncodingException e) {
                        ctx.fail(e);
                    }
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                    fileDownloadDto.setFile_type("export_receipt");
                    fileDownloadDto.setFile_name(fileName);
                    fileDownloadDto.setFile_hash_name(fileHashName);
                    int totalRows = ExportReceiptDAO.exportReceiptDownload(mIn).size();
                    if (totalRows <= Config.getFileRowLevel()) {
                        fileDownloadDto.setExt("xls");
                    } else {
                        fileDownloadDto.setExt("zip");
                    }
                    FileDownloadDao.insert(fileDownloadDto);
                    if (totalRows <= Config.getFileRowLevel()) {
                        //fileDownload.setExt("csv");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                    } else {
                        //fileDownload.setExt("zip");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                    }
                    sendResponse(ctx, 200, fileDownloadDto);
                } catch (Exception e) {
                    logger.log(Level.FINE, "Export Receipt - export file error: ", e);
                    ctx.fail(e);
                }
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void exportReceiptDetailDownload(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                try {
                    JsonObject body = ctx.getBodyAsJson();
                    if (body.getString("id") == null)
                        throw IErrors.VALIDATION_ERROR;
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("id", body.getString("id"));
                    mIn.put("password", body.getBoolean("password"));

                    // requestData in message
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    // Initial file information
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
                    String date = sdf.format(new Date());
                    Calendar c = Calendar.getInstance();
                    c.set(Calendar.DAY_OF_MONTH, 1);
                    String fileName = "Input Misa " + date + " DS " + new SimpleDateFormat("MM.dd").format(c.getTime())
                            + (!isEmpty(body.getString("payment_channel")) ? ("-" + body.getString("payment_channel")) : "")
                            + (!isEmpty(body.getString("advance_type")) ? ("-" + body.getString("advance_type")) : "")
                            + "-" + new SimpleDateFormat("yyyy.MM.dd").format(new Date());
                    String fileHashName = "";
                    try {
                        fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                    } catch (NoSuchAlgorithmException e) {
                        ctx.fail(e);
                    } catch (UnsupportedEncodingException e) {
                        ctx.fail(e);
                    }
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                    fileDownloadDto.setFile_type("export_receipt");
                    fileDownloadDto.setFile_name(fileName);
                    fileDownloadDto.setFile_hash_name(fileHashName);
                    int totalRows = ExportReceiptDAO.exportReceiptDownload(mIn).size();
                    if (totalRows <= Config.getFileRowLevel()) {
                        fileDownloadDto.setExt("xls");
                    } else {
                        fileDownloadDto.setExt("zip");
                    }
                    FileDownloadDao.insert(fileDownloadDto);
                    if (totalRows <= Config.getFileRowLevel()) {
                        //fileDownload.setExt("csv");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                    } else {
                        //fileDownload.setExt("zip");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                    }
                    sendResponse(ctx, 200, fileDownloadDto);
                } catch (Exception e) {
                    logger.log(Level.FINE, "Export Receipt - export file error: ", e);
                    ctx.fail(e);
                }
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void getReceiptDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String receiptId = ctx.request().getParam("id");
                if (receiptId == null || receiptId.isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                Util.sendResponse(ctx, 200, ExportReceiptDAO.getReceiptDetail(Integer.parseInt(receiptId)));
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateReceiptDetailStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                String ids = body.getString("id");
                String state = body.getString("state");
                String parentIds = body.getString("parent_id");
                String reasonApprove = body.getString("reason") == null ? "" : body.getString("reason");
                if (ids == null || ids.isEmpty() || state == null || state.isEmpty() || parentIds == null || parentIds.isEmpty())
                    throw IErrors.VALIDATION_ERROR;

                String[] arrIds = ids.split(",");
                Integer[] intIds = new Integer[arrIds.length];
                for (int i = 0; i < arrIds.length; i++) {
                    intIds[i] = Convert.parseInt(arrIds[i].trim(), 0);
                }
                boolean isSuccess = ExportReceiptDAO.updateReceiptDetailState(state, intIds, reasonApprove);
                int result = isSuccess ? 200 : 500;
                if (isSuccess) {
                    String[] arrParentIds = parentIds.split(",");
                    Set<Integer> updateIds = new HashSet<>();
                    for (int i = 0; i < arrParentIds.length; i++) {
                        updateIds.add(Integer.parseInt(arrParentIds[i].trim()));
                    }
                    ExportReceiptDAO.updateFeeMonthAmount(updateIds);
                }

                Map<String, Object> map = new HashMap<>();
                map.put("nResult", result);
                Util.sendResponse(ctx, result, map);
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void approveExportReceipt(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String approveIds = ctx.getBodyAsJson().getString("approve_ids");
                if (approveIds == null || approveIds.isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                String[] arrApproveIds = approveIds.split(",");
                Integer[] intApproveIds = new Integer[arrApproveIds.length];
                for (int i = 0; i < intApproveIds.length; i++)
                    intApproveIds[i] = Integer.parseInt(arrApproveIds[i]);
                ExportReceiptDAO.updateBatchReceipt(intApproveIds, "approved");
                Integer nResult = 200;
                Map<String, Object> result = new HashMap();
                result.put("nResult", nResult);
                Util.sendResponse(ctx, nResult, result);
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void checkExportReceipt(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String ids = ctx.getBodyAsJson().getString("ids");
                if (isEmpty(ids))
                    throw IErrors.VALIDATION_ERROR;
                String[] idsArr = ids.split(",");
                Integer[] idsInt = new Integer[idsArr.length];
                for (int index = 0; index < idsArr.length; index++) {
                    idsInt[index] = Integer.parseInt(idsArr[index]);
                }
                ExportReceiptDAO.updateBatchReceipt(idsInt, "checked");
                //update insert data into table oneaccounting.tbl_expense
                boolean insertExpense = ExportReceiptDAO.createAccountantReceipt(ids);
                Integer nResult = insertExpense ? 200 : 500;
                Map<String, Object> result = new HashMap();
                result.put("nResult", nResult);
                Util.sendResponse(ctx, nResult, result);
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static boolean isEmpty(String value) {
        return value == null || value.isEmpty();
    }

    public static void getPartnerByName(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map mIn = new HashMap();
                mIn.put(KEYWORD, ctx.request().getParam(KEYWORD));
                mIn.put(PAGE, ctx.request().getParam(PAGE));
                mIn.put(PAGE_SIZE, ctx.request().getParam(PAGE_SIZE));

                Map mOut = new HashMap();
                mOut.put("data", ExportReceiptDAO.getPartners(mIn));
                sendResponse(ctx, 200, mOut);
            } catch (Exception ex) {
                logger.log(Level.FINE, "Error in arCustomerList", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
