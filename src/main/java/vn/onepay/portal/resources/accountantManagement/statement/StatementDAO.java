package vn.onepay.portal.resources.accountantManagement.statement;

import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.accountantManagement.statement.dto.StatementDetailDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;

import java.sql.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import static vn.onepay.portal.Util.resultSetToList;

public class StatementDAO extends Db {
    private static final String LIST_STATEMENT = "{call ONEFIN.pkg_accountant.list_statement(?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_STATEMENT_BY_IDS = "{call ONEFIN.pkg_accountant.list_statement_by_ids(?,?,?,?)}";
    private static final String LIST_STATEMENT_DETAIL = "{call ONEFIN.pkg_accountant.list_statement_detail(?,?,?,?,?,?,?)}";
    private static final String LIST_STATEMENT_DETAIL_FOR_IB = "{call ONEFIN.pkg_accountant.list_stm_detail_for_ib(?,?,?,?,?,?)}";
    private static final String UPDATE_STATEMENT_STATUS = "{call ONEFIN.pkg_accountant.update_statement_status(?,?,?,?,?,?,?)}";
    private static final String CREATE_ACCOUNTANT_STATEMENT = "{call ONEFIN.create_accountant_statement(?,?,?,?,?,?,?)}";
    private static final String RECREATE_ACCOUNTANT_STATEMENT = "{call ONEFIN.recreate_accountant_statement(?,?,?,?,?,?,?)}";
    private static final String IMPORT_PAYMENT_DATA = "{call ONEFIN.pkg_accountant.import_payment_data(?,?,?)}";
    private static final String DELETE_ACCOUNTANT_STATEMENT = "{call ONEFIN.pkg_accountant.delete_statement(?,?,?)}";
    private static final String INSERT_TB_STATEMENT_DETAIL = "{call ONEFIN.pkg_accountant.import_statement_detail(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String INSERT_TB_STATEMENT_OTHER = "{? = call ONEFIN.pkg_accountant.create_statement_other(?,?,?,?,?,?,?)}";
    private static final String LIST_STM_FEE_CONFIG = "{ call ONEFIN.pkg_accountant.list_stm_fee_config(?)}";
    private static final String LIST_ADVANCE_AMOUNT = "{ call ONEFIN.pkg_accountant.list_advance_amount(?,?,?,?)}";
    private static final String LIST_ADVANCE2_AMOUNT = "{ call ONEFIN.list_advance2_amount(?,?,?,?)}";
    private static final String CREATE_ACCOUNTANT_IB = "{ call ONEFIN.create_accountant_ib(?,?,?)}";
    private static final String GET_SUMMARY_PAYCOLLECT = "{call ONEFIN.get_summary_paycollect(?,?)}";

    public static BaseList<Map<String, Object>> listStatement(String source, String session, String date, String state, int page, int pageSize) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_STATEMENT);
            cs.setString(1, source);
            cs.setString(2, session);
            cs.setString(3, date);
            cs.setString(4, state);
            cs.setInt(5, page);
            cs.setInt(6, pageSize);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.VARCHAR);
            cs.registerOutParameter(9, OracleTypes.NUMBER);
            cs.registerOutParameter(10, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(7);
            String statusDesc = cs.getString(8);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("List statement fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = cs.getInt(9);
            rs = (ResultSet) cs.getObject(10);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            int indexOfOther = -1;
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).get("N_EXPORT_TYPE").toString().equals("8")) {
                    indexOfOther = i;
                    break;
                }
            }
            if (indexOfOther != -1) {
                Collections.swap(list, indexOfOther, list.size() - 1);
            }
            result.setList(list);
            result.setTotalItems(total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<StatementDetailDTO> listStatementDetail(long id, int page, int pageSize) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<StatementDetailDTO> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_STATEMENT_DETAIL);
            cs.setLong(1, id);
            cs.setInt(2, page);
            cs.setInt(3, pageSize);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.registerOutParameter(7, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(4);
            String statusDesc = cs.getString(5);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("List statement detail fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = cs.getInt(6);
            List<StatementDetailDTO> listStmDetail = new ArrayList<>();
            rs = (ResultSet) cs.getObject(7);
            while (rs != null && rs.next()) {
                listStmDetail.add(blindStmDetail(rs));
            }
            result = new BaseList<>();
            result.setTotalItems(total);
            result.setList(listStmDetail);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<Map<String, Object>> listStatementDetail(long id) throws Exception {
        Exception exception = null;
        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "select d.* from onefin.tb_statement_detail d inner join onefin.tb_statement s on d.n_statement_id = s.n_id where s.n_id=? and s.s_state <> 'deleted' order by d.s_payment_voucher asc, d.s_account_number asc";
            con = getConnection114();
            ps = con.prepareStatement(sql);
            ps.setLong(1, id);
            rs = ps.executeQuery();
            result = resultSetToList(rs);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static boolean updateStatementStatus(String source, String session, int userId, String state, String date) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                cs = connection.prepareCall(UPDATE_STATEMENT_STATUS);
                cs.setInt(1, userId);
                cs.setString(2, state);
                cs.setString(3, date);
                cs.registerOutParameter(4, OracleTypes.NUMBER);
                cs.registerOutParameter(5, OracleTypes.VARCHAR);
                cs.setString(6, source);
                cs.setString(7, session);
                cs.execute();
                int statusCode = cs.getInt(4);
                String statusDesc = cs.getString(5);
                if (statusCode != 200 && statusCode != 201) {
                    logger.log(Level.INFO, "Update statement status fail: {0}", statusDesc);
                    return false;
                }
                return true;
            } else {
                logger.info("can not cast connection");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static List<Map<String, Object>> listStatementByIds(Integer[] ids) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                ArrayDescriptor arrDesc =
                        ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, ids);
                cs = conn.prepareCall(LIST_STATEMENT_BY_IDS);
                cs.setArray(1, array);

                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.registerOutParameter(4, OracleTypes.CURSOR);
                cs.execute();

                int statusCode = cs.getInt(2);
                String statusDesc = cs.getString(3);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("List statement by ids fail: " + statusDesc);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                rs = (ResultSet) cs.getObject(4);
                result = resultSetToList(rs);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void importPaymentData(String date) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(IMPORT_PAYMENT_DATA);
            cs.setString(1, date);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(2);
            String statusDesc = cs.getString(3);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("Import payment data fail: " + statusDesc);
                if (statusCode == 400)
                    throw IErrors.VALIDATION_ERROR;
                else
                    throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;
    }

    public static BaseList<Map<String, Object>> createAccountantStatement(String source, String session, String date, int userId, String type) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        String sql = type.equals("create") ? CREATE_ACCOUNTANT_STATEMENT : RECREATE_ACCOUNTANT_STATEMENT;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(sql);
            cs.setString(1, source);
            cs.setString(2, session);
            cs.setString(3, date);
            cs.setInt(4, userId);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.SEVERE, "create statement fail: {0}", statusDesc);
                if (statusCode == 400) {
                    if (statusDesc.equals("ACCOUNT_NUMBER_NOT_MATCH"))
                        throw IErrors.ACCOUNT_NUMBER_NOT_MATCH;
                    else if (statusDesc.equals("STATEMENT_EXISTED"))
                        throw IErrors.STATEMENT_EXISTED;
                    else if (statusDesc.equals("DATA_IMPORTED"))
                        throw IErrors.STATEMENT_DATA_IMPORTED;
                    else if (statusDesc.equals("IMPORT_DATA_FAILED"))
                        throw IErrors.IMPORT_DATA_FAILED;
                    else if (statusDesc.equals("DATA_NOT_CREATED"))
                        throw IErrors.DATA_NOT_CREATED;
                    else
                        throw IErrors.CREATE_STATEMENT_FAIL;
                } else
                    throw IErrors.CREATE_STATEMENT_FAIL;
            }
            rs = (ResultSet) cs.getObject(7);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            result.setList(list);
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void deleteAccountantStatement(String date) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(DELETE_ACCOUNTANT_STATEMENT);
            cs.setString(1, date);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(2);
            String statusDesc = cs.getString(3);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "create statement fail: {0}", statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;
    }


    public static void importStatement(String source, String session, List<Map> listMerchant, java.sql.Date date, int userId, double fee, double amount, double totalAmount) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        CallableStatement cs1 = null;
        try {
            con = getConnection114();
            con.setAutoCommit(false);

            cs1 = con.prepareCall(INSERT_TB_STATEMENT_OTHER);
            cs1.registerOutParameter(1, OracleTypes.NUMBER);
            cs1.setInt(2, userId);
            cs1.setDate(3, date);
            cs1.setDouble(4, fee);
            cs1.setDouble(5, amount);
            cs1.setDouble(6, totalAmount);
            cs1.setString(7, source);
            cs1.setString(8, session);
            cs1.execute();
            long statementId = cs1.getLong(1);

            cs = con.prepareCall(INSERT_TB_STATEMENT_DETAIL);
            for (Map<String, Object> t : listMerchant) {
                cs.setInt(1, -1);
                cs.setString(2, t.getOrDefault("paymentVoucher", "").toString().trim());
                cs.setString(3, t.getOrDefault("merchantId", "").toString().trim());
                cs.setString(4, t.getOrDefault("accountNumber", "").toString().trim());
                cs.setString(5, t.getOrDefault("accountName", "").toString().trim());
                cs.setString(6, t.getOrDefault("bankCode", "").toString().trim());
                cs.setDouble(7, Double.parseDouble(t.getOrDefault("amount", "").toString().trim()));
                cs.setString(8, t.getOrDefault("detail", "").toString().trim());
                cs.setLong(9, statementId);
                cs.setString(10, t.getOrDefault("bankProvince", "").toString().trim());
                cs.setString(11, t.getOrDefault("bankBranch", "").toString().trim());
                cs.setString(12, "Khac");
                cs.setString(13, t.getOrDefault("acqBank", "").toString().trim());
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            if (con != null)
                con.rollback();
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs1, null);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static List<Map<String, Object>> listStmDetailForIB(String date, String source, String session) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_STATEMENT_DETAIL_FOR_IB);
            cs.setString(1, date);
            cs.setString(2, source);
            cs.setString(3, session);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.registerOutParameter(6, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(4);
            String statusDesc = cs.getString(5);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "List statement detail for ib fail: {0}", statusDesc);
                if (statusCode == 400 && "INVALID_STATEMENT_STATE".equals(statusDesc)) {
                    throw IErrors.INVALID_STATEMENT_STATE;
                } else
                    throw IErrors.INTERNAL_SERVER_ERROR;
            }

            rs = (ResultSet) cs.getObject(6);
            result = resultSetToList(rs);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<Map<String, Object>> listFeeConfig() throws Exception {
        Exception exception = null;
        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_STM_FEE_CONFIG);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            result = resultSetToList(rs);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> searchAdvanceAmount(String date) throws Exception {
        Exception exception = null;
        Map<String, Object> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_ADVANCE_AMOUNT);
            cs.setString(1, date);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.execute();
            int code = cs.getInt(2);
            if (code != 200) {
                logger.info("List advance amount fail:" + cs.getString(3));
            } else {
                rs = (ResultSet) cs.getObject(4);
                result = new HashMap<>();
                while (rs.next()) {
                    result.put(ParamsPool.ND_AMOUNT, rs.getDouble("N_ND_AMOUNT"));
                    result.put(ParamsPool.QT_AMOUNT, rs.getDouble("N_QT_AMOUNT"));
                    result.put(ParamsPool.WEEK_AMOUNT, rs.getDouble("N_WEEK_AMOUNT"));
                    result.put(ParamsPool.OTHER_AMOUNT, rs.getDouble("N_OTHER_AMOUNT"));
                    result.put(ParamsPool.TOTAL_AMOUNT, rs.getDouble("N_TOTAL_AMOUNT"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> searchAdvance2Amount(String date) throws Exception {
        Exception exception = null;
        Map<String, Object> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_ADVANCE2_AMOUNT);
            cs.setString(1, date);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.execute();
            int code = cs.getInt(2);
            if (code != 200) {
                logger.info("List advance amount fail:" + cs.getString(3));
            } else {
                rs = (ResultSet) cs.getObject(4);
                return Util.resultSetToMap(rs);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void createAccountantIb(String source, String session, String date) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(CREATE_ACCOUNTANT_IB);
            cs.setString(1, date);
            cs.setString(2, source);
            cs.setString(3, session);
            cs.execute();
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    private static StatementDetailDTO blindStmDetail(ResultSet rs) throws SQLException {
        StatementDetailDTO stmDetailDTO = new StatementDetailDTO();
        stmDetailDTO.setId(rs.getLong("N_ID"));
        stmDetailDTO.setAdvanceId(rs.getLong("N_ADVANCE_ID"));
        stmDetailDTO.setPaymentVoucher(rs.getString("S_PAYMENT_VOUCHER"));
        stmDetailDTO.setMerchantId(rs.getString("S_MERCHANT_ID"));
        stmDetailDTO.setAccountNumber(rs.getString("S_ACCOUNT_NUMBER"));
        stmDetailDTO.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        stmDetailDTO.setBankCode(rs.getString("S_BANK_CODE"));
        stmDetailDTO.setDetail(rs.getString("S_DETAIL"));
        stmDetailDTO.setAmount(rs.getDouble("N_AMOUNT"));
        stmDetailDTO.setCreatedDate(rs.getTimestamp("D_CREATE"));
        stmDetailDTO.setStatementId(rs.getLong("N_STATEMENT_ID"));
        stmDetailDTO.setBankProvince(rs.getString("S_BANK_PROVINCE"));
        stmDetailDTO.setBankBranch(rs.getString("S_BANK_BRANCH"));
        stmDetailDTO.setAcqBank(rs.getString("S_ACQ_BANK"));
        return stmDetailDTO;
    }

    public static List<Map<String, Object>> getSummaryPaycollect(String date) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_SUMMARY_PAYCOLLECT);
            cs.setString(1, date);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);
            if (rs != null) {
                result = Util.resultSetToList(rs);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
