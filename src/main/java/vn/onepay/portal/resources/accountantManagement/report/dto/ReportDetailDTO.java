package vn.onepay.portal.resources.accountantManagement.report.dto;

import java.sql.Timestamp;

public class ReportDetailDTO {
    private Long id;
    private Timestamp fromDate;
    private Timestamp toDate;
    private Double statementVCB;
    private Double statementVTB;
    private Double statementTCB;
    private Double ibVCB;
    private Double ibVTB;
    private Double ibTCB;
    private String state;
    private Timestamp createDate;
    private Timestamp updateDate;
    private Integer userUpdate;
    private Integer reportId;
    private Timestamp reportDate;
    private String reportName;
    private Double reportPs;
    private Double reportVcb;
    private Double reportVtb;
    private Double reportTcb;
    private Double reportTcbInternal;
    private Double reportTcbExternal;
    private Double reportTotal;
    private Double reportDifference;
    private Double reportDescription;
    private Integer reportExportType;
    private Double reportFee;

    private ExplanationDetailDTO explanation_detail;

    public ReportDetailDTO() {

    }
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Timestamp getFromDate() {
        return fromDate;
    }
    public void setFromDate(Timestamp fromDate) {
        this.fromDate = fromDate;
    }
    public Timestamp getToDate() {
        return toDate;
    }
    public void setToDate(Timestamp toDate) {
        this.toDate = toDate;
    }
    public Double getStatementVCB() {
        return statementVCB;
    }
    public void setStatementVCB(Double statementVCB) {
        this.statementVCB = statementVCB;
    }
    public Double getStatementVTB() {
        return statementVTB;
    }
    public void setStatementVTB(Double statementVTB) {
        this.statementVTB = statementVTB;
    }
    public Double getStatementTCB() {
        return statementTCB;
    }
    public void setStatementTCB(Double statementTCB) {
        this.statementTCB = statementTCB;
    }
    public Double getIbVCB() {
        return ibVCB;
    }
    public void setIbVCB(Double ibVCB) {
        this.ibVCB = ibVCB;
    }
    public Double getIbVTB() {
        return ibVTB;
    }
    public void setIbVTB(Double ibVTB) {
        this.ibVTB = ibVTB;
    }
    public Double getIbTCB() {
        return ibTCB;
    }
    public void setIbTCB(Double ibTCB) {
        this.ibTCB = ibTCB;
    }
    public String getState() {
        return state;
    }
    public void setState(String state) {
        this.state = state;
    }
    public Timestamp getCreateDate() {
        return createDate;
    }
    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }
    public Timestamp getUpdateDate() {
        return updateDate;
    }
    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }
    public Integer getUserUpdate() {
        return userUpdate;
    }
    public void setUserUpdate(Integer userUpdate) {
        this.userUpdate = userUpdate;
    }
    public Integer getReportId() {
        return reportId;
    }
    public void setReportId(Integer reportId) {
        this.reportId = reportId;
    }
    public Timestamp getReportDate() {
        return reportDate;
    }
    public void setReportDate(Timestamp reportDate) {
        this.reportDate = reportDate;
    }
    public String getReportName() {
        return reportName;
    }
    public void setReportName(String reportName) {
        this.reportName = reportName;
    }
    public Double getReportPs() {
        return reportPs;
    }
    public void setReportPs(Double reportPs) {
        this.reportPs = reportPs;
    }
    public Double getReportVcb() {
        return reportVcb;
    }
    public void setReportVcb(Double reportVcb) {
        this.reportVcb = reportVcb;
    }
    public Double getReportVtb() {
        return reportVtb;
    }
    public void setReportVtb(Double reportVtb) {
        this.reportVtb = reportVtb;
    }
    public Double getReportTcb() {
        return reportTcb;
    }
    public void setReportTcb(Double reportTcb) {
        this.reportTcb = reportTcb;
    }
    public Double getReportTcbInternal() {
        return reportTcbInternal;
    }
    public void setReportTcbInternal(Double reportTcbInternal) {
        this.reportTcbInternal = reportTcbInternal;
    }
    public Double getReportTcbExternal() {
        return reportTcbExternal;
    }
    public void setReportTcbExternal(Double reportTcbExternal) {
        this.reportTcbExternal = reportTcbExternal;
    }
    public Double getReportTotal() {
        return reportTotal;
    }
    public void setReportTotal(Double reportTotal) {
        this.reportTotal = reportTotal;
    }
    public Double getReportDifference() {
        return reportDifference;
    }
    public void setReportDifference(Double reportDifference) {
        this.reportDifference = reportDifference;
    }
    public Integer getReportExportType() {
        return reportExportType;
    }
    public void setReportExportType(Integer reportExportType) {
        this.reportExportType = reportExportType;
    }
    public ExplanationDetailDTO getExplanation_detail() {
        return explanation_detail;
    }
    public void setExplanation_detail(ExplanationDetailDTO explanation_detail) {
        this.explanation_detail = explanation_detail;
    }
    public Double getReportDescription() {
        return reportDescription;
    }
    public void setReportDescription(Double reportDescription) {
        this.reportDescription = reportDescription;
    }
    public Double getReportFee() {
        return reportFee;
    }
    public void setReportFee(Double reportFee) {
        this.reportFee = reportFee;
    }
    
}
