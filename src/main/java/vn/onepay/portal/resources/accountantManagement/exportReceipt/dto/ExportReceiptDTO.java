package vn.onepay.portal.resources.accountantManagement.exportReceipt.dto;

public class ExportReceiptDTO implements Cloneable {
    private Integer nId;
    private String settleDate;
    private String receiptNumber;
    private String misaCode;
    private String misaMCC;
    private Double unitPrice;
    private Double tax;
    private Double totalAmount;
    private String status;
    private Integer lastReceiptNumber;
    //export data
    private Integer paymentType;
    private Integer paymentMethod;
    private Integer deliveryBill;
    private Integer withReceipt;
    private String dateReceipt;
    private String dateDocument;
    private String detail;
    private String accountPayment;
    private String accountSale;
    private Integer nCount;
    private Double price;
    private String accountDiscount;
    private Double vat;
    private String accountTax;
    private Double eCom;
    private Double instFee;

    private String merchantId;
    private String description;
    private Double eComVat;
    private Double instFeeVat;
    private Double invoiceFee;
    private Double invoiceVat;
    private String instDetail;
    private String instMCC;
    private String instAccountSale;
    private String invoiceMisaNumber;
    private String contractCode;
    private String state;

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Integer getnId() {
        return nId;
    }

    public void setnId(Integer nId) {
        this.nId = nId;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public String getMisaCode() {
        return misaCode;
    }

    public void setMisaCode(String misaCode) {
        this.misaCode = misaCode;
    }

    public String getMisaMCC() {
        return misaMCC;
    }

    public void setMisaMCC(String misaMCC) {
        this.misaMCC = misaMCC;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Integer getLastReceiptNumber() {
        return lastReceiptNumber;
    }

    public void setLastReceiptNumber(Integer lastReceiptNumber) {
        this.lastReceiptNumber = lastReceiptNumber;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Integer getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(Integer paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Integer getDeliveryBill() {
        return deliveryBill;
    }

    public void setDeliveryBill(Integer deliveryBill) {
        this.deliveryBill = deliveryBill;
    }

    public Integer getWithReceipt() {
        return withReceipt;
    }

    public void setWithReceipt(Integer withReceipt) {
        this.withReceipt = withReceipt;
    }

    public String getDateReceipt() {
        return dateReceipt;
    }

    public void setDateReceipt(String dateReceipt) {
        this.dateReceipt = dateReceipt;
    }

    public String getDateDocument() {
        return dateDocument;
    }

    public void setDateDocument(String dateDocument) {
        this.dateDocument = dateDocument;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getAccountPayment() {
        return accountPayment;
    }

    public void setAccountPayment(String accountPayment) {
        this.accountPayment = accountPayment;
    }

    public String getAccountSale() {
        return accountSale;
    }

    public void setAccountSale(String accountSale) {
        this.accountSale = accountSale;
    }

    public Integer getnCount() {
        return nCount;
    }

    public void setnCount(Integer nCount) {
        this.nCount = nCount;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getAccountDiscount() {
        return accountDiscount;
    }

    public void setAccountDiscount(String accountDiscount) {
        this.accountDiscount = accountDiscount;
    }

    public Double getVat() {
        return vat;
    }

    public void setVat(Double vat) {
        this.vat = vat;
    }

    public String getAccountTax() {
        return accountTax;
    }

    public void setAccountTax(String accountTax) {
        this.accountTax = accountTax;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double geteCom() {
        return eCom;
    }

    public void seteCom(Double eCom) {
        this.eCom = eCom;
    }

    public Double getInstFee() {
        return instFee;
    }

    public void setInstFee(Double instFee) {
        this.instFee = instFee;
    }

    public Double geteComVat() {
        return eComVat;
    }

    public void seteComVat(Double eComVat) {
        this.eComVat = eComVat;
    }

    public Double getInstFeeVat() {
        return instFeeVat;
    }

    public void setInstFeeVat(Double instFeeVat) {
        this.instFeeVat = instFeeVat;
    }

    public Double getInvoiceFee() {
        return invoiceFee;
    }

    public void setInvoiceFee(Double invoiceFee) {
        this.invoiceFee = invoiceFee;
    }

    public Double getInvoiceVat() {
        return invoiceVat;
    }

    public void setInvoiceVat(Double invoiceVat) {
        this.invoiceVat = invoiceVat;
    }

    public String getInstDetail() {
        return instDetail;
    }

    public void setInstDetail(String instDetail) {
        this.instDetail = instDetail;
    }

    public String getInstMCC() {
        return instMCC;
    }

    public void setInstMCC(String instMCC) {
        this.instMCC = instMCC;
    }

    public String getInstAccountSale() {
        return instAccountSale;
    }

    public void setInstAccountSale(String instAccountSale) {
        this.instAccountSale = instAccountSale;
    }

    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getInvoiceMisaNumber() {
        return invoiceMisaNumber;
    }

    public void setInvoiceMisaNumber(String invoiceMisaNumber) {
        this.invoiceMisaNumber = invoiceMisaNumber;
    }
}
