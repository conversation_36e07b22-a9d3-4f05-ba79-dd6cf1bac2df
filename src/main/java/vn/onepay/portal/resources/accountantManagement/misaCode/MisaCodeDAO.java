package vn.onepay.portal.resources.accountantManagement.misaCode;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.accountantManagement.misaCode.dto.PartnerContractDTO;
import vn.onepay.portal.resources.accountantManagement.misaCode.dto.MisaCodeDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MisaCodeDAO extends Db {
    private static final String INSERT_MISA_CODE = "{call ONEFIN.pkg_accountant.insert_misa_code(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String UPDATE_MISA_CODE = "{call ONEFIN.pkg_accountant.update_misa_code(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DELETE_MISA_CODE = "{call ONEFIN.pkg_accountant.delete_misa_code(?,?,?)}";
    private static final String IMPORT_MISACODE = "{call ONEFIN.pkg_accountant.import_misa_code(?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_MISA_CODE = "{call ONEFIN.pkg_accountant.list_misa_code(?,?,?,?,?,?,?,?)}";

    public static BaseList<MisaCodeDTO> listMisaCode(String keywords, String state, int page, int pageSize) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<MisaCodeDTO> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_MISA_CODE);
            cs.setString(1, keywords);
            cs.setString(2, state);
            cs.setInt(3, page);
            cs.setInt(4, pageSize);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                String failMessage = "List merchant fail: " + statusDesc;
                logger.info(failMessage);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = cs.getInt(7);
            List<MisaCodeDTO> listMerchant = new ArrayList<>();
            rs = (ResultSet) cs.getObject(8);
            while (rs != null && rs.next()) {
                listMerchant.add(bindMisaCode(rs));
            }
            result = new BaseList<>();
            result.setTotalItems(total);
            result.setList(listMerchant);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<PartnerContractDTO> listPartnerContractByContractCode(String contractCode) throws Exception {
        Exception exception = null;
        List<PartnerContractDTO> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call " + Config.getOnepartnerSchema() + ".p_merchant_management.get_partner_contract_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, contractCode);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                throw new SQLException("DB load Acceptance list error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(new PartnerContractDTO(rs.getInt("N_PARTNER_ID"), rs.getString("S_SHORT_NAME"),
                            rs.getInt("N_CONTRACT_ID"), rs.getString("S_CONTRACT_NAME")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void insertMisaCode(MisaCodeDTO misaDTO) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "Insert misa code fail: ";
        try {
            conn = getConnection114();
            cs = conn.prepareCall(INSERT_MISA_CODE);
            cs.setString(1, misaDTO.getContractCode());
            cs.setInt(2, misaDTO.getPartnerId());
            cs.setString(3, misaDTO.getPartnerName());
            cs.setString(4, misaDTO.getMerchantId());
            cs.setString(5, misaDTO.getMisaCode());
            cs.setString(6, misaDTO.getMisaName());
            cs.setString(7, misaDTO.getAddress());
            cs.setString(8, misaDTO.getTaxNumber());
            cs.setString(9, misaDTO.getEmail());
            cs.setInt(10, misaDTO.getContractId());
            cs.registerOutParameter(11, OracleTypes.NUMBER);
            cs.registerOutParameter(12, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(11);
            String statusDesc = cs.getString(12);
            if (statusCode != 200 && statusCode != 201) {
                error += statusDesc;
                logger.info(error);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
    }

    public static void updateMisaCode(MisaCodeDTO misaDTO) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "Update misa code fail: ";
        try {
            conn = getConnection114();
            cs = conn.prepareCall(UPDATE_MISA_CODE);
            cs.setInt(1, misaDTO.getId());
            cs.setString(2, misaDTO.getContractCode());
            cs.setInt(3, misaDTO.getPartnerId());
            cs.setString(4, misaDTO.getPartnerName());
            cs.setString(5, misaDTO.getMerchantId());
            cs.setString(6, misaDTO.getMisaName());
            cs.setString(7, misaDTO.getAddress());
            cs.setString(8, misaDTO.getEmail());
            cs.setInt(9, misaDTO.getContractId());
            cs.setString(10, misaDTO.getState());
            cs.registerOutParameter(11, OracleTypes.NUMBER);
            cs.registerOutParameter(12, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(11);
            String statusDesc = cs.getString(12);
            if (statusCode != 200 && statusCode != 201) {
                error += statusDesc;
                logger.info(error);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
    }

    public static boolean deleteMisaCode(int id) throws Exception {
        Exception exception;
        Connection conn = null;
        CallableStatement cs = null;
        String error = "Delete misa code fail: ";
        try {
            conn = getConnection114();
            cs = conn.prepareCall(DELETE_MISA_CODE);
            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(2);
            String statusDesc = cs.getString(3);
            if (statusCode != 200 && statusCode != 201) {
                error += statusDesc;
                logger.info(error);
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        throw exception;
    }

    public static void importMisaCode(List<Map<String, Object>> listMerchant) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(IMPORT_MISACODE);
            for (Map<String, Object> t : listMerchant) {
                cs.setString(1, trimOrNull(t.get("contractCode")));
                cs.setString(2, trimOrNull(t.get("merchantId")));
                cs.setString(3, trimOrNull(t.get("misaCode")));
                cs.setString(4, trimOrNull(t.get("misaName")));
                cs.setString(5, trimOrNull(t.get("address")));
                cs.setString(6, trimOrNull(t.get("taxNumber")));
                cs.setString(7, trimOrNull(t.get("partnerName")));
                cs.setString(8, trimOrNull(t.get("email")));
                cs.setString(9, trimOrNull(t.get("state")));
                cs.addBatch();
            }
            cs.executeBatch();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static String trimOrNull(Object input) {
        try {
            String output = (String) input;
            if (output == null) return null;
            else if (output.trim().equals("")) return null;
            else return output.trim();
        } catch (Exception e) {
            return null;
        }
    }

    private static MisaCodeDTO bindMisaCode(ResultSet rs) throws SQLException {
        MisaCodeDTO merchantDTO = new MisaCodeDTO();
        merchantDTO.setId(rs.getInt("N_ID"));
        merchantDTO.setContractCode(rs.getString("S_CONTRACT_CODE"));
        merchantDTO.setPartnerId(rs.getInt("N_PARTNER_ID"));
        merchantDTO.setPartnerName(rs.getString("S_PARTNER_NAME"));
        merchantDTO.setMerchantId(rs.getString("S_MERCHANT_ID"));
        merchantDTO.setMisaCode(rs.getString("S_MISA_CODE"));
        merchantDTO.setMisaName(rs.getString("S_MISA_NAME"));
        merchantDTO.setAddress(rs.getString("S_ADDRESS"));
        merchantDTO.setTaxNumber(rs.getString("S_TAX_NUMBER"));
        merchantDTO.setEmail(rs.getString("S_EMAIL"));
        merchantDTO.setContractId(rs.getInt("N_CONTRACT_ID"));
        merchantDTO.setState(rs.getString("S_STATE"));
        merchantDTO.setCreatedDate(rs.getTimestamp("D_CREATE"));
        merchantDTO.setUpdatedDate(rs.getTimestamp("D_UPDATE"));
        merchantDTO.setMerchantId(rs.getString("S_MERCHANT_ID"));
        return merchantDTO;
    }
}
