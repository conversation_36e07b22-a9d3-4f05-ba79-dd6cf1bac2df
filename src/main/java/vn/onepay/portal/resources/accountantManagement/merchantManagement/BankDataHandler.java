package vn.onepay.portal.resources.accountantManagement.merchantManagement;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.Util.sendResponse;

public class BankDataHandler implements IConstants {
    private static final Logger logger = Logger.getLogger(BankDataHandler.class.getName());
    private static final String LIST_BANK_CODE_SQL = "SELECT * FROM ONEFIN.TB_BANK_CODE ORDER BY S_ACQ_BANK, S_BANK_CODE ASC";
    private static final String LIST_BANK_PROVINCE_SQL = "SELECT * FROM ONEFIN.TB_BANK_PROVINCE ORDER BY N_BANK_CODE_ID, S_PROVINCE ASC";
    private static final String LIST_BANK_BRANCH_SQL = "SELECT * FROM ONEFIN.TB_BANK_BRANCH ORDER BY N_BANK_PROVINCE_ID, S_BRANCH ASC";

    public static void listBankCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                List<Map<String, Object>> listResult = MerchantManagementDAO.listBankData(LIST_BANK_CODE_SQL);
                Map<String, List<Map<String, Object>>> bankCodeMap = listResult.stream().collect(Collectors.groupingBy(t -> (String) t.get("S_ACQ_BANK")));
                sendResponse(ctx, 200, bankCodeMap);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void listBankProvince(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                new JsonObject(new HashMap<>());
                List<Map<String, Object>> listResult = MerchantManagementDAO.listBankData(LIST_BANK_PROVINCE_SQL);
                Map<String, List<Map<String, Object>>> bankProvinceMap = listResult.stream().collect(Collectors.groupingBy(t -> t.get("N_BANK_CODE_ID").toString()));
                sendResponse(ctx, 200, bankProvinceMap);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void listBankBranch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                List<Map<String, Object>> listResult = MerchantManagementDAO.listBankData(LIST_BANK_BRANCH_SQL);
                Map<String, List<Map<String, Object>>> bankBranchMap = listResult.stream().collect(Collectors.groupingBy(t -> t.get("N_BANK_PROVINCE_ID").toString()));
                sendResponse(ctx, 200, bankBranchMap);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
