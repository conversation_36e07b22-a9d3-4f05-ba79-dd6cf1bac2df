package vn.onepay.portal.resources.accountantManagement.merchantManagement;

import com.google.gson.Gson;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.accountantManagement.merchantManagement.dto.MerchantDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.ReadExcel;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantManagementHandler implements IConstants {
    private MerchantManagementHandler() {}

    private static final Logger logger = Logger.getLogger(MerchantManagementHandler.class.getName());
    private static final String TEMPLATE_FILE_PATH = Config.getString("accountant.template_file_path", "");
    private static final String TEMPLATE_FILE_NAME = Config.getString("accountant.template_merchant_account_file", "");

    public static void listMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                String keywords = httpServerRequest.getParam(ParamsPool.KEY_WORDS) == null ? "" : httpServerRequest.getParam(ParamsPool.KEY_WORDS);
                String state = httpServerRequest.getParam(ParamsPool.STATE) == null ? "" : httpServerRequest.getParam(ParamsPool.STATE);
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0 : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20 : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
                BaseList<MerchantDTO> listMerchant = MerchantManagementDAO.listMerchant(keywords, state, page, pageSize);
                sendResponse(ctx, 200, listMerchant);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void addMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String body = ctx.getBodyAsString();
                MerchantDTO merchantDTO = new Gson().fromJson(body, MerchantDTO.class);

                MerchantDTO merchantRes = MerchantManagementDAO.insertMerchant(merchantDTO);
                sendResponse(ctx, 201, merchantRes);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int nId = ctx.request().getParam(ParamsPool.ID) == null ? 0 : Integer.parseInt(ctx.request().getParam(ParamsPool.ID));
                if (nId == 0)
                    throw IErrors.VALIDATION_ERROR;
                String body = ctx.getBodyAsString();
                MerchantDTO merchantDTO = new Gson().fromJson(body, MerchantDTO.class);
                merchantDTO.setId(nId);
                MerchantDTO merchantRes = MerchantManagementDAO.updateMerchant(merchantDTO);

                sendResponse(ctx, 200, merchantRes);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void deleteMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int nId = ctx.request().getParam(ParamsPool.ID) == null ? 0 : Integer.parseInt(ctx.request().getParam(ParamsPool.ID));
                if (nId == 0)
                    throw IErrors.VALIDATION_ERROR;
                boolean isDeleted = MerchantManagementDAO.deleteMerchant(nId);
                int statusRes = isDeleted ? 200 : 500;
                sendResponse(ctx, statusRes, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void uploadMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Set<FileUpload> fileUploadSet = ctx.fileUploads();

                FileUpload fileUpload = fileUploadSet.stream().findFirst().get();
                if (fileUpload == null)
                    throw IErrors.VALIDATION_ERROR;
                Map<String, Object> mTemp = new HashMap<>();
                mTemp.put("row_start", 1);
                mTemp.put("sheet_number", 0);
                List<String> columns = new ArrayList<>();
                columns.add("no");
                columns.add("merchantId");
                columns.add("merchantName");
                columns.add("accountNumber");
                columns.add("accountName");
                columns.add("acqBank");
                columns.add("bankCode");
                columns.add("bankProvince");
                columns.add("bankBranch");
                columns.add("state");
                mTemp.put("list_column_name", columns);

                List<Map<String, Object>> listImport;
                try {
                    logger.info("file Type: " + fileUpload.contentType());
                    if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXlsx2(fileUpload.uploadedFileName(), mTemp);
                    } else if ("application/vnd.ms-excel".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXls2(fileUpload.uploadedFileName(), mTemp);
                    } else {
                        throw IErrors.INVALID_FILE_MUST_EXCEL;
                    }
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "ERROR TO MERCHANT ACCOUNTANT IMPORT: ", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }

                if (listImport == null || listImport.isEmpty()) {
                    logger.info("No import file data found");
                    throw IErrors.VALIDATION_ERROR;
                }
                for (int i = 0; i < listImport.size(); i++) {
                    logger.info("value import:" + new JsonObject(listImport.get(i)));
                }

                precheckMerchant(listImport);
                MerchantManagementDAO.importMerchant(listImport);
                sendResponse(ctx, 200, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static void precheckMerchant(List<Map<String, Object>> listImport) {
        for (int i = 0; i < listImport.size(); i++) {
            Map<String, Object> t = listImport.get(i);
            String error = "";
            if (t.getOrDefault("merchantId", "").toString().trim().isEmpty()) {
                error = "MerchantId is blank.";
            }
            if (t.getOrDefault("accountNumber", "").toString().trim().isEmpty()) {
                error = "AccountNumber is blank.";
            }

            if (t.getOrDefault("acqBank", "").toString().trim().isEmpty()) {
                error = "AcqBank is blank.";
            }
            if (t.getOrDefault("bankCode", "").toString().trim().isEmpty()) {
                error = "BankCode is blank.";
            }
            String state = t.getOrDefault("state", "").toString().trim();

            if (state.isEmpty() || (!state.equals("actived") && !state.equals("deleted") && !state.equals("disabled"))) {
                error = "State is blank or invalid.";
            }
            if (!error.isEmpty()) {
                error = "Import failed at row " + (i + 2) + ". " + error;
                throw new ErrorException(400, "IMPORT_FAILED", error, "", error);
            }
        }
    }

    public static void downloadTempFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String[] arr = TEMPLATE_FILE_NAME.split("\\.");
                String fileName = arr[0];
                String fileExt = arr[1];

                Path requestPath = FileSystems.getDefault().getPath(TEMPLATE_FILE_PATH + File.separator + TEMPLATE_FILE_NAME).normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                        Map<String, Object> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE, String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            logger.log(Level.SEVERE, ERROR, e);
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv" : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file" : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition", "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                logger.info("Download Success");
                            } else {
                                logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void exportMerchantAccountant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                HttpServerRequest request = ctx.request();
                requestData.put(X_USER_ID, Util.handleHeaderHttpRequest(request, X_USER_ID, ""));
                requestData.put(X_REQUEST_ID, Util.handleHeaderHttpRequest(request, X_REQUEST_ID, ""));
                requestData.put(X_REAL_IP, Util.handleHeaderHttpRequest(request, X_REAL_IP, ""));

                int totalRows = MerchantManagementDAO.countMerchantAccountant();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = dateFormat.format(new Date());
                String fileName = "Merchant_accountant_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("file_name", fileName);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("merchant_accountant");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(null, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                }else{
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(null, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            }
            catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                future.fail(ex);
            }
        }, false, null);
    }
}
