package vn.onepay.portal.resources.accountantManagement.exportReceipt;

import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptDTO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptDetailDTO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptGeneralDTO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptStatusDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportReceiptDAO extends Db implements IConstants {
    private final static Logger logger = Logger.getLogger(ExportReceiptDAO.class.getName());
    private static String LIST_ACTIVE_PARTNER = "SELECT N_ID, S_SHORT_NAME FROM ONEPARTNER.TBL_PARTNER WHERE N_ACTIVE = 1 ORDER BY S_SHORT_NAME";
    private static String EXPORT_RECEIPT = "{call ONEFIN.PKG_EXPORT_RECEIPT.get_export_receipt(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static String UPDATE_LAST_RECEIPT_NUMBER = "UPDATE ONESCHED.tb_mark SET s_mark = ? WHERE s_service_id = ? ";
    private static String UPDATE_RECEIPT_NUMBER = "{call ONEFIN.PKG_EXPORT_RECEIPT.update_receipt_number(?,?)}";
    private static String UPDATE_SETTLEMENT_DATE = "{call ONEFIN.PKG_EXPORT_RECEIPT.update_settlement_date(?,?)}";
    private static String EXPORT_RECEIPT_DOWNLOAD = "{call ONEFIN.PKG_EXPORT_RECEIPT.export_receipt_download(?,?,?,?)}";
    private static String EXPORT_RECEIPT_DETAIL_DOWNLOAD = "{call ONEFIN.PKG_EXPORT_RECEIPT.export_receipt_detail_download(?,?,?,?)}";
    private static String RECEIPT_DETAIL = "{call ONEFIN.PKG_EXPORT_RECEIPT.get_receipt_detail(?,?,?,?,?)}";
    private static String UPDATE_RECEIPT_DETAIL_STATE = "{call ONEFIN.PKG_EXPORT_RECEIPT.update_receipt_detail_state(?,?,?,?,?)}";
    private static final String APPROVE_RECEIPT = "{call ONEFIN.PKG_EXPORT_RECEIPT.approve_receipt(?,?,?)}";
    private static final String UPDATE_FEE_MONTH_AMOUNT = "{call ONEFIN.PKG_EXPORT_RECEIPT.update_fee_month_amount(?)}";
    private static final String CHECK_RECEIPT = "{call ONEFIN.PKG_EXPORT_RECEIPT.check_receipt(?,?,?)}";
    private static final String CREATE_ACCOUNTANT_RECEIPT = "{call ONEACCOUNTING.PKG_ACCOUNTANT_RECEIPT.create_accountant_receipt(?,?,?)}";
    private static final String EXPORT_RECEIPT_PARTNERS = "{call ONEFIN.PKG_EXPORT_RECEIPT.get_partner_list(?,?,?,?,?,?)}";
    private static final String BATCH_UPDATE_ACCOUNTANT_RECEIPT = "{call ONEFIN.PKG_EXPORT_RECEIPT.batch_update_receipt(?,?)}";


    public static BaseList<PartnerDto> getActivePartners() throws Exception {
        BaseList<PartnerDto> baseList = new BaseList<>();
        List<PartnerDto> lstResult = new ArrayList<>();
        Exception exception = null;
        PartnerDto partnerDto;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            ps = con.prepareStatement(LIST_ACTIVE_PARTNER);
            rs = ps.executeQuery();
            while (rs != null && rs.next()) {
                partnerDto = new PartnerDto();
                partnerDto.setId(rs.getInt(1));
                partnerDto.setShortName(rs.getString(2));

                lstResult.add(partnerDto);
            }
            baseList.setList(lstResult);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        if (exception != null)
            throw exception;

        return baseList;
    }

    public static Map getExportReceiptList(Map mIn) throws Exception {
        Map resultMap = new HashMap();
        List<ExportReceiptDTO> exportReceiptDTOs = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(EXPORT_RECEIPT);

            cs.setString(1, mIn.get(FROMDATE).toString());
            cs.setString(2, mIn.get(TODATE).toString());
            cs.setString(3, mIn.getOrDefault(PARTNERID, "").toString());
            cs.setString(4, mIn.getOrDefault(ADVANCETYPE, "").toString());
            cs.setString(5, mIn.getOrDefault(PAYCHANNEL, "").toString());
            cs.setString(6, mIn.getOrDefault(STATUS, "").toString());
            cs.setString(7, mIn.getOrDefault(STATE, "").toString());
            cs.setString(8, mIn.getOrDefault(MERCHANTCLASSIFY, "").toString());
            cs.setString(9, mIn.getOrDefault(TRANS_VALUE, "").toString());
            cs.setInt(10, Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setInt(11, Integer.parseInt(mIn.get(PAGESIZE).toString()));

            cs.registerOutParameter(12, OracleTypes.INTEGER);
            cs.registerOutParameter(13, OracleTypes.VARCHAR);
            cs.registerOutParameter(14, OracleTypes.CURSOR);
            cs.registerOutParameter(15, OracleTypes.INTEGER);
            cs.registerOutParameter(16, OracleTypes.INTEGER);

            cs.execute();

            int nResult = cs.getInt(12);
            String sResult = cs.getString(13);
            rs = (ResultSet) cs.getObject(14);

            if (nResult != 200)
                throw new Exception("DB get_export_receipt error: " + sResult);
            while (rs != null && rs.next())
                exportReceiptDTOs.add(bindExportReceipt(rs));

            //get last receipt number
            resultMap.put(LAST_RECEIPT_NUMBER, cs.getInt(15));
            resultMap.put(DATA, exportReceiptDTOs);
            resultMap.put(TOTAL_ITEMS, cs.getInt(16));

        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in getExportReceiptList", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return resultMap;
    }

    private static ExportReceiptDTO bindExportReceipt(ResultSet rs) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        ExportReceiptDTO exportReceipt = new ExportReceiptDTO();
        //bind du lieu man hinh tim kiem
        exportReceipt.setnId(Util.getColumnInteger(rs, "N_ID"));
        exportReceipt.setSettleDate(rs.getString("D_SETTLEMENT") != null
                ? sdf.format(Util.getColumnTimeStamp(rs, "D_SETTLEMENT")) : "");
        exportReceipt.setReceiptNumber(Util.getColumnString(rs, "S_RECEIPT_NUMBER"));
        exportReceipt.setMisaCode(Util.getColumnString(rs, "S_MISA_CODE"));
        exportReceipt.setMisaMCC(Util.getColumnString(rs, "S_MISA_MCC"));
        exportReceipt.setUnitPrice(Util.getColumnDouble(rs, "N_PRICE"));
        exportReceipt.setTax(Util.getColumnDouble(rs, "N_TAX"));
        exportReceipt.setTotalAmount(Util.getColumnDouble(rs, "N_AMOUNT"));
        exportReceipt.setStatus(Util.getColumnString(rs, "S_STATUS"));
        exportReceipt.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
        exportReceipt.setDescription(Util.getColumnString(rs, "S_DESC"));
        exportReceipt.setInvoiceFee(Util.getColumnDouble(rs, "N_INVOICE_FEE"));
        exportReceipt.setInvoiceVat(Util.getColumnDouble(rs, "N_INVOICE_VAT"));
        exportReceipt.setDetail(Util.getColumnString(rs, "S_DETAIL"));
        exportReceipt.setInstDetail(Util.getColumnString(rs, "S_DETAIL_ITA"));
        exportReceipt.setState(Util.getColumnString(rs, "S_STATE"));
        exportReceipt.setContractCode(Util.getColumnString(rs, "S_CONTRACT_CODE"));
        //bind du lieu export excel
        exportReceipt.setPaymentType(Util.getColumnInteger(rs, "N_PAYMENT_TYPE"));
        exportReceipt.setPaymentMethod(Util.getColumnInteger(rs, "N_PAYMENT_METHOD"));
        exportReceipt.setDeliveryBill(Util.getColumnInteger(rs, "N_DELIVERY_BILL"));
        exportReceipt.setWithReceipt(Util.getColumnInteger(rs, "N_WITH_RECEIPT"));
        exportReceipt.setDateDocument(Util.getColumnString(rs, "D_DOCUMENT") != null
                ? sdf.format(Util.getColumnTimeStamp(rs, "D_DOCUMENT")) : "");

        exportReceipt.setDateReceipt(Util.getColumnString(rs, "D_RECEIPT") != null
                ? sdf.format(Util.getColumnTimeStamp(rs, "D_RECEIPT")) : "");
        exportReceipt.setAccountPayment(Util.getColumnString(rs, "S_ACCOUNT_PAYMENT"));
        exportReceipt.setAccountSale(Util.getColumnString(rs, "S_ACCOUNT_SALE"));
        exportReceipt.setnCount(Util.getColumnInteger(rs, "N_COUNT"));
        exportReceipt.setVat(Util.getColumnDouble(rs, "N_VAT"));
        exportReceipt.setAccountTax(Util.getColumnString(rs, "S_ACCOUNT_TAX"));
        exportReceipt.seteCom(Util.getColumnDouble(rs, "N_ECOM_FEE"));
        exportReceipt.setInstFee(Util.getColumnDouble(rs, "N_ITA_FEE"));
        exportReceipt.seteComVat(Util.getColumnDouble(rs, "N_ECOM_VAT"));
        exportReceipt.setInstFeeVat(Util.getColumnDouble(rs, "N_ITA_VAT"));
        exportReceipt.setAccountDiscount(Util.getColumnString(rs, "S_ACCOUNT_DISCOUNT"));
        exportReceipt.setInstAccountSale(Util.getColumnString(rs, "S_ITA_ACCOUNT_SALE"));
        exportReceipt.setInstMCC(Util.getColumnString(rs, "S_ITA_MISA_MCC"));
        exportReceipt.setInvoiceMisaNumber(Util.getColumnString(rs, "S_MISA_INVOICE_NUMBER"));
        return exportReceipt;
    }

    public static ActionDto updateReceiptNumber(List<String> listUpdate, Integer lastReceiptNumber, Integer currentReceiptNumber) throws Exception {
        ActionDto actionDto = new ActionDto();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall(UPDATE_RECEIPT_NUMBER);
            String updateNumber = "";
            for (String updateItem : listUpdate) {
                currentReceiptNumber++;
                cs.setString(1, StringUtils.leftPad(String.valueOf(currentReceiptNumber), 7, "0"));
                cs.setInt(2, Convert.parseInt(updateItem, 0));
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            updateLastReceiptNumber(lastReceiptNumber);
            actionDto.setNResult(200);
            actionDto.setSResult("Update Successfully!");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateSoChungTu ", ex);
            if (con != null)
                con.rollback();
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateLastReceiptNumber(Integer lastReceiptNumber) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        PreparedStatement ps = null;
        try {
            con = getConnection114();
            ps = con.prepareStatement(UPDATE_LAST_RECEIPT_NUMBER);
            ps.setInt(1, lastReceiptNumber);
            ps.setString(2, IConstants.SO_CHUNG_TU_CUOI);
            ps.execute();
            actionDto = new ActionDto();
            actionDto.setNResult(200);
            actionDto.setSResult("OK");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateSochungTuCuoi", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, ps, null, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateSettleDate(String date, List<String> lstUpdate) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall(UPDATE_SETTLEMENT_DATE);
            for (String updateId : lstUpdate) {
                cs.setString(1, date);
                cs.setLong(2, Convert.parseInt(updateId, 0));
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            actionDto = new ActionDto();
            actionDto.setSResult("OK");
            actionDto.setNResult(200);
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateSettlementDate", ex);
            if (con != null)
                con.rollback();
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static List<ExportReceiptDTO> exportReceiptDownload(Map mIn) throws Exception {
        Exception exception = null;
        List<ExportReceiptDTO> lstDownload = new ArrayList<>();
        List<ExportReceiptDTO> lstResult = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(EXPORT_RECEIPT_DOWNLOAD);
            cs.setString(1, mIn.getOrDefault("id", "").toString());
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);

            cs.execute();

            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("exportReceiptDownload error: " + sResult);
            rs = (ResultSet) cs.getObject(4);
            while (rs != null && rs.next())
                lstDownload.add(bindExportReceipt(rs));
            for (ExportReceiptDTO item : lstDownload) {
                lstResult.add(item);
                if (item.getInstFee() != null && item.getInstFee() > 0) {
                    ExportReceiptDTO eCom = (ExportReceiptDTO) item.clone();
                    eCom.setDetail(eCom.getInstDetail());
                    eCom.seteCom(item.getInstFee());
                    eCom.seteComVat(item.getInstFeeVat());
                    eCom.setMisaMCC(item.getInstMCC());
                    eCom.setAccountSale(item.getInstAccountSale());
                    eCom.setMerchantId("");
                    lstResult.add(eCom);
                }
            }

        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return lstResult;
    }

    public static ExportReceiptDTO exportReceiptDetailDownload(Map mIn) throws Exception {
        Exception exception = null;
        ExportReceiptDTO receiptDTO = new ExportReceiptDTO();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(EXPORT_RECEIPT_DETAIL_DOWNLOAD);
            cs.setString(1, mIn.getOrDefault("id", "").toString());
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.execute();
            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);

            if (nResult != 200)
                throw new Exception("exportReceiptDetailDownload error: " + sResult);
            rs = (ResultSet) cs.getObject(4);
            while (rs != null && rs.next())
                receiptDTO = bindExportReceipt(rs);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return receiptDTO;
    }

    public static Map<String, Object> getReceiptDetail(Integer receiptId) throws Exception {
        Map<String, Object> mapResult = new HashMap<>();
        List<ExportReceiptDetailDTO> receiptDetails = new ArrayList<>();
        ExportReceiptDTO exportReceipt = new ExportReceiptDTO();

        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(RECEIPT_DETAIL);
            cs.setInt(1, receiptId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.registerOutParameter(5, OracleTypes.CURSOR);

            cs.execute();
            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("Error in getReceiptDetail: " + sResult);
            rs = (ResultSet) cs.getObject(4);
            while (rs != null && rs.next()) {
                exportReceipt = bindExportReceipt(rs);
            }
            closeConnectionDB(rs, null, null, null);
            rs = (ResultSet) cs.getObject(5);
            while (rs != null && rs.next()) {
                receiptDetails.add(bindReceiptDetail(rs));
            }
            mapResult.put("exportDetail", exportReceipt);
            mapResult.put("exportDetailList", receiptDetails);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return mapResult;
    }

    public static ExportReceiptGeneralDTO getReceiptDetailGeneral(Integer receiptId) throws Exception {
        ExportReceiptGeneralDTO mapResult = new ExportReceiptGeneralDTO();
        List<ExportReceiptDetailDTO> receiptDetails = new ArrayList<>();
        ExportReceiptDTO exportReceipt = new ExportReceiptDTO();

        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(RECEIPT_DETAIL);
            cs.setInt(1, receiptId);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.registerOutParameter(5, OracleTypes.CURSOR);

            cs.execute();
            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200)
                throw new Exception("Error in getReceiptDetail: " + sResult);
            rs = (ResultSet) cs.getObject(4);
            while (rs != null && rs.next()) {
                exportReceipt = bindExportReceipt(rs);
            }
            closeConnectionDB(rs, null, null, null);
            rs = (ResultSet) cs.getObject(5);
            while (rs != null && rs.next()) {
                receiptDetails.add(bindReceiptDetail(rs));
            }
            mapResult.setExportReceipt(exportReceipt);
            mapResult.setReceiptDetails(receiptDetails);
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return mapResult;
    }

    private static ExportReceiptDetailDTO bindReceiptDetail(ResultSet rs) throws SQLException {
        ExportReceiptDetailDTO exportReceiptDetail = new ExportReceiptDetailDTO();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        exportReceiptDetail.setDateFrom(Util.getColumnString(rs, "D_FROM") != null ?
                sdf.format(Util.getColumnTimeStamp(rs, "D_FROM")) : "");
        exportReceiptDetail.setDateTo(Util.getColumnString(rs, "D_TO") != null ?
                sdf.format(Util.getColumnTimeStamp(rs, "D_TO")) : "");
        exportReceiptDetail.setFeeReceipt(Util.getColumnDouble(rs, "N_FEE_RECEIPT"));
        exportReceiptDetail.setAmountStm(Util.getColumnDouble(rs, "N_AMOUNT_STM"));
        exportReceiptDetail.setAmountAdv(Util.getColumnDouble(rs, "N_AMOUNT_ADV"));
        exportReceiptDetail.setFeeVat(Util.getColumnDouble(rs, "N_FEE_VAT"));
        exportReceiptDetail.setFeeTotalReceipt(Util.getColumnDouble(rs, "N_FEE_TOTAL_RECEIPT"));
        exportReceiptDetail.setStatus(Util.getColumnString(rs, "S_STATUS"));
        exportReceiptDetail.setId(Util.getColumnInteger(rs, "N_ID"));
        exportReceiptDetail.setPaymentVoucher(Util.getColumnString(rs, "S_PAYMENT_VOUCHER"));
        exportReceiptDetail.setAdvance(Util.getColumnDouble(rs, "N_ADVANCE"));
        exportReceiptDetail.setInvoice(Util.getColumnDouble(rs, "N_INVOICE"));
        exportReceiptDetail.setParentId(Util.getColumnInteger(rs, "N_PARENT_ID"));
        exportReceiptDetail.setDescription(Util.getColumnString(rs, "S_DESC"));
        exportReceiptDetail.seteCom(Util.getColumnDouble(rs, "N_ECOM_FEE"));
        exportReceiptDetail.setInstFee(Util.getColumnDouble(rs, "N_ITA_FEE"));
        exportReceiptDetail.setCutOff(Util.getColumnDouble(rs, "N_CUTOFF"));
        exportReceiptDetail.setDiscrepancies(exportReceiptDetail.getInvoice() - exportReceiptDetail.getAmountStm() - exportReceiptDetail.getCutOff());
        exportReceiptDetail.setState(Util.getColumnString(rs, "S_STATE"));


        return exportReceiptDetail;
    }

    public static boolean updateReceiptDetailState(String status, Integer[] ids, String reasonApprove) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                ArrayDescriptor arrDesc = ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, ids);

                cs = connection.prepareCall(UPDATE_RECEIPT_DETAIL_STATE);
                cs.setString(1, status);
                cs.setArray(2, array);
                cs.setString(3, reasonApprove);
                cs.registerOutParameter(4, OracleTypes.NUMBER);
                cs.registerOutParameter(5, OracleTypes.VARCHAR);
                cs.execute();
                int statusCode = cs.getInt(4);
                String statusDesc = cs.getString(5);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("Update receipt detail status fail: " + statusDesc);
                    return false;
                }
                return true;
            } else {
                logger.info("can not cast connection");
            }

        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateReceiptDetailStatus", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static boolean approveReceipt(Integer[] approveIds) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                ArrayDescriptor arrDesc = ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, approveIds);

                cs = connection.prepareCall(APPROVE_RECEIPT);
                cs.setArray(1, array);
                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.execute();
                int statusCode = cs.getInt(2);
                String statusDesc = cs.getString(3);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("Approve receipt fail: " + statusDesc);
                    return false;
                }
                return true;
            } else {
                logger.info("can not cast connection");
            }

        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in approveReceipt", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static void updateFeeMonthAmount(Set<Integer> parentIds) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall(UPDATE_FEE_MONTH_AMOUNT);
            for (Integer id : parentIds) {
                cs.setInt(1, id);
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
        } catch (Exception ex) {
            logger.log(Level.FINE, "updateFeeMonthAmount error: ", ex);
            if (con != null)
                con.rollback();
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }


    public static boolean checkReceipt(Integer[] ids) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                ArrayDescriptor arrDesc = ArrayDescriptor.createDescriptor("ONEFIN.NUMBER_ARRAY", connection);
                Array array = new ARRAY(arrDesc, connection, ids);

                cs = connection.prepareCall(CHECK_RECEIPT);
                cs.setArray(1, array);
                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.execute();
                int statusCode = cs.getInt(2);
                String statusDesc = cs.getString(3);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("Check receipt fail: " + statusDesc);
                    return false;
                }
                return true;
            } else {
                logger.info("can not cast connection");
            }

        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in checkReceipt", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static Double parseDouble(String value, Double defaultValue) {
        try {
            return Double.parseDouble(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static boolean createAccountantReceipt(String ids) throws Exception {
        boolean isSuccess = false;
        Exception exception = null;
        CallableStatement cs = null;
        Connection conn = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(CREATE_ACCOUNTANT_RECEIPT);
            cs.setString(1, ids);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();

            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);

            if (nResult != 200)
                throw new Exception("DB create_accountant_receipt error: " + sResult);
            isSuccess = true;
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in createAccountantReceipt", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return isSuccess;
    }

    public static List<ExportReceiptStatusDTO> getPartners(Map mIn) throws Exception {
        ExportReceiptStatusDTO statusDTO;
        List<ExportReceiptStatusDTO> partnerList = new ArrayList<>();
        Exception exception = null;
        CallableStatement cs = null;
        Connection conn = null;
        ResultSet rs = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall(EXPORT_RECEIPT_PARTNERS);
            cs.setString(1, mIn.get(KEYWORD) != null ? mIn.get(KEYWORD).toString() : "");
            cs.setInt(2, Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setInt(3, Integer.parseInt(mIn.get(PAGE_SIZE).toString()));
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.CURSOR);
            cs.execute();

            String sResult = cs.getString(4);
            Integer nResult = cs.getInt(5);
            if (nResult != 200)
                throw new Exception("DB get_partner_list error: " + sResult);
            rs = (ResultSet) cs.getObject(6);
            while (rs != null && rs.next()) {
                statusDTO = new ExportReceiptStatusDTO();
                statusDTO.setLabel(Util.getColumnString(rs, "S_SHORT_NAME"));
                statusDTO.setValue(Util.getColumnString(rs, "N_ID"));
                partnerList.add(statusDTO);
            }
        } catch (Exception ex) {
            logger.log(Level.FINE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return partnerList;
    }

    public static void updateBatchReceipt(Integer[] ids, String state) throws Exception {
        Exception exception = null;
        PreparedStatement ps = null;
        Connection conn = null;
        try {
            conn = getConnection114();
            ps = conn.prepareCall(BATCH_UPDATE_ACCOUNTANT_RECEIPT);
            conn.setAutoCommit(false);
            for (Integer nId : ids) {
                ps.setInt(1, nId);
                ps.setString(2, state);
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit();
        } catch (Exception ex) {
            conn.rollback();
            logger.log(Level.FINE, "Error in batch update receipt", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, ps, null, conn);
        }
        if (exception != null)
            throw exception;
    }
}
