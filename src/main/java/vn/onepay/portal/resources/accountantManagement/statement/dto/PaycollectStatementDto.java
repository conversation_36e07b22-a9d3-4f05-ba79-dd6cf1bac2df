package vn.onepay.portal.resources.accountantManagement.statement.dto;

public class PaycollectStatementDto {
    private int order_number;
    private String name;
    private double discrepancy;
    private double out_amount;
    private double in_amount;
    private double advance_amount;
    private double statement_amount;

    public int getOrder_number() {
        return this.order_number;
    }

    public void setOrder_number(int order_number) {
        this.order_number = order_number;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getDiscrepancy() {
        return this.discrepancy;
    }

    public void setDiscrepancy(double discrepancy) {
        this.discrepancy = discrepancy;
    }

    public double getOut_amount() {
        return this.out_amount;
    }

    public void setOut_amount(double out_amount) {
        this.out_amount = out_amount;
    }

    public double getIn_amount() {
        return this.in_amount;
    }

    public void setIn_amount(double in_amount) {
        this.in_amount = in_amount;
    }

    public double getAdvance_amount() {
        return this.advance_amount;
    }

    public void setAdvance_amount(double advance_amount) {
        this.advance_amount = advance_amount;
    }

    public double getStatement_amount() {
        return this.statement_amount;
    }

    public void setStatement_amount(double statement_amount) {
        this.statement_amount = statement_amount;
    }

}
