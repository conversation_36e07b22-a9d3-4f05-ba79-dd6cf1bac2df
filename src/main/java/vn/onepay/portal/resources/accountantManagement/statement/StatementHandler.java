package vn.onepay.portal.resources.accountantManagement.statement;

import com.google.gson.Gson;
import io.vertx.core.file.FileSystem;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.accountantManagement.statement.dto.DetailStatementDto;
import vn.onepay.portal.resources.accountantManagement.statement.dto.PaycollectStatementDto;
import vn.onepay.portal.resources.accountantManagement.statement.dto.StatementDetailDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.ReadExcel;
import vn.onepay.portal.utils.RoutePool;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static vn.onepay.portal.IConstants.*;
import static vn.onepay.portal.Util.sendResponse;
import static vn.onepay.portal.utils.ParamsPool.X_USER_ID;

public class StatementHandler {
    private StatementHandler() {}
    private static final String TEMPLATE_FILE_PATH = Config.getString("accountant.template_file_path", "");
    private static final String TEMPLATE_FILE_NAME = Config.getString("accountant.template_statement_file", "");

    public static void listStatement(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                String date = httpServerRequest.getParam("date");
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
                String state = httpServerRequest.getParam(STATE) == null ? "" : httpServerRequest.getParam(STATE);

                String source = httpServerRequest.getParam(SOURCE);
                String session = httpServerRequest.getParam(SESSION);
                BaseList<Map<String, Object>> listResult = StatementDAO.listStatement(source, session, date, state, page, pageSize);

                Map<String, Object> responseMap = new HashMap<>();
                responseMap.put("totalItems", listResult.getTotalItems());
                responseMap.put("list", listResult.getList());
                if ("OF1".equals(source))
                    responseMap.putAll(getSummaryStm(date));
                else
                    responseMap.putAll(getSummaryStm2(date));
                sendResponse(ctx, 200, responseMap);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void listStatement2(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                List<Map<String, Object>> list = new ArrayList<>();

                String fromDate = httpServerRequest.getParam("d_from");
                String toDate = httpServerRequest.getParam("d_to");
                if (fromDate == null || fromDate.isEmpty()
                        || toDate == null || toDate.isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
                String state = httpServerRequest.getParam(STATE) == null ? "" : httpServerRequest.getParam(STATE);

                SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
                SimpleDateFormat sdf2 = new SimpleDateFormat(YYYYMMDD);
                SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd");
                LocalDate start = LocalDate.parse(sdf3.format(sdf.parse(fromDate)));
                LocalDate end = LocalDate.parse(sdf3.format(sdf.parse(toDate)));
                for (LocalDate date = start; date.isBefore(end.plusDays(1)); date = date.plusDays(1)) {
                    BaseList<Map<String, Object>> listResult = StatementDAO.listStatement("OF1", "C", sdf2.format(sdf3.parse(date.toString())), state, page, pageSize);
                    Map<String, Object> responseMapItem = new HashMap<>();
                    responseMapItem.put("totalItems", listResult.getTotalItems());
                    responseMapItem.put("list", listResult.getList());
                    responseMapItem.putAll(getSummaryStm(sdf2.format(sdf3.parse(date.toString()))));
                    list.add(responseMapItem);
                }

                Map<String, Object> responseMap = new HashMap<>();
                responseMap.put("response", list);
                sendResponse(ctx, 200, responseMap);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static Map<String, Object> getSummaryStm(String date) throws Exception {
        // xu ly them bang tom tat
        List<Map<String, Object>> listDetail = new ArrayList<>();
        List<Map<String, Object>> listFeeConfig = StatementDAO.listFeeConfig();
        Map<String, Object> feeConfigMap = listFeeConfig.stream().collect(
                Collectors.toMap(t -> (String) t.get(S_ACQ_BANK), t -> ((BigDecimal) t.get(N_FEE)).doubleValue()));

        BaseList<Map<String, Object>> listStmFull = StatementDAO.listStatement("OF1", "C", date, "", 0, 9999);
        Map<String, Object> advanceAmountMap = StatementDAO.searchAdvanceAmount(date);

        List<Map<String, Object>> listStm = listStmFull.getList();
        List<Map<String, Object>> listQt = listStm.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();

            return (exportType == 1 || exportType == 2);
        }).collect(Collectors.toList());

        double qtAmount = (Double) advanceAmountMap.get(ParamsPool.QT_AMOUNT);
        if ((listQt != null && !listQt.isEmpty()) || qtAmount != 0) {
            Map<String, Object> qtMap = new HashMap<>();
            qtMap.put(NAME, "Danh sách 1 - Quốc tế");
            qtMap.put(ParamsPool.ORDER_NUMBER, 1);
            qtMap.putAll(calDetail(listQt, advanceAmountMap, ParamsPool.QT_AMOUNT));
            listDetail.add(qtMap);
        }

        List<Map<String, Object>> listNd = listStm.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();

            return (exportType == 3 || exportType == 4 || exportType == 7);
        }).collect(Collectors.toList());

        double ndAmount = (Double) advanceAmountMap.get(ParamsPool.ND_AMOUNT);
        if ((listNd != null && !listNd.isEmpty()) || ndAmount != 0) {
            Map<String, Object> qtMap = new HashMap<>();
            qtMap.put(NAME, "Danh sách 2 - Nội địa");
            qtMap.put(ParamsPool.ORDER_NUMBER, 2);
            qtMap.putAll(calDetail(listNd, advanceAmountMap, ParamsPool.ND_AMOUNT));
            listDetail.add(qtMap);
        }

        List<Map<String, Object>> listWeek = listStm.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();

            return (exportType == 5 || exportType == 6);
        }).collect(Collectors.toList());

        double wAmount = (Double) advanceAmountMap.get(ParamsPool.WEEK_AMOUNT);
        if ((listWeek != null && !listWeek.isEmpty()) || wAmount != 0) {
            Map<String, Object> qtMap = new HashMap<>();
            qtMap.put(NAME, "Danh sách - Tuần");
            qtMap.put(ParamsPool.ORDER_NUMBER, 3);
            qtMap.putAll(calDetail(listWeek, advanceAmountMap, ParamsPool.WEEK_AMOUNT));
            listDetail.add(qtMap);
        }

        Map<String, Object> mapOtherStm = listStm.stream()
                .filter(t -> ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue() == 8).findAny().orElse(null);
        if (mapOtherStm != null && !mapOtherStm.isEmpty()) {
            double vcbAdvance = 0;
            double vcbAmount = 0;
            double vtbAmount = 0;
            double inTcbAmount = 0;
            double outTcbAmount = 0;
            double tcbAmount = 0;
            double stmTotal = 0;
            double vcbFee = 0;
            int vcbQuantity = 0;
            Map<String, Object> otherMap = new HashMap<>();
            Map<String, Object> vcbMap = new HashMap<>();
            Map<String, Object> tcbMap = new HashMap<>();
            long stmId = ((BigDecimal) mapOtherStm.get("N_ID")).longValue();

            BaseList<StatementDetailDTO> baseListOtherStm = StatementDAO.listStatementDetail(stmId, 0, 9999);
            List<StatementDetailDTO> listOtherStm = baseListOtherStm.getList();

            List<StatementDetailDTO> listVCB = listOtherStm.stream().filter(t -> "VCB".equals(t.getAcqBank()))
                    .collect(Collectors.toList());
            List<StatementDetailDTO> listVTB = listOtherStm.stream().filter(t -> "VTB".equals(t.getAcqBank()))
                    .collect(Collectors.toList());
            List<StatementDetailDTO> listInTCB = listOtherStm.stream()
                    .filter(t -> "TCB".equals(t.getAcqBank()) && "TCB".equals(t.getBankCode()))
                    .collect(Collectors.toList());
            List<StatementDetailDTO> listOutTCB = listOtherStm.stream()
                    .filter(t -> "TCB".equals(t.getAcqBank()) && !"TCB".equals(t.getBankCode()))
                    .collect(Collectors.toList());

            if (listVCB != null && !listVCB.isEmpty()) {
                vcbQuantity = listVCB.size();
                double vcbRate = feeConfigMap.get("VCB") == null ? 0 : (Double) feeConfigMap.get("VCB");
                vcbFee = vcbQuantity * vcbRate;
                vcbAdvance = listVCB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
                vcbAmount = vcbAdvance + vcbFee;
            }
            vtbAmount = listVTB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
            inTcbAmount = listInTCB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
            outTcbAmount = listOutTCB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
            tcbAmount = inTcbAmount + outTcbAmount;
            stmTotal = vcbAdvance + vtbAmount + tcbAmount;

            vcbMap.put(AMOUNT, vcbAdvance);
            vcbMap.put(FEE, vcbFee);
            vcbMap.put(ParamsPool.QUANTITY, vcbQuantity);
            vcbMap.put(TOTAL, vcbAmount);

            tcbMap.put(ParamsPool.IN_AMOUNT, inTcbAmount);
            tcbMap.put(ParamsPool.OUT_AMOUNT, outTcbAmount);
            tcbMap.put(TOTAL, tcbAmount);
            double statementAmount = (Double) advanceAmountMap.get(ParamsPool.OTHER_AMOUNT);
            double discrepancy = statementAmount - stmTotal;

            otherMap.put(NAME, "Danh sách - Khác");
            otherMap.put(ADVANCE_AMOUNT, statementAmount);
            otherMap.put(ParamsPool.ORDER_NUMBER, 4);
            otherMap.put("VCB", vcbMap);
            otherMap.put("VTB", vtbAmount);
            otherMap.put("TCB", tcbMap);
            otherMap.put(STATEMENT_AMOUNT, stmTotal);
            otherMap.put(ParamsPool.DISCREPANCY, discrepancy);
            listDetail.add(otherMap);
        }

        Map<String, Object> totalMap = new HashMap<>();
        Map<String, Object> fileMap = new HashMap<>();
        Map<String, Object> diffMap = new HashMap<>();
        // xu ly total map
        double advanceTotal = (Double) advanceAmountMap.get(ParamsPool.TOTAL_AMOUNT);
        double vcbAdvTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> vcbMap = (Map<String, Object>) t.get("VCB");
            return (Double) vcbMap.get(AMOUNT);
        }).sum();

        double vcbFeeTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> vcbMap = (Map<String, Object>) t.get("VCB");
            return (Double) vcbMap.get(FEE);
        }).sum();

        double vcbAmountTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> vcbMap = (Map<String, Object>) t.get("VCB");
            return (Double) vcbMap.get(TOTAL);
        }).sum();

        int vcbQuantityTotal = listDetail.stream().mapToInt(t -> {
            Map<String, Object> vcbMap = (Map<String, Object>) t.get("VCB");
            return (Integer) vcbMap.get(ParamsPool.QUANTITY);
        }).sum();

        double inTcbAmountTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> tcbMap = (Map<String, Object>) t.get("TCB");
            return (Double) tcbMap.get(ParamsPool.IN_AMOUNT);
        }).sum();

        double outTcbAmountTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> tcbMap = (Map<String, Object>) t.get("TCB");
            return (Double) tcbMap.get(ParamsPool.OUT_AMOUNT);
        }).sum();
        double tcbAmountTotal = listDetail.stream().mapToDouble(t -> {
            Map<String, Object> tcbMap = (Map<String, Object>) t.get("TCB");
            return (Double) tcbMap.get(TOTAL);
        }).sum();

        double vtbAmountTotal = listDetail.stream().mapToDouble(t -> (Double) t.get("VTB")).sum();
        double stmAmountTotal = listDetail.stream().mapToDouble(t -> (Double) t.get(STATEMENT_AMOUNT)).sum();
        double discrepancyTotal = listDetail.stream().mapToDouble(t -> (Double) t.get(ParamsPool.DISCREPANCY)).sum();

        Map<String, Object> vcbTotal = new HashMap<>();
        Map<String, Object> tcbTotal = new HashMap<>();
        vcbTotal.put(AMOUNT, vcbAdvTotal);
        vcbTotal.put(FEE, vcbFeeTotal);
        vcbTotal.put(ParamsPool.QUANTITY, vcbQuantityTotal);
        vcbTotal.put(TOTAL, vcbAmountTotal);

        tcbTotal.put(ParamsPool.IN_AMOUNT, inTcbAmountTotal);
        tcbTotal.put(ParamsPool.OUT_AMOUNT, outTcbAmountTotal);
        tcbTotal.put(TOTAL, tcbAmountTotal);

        totalMap.put(ADVANCE_AMOUNT, advanceTotal);
        totalMap.put("VCB", vcbTotal);
        totalMap.put("VTB", vtbAmountTotal);
        totalMap.put("TCB", tcbTotal);
        totalMap.put(STATEMENT_AMOUNT, stmAmountTotal);
        totalMap.put(ParamsPool.DISCREPANCY, discrepancyTotal);

        // xu ly file map
        List<Map<String, Object>> listStmDetail = StatementDAO.listStmDetailForIB(date, "OF1", "C");

        List<Map<String, Object>> listVCB = listStmDetail.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
            return exportType == 1 || exportType == 3 || exportType == 5;
        }).collect(Collectors.toList());

        List<Map<String, Object>> listVTB = listStmDetail.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
            return exportType == 7;
        }).collect(Collectors.toList());

        List<Map<String, Object>> listTCB = listStmDetail.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
            String bankCode = (String) t.get(S_BANK_CODE);
            return (exportType == 2 || exportType == 4 || exportType == 6) && (bankCode.equals("TCB"));
        }).collect(Collectors.toList());

        List<Map<String, Object>> listTCBOther = listStmDetail.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
            String bankCode = (String) t.get(S_BANK_CODE);
            return (exportType == 2 || exportType == 4 || exportType == 6) && (!bankCode.equals("TCB"));
        }).collect(Collectors.toList());

        List<Map<String, Object>> listOtherStm = listStmDetail.stream().filter(t -> {
            int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
            return exportType == 8;
        }).collect(Collectors.toList());

        for (Map<String, Object> stm : listOtherStm) {
            String acqBank = (String) stm.get(S_ACQ_BANK);

            if ("VCB".equals(acqBank))
                listVCB.add(stm);
            if ("VTB".equals(acqBank))
                listVTB.add(stm);
            if ("TCB".equals(acqBank)) {
                String bankCode = (String) stm.get(S_BANK_CODE);
                if (bankCode.equals("TCB"))
                    listTCB.add(stm);
                else
                    listTCBOther.add(stm);
            }
        }

        double vtbFile = listVTB.stream().mapToDouble(t -> ((BigDecimal) t.get(N_AMOUNT)).doubleValue()).sum();
        double inTcbFile = listTCB.stream().mapToDouble(t -> ((BigDecimal) t.get(N_AMOUNT)).doubleValue()).sum();
        double outTcbFile = listTCBOther.stream().mapToDouble(t -> ((BigDecimal) t.get(N_AMOUNT)).doubleValue())
                .sum();
        double tcbTotalFile = inTcbFile + outTcbFile;

        double advVcbFile = listVCB.stream().mapToDouble(t -> ((BigDecimal) t.get(N_AMOUNT)).doubleValue()).sum();
        int vcbQuantityFile = listVCB.size();
        double vcbRateFile = feeConfigMap.get("VCB") == null ? 0 : (Double) feeConfigMap.get("VCB");
        double vcbFeeFile = vcbQuantityFile * vcbRateFile;
        double vcbTotalFile = advVcbFile + vcbFeeFile;

        Map<String, Object> vcbFile = new HashMap<>();
        Map<String, Object> tcbFile = new HashMap<>();
        vcbFile.put(AMOUNT, advVcbFile);
        vcbFile.put(FEE, vcbFeeFile);
        vcbFile.put(ParamsPool.QUANTITY, vcbQuantityFile);
        vcbFile.put(TOTAL, vcbTotalFile);

        tcbFile.put(ParamsPool.IN_AMOUNT, inTcbFile);
        tcbFile.put(ParamsPool.OUT_AMOUNT, outTcbFile);
        tcbFile.put(TOTAL, tcbTotalFile);

        double totalFile = (Double) totalMap.get(STATEMENT_AMOUNT);
        fileMap.put("VCB", vcbFile);
        fileMap.put("TCB", tcbFile);
        fileMap.put("VTB", vtbFile);
        fileMap.put(TOTAL, totalFile);

        // xu ly file lech
        Map<String, Object> vcbDiff = new HashMap<>();
        Map<String, Object> tcbDiff = new HashMap<>();
        vcbDiff.put(AMOUNT, vcbAdvTotal - advVcbFile);
        vcbDiff.put(FEE, vcbFeeTotal - vcbFeeFile);
        vcbDiff.put(ParamsPool.QUANTITY, vcbQuantityTotal - vcbQuantityFile);
        vcbDiff.put(TOTAL, vcbAmountTotal - vcbTotalFile);

        tcbDiff.put(ParamsPool.IN_AMOUNT, inTcbAmountTotal - inTcbFile);
        tcbDiff.put(ParamsPool.OUT_AMOUNT, outTcbAmountTotal - outTcbFile);
        tcbDiff.put(TOTAL, tcbAmountTotal - tcbTotalFile);

        diffMap.put("VCB", vcbDiff);
        diffMap.put("TCB", tcbDiff);
        diffMap.put("VTB", vtbAmountTotal - vtbFile);
        diffMap.put(TOTAL, stmAmountTotal - totalFile);

        Map<String, Object> responseMap = new HashMap<>();

        responseMap.put(DETAIL, listDetail);
        responseMap.put(TOTAL, totalMap);
        responseMap.put("file", fileMap);
        responseMap.put("diff", diffMap);
        return responseMap;
    }

    public static Map<String, Object> getSummaryStm2(String date) throws Exception {
        Map<String, Object> responseMap = new HashMap<>();
        List<Map<String, Object>> rawData = StatementDAO.getSummaryPaycollect(date);
        double inAM = 0;
        double outAM = 0;
        double inPM = 0;
        double outPM = 0;
        for (Map<String, Object> map : rawData) {
            String session = (String) map.get("S_SESSION");
            boolean isVPB = "VPBank".equals(map.get("S_BANK_CODE"));
            double amount = ((BigDecimal) map.get(N_AMOUNT)).doubleValue();
            if ("S".equals(session)) {
                if (isVPB)
                    inAM += amount;
                else
                    outAM += amount;
            } else {
                if (isVPB)
                    inPM += amount;
                else
                    outPM += amount;
            }
        }
        List<Map<String, Object>> listDetail = new ArrayList<>();
        if (!rawData.isEmpty()) {
            Map<String, Object> advanceAmounts = StatementDAO.searchAdvance2Amount(date);
            double advanceAM = ((BigDecimal) advanceAmounts.get("V_S_AMOUNT")).doubleValue();
            double advancePM = ((BigDecimal) advanceAmounts.get("V_C_AMOUNT")).doubleValue();

            Map<String, Object> mapAM = new HashMap<>();
            mapAM.put(ParamsPool.ORDER_NUMBER, 1);
            mapAM.put(NAME, "DSTU (Phiên Sáng)");
            mapAM.put(ADVANCE_AMOUNT, advanceAM);
            mapAM.put(ParamsPool.IN_AMOUNT, inAM);
            mapAM.put(ParamsPool.OUT_AMOUNT, outAM);
            mapAM.put(STATEMENT_AMOUNT, inAM + outAM);
            mapAM.put(ParamsPool.DISCREPANCY, advanceAM - (inAM + outAM));
            listDetail.add(mapAM);

            Map<String, Object> mapPM = new HashMap<>();
            mapPM.put(ParamsPool.ORDER_NUMBER, 2);
            mapPM.put(NAME, "DSTU (Phiên Chiều)");
            mapPM.put(ADVANCE_AMOUNT, advancePM);
            mapPM.put(ParamsPool.IN_AMOUNT, inPM);
            mapPM.put(ParamsPool.OUT_AMOUNT, outPM);
            mapPM.put(STATEMENT_AMOUNT, inPM + outPM);
            mapPM.put(ParamsPool.DISCREPANCY, advancePM - (inPM + outPM));
            listDetail.add(mapPM);

            Map<String, Object> totalStatement = new HashMap<>();
            totalStatement.put(ADVANCE_AMOUNT, advanceAM + advancePM);
            totalStatement.put(ParamsPool.IN_AMOUNT, inAM + inPM);
            totalStatement.put(ParamsPool.OUT_AMOUNT, outAM + outPM);
            totalStatement.put(STATEMENT_AMOUNT, inPM + outPM + inAM + outAM);
            totalStatement.put(ParamsPool.DISCREPANCY, advanceAM + advancePM - (inPM + outPM + inAM + outAM));
            responseMap.put(TOTAL, totalStatement);
            // xu ly file map
            List<Map<String, Object>> listStmDetailIB = StatementDAO.listStmDetailForIB(date, "OF2", "S");
            listStmDetailIB.addAll(StatementDAO.listStmDetailForIB(date, "OF2", "C"));
            double inIB = 0;
            double outIB = 0;
            for (Map<String, Object> stmDetailIB : listStmDetailIB) {
                if ("VPBank".equals(stmDetailIB.get("S_BANK_CODE"))) {
                    inIB += ((BigDecimal) stmDetailIB.get(N_AMOUNT)).doubleValue();
                } else {
                    outIB += ((BigDecimal) stmDetailIB.get(N_AMOUNT)).doubleValue();
                }
            }
            Map<String, Object> fileMap = new HashMap<>();
            fileMap.put(ParamsPool.IN_AMOUNT, inIB);
            fileMap.put(ParamsPool.OUT_AMOUNT, outIB);
            fileMap.put(STATEMENT_AMOUNT, inIB + outIB);
            responseMap.put("file", fileMap);

            Map<String, Object> diffMap = new HashMap<>();
            diffMap.put(ParamsPool.IN_AMOUNT, inIB - (inAM + inPM));
            diffMap.put(ParamsPool.OUT_AMOUNT, outIB - (outAM + outPM));
            diffMap.put(STATEMENT_AMOUNT, inIB + outIB - (inPM + outPM + inAM + outAM));
            responseMap.put("diff", diffMap);
        }
        responseMap.put(DETAIL, listDetail);
        return responseMap;
    }

    private static Map<String, Object> calDetail(List<Map<String, Object>> listData,
            Map<String, Object> advanceAmountMap, String type) throws Exception {
        double vcbAdvance = 0;
        double vcbAmount = 0;
        double vtbAmount = 0;
        double inTcbAmount = 0;
        double outTcbAmount = 0;
        double tcbAmount = 0;
        double stmTotal = 0;
        double vcbFee = 0;
        int vcbQuantity = 0;

        for (Map<String, Object> stmMap : listData) {
            long stmId = ((BigDecimal) stmMap.get("N_ID")).longValue();
            int exportType = ((BigDecimal) stmMap.get(N_EXPORT_TYPE)).intValue();
            BaseList<StatementDetailDTO> baseListStmDetail = StatementDAO.listStatementDetail(stmId, 0, 9999);
            List<StatementDetailDTO> listStmDetail = baseListStmDetail.getList();
            // vcb
            if (exportType == 1 || exportType == 3 || exportType == 5) {
                vcbQuantity += listStmDetail.size();
                vcbAdvance += ((BigDecimal) stmMap.get(N_AMOUNT)).doubleValue();
                vcbFee += ((BigDecimal) stmMap.get(N_FEE)).doubleValue();
                vcbAmount += ((BigDecimal) stmMap.get("N_TOTAL_AMOUNT")).doubleValue();
            }

            // tcb
            if (exportType == 2 || exportType == 4 || exportType == 6) {
                List<StatementDetailDTO> listInTCB = listStmDetail.stream().filter(t -> "TCB".equals(t.getBankCode()))
                        .collect(Collectors.toList());
                List<StatementDetailDTO> listOutTCB = listStmDetail.stream().filter(t -> !"TCB".equals(t.getBankCode()))
                        .collect(Collectors.toList());
                inTcbAmount += listInTCB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
                outTcbAmount += listOutTCB.stream().mapToDouble(StatementDetailDTO::getAmount).sum();
                tcbAmount += ((BigDecimal) stmMap.get(N_AMOUNT)).doubleValue();
            }

            // vtb
            if (exportType == 7) {
                vtbAmount += ((BigDecimal) stmMap.get(N_AMOUNT)).doubleValue();
            }
        }
        stmTotal = vcbAdvance + vtbAmount + tcbAmount;

        Map<String, Object> detailMap = new HashMap<>();
        Map<String, Object> vcbMap = new HashMap<>();
        Map<String, Object> tcbMap = new HashMap<>();
        vcbMap.put(AMOUNT, vcbAdvance);
        vcbMap.put(FEE, vcbFee);
        vcbMap.put(ParamsPool.QUANTITY, vcbQuantity);
        vcbMap.put(TOTAL, vcbAmount);

        tcbMap.put(ParamsPool.IN_AMOUNT, inTcbAmount);
        tcbMap.put(ParamsPool.OUT_AMOUNT, outTcbAmount);
        tcbMap.put(TOTAL, tcbAmount);
        double statementAmount = (Double) advanceAmountMap.get(type);
        double discrepancy = statementAmount - stmTotal;
        detailMap.put(ADVANCE_AMOUNT, statementAmount);
        detailMap.put(ParamsPool.ORDER_NUMBER, 4);
        detailMap.put("VCB", vcbMap);
        detailMap.put("VTB", vtbAmount);
        detailMap.put("TCB", tcbMap);
        detailMap.put(STATEMENT_AMOUNT, stmTotal);
        detailMap.put(ParamsPool.DISCREPANCY, discrepancy);

        return detailMap;
    }


    public static void listStatementDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                long id = httpServerRequest.getParam("statement_id") == null ? 0
                        : Long.parseLong(httpServerRequest.getParam("statement_id"));
                if (id == 0)
                    throw IErrors.VALIDATION_ERROR;
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));

                BaseList<StatementDetailDTO> listResult = StatementDAO.listStatementDetail(id, page, pageSize);
                sendResponse(ctx, 200, listResult);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateStatementStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jRequest = ctx.getBodyAsJson();
                String date = jRequest.getString("date");
                String state = jRequest.getString(STATE);
                Integer userId = jRequest.getInteger(USER_ID);
                String source = jRequest.getString(SOURCE);
                String session = jRequest.getString(SESSION);

                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;

                if (state == null || state.isEmpty() || (!state.equals(CHECKED) && !state.equals("un_checked")
                        && !state.equals("approved") && !state.equals("un_approved")))
                    throw IErrors.VALIDATION_ERROR;
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;
                boolean isUpdated = StatementDAO.updateStatementStatus(source, session, userId, state, date);

                if (isUpdated && state.equals(CHECKED)) {
                    try {
                        StatementDAO.createAccountantIb(source, session, date);
                        Map<String, Object> summaryMap = "OF1".equals(source) ? getSummaryStm(date) : getSummaryStm2(date);
                        List<Map<String, Object>> listDetail = (List<Map<String, Object>>) summaryMap.get(DETAIL);
                        Map<String, Object> totalMap = (Map<String, Object>) summaryMap.get(TOTAL);
                        Map<String, Object> fileMap = (Map<String, Object>) summaryMap.get("file");
                        Map<String, Object> diffMap = (Map<String, Object>) summaryMap.get("diff");
                        String mailContent;
                        if ("OF1".equals(source)) {
                            List<DetailStatementDto> listDto = listDetail.stream()
                                    .map(t -> Util.gson.fromJson(new JsonObject(t).encode(), DetailStatementDto.class))
                                    .collect(Collectors.toList());

                            mailContent = generateMailContent(listDto, new JsonObject(totalMap), new JsonObject(fileMap), new JsonObject(diffMap),
                                    date);
                        } else {
                            List<PaycollectStatementDto> listDto = listDetail.stream()
                                    .map(t -> Util.gson.fromJson(new JsonObject(t).encode(), PaycollectStatementDto.class))
                                    .collect(Collectors.toList());

                            mailContent = generateMailPaycollect(listDto, new JsonObject(totalMap), new JsonObject(fileMap), new JsonObject(diffMap),
                                    date);
                        }
                        String host = Config.getString("accountant.email.host", "");
                        Integer port = Config.getInteger("accountant.email.port", 25);
                        String username = Config.getString("accountant.email.username", "");
                        String password = Config.getString("accountant.email.password", "");
                        String fromEmail = Config.getString("accountant.email.address", "");
                        String toEmail = Config.getString("accountant.email.toEmail", "");
                        String ccEmail = Config.getString("accountant.email.ccEmail", "");

                        String subject = "File up lệnh DS ngày "
                                + date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$3/$2/$1");

                        MailUtil.sendMail2(host, port, username, password, fromEmail, toEmail, ccEmail, subject,
                                mailContent);
                    } catch (Exception ex) {
                        logger.log(Level.SEVERE, ERROR, ex);
                    }
                }
                int statusRes = isUpdated ? 200 : 500;
                sendResponse(ctx, statusRes, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void createStatement(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jRequest = ctx.getBodyAsJson();
                String date = jRequest.getString("date");
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                Integer userId = jRequest.getInteger(USER_ID);
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;
                String source = jRequest.getString(SOURCE);
                String session = jRequest.getString(SESSION);
                BaseList<Map<String, Object>> listStm = StatementDAO.createAccountantStatement(source, session, date, userId, "create");
                sendResponse(ctx, 200, listStm);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void recreateStatement(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jRequest = ctx.getBodyAsJson();
                String date = jRequest.getString("date");
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                Integer userId = jRequest.getInteger(USER_ID);
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;
                String source = jRequest.getString(SOURCE);
                String session = jRequest.getString(SESSION);
                BaseList<Map<String, Object>> listStm = StatementDAO.createAccountantStatement(source, session, date, userId, "recreate");
                sendResponse(ctx, 200, listStm);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadStatement(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String xUserId = ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID);
                JsonObject jRequest = ctx.getBodyAsJson();
                String ids = jRequest.getString("statement_ids");

                if (ids == null || ids.isEmpty())
                    throw IErrors.VALIDATION_ERROR;
                String[] arrIds = ids.split(",");
                Integer[] intIds = new Integer[arrIds.length];
                for (int i = 0; i < arrIds.length; i++) {
                    intIds[i] = Integer.parseInt(arrIds[i].trim());
                }
                String fileName = "statement_" + System.currentTimeMillis();
                String fileHashName = Convert.hash(fileName);

                List<Map<String, Object>> listStm = StatementDAO.listStatementByIds(intIds);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put("list_stm", listStm);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, xUserId);
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(xUserId);
                fileDownloadDto.setFile_type("accountant_statement");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setExt("zip");

                FileDownloadDao.insert(fileDownloadDto);

                String requestPath = RoutePool.ACCOUNTANT_EXPORT_STATEMENT_FILE;
                QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                        QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), requestPath, requestData));
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadTemplateFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info("========downloadTemplateFile===========");
                String[] arr = TEMPLATE_FILE_NAME.split("\\.");
                String fileName = arr[0];
                String fileExt = arr[1];

                Path requestPath = FileSystems.getDefault().getPath(TEMPLATE_FILE_PATH + File.separator + TEMPLATE_FILE_NAME)
                        .normalize();

                FileSystem fs = ctx.vertx().fileSystem();
                fs.exists(requestPath.toString(), ar -> {
                    if (ar.succeeded() && Boolean.TRUE.equals(ar.result())) {
                        Map<String, Object> data = new HashMap<>();
                        data.put(ParamsPool.PATH_FILE, requestPath.toString());
                        data.put(ParamsPool.FILE_NAME, fileName);
                        data.put(ParamsPool.FILE_EXT, fileExt);
                        try {
                            data.put(ParamsPool.FILE_SIZE,
                                    String.valueOf(Files.size(Paths.get(requestPath.toString()))));
                        } catch (IOException e) {
                            logger.log(Level.SEVERE, ERROR, e);
                            ctx.fail(e);
                        }
                        ctx.response().setChunked(true);

                        String extFile = data.get(ParamsPool.FILE_EXT) == null ? "csv"
                                : String.valueOf(data.get(ParamsPool.FILE_EXT));
                        String fileOrg = data.get(ParamsPool.FILE_NAME) == null ? "file"
                                : String.valueOf(data.get(ParamsPool.FILE_NAME));
                        String contentType = "application/octet-stream";
                        if (extFile.equals("zip")) {
                            contentType = "application/zip";
                        }
                        ctx.response().putHeader(String.valueOf(HttpHeaders.CONTENT_TYPE), contentType);
                        ctx.response().putHeader("Content-Disposition",
                                "attachment; filename=" + fileOrg + "." + extFile);

                        ctx.response().sendFile(requestPath.toString(), result -> {
                            if (result.succeeded()) {
                                logger.info("Download Success");
                            } else {
                                logger.info("Download Fail");
                            }
                        });
                    } else {
                        ctx.fail(IErrors.RESOURCE_NOT_FOUND);
                    }
                });
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void uploadStatement(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String date = ctx.request().getParam("date");
                int userId = ctx.request().getParam(USER_ID) == null ? 0
                        : Integer.parseInt(ctx.request().getParam(USER_ID));
                if (userId == 0)
                    throw IErrors.VALIDATION_ERROR;
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                String source = ctx.request().getParam(SOURCE);
                String session = ctx.request().getParam(SESSION);
                // check state of stm
                BaseList<Map<String, Object>> listResult = StatementDAO.listStatement(source, session, date, "", 0, 9999);
                List<Map<String, Object>> listStm = listResult.getList();
                if (listStm != null) {
                    List<Map<String, Object>> listFilter = listStm.stream()
                            .filter(t -> t.get(S_STATE).toString().equals(CHECKED) || t.get(S_STATE).toString().equals("approved")).collect(Collectors.toList());
                    if (listFilter != null && !listFilter.isEmpty())
                        throw IErrors.INVALID_STATEMENT_STATE;

                }
                SimpleDateFormat format = new SimpleDateFormat(YYYYMMDD);
                Date parsed = format.parse(date);
                java.sql.Date sqlDate = new java.sql.Date(parsed.getTime());

                Set<FileUpload> fileUploadSet = ctx.fileUploads();

                FileUpload fileUpload = fileUploadSet.stream().findFirst().orElse(null);
                if (fileUpload == null)
                    throw IErrors.VALIDATION_ERROR;

                List<Map> listImport = null;
                Map<String, Object> mTemp = new HashMap<>();
                mTemp.put("row_start", 1);
                mTemp.put("sheet_number", 0);
                List<String> columns = new ArrayList<>();
                columns.add("no");
                columns.add("paymentVoucher");
                columns.add("merchantId");
                columns.add("accountNumber");
                columns.add("accountName");
                columns.add("acqBank");
                columns.add("bankCode");
                columns.add(AMOUNT);
                columns.add("bankProvince");
                columns.add("bankBranch");
                columns.add(DETAIL);
                mTemp.put("list_column_name", columns);

                try {
                    logger.info("file Type: " + fileUpload.contentType());
                    if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            .equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXlsx2(fileUpload.uploadedFileName(), mTemp);
                    } else if ("application/vnd.ms-excel".equals(fileUpload.contentType())) {
                        listImport = ReadExcel.readExcelXls2(fileUpload.uploadedFileName(), mTemp);
                    } else {
                        throw IErrors.INVALID_FILE_MUST_EXCEL;
                    }
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "ERROR TO MERCHANT ACCOUNTANT IMPORT: ", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }

                if (listImport == null || listImport.isEmpty()) {
                    logger.info("No import file data found");
                    throw IErrors.VALIDATION_ERROR;
                }
                for (int i = 0; i < listImport.size(); i++) {
                    logger.info("value import:" + new JsonObject(listImport.get(i)));
                }
                precheckListStmImport(ctx, listImport);
                double amount = ctx.get("total_amount");
                double fee = ctx.get("total_fee");
                StatementDAO.importStatement(source, session, listImport, sqlDate, userId, fee, amount, fee + amount);
                sendResponse(ctx, 200, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static void precheckListStmImport(RoutingContext ctx, List<Map> listImport) throws Exception {
        double totalAmount = 0;
        double totalFee = 0;
        List<Map<String, Object>> listFeeConfig = StatementDAO.listFeeConfig();
        Map<String, Object> feeConfigMap = listFeeConfig.stream().collect(
                Collectors.toMap(t -> (String) t.get(S_ACQ_BANK), t -> ((BigDecimal) t.get(N_FEE)).doubleValue()));
        logger.log(Level.INFO, "feeConfigMap={0}", new JsonObject(feeConfigMap));
        for (int i = 0; i < listImport.size(); i++) {
            Map<String, Object> t = listImport.get(i);
            String error = "";
            if (t.getOrDefault("paymentVoucher", "").toString().trim().isEmpty()) {
                error = "PaymentVoucher is blank.";
            }
            if (t.getOrDefault("merchantId", "").toString().trim().isEmpty()) {
                error = "MerchantId is blank.";
            }
            if (t.getOrDefault("accountNumber", "").toString().trim().isEmpty()) {
                error = "AccountNumber is blank.";
            }
            if (t.getOrDefault("accountName", "").toString().trim().isEmpty()) {
                error = "AccountName is blank.";
            }
            if (t.getOrDefault("bankCode", "").toString().trim().isEmpty()) {
                error = "BankCode is blank.";
            }

            String acqBank = t.getOrDefault("acqBank", "").toString().trim();

            if (acqBank.isEmpty()) {
                error = "AcqBank is blank.";
            }
            totalFee += feeConfigMap.get(acqBank) == null ? 0 : (Double) feeConfigMap.get(acqBank);

            String amountStr = t.getOrDefault(AMOUNT, "").toString().trim();
            logger.log(Level.INFO, "amountStr={0}", amountStr);
            if (amountStr.isEmpty()) {
                error = "Amount is blank.";
            }
            try {
                double amount = Double.parseDouble(amountStr);
                if (amount <= 0)
                    error = "Amount must be greater than 0.";
                totalAmount += amount;
            } catch (Exception ex) {
                error = "Amount is invalid format.";
            }

            if (!error.isEmpty()) {
                error = "Import failed at row " + (i + 2) + ". " + error;
                throw new ErrorException(400, "IMPORT_FAILED", error, "", error);
            }
        }
        ctx.put("total_amount", totalAmount);
        ctx.put("total_fee", totalFee);
    }

    public static void downloadStatementSummary(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String xUserId = ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID);
                String date = ctx.request().getParam("date");
                String source = ctx.request().getParam(SOURCE);
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;

                String fileName = null;
                if ("OF1".equals(source)) {
                    fileName = "stm_summary_" + date + "_file_" + System.currentTimeMillis();
                } else {
                    StringBuilder sb = new StringBuilder(date);
                    sb.insert(4, '.');
                    sb.insert(7, '.');
                    fileName = "Summary Paycollect " + sb.toString();
                }
                String fileHashName = Convert.hash(fileName);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put("date", date);
                mIn.put(SOURCE, source);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, xUserId);
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(xUserId);
                fileDownloadDto.setFile_type("accountant_stm_summary_file");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                fileDownloadDto.setExt("xls");

                FileDownloadDao.insert(fileDownloadDto);

                QueueProducer.sendMessage(
                        new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }

        }, false, null);
    }

    public static void downloadIBFile(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String xUserId = ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID);
                String date = ctx.request().getParam("date");
                if (date == null || date.isEmpty() || !Util.isValidDate(date, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;

                String source = ctx.request().getParam(SOURCE);
                String session = ctx.request().getParam(SESSION);
                BaseList<Map<String, Object>> listResult = StatementDAO.listStatement(source, session, date, "", 0, 9999);
                List<Map<String, Object>> listStm = listResult.getList();
                if (listStm == null || listStm.isEmpty())
                    throw IErrors.RESOURCE_NOT_FOUND;
                List<Map<String, Object>> listFilter = listStm.stream()
                        .filter(t -> t.get(S_STATE).toString().equals("created")).collect(Collectors.toList());
                if (!listFilter.isEmpty())
                    throw IErrors.INVALID_STATEMENT_STATE;

                String fileName = "ib_" + date + "_file_" + System.currentTimeMillis();
                String fileHashName = Convert.hash(fileName);

                Map<String, Object> mIn = new HashMap<>();
                mIn.put("date", date);
                mIn.put(SOURCE, source);
                mIn.put(SESSION, session);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, xUserId);
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(xUserId);
                fileDownloadDto.setFile_type("accountant_ib_file");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                fileDownloadDto.setExt("zip");

                FileDownloadDao.insert(fileDownloadDto);

                String requestPath = RoutePool.ACCOUNTANT_EXPORT_IB_FILE;
                QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                        QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), requestPath, requestData));
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static String generateMailContent(List<DetailStatementDto> list, JsonObject totalStatement,
            JsonObject fileStatement, JsonObject diffStatement, String date) throws Exception {
        File file = new File(StatementHandler.class.getClassLoader()
                .getResource("templates/mails/temp_request_accountant_statement.html").getFile());
        String content = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);

        StringBuilder detailTable = new StringBuilder();
        Integer count = 0;
        for (DetailStatementDto item : list) {
            count++;
            detailTable.append("<tr class='tr_body ng-star-inserted'>");
            detailTable.append("<td align='center'>" + count + "</td>");
            detailTable.append("<td align='left'>" + item.getName() + "</td>");
            detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(item.getAdvance_amount()) + "</b></td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getVCB().get(AMOUNT).toString())) + "</td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getVCB().get("quantity").toString())) + "</td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getVCB().get("fee").toString())) + "</td>");
            detailTable.append("<td align='right'><b>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getVCB().get(TOTAL).toString())) + "</b></td>");
            detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(item.getVTB()) + "</b></td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getTCB().get("in_amount").toString()))
                    + "</td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getTCB().get("out_amount").toString()))
                    + "</td>");
            detailTable.append("<td align='right'><b>"
                    + Util.formatCurrencyDouble(Double.parseDouble(item.getTCB().get(TOTAL).toString())) + "</b></td>");
            detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(item.getStatement_amount()) + "</b></td>");
            detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(item.getDiscrepancy()) + "</font></b></td>");
            detailTable.append("</tr>");
        }

        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>TOTAL</td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(totalStatement.getDouble(ADVANCE_AMOUNT))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("VCB").getDouble(AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("VCB").getDouble("quantity").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(
                Double.parseDouble(totalStatement.getJsonObject("VCB").getDouble("fee").toString())) + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("VCB").getDouble(TOTAL).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(totalStatement.getDouble("VTB")) + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("TCB").getDouble("in_amount").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("TCB").getDouble("out_amount").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getJsonObject("TCB").getDouble(TOTAL).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(totalStatement.getDouble(STATEMENT_AMOUNT)) + "</b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(totalStatement.getDouble("discrepancy"))
                + "</font></b></td>");
        detailTable.append("</tr>");
        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>FILE UP LỆNH</td>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getJsonObject("VCB").getDouble(AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getJsonObject("VCB").getDouble("quantity").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(
                Double.parseDouble(fileStatement.getJsonObject("VCB").getDouble("fee").toString())) + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(
                Double.parseDouble(fileStatement.getJsonObject("VCB").getDouble(TOTAL).toString())) + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(fileStatement.getDouble("VTB")) + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getJsonObject("TCB").getDouble("in_amount").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getJsonObject("TCB").getDouble("out_amount").toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(
                Double.parseDouble(fileStatement.getJsonObject("TCB").getDouble(TOTAL).toString())) + "</td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(fileStatement.getDouble(TOTAL))
                + "</b></td>");
        detailTable.append("<td align='right'></td>");
        detailTable.append("</tr>");
        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>FILE UP - BẢNG KÊ</td>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getJsonObject("VCB").getDouble(AMOUNT).toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getJsonObject("VCB").getDouble("quantity").toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(
                Double.parseDouble(diffStatement.getJsonObject("VCB").getDouble("fee").toString())) + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(
                Double.parseDouble(diffStatement.getJsonObject("VCB").getDouble(TOTAL).toString())) + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(diffStatement.getDouble("VTB")) + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getJsonObject("TCB").getDouble("in_amount").toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getJsonObject("TCB").getDouble("out_amount").toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(
                Double.parseDouble(diffStatement.getJsonObject("TCB").getDouble(TOTAL).toString())) + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(diffStatement.getDouble(TOTAL))
                + "</font></b></td>");
        detailTable.append("<td align='right'></td>");
        detailTable.append("</tr>");
        content = content.replace("${TABLE_DETAIL}", detailTable);
        content = content.replace("${DATE}", date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$3/$2/$1"));

        return content;
    }

    private static String generateMailPaycollect(List<PaycollectStatementDto> list, JsonObject totalStatement,
            JsonObject fileStatement, JsonObject diffStatement, String date) throws Exception {
        File file = new File(StatementHandler.class.getClassLoader()
                .getResource("templates/mails/temp_request_accountant_statement_paycollect.html").getFile());
        String content = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);

        StringBuilder detailTable = new StringBuilder();
        Integer count = 0;
        for (PaycollectStatementDto item : list) {
            count++;
            detailTable.append("<tr class='tr_body ng-star-inserted'>");
            detailTable.append("<td align='center'>" + count + "</td>");
            detailTable.append("<td align='left'>" + item.getName() + "</td>");
            detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(item.getAdvance_amount()) + "</b></td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(item.getIn_amount())
                    + "</td>");
            detailTable.append("<td align='right'>"
                    + Util.formatCurrencyDouble(item.getOut_amount())
                    + "</td>");
            detailTable.append("<td align='right'><b>"
                    + Util.formatCurrencyDouble(item.getStatement_amount()) + "</b></td>");
            detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(item.getDiscrepancy()) + "</font></b></td>");
            detailTable.append("</tr>");
        }
        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>TOTAL</td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(totalStatement.getDouble(ADVANCE_AMOUNT))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getDouble(ParamsPool.IN_AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getDouble(ParamsPool.OUT_AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(totalStatement.getDouble(STATEMENT_AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(totalStatement.getDouble(ParamsPool.DISCREPANCY))
                + "</font></b></td>");
        detailTable.append("</tr>");
        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>FILE UP LỆNH</td>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getDouble(ParamsPool.IN_AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(fileStatement.getDouble(ParamsPool.OUT_AMOUNT).toString()))
                + "</b></td>");
        detailTable.append("<td align='right'><b>" + Util.formatCurrencyDouble(
                Double.parseDouble(fileStatement.getDouble(STATEMENT_AMOUNT).toString())) + "</td>");
        detailTable.append("<td align='right'></td>");
        detailTable.append("</tr>");
        detailTable.append("<tr class='tr_body tr_footer'>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='left'>FILE UP - BẢNG KÊ</td>");
        detailTable.append("<td align='center'></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getDouble(ParamsPool.IN_AMOUNT).toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>"
                + Util.formatCurrencyDouble(
                        Double.parseDouble(diffStatement.getDouble(ParamsPool.OUT_AMOUNT).toString()))
                + "</font></b></td>");
        detailTable.append("<td align='right'><b><font color='#C9211E'>" + Util.formatCurrencyDouble(
                Double.parseDouble(diffStatement.getDouble(STATEMENT_AMOUNT).toString())) + "</font></b></td>");
        detailTable.append("<td align='right'></td>");
        detailTable.append("</tr>");
        content = content.replace("${TABLE_DETAIL}", detailTable);
        content = content.replace("${DATE}", date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$3/$2/$1"));

        return content;
    }

    private static final Logger logger = Logger.getLogger(StatementHandler.class.getName());
    private static final Gson gson = new Gson();
}
