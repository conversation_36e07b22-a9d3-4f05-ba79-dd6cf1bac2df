package vn.onepay.portal.resources.accountantManagement.report;

import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.accountantManagement.report.dto.ExplanationDetailDTO;
import vn.onepay.portal.resources.accountantManagement.report.dto.ReportDetailDTO;
import vn.onepay.portal.resources.accountantManagement.statement.dto.StatementDetailDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import static vn.onepay.portal.Util.resultSetToList;

public class ReportDAO extends Db {
    private static final String LIST_REPORT = "{call ONEFIN.pkg_accountant_report.list_report(?,?,?,?,?,?,?,?)}";
    private static final String LIST_REPORT_BY_ID = "{call ONEFIN.pkg_accountant_report.list_report_by_id(?,?,?,?)}";
    private static final String LIST_REPORT_DETAIL = "{call ONEFIN.pkg_accountant_report.list_report_detail(?,?,?,?,?,?,?,?)}";
    private static final String CREATE_ACCOUNTANT_REPORT = "{call ONEFIN.pkg_accountant_report.create_report(?,?,?,?,?,?)}";
    private static final String UPDATE_ACCOUNTANT_REPORT = "{call ONEFIN.pkg_accountant_report.update_report(?,?,?,?,?,?,?,?)}";
    private static final String UPDATE_ACCOUNTANT_EXPLANATION = "{call ONEFIN.pkg_accountant_report.update_explanation(?,?,?,?,?,?,?,?,?)}";
    private static final String GET_STATEMENT_REPORT = "{call ONEFIN.PKG_ACCOUNTANT_REPORT.get_report_by_id(?,?,?,?,?) }";
    private static final String DELETE_EXPLANATION_BY_REPORT_ID = "{call ONEFIN.PKG_ACCOUNTANT_REPORT.delete_explanation(?,?,?)}";
    private static final String UPDATE_REPORT_STATUS = "{call ONEFIN.PKG_ACCOUNTANT_REPORT.update_report_status(?,?,?,?,?)}";

    public static BaseList<Map<String, Object>> listReport(String fromDate, String toDate, int page, int pageSize)
            throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_REPORT);
            cs.setString(1, fromDate);
            cs.setString(2, toDate);
            cs.setInt(3, page);
            cs.setInt(4, pageSize);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "List statement fail: {0}", statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            int total = cs.getInt(7);
            rs = (ResultSet) cs.getObject(8);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            result.setList(list);
            result.setTotalItems(total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<ReportDetailDTO> listReportDetail(String id, String method, int page, int pageSize) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<ReportDetailDTO> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_REPORT_DETAIL);
            cs.setString(1, id);
            cs.setString(2, method);
            cs.setInt(3, page);
            cs.setInt(4, pageSize);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("List report detail fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = cs.getInt(7);
            List<ReportDetailDTO> listReportDetail = new ArrayList<>();
            rs = (ResultSet) cs.getObject(8);
            while (rs != null && rs.next()) {
                listReportDetail.add(blindStatementReport(rs));
            }
            result = new BaseList<>();
            result.setTotalItems(total);
            result.setList(listReportDetail);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<ExplanationDetailDTO> listExplanationDetail(String id, String method, int page, int pageSize) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<ExplanationDetailDTO> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_REPORT_DETAIL);
            cs.setString(1, id);
            cs.setString(2, method);
            cs.setInt(3, page);
            cs.setInt(4, pageSize);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("List explanation detail fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = cs.getInt(7);
            List<ExplanationDetailDTO> listExplanationDetail = new ArrayList<>();
            rs = (ResultSet) cs.getObject(8);
            while (rs != null && rs.next()) {
                listExplanationDetail.add(blindStatementExplanation(rs));
            }
            result = new BaseList<>();
            result.setTotalItems(total);
            result.setList(listExplanationDetail);
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<Map<String, Object>> listReportById(Long id) throws Exception {
        Exception exception = null;
        // Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            // conn = getConnection114();
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                cs = conn.prepareCall(LIST_REPORT_BY_ID);
                cs.setLong(1, id);
                cs.registerOutParameter(2, OracleTypes.NUMBER);
                cs.registerOutParameter(3, OracleTypes.VARCHAR);
                cs.registerOutParameter(4, OracleTypes.CURSOR);
                cs.execute();

                int statusCode = cs.getInt(2);
                String statusDesc = cs.getString(3);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("List report by id fail: " + statusDesc);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                rs = (ResultSet) cs.getObject(4);
                result = resultSetToList(rs);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, null);
            // try {
            // connection.close();
            // } catch (Exception ex) {
            // logger.log(Level.SEVERE, ERROR, ex);
            // }
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<Map<String, Object>> createAccountantReport(String fromDate, String toDate, int userId) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        String sql = CREATE_ACCOUNTANT_REPORT;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(sql);
            cs.setString(1, fromDate);
            cs.setString(2, toDate);
            cs.setInt(3, userId);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.registerOutParameter(5, OracleTypes.VARCHAR);
            cs.registerOutParameter(6, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(4);
            String statusDesc = cs.getString(5);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("create statement fail: " + statusDesc);
                if (statusCode == 400) {
                    if (statusDesc.equals("REPORT_DATA_NOT_FOUND"))
                        throw IErrors.REPORT_DATA_NOT_FOUND;
                    else if (statusDesc.equals("REPORT_EXISTED"))
                        throw IErrors.REPORT_EXISTED;
                    else
                        throw IErrors.INTERNAL_SERVER_ERROR;
                } else
                    throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) cs.getObject(6);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            result.setList(list);
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<Map<String, Object>> updateAccountantReport(long reportId, int userId, double ibVcb,
            double ibVtb, double ibTcb) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        String sql = UPDATE_ACCOUNTANT_REPORT;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(sql);
            cs.setLong(1, reportId);
            cs.setInt(2, userId);
            cs.setDouble(3, ibVcb);
            cs.setDouble(4, ibVtb);
            cs.setDouble(5, ibTcb);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.registerOutParameter(7, OracleTypes.VARCHAR);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(6);
            String statusDesc = cs.getString(7);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("update statement report fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) cs.getObject(8);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            result.setList(list);
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<Map<String, Object>> updateAccountantExplanation(long reportId, int userId, String bank,
            String desc, int count, double amount) throws Exception {
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<Map<String, Object>> result = null;
        String sql = UPDATE_ACCOUNTANT_EXPLANATION;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(sql);
            cs.setLong(1, reportId);
            cs.setInt(2, userId);
            cs.setString(3, bank);
            cs.setString(4, desc);
            cs.setInt(5, count);
            cs.setDouble(6, amount);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.VARCHAR);
            cs.registerOutParameter(9, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(7);
            String statusDesc = cs.getString(8);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("update statement report fail: " + statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) cs.getObject(9);
            result = new BaseList<>();
            List<Map<String, Object>> list = resultSetToList(rs);
            result.setList(list);
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalReport(Map mIn, String type) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_STATEMENT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get("id").toString());
            cs.setString(5, type);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_TOTAL_REPORT: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ReportDetailDTO> getStatementReport(Map mIn, String type) throws Exception {
        Exception exception = null;
        List<ReportDetailDTO> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_STATEMENT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get("id").toString());
            cs.setString(5, "SELECT_REPORT");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_STATEMENT_REPORT: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(blindStatementReport(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ExplanationDetailDTO> getStatementExplanation(Map mIn, String type) throws Exception {
        Exception exception = null;
        List<ExplanationDetailDTO> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_STATEMENT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get("id").toString());
            cs.setString(5, "SELECT_EXPLANATION");
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_STATEMENT_EXPLANATION: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(blindStatementExplanation(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static boolean deleteExplanationByReportId(int id) throws Exception {
        Exception exception;
        Connection conn = null;
        CallableStatement cs = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(DELETE_EXPLANATION_BY_REPORT_ID);
            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(2);
            String statusDesc = cs.getString(3);
            if (statusCode != 200 && statusCode != 201) {
                logger.info("Delete merchant fail: " + statusDesc);
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    public static boolean updateReportStatus(int userId, int reportId, String state) throws Exception {
        Exception exception = null;
        // Connection conn = null;
        CallableStatement cs = null;
        OracleConnection connection = null;
        try (Connection conn = getPds114().getConnection()) {
            // conn = getConnection114();
            if (conn.isWrapperFor(OracleConnection.class)) {
                connection = conn.unwrap(OracleConnection.class);
                cs = connection.prepareCall(UPDATE_REPORT_STATUS);
                cs.setInt(1, userId);
                cs.setInt(2, reportId);
                cs.setString(3, state);
                cs.registerOutParameter(4, OracleTypes.NUMBER);
                cs.registerOutParameter(5, OracleTypes.VARCHAR);
                cs.execute();
                int statusCode = cs.getInt(4);
                String statusDesc = cs.getString(5);
                if (statusCode != 200 && statusCode != 201) {
                    logger.info("Update report status fail: " + statusDesc);
                    return false;
                }
                return true;
            } else {
                logger.info("can not cast connection");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return false;
    }

    // private static ReportDetailDTO blindReportDetail(ResultSet rs) throws SQLException {
    //     ReportDetailDTO reportDetailDTO = new ReportDetailDTO();
    //     reportDetailDTO.setId(rs.getLong("N_ID"));
    //     reportDetailDTO.setFromDate(rs.getTimestamp("FROM_DATE"));
    //     reportDetailDTO.setToDate(rs.getTimestamp("TO_DATE"));
    //     reportDetailDTO.setStatementVCB(rs.getDouble("STATEMENT_VCB"));
    //     reportDetailDTO.setStatementVTB(rs.getDouble("STATEMENT_VTB"));
    //     reportDetailDTO.setStatementTCB(rs.getDouble("STATEMENT_TCB"));
    //     reportDetailDTO.setState(rs.getString("STATE"));
    //     reportDetailDTO.setCreateDate(rs.getTimestamp("CREATE_DATE"));
    //     reportDetailDTO.setUpdateDate(rs.getTimestamp("UPDATE_DATE"));
    //     reportDetailDTO.setUserUpdate(rs.getInt("USER_UPDATE"));

    //     return reportDetailDTO;
    // }

    private static ReportDetailDTO blindStatementReport(ResultSet rs) throws SQLException {
        ReportDetailDTO reportDetailDTO = new ReportDetailDTO();
        reportDetailDTO.setId(rs.getLong("N_ID"));
        reportDetailDTO.setReportId(rs.getInt("REPORT_ID"));
        reportDetailDTO.setReportDate(rs.getTimestamp("REPORT_DATE"));
        reportDetailDTO.setReportName(rs.getString("REPORT_NAME"));
        reportDetailDTO.setReportPs(rs.getDouble("PS"));
        reportDetailDTO.setReportVcb(rs.getDouble("REPORT_STATEMENT_VCB"));
        reportDetailDTO.setReportVtb(rs.getDouble("REPORT_STATEMENT_VTB"));
        reportDetailDTO.setReportTcb(rs.getDouble("REPORT_STATEMENT_TCB"));
        reportDetailDTO.setReportTcbInternal(rs.getDouble("REPORT_IN_TCB"));
        reportDetailDTO.setReportTcbExternal(rs.getDouble("REPORT_OUT_TCB"));
        reportDetailDTO.setReportTotal(rs.getDouble("REPORT_TOTAL"));
        reportDetailDTO.setReportDifference(rs.getDouble("REPORT_DIFFERENCE"));
        reportDetailDTO.setReportExportType(rs.getInt("REPORT_EXPORT_TYPE"));
        reportDetailDTO.setReportFee(rs.getDouble("REPORT_FEE"));
        reportDetailDTO.setIbVCB(rs.getDouble("IB_VCB"));
        reportDetailDTO.setIbVTB(rs.getDouble("IB_VTB"));
        reportDetailDTO.setIbTCB(rs.getDouble("IB_TCB"));
        return reportDetailDTO;
    }

    private static ExplanationDetailDTO blindStatementExplanation(ResultSet rs) throws SQLException {
        ExplanationDetailDTO explanationDetailDTO = new ExplanationDetailDTO();
        explanationDetailDTO.setId(rs.getLong("N_ID"));
        explanationDetailDTO.setIdExplanation(rs.getInt("EXPLANATION_ID"));
        explanationDetailDTO.setBank(rs.getString("EXPLANATION_BANK"));
        explanationDetailDTO.setDesc(rs.getString("EXPLANATION_DESCRIPTION"));
        explanationDetailDTO.setCount(rs.getInt("EXPLANATION_COUNT"));
        explanationDetailDTO.setAmount(rs.getDouble("EXPLANATION_AMOUNT"));
        explanationDetailDTO.setCreate(rs.getTimestamp("EXPLANATION_CREATE_DATE"));
        explanationDetailDTO.setUpdate(rs.getTimestamp("EXPLANATION_UPDATE_DATE"));
        explanationDetailDTO.setUserUpdate(rs.getInt("EXPLANATION_USER_UPDATE"));

        return explanationDetailDTO;
    }
}
