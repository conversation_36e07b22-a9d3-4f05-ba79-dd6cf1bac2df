package vn.onepay.portal.resources.accountantManagement.report.dto;

import java.sql.Timestamp;

public class ExplanationDetailDTO {
    private Long id;
    private Integer idExplanation;
    private String bank;
    private String desc;
    private Integer count;
    private Double amount;
    private Timestamp create;
    private Timestamp update;
    private Integer userUpdate;

    public ExplanationDetailDTO() {

    }
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public Integer getIdExplanation() {
        return idExplanation;
    }
    public void setIdExplanation(Integer idExplanation) {
        this.idExplanation = idExplanation;
    }
    public String getBank() {
        return bank;
    }
    public void setBank(String bank) {
        this.bank = bank;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public Integer getCount() {
        return count;
    }
    public void setCount(Integer count) {
        this.count = count;
    }
    public Double getAmount() {
        return amount;
    }
    public void setAmount(Double amount) {
        this.amount = amount;
    }
    public Timestamp getCreate() {
        return create;
    }
    public void setCreate(Timestamp create) {
        this.create = create;
    }
    public Timestamp getUpdate() {
        return update;
    }
    public void setUpdate(Timestamp update) {
        this.update = update;
    }
    public Integer getUserUpdate() {
        return userUpdate;
    }
    public void setUserUpdate(Integer userUpdate) {
        this.userUpdate = userUpdate;
    }
}
