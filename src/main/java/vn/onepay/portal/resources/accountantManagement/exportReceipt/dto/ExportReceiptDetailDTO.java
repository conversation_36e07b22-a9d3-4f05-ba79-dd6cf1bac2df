package vn.onepay.portal.resources.accountantManagement.exportReceipt.dto;

public class ExportReceiptDetailDTO {
    private String paymentVoucher;
    private String dateFrom;
    private String dateTo;
    private Double feeReceipt;
    private Double amountStm;
    private Double amountAdv;
    private Double feeVat;
    private Double feeTotalReceipt;
    private String status;
    private Double cutOff;
    private Double invoice;
    private Integer id;
    private Double advance;
    private Integer parentId;
    private String description;
    private Double eCom;
    private Double instFee;
    private Double discrepancies;
    private String state;

    public String getPaymentVoucher() {
        return paymentVoucher;
    }

    public void setPaymentVoucher(String paymentVoucher) {
        this.paymentVoucher = paymentVoucher;
    }

    public String getDateFrom() {
        return dateFrom;
    }

    public void setDateFrom(String dateFrom) {
        this.dateFrom = dateFrom;
    }

    public String getDateTo() {
        return dateTo;
    }

    public void setDateTo(String dateTo) {
        this.dateTo = dateTo;
    }

    public Double getFeeReceipt() {
        return feeReceipt;
    }

    public void setFeeReceipt(Double feeReceipt) {
        this.feeReceipt = feeReceipt;
    }

    public Double getAmountStm() {
        return amountStm;
    }

    public void setAmountStm(Double amountStm) {
        this.amountStm = amountStm;
    }

    public Double getAmountAdv() {
        return amountAdv;
    }

    public void setAmountAdv(Double amountAdv) {
        this.amountAdv = amountAdv;
    }

    public Double getFeeVat() {
        return feeVat;
    }

    public void setFeeVat(Double feeVat) {
        this.feeVat = feeVat;
    }

    public Double getFeeTotalReceipt() {
        return feeTotalReceipt;
    }

    public void setFeeTotalReceipt(Double feeTotalReceipt) {
        this.feeTotalReceipt = feeTotalReceipt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getCutOff() {
        return cutOff;
    }

    public void setCutOff(Double cutOff) {
        this.cutOff = cutOff;
    }

    public Double getInvoice() {
        return invoice;
    }

    public void setInvoice(Double invoice) {
        this.invoice = invoice;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getAdvance() {
        return advance;
    }

    public void setAdvance(Double advance) {
        this.advance = advance;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double geteCom() {
        return eCom;
    }

    public void seteCom(Double eCom) {
        this.eCom = eCom;
    }

    public Double getInstFee() {
        return instFee;
    }

    public void setInstFee(Double instFee) {
        this.instFee = instFee;
    }

    public Double getDiscrepancies() {
        return discrepancies;
    }

    public void setDiscrepancies(Double discrepancies) {
        this.discrepancies = discrepancies;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
