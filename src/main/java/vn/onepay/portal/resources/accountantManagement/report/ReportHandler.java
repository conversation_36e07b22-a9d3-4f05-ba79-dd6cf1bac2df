package vn.onepay.portal.resources.accountantManagement.report;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.ReadExcel;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class ReportHandler implements IConstants {
    private ReportHandler() {}

    public static void listReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpServerRequest httpServerRequest = ctx.request();
                String fromDate = httpServerRequest.getParam(FROM_DATE);
                String toDate = httpServerRequest.getParam(TO_DATE);
                if (fromDate == null || fromDate.isEmpty() || !Util.isValidDate(fromDate, YYYYMMDD) || toDate == null
                        || toDate.isEmpty() || !Util.isValidDate(toDate, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
                int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20
                        : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
                BaseList<Map<String, Object>> listResult = ReportDAO.listReport(fromDate, toDate, page, pageSize);
                sendResponse(ctx, 200, listResult);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void listReportDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            final HttpServerRequest httpServerRequest = ctx.request();
            String id = httpServerRequest.getParam(REPORT_ID) == null ? "0"
                    : (String) httpServerRequest.getParam(REPORT_ID);
            String method = httpServerRequest.getParam("method");
            int page = httpServerRequest.getParam(ParamsPool.PAGE) == null ? 0
                    : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE));
            int pageSize = httpServerRequest.getParam(ParamsPool.PAGE_SIZE) == null ? 20
                    : Integer.parseInt(httpServerRequest.getParam(ParamsPool.PAGE_SIZE));
            if (id == null || id.isEmpty())
                throw IErrors.VALIDATION_ERROR;
            if (method == null || method.isEmpty())
                throw IErrors.VALIDATION_ERROR;

            try {
                if ("SELECT_REPORT".equals(method)) {
                    sendResponse(ctx, 200, ReportDAO.listReportDetail(id, method, page, pageSize));
                } else {
                    sendResponse(ctx, 200, ReportDAO.listExplanationDetail(id, method, page, pageSize));
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateReportStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jRequest = ctx.getBodyAsJson();
                String state = jRequest.getString("state");
                Integer userId = jRequest.getInteger(USER_ID);
                Integer reportId = jRequest.getInteger(REPORT_ID);
                if (state == null || state.isEmpty() || (!state.equals("create") && !state.equals("approve")))
                    throw IErrors.VALIDATION_ERROR;
                if (reportId == null)
                    throw IErrors.VALIDATION_ERROR;
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;
                boolean isUpdated = ReportDAO.updateReportStatus(userId, reportId, state);
                int statusRes = isUpdated ? 200 : 500;
                sendResponse(ctx, statusRes, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void createReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject jRequest = ctx.getBodyAsJson();
                String fromDate = jRequest.getString("fromDate");
                String toDate = jRequest.getString("toDate");
                if (fromDate == null || fromDate.isEmpty() || !Util.isValidDate(fromDate, YYYYMMDD) || toDate == null
                        || toDate.isEmpty() || !Util.isValidDate(toDate, YYYYMMDD))
                    throw IErrors.VALIDATION_ERROR;
                Integer userId = jRequest.getInteger(USER_ID);
                if (userId == null)
                    throw IErrors.VALIDATION_ERROR;

                BaseList<Map<String, Object>> listStm = ReportDAO.createAccountantReport(fromDate, toDate, userId);
                sendResponse(ctx, 200, listStm);
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                Map<String, Object> mIn = new HashMap<>();

                mIn.put("id", bodyJson.getString(REPORT_ID) == null ? "" : bodyJson.getString(REPORT_ID));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = 0;
                int totalReport = ReportDAO.getTotalReport(mIn, "TOTAL_REPORT");
                int totalExplanation = ReportDAO.getTotalReport(mIn, "TOTAL_EXPLANATION");
                totalRows = totalReport + totalExplanation;
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                String fromDate = bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE);
                String toDate = bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE);
                Calendar clFrom = Calendar.getInstance();
                Calendar clTo = Calendar.getInstance();
                SimpleDateFormat dfFrom = new SimpleDateFormat("dd-MM-yyyy");
                Date dFrom = dfFrom.parse(fromDate);
                Date dTo = dfFrom.parse(toDate);
                clFrom.setTime(dFrom);
                clTo.setTime(dTo);
                String week = "";
                if (clFrom.get(Calendar.WEEK_OF_YEAR) == clTo.get(Calendar.WEEK_OF_YEAR)) {
                    week = "Tuan " + clFrom.get(Calendar.WEEK_OF_YEAR);
                } else {
                    week = "Tuan " + clFrom.get(Calendar.WEEK_OF_YEAR) + " - " + clTo.get(Calendar.WEEK_OF_YEAR);
                }
                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = clFrom.get(Calendar.YEAR) + " OP_ACC- Bao cao TU Merchant " + week;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("statement_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(
                            new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(
                            new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD STATEMENT REPORT ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void uploadReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int userId = ctx.request().getParam(USER_ID) == null ? 0
                        : Integer.parseInt(ctx.request().getParam(USER_ID));
                int reportId = ctx.request().getParam(REPORT_ID) == null ? 0
                        : Integer.parseInt(ctx.request().getParam(REPORT_ID));
                if (userId == 0)
                    throw IErrors.VALIDATION_ERROR;
                if (reportId == 0)
                    throw IErrors.VALIDATION_ERROR;
                Set<FileUpload> fileUploadSet = ctx.fileUploads();

                FileUpload fileUpload = fileUploadSet.stream().findFirst().get();
                if (fileUpload == null)
                    throw IErrors.VALIDATION_ERROR;

                Map<String, Object> ibImport = new HashMap<>();
                Map<String, Object> mTempReport = new HashMap<>();
                List<String> columns = new ArrayList<>();
                columns.add("day");
                columns.add("date");
                columns.add("ds");
                columns.add("space1");
                columns.add("ps");
                columns.add("space2");
                columns.add("vcbAmount");
                columns.add("vcbQuantity");
                columns.add("vcbFee");
                columns.add("vcb");
                columns.add("space3");
                columns.add("vtb");
                columns.add("space4");
                columns.add("inTcb");
                columns.add("outTcb");
                columns.add("tcb");
                columns.add("space5");
                columns.add("total");
                columns.add("space6");
                columns.add("difference");
                mTempReport.put("list_column_name", columns);
                mTempReport.put("row_start", 4);
                mTempReport.put("sheet_number", 0);

                List<Map<String, Object>> explanationImport = null;
                Map<String, Object> mTempExplanation = new HashMap<>();
                mTempExplanation.put("row_start", 6);
                mTempExplanation.put("sheet_number", 1);

                try {
                    logger.info("file Type: " + fileUpload.contentType());
                    if ("application/vnd.ms-excel".equals(fileUpload.contentType())) {
                        ibImport = ReadExcel.readExcelXlsReport(fileUpload.uploadedFileName(), mTempReport);
                        explanationImport = ReadExcel.readExcelXlsExplanation(fileUpload.uploadedFileName(),
                                mTempExplanation);
                    } else {
                        throw IErrors.INVALID_FILE_MUST_EXCEL;
                    }
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "ERROR TO MERCHANT ACCOUNTANT IMPORT: ", e);
                    throw IErrors.INTERNAL_SERVER_ERROR;
                }
                logger.info("Ib Import" + ibImport.get("vcb"));
                logger.info("Ib Import" + Double.parseDouble(String.valueOf(ibImport.get("vcb"))));
                logger.info("Explanation Import" + explanationImport);

                if (ibImport == null || ibImport.size() == 0 || explanationImport == null
                        || explanationImport.isEmpty()) {
                    logger.info("No import file data found");
                    throw IErrors.VALIDATION_ERROR;
                }
                ReportDAO.updateAccountantReport(reportId, userId,
                        Double.parseDouble(String.valueOf(ibImport.get("vcb"))),
                        Double.parseDouble(String.valueOf(ibImport.get("vtb"))),
                        Double.parseDouble(String.valueOf(ibImport.get("tcb"))));
                updateExplanationData(explanationImport, reportId, userId);
                sendResponse(ctx, 200, new JsonObject());
            } catch (Exception ex) {
                logger.log(Level.SEVERE, ERROR, ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static void updateExplanationData(List<Map<String, Object>> listImport, int reportId, int userId)
            throws Exception {
        for (int i = 0; i < listImport.size(); i++) {
            Map<String, Object> m = new HashMap<>();
            if (i == 0) {
                ReportDAO.deleteExplanationByReportId(reportId);
            }
            logger.info("" + listImport.get(i).get("1"));
            boolean insert = false;
            if ((String) listImport.get(i).get("1") != null && !((String) listImport.get(i).get("1")).equals("")) {
                insert = false;
                m.put("bank", listImport.get(i).get("1"));
            } else {
                for (int j = 1; j <= i; j++) {
                    if (!((String) listImport.get(i - j).get("1")).equals("")) {
                        m.put("bank", listImport.get(i - j).get("1"));
                        insert = true;
                        break;
                    }
                }
            }
            int count = 0;
            double amount = 0;
            if (listImport.get(i).get("4") != null && !String.valueOf(listImport.get(i).get("4")).equals("")) {
                count = Integer.parseInt(String.valueOf(listImport.get(i).get("4")));
            }
            if (String.valueOf(listImport.get(i).get("5")) != null
                    && !"".equals(String.valueOf(listImport.get(i).get("5")))) {
                amount = Double.parseDouble(String.valueOf(listImport.get(i).get("5")));
            }
            if (insert) {
                ReportDAO.updateAccountantExplanation(reportId, userId, String.valueOf(m.get("bank")),
                        String.valueOf(listImport.get(i).get("3")), count, amount);
            }
        }
    }

    private static final Logger logger = Logger.getLogger(ReportHandler.class.getName());
}
