package vn.onepay.portal.resources.accountantManagement.statement.dto;

import java.util.Map;

public class DetailStatementDto {
    private String name;
    private Double advance_amount;
    private Map<String, Object> VCB;
    private Double VTB;
    private Map<String, Object> TCB;
    private Double statement_amount;
    private Double discrepancy;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getAdvance_amount() {
        return advance_amount;
    }

    public void setAdvance_amount(Double advance_amount) {
        this.advance_amount = advance_amount;
    }

    public Map<String, Object> getVCB() {
        return VCB;
    }

    public void setVCB(Map<String, Object> vCB) {
        VCB = vCB;
    }

    public Double getVTB() {
        return VTB;
    }

    public void setVTB(Double vTB) {
        VTB = vTB;
    }

    public Map<String, Object> getTCB() {
        return TCB;
    }

    public void setTCB(Map<String, Object> tCB) {
        TCB = tCB;
    }

    public Double getStatement_amount() {
        return statement_amount;
    }

    public void setStatement_amount(Double statement_amount) {
        this.statement_amount = statement_amount;
    }

    public Double getDiscrepancy() {
        return discrepancy;
    }

    public void setDiscrepancy(Double discrepancy) {
        this.discrepancy = discrepancy;
    }

}
