package vn.onepay.portal.resources.accountantManagement.merchantManagement;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.accountantManagement.merchantManagement.dto.MerchantDTO;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import static vn.onepay.portal.Util.resultSetToList;

public class MerchantManagementDAO extends Db {
    private static final String INSERT_MERCHANT = "{call ONEFIN.pkg_accountant.insert_merchant(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String UPDATE_MERCHANT = "{call ONEFIN.pkg_accountant.update_merchant(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String DELETE_MERCHANT = "{call ONEFIN.pkg_accountant.delete_merchant(?,?,?)}";
    private static final String IMPORT_MERCHANT = "{call ONEFIN.pkg_accountant.import_merchant(?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_MERCHANT = "{call ONEFIN.pkg_accountant.list_merchant(?,?,?,?,?,?,?,?)}";

    public static List<Map<String, Object>> listBankData(String sql) throws SQLException {
        SQLException exception = null;
        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            result = resultSetToList(rs);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "List bank data fail", e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, ps, null, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<MerchantDTO> listMerchant(String keywords, String state, int page, int pageSize) throws SQLException {
        SQLException exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        BaseList<MerchantDTO> result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(LIST_MERCHANT);
            cs.setString(1, keywords);
            cs.setString(2, state);
            cs.setInt(3, page);
            cs.setInt(4, pageSize);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.CURSOR);
            cs.execute();

            int statusCode = cs.getInt(5);
            String statusDesc = cs.getString(6);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "List merchant fail: {0}", statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }

            int total = cs.getInt(7);
            List<MerchantDTO> listMerchant = new ArrayList<>();
            rs = (ResultSet) cs.getObject(8);
            while (rs != null && rs.next()) {
                listMerchant.add(blindMerchant(rs));
            }
            result = new BaseList<>();
            result.setTotalItems(total);
            result.setList(listMerchant);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static MerchantDTO insertMerchant(MerchantDTO merchantDTO) throws SQLException {
        SQLException exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        MerchantDTO result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(INSERT_MERCHANT);
            cs.setString(1, merchantDTO.getMerchantId());
            cs.setString(2, merchantDTO.getAccountNumber());
            cs.setString(3, merchantDTO.getAccountName() == null ? "" : merchantDTO.getAccountName());
            cs.setString(4, merchantDTO.getMerchantName() == null ? "" : merchantDTO.getMerchantName());
            cs.setString(5, merchantDTO.getAcqBank() == null ? "" : merchantDTO.getAcqBank());
            cs.setString(6, merchantDTO.getBankCode() == null ? "" : merchantDTO.getBankCode());
            cs.setString(7, merchantDTO.getBankProvince() == null ? "" : merchantDTO.getBankProvince());
            cs.setString(8, merchantDTO.getBankBranch() == null ? "" : merchantDTO.getBankBranch());
            cs.registerOutParameter(9, OracleTypes.NUMBER);
            cs.registerOutParameter(10, OracleTypes.VARCHAR);
            cs.registerOutParameter(11, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(9);
            String statusDesc = cs.getString(10);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "Insert merchant fail: {0}", statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) cs.getObject(11);
            while (rs != null && rs.next()) {
                merchantDTO.setId(rs.getInt("N_ID"));
                merchantDTO.setCreatedDate(rs.getTimestamp(D_CREATE));
                merchantDTO.setUpdatedDate(rs.getTimestamp(D_UPDATE));
                result = merchantDTO;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static MerchantDTO updateMerchant(MerchantDTO merchantDTO) throws SQLException {
        SQLException exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        MerchantDTO result = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(UPDATE_MERCHANT);
            cs.setInt(1, merchantDTO.getId());
            cs.setString(2, merchantDTO.getMerchantId());
            cs.setString(3, merchantDTO.getAccountNumber());
            cs.setString(4, merchantDTO.getAccountName() == null ? "" : merchantDTO.getAccountName());
            cs.setString(5, merchantDTO.getMerchantName() == null ? "" : merchantDTO.getMerchantName());
            cs.setString(6, merchantDTO.getAcqBank() == null ? "" : merchantDTO.getAcqBank());
            cs.setString(7, merchantDTO.getBankCode() == null ? "" : merchantDTO.getBankCode());
            cs.setString(8, merchantDTO.getBankProvince() == null ? "" : merchantDTO.getBankProvince());
            cs.setString(9, merchantDTO.getBankBranch() == null ? "" : merchantDTO.getBankBranch());
            cs.setString(10, merchantDTO.getState() == null ? "" : merchantDTO.getState());
            cs.registerOutParameter(11, OracleTypes.NUMBER);
            cs.registerOutParameter(12, OracleTypes.VARCHAR);
            cs.registerOutParameter(13, OracleTypes.CURSOR);
            cs.execute();
            int statusCode = cs.getInt(11);
            String statusDesc = cs.getString(12);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "Update merchant fail: {0}", statusDesc);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            rs = (ResultSet) cs.getObject(13);
            while (rs != null && rs.next()) {
                merchantDTO.setId(rs.getInt("N_ID"));
                merchantDTO.setCreatedDate(rs.getTimestamp(D_CREATE));
                merchantDTO.setUpdatedDate(rs.getTimestamp(D_UPDATE));
                result = merchantDTO;
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static boolean deleteMerchant(int id) throws SQLException {
        SQLException exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        boolean result = false;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(DELETE_MERCHANT);
            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            int statusCode = cs.getInt(2);
            String statusDesc = cs.getString(3);
            if (statusCode != 200 && statusCode != 201) {
                logger.log(Level.INFO, "Delete merchant fail: {0}", statusDesc);
                return result;
            }
            result = true;
        } catch (Exception e) {
            logger.log(Level.SEVERE, ERROR, e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(null, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void importMerchant(List<Map<String, Object>> listMerchant) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(IMPORT_MERCHANT);
            for (Map<String, Object> t : listMerchant) {
                cs.setString(1, t.getOrDefault("merchantId", "").toString().trim());
                cs.setString(2, t.getOrDefault("accountNumber", "").toString().trim());
                cs.setString(3, t.getOrDefault("accountName", "").toString().trim());
                cs.setString(4, t.getOrDefault("merchantName", "").toString().trim());
                cs.setString(5, t.getOrDefault("acqBank", "").toString().trim());
                cs.setString(6, t.getOrDefault("bankCode", "").toString().trim());
                cs.setString(7, t.getOrDefault("bankProvince", "").toString().trim());
                cs.setString(8, t.getOrDefault("bankBranch", "").toString().trim());
                cs.setString(9, t.getOrDefault("state", "").toString().trim());
                cs.addBatch();
            }
            cs.executeBatch();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    private static MerchantDTO blindMerchant(ResultSet rs) throws SQLException {
        MerchantDTO merchantDTO = new MerchantDTO();
        merchantDTO.setId(rs.getInt("N_ID"));
        merchantDTO.setMerchantId(rs.getString("S_MERCHANT_ID"));
        merchantDTO.setMerchantName(rs.getString("S_MERCHANT_NAME"));
        merchantDTO.setAccountNumber(rs.getString("S_ACCOUNT_NUMBER"));
        merchantDTO.setAccountName(rs.getString("S_ACCOUNT_NAME"));
        merchantDTO.setCreatedDate(rs.getTimestamp(D_CREATE));
        merchantDTO.setUpdatedDate(rs.getTimestamp(D_UPDATE));
        merchantDTO.setAcqBank(rs.getString("S_ACQ_BANK"));
        merchantDTO.setBankCode(rs.getString("S_BANK_CODE"));
        merchantDTO.setBankProvince(rs.getString("S_BANK_PROVINCE"));
        merchantDTO.setBankBranch(rs.getString("S_BANK_BRANCH"));
        merchantDTO.setState(rs.getString("S_STATE"));
        return merchantDTO;
    }

    public static List<Map<String, Object>> getListMerchantAccountant() throws SQLException {
        List<Map<String, Object>> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement cs = null;
        ResultSet rs = null;
        SQLException exception = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("select * from ONEFIN.tb_merchant_accountant order by N_ID");
            rs = cs.executeQuery();
            if (rs != null) {
                list = Util.resultSetToList(rs);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, cs, null, conn);
        }
        if (exception != null)
            throw exception;
        return list;
    }

    public static Integer countMerchantAccountant() throws SQLException {
        Integer total = 0;
        Connection conn = null;
        PreparedStatement cs = null;
        ResultSet rs = null;
        SQLException exception = null;
        try {
            conn = getConnection114();
            cs = conn.prepareCall("select count(*) as total from ONEFIN.tb_merchant_accountant");
            rs = cs.executeQuery();
            if (rs != null && rs.next()) {
                total = rs.getInt("total");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = new SQLException(e.getMessage());
        } finally {
            closeConnectionDB(rs, cs, null, conn);
        }
        if (exception != null)
            throw exception;
        return total;
    }
}
