package vn.onepay.portal.resources.audit_trail.request;

import io.vertx.core.json.JsonArray;

import java.io.Serializable;

public class AuditTrailRequest implements Serializable {
    private String code = "";
    private String module = "";
    private String actor = "";
    private String content = "";
    private String action = "";
    private String note = "";
    private String userId = "";
    private String realIp = "";
    private String requestId = "";
    private String source = "";
    private String type = "";
    private JsonArray newValue = new JsonArray();
    private JsonArray oldValue = new JsonArray();
    private String newValueString = "";
    private String oldValueString = "";

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealIp() {
        return realIp;
    }

    public void setRealIp(String realIp) {
        this.realIp = realIp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public JsonArray getNewValue() {
        return newValue;
    }

    public void setNewValue(JsonArray newValue) {
        this.newValue = newValue;
    }

    public JsonArray getOldValue() {
        return oldValue;
    }

    public void setOldValue(JsonArray oldValue) {
        this.oldValue = oldValue;
    }

    public String getNewValueString() {
        return newValueString;
    }

    public void setNewValueString(String newValueString) {
        this.newValueString = newValueString;
    }

    public String getOldValueString() {
        return oldValueString;
    }

    public void setOldValueString(String oldValueString) {
        this.oldValueString = oldValueString;
    }
}
