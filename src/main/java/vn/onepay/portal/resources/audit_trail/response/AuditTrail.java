package vn.onepay.portal.resources.audit_trail.response;

import io.vertx.core.json.JsonObject;

import java.io.Serializable;

public class AuditTrail implements Serializable {
    private Integer id;
    private String code;
    private String module;
    private String source;
    private String actor;
    private String content;
    private String action;
    private JsonObject oldValue;
    private JsonObject newValue;
    private String note;
    private String userId;
    private String realIp;
    private String requestId;
    private String type;
    private String timestamp;
    private String state;


    public AuditTrail(Integer id, String code, String module, String source, String actor, String content, String action
            , JsonObject oldValue, JsonObject newValue, String note, String userId, String realIp,
                      String requestId, String type, String timestamp, String state) {
        this.id = id;
        this.code = code;
        this.module = module;
        this.source = source;
        this.actor = actor;
        this.content = content;
        this.action = action;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.note = note;
        this.userId = userId;
        this.realIp = realIp;
        this.requestId = requestId;
        this.type = type;
        this.timestamp = timestamp;
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public JsonObject getOldValue() {
        return oldValue;
    }

    public void setOldValue(JsonObject oldValue) {
        this.oldValue = oldValue;
    }

    public JsonObject getNewValue() {
        return newValue;
    }

    public void setNewValue(JsonObject newValue) {
        this.newValue = newValue;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealIp() {
        return realIp;
    }

    public void setRealIp(String realIp) {
        this.realIp = realIp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
