package vn.onepay.portal.resources.audit_trail.handler;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.audit_trail.dao.AuditTrailDAO;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;

import java.util.HashMap;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class AuditTrailHandler implements IConstants {
    private static final Logger _LOGGER = Logger.getLogger(AuditTrailHandler.class.getName());

    public static void insertAuditTrail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                AuditTrailRequest request = new AuditTrailRequest();
                // mã code tạm thời để UUID
                String code = bodyJson.getString(CODE);
                if(!StringUtils.isBlank(code)){
                    request.setCode(code);
                }else {
                    request.setCode(UUID.randomUUID().toString());
                }
                // module
                String module = bodyJson.getString(MODULE);
                if (!StringUtils.isBlank(module)) {
                    request.setModule(module);
                }
                // actor
                String actor = bodyJson.getString(ACTOR);
                if (!StringUtils.isBlank(actor)) {
                    request.setActor(actor);
                }
                // content
                String content = bodyJson.getString(CONTENT);
                if (!StringUtils.isBlank(content)) {
                    request.setContent(content);
                }
                // action
                String action = bodyJson.getString(ACTION);
                if (!StringUtils.isBlank(action)) {
                    request.setAction(action);
                }
                // note
                String note = bodyJson.getString(NOTE);
                if (!StringUtils.isBlank(note)) {
                    request.setNote(note);
                }
                // userId
                String userId = ctx.request().getHeader(X_USER_ID);
                if (!StringUtils.isBlank(userId)) {
                    request.setUserId(userId);
                }

                // realIp
                String realIp = ctx.request().getHeader(X_REAL_IP);
                if (!StringUtils.isBlank(realIp)) {
                    request.setRealIp(realIp);
                }

                // requestId
                String requestId = ctx.request().getHeader(X_REQUEST_ID);
                if (!StringUtils.isBlank(requestId)) {
                    request.setRequestId(requestId);
                }

                // source
                String source = bodyJson.getString(SOURCE);
                if (!StringUtils.isBlank(source)) {
                    request.setSource(source);
                }

                // type
                String type = bodyJson.getString(TYPE);
                if (!StringUtils.isBlank(type)) {
                    request.setType(type);
                }

                try{
                    // newValue
                    JsonArray newValue = bodyJson.getJsonArray(NEW_VALUE) != null ? bodyJson.getJsonArray(NEW_VALUE) : new JsonArray();
                    request.setNewValue(newValue);
                    // oldValue
                    JsonArray oldValue = bodyJson.getJsonArray(OLD_VALUE) != null ? bodyJson.getJsonArray(OLD_VALUE) : new JsonArray();
                    request.setOldValue(oldValue);
                } catch (Exception e) {
                    _LOGGER.log(Level.SEVERE, "insert audit trail not json array: ", e);
                    // newValue
                    JsonObject newValue = bodyJson.getValue(NEW_VALUE) != null ? bodyJson.getJsonObject(NEW_VALUE) : new JsonObject();
                    request.setNewValueString(newValue+"");
                    // oldValue
                    JsonObject oldValue = bodyJson.getValue(OLD_VALUE) != null ? bodyJson.getJsonObject(OLD_VALUE) : new JsonObject();
                    request.setOldValueString(oldValue+"");
                    _LOGGER.log(Level.SEVERE, "value: "+request.getNewValue()+"|"+request.getNewValueString());
                }
                

                AuditTrailDAO.insertAuditTrail(request);
                sendResponse(ctx, OK, new HashMap<>());
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "insert audit trail error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
