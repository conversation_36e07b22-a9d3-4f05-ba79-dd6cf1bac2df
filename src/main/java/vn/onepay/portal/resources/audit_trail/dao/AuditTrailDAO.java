package vn.onepay.portal.resources.audit_trail.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;
import vn.onepay.portal.resources.audit_trail.response.AuditTrail;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class AuditTrailDAO extends Db implements IConstants {


    public static void main(String[] args) {
        try {

            AuditTrailDAO.insertAuditTrail(new AuditTrailRequest());
//            JsonObject querySearch = new JsonObject();
//            querySearch.put(PAGE, "0");
//            querySearch.put(PAGESIZE, "10");
//            querySearch.put(KEYWORD, "ngocanh");
//            querySearch.put(ACTION, BLANK);
//            querySearch.put(TYPE, BLANK);
//            querySearch.put(CODE, BLANK);
//            querySearch.put(USERNAME, BLANK);
//            querySearch.put(SOURCE, BLANK);
//            querySearch.put(MODULE, BLANK);
//            querySearch.put(FROMDATE, BLANK);
//            querySearch.put(TODATE, BLANK);
//            var result = AuditTrailDAO.searchAuditTrail(querySearch);
//            System.out.println(result);
        } catch (SQLException e) {
            System.out.println(e.getMessage());
        }
    }

    /**
     * @param request input
     * @throws SQLException error
     */
    public static Integer insertAuditTrail(AuditTrailRequest request) throws SQLException {
        Integer logId = 0;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nError = 500;
        String sError = "";
        try {
            conn = getConnectionOnefin118();
            cs = conn.prepareCall("{ call ONEFIN.PKG_AUDIT_TRAIL_INSERT_DATA(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, request.getCode());
            cs.setString(5, request.getModule());
            cs.setString(6, request.getActor());
            cs.setString(7, request.getContent());
            cs.setString(8, request.getAction());
            cs.setString(9, request.getNote());
            cs.setString(10, request.getUserId());
            cs.setString(11, request.getRealIp());
            cs.setString(12, request.getRequestId());
            cs.setString(13, request.getSource());
            cs.setString(14, request.getType());
            Clob newValue = conn.createClob();
            newValue.setString(1, (request.getNewValue()+"").equals("[]") ? request.getNewValueString() : request.getNewValue().toString());
            cs.setClob(15, newValue);
            Clob oldValue = conn.createClob();
            oldValue.setString(1, (request.getOldValue()+"").equals("[]") ? request.getOldValueString() : request.getOldValue().toString());
            cs.setClob(16, oldValue);
            cs.execute();
            sError = cs.getString(2);
            nError = cs.getInt(1);
            logId = cs.getInt(3);
            if (nError != 200) {
                logger.severe("Insert to tb_audit_trail error : " + sError);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }else {
                logger.info("Insert audit trail successfully !");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        return logId;
    }

    public static Map<String, Object> searchAuditTrail(JsonObject mIn) throws SQLException{
        Map<String, Object> result = new HashMap<>();
        List<AuditTrail> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total;
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_audit_trail_search(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, mIn.getString(KEYWORD));
            cs.setString(6, mIn.getString(ACTION));
            cs.setString(7, mIn.getString(TYPE));
            cs.setString(8, mIn.getString(CODE));
            cs.setString(9, mIn.getString(USERNAME));
            cs.setString(10, mIn.getString(SOURCE));
            cs.setString(11, mIn.getString(MODULE));
            cs.setString(12, mIn.getString(FROMDATE));
            cs.setString(13, mIn.getString(TODATE));
            cs.setInt(14, Integer.parseInt(mIn.getString(PAGE)));
            cs.setInt(15, Integer.parseInt(mIn.getString(PAGESIZE)));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "pkg_audit_trail_search error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(bindingAuditTrail(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }


    /**
     * @param rs resultSet
     * @return convert data
     * @throws SQLException error
     */
    private static AuditTrail bindingAuditTrail(ResultSet rs) throws SQLException {
        JsonObject objectPrevious = StringUtils.isEmpty(Util.getColumnString(rs, "s_old_value")) ? null :  new JsonObject(Util.getColumnString(rs, "s_old_value"));
        JsonObject objectNext = StringUtils.isEmpty(Util.getColumnString(rs, "s_new_value")) ?  null :  new JsonObject(Util.getColumnString(rs, "s_new_value"));
        return new AuditTrail(
                Util.getColumnInteger(rs, "n_id"),
                Util.getColumnString(rs, "s_code"),
                Util.getColumnString(rs, "s_module"),
                Util.getColumnString(rs, "s_source"),
                Util.getColumnString(rs, "s_actor"),
                Util.getColumnString(rs, "s_content"),
                Util.getColumnString(rs, "s_action"),
                objectPrevious,
                objectNext,
                Util.getColumnString(rs, "s_note"),
                Util.getColumnString(rs, "s_user_id"),
                Util.getColumnString(rs, "s_real_ip"),
                Util.getColumnString(rs, "s_request_id"),
                Util.getColumnString(rs, "s_type"),
                Util.getColumnString(rs, "d_date_create"),
                Util.getColumnString(rs, "s_state")
        );
    }
}
