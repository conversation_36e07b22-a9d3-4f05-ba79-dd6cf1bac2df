package vn.onepay.portal.resources.mp_permission.user;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.mp_permission.util.MpPermUtil;

public class MpPermUserHandler implements IConstants {
	private MpPermUserHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermUserHandler.class.getName());

    public static void getUsersByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                Integer pageSize = Integer.parseInt(req.getParam("pageSize"));
                Integer page = Integer.parseInt(req.getParam("page"));

                JsonObject json = MpPermUserService.getUsersByPartner(partnerId, pageSize, page);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET USERS ERROR: ", e);
                resp.put(MESSAGE, "get_users_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addLegacyUserList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray userIds = bodyJson.getJsonArray("userIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserService.addLegacyUserList(partnerId, userIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ADD BATCH USERS ERROR: ", e);
                resp.put(MESSAGE, "add_users_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void addLegacyUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserService.addLegacyUser(partnerId, userId, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ADD BATCH USERS ERROR: ", e);
                resp.put(MESSAGE, "add_users_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject bodyJson = ctx.getBodyAsJson();
                String fullName = bodyJson.getString("fullName");
                String jobTitle = bodyJson.getString("jobTitle");
                String email = bodyJson.getString("email");
                String phone = bodyJson.getString("phone");
                String password = bodyJson.getString("password");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserService.createUser(partnerId, operator, email, phone, fullName, jobTitle, password);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CREATE USER ERROR: ", e);
                resp.put(MESSAGE, "create_user_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteUserById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserService.deleteUser(partnerId, userId, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "DELETE USER ERROR: ", e);
                resp.put(MESSAGE, "delete_user_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getUsersExcludePartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                Integer pageSize = Integer.parseInt(req.getParam("pageSize"));
                Integer page = Integer.parseInt(req.getParam("page"));
                String keywords = req.getParam("keywords");

                JsonObject json = MpPermUserService.getUsersExcludePartner(partnerId, pageSize, page, keywords);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET USERS ERROR: ", e);
                resp.put(MESSAGE, "get_users_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getLegacyUsersByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject json = MpPermUserService.getLegacyUsersByPartner(partnerId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET USERS ERROR: ", e);
                resp.put(MESSAGE, "get_users_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
