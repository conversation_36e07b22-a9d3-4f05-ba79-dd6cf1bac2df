package vn.onepay.portal.resources.mp_permission.user;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import java.util.Objects;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermUserService {
	private MpPermUserService() {}

    public static JsonObject getUserByEmailPhone(String emailOrPhone) throws Exception {
        StringBuilder reqUrl = new StringBuilder("/user/by-email-phone");

        reqUrl.append("?emailOrPhone=").append(emailOrPhone);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
	
	public static JsonObject getUsersByPartner(Integer partnerId, Integer pageSize, Integer page) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/iportal");
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/users");

		reqUrl.append("?pageSize=").append(pageSize);
        reqUrl.append("&page=").append(page);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject addLegacyUserList(
        Integer partnerId,
        JsonArray userIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/legacy-users");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("userIds", userIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject addLegacyUser(
        Integer partnerId,
        String userId,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/legacy-user/").append(userId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject createUser(
        Integer partnerId,
        String operator,
        String email, 
        String phone,
        String fullName,
        String jobTitle,
        String password
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("email", email);
        body.put("phone", phone);
        body.put("fullName", fullName);
        body.put("jobTitle", jobTitle);
        body.put("password", password);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject deleteUser(
        Integer partnerId,
        String userId,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "DELETE", body);
    }

    public static JsonObject getUsersExcludePartner(Integer partnerId, Integer pageSize, Integer page, String keywords) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/users/exclude-partner");

        reqUrl.append("?partnerId=").append(partnerId);
        reqUrl.append("&pageSize=").append(pageSize);
        reqUrl.append("&page=").append(page);
        
        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("&keywords=").append(encodeStr);
        }

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getLegacyUsersByPartner(Integer partnerId) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/legacy-users");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
}
