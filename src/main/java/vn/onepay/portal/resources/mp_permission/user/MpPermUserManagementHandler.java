package vn.onepay.portal.resources.mp_permission.user;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.mp_permission.util.MpPermUtil;

public class MpPermUserManagementHandler implements IConstants {
	private MpPermUserManagementHandler(){}

    private static final Logger logger = Logger.getLogger(MpPermUserManagementHandler.class.getName());
	
    public static void addUserToPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserManagementService.addUserToPartner(partnerId, userId, operator);
                JsonObject respData = new JsonObject();
                respData.put("userId", userId);
                json.put(DATA, respData);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ADD USERS ERROR: ", e);
                resp.put(MESSAGE, "add_user_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void assignRolesToUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray roleIds = bodyJson.getJsonArray("roleIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserManagementService.assignRolesToUser(partnerId, userId, roleIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ASSIGN ROLES ERROR: ", e);
                resp.put(MESSAGE, "assign_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void assignMerchantsToUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray merchantIds = bodyJson.getJsonArray("merchantIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserManagementService.assignMerchantsToUser(partnerId, userId, merchantIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "ASSIGN MERCHANTS ERROR: ", e);
                resp.put(MESSAGE, "assign_merchants_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void markUserAsOwner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                JsonObject bodyJson = ctx.getBodyAsJson();
                boolean asOwner = bodyJson.getBoolean("markAsOwner");

                String userType = asOwner ? "owner" : "user";

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermUserManagementService.updateUserType(partnerId, userId, userType, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "UPDATE USER ERROR: ", e);
                resp.put(MESSAGE, "update_user_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
