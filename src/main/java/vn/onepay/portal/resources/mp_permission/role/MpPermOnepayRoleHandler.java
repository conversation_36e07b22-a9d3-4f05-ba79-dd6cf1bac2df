package vn.onepay.portal.resources.mp_permission.role;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.mp_permission.util.MpPermUtil;

public class MpPermOnepayRoleHandler implements IConstants {
	private MpPermOnepayRoleHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermOnepayRoleHandler.class.getName());

	public static void getOnepayRoles(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                String keywords = req.getParam("keywords");
                Integer pageSize = req.getParam("pageSize") != null ? Integer.parseInt(req.getParam("pageSize")) : 200;
                Integer page = req.getParam("page") != null ? Integer.parseInt(req.getParam("page")) : 1;

                JsonObject json = MpPermOnepayRoleService.getOnepayRoles(pageSize, page, keywords);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getOnepayRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                String roleId = req.getParam("roleId");
                
                JsonObject json = MpPermOnepayRoleService.getOnepayRoleById(roleId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLE ERROR: ", e);
                resp.put(MESSAGE, "get_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createOnepayRole(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String name = bodyJson.getString("name");
                String description = bodyJson.getString("description");
                String name_vi = bodyJson.getString("name_vi");
                String description_vi = bodyJson.getString("description_vi");
                JsonArray permissionIds = bodyJson.getJsonArray("permissionIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermOnepayRoleService.createOnepayRole(name, description, name_vi, description_vi, permissionIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CREATE ROLE ERROR: ", e);
                resp.put(MESSAGE, "create_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateOnepayRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                String roleId = req.getParam("roleId");

                JsonObject bodyJson = ctx.getBodyAsJson();
                String name = bodyJson.getString("name");
                String description = bodyJson.getString("description");
                String name_vi = bodyJson.getString("name_vi");
                String description_vi = bodyJson.getString("description_vi");
                JsonArray permissionIds = bodyJson.getJsonArray("permissionIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermOnepayRoleService.updateOnepayRoleById(roleId, name, description, name_vi, description_vi, permissionIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CREATE ROLE ERROR: ", e);
                resp.put(MESSAGE, "create_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteOnepayRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                String roleId = req.getParam("roleId");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermOnepayRoleService.deleteOnepayRoleById(roleId, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "DELETE ROLE ERROR: ", e);
                resp.put(MESSAGE, "delete_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkOnepayRoleName(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String roleId = bodyJson.getString("roleId");
                String name = bodyJson.getString("name");

                JsonObject json = MpPermOnepayRoleService.checkOnepayRoleName(roleId, name);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CHECK ROLE NAME ERROR: ", e);
                resp.put(MESSAGE, "check_role_name_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
