package vn.onepay.portal.resources.mp_permission.partner;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import java.util.Objects;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermPartnerService {
	private MpPermPartnerService() {}

    public static JsonObject getPartners(Integer pageSize, Integer page, String keywords, boolean noEcoApp, boolean noUser) throws Exception {        
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partners");
		reqUrl.append("?pageSize=").append(pageSize);
        reqUrl.append("&page=").append(page);
        reqUrl.append("&noEcoApp=").append(noEcoApp);
        reqUrl.append("&noUser=").append(noUser);
        
        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("&keywords=").append(encodeStr);
        }

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
}
