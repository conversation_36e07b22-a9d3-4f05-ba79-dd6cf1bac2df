package vn.onepay.portal.resources.mp_permission.user;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermUserManagementService {
	private MpPermUserManagementService(){}

    public static JsonObject checkUserExisted(String emailOrPhone) throws Exception {
        StringBuilder reqUrl = new StringBuilder("/user/check-existed");

        String emailPhoneEncoded = URLEncoder.encode(emailOrPhone, UTF_8);
        reqUrl.append("?emailOrPhone=").append(emailPhoneEncoded);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

	public static JsonObject addUserToPartner(
        Integer partnerId,
		String userId,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

	public static JsonObject assignRolesToUser(
        Integer partnerId,
		String userId,
        JsonArray roleIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/roles");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
		body.put("roleIds", roleIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

	public static JsonObject assignMerchantsToUser(
        Integer partnerId,
		String userId,
		JsonArray merchantIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/merchants");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
		body.put("merchantIds", merchantIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject updateUserType(
        Integer partnerId,
        String userId,
        String type,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/update-type");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("type", type);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "PUT", body);
    }
}
