package vn.onepay.portal.resources.mp_permission.role;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import java.util.Objects;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermCustomRoleService {
	private MpPermCustomRoleService() {}

    public static JsonObject getCustomRolesByPartner(Integer partnerId, Integer pageSize, Integer page, String keywords) throws Exception {  
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/iportal");
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/custom-roles");

        reqUrl.append("?pageSize=").append(pageSize);
        reqUrl.append("&page=").append(page);

        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("&keywords=").append(encodeStr);
        }        

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getCustomRolesByUser(Integer partnerId, String userId) throws Exception {  
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/custom-roles");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getCustomRoleById(Integer partnerId, String roleId) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/role/").append(roleId);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject createCustomRole(
        Integer partnerId,
        String name,
        String description,
        JsonArray permissionIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/role");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("name", name);
        body.put("description", description);
        body.put("permissionIds", permissionIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject updateCustomRoleById(
        Integer partnerId,
        String roleId,
        String name,
        String description,
        JsonArray permissionIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/role/").append(roleId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("name", name);
        body.put("description", description);
        body.put("permissionIds", permissionIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "PUT", body);
    }

    public static JsonObject deleteCustomRoleById(
        Integer partnerId,
        String roleId,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/role/").append(roleId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "DELETE", body);
    }

    public static JsonObject checkCustomRoleName(
        Integer partnerId,
        String roleId,
        String name
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/custom-role/check-name");

        JsonObject body = new JsonObject();
        body.put("roleId", roleId);
        body.put("name", name);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }
}
