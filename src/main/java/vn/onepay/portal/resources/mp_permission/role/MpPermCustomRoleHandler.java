package vn.onepay.portal.resources.mp_permission.role;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.mp_permission.util.MpPermUtil;

public class MpPermCustomRoleHandler implements IConstants {
	private MpPermCustomRoleHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermCustomRoleHandler.class.getName());

	public static void getCustomRolesByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String keywords = req.getParam("keywords") != null ? req.getParam("keywords") : "";
                Integer pageSize = req.getParam("pageSize") != null ? Integer.parseInt(req.getParam("pageSize")) : 200;
                Integer page = req.getParam("page") != null ? Integer.parseInt(req.getParam("page")) : 1;

                JsonObject json = MpPermCustomRoleService.getCustomRolesByPartner(partnerId, pageSize, page, keywords);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

	public static void getCustomRolesByUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                String loggedUserId = ctx.get(S_USER_ID);
                if (loggedUserId == null) {
                    throw IErrors.FORBIDDEN;
                }

                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");
                
                JsonObject json = MpPermCustomRoleService.getCustomRolesByUser(partnerId, userId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getCustomRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String roleId = req.getParam("roleId");
                
                JsonObject json = MpPermCustomRoleService.getCustomRoleById(partnerId, roleId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLE ERROR: ", e);
                resp.put(MESSAGE, "get_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createCustomRole(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject bodyJson = ctx.getBodyAsJson();
                String name = bodyJson.getString("name");
                String description = bodyJson.getString("description");
                JsonArray permissionIds = bodyJson.getJsonArray("permissionIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermCustomRoleService.createCustomRole(partnerId, name, description, permissionIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CREATE ROLE ERROR: ", e);
                resp.put(MESSAGE, "create_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateCustomRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String roleId = req.getParam("roleId");

                JsonObject bodyJson = ctx.getBodyAsJson();
                String name = bodyJson.getString("name");
                String description = bodyJson.getString("description");
                JsonArray permissionIds = bodyJson.getJsonArray("permissionIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermCustomRoleService.updateCustomRoleById(partnerId, roleId, name, description, permissionIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CREATE ROLE ERROR: ", e);
                resp.put(MESSAGE, "create_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteCustomRoleById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String roleId = req.getParam("roleId");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermCustomRoleService.deleteCustomRoleById(partnerId, roleId, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "DELETE ROLE ERROR: ", e);
                resp.put(MESSAGE, "delete_role_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkCustomRoleName(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = req.getParam("partnerId") != null ? Integer.parseInt(req.getParam("partnerId")) : null;
                
                JsonObject bodyJson = ctx.getBodyAsJson();
                String roleId = bodyJson.getString("roleId");
                String name = bodyJson.getString("name");

                JsonObject json = MpPermCustomRoleService.checkCustomRoleName(partnerId, roleId, name);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "CHECK ROLE NAME ERROR: ", e);
                resp.put(MESSAGE, "check_role_name_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
