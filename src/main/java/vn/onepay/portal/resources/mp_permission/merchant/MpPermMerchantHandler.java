package vn.onepay.portal.resources.mp_permission.merchant;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;

public class MpPermMerchantHandler implements IConstants {
	private MpPermMerchantHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermMerchantHandler.class.getName());

    public static void getMerchantsByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String keywords = req.getParam("keywords");
                
                JsonObject json = MpPermMerchantService.getMerchantsByPartner(partnerId, keywords);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET MERCHANTS BY PARTNER ERROR: ", e);
                resp.put(MESSAGE, "get_merchants_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantsByUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");
                String keywords = req.getParam("keywords");
                
                JsonObject json = MpPermMerchantService.getMerchantsByUser(partnerId, userId, keywords);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET MERCHANTS BY USER ERROR: ", e);
                resp.put(MESSAGE, "get_merchants_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
