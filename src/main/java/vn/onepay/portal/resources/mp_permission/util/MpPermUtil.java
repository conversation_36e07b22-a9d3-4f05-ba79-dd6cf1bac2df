package vn.onepay.portal.resources.mp_permission.util;

import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;

public class MpPermUtil implements IConstants {
	private MpPermUtil(){}
	
	public static String getOperator(RoutingContext ctx) throws Exception {
		String userId = ctx.request().getHeader(X_USER_ID);
        User user = UserDao.get(userId);
        
		if (user != null){
			return user.getEmail();
		}
		
		return "admin_onepay";
	}
}
