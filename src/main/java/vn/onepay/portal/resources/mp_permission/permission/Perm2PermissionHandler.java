package vn.onepay.portal.resources.mp_permission.permission;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;

public class Perm2PermissionHandler implements IConstants{
	private Perm2PermissionHandler() {}

    private static final Logger logger = Logger.getLogger(Perm2PermissionHandler.class.getName());

    public static void getBasePermissions(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                JsonObject json = Perm2PermissionService.getBasePermissions();

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET PERMISSIONS ERROR: ", e);
                resp.put(MESSAGE, "get_permissions_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPermissionByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                
                JsonObject json = Perm2PermissionService.getPermissionByPartner(partnerId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET PERMISSIONS ERROR: ", e);
                resp.put(MESSAGE, "get_permissions_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
