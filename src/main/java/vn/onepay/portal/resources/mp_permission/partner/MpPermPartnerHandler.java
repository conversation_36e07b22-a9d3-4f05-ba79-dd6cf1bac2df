package vn.onepay.portal.resources.mp_permission.partner;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;

public class MpPermPartnerHandler implements IConstants {
	private MpPermPartnerHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermPartnerHandler.class.getName());

    public static void getPartners(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer pageSize = req.getParam("pageSize") != null ? Integer.parseInt(req.getParam("pageSize")) : 100;
				Integer page = req.getParam("page") != null ? Integer.parseInt(req.getParam("page")) : 0;
                String keywords = req.getParam("keywords");
                boolean noEcoApp = Boolean.parseBoolean(req.getParam("noEcoApp"));
                boolean noUser = Boolean.parseBoolean(req.getParam("noUser"));
                
                JsonObject json = MpPermPartnerService.getPartners(pageSize, page, keywords, noEcoApp, noUser);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET PARTNERS ERROR: ", e);
                resp.put(MESSAGE, "get_partners_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
