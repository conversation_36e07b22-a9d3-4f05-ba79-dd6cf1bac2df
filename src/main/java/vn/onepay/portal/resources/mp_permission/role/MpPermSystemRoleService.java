package vn.onepay.portal.resources.mp_permission.role;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermSystemRoleService {
	private MpPermSystemRoleService() {}

    public static JsonObject getBaseSystemRoles() throws Exception {  
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/base-system-roles");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

	public static JsonObject getSystemRolesByPartner(Integer partnerId) throws Exception {  
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/system-roles");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getSystemRoleIdsByPartner(Integer partnerId) throws Exception {  
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/system-role-ids");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
    
    public static JsonObject getSystemRolesByUser(Integer partnerId, String userId) throws Exception {  
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/system-roles");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject updateSystemRolesByPartner(
        Integer partnerId,
        JsonArray roleIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/system-roles");

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("roleIds", roleIds);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "PUT", body);
    }
}
