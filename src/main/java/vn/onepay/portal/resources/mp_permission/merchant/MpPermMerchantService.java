package vn.onepay.portal.resources.mp_permission.merchant;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import java.util.Objects;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermMerchantService {
	private MpPermMerchantService() {}

	public static JsonObject getMerchantsByPartner(Integer partnerId, String keywords) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/merchants");

        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("?keywords=").append(encodeStr);
        }

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

	public static JsonObject getMerchantsByUser(Integer partnerId, String userId, String keywords) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
        reqUrl.append("/user/").append(userId);
        reqUrl.append("/merchants");

        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("?keywords=").append(encodeStr);
        }

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
}
