package vn.onepay.portal.resources.mp_permission.permission;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class Perm2PermissionService {
	private Perm2PermissionService() {}

    public static JsonObject getBasePermissions() throws Exception {        
        StringBuilder reqUrl = new StringBuilder();
        
		reqUrl.append("/base-permissions");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getPermissionByPartner(Integer partnerId) throws Exception {        
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/partner/").append(partnerId);
		reqUrl.append("/permissions");

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }
}
