package vn.onepay.portal.resources.mp_permission.role;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.net.URLEncoder;
import java.util.Objects;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.client.MpPermissionClient;

public class MpPermOnepayRoleService {
	private MpPermOnepayRoleService() {}

    public static JsonObject getOnepayRoles(Integer pageSize, Integer page, String keywords) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/onepay-roles");

        reqUrl.append("?pageSize=").append(pageSize);
        reqUrl.append("&page=").append(page);

        if (Objects.nonNull(keywords)) {
            String encodeStr = URLEncoder.encode(keywords, UTF_8);
            reqUrl.append("&keywords=").append(encodeStr);
        }

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

    public static JsonObject getOnepayRoleById(String roleId) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/onepay-role/").append(roleId);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "GET", null);
    }

	public static JsonObject createOnepayRole(
        String name,
        String description,
        String name_vi,
        String description_vi,
        JsonArray permissionIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/onepay-role");

        JsonObject body = new JsonObject();
        body.put("name", name);
        body.put("description", description);
        body.put("name_vi", name_vi);
        body.put("description_vi", description_vi);
        body.put("permissionIds", permissionIds);
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }

    public static JsonObject updateOnepayRoleById(
        String roleId,
        String name,
        String description,
        String name_vi,
        String description_vi,
        JsonArray permissionIds,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/onepay-role/").append(roleId);

        JsonObject body = new JsonObject();
        body.put("name", name);
        body.put("description", description);
        body.put("name_vi", name_vi);
        body.put("description_vi", description_vi);
        body.put("permissionIds", permissionIds);
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "PUT", body);
    }

    public static JsonObject deleteOnepayRoleById(
        String roleId,
        String operator
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();

        reqUrl.append("/onepay-role/").append(roleId);

        JsonObject body = new JsonObject();
        body.put("operator", operator);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "DELETE", body);
    }

    public static JsonObject checkOnepayRoleName(
        String roleId,
        String name
    ) throws Exception {
        StringBuilder reqUrl = new StringBuilder();
        
        reqUrl.append("/onepay-role/check-name");

        JsonObject body = new JsonObject();
        body.put("roleId", roleId);
        body.put("name", name);

        return MpPermissionClient.sendRequest(reqUrl.toString(), "POST", body);
    }
}
