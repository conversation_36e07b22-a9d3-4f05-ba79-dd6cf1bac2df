package vn.onepay.portal.resources.mp_permission.role;

import static vn.onepay.portal.Util.sendResponse;

import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.mp_permission.util.MpPermUtil;

public class MpPermSystemRoleHandler implements IConstants {
	private MpPermSystemRoleHandler() {}

    private static final Logger logger = Logger.getLogger(MpPermSystemRoleHandler.class.getName());

    public static void getBaseSystemRoles(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                JsonObject json = MpPermSystemRoleService.getBaseSystemRoles();

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET BASE SYSTEM ROLES ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

	public static void getSystemRolesByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject json = MpPermSystemRoleService.getSystemRolesByPartner(partnerId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES BY PARTNER ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSystemRoleIdsByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject json = MpPermSystemRoleService.getSystemRoleIdsByPartner(partnerId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLE IDS BY PARTNER ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateSystemRolesByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));

                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray roleIds = bodyJson.getJsonArray("roleIds");

                String operator = MpPermUtil.getOperator(ctx);

                JsonObject json = MpPermSystemRoleService.updateSystemRolesByPartner(partnerId, roleIds, operator);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES BY PARTNER ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getSystemRolesByUser(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject resp = new JsonObject();

            try {
                HttpServerRequest req = ctx.request();
                Integer partnerId = Integer.parseInt(req.getParam("partnerId"));
                String userId = req.getParam("userId");

                JsonObject json = MpPermSystemRoleService.getSystemRolesByUser(partnerId, userId);

                sendResponse(ctx, 200, json);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "GET ROLES BY USER ERROR: ", e);
                resp.put(MESSAGE, "get_roles_failed");
                sendResponse(ctx, 500, resp);
                ctx.fail(e);
            }
        }, false, null);
    }
}
