package vn.onepay.portal.resources.monthly_fee_report;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.axis.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.ReportFileBuilder;

public class MonthlyFeeReportService implements IConstants {

    private static final Logger logger = LogManager.getLogger(MonthlyFeeReportService.class);

    public static Map<String, Object> buildParamsMergedEmailRequest(JsonArray selectedRows, SimpleDateFormat sdfEmail, String subjectEmail, String contentEmail, String toEmail, String ccEmail, String fileTypes, String attachDetail, String emailFeeMonthType) throws Exception {
        Set<String> messages = new HashSet<>();
        Map<String, Object> mIn = new HashMap<>();
        Map<String, Object> result = new HashMap<>();
        try {
            String reportIds = selectedRows.stream()
                .map(report -> ((JsonObject) report).getValue("reportId").toString()).collect(Collectors.joining(","));
            // check exist big merchant report
            List<Integer> bigMerchantReports = MonthlyFeeReportDao.getBigMerchantReports(reportIds);

            // check if GSM report is send at day <= 4th of month
            List<Integer> gsmReports = selectedRows.stream()
                .map(report -> (JsonObject) report)
                .filter(report -> {
                    JsonObject rowInstance = (JsonObject) report;
                    Long partnerId = rowInstance.getLong("partnerId");
                    return partnerId.longValue() == 74271124 && LocalDate.now().getDayOfMonth() <= 4;
                })
                .map(report -> report.getInteger("reportId"))
                .collect(Collectors.toList());

            // filter big merchant report
            reportIds = selectedRows.stream()
                .map(report -> ((JsonObject) report).getInteger("reportId"))
                .filter(reportId -> !bigMerchantReports.contains(reportId))
                .filter(reportId -> !gsmReports.contains(reportId))
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            // process insert request send mail bbds merge
            List<JsonObject> listMonthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportIds);
            // find lastest dReport
            JsonObject lastestMonthlyFeeReport = listMonthlyFeeReport.stream()
                    .max(Comparator.comparingLong(item -> item.getLong("dateReportTime")))
                    .get();

            // unique merchantIds,payChannels from list report
            String merchantIds = listMonthlyFeeReport.stream().map(item -> item.getString("merchantIds")).collect(Collectors.joining(","));
            merchantIds = Arrays.asList(merchantIds.split(","))
                    .stream().collect(Collectors.toSet())
                    .stream().collect(Collectors.joining(","));
            String payChannels = listMonthlyFeeReport.stream().map(item -> item.getString("payChannels")).collect(Collectors.joining(","));
            payChannels = Arrays.asList(payChannels.split(","))
                    .stream().collect(Collectors.toSet())
                    .stream().collect(Collectors.joining(","));

            // receiptType,lastMerchantName,payChannels,idConfig from lastest
            Integer lastestIdReport = lastestMonthlyFeeReport.getInteger("reportId");
            Integer idConfig = lastestMonthlyFeeReport.getInteger("idConfig");
            String receiptType = lastestMonthlyFeeReport.getString("receiptType");
            String lastMerchantName = lastestMonthlyFeeReport.getString("lastMerchantName");
            if (receiptType == null) {
                logger.warn("Missing receipt type value");
                throw new Exception("Missing receipt type value");
            }
            // find min fromDateTime, max toDateTime
            Date fromDate = null, toDate = null;
            Long fromDateTime = listMonthlyFeeReport.stream()
                    .min(Comparator.comparingLong(item -> item.getLong("fromDateTime")))
                    .get()
                    .getLong("fromDateTime");
            Long toDateTime = listMonthlyFeeReport.stream()
                    .max(Comparator.comparingLong(item -> item.getLong("toDateTime")))
                    .get()
                    .getLong("toDateTime");
            Long toDateTimeView = listMonthlyFeeReport.stream()
                    .max(Comparator.comparingLong(item -> item.getLong("toDateTimeView")))
                    .get()
                    .getLong("toDateTimeView");
            if (fromDateTime != null && toDateTime != null) {
                if (receiptType.contains("by_advance_date")) {
                    fromDate = new Date(fromDateTime);
                    toDate = new Date(toDateTimeView);
                }
                if (receiptType.contains("by_transaction_date")) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(fromDateTime);
                    int hoursFromDate = calendar.get(Calendar.HOUR_OF_DAY);
                    logger.info("hoursFromDate: " + hoursFromDate);
                    // cutoff 24h
                    if (hoursFromDate == 0) {
                        fromDate = new Date(fromDateTime);
                        toDate = new Date(toDateTimeView);
                    }
                    // cutoff 17h
                    if (hoursFromDate == 17) {
                        long plusHours = 7 * 60 * 60 * 1000; // 7 hours
                        fromDate = new Date(fromDateTime + plusHours);
                        toDate = new Date(toDateTime);
                    }
                }
            }
            String timeAdvance = (fromDate != null && toDate != null) ? sdfEmail.format(fromDate) + " - " + sdfEmail.format(toDate) : "";

            // filter for special merchant ids
            String specialMerchantIds = ReportFileBuilder.filterSpecialMerchantIds(merchantIds);
            String subjectEmailRow = subjectEmail.replace("PAY_CHANNELS", payChannels.replaceAll("DD", "Direct Debit"))
                    .replace("LAST_MERCHANT_NAME", lastMerchantName)
                    .replace("MERCHANT_IDS", specialMerchantIds == null ? "" : specialMerchantIds)
                    .replace("TIME_ADVANCE_CONFIG", timeAdvance);
            String contentEmailRow = contentEmail.replaceAll("TIME_ADVANCE_CONFIG", timeAdvance);
            // process email
            Map<String, Object> emailMap = new HashMap<>();
            String emailFeeMonth = "";
            String emailMerchant = "";
            String emailChosenToSend = "";
            emailMap = MonthlyFeeReportDao.getEmailMonthlyFee(idConfig);
            List<?> emailFeeMonthList = (List<?>) emailMap.get("emailFeeMonthList");
            List<?> emailMerchantList = (List<?>) emailMap.get("emailMerchantList");
            emailFeeMonth = emailFeeMonthList.stream().filter(email -> email != null).map(Object::toString).collect(Collectors.joining(","));
            emailMerchant = emailMerchantList.stream().filter(email -> email != null).map(Object::toString).collect(Collectors.joining(","));
            if ("".equals(emailFeeMonth) || null == emailFeeMonth)
                emailChosenToSend = emailMerchant;
            else
                emailChosenToSend = emailFeeMonth;

            String emailConfig = "MAIL_LOAD_FROM_CONFIG";
            String environment = Config.getString("payment2.environment", "pilot");
            String mailToDef = Config.getString("payment2.control_minutes_mail_to_def", "<EMAIL>");
            if ("prod".equals(environment)) {
                emailChosenToSend = toEmail.trim().replace(emailConfig, emailChosenToSend);
            } else {
                emailChosenToSend = toEmail.trim().replace(emailConfig, mailToDef);
            }

            mIn.put(MFR_ID_CONFIG, idConfig);
            mIn.put(MFR_EMAIL_TO, emailChosenToSend);
            mIn.put(MFR_EMAIL_CC, ccEmail);
            mIn.put(MFR_EMAIL_SUBJECT, subjectEmailRow);
            mIn.put(MFR_EMAIL_CONTENT, contentEmailRow);
            mIn.put(MFR_EMAIL_FILE_TYPES, fileTypes);
            mIn.put(MFR_EMAIL_ATTACH_DETAIL, attachDetail);
            mIn.put(MFR_EMAIL_FEE_MONTH_TYPE, emailFeeMonthType);
            mIn.put(MFR_ID_MONTHLY_REPORT, lastestIdReport);
            mIn.put(MFR_IDS_MONTHLY_REPORT, reportIds);
            MonthlyFeeReportDao.insertEmailMonthlyFee(mIn);

            // Handle result
            if (!bigMerchantReports.isEmpty() || !gsmReports.isEmpty()) {
                result.put("responseCode", 204);
                if (!bigMerchantReports.isEmpty()) {
                    messages.add("Tồn tại báo cáo của BIG MERCHANT. Hệ thống tự động bỏ qua các báo cáo này.");
                }
                if (!gsmReports.isEmpty()) {
                    messages.add("Không được tạo với GSM: Ngày gửi của GSM chưa đến mùng 4 hàng tháng.");
                }
            } else {
                result.put("responseCode", 200);
                messages.add("Tạo yêu cầu gửi BBĐS thành công!");
            }
            result.put("messages", messages.stream().collect(Collectors.joining("\n")));

            return result;
        } catch (Exception e) {
            throw e;
        }
    }

    public static Map<String, Object> buildParamsStandAloneEmailRequest(JsonArray selectedRows, SimpleDateFormat sdfEmail, String subjectEmail, String contentEmail, String toEmail, String ccEmail, String fileTypes, String attachDetail, String emailFeeMonthType) throws Exception {
        Set<String> messages = new HashSet<>();
        Map<String, Object> mIn = new HashMap<>();
        Map<String, Object> result = new HashMap<>();
        try {

            // check exist big merchant report
            String reportIds = selectedRows.stream()
                .map(report -> ((JsonObject) report).getValue("reportId").toString()).collect(Collectors.joining(","));
            List<Integer> bigMerchantReports = MonthlyFeeReportDao.getBigMerchantReports(reportIds);

            // check if GSM report is send at day <= 4th of month
            List<Integer> gsmReports = selectedRows.stream()
                .map(report -> (JsonObject) report)
                .filter(report -> {
                    JsonObject rowInstance = (JsonObject) report;
                    Long partnerId = rowInstance.getLong("partnerId");
                    return partnerId.longValue() == 74271124 && LocalDate.now().getDayOfMonth() <= 4;
                })
                .map(report -> report.getInteger("reportId"))
                .collect(Collectors.toList());

            // filter selected rows
            List<JsonObject> filteredSelectedRows = selectedRows.stream()
                .map(report -> (JsonObject) report)
                .filter(report -> !bigMerchantReports.contains(((JsonObject) report).getInteger("reportId")))
                .filter(report -> !gsmReports.contains(((JsonObject) report).getInteger("reportId")))
                .collect(Collectors.toList());
            
            for (Object row : filteredSelectedRows) {
                try {
                    JsonObject rowInstance = (JsonObject) row;
                    Integer reportId = Convert.parseInt(rowInstance.getValue("reportId").toString(), 0);
                    List<JsonObject> listMonthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString());
                    if (listMonthlyFeeReport.isEmpty()) {
                        logger.warn("Can not find reportId: " + reportId);
                        continue;
                    } ;
                    JsonObject monthlyFeeReport = listMonthlyFeeReport.get(0);
                    Integer idConfig = monthlyFeeReport.getInteger("idConfig");
                    // process time by cutoff config
                    Date fromDate = null, toDate = null;
                    String receiptType = monthlyFeeReport.getString("receiptType");
                    if (receiptType == null) {
                        logger.warn("ReportId: " + reportId + " is missing receipt type value");
                        continue;
                    }
                    Long fromDateTime = monthlyFeeReport.getLong("fromDateTime");
                    Long toDateTime = monthlyFeeReport.getLong("toDateTime");
                    Long toDateTimeView = monthlyFeeReport.getLong("toDateTimeView");

                    if (fromDateTime != null && toDateTime != null) {
                        if (receiptType.contains("by_advance_date")) {
                            fromDate = new Date(fromDateTime);
                            toDate = new Date(toDateTimeView);
                        }
                        if (receiptType.contains("by_transaction_date")) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTimeInMillis(fromDateTime);
                            int hoursFromDate = calendar.get(Calendar.HOUR_OF_DAY);
                            logger.info("hoursFromDate: " + hoursFromDate);
                            // cutoff 24h
                            if (hoursFromDate == 0) {
                                fromDate = new Date(fromDateTime);
                                toDate = new Date(toDateTimeView);
                            }
                            // cutoff 17h
                            if (hoursFromDate == 17) {
                                long plusHours = 7 * 60 * 60 * 1000; // 7 hours
                                fromDate = new Date(fromDateTime + plusHours);
                                toDate = new Date(toDateTime);
                            }
                        }
                    }
                    String timeAdvance = (fromDate != null && toDate != null) ? sdfEmail.format(fromDate) + " - " + sdfEmail.format(toDate) : "";

                    // String partnerName = monthlyFeeReport.getString("partnerName");
                    String lastMerchantName = monthlyFeeReport.getString("lastMerchantName");
                    String payChannels = monthlyFeeReport.getString("payChannels");
                    String merchantIds = monthlyFeeReport.getString("merchantIds");
                    // filter for special merchant ids
                    String specialMerchantIds = ReportFileBuilder.filterSpecialMerchantIds(merchantIds);
                    String subjectEmailRow = subjectEmail.replace("PAY_CHANNELS", payChannels)
                            .replace("LAST_MERCHANT_NAME", lastMerchantName)
                            .replace("MERCHANT_IDS", specialMerchantIds == null ? "" : specialMerchantIds)
                            .replace("TIME_ADVANCE_CONFIG", timeAdvance);
                    String contentEmailRow = contentEmail.replaceAll("TIME_ADVANCE_CONFIG", timeAdvance);
                    // process email
                    Map<String, Object> emailMap = new HashMap<>();
                    String emailFeeMonth = "";
                    String emailMerchant = "";
                    String emailChosenToSend = "";
                    emailMap = MonthlyFeeReportDao.getEmailMonthlyFee(idConfig);
                    List<?> emailFeeMonthList = (List<?>) emailMap.get("emailFeeMonthList");
                    List<?> emailMerchantList = (List<?>) emailMap.get("emailMerchantList");
                    emailFeeMonth = emailFeeMonthList.stream().filter(email -> email != null).map(Object::toString).collect(Collectors.joining(","));
                    emailMerchant = emailMerchantList.stream().filter(email -> email != null).map(Object::toString).collect(Collectors.joining(","));
                    if ("".equals(emailFeeMonth) || null == emailFeeMonth)
                        emailChosenToSend = emailMerchant;
                    else
                        emailChosenToSend = emailFeeMonth;

                    String emailConfig = "MAIL_LOAD_FROM_CONFIG";
                    String environment = Config.getString("payment2.environment", "pilot");
                    String mailToDef = Config.getString("payment2.control_minutes_mail_to_def", "<EMAIL>");
                    if ("prod".equals(environment)) {
                        emailChosenToSend = toEmail.trim().replace(emailConfig, emailChosenToSend);
                    } else {
                        emailChosenToSend = toEmail.trim().replace(emailConfig, mailToDef);
                    }

                    if (StringUtils.isEmpty(emailChosenToSend)) {
                        logger.warn("Error create request send mail fee month: Report ID: " + reportId + " - Config ID: " + idConfig + " - Mail CC: " + emailChosenToSend);
                        continue;
                    }

                    mIn.put(MFR_ID_CONFIG, idConfig);
                    mIn.put(MFR_EMAIL_TO, emailChosenToSend);
                    mIn.put(MFR_EMAIL_CC, ccEmail);
                    mIn.put(MFR_EMAIL_SUBJECT, subjectEmailRow);
                    mIn.put(MFR_EMAIL_CONTENT, contentEmailRow);
                    mIn.put(MFR_EMAIL_FILE_TYPES, fileTypes);
                    mIn.put(MFR_EMAIL_ATTACH_DETAIL, attachDetail);
                    mIn.put(MFR_EMAIL_FEE_MONTH_TYPE, emailFeeMonthType);
                    mIn.put(MFR_ID_MONTHLY_REPORT, reportId);

                    MonthlyFeeReportDao.insertEmailMonthlyFee(mIn);

                    
                } catch (Exception e) {
                    logger.warn(e);
                    continue;
                }
            } 
            // Handle result
            if (!bigMerchantReports.isEmpty() || !gsmReports.isEmpty()) {
                result.put("responseCode", 204);
                if (!bigMerchantReports.isEmpty()) {
                    messages.add("Tồn tại báo cáo của BIG MERCHANT. Hệ thống tự động bỏ qua các báo cáo này.");
                }
                if (!gsmReports.isEmpty()) {
                    messages.add("Không được tạo với GSM: Ngày gửi của GSM chưa đến mùng 4 hàng tháng.");
                }
            } else {
                result.put("responseCode", 200);
                messages.add("Tạo yêu cầu gửi BBĐS thành công!");
            }
            result.put("messages", messages.stream().collect(Collectors.joining("\n")));
        } catch (Exception e) {
            throw e;
        }
        return result;
    }
}
