package vn.onepay.portal.resources.monthly_fee_report;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.onepay.commons.util.Convert;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.ReportFileBuilder;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.Commons;
import vn.onepay.portal.resources.payment2.dto.MonthlyReportDetail;

public class MonthlyFeeReportDao extends Db {
    private static final String MONTHLY_FEE_REPORT_SEARCH = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.SEARCH_MONTHLY(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_MONTHLY_FEE_REPORT_BY_ID = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_MONTHLY_FEE_REPORT_BY_ID(?,?,?,?)}";
    private static final String COUNT_MONTHLY_FEE_REPORT_BY_ID = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.IS_EXIST_MONTHLY_FEE_REPORT(?,?)}";
    private static final String GET_PARTNERS = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_PARTNERS(?,?)}";
    private static final String GET_MERCHANTS = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_MERCHANTS(?,?)}";
    private static final String GET_TOTAL_ROW_MONTH_FEE_REPORT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_TOTAL_ROW_MONTHLY_FEE_REPORT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private final static String LOAD_ADV_MONTH_FEE_REPORT = "{call ONEFIN.PKG_MONTHLY_FEE_REPORT_DETAIL.GET_MONTHLY_REPORT_DETAIL(?,?,?,?,?,?,?,?)}";
    private static final String LOAD_ADV_DETAIL_MONTH_FEE_REPORT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.LOAD_DETAIL_ADV_MONTH(?,?,?,?)}";
    private static final String GET_TOTAL_ROW_ADV_DETAIL_MONTH_FEE_REPORT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.TOTAL_ROWS_LOAD_DETAIL_ADV_MONTH(?,?)}";
    private static final String GET_MONTHLY_FEE_NOT_SENDING_EMAIL = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_MONTHLY_FEE_NOT_SENDING_EMAIl(?,?,?,?)}";
    private static final String INSERT_EMAIL_MONTHLY_FEE = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.INSERT_EMAIL_MONTHLY_FEE(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_EMAIl_MERCHANT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_MERCHANT_EMAIL(?,?,?,?,?)}";
    private static final String CREATE_RECEIPT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.CREATE_UPDATE_RECEIPT_REQUEST(?)}";
    private static final String GET_CREATED_RECEIPT = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_CREATED_RECEIPT_REQUESTS(?,?,?,?)}";
    private static final String GET_BIG_MERCHANT_REPROTS = "{ call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_BIG_MERCHANT_REPORTS(?,?,?,?)}";
    private static final String GET_FEE_MONTH = "{call ONEFIN.PKG_MONTHLY_FEE_REPORT.GET_FEE_MONTH(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(MonthlyFeeReportDao.class.getName());

    public static Map<String, Object> list(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<MonthlyFeeReportDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        Integer nTotal = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(MONTHLY_FEE_REPORT_SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, (String) mIn.get(MFR_PARTNER_IDS));
            cs.setString(6, (String) mIn.get(MFR_MERCHANT_IDS));
            cs.setString(7, (String) mIn.get(MFR_TAX_CODE));
            cs.setString(8, (String) mIn.get(MFR_FROM_DATE));
            cs.setString(9, (String) mIn.get(MFR_TO_DATE));
            cs.setInt(10, mIn.get(MFR_PAGE_ACTIVE) == null ? 0 : (Integer) mIn.get(MFR_PAGE_ACTIVE));
            cs.setInt(11, mIn.get(MFR_PAGE_SIZE) == null ? Integer.MAX_VALUE : (Integer) mIn.get(MFR_PAGE_SIZE));
            cs.setString(12, (String) mIn.get(MFR_TYPE_ADVANCES));
            cs.setString(13, (String) mIn.get(MFR_TIME_ADVANCES));
            cs.setString(14, (String) mIn.get(MFR_PAY_CHANNELS));
            cs.setString(15, (String) mIn.get(MFR_RECEIPT_TYPES));
            cs.setString(16, (String) mIn.get(MFR_RECEIPT_STATE));
            cs.setString(17, (String) mIn.get(MFR_NOTIFY_STATE));
            cs.setString(18, (String) mIn.get(MFR_CONTROL_MINUTES));
            cs.execute();

            sResult = cs.getString(4);
            nResult = cs.getInt(3);
            nTotal = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.search_monthly_adv_payments: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    MonthlyFeeReportDto mfrd = new MonthlyFeeReportDto();
                    mfrd.setIndex(rs.getInt("RNUM"));
                    mfrd.setReportId(rs.getInt("REPORT_ID"));
                    mfrd.setPartnerName(rs.getString("PARTNER_NAME"));
                    mfrd.setBusinessName(rs.getString("BUSINESS_NAME"));
                    mfrd.setPartnerId(rs.getInt("PARTNER_ID"));
                    mfrd.setMerchantIds(rs.getString("MERCHANT_IDS"));
                    mfrd.setContractCode(rs.getString("CONTRACT_CODE"));
                    mfrd.setContractDateTime(rs.getDate("CONTRACT_DATE") == null ? 0 : rs.getDate("CONTRACT_DATE").getTime());
                    mfrd.setContractId(rs.getInt("CONTRACT_ID"));
                    mfrd.setAddendum(rs.getString("ADDENDUM"));
                    mfrd.setControlMinutes(rs.getString("CONTROL_MINUTES"));
                    mfrd.setMerchantName(rs.getString("MERCHANT_NAME"));
                    mfrd.setFromDateTime(rs.getTimestamp("D_FROM") == null ? 0 : rs.getTimestamp("D_FROM").getTime());
                    mfrd.setToDateTime(rs.getTimestamp("D_TO") == null ? 0 : rs.getTimestamp("D_TO").getTime());
                    mfrd.setToDateTimeView(mfrd.getToDateTime()-1000);

                    JsonObject jAdvance = new JsonObject(rs.getString("J_ADVANCE") == null ? "{}" : rs.getString("J_ADVANCE"));
                    String countSuccess = filterStringNullPointer(jAdvance.getValue("total_txn_success"));
                    mfrd.setCountSuccess(countSuccess);
                    String countFailed = filterStringNullPointer(jAdvance.getValue("total_txn_failed"));
                    mfrd.setCountFailed(countFailed);
                    String originAmountUsd = filterStringNullPointer(jAdvance.getValue("total_usd"));
                    mfrd.setOriginAmountUsd(originAmountUsd);
                    String originAmountVnd = filterStringNullPointer(jAdvance.getValue("total_vnd"));
                    mfrd.setOriginAmountVnd(originAmountVnd);
                    String partnerDiscountAmount = filterStringNullPointer(jAdvance.getValue("total_partner_discount_amount"));
                    mfrd.setPartnerDiscountAmount(partnerDiscountAmount);
                    String merchantDiscountAmount = filterStringNullPointer(jAdvance.getValue("total_merchant_discount_amount"));
                    mfrd.setMerchantDiscountAmount(merchantDiscountAmount);
                    String amountTotalVND = filterStringNullPointer(jAdvance.getValue("total_change_to_vnd"));
                    mfrd.setAmountTotalVnd(amountTotalVND);
                    String feeSuccess = filterStringNullPointer(jAdvance.getValue("total_fee_txn_success"));
                    mfrd.setFeeSuccess(feeSuccess);
                    String feeFailed = filterStringNullPointer(jAdvance.getValue("total_fee_txn_failed"));
                    mfrd.setFeeFailed(feeFailed);
                    String feeIta = filterStringNullPointer(jAdvance.getValue("total_fee_ita"));
                    mfrd.setFeeIta(feeIta);
                    String feeEcom = filterStringNullPointer(jAdvance.getValue("total_fee_ecom"));
                    mfrd.setFeeEcom(feeEcom);
                    String totalFeeStr = filterStringNullPointer(jAdvance.getValue("total_fee_inc_vat"));
                    Double totalFee = Double.parseDouble(totalFeeStr);
                    Double feeMonth = rs.getDouble("FEE_MONTH");
                    Double feeDiscount = rs.getDouble("FEE_DISCOUNT");
                    String totalFeeFinal = filterStringNullPointer(totalFee + feeMonth + feeDiscount);
                    mfrd.setTotalFee(totalFeeFinal);
                    String totalVAT = filterStringNullPointer(jAdvance.getValue("total_vat"));
                    mfrd.setTotalVAT(totalVAT);
                    String advanceAmount = filterStringNullPointer(jAdvance.getValue("total_advance"));
                    mfrd.setAdvanceAmount(advanceAmount);
                    String totalFeeCollected = filterStringNullPointer(jAdvance.getValue("total_fee_collected"));
                    mfrd.setTotalFeeCollected(totalFeeCollected);
                    String totalFeeReceivable = filterStringNullPointer(Double.parseDouble(totalFeeFinal) - Double.parseDouble(totalFeeCollected));
                    mfrd.setTotalFeeReceivable(totalFeeReceivable);
                    
                    mfrd.setDiscountFee(feeDiscount);
                    mfrd.setFeeMonth(feeMonth);
                    mfrd.setPayChannels(rs.getString("PAY_CHANNELS").replaceAll("DD","Direct Debit"));
                    mfrd.setTimeAdvance(rs.getString("TIME_ADVANCE"));
                    mfrd.setTypeAdvance(rs.getString("TYPE_ADVANCE"));
                    mfrd.setReceiptType(rs.getString("RECEIPT_TYPE"));
                    mfrd.setReceiptState(rs.getString("RECEIPT_STATE"));
                    mfrd.setIdConfig(rs.getInt("ID_CONFIG"));
                    mfrd.setTaxCode(rs.getString("TAX_CODE"));
                    mfrd.setAccountNumbers(rs.getString("ACCOUNT_NUMBERS"));
                    mfrd.setNumberEmailSent(rs.getLong("NUMBER_EMAIL_SENT"));
                    result.add(mfrd);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("data", result);
        dataList.put("total", nTotal);
        return dataList;
    }

    public static List<Integer> getBigMerchantReports(String reportIds) throws Exception {
        Exception exception = null;
        List<Integer> bigMerchantReports = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String sResult = "";
        Integer nResult = 0; 
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_BIG_MERCHANT_REPROTS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, reportIds);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 1) 
                throw new Exception("DB GET BIG MERCHANT REPORTS ERROR: " + sResult);
            while (rs!=null && rs.next()) {
                Integer reportId = rs.getInt("N_ID");
                bigMerchantReports.add(reportId);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return bigMerchantReports;
    }

    public static List<JsonObject> getMonthlyFeeReport(String reportId) throws Exception {
        Exception exception = null;
        List<JsonObject> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String sResult = "";
        Integer nResult = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_MONTHLY_FEE_REPORT_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, reportId);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 1) 
                throw new Exception("DB GET_MONTHLY_FEE_REPORT_BY_ID ERROR: " + sResult);
            while (rs!=null && rs.next()) {
                JsonObject monthlyFeeReport = new JsonObject();
                monthlyFeeReport.put("reportId", rs.getInt("N_ID"));
                monthlyFeeReport.put("partnerName", rs.getString("S_SHORT_NAME"));
                monthlyFeeReport.put("contractCode", rs.getString("S_CONTRACT_CODE"));
                monthlyFeeReport.put("contractDateTime", rs.getDate("D_CONTRACT_SIGN") == null ? null : rs.getDate("D_CONTRACT_SIGN").getTime());
                monthlyFeeReport.put("merchantIds", rs.getString("S_MERCHANT_IDS"));
                monthlyFeeReport.put("payChannels", rs.getString("S_PAY_CHANNELS").replaceAll("DD","Direct Debit"));
                monthlyFeeReport.put("taxCode", rs.getString("S_TAX_CODE"));
                monthlyFeeReport.put("idConfig", rs.getInt("ID_CONFIG"));
                monthlyFeeReport.put("typeAdvances", rs.getString("S_TYPE_ADVANCE"));
                monthlyFeeReport.put("partnerId", rs.getInt("N_PARTNER_ID"));
                monthlyFeeReport.put("receiptType", rs.getString("S_TYPE_RECEIPT"));
                monthlyFeeReport.put("contractId", rs.getInt("N_CONTRACT_ID"));
                monthlyFeeReport.put("lastMerchantName", rs.getString("LAST_MERCHANT_NAME"));
                monthlyFeeReport.put("businessRegisName", rs.getString("S_BUSINESS_REGIS_NAME"));
                monthlyFeeReport.put("feeMonthTemplate", rs.getString("S_FEEMONTH_TEMPLATE"));
                monthlyFeeReport.put("dateReportTime", rs.getDate("D_REPORT") == null ? null : rs.getDate("D_REPORT").getTime());

                //process J_ADVANCE
                JsonObject jAdvance = new JsonObject(rs.getString("J_ADVANCE") == null ? "{}" : rs.getString("J_ADVANCE"));
                Long countSuccess = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_txn_success")), 0);
                monthlyFeeReport.put("countSuccess", countSuccess);
                Long countFailed = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_txn_failed")), 0);
                monthlyFeeReport.put("countFailed", countFailed);
                Double originAmountUsd = Convert.parseDouble(filterStringNullPointer(jAdvance.getValue("total_usd")), 0);
                monthlyFeeReport.put("originAmountUsd", originAmountUsd);
                Long originAmountVnd = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_vnd")), 0);
                monthlyFeeReport.put("originAmountVnd", originAmountVnd);
                Long partnerDiscountAmount = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_partner_discount_amount")), 0);
                monthlyFeeReport.put("partnerDiscountAmount", partnerDiscountAmount);
                Long merchantDiscountAmount = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_merchant_discount_amount")), 0);
                monthlyFeeReport.put("merchantDiscountAmount", merchantDiscountAmount);
                Long amountTotalVND = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_change_to_vnd")), 0);
                monthlyFeeReport.put("amountTotalVND", amountTotalVND);
                Long feeSuccess = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_txn_success")), 0);
                monthlyFeeReport.put("feeSuccess", feeSuccess);
                Long feeFailed = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_txn_failed")), 0);
                monthlyFeeReport.put("feeFailed", feeFailed);
                Long feeIta = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_ita")), 0);
                monthlyFeeReport.put("feeIta", feeIta);
                Long feeEcom = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_ecom")), 0);
                monthlyFeeReport.put("feeEcom",feeEcom);
                Long totalFee = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_inc_vat")), 0);
                monthlyFeeReport.put("totalFee", totalFee);
                Long totalVAT = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_vat")), 0);
                monthlyFeeReport.put("totalVAT", totalVAT);
                Long advanceAmount = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_advance")), 0);
                monthlyFeeReport.put("advanceAmount", advanceAmount);
                Long discountFee = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("fee_discount")), 0);
                monthlyFeeReport.put("discountFee", discountFee);
                Long totalFeeCollected = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_collected")), 0);
                monthlyFeeReport.put("totalFeeCollected", totalFeeCollected);
                Long totalFeeReceivable = Convert.parseLong(filterStringNullPointer(jAdvance.getValue("total_fee_receivable")), 0);
                monthlyFeeReport.put("totalFeeReceivable", totalFeeReceivable);

                //process J_FROM,J_TO time
                SimpleDateFormat sdfHHMMSSMMDDYYYY = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy");
                JsonObject jFrom = new JsonObject(rs.getString("J_FROM") == null ? "{}" : rs.getString("J_FROM"));
                JsonObject jTo = new JsonObject(rs.getString("J_TO") == null ? "{}" : rs.getString("J_TO"));
                //QT
                java.util.Date fromDateQT = null;
                java.util.Date toDateQT = null;
                //ND
                java.util.Date fromDateND = null;
                java.util.Date toDateND = null;
                //DD
                java.util.Date fromDateDD = null;
                java.util.Date toDateDD = null;
                //QR
                java.util.Date fromDateQR = null;
                java.util.Date toDateQR = null;
                //SMS
                java.util.Date fromDateSMS = null;
                java.util.Date toDateSMS = null;
                //BILING
                java.util.Date fromDateBL = null;
                java.util.Date toDateBL = null;
                //BNPL
                java.util.Date fromDateBNPL = null;
                java.util.Date toDateBNPL = null;
                //UPOS
                java.util.Date fromDateUPOS = null;
                java.util.Date toDateUPOS = null;
                //PAYCOLLECT
                java.util.Date fromDatePAYCOLLECT = null;
                java.util.Date toDatePAYCOLLECT = null;
                if (jFrom != null && !jFrom.equals("{}")) {
                    if (jFrom.containsKey("qt")) {
                        fromDateQT = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qt"));
                    }
                    if (jFrom.containsKey("nd")) {
                        fromDateND = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("nd"));
                    }
                    if (jFrom.containsKey("dd")) {
                        fromDateDD = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("dd"));
                    }
                    if (jFrom.containsKey("qr")) {
                        fromDateQR = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qr"));
                    }
                    if (jFrom.containsKey("sms")) {
                        fromDateSMS = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("sms"));
                    }
                    if (jFrom.containsKey("bl")) {
                        fromDateBL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bl"));
                    }
                    if (jFrom.containsKey("bnpl")) {
                        fromDateBNPL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bnpl"));
                    }
                    if (jFrom.containsKey("upos")) {
                        fromDateUPOS = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("upos"));
                    }
                    if (jFrom.containsKey("pc")) {
                        fromDatePAYCOLLECT = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("pc"));
                    }
                }
                if (jTo != null &&!jTo.equals("{}")) {
                    if (jTo.containsKey("qt")) {
                        toDateQT = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qt"));
                    }
                    if (jTo.containsKey("nd")) {
                        toDateND = sdfHHMMSSMMDDYYYY.parse(jTo.getString("nd"));
                    }
                    if (jTo.containsKey("dd")) {
                        toDateDD = sdfHHMMSSMMDDYYYY.parse(jTo.getString("dd"));
                    }
                    if (jTo.containsKey("qr")) {
                        toDateQR = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qr"));
                    }
                    if (jTo.containsKey("sms")) {
                        toDateSMS = sdfHHMMSSMMDDYYYY.parse(jTo.getString("sms"));
                    }
                    if (jTo.containsKey("bl")) {
                        toDateBL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bl"));
                    }
                    if (jTo.containsKey("bnpl")) {
                        toDateBNPL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bnpl"));
                    }
                    if (jTo.containsKey("upos")) {
                        toDateUPOS = sdfHHMMSSMMDDYYYY.parse(jTo.getString("upos"));
                    }
                    if (jTo.containsKey("pc")) {
                        toDatePAYCOLLECT = sdfHHMMSSMMDDYYYY.parse(jTo.getString("pc"));
                    }
                }
                monthlyFeeReport.put("fromDateQTTime",fromDateQT == null ? 0 : fromDateQT.getTime());
                monthlyFeeReport.put("toDateQTTime",toDateQT == null ? 0 : toDateQT.getTime());

                monthlyFeeReport.put("fromDateNDTime",fromDateND == null ? 0 : fromDateND.getTime());
                monthlyFeeReport.put("toDateNDTime",toDateND == null ? 0 : toDateND.getTime());

                monthlyFeeReport.put("fromDateDDTime",fromDateDD == null ? 0 : fromDateDD.getTime());
                monthlyFeeReport.put("toDateDDTime",toDateDD == null ? 0 : toDateDD.getTime());

                monthlyFeeReport.put("fromDateQRTime",fromDateQR == null ? 0 : fromDateQR.getTime());
                monthlyFeeReport.put("toDateQRTime", toDateQR == null ? 0 : toDateQR.getTime());

                monthlyFeeReport.put("fromDateSMSTime", fromDateSMS == null ? 0 : fromDateSMS.getTime());
                monthlyFeeReport.put("toDateSMSTime", toDateSMS == null ? 0 : toDateSMS.getTime());

                monthlyFeeReport.put("fromDateBLTime", fromDateBL == null ? 0 : fromDateBL.getTime());
                monthlyFeeReport.put("toDateBLTime", toDateBL == null ? 0 : toDateBL.getTime());

                monthlyFeeReport.put("fromDateBNPLTime", fromDateBNPL == null ? 0 : fromDateBNPL.getTime());
                monthlyFeeReport.put("toDateBNPLTime", toDateBNPL == null ? 0 : toDateBNPL.getTime());

                monthlyFeeReport.put("fromDateUPOSTime", fromDateUPOS == null ? 0 : fromDateUPOS.getTime());
                monthlyFeeReport.put("toDateUPOSTime", toDateUPOS == null ? 0 : toDateUPOS.getTime());

                monthlyFeeReport.put("fromDatePAYCOLLECTTime", fromDatePAYCOLLECT == null ? 0 : fromDatePAYCOLLECT.getTime());
                monthlyFeeReport.put("toDatePAYCOLLECTTime", toDatePAYCOLLECT == null ? 0 : toDatePAYCOLLECT.getTime());

                monthlyFeeReport.put("fromDateTime", rs.getDate("D_FROM") == null ? null : rs.getDate("D_FROM").getTime());
                monthlyFeeReport.put("toDateTime", rs.getDate("D_TO") == null ? null : rs.getDate("D_TO").getTime());
                monthlyFeeReport.put("toDateTimeView", rs.getDate("D_TO")  == null ? null : rs.getDate("D_TO").getTime()-1000);
                result.add(monthlyFeeReport);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> isExistMonthlyFeeReport(String reportIds) throws Exception {
        Exception exception = null;
        Map<String, Object> result = new HashMap<>(); 
        Connection con = null;
        CallableStatement cs = null;
        Integer nOutPut = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(COUNT_MONTHLY_FEE_REPORT_BY_ID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setString(2, reportIds);
            cs.execute();

            nOutPut = cs.getInt(1);
            result.put("isExist", nOutPut == 0 ? false : true);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static String filterStringNullPointer(Object object) {
        return object == null ? "0" : object.toString();
    }

    public static Map<String, Object> getPartners(String name) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();

        List<PartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_PARTNERS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.setString(2, name);
            cs.execute();

            rs = (ResultSet) cs.getObject(1);
            while(rs!=null && rs.next()) {
                PartnerDto partnerDto = new PartnerDto();
                partnerDto.setPartnerId(rs.getInt("N_ID"));
                partnerDto.setShortName(rs.getString("S_SHORT_NAME"));
                partnerDto.setFullName(rs.getString("S_PARTNER_NAME"));
                result.add(partnerDto);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("data", result);
        return dataList;
    }

    public static Map<String, Object> getMerchants(String merchantId) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();

        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_MERCHANTS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.setString(2, merchantId);
            cs.execute();

            rs = (ResultSet) cs.getObject(1);
            while(rs!=null && rs.next()) {
                Map<String, Object> merchant = new HashMap();
                merchant.put("merchantId", rs.getString("S_MERCHANT_ID"));
                result.add(merchant);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("data", result);
        return dataList;
    }

    public static Integer getTotalRowMonthlyFeeReport(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;

        ResultSet rs = null;
        Integer nTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_TOTAL_ROW_MONTH_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setString(2, (String) mIn.get(MFR_PARTNER_IDS));
            cs.setString(3, (String) mIn.get(MFR_MERCHANT_IDS));
            cs.setString(4, (String) mIn.get(MFR_TAX_CODE));
            cs.setString(5, (String) mIn.get(MFR_FROM_DATE));
            cs.setString(6, (String) mIn.get(MFR_TO_DATE));
            cs.setInt(7, mIn.get(MFR_PAGE_ACTIVE) == null ? 0 : (Integer) mIn.get(MFR_PAGE_ACTIVE));
            cs.setInt(8, mIn.get(MFR_PAGE_SIZE) == null ? Integer.MAX_VALUE : (Integer) mIn.get(MFR_PAGE_SIZE));
            cs.setString(9, (String) mIn.get(MFR_TYPE_ADVANCES));
            cs.setString(10, (String) mIn.get(MFR_TIME_ADVANCES));
            cs.setString(11, (String) mIn.get(MFR_PAY_CHANNELS));
            cs.setString(12, (String) mIn.get(MFR_RECEIPT_TYPES));
            cs.setString(13, (String) mIn.get(MFR_RECEIPT_STATE));
            cs.setString(14, (String) mIn.get(MFR_NOTIFY_STATE));
            cs.setString(15, (String) mIn.get(MFR_CONTROL_MINUTES));
            cs.execute();

            nTotal = cs.getInt(1);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nTotal;
    }

    public static List<Map<String, Object>> processPeriodsDownloadAdvance(JsonObject monthlyFeeReport) {
        try {       
            //process header period
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm dd/MM/yyyy");
            List<Map<String, Object>> periods = new ArrayList<>();
            Long fromDateQTTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_QT_TIME); //QT
            Long toDateQTTime =  monthlyFeeReport.getLong(MFR_TO_DATE_QT_TIME);
            String fromDateQT = (fromDateQTTime == 0 || fromDateQTTime == null) ? "" : sdf.format(new Date(fromDateQTTime));
            String toDateQT = (toDateQTTime == 0 || toDateQTTime == null) ? "" : sdf.format(new Date(toDateQTTime-1000));
            
            Long fromDateNDTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_ND_TIME); //ND
            Long toDateNDTime =  monthlyFeeReport.getLong(MFR_TO_DATE_ND_TIME);
            String fromDateND = (fromDateNDTime == 0 || fromDateNDTime == null) ? "" : sdf.format(new Date(fromDateNDTime));
            String toDateND = (toDateNDTime == 0 || toDateNDTime == null) ? "" : sdf.format(new Date(toDateNDTime-1000));

            Long fromDateDDTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_DD_TIME); //DD
            Long toDateDDTime =  monthlyFeeReport.getLong(MFR_TO_DATE_DD_TIME);
            String fromDateDD = (fromDateDDTime == 0 || fromDateDDTime == null) ? "" : sdf.format(new Date(fromDateDDTime));
            String toDateDD = (toDateDDTime == 0 || toDateDDTime == null) ? "" : sdf.format(new Date(toDateDDTime-1000));

            Long fromDateQRTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_QR_TIME); //QR
            Long toDateQRTime =  monthlyFeeReport.getLong(MFR_TO_DATE_QR_TIME);
            String fromDateQR = (fromDateQRTime == 0 || fromDateQRTime == null) ? "" : sdf.format(new Date(fromDateQRTime));
            String toDateQR = (toDateQRTime == 0 || toDateQRTime == null) ? "" : sdf.format(new Date(toDateQRTime-1000));

            Long fromDateSMSTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_SMS_TIME); //SMS
            Long toDateSMSTime =  monthlyFeeReport.getLong(MFR_TO_DATE_SMS_TIME);
            String fromDateSMS = (fromDateSMSTime == 0 || fromDateSMSTime == null) ? "" : sdf.format(new Date(fromDateSMSTime));
            String toDateSMS = (toDateSMSTime == 0 || toDateSMSTime == null) ? "" : sdf.format(new Date(toDateSMSTime-1000));

            Long fromDateBLTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_BL_TIME); //BL
            Long toDateBLTime =  monthlyFeeReport.getLong(MFR_TO_DATE_BL_TIME);
            String fromDateBL = (fromDateBLTime == 0 || fromDateBLTime == null) ? "" : sdf.format(new Date(fromDateBLTime));
            String toDateBL = (toDateBLTime == 0 || toDateBLTime == null) ? "" : sdf.format(new Date(toDateBLTime-1000));

            Long fromDateBNPLTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_BNPL_TIME); //BNPL
            Long toDateBNPLTime =  monthlyFeeReport.getLong(MFR_TO_DATE_BNPL_TIME);
            String fromDateBNPL = (fromDateBNPLTime == 0 || fromDateBNPLTime == null) ? "" : sdf.format(new Date(fromDateBNPLTime));
            String toDateBNPL = (toDateBNPLTime == 0 || toDateBNPLTime == null) ? "" : sdf.format(new Date(toDateBNPLTime-1000));

            Long fromDateUPOSTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_UPOS_TIME); //UPOS
            Long toDateUPOSTime =  monthlyFeeReport.getLong(MFR_TO_DATE_UPOS_TIME);
            String fromDateUPOS = (fromDateUPOSTime == 0 || fromDateUPOSTime == null) ? "" : sdf.format(new Date(fromDateUPOSTime));
            String toDateUPOS = (toDateUPOSTime == 0 || toDateUPOSTime == null) ? "" : sdf.format(new Date(toDateUPOSTime-1000));

            Long fromDatePAYCOLLECTTime =  monthlyFeeReport.getLong(MFR_FROM_DATE_PAYCOLLECT_TIME); //PAYCOLLECT
            Long toDatePAYCOLLECTTime =  monthlyFeeReport.getLong(MFR_TO_DATE_PAYCOLLECT_TIME);
            String fromDatePAYCOLLECT = (fromDatePAYCOLLECTTime == 0 || fromDatePAYCOLLECTTime == null) ? "" : sdf.format(new Date(fromDatePAYCOLLECTTime));
            String toDatePAYCOLLECT = (toDatePAYCOLLECTTime == 0 || toDatePAYCOLLECTTime == null) ? "" : sdf.format(new Date(toDatePAYCOLLECTTime-1000));

            if (!(fromDateQT.isBlank() && toDateQT.isBlank())) {
                Map<String, Object> periodQT = new HashMap<>();
                periodQT.put("S_PAY_CHANNEL", "Quốc tế/International Card:");
                periodQT.put("S_TIME", "From " + fromDateQT + " to " + toDateQT);
                periods.add(periodQT);
            }
            if (!(fromDateND.isBlank() && toDateND.isBlank())) {
                Map<String, Object> periodND = new HashMap<>();
                periodND.put("S_PAY_CHANNEL", "Nội địa/Domestic Card:");
                periodND.put("S_TIME", "From " + fromDateND + " to " + toDateND);
                periods.add(periodND);
            }
            if (!(fromDateDD.isBlank() && toDateDD.isBlank())) {
                Map<String, Object> periodDD = new HashMap<>();
                periodDD.put("S_PAY_CHANNEL", "Direct Debit:");
                periodDD.put("S_TIME", "From " + fromDateDD + " to " + toDateDD);
                periods.add(periodDD);
            }
            if (!(fromDateQR.isBlank() && toDateQR.isBlank())) {
                Map<String, Object> periodQR = new HashMap<>();
                periodQR.put("S_PAY_CHANNEL", "QR:");
                periodQR.put("S_TIME", "From " + fromDateQR + " to " + toDateQR);
                periods.add(periodQR);
            }
            if (!(fromDateSMS.isBlank() && toDateSMS.isBlank())) {
                Map<String, Object> periodSMS = new HashMap<>();
                periodSMS.put("S_PAY_CHANNEL", "SMS:");
                periodSMS.put("S_TIME", "From " + fromDateSMS + " to " + toDateSMS);
                periods.add(periodSMS);
            }
            if (!(fromDateBL.isBlank() && toDateBL.isBlank())) {
                Map<String, Object> periodBL = new HashMap<>();
                periodBL.put("S_PAY_CHANNEL", "Billing:");
                periodBL.put("S_TIME", "From " + fromDateBL + " to " + toDateBL);
                periods.add(periodBL);
            }
            if (!(fromDateBNPL.isBlank() && toDateBNPL.isBlank())) {
                Map<String, Object> periodBNPL = new HashMap<>();
                periodBNPL.put("S_PAY_CHANNEL", "BNPL/Buy now pay later:");
                periodBNPL.put("S_TIME", "From " + fromDateBNPL + " to " + toDateBNPL);
                periods.add(periodBNPL);
            }
            if (!(fromDateUPOS.isBlank() && toDateUPOS.isBlank())) {
                Map<String, Object> periodUPOS = new HashMap<>();
                periodUPOS.put("S_PAY_CHANNEL", "UPOS:");
                periodUPOS.put("S_TIME", "From " + fromDateUPOS + " to " + toDateUPOS);
                periods.add(periodUPOS);
            }
            if (!(fromDatePAYCOLLECT.isBlank() && toDatePAYCOLLECT.isBlank())) {
                Map<String, Object> periodPAYCOLLECT = new HashMap<>();
                periodPAYCOLLECT.put("S_PAY_CHANNEL", "PAYCOLLECT:");
                periodPAYCOLLECT.put("S_TIME", "From " + fromDatePAYCOLLECT + " to " + toDatePAYCOLLECT);
                periods.add(periodPAYCOLLECT);
            }
            return periods;
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "processPeriodsDownloadAdvance file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
    }

    public static Map getAdvanceMonthlyFeeReport(Map mIn, JsonObject monthlyFeeReport) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        List<MonthlyReportDetail> monthlyReportDetailsFrontEnd = new ArrayList<>();
        List<MonthlyReportDetail> monthlyReportDetailsFileExport = new ArrayList<>();
        Map results = new HashMap();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        Integer totalItems = 0;
        
        try {
            Integer reportId = (Integer) mIn.get(MFR_ID_MONTHLY_REPORT);
            String advanceStates = (String) mIn.get(MFR_ADVANCE_STATES);
            Integer pageActive = (Integer) mIn.get(MFR_PAGE_ACTIVE);
            Integer pageSize = (Integer) mIn.get(MFR_PAGE_SIZE);
            String queryMethod = (String) mIn.get(MFR_QUERY_METHOD);

            //Get monthly fee report information
            String businessRegisName = monthlyFeeReport.getString("businessRegisName");
            String partnerName = monthlyFeeReport.getString("partnerName");
            String payChannels = filterPayChannel(monthlyFeeReport.getString("payChannels"));
            String merchantIds = monthlyFeeReport.getString("merchantIds");
            String specialMerchantIds = ReportFileBuilder.filterSpecialMerchantIds(merchantIds);
            String contractCode = monthlyFeeReport.getString("contractCode");
            String contractDate = monthlyFeeReport.getLong("contractDateTime") == 0 ? "" : sdf.format(new Date(monthlyFeeReport.getLong("contractDateTime")));
            String taxCode = monthlyFeeReport.getString("taxCode");
            List<Map<String, Object>> periods = processPeriodsDownloadAdvance(monthlyFeeReport);

            conn = getConnectionOnefin118();
            cs = conn.prepareCall(LOAD_ADV_MONTH_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setInt(4, reportId);
            cs.setString(5, advanceStates);
            cs.setString(6, queryMethod);
            cs.setInt(7, pageSize == null ? Integer.MAX_VALUE : pageSize);
            cs.setInt(8, pageActive == null ? 0 : pageActive);
            cs.execute();

            int nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB get_monthly_report_detail error: " + sResult);
            rs = (ResultSet) cs.getObject(3);

            while (rs != null && rs.next())  {
                /******************************1. LIST NGOÀI MÀN HÌNH***************************************/
                monthlyReportDetailsFrontEnd.add(bindMonthlyReportDetails(rs));

                /******************************2. LIST DOWNLOAD EXCEL, PDF**********************************/
                // Truong hop merchant id dac biet, chi lay thong tin but toan goc, bo but toan chi nhanh
                // OP_VINHOMES
                String[] intersectionMerchantIds = ReportFileBuilder.intersectionMerchantIdsWithConfig(merchantIds);
                if (intersectionMerchantIds.length > 0) {
                    String[] merchantIdsAdvance = Util.getColumnString(rs, "S_MERCHANT_IDS").split(",");
                    String[] intersectionMerchantIdsAdvanceWithMonth = Arrays.stream(merchantIdsAdvance)
                                                                        .distinct()
                                                                        .filter(x -> Arrays.asList(intersectionMerchantIds).contains(x))
                                                                        .toArray(String[]::new);
                    if (intersectionMerchantIdsAdvanceWithMonth.length > 0) {
                        monthlyReportDetailsFileExport.add(bindMonthlyReportDetails(rs));
                    }
                } else {
                    monthlyReportDetailsFileExport.add(bindMonthlyReportDetails(rs));
                }
            }
            totalItems = getTotalItems(reportId);
            results.put(TOTAL_ITEMS, totalItems);
            results.put(DATA, monthlyReportDetailsFrontEnd);
            results.put(DATA_EXPORT, monthlyReportDetailsFileExport);

            //total detail
            Map totalDetail = new HashMap<>();
            totalDetail.put("totalChangeToVndSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalChangeToVnd).sum());
            totalDetail.put("totalUsdSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalUsd).sum());
            totalDetail.put("totalVndSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalVnd).sum());
            totalDetail.put("totalFeeSuccessSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFeeSuccess).sum());
            totalDetail.put("totalFeeItaSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFeeIta).sum());
            totalDetail.put("totalFeeFailedSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFeeFailed).sum());
            totalDetail.put("totalFeeSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFee).sum());
            totalDetail.put("totalFeeCollectedSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFeeCollected).sum());
            totalDetail.put("totalFeeReceivableSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalFeeReceivable).sum());
            totalDetail.put("totalAdvanceSum", monthlyReportDetailsFrontEnd.stream().mapToDouble(MonthlyReportDetail::getTotalAdvance).sum());
            results.put("totalDetail", totalDetail);

            //Excel
            Map header = new HashMap();
            Map fm = new HashMap();
            Map total = new HashMap();
            Map footer = new HashMap();

            //Pdf
            Map params = new HashMap();

            //fee month
            Map feeMonthData = getFeeMonth(reportId);
            double feeMonthAmount = (Double) feeMonthData.get("N_FEE_MONTH");
            double feeMonthCollected = (Double) feeMonthData.get("N_FEE_MONTH_COLLECTED");
            double feeMonthReceivable = (Double) feeMonthData.get("N_FEE_MONTH_RECEIVABLE");

            //total
            Integer totalTransSum = monthlyReportDetailsFileExport.stream().mapToInt(MonthlyReportDetail::getTotalTrans).sum();
            Integer totalTransSuccessSum = monthlyReportDetailsFileExport.stream().mapToInt(MonthlyReportDetail::getTotalTransSuccess).sum();
            Integer totalTransFailedSum = monthlyReportDetailsFileExport.stream().mapToInt(MonthlyReportDetail::getTotalTransFailed).sum();
            Double totalUsdSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalUsd).sum();
            Double totalVndSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalVnd).sum();
            Double totalOriginalAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalOriginalAmount).sum();
            Double totalMerchantDiscountAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalMerchantDiscountAmount).sum();
            Double totalPartnerDiscountAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalPartnerDiscountAmount).sum();
            Double totalChangeToVndSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalChangeToVnd).sum();
            Double totalAdvanceSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalAdvance).sum(); 
            Double totalAdvanceP2Sum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalAdvanceP2).sum();
            Double totalGuaranteeAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalGuaranteeAmount).sum();
            Double totalFeeSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalFee).sum() + feeMonthAmount;
            Double totalPaylaterAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalPaylaterAmount).sum();
            Double totalDisbursedAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalDisbursedAmount).sum();
            Double totalFeeCollectedSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalFeeCollected).sum() + feeMonthCollected;
            Double totalFeeReceivableSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalFeeReceivable).sum() + feeMonthReceivable;
            Double totalRefundHoldAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalRefundHoldAmount).sum();
            Double totalRefundUnHoldAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalRefundUnholdAmount).sum();
            Double totalPayoutAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalPayoutAmount).sum();
            Double totalBankTransferAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalBankTransferAmount).sum();
            Double totalTopupRefundAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalTopupRefundAmount).sum();
            Double totalTotalAdjustAdvAmountSum = monthlyReportDetailsFileExport.stream().mapToDouble(MonthlyReportDetail::getTotalTotalAdjustAdvAmount).sum();

            char[] totalFeeSumTextChar = Commons.NumberToVND.getVND(Math.round(totalFeeSum)).concat(".").toLowerCase().toCharArray();
            totalFeeSumTextChar[0] = Character.toUpperCase(totalFeeSumTextChar[0]);
            String totalFeeSumText = String.valueOf(totalFeeSumTextChar);

            java.util.Date dateNow = new java.util.Date();
            String exportDate = "Ngày " + Convert.toString(dateNow, "dd") + 
                                " tháng " + Convert.toString(dateNow, "MM") + 
                                " năm " + Convert.toString(dateNow, "yyyy");

            if ("excel".equals(mIn.get("exportType"))) {
                //header
                header.put("L_PERIODS", periods);
                header.put("S_CONTRACT_CODE", contractCode);
                header.put("S_CONTRACT_DATE", contractDate);
                header.put("S_PARTNER_NAME", partnerName);
                header.put("S_BUSI_REGIS_NAME", businessRegisName);
                header.put("S_PAY_CHANNELS", payChannels);
                header.put("S_MERCHANT_IDS", specialMerchantIds);
                header.put("S_TAX_CODE", taxCode);

                //fee month
                fm.put("N_FEE_MONTH_AMOUNT", feeMonthAmount);
                fm.put("N_FEE_MONTH_COLLECTED", feeMonthCollected);
                fm.put("N_FEE_MONTH_RECEIVABLE", feeMonthReceivable);

                //total
                total.put("N_TOTAL_TRANS_SUM", totalTransSum);
                total.put("N_TOTAL_TRANS_SUCCESS_SUM", totalTransSuccessSum);
                total.put("N_TOTAL_TRANS_FAIL_SUM", totalTransFailedSum);
                total.put("N_TOTAL_USD_SUM", totalUsdSum);
                total.put("N_TOTAL_VND_SUM", totalVndSum);
                total.put("N_TOTAL_ORIGINAL_AMOUNT_SUM", totalOriginalAmountSum);
                total.put("N_TOTAL_MERCHANT_DISCOUNT_AMOUNT_SUM", totalMerchantDiscountAmountSum);
                total.put("N_TOTAL_PARTNER_DISCOUNT_AMOUNT_SUM", totalPartnerDiscountAmountSum);
                total.put("N_TOTAL_CHANGE_TO_VND_SUM", totalChangeToVndSum);
                total.put("N_TOTAL_ADVANCE_SUM", totalAdvanceSum);
                total.put("N_TOTAL_ADVANCE_P2_SUM", totalAdvanceP2Sum);
                total.put("N_GUARANTEE_AMOUNT_SUM", totalGuaranteeAmountSum);
                total.put("N_TOTAL_FEE_SUM", totalFeeSum);
                //bnpl
                total.put("N_TOTAL_PAYLATER_AMOUNT_SUM", totalPaylaterAmountSum);
                total.put("N_TOTAL_DISBURSED_AMOUNT_SUM", totalDisbursedAmountSum);
                //pay collect
                total.put("N_TOTAL_FEE_COLLECTED_SUM", totalFeeCollectedSum);
                total.put("N_TOTAL_FEE_RECEIVABLE_SUM", totalFeeReceivableSum);
                // refund hold
                total.put("N_TOTAL_REFUND_HOLD_AMOUNT_SUM", totalRefundHoldAmountSum);
                total.put("N_TOTAL_REFUND_UNHOLD_AMOUNT_SUM", totalRefundUnHoldAmountSum);
                //topup refund
                total.put("N_TOTAL_TOPUP_REFUND_AMOUNT_SUM", totalTopupRefundAmountSum);
                //adjust tong cuoi
                total.put("N_TOTAL_TOTAL_ADJUST_ADV_AMOUNT_SUM", totalTotalAdjustAdvAmountSum);
                //payout
                total.put("N_TOTAL_PAYOUT_AMOUNT_SUM", totalPayoutAmountSum);
                total.put("N_TOTAL_BANK_TRANSFER_AMOUNT_SUM", totalBankTransferAmountSum);
                
                //footer
                footer.put("N_TOTAL_FEE_SUM", totalFeeSum);
                footer.put("S_TOTAL_FEE_SUM_TEXT", totalFeeSumText);
                footer.put("S_EXPORT_DATE", exportDate);

                results.put("header", header);
                results.put("fm", fm);
                results.put("total", total);
                results.put("footer", footer);
            }

            if ("pdf".equals(mIn.get("exportType"))) {
                //header
                String periodsStr = periods.stream().map(item->(String)item.get("S_PAY_CHANNEL")+" "+(String)item.get("S_TIME")).collect(Collectors.joining(";\n"));
                params.put("L_PERIODS", Objects.requireNonNullElse(periodsStr, ""));
                params.put("S_PARTNER_NAME", Objects.requireNonNullElse(partnerName, ""));
                params.put("S_BUSI_REGIS_NAME", Objects.requireNonNullElse(businessRegisName, ""));
                params.put("S_PAY_CHANNELS", Objects.requireNonNullElse(payChannels, ""));
                params.put("S_MERCHANT_IDS", Objects.requireNonNullElse(specialMerchantIds, ""));                  
                params.put("S_CONTRACT_CODE", Objects.requireNonNullElse(contractCode, ""));                  
                params.put("S_CONTRACT_DATE", Objects.requireNonNullElse(contractDate,""));
                params.put("S_TAX_CODE", Objects.requireNonNullElse(taxCode, ""));

                //fee month
                params.put("N_FEE_MONTH_AMOUNT", Convert.toString(feeMonthAmount, "###,###"));
                params.put("N_FEE_MONTH_COLLECTED", Convert.toString(feeMonthCollected, "###,###"));
                params.put("N_FEE_MONTH_RECEIVABLE", Convert.toString(feeMonthReceivable, "###,###"));

                //total
                params.put("N_TOTAL_TRANS_SUM", Convert.toString(totalTransSum, "###,###"));
                params.put("N_TOTAL_TRANS_SUCCESS_SUM", Convert.toString(totalTransSuccessSum, "###,###"));
                params.put("N_TOTAL_TRANS_FAIL_SUM", Convert.toString(totalTransFailedSum, "###,###"));
                params.put("N_TOTAL_USD_SUM", Convert.toString(totalUsdSum, "###,###"));
                params.put("N_TOTAL_VND_SUM", Convert.toString(totalVndSum, "###,###"));
                params.put("N_TOTAL_ORIGINAL_AMOUNT_SUM", Convert.toString(totalOriginalAmountSum, "###,###"));
                params.put("N_TOTAL_MERCHANT_DISCOUNT_AMOUNT_SUM", Convert.toString(totalMerchantDiscountAmountSum, "###,###"));
                params.put("N_TOTAL_PARTNER_DISCOUNT_AMOUNT_SUM", Convert.toString(totalPartnerDiscountAmountSum, "###,###"));
                params.put("N_TOTAL_CHANGE_TO_VND_SUM", Convert.toString(totalChangeToVndSum, "###,###"));
                params.put("N_TOTAL_ADVANCE_SUM", Convert.toString(totalAdvanceSum, "###,###"));
                params.put("N_TOTAL_ADVANCE_P2_SUM", Convert.toString(totalAdvanceP2Sum, "###,###"));
                params.put("N_GUARANTEE_AMOUNT_SUM", Convert.toString(totalGuaranteeAmountSum, "###,###"));
                params.put("N_TOTAL_FEE_SUM", Convert.toString(totalFeeSum, "###,###"));
                //bnpl
                params.put("N_TOTAL_PAYLATER_AMOUNT_SUM", Convert.toString(totalPaylaterAmountSum, "###,###"));
                params.put("N_TOTAL_DISBURSED_AMOUNT_SUM", Convert.toString(totalDisbursedAmountSum, "###,###"));
                //pay collect
                params.put("N_TOTAL_FEE_COLLECTED_SUM", Convert.toString(totalFeeCollectedSum, "###,###"));
                params.put("N_TOTAL_FEE_RECEIVABLE_SUM", Convert.toString(totalFeeReceivableSum, "###,###"));
                // refund hold
                params.put("N_TOTAL_REFUND_HOLD_AMOUNT_SUM", Convert.toString(totalRefundHoldAmountSum, "###,###"));
                params.put("N_TOTAL_REFUND_UNHOLD_AMOUNT_SUM", Convert.toString(totalRefundUnHoldAmountSum, "###,###"));
                //topup refund
                params.put("N_TOTAL_TOPUP_REFUND_AMOUNT_SUM", Convert.toString(totalTopupRefundAmountSum, "###,###"));
                //adjust tong cuoi
                params.put("N_TOTAL_TOTAL_ADJUST_ADV_AMOUNT_SUM", Convert.toString(totalTotalAdjustAdvAmountSum, "###,###"));
                //payout
                params.put("N_TOTAL_PAYOUT_AMOUNT_SUM", Convert.toString(totalPayoutAmountSum, "###,###"));
                params.put("N_TOTAL_BANK_TRANSFER_AMOUNT_SUM", Convert.toString(totalBankTransferAmountSum, "###,###"));

                //footer
                params.put("S_TOTAL_FEE_SUM_TEXT", totalFeeSumText);
                params.put("S_EXPORT_DATE", exportDate);

                results.put("params", params);
            }
        } catch (Exception ex) {
            exception = ex;
            logger.log(Level.FINE, "Payment2DAO - downloadMonthlyFee error: " + ex);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return results;
    }

    public static String filterPayChannel(String payChannel) {
        switch (payChannel) {
            case "PC":
                return "Paycollect";
            case "DD":
                return "Direct Debit";
            default:
                return payChannel;
        }
    }

    public static MonthlyReportDetail bindMonthlyReportDetails(ResultSet rs) throws SQLException, ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        MonthlyReportDetail monthlyReportDetail = new MonthlyReportDetail();
        monthlyReportDetail.setIdMonthlyFee(Util.getColumnLong(rs, "N_ID_MONTHLY_REPORT"));
        monthlyReportDetail.setIdMonthlyFeeDetail(Util.getColumnLong(rs, "N_ID"));
        monthlyReportDetail.setTemplate(Util.getColumnString(rs, "S_TEMPLATE_NAME"));
        monthlyReportDetail.setIdConfig(Util.getColumnInteger(rs, "N_ID_CONFIG"));
        monthlyReportDetail.setContractCode(Util.getColumnString(rs, "S_CONTRACT_CODE"));
        monthlyReportDetail.setContractSignDate(Util.getColumnTimeStamp(rs, "D_CONTRACT_SIGN_DATE"));
        monthlyReportDetail.setPartnerName(Util.getColumnString(rs, "S_PARTNER_FULL_NAME"));
        monthlyReportDetail.setPayChannels(filterPayChannel(Util.getColumnString(rs, "S_PAY_CHANNELS")));
        monthlyReportDetail.setMerchantIds(Util.getColumnString(rs, "S_MERCHANT_IDS"));
        monthlyReportDetail.setPaymentVoucher(Util.getColumnString(rs, "S_PAYMENT_VOUCHER"));
        if (Util.getColumnTimeStamp(rs, "D_ADVANCE") != null)
            monthlyReportDetail.setAdvDate(sdf.format(new Date(Util.getColumnTimeStamp(rs, "D_ADVANCE").getTime())));
        monthlyReportDetail.setFromDate(Util.getColumnTimeStamp(rs, "D_FROM"));
        monthlyReportDetail.setToDate(Util.getColumnTimeStamp(rs, "D_TO"));
        monthlyReportDetail.setAdvanceState(Util.getColumnString(rs, "S_STATE"));
        monthlyReportDetail.setTypeAdvanceAmount(Util.getColumnString(rs, "S_ADVANCE_AMOUNT"));
        monthlyReportDetail.setTaxCode(Util.getColumnString(rs, "S_TAX_CODE"));

        //PROCESS J_FROM, J_TO
        SimpleDateFormat sdfHHMMSSMMDDYYYY = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy");
                    
        JsonObject jFrom = new JsonObject(rs.getString("J_FROM") == null ? "{}" : rs.getString("J_FROM"));
        JsonObject jTo = new JsonObject(rs.getString("J_TO") == null ? "{}" : rs.getString("J_TO"));

        //QT
        java.util.Date fromDateQT = null;
        java.util.Date toDateQT = null;
        //ND
        java.util.Date fromDateND = null;
        java.util.Date toDateND = null;
        //DD
        java.util.Date fromDateDD = null;
        java.util.Date toDateDD = null;
        //QR
        java.util.Date fromDateQR = null;
        java.util.Date toDateQR = null;
        //SMS
        java.util.Date fromDateSMS = null;
        java.util.Date toDateSMS = null;
        //BILING
        java.util.Date fromDateBL = null;
        java.util.Date toDateBL = null;
        //BNPL
        java.util.Date fromDateBNPL = null;
        java.util.Date toDateBNPL = null;
        //UPOS
        java.util.Date fromDateUPOS = null;
        java.util.Date toDateUPOS = null;

        if (!jFrom.equals("{}")) {
            if (jFrom.containsKey("qt")) {
                fromDateQT = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qt"));
            }
            if (jFrom.containsKey("nd")) {
                fromDateND = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("nd"));
            }
            if (jFrom.containsKey("dd")) {
                fromDateDD = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("dd"));
            }
            if (jFrom.containsKey("qr")) {
                fromDateQR = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("qr"));
            }
            if (jFrom.containsKey("sms")) {
                fromDateSMS = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("sms"));
            }
            if (jFrom.containsKey("bl")) {
                fromDateBL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bl"));
            }
            if (jFrom.containsKey("bnpl")) {
                fromDateBNPL = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("bnpl"));
            }
            if (jFrom.containsKey("upos")) {
                fromDateUPOS = sdfHHMMSSMMDDYYYY.parse(jFrom.getString("upos"));
            }
        }
        if (!jTo.equals("{}")) {
            if (jTo.containsKey("qt")) {
                toDateQT = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qt"));
            }
            if (jTo.containsKey("nd")) {
                toDateND = sdfHHMMSSMMDDYYYY.parse(jTo.getString("nd"));
            }
            if (jTo.containsKey("dd")) {
                toDateDD = sdfHHMMSSMMDDYYYY.parse(jTo.getString("dd"));
            }
            if (jTo.containsKey("qr")) {
                toDateQR = sdfHHMMSSMMDDYYYY.parse(jTo.getString("qr"));
            }
            if (jTo.containsKey("sms")) {
                toDateSMS = sdfHHMMSSMMDDYYYY.parse(jTo.getString("sms"));
            }
            if (jTo.containsKey("bl")) {
                toDateBL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bl"));
            }
            if (jTo.containsKey("bnpl")) {
                toDateBNPL = sdfHHMMSSMMDDYYYY.parse(jTo.getString("bnpl"));
            }
            if (jTo.containsKey("upos")) {
                toDateUPOS = sdfHHMMSSMMDDYYYY.parse(jTo.getString("upos"));
            }
        }
        monthlyReportDetail.setFromDateQT(fromDateQT);
        monthlyReportDetail.setFromDateQTTime(fromDateQT == null ? 0 : fromDateQT.getTime());
        monthlyReportDetail.setFromDateND(fromDateND);
        monthlyReportDetail.setFromDateNDTime(fromDateND == null ? 0 : fromDateND.getTime());
        monthlyReportDetail.setFromDateDD(fromDateDD);
        monthlyReportDetail.setFromDateDDTime(fromDateDD == null ? 0 : fromDateDD.getTime());
        monthlyReportDetail.setFromDateQR(fromDateQR);
        monthlyReportDetail.setFromDateQRTime(fromDateQR == null ? 0 : fromDateQR.getTime());
        monthlyReportDetail.setFromDateSMS(fromDateSMS);
        monthlyReportDetail.setFromDateSMSTime(fromDateSMS == null ? 0 : fromDateSMS.getTime());
        monthlyReportDetail.setFromDateBL(fromDateBL);
        monthlyReportDetail.setFromDateBLTime(fromDateBL == null ? 0 : fromDateBL.getTime());
        monthlyReportDetail.setFromDateBNPL(fromDateBNPL);
        monthlyReportDetail.setFromDateBNPLTime(fromDateBNPL == null ? 0 : fromDateBNPL.getTime());
        monthlyReportDetail.setFromDateUPOS(fromDateUPOS);
        monthlyReportDetail.setFromDateUPOSTime(fromDateUPOS == null ? 0 : fromDateUPOS.getTime());

        monthlyReportDetail.setToDateQT(toDateQT); 
        monthlyReportDetail.setToDateQTTime(toDateQT == null ? 0 : toDateQT.getTime());
        monthlyReportDetail.setToDateND(toDateND);
        monthlyReportDetail.setToDateNDTime(toDateND == null ? 0 : toDateND.getTime());
        monthlyReportDetail.setToDateDD(toDateDD);
        monthlyReportDetail.setToDateDDTime(toDateDD == null ? 0 : toDateDD.getTime());
        monthlyReportDetail.setToDateQR(toDateQR);
        monthlyReportDetail.setToDateQRTime(toDateQR == null ? 0 : toDateQR.getTime());
        monthlyReportDetail.setToDateSMS(toDateSMS);
        monthlyReportDetail.setToDateSMSTime(toDateSMS == null ? 0 : toDateSMS.getTime());
        monthlyReportDetail.setToDateBL(toDateBL);
        monthlyReportDetail.setToDateBLTime(toDateBL == null ? 0 : toDateBL.getTime());
        monthlyReportDetail.setToDateBNPL(toDateBNPL);
        monthlyReportDetail.setToDateBNPLTime(toDateBNPL == null ? 0 : toDateBNPL.getTime());
        monthlyReportDetail.setToDateUPOS(toDateUPOS);
        monthlyReportDetail.setToDateUPOSTime(toDateUPOS == null ? 0 : toDateUPOS.getTime());
        monthlyReportDetail.setPeriods(processPeriodDetail(monthlyReportDetail));

        //PROCESS J_ADVANCE
        JsonObject jAdvance = new JsonObject(rs.getString("J_ADVANCE") == null ? "{}" : rs.getString("J_ADVANCE"));
        Integer totalTrans = 0;
        Integer totalTransSuccess = 0;
        Integer totalTransFailed = 0;
        Double totalFee = 0.0;
        Double totalFeeIta = 0.0;
        Double totalFeeEcom = 0.0;
        Double totalFeeFailed = 0.0;
        Double totalFeeSuccess = 0.0;
        Double totalFeeCollected = 0.0;
        Double totalFeeReceivable = 0.0;
        Double totalAdvance = 0.0;
        Double totalChangeToVnd = 0.0;
        Double totalUsd = 0.0;
        Double totalVnd = 0.0;
        Double totalOriginalAmount = 0.0;
        Double totalMerchantDiscountAmount = 0.0;
        Double totalPartnerDiscountAmount = 0.0;
        Double totalAdvanceP2 = 0.0;
        Double totalGuaranteeAmount = 0.0;
        Double totalPaylaterAmount = 0.0;
        Double totalDisbursedAmount = 0.0;
        Double totalRefundHoldAmount = 0.0;
        Double totalRefundUnholdAmount = 0.0;
        Double totalPayoutAmount = 0.0;
        Double totalBankTransferAmount = 0.0;
        Double totalTopupRefundAmount = 0.0;
        Double totalTotalAdjustAdvAmount = 0.0;
        if (jAdvance != null && !jAdvance.isEmpty()) {
            totalTrans = jAdvance.getInteger("total_txn") == null ? 0 : jAdvance.getInteger("total_txn");
            totalTransSuccess = jAdvance.getInteger("total_txn_success") == null ? 0 : jAdvance.getInteger("total_txn_success");
            totalTransFailed = jAdvance.getInteger("total_txn_failed") == null ? 0 : jAdvance.getInteger("total_txn_failed");
            totalFee = jAdvance.getDouble("total_fee_inc_vat") == null ? 0.0 : jAdvance.getDouble("total_fee_inc_vat");
            totalFeeIta = jAdvance.getDouble("total_fee_ita") == null ? 0.0 : jAdvance.getDouble("total_fee_ita");
            totalFeeEcom = jAdvance.getDouble("total_fee_ecom") == null ? 0.0 : jAdvance.getDouble("total_fee_ecom");
            totalFeeFailed = jAdvance.getDouble("total_fee_txn_failed") == null ? 0.0 : jAdvance.getDouble("total_fee_txn_failed");
            totalFeeSuccess = jAdvance.getDouble("total_fee_txn_success") == null ? 0.0 : jAdvance.getDouble("total_fee_txn_success");
            totalFeeCollected = jAdvance.getDouble("total_fee_collected") == null ? 0.0 : jAdvance.getDouble("total_fee_collected");
            totalFeeReceivable = jAdvance.getDouble("total_fee_receivable") == null ? 0.0 : jAdvance.getDouble("total_fee_receivable");
            totalAdvance = jAdvance.getDouble("total_advance") == null ? 0.0 : jAdvance.getDouble("total_advance");
            totalChangeToVnd = jAdvance.getDouble("total_change_to_vnd") == null ? 0.0 : jAdvance.getDouble("total_change_to_vnd");
            totalUsd = jAdvance.getDouble("total_usd") == null ? 0.0 : jAdvance.getDouble("total_usd");
            totalVnd = jAdvance.getDouble("total_vnd") == null ? 0.0 : jAdvance.getDouble("total_vnd");
            totalOriginalAmount = jAdvance.getDouble("total_original_amount") == null ? 0.0 : jAdvance.getDouble("total_original_amount");
            totalMerchantDiscountAmount = jAdvance.getDouble("total_merchant_discount_amount") == null ? 0.0 : jAdvance.getDouble("total_merchant_discount_amount");
            totalPartnerDiscountAmount = jAdvance.getDouble("total_partner_discount_amount") == null ? 0.0 : jAdvance.getDouble("total_partner_discount_amount");
            totalAdvanceP2 = jAdvance.getDouble("total_advance_p2") == null ? 0.0 : jAdvance.getDouble("total_advance_p2");
            totalGuaranteeAmount = jAdvance.getDouble("total_guarantee") == null ? 0.0 : jAdvance.getDouble("total_guarantee");
            totalPaylaterAmount = jAdvance.getDouble("total_installment_amount") == null ? 0.0 : jAdvance.getDouble("total_installment_amount");
            totalDisbursedAmount = jAdvance.getDouble("total_change_to_vnd") == null ? 0.0 : jAdvance.getDouble("total_change_to_vnd");
            totalRefundHoldAmount = jAdvance.getDouble("total_refund_hold_amount", 0.0);
            totalRefundUnholdAmount = jAdvance.getDouble("total_refund_unhold_amount", 0.0);
            totalPayoutAmount = jAdvance.getDouble("total_payout_amount", 0.0);
            totalBankTransferAmount = jAdvance.getDouble("total_bank_transfer_amount", 0.0);
            totalTopupRefundAmount = jAdvance.getDouble("total_topup_refund_amount", 0.0);
            totalTotalAdjustAdvAmount = jAdvance.getDouble("total_total_adjust_adv_amount", 0.0);
        }
        monthlyReportDetail.setTotalTrans(totalTrans);
        monthlyReportDetail.setTotalTransSuccess(totalTransSuccess);
        monthlyReportDetail.setTotalTransFailed(totalTransFailed);
        monthlyReportDetail.setTotalFee(totalFee);
        monthlyReportDetail.setTotalFeeIta(totalFeeIta);
        monthlyReportDetail.setTotalFeeEcom(totalFeeEcom);
        monthlyReportDetail.setTotalFeeSuccess(totalFeeSuccess);
        monthlyReportDetail.setTotalFeeFailed(totalFeeFailed);
        monthlyReportDetail.setTotalFeeCollected(totalFeeCollected);
        monthlyReportDetail.setTotalFeeReceivable(totalFeeReceivable);
        monthlyReportDetail.setTotalAdvance(totalAdvance);
        monthlyReportDetail.setTotalChangeToVnd(totalChangeToVnd);
        monthlyReportDetail.setTotalUsd(totalUsd);
        monthlyReportDetail.setTotalVnd(totalVnd);
        monthlyReportDetail.setTotalOriginalAmount(totalOriginalAmount);
        monthlyReportDetail.setTotalMerchantDiscountAmount(totalMerchantDiscountAmount);
        monthlyReportDetail.setTotalPartnerDiscountAmount(totalPartnerDiscountAmount);
        monthlyReportDetail.setTotalAdvanceP2(totalAdvanceP2);
        monthlyReportDetail.setTotalGuaranteeAmount(totalGuaranteeAmount);
        monthlyReportDetail.setTotalPaylaterAmount(totalPaylaterAmount);
        monthlyReportDetail.setTotalDisbursedAmount(totalDisbursedAmount);
        monthlyReportDetail.setTotalRefundHoldAmount(totalRefundHoldAmount);
        monthlyReportDetail.setTotalRefundUnholdAmount(totalRefundUnholdAmount);
        monthlyReportDetail.setTotalPayoutAmount(totalPayoutAmount);
        monthlyReportDetail.setTotalBankTransferAmount(totalBankTransferAmount);
        monthlyReportDetail.setTotalTopupRefundAmount(totalTopupRefundAmount);
        monthlyReportDetail.setTotalTotalAdjustAdvAmount(totalTotalAdjustAdvAmount);
        return monthlyReportDetail;
    }

    //CHI TIET THEO TUNG PAY CHANNEL
    public static String processPeriodDetail(MonthlyReportDetail item) { 
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss dd/MM/yyyy");
        //process s_date
        StringBuilder periodSummaryBuilder = new StringBuilder();
        String periodSummary = "";
        String fromDateQT = item.getFromDateQTTime() == 0 ? "" : sdf.format(item.getFromDateQTTime());
        String toDateQT = item.getToDateQTTime() == 0 ? "" : sdf.format(item.getToDateQTTime()-1000);
        String periodQT = "QT:"+fromDateQT+" - "+toDateQT;

        String fromDateND = item.getFromDateNDTime() == 0 ? "" : sdf.format(item.getFromDateNDTime());
        String toDateND = item.getToDateNDTime() == 0 ? "" :sdf.format(item.getToDateNDTime()-1000);
        String periodND = "ND:"+fromDateND+" - "+toDateND;

        String fromDateDD = item.getFromDateDDTime() == 0 ? "" : sdf.format(item.getFromDateDDTime());
        String toDateDD = item.getToDateDDTime() == 0 ? "" :sdf.format(item.getToDateDDTime()-1000);
        String periodDD = "Direct Debit:"+fromDateDD+" - "+toDateDD;

        String fromDateQR = item.getFromDateQRTime() == 0 ? "" : sdf.format(item.getFromDateQRTime());
        String toDateQR = item.getToDateQRTime() == 0 ? "" : sdf.format(item.getToDateQRTime()-1000);
        String periodQR = "QR:"+fromDateQR+" - "+toDateQR;
        
        String fromDateSMS = item.getFromDateSMSTime() == 0 ? "" : sdf.format(item.getFromDateSMSTime());
        String toDateSMS = item.getToDateSMSTime() == 0 ? "" : sdf.format(item.getToDateSMSTime()-1000);
        String periodSMS = "SMS:"+fromDateSMS+" - "+toDateSMS;

        String fromDateBL = item.getFromDateBLTime() == 0 ? "" : sdf.format(item.getFromDateBLTime());
        String toDateBL = item.getToDateBLTime() == 0 ? "" : sdf.format(item.getToDateBLTime()-1000);
        String periodBL = "BL:"+fromDateBL+" - "+toDateBL;

        String fromDateBNPL = item.getFromDateBNPLTime() == 0 ? "" : sdf.format(item.getFromDateBNPLTime());
        String toDateBNPL = item.getToDateBNPLTime() == 0 ? "" : sdf.format(item.getToDateBNPLTime()-1000);
        String periodBNPL = "BNPL:"+fromDateBNPL+" - "+toDateBNPL;

        String fromDateUPOS = item.getFromDateUPOSTime() == 0 ? "" : sdf.format(item.getFromDateUPOSTime());
        String toDateUPOS = item.getToDateUPOSTime() == 0 ? "" : sdf.format(item.getToDateUPOSTime()-1000);
        String periodUPOS = "UPOS:"+fromDateUPOS+" - "+toDateUPOS;

        if (!(isEmptyString(fromDateQT) && isEmptyString(toDateQT))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodQT);
            } else {
                periodSummaryBuilder.append(periodQT);
            }
        } 
        if (!(isEmptyString(fromDateND) && isEmptyString(toDateND))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodND);
            } else {
                periodSummaryBuilder.append(periodND);
            }
        }
        if (!(isEmptyString(fromDateDD) && isEmptyString(toDateDD))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodDD);
            } else {
                periodSummaryBuilder.append(periodDD);
            }
        }
        if (!(isEmptyString(fromDateQR) && isEmptyString(toDateQR))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodQR);
            } else {
                periodSummaryBuilder.append(periodQR);
            }
        }
        if (!(isEmptyString(fromDateSMS) && isEmptyString(toDateSMS))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodSMS);
            } else {
                periodSummaryBuilder.append(periodSMS);
            }
        }
        if (!(isEmptyString(fromDateBL) && isEmptyString(toDateBL))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodBL);
            } else {
                periodSummaryBuilder.append(periodBL);
            }
        }
        if (!(isEmptyString(fromDateBNPL) && isEmptyString(toDateBNPL))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodBNPL);
            } else {
                periodSummaryBuilder.append(periodBNPL);
            }
        }
        if (!(isEmptyString(fromDateUPOS) && isEmptyString(toDateUPOS))) {
            if (periodSummaryBuilder.length() != 0) {
                periodSummaryBuilder.append("\n").append(periodUPOS);
            } else {
                periodSummaryBuilder.append(periodUPOS);
            }
        }
        periodSummary = periodSummaryBuilder.toString();
        return periodSummary;
    }

    //TONG HOP
    public static String processPeriodDetailClone1(MonthlyReportDetail item) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy");
        StringBuilder periods = new StringBuilder();
        String fromDateString = item.getFromDate() == null ? "" : sdf.format(item.getFromDate());
        Date toDate = item.getToDate() == null ? null : new Date(item.getToDate().getTime()-1000); //minus 1 second
        String toDateString = toDate == null ? "" : sdf.format(toDate);
        periods.append(fromDateString).append("-").append(toDateString);
        return periods.toString();
    }   

    public static String processPeriodDetailClone2(MonthlyReportDetail item) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy HH:mm:ss");
        StringBuilder periods = new StringBuilder();
        String fromDateString = item.getFromDate() == null ? "" : sdf.format(item.getFromDate());
        Date toDate = item.getToDate() == null ? null : new Date(item.getToDate().getTime()-1000); //minus 1 second
        String toDateString = toDate == null ? "" : sdf.format(toDate);
        periods.append(fromDateString).append("-").append(toDateString);
        return periods.toString();
    }   

    public static boolean isEmptyString(String value) {
        if ("".equals(value) || value == null) {
            return true;
        }
        return false;
    }

    public static int getTotalItems(Integer reportId) throws Exception {
        int totalItems = 0;
        ResultSet rs = null;
        CallableStatement cs = null;
        Exception exception = null;
        Connection conn = null;
        try {
            conn = getConnectionOnefin118();
            cs = conn.prepareCall(LOAD_ADV_MONTH_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setInt(4, reportId);
            cs.setNull(5, OracleTypes.NULL);
            cs.setString(6, QUERY_TOTAL);
            cs.setNull(7, OracleTypes.NULL);
            cs.setNull(8, OracleTypes.NULL);

            cs.execute();

            int nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB get_monthly_report_detail error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null && rs.next())
                totalItems = Util.getColumnInteger(rs, N_TOTAL);
        } catch (Exception ex) {
            logger.log(Level.FINE, "Payment2DAO - getTotalItems error: " + ex.getMessage());
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return totalItems;
    }

    private static Double parseDouble(Double amount) {
        if (amount == null)
            return 0.0;
        return amount;
    }

    public static List<JsonObject> getAdvanceDetailMonthlyFeeReport(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        List<JsonObject> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;

        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(LOAD_ADV_DETAIL_MONTH_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Integer.parseInt(mIn.get(MFR_ID_MONTHLY_REPORT).toString()));
            cs.execute();
            sResult = cs.getString(3);
            nResult = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.LOAD_DETAIL_ADV_MONTH ERROR: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    JsonObject map = new JsonObject();
                    map.put("NO", rs.getLong("NO"));
                    map.put("PAYMENT_VOUCHER", rs.getString("S_PAYMENT_VOUCHER"));
                    map.put("MERCHANT_ID", rs.getString("S_MERCHANT_ID"));
                    map.put("ACQUIRER", rs.getString("S_ACQUIRER"));
                    map.put("TRANSACTION_ID", rs.getString("S_TRANSACTION_ID"));
                    map.put("TRANSACTION_DATE", rs.getString("S_DATE"));
                    map.put("CARD_NO", rs.getString("S_CARD_NO"));
                    map.put("CARD_TYPE", rs.getString("S_CARD_TYPE"));
                    map.put("STATE", rs.getString("S_STATE"));
                    map.put("ORDER_INFO", rs.getString("S_ORDER_INFO"));
                    map.put("TRANS_REF", rs.getString("S_TRANSACTION_REF"));
                    map.put("AUTH_CODE", rs.getString("S_AUTH_CODE"));
                    map.put("RRN", rs.getString("S_RRN"));
                    map.put("TRANSACTION_TYPE", rs.getString("S_TRANSACTION_TYPE"));
                    map.put("CURRENCY", rs.getString("S_CURRENCY"));
                    map.put("AMOUNT", rs.getDouble("N_AMOUNT"));
                    map.put("RESPONSE_CODE", rs.getString("S_RESPONSE_CODE"));
                    map.put("OPERATOR", rs.getString("S_OPERATOR"));
                    map.put("BIN_COUNTRY", rs.getString("S_BIN_COUNTRY"));
                    map.put("BIN_BANK", rs.getString("S_BIN_BANK"));
                    map.put("ITA_BANK", rs.getString("S_ITA_BANK"));
                    map.put("ITA_TERM", rs.getString("N_ITA_TERM"));
                    map.put("PAY_CHANNEL", rs.getString("S_PAY_CHANNEL"));
                    map.put("CARD_BIN", rs.getString("S_CARD_BIN"));
                    map.put("EXCHANGE_RATE", rs.getDouble("N_EXCHANGE_RATE"));
                    map.put("AMOUNT_AFTER", rs.getDouble("N_AMOUNT_AFTER"));
                    map.put("FEE_FIX", rs.getDouble("N_FEE_FIX"));
                    map.put("PERCENT_FEE", rs.getDouble("N_PERCENT_FEE"));
                    map.put("ITA_FEE", rs.getDouble("N_ITA_FEE"));
                    map.put("TOTAL_FEE", rs.getDouble("N_TOTAL_FEE"));
                    map.put("TOTAL_ITA_FEE", rs.getDouble("N_TOTAL_ITA_FEE"));
                    map.put("ADV_AMOUNT", rs.getDouble("N_AMOUNT_ADV"));
                    map.put("ECOM_FEE", rs.getDouble("N_ECOM_FEE"));
                    map.put("D_CHECK", rs.getString("D_CHECK"));
                    map.put("D_ADVANCE_FROM", rs.getDate("D_ADVANCE_FROM").getTime());
                    map.put("D_ADVANCE_TO", rs.getDate("D_ADVANCE_TO").getTime());
                    map.put("D_ADVANCE_TO_VIEW", rs.getDate("D_ADVANCE_TO").getTime()-1000);
                    map.put("TOTAL_FEE_COLLECTED", rs.getDouble("N_COLLECTED_FEE"));
                    map.put("TOTAL_FEE_RECEIVABLE", rs.getDouble("N_RECEIVABLE_FEE"));
                    map.put("S_DATA", rs.getString("S_DATA"));
                    map.put("S_CMF_ACTIVE", rs.getString("S_CMF_ACTIVE"));
                    map.put("N_CMF_AMOUNT", rs.getObject("N_CMF_AMOUNT") == null ? null : rs.getDouble("N_CMF_AMOUNT"));
                    map.put("N_ORIGINAL_FEE", rs.getObject("N_ORIGINAL_FEE") == null ? null : rs.getDouble("N_ORIGINAL_FEE"));
                    map.put("N_COMPUTED_FEE", rs.getObject("N_COMPUTED_FEE") == null ? null : rs.getDouble("N_COMPUTED_FEE"));
                    map.put("N_PAYMENT_AMOUNT", rs.getObject("N_PAYMENT_AMOUNT") == null ? null : rs.getDouble("N_PAYMENT_AMOUNT"));
                    map.put("S_FEE_CALCULATION", rs.getString("S_FEE_CALCULATION"));
                    result.add(map);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Long getTotalRowAdvDetailMonthlyFeeReport(String idMonthlyFees) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        Long nTotal = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_TOTAL_ROW_ADV_DETAIL_MONTH_FEE_REPORT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setString(2, idMonthlyFees);
            cs.execute();
            nTotal = cs.getLong(1);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nTotal;
    }

    public static Map<String, Object> getMonthlyFeeNotSendingEmail(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Map<String, Object> result = new HashMap<>();
        List<Integer> dataList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        ResultSet rs = null;
        String sResult = null;
        Integer nResult = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_MONTHLY_FEE_NOT_SENDING_EMAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, (String) mIn.get(MFR_IDS_MONTHLY_REPORT));
            cs.execute();

            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.GET_MONTHLY_FEE_NOT_SENDING_EMAIL ERROR: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    dataList.add(rs.getInt("N_ID"));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.put("data", dataList);
        return result;
    }

    public static void insertEmailMonthlyFee(Map<String, Object> mIn) throws Exception {
        Exception exception = null;

        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        Integer result = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(INSERT_EMAIL_MONTHLY_FEE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, (Integer) mIn.get(MFR_ID_CONFIG));
            cs.setString(4, (String) mIn.get(MFR_EMAIL_TO));
            cs.setString(5, (String) mIn.get(MFR_EMAIL_CC));
            cs.setString(6, (String) mIn.get(MFR_EMAIL_SUBJECT));
            cs.setString(7, (String) mIn.get(MFR_EMAIL_CONTENT));
            cs.setString(8, (String) mIn.get(MFR_EMAIL_FILE_TYPES));
            cs.setString(9, (String) mIn.get(MFR_EMAIL_ATTACH_DETAIL));
            cs.setString(10, (String) mIn.get(MFR_EMAIL_FEE_MONTH_TYPE));
            Object idMonthlyReport = mIn.get(MFR_ID_MONTHLY_REPORT);
            Object idMonthlyReports = mIn.get(MFR_IDS_MONTHLY_REPORT);
            if (idMonthlyReport == null) {
                cs.setNull(11, OracleTypes.NUMBER);
            } else {
                cs.setInt(11, (Integer) idMonthlyReport);
            }
            if (idMonthlyReports == null) {
                cs.setNull(12, OracleTypes.NUMBER);
            } else {
                cs.setString(12, (String) idMonthlyReports);
            }
            cs.execute();
            nResult = cs.getInt(1);
            sResult = cs.getString(2);
            if (nResult != 1) {
                throw new Exception("INSERT EMAIL MONTHLY FEE REPORT ERROR: " + sResult);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static Map<String, Object> getEmailMonthlyFee(Integer idConfig) throws Exception {
        Exception exception = null;

        Connection con = null;
        CallableStatement cs = null;

        Map<String, Object> result = new HashMap<>();
        List<String> emailMerchantList = new ArrayList<>();
        List<String> emailFeeMonthList = new ArrayList<>();
        ResultSet emailMerchantRs = null;
        ResultSet emailFeeMonthRs = null;
        Integer nResult = null;
        String sResult = null;

        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_EMAIl_MERCHANT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, idConfig);
            cs.execute();

            emailFeeMonthRs = (ResultSet) cs.getObject(1);
            emailMerchantRs = (ResultSet) cs.getObject(2);
            nResult = cs.getInt(3);
            sResult = cs.getString(4);
            
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.GET_MERCHANT_EMAIL ERROR: " + sResult);
            } else {
                while (emailFeeMonthRs != null && emailFeeMonthRs.next()) {
                    String email =  emailFeeMonthRs.getString("EMAIL_CONFIG") == null ? "" : emailFeeMonthRs.getString("EMAIL_CONFIG");
                    emailFeeMonthList.add(email);
                }
                result.put("emailFeeMonthList", emailFeeMonthList);

                while (emailMerchantRs != null && emailMerchantRs.next()) {
                    String email = emailMerchantRs.getString("EMAIL_MERCHANT") == null ? "" : emailMerchantRs.getString("EMAIL_MERCHANT");
                    emailMerchantList.add(email);
                }
                result.put("emailMerchantList", emailMerchantList);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(emailFeeMonthRs, null, cs, con);
            closeConnectionDB(emailMerchantRs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        
        return result;
    }

    public static void createReceipt(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(CREATE_RECEIPT);
            cs.setInt(1, (Integer) mIn.get(MFR_ID_MONTHLY_REPORT));
            cs.execute();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static Map<String, Object> getCreatedReceipt(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        Integer nTotal = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_CREATED_RECEIPT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, (String) mIn.get(MFR_IDS_MONTHLY_REPORT));
            cs.execute();

            sResult = cs.getString(3);
            nResult = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.get_created_receipt_requests: " + sResult);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> out = new HashMap<>();
                    out.put(MFR_PARTNER_NAME, rs.getString("S_PARTNER_NAME"));
                    out.put(MFR_ID_CONFIG, rs.getString("N_ID_CONFIG"));
                    out.put(MFR_PARTNER_ID, rs.getString("N_PARTNER_ID"));
                    result.add(out);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("data", result);
        return dataList;
    }

    public static Map<String, Object> getFeeMonth(Integer reportId) throws Exception {
        Exception exception = null;
        Map<String, Object> dbReturnData = new HashMap();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_FEE_MONTH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, reportId);
            cs.execute();
            Integer nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 1) {
                throw new Exception("DB PKG_MONTHLY_FEE_REPORT.GET_TOTAL_FEE_MONTH: " + sResult);
            } else {
                rs = (ResultSet) cs.getObject(1);
                if (rs != null && rs.next()) {
                    double feeMonthAmount = Util.getColumnDouble(rs, "N_FEE_MONTH");
                    double feeMonthCollected = Util.getColumnDouble(rs, "N_FEE_MONTH_COLLECTED");
                    double feeMonthReceivable = Util.getColumnDouble(rs, "N_FEE_MONTH_RECEIVABLE");
                    dbReturnData.put("N_FEE_MONTH", feeMonthAmount);
                    dbReturnData.put("N_FEE_MONTH_COLLECTED", feeMonthCollected);
                    dbReturnData.put("N_FEE_MONTH_RECEIVABLE", feeMonthReceivable);
                }
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnection(rs, con, cs);
        }
        if (exception != null)
            throw exception;
        return dbReturnData;
    }
}
