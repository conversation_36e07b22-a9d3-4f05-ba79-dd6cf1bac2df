package vn.onepay.portal.resources.monthly_fee_report;

import java.sql.Date;

public class SearchDto {
    private Date fromDate;
    private Date toDate;
    private Integer partnerId;
    private String typeAdvances;
    private String payChannels;
    private String receiptTypes;
    private String receiptState;
    private Integer pageActive;
    private Integer pageSize;

    public Date getFromDate() {
        return this.fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return this.toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public Integer getPartnerId() {
        return this.partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getTypeAdvances() {
        return this.typeAdvances;
    }

    public void setTypeAdvances(String typeAdvances) {
        this.typeAdvances = typeAdvances;
    }

    public String getPayChannels() {
        return this.payChannels;
    }

    public void setPayChannels(String payChannels) {
        this.payChannels = payChannels;
    }

    public String getReceiptTypes() {
        return this.receiptTypes;
    }

    public void setReceiptTypes(String receiptTypes) {
        this.receiptTypes = receiptTypes;
    }

    public String getReceiptState() {
        return this.receiptState;
    }

    public void setReceiptState(String receiptState) {
        this.receiptState = receiptState;
    }

    public Integer getPageActive() {
        return this.pageActive;
    }

    public void setPageActive(Integer pageActive) {
        this.pageActive = pageActive;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
