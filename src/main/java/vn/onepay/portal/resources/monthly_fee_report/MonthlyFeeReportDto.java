package vn.onepay.portal.resources.monthly_fee_report;

import java.util.Date;
import java.text.SimpleDateFormat;

public class MonthlyFeeReportDto {

    private SimpleDateFormat sdf = null;

    private Integer index;
    private Integer reportId;
    private String partnerName;
    private String businessName;
    private Integer partnerId;
    private String merchantIds;
    private String contractCode;
    private Long contractDateTime;
    private Integer contractId;
    private String addendum;
    private String countSuccess;
    private String countFailed;
    private String originAmountUsd;
    private String originAmountVnd;
    private String partnerDiscountAmount;
    private String merchantDiscountAmount;
    private String amountTotalVnd;
    private String feeSuccess;
    private String feeFailed;
    private String feeEcom;
    private String feeIta;
    private String totalFee;
    private String advanceAmount;
    private String payChannels;
    private String timeAdvance;
    private String typeAdvance;
    private String receiptType;
    private String receiptState;
    private Integer idConfig;
    private String taxCode;
    private String accountNumbers;
    private String totalVAT;
    private Date fromDate;
    private Long fromDateTime;
    private Date toDate;
    private Long toDateTime;
    private Long toDateTimeView;
    private Long numberEmailSent;
    private String controlMinutes;
    private String merchantName;
    private Double discountFee;
    private Double feeMonth;
    private String totalFeeCollected;
    private String totalFeeReceivable;

    public Date getFromDate() {
        return this.fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return this.toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public Double getDiscountFee() {
        return this.discountFee;
    }

    public void setDiscountFee(Double discountFee) {
        this.discountFee = discountFee;
    }

    public String getTotalFeeCollected() {
        return this.totalFeeCollected;
    }

    public void setTotalFeeCollected(String totalFeeCollected) {
        this.totalFeeCollected = totalFeeCollected;
    }

    public String getTotalFeeReceivable() {
        return this.totalFeeReceivable;
    }

    public void setTotalFeeReceivable(String totalFeeReceivable) {
        this.totalFeeReceivable = totalFeeReceivable;
    }

    public Integer getIndex() {
        return this.index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getReportId() {
        return this.reportId;
    }

    public void setReportId(Integer reportId) {
        this.reportId = reportId;
    }

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getBusinessName() {
        return this.businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Integer getPartnerId() {
        return this.partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getMerchantIds() {
        return this.merchantIds;
    }

    public void setMerchantIds(String merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getContractCode() {
        return this.contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public Long getContractDateTime() {
        return this.contractDateTime;
    }

    public void setContractDateTime(Long contractDateTime) {
        this.contractDateTime = contractDateTime;
    }

    public Integer getContractId() {
        return this.contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public String getCountSuccess() {
        return this.countSuccess;
    }

    public void setCountSuccess(String countSuccess) {
        this.countSuccess = countSuccess;
    }

    public String getCountFailed() {
        return this.countFailed;
    }

    public void setCountFailed(String countFailed) {
        this.countFailed = countFailed;
    }

    public String getPartnerDiscountAmount() {
        return this.partnerDiscountAmount;
    }

    public void setPartnerDiscountAmount(String partnerDiscountAmount) {
        this.partnerDiscountAmount = partnerDiscountAmount;
    }

    public String getMerchantDiscountAmount() {
        return this.merchantDiscountAmount;
    }

    public void setMerchantDiscountAmount(String merchantDiscountAmount) {
        this.merchantDiscountAmount = merchantDiscountAmount;
    }

    public String getAmountTotalVnd() {
        return this.amountTotalVnd;
    }

    public void setAmountTotalVnd(String amountTotalVnd) {
        this.amountTotalVnd = amountTotalVnd;
    }

    public String getFeeSuccess() {
        return this.feeSuccess;
    }

    public void setFeeSuccess(String feeSuccess) {
        this.feeSuccess = feeSuccess;
    }

    public String getFeeFailed() {
        return this.feeFailed;
    }

    public void setFeeFailed(String feeFailed) {
        this.feeFailed = feeFailed;
    }

    public String getFeeIta() {
        return this.feeIta;
    }

    public void setFeeIta(String feeIta) {
        this.feeIta = feeIta;
    }

    public String getTotalFee() {
        return this.totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getAdvanceAmount() {
        return this.advanceAmount;
    }

    public void setAdvanceAmount(String advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getPayChannels() {
        return this.payChannels;
    }

    public void setPayChannels(String payChannels) {
        this.payChannels = payChannels;
    }

    public String getTypeAdvance() {
        return this.typeAdvance;
    }

    public void setTypeAdvance(String typeAdvance) {
        this.typeAdvance = typeAdvance;
    }

    public String getReceiptType() {
        return this.receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getReceiptState() {
        return this.receiptState;
    }

    public void setReceiptState(String receiptState) {
        this.receiptState = receiptState;
    }

    public SimpleDateFormat getSdf() {
        return this.sdf;
    }

    public void setSdf(SimpleDateFormat sdf) {
        this.sdf = sdf;
    }

    public String getFeeEcom() {
        return this.feeEcom;
    }

    public void setFeeEcom(String feeEcom) {
        this.feeEcom = feeEcom;
    }

    public Integer getIdConfig() {
        return this.idConfig;
    }

    public void setIdConfig(Integer idConfig) {
        this.idConfig = idConfig;
    }

    public String getTaxCode() {
        return this.taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getTotalVAT() {
        return this.totalVAT;
    }

    public void setTotalVAT(String totalVAT) {
        this.totalVAT = totalVAT;
    }

    public String getTimeAdvance() {
        return this.timeAdvance;
    }

    public void setTimeAdvance(String timeAdvance) {
        this.timeAdvance = timeAdvance;
    }

    public String getAccountNumbers() {
        return this.accountNumbers;
    }

    public void setAccountNumbers(String accountNumbers) {
        this.accountNumbers = accountNumbers;
    }

    public String getOriginAmountUsd() {
        return this.originAmountUsd;
    }

    public void setOriginAmountUsd(String originAmountUsd) {
        this.originAmountUsd = originAmountUsd;
    }

    public String getOriginAmountVnd() {
        return this.originAmountVnd;
    }

    public void setOriginAmountVnd(String originAmountVnd) {
        this.originAmountVnd = originAmountVnd;
    }

    public Long getNumberEmailSent() {
        return this.numberEmailSent;
    }

    public void setNumberEmailSent(Long numberEmailSent) {
        this.numberEmailSent = numberEmailSent;
    }

    public String getAddendum() {
        return this.addendum;
    }

    public void setAddendum(String addendum) {
        this.addendum = addendum;
    }

    public String getControlMinutes() {
        return this.controlMinutes;
    }

    public void setControlMinutes(String controlMinutes) {
        this.controlMinutes = controlMinutes;
    }

    public String getMerchantName() {
        return this.merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public Long getFromDateTime() {
        return this.fromDateTime;
    }

    public void setFromDateTime(Long fromDateTime) {
        this.fromDateTime = fromDateTime;
    }

    public Long getToDateTime() {
        return this.toDateTime;
    }

    public void setToDateTime(Long toDateTime) {
        this.toDateTime = toDateTime;
    }

    public Long getToDateTimeView() {
        return this.toDateTimeView;
    }

    public void setToDateTimeView(Long toDateTimeView) {
        this.toDateTimeView = toDateTimeView;
    }

    public Double getFeeMonth() {
        return this.feeMonth;
    }

    public void setFeeMonth(Double feeMonth) {
        this.feeMonth = feeMonth;
    }
}
