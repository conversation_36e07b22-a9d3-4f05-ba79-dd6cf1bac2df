package vn.onepay.portal.resources.monthly_fee_report;

import static vn.onepay.portal.Util.gson;
import static vn.onepay.portal.Util.sendResponse;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.impl.URIDecoder;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.ReportFileBuilder;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.RoutePool;

public class MonthlyFeeReportHandler implements IConstants {
    private static final Logger logger = LogManager.getLogger(MonthlyFeeReportHandler.class);
    private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();

                String fromDate = isNull(params.get("fromDate")) ? "" : params.get("fromDate");
                String toDate = isNull(params.get("toDate")) ? "" : params.get("toDate");
                JsonArray partners = new JsonArray(params.get("partners"));
                String partnerIds = partners.stream().map(partner -> ((JsonObject) partner).getValue("partnerId") == null ? "" : ((JsonObject) partner).getValue("partnerId").toString()).collect(Collectors.joining(","));
                JsonArray merchants = new JsonArray(params.get("merchants"));
                String merchantIds = merchants.stream().map(merchant -> ((JsonObject) merchant).getValue("merchantId").toString()).collect(Collectors.joining(","));
                String taxCode = isNull(params.get("taxCode")) ? "" : params.get("taxCode");
                String encodedTypeAdvances = isNull(params.get("typeAdvances")) ? "" : params.get("typeAdvances"); //Chua ki tu '+' -> encode tren FE
                String typeAdvances = URIDecoder.decodeURIComponent(encodedTypeAdvances);
                String timeAdvanes = isNull(params.get("timeAdvances")) ? "" : params.get("timeAdvances");
                String payChannels = isNull(params.get("payChannels")) ? "" : params.get("payChannels");
                String receiptTypes = isNull(params.get("receiptTypes")) ? "" : params.get("receiptTypes");
                String receiptState = (isNull(params.get("receiptState")) || "not_yet".equals(params.get("receiptState").toString())) ? "" : params.get("receiptState");
                String notifyState = isNull(params.get("notifyState")) ? "" : params.get("notifyState");
                String controlMinutes = isNull(params.get("controlMinutes")) ? "" : params.get("controlMinutes");
                Integer pageActive = Integer.parseInt(params.get("pageActive"));
                Integer pageSize = Integer.parseInt(params.get("pageSize"));
                
                Map<String, Object> mIn = new HashMap();
                mIn.put(MFR_FROM_DATE, fromDate);
                mIn.put(MFR_TO_DATE, toDate);
                mIn.put(MFR_PARTNER_IDS, partnerIds);
                mIn.put(MFR_MERCHANT_IDS, merchantIds);
                mIn.put(MFR_TAX_CODE, taxCode);
                mIn.put(MFR_TYPE_ADVANCES, typeAdvances);
                mIn.put(MFR_TIME_ADVANCES, timeAdvanes);
                mIn.put(MFR_PAY_CHANNELS, payChannels);
                mIn.put(MFR_RECEIPT_TYPES, receiptTypes);
                mIn.put(MFR_RECEIPT_STATE, receiptState);
                mIn.put(MFR_NOTIFY_STATE, notifyState);
                mIn.put(MFR_CONTROL_MINUTES, controlMinutes);
                mIn.put(MFR_PAGE_ACTIVE, pageActive);
                mIn.put(MFR_PAGE_SIZE, pageSize);
                logger.info("MONTHLY FEE REPORT SEARCH REQUEST PARAMS | " + mIn);

                sendResponse(ctx, 200, MonthlyFeeReportDao.list(mIn));
            } catch (Exception e) {
                logger.log(Level.WARN, "SEARCH MONTHLY FEE REPORT ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartners(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                String partnerName = params.get("name");
                sendResponse(ctx, 200, MonthlyFeeReportDao.getPartners(partnerName));
            } catch (Exception e) {
                logger.log(Level.WARN, "GET ALL PARTNERS ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchants(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                String merchantId = params.get("name");
                sendResponse(ctx, 200, MonthlyFeeReportDao.getMerchants(merchantId));
            } catch (Exception e) {
                logger.log(Level.WARN, "GET ALL PARTNERS ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void searchDetail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                if ((params.get(REPORTID) == null || params.get(REPORTID).isEmpty()))
                    throw IErrors.VALIDATION_ERROR;
                Integer reportId = Integer.parseInt(params.get(REPORTID));
                String advanceStates = params.get(MFR_ADVANCE_STATES);
                Integer page = params.get(PAGE) != null ? Integer.parseInt(params.get(PAGE)) : 0;
                Integer pageSize = params.get(PAGESIZE) != null ? Integer.parseInt(params.get(PAGESIZE)) : 20;
                String queryMethod = QUERY_SELECT;

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MFR_ID_MONTHLY_REPORT, reportId);
                mIn.put(MFR_ADVANCE_STATES, advanceStates);
                mIn.put(MFR_PAGE_ACTIVE, page);
                mIn.put(MFR_PAGE_SIZE, pageSize);
                mIn.put(MFR_QUERY_METHOD, queryMethod);

                JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                sendResponse(ctx, 200, MonthlyFeeReportDao.getAdvanceMonthlyFeeReport(mIn, monthlyFeeReport));
            } catch (Exception e) {
                logger.log(Level.WARN, "GET ALL PARTNERS ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void isExistMonthlyFeeReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                String reportIds = params.get(MFR_IDS_MONTHLY_REPORT);
                if (reportIds == null || "".equals(reportIds))
                    throw IErrors.VALIDATION_ERROR;
                sendResponse(ctx, 200, MonthlyFeeReportDao.isExistMonthlyFeeReport(reportIds));
            } catch (Exception e) {
                logger.log(Level.WARN, "COUNT MONTHLY FEE BY ID ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportExcel(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                String fromDate = isNull(bodyJson.getString("fromDate")) ? "" : bodyJson.getString("fromDate");
                String toDate = isNull(bodyJson.getString("toDate")) ? "" : bodyJson.getString("toDate");
                JsonArray partners = new JsonArray(bodyJson.getString("partners"));
                String partnerIds = partners.stream().map(partner -> ((JsonObject) partner).getValue("partnerId") == null ? "" : ((JsonObject) partner).getValue("partnerId").toString()).collect(Collectors.joining(","));
                JsonArray merchants = new JsonArray(bodyJson.getString("merchants"));
                String merchantIds = merchants.stream().map(merchant -> ((JsonObject) merchant).getValue("merchantId").toString()).collect(Collectors.joining(","));
                String taxCode = isNull(bodyJson.getString("taxCode")) ? "" : bodyJson.getString("taxCode");
                String encodedTypeAdvances = isNull(bodyJson.getString("typeAdvances")) ? "" : bodyJson.getString("typeAdvances"); //Chua ki tu '+' -> encode tren FE
                String typeAdvances = URIDecoder.decodeURIComponent(encodedTypeAdvances);
                String timeAdvanes = isNull(bodyJson.getString("timeAdvances")) ? "" : bodyJson.getString("timeAdvances");
                String payChannels = isNull(bodyJson.getString("payChannels")) ? "" : bodyJson.getString("payChannels");
                String receiptTypes = isNull(bodyJson.getString("receiptTypes")) ? "" : bodyJson.getString("receiptTypes");
                String receiptState = (isNull(bodyJson.getString("receiptState")) || "not_yet".equals(bodyJson.getString("receiptState").toString())) ? "" : bodyJson.getString("receiptState");
                String notifyState = isNull(bodyJson.getString("notifyState")) ? "" : bodyJson.getString("notifyState");
                String controlMinutes = isNull(bodyJson.getString("controlMinutes")) ? "" : bodyJson.getString("controlMinutes");
                String exportType = isNull(bodyJson.getString("exportType")) ? "" : bodyJson.getString("exportType");
                Integer pageActive = 0;
                Integer pageSize = Integer.MAX_VALUE;
                
                Map<String, Object> mIn = new HashMap();
                mIn.put(MFR_FROM_DATE, fromDate);
                mIn.put(MFR_TO_DATE, toDate);
                mIn.put(MFR_PARTNER_IDS, partnerIds);
                mIn.put(MFR_MERCHANT_IDS, merchantIds);
                mIn.put(MFR_TAX_CODE, taxCode);
                mIn.put(MFR_TYPE_ADVANCES, typeAdvances);
                mIn.put(MFR_TIME_ADVANCES, timeAdvanes);
                mIn.put(MFR_PAY_CHANNELS, payChannels);
                mIn.put(MFR_RECEIPT_TYPES, receiptTypes);
                mIn.put(MFR_RECEIPT_STATE, receiptState);
                mIn.put(MFR_NOTIFY_STATE, notifyState);
                mIn.put(MFR_CONTROL_MINUTES, controlMinutes);
                mIn.put(MFR_EXPORT_TYPE, exportType);
                mIn.put(MFR_PAGE_ACTIVE, pageActive);
                mIn.put(MFR_PAGE_SIZE, pageSize);
                logger.info("MONTHLY FEE REPORT EXPORT REQUEST PARAMS | " + mIn);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = MonthlyFeeReportDao.getTotalRowMonthlyFeeReport(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "BAO CAO PHI THANG_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("monthly_fee_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                    QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                    QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch(Exception e) {
                logger.log(Level.WARN, "EXPORT EXCEL MONTHLY FEE REPORT Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
        
    }

    public static void downloadAdvancePdf(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) bodyJson.getJsonArray("selectedRows").getList();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MFR_SELECTED_ROWS, selectedRows);
                mIn.put(MFR_QUERY_METHOD, QUERY_DOWNLOAD);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                fileDownloadDto.setFile_type("monthly_report_advance");
                String fileName = null;
                String fileHashName = null;
                if (selectedRows.size() > 1) {
                    fileDownloadDto.setExt("zip");
                    fileName = "BBDS_" + System.currentTimeMillis();
                    fileHashName = Convert.hash(fileName);
                } else {
                    LinkedHashMap selectedRow = selectedRows.get(0);
                    Integer reportId = (Integer) selectedRow.get("reportId");

                    JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                    String merchantIds = monthlyFeeReport.getString("merchantIds");
                    Long fromDateTime = monthlyFeeReport.getLong("fromDateTime");
                    Long toDateTime = monthlyFeeReport.getLong("toDateTime");
                    Long toDateTimeView = monthlyFeeReport.getLong("toDateTimeView");
                    String payChannels = monthlyFeeReport.getString("payChannels");
                    String receiptType = monthlyFeeReport.getString("receiptType");

                    fileDownloadDto.setExt("pdf");
                    fileName = ReportFileBuilder.customExportFileNameMonthlyFeeReport("BBDS_", receiptType, merchantIds, fromDateTime, toDateTime, toDateTimeView, payChannels);
                    fileHashName = Convert.hash(fileName);
                }
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                FileDownloadDao.insert(fileDownloadDto);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                String requestPath = RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_PDF;
                if (selectedRows.size() > 1) {
                    QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                        QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), requestPath, requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                        QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), requestPath, requestData));
                }     
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.warn("Payment2 - downloadAdvancePdf error " + ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void downloadAdvanceExcel(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) bodyJson.getJsonArray("selectedRows").getList();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MFR_SELECTED_ROWS, selectedRows);
                mIn.put(MFR_QUERY_METHOD, QUERY_DOWNLOAD);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                fileDownloadDto.setFile_type("monthly_report_advance");
                String fileName = null;
                String fileHashName = null;
                if (selectedRows.size() > 1) {
                    fileDownloadDto.setExt("zip");
                    fileName = "BBDS_" + System.currentTimeMillis();
                    fileHashName = Convert.hash(fileName);
                } else {
                    LinkedHashMap selectedRow = selectedRows.get(0);
                    Integer reportId = (Integer) selectedRow.get("reportId");

                    JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                    String merchantIds = monthlyFeeReport.getString("merchantIds");
                    Long fromDateTime = monthlyFeeReport.getLong("fromDateTime");
                    Long toDateTime = monthlyFeeReport.getLong("toDateTime");
                    Long toDateTimeView = monthlyFeeReport.getLong("toDateTimeView");
                    String payChannels = monthlyFeeReport.getString("payChannels");
                    String receiptType = monthlyFeeReport.getString("receiptType");

                    fileDownloadDto.setExt("xls");
                    fileName = ReportFileBuilder.customExportFileNameMonthlyFeeReport("BBDS_", receiptType, merchantIds, fromDateTime, toDateTime, toDateTimeView, payChannels);
                    fileHashName = Convert.hash(fileName);
                }
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                FileDownloadDao.insert(fileDownloadDto);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                String requestPath = RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_EXCEL;
                if (selectedRows.size() > 1) {
                  QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                        QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), requestPath, requestData));
                } else {
                  QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                        QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), requestPath, requestData));
                }
                
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception ex) {
                logger.error("Payment2 - exportExcelDetail error " + ex.getMessage());
                ctx.fail(ex);
            }
        }, false, null);
    }


    public static void downloadAdvanceDetailTransExcel(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) bodyJson.getJsonArray("selectedRows").getList();

                if (selectedRows == null || selectedRows.isEmpty())
                    throw IErrors.VALIDATION_ERROR;

                Map<String, Object> mIn = new HashMap();  
                mIn.put(MFR_SELECTED_ROWS, selectedRows);

                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                fileDownloadDto.setFile_type("monthly_report_advance_detail_trans");
                String fileName = null;
                String fileHashName = null;

                //get total rows data
                String idMonthlyFees = selectedRows.stream().map(item->item.get("reportId").toString()).collect(Collectors.joining(","));
                Long totalRows = MonthlyFeeReportDao.getTotalRowAdvDetailMonthlyFeeReport(idMonthlyFees);

                if (totalRows > Config.getFileRowLevel() || selectedRows.size() > 1) {
                    fileDownloadDto.setExt("zip");
                    fileName = "BB_CHI TIET GD_" + System.currentTimeMillis();
                    fileHashName = Convert.hash(fileName);
                } else {
                    LinkedHashMap selectedRow = selectedRows.get(0);
                    Integer reportId = (Integer) selectedRow.get("reportId");

                    JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);                    
                    String merchantIds = monthlyFeeReport.getString("merchantIds");
                    Long fromDateTime = monthlyFeeReport.getLong("fromDateTime");
                    Long toDateTime = monthlyFeeReport.getLong("toDateTime");
                    Long toDateTimeView = monthlyFeeReport.getLong("toDateTimeView");
                    String payChannels = monthlyFeeReport.getString("payChannels");
                    String receiptType = monthlyFeeReport.getString("receiptType");

                    fileDownloadDto.setExt("xls");
                    fileName = ReportFileBuilder.customExportFileNameMonthlyFeeReport("BB_CHI TIET GD_", receiptType, merchantIds, fromDateTime, toDateTime, toDateTimeView, payChannels);
                    fileHashName = Convert.hash(fileName);
                }

                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                FileDownloadDao.insert(fileDownloadDto);

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);

                String requestPath = RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_DETAIL_TRANS_EXCEL;

                if (totalRows > Config.getFileRowLevel() || selectedRows.size() > 1) {
                    QueueProducer.sendMessage(new Message<>(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                            QueueServer.getServiceQueueIn(), QueueServer.getServiceQueueOut(), requestPath, requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, 
                        QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), requestPath, requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch(Exception e) {
                logger.log(Level.WARN, "EXPORT EXCEL MONTHLY FEE REPORT Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMonthlyFeeNotSendingEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                String reportIds = request.getParam("reportIds");
                mIn.put(MFR_IDS_MONTHLY_REPORT, reportIds);

                sendResponse(ctx, 200, MonthlyFeeReportDao.getMonthlyFeeNotSendingEmail(mIn));
            } catch(Exception e) {
                logger.log(Level.WARN, "INSERT EMAIL MONTHLY FEE ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void sendEmail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> result = new HashMap<>();
            String message = "";
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                SimpleDateFormat sdfEmail = new SimpleDateFormat("dd.MM.yyyy");
                JsonArray selectedRows = bodyJson.getJsonArray("selectedRows");
                boolean attachDetailChecked = bodyJson.getBoolean("attachDetailChecked");
                String attachDetail = attachDetailChecked ? "YES" : "NO";
                String fileTypes = isNull(bodyJson.getString("fileTypes")) ? "" : bodyJson.getString("fileTypes");
                String toEmail = URIDecoder.decodeURIComponent(bodyJson.getString("toEmail"));
                String ccEmail = URIDecoder.decodeURIComponent(bodyJson.getString("ccEmail"));
                String subjectEmail = URIDecoder.decodeURIComponent(bodyJson.getString("subjectEmail"));
                String contentEmail = URIDecoder.decodeURIComponent(bodyJson.getString("contentEmail"));
                String emailFeeMonthType = bodyJson.getString("emailFeeMonthType");
                
                if (emailFeeMonthType.equals("merge")) {
                    result = MonthlyFeeReportService.buildParamsMergedEmailRequest(selectedRows, sdfEmail, subjectEmail, contentEmail, toEmail, ccEmail, fileTypes, attachDetail, emailFeeMonthType);
                }
                if (emailFeeMonthType.equals("stand_alone")) {
                    result = MonthlyFeeReportService.buildParamsStandAloneEmailRequest(selectedRows, sdfEmail, subjectEmail, contentEmail, toEmail, ccEmail, fileTypes, attachDetail, emailFeeMonthType);
                }
                
            } catch(Exception e) {
                logger.log(Level.WARN, "INSERT EMAIL MONTHLY FEE ERROR: ", e);
                result.put("responseCode", 500);
                result.put("message", "Yêu cầu gửi mail BBĐS thất bại!");
                ctx.fail(e);
            }
            sendResponse(ctx, 200, result);
        }, false, null);
    }

    public static void createReceipt(RoutingContext ctx) {
      ctx.vertx().executeBlocking(future -> {
        Set<String> messages = new HashSet<>();
        try {
            JsonObject bodyJson = ctx.getBodyAsJson();
            Map<String, Object> result = new HashMap<>();
            JsonArray selectedRows = bodyJson.getJsonArray("selectedRows");

            // check if GSM report is send at day <= 4th of month
            List<Integer> gsmReports = selectedRows.stream()
            .map(report -> (JsonObject) report)
            .filter(report -> {
                JsonObject rowInstance = (JsonObject) report;
                Long partnerId = rowInstance.getLong("partnerId");
                return partnerId.longValue() == ******** && LocalDate.now().getDayOfMonth() <= 4;
            })
            .map(report -> report.getInteger("reportId"))
            .collect(Collectors.toList());
            
            List<JsonObject> filteredRows = selectedRows.stream()
            .map(report -> (JsonObject) report)
            .filter(row -> !gsmReports.contains(row.getInteger("reportId")))
            .collect(Collectors.toList());
            
            filteredRows.stream().forEach(row -> {
                JsonObject rowInstance = (JsonObject) row;
                Integer reportId = Convert.parseInt(rowInstance.getValue("reportId").toString(), 0);
                
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MFR_ID_MONTHLY_REPORT, reportId);
                try {
                MonthlyFeeReportDao.createReceipt(mIn);
                } catch (Exception e) {
                logger.log(Level.WARN, "INSERT EMAIL MONTHLY FEE ERROR: ", e);
                messages.add("Tạo yêu cầu tạo hoá đơn thất bại.");
                result.put("responseCode", 500);
                }
            });
            if (!gsmReports.isEmpty()) {
                result.put("responseCode", 204);
                messages.add("Không được tạo với GSM: Ngày gửi của GSM chưa đến mùng 4 hàng tháng.");
            } else {
                result.put("responseCode", 200);
                messages.add("Tạo yêu cầu tạo hoá đơn thành công.");
            }
            result.put("messages", messages.stream().collect(Collectors.joining("\n")));
            sendResponse(ctx, 200, result);
        } catch(Exception e) {
            logger.log(Level.WARN, "INSERT EMAIL MONTHLY FEE ERROR: ", e);
            ctx.fail(e);
        }
      }, false, null);
    }

    public static void getCreatedReceipt(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                String idsMonthly = bodyJson.getString("idsMonthly");
                
                Map<String, Object> mIn = new HashMap();
                mIn.put(MFR_IDS_MONTHLY_REPORT, idsMonthly);

                sendResponse(ctx, 200, MonthlyFeeReportDao.getCreatedReceipt(mIn));
            } catch (Exception e) {
                logger.log(Level.WARN, "SEARCH MONTHLY FEE REPORT ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static boolean isNull(Object obj) {
        return obj == null ? true : false;
    }

    public static void createMonthlyFeeReportRequest(String configIds) {
        String requestURI = ONEPAY_PAYMENT2_SERVICE_BASE_URL + "/monthly-fee-report/request";
        String requestMethod = "POST";
        JsonObject requestData = new JsonObject();
        requestData.put("configIds", configIds);
        requestData.put("dProcess", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        Util.sendRequestToOtherService(requestURI, requestMethod, requestData);
    }
}
