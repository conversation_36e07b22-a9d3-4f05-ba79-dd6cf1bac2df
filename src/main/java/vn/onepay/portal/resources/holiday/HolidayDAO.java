package vn.onepay.portal.resources.holiday;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import vn.onepay.portal.Convert;
import vn.onepay.portal.resources.Db;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

public class HolidayDAO extends Db {
    private static final Logger LOGGER = LogManager.getLogger(HolidayDAO.class);
    private static final String SEARCH = "{call ONECDR.PKG_HOLIDAY_CONFIG.gl_holiday(?,?,?,?,?,?)}";
    private static final String ADD_UPDATE = "{call ONECDR.PKG_HOLIDAY_CONFIG.insert_or_update_holiday(?,?,?,?,?,?,?)}";
    private static final String DELETE = "{call ONECDR.PKG_HOLIDAY_CONFIG.d_holiday(?,?,?)}";

    public static JsonObject search(int page, int size) throws Exception {
        JsonObject result = new JsonObject();
        JsonArray list = new JsonArray();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(SEARCH);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, page);
            cs.setInt(5, size);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            while (rs != null && rs.next()) {
                list.add(
                        convertHoliday(rs)
                );
            }
            result.put("list", list);
            result.put("totalItem", cs.getInt(6));
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static Map<String, Object> insertOrUpdate(Map<String, Object> mIn) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(ADD_UPDATE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, Convert.parseInt(mIn.get("id").toString(), 0));
            cs.setString(4, mIn.get("fromDate").toString());
            cs.setString(5, mIn.get("toDate").toString());
            cs.setString(6, mIn.get("description").toString());
            cs.setString(7, mIn.get("updatedBy").toString());
            cs.execute();
            int statusCode = cs.getInt(1);
            String message = cs.getString(2);
            if (statusCode != 200 && statusCode != 400) {
                throw new Exception(message);
            }
            result.put("status", String.valueOf(statusCode));
            result.put("message", message);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static Map<String, Object> delete(int id) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(DELETE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.execute();
            int statusCode = cs.getInt(1);
            String message = cs.getString(2);
            if (statusCode != 200 && statusCode != 400) {
                throw new Exception(message);
            }
            result.put("status", String.valueOf(statusCode));
            result.put("message", message);
        } catch (Exception e) {
            LOGGER.log(Level.ERROR, "", e);
            throw e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }


    private static JsonObject convertHoliday(ResultSet rs) throws SQLException {
        JsonObject jObject = new JsonObject();
        jObject.put("id", rs.getLong("N_ID"));
        jObject.put("fromDate", rs.getString("D_FROM"));
        jObject.put("toDate", rs.getString("D_TO"));
        jObject.put("description", rs.getString("S_DESC"));
        jObject.put("updatedAt", rs.getString("D_UPDATE"));
        jObject.put("updatedBy", rs.getString("S_UPDATE"));
        jObject.put("num", rs.getLong("RNUM"));
        return jObject;
    }
}
