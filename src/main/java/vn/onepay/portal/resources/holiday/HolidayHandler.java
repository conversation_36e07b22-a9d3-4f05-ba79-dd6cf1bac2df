package vn.onepay.portal.resources.holiday;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;

import java.util.HashMap;
import java.util.Map;

public class HolidayHandler implements IConstants {
    private static final Logger logger = LogManager.getLogger(HolidayHandler.class);
    private static String ONEPAY_PAYMENT2_SERVICE_BASE_URL = Config.getString("advance-service.url", "");

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int page = Convert.parseInt(ctx.request().getParam("page"), 1);
                int size = Convert.parseInt(ctx.request().getParam("size"), 10);
                Util.sendResponse(ctx, 200, HolidayDAO.search(page, size));
            } catch (Exception e) {
                logger.log(Level.ERROR, "GET LIST: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insertOrUpdate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("id", body.getString("id"));
                mIn.put("fromDate", body.getString("fromDate"));
                mIn.put("toDate", body.getString("toDate"));
                mIn.put("description", body.getString("description")!= null? body.getString("description") : BLANK);
                mIn.put("updatedBy", body.getString("updatedBy"));
                var result = HolidayDAO.insertOrUpdate(mIn);
                Util.sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.ERROR, "INSERT OR UPDATE HOLIDAY: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void delete(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int id = Convert.parseInt(ctx.request().getParam("id").toString(), 0);
                var result = HolidayDAO.delete(id);
                Util.sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.ERROR, "DELETE HOLIDAY: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
