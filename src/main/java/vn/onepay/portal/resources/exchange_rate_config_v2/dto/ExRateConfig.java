package vn.onepay.portal.resources.exchange_rate_config_v2.dto;

import java.sql.Timestamp;

public class ExRateConfig {
    private int id;
    private Timestamp exchangeRateDate;
    private String currency;
    private double exchangeRateValue;
    private double exchangeRateUsd;
    private int month;
    private int year;
    private String createdBy;
    private String updatedBy;
    private Timestamp createdAt;
    private Timestamp updatedAt;

    public Timestamp getExchangeRateDate() {
        return exchangeRateDate;
    }

    public void setExchangeRateDate(Timestamp exchangeRateDate) {
        this.exchangeRateDate = exchangeRateDate;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public double getExchangeRateValue() {
        return exchangeRateValue;
    }

    public void setExchangeRateValue(double exchangeRateValue) {
        this.exchangeRateValue = exchangeRateValue;
    }

    public double getExchangeRateUsd() {
        return exchangeRateUsd;
    }

    public void setExchangeRateUsd(double exchangeRateUsd) {
        this.exchangeRateUsd = exchangeRateUsd;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}
