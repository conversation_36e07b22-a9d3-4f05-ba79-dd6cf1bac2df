package vn.onepay.portal.resources.exchange_rate_config_v2;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.exchange_rate_config_v2.dto.ExRateConfig;
import vn.onepay.portal.resources.exchange_rate_config_v2.dto.ExRateObj;

public class ExRateConfigDao extends Db {
    private static final Logger _LOGGER = Logger.getLogger(ExRateConfigDao.class.getName());

    private static final String GET_LIST_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG_V2.search_exchange_rate(?,?,?,?,?,?)}";
    private static final String CREATE_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG_V2.upsert_exchange_rate(?,?,?,?,?,?)}";

    public static BaseList<ExRateConfig> getListExchangeRateConfig(Map<String, String> req) throws SQLException {
        SQLException exception = null;
        List<ExRateConfig> listConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_LIST_EXCHANGE_RATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, req.getOrDefault(YEAR, BLANK));
            cs.setString(5, req.getOrDefault(MONTH, BLANK));
            cs.setString(6, req.getOrDefault(CURRENCY, BLANK));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                while (rs != null && rs.next()) {
                    ExRateConfig config = new ExRateConfig();
                    config.setId(Util.getColumnInteger(rs, "N_ID"));
                    config.setCurrency(Util.getColumnString(rs, "S_CURRENCY"));
                    config.setExchangeRateDate(Util.getColumnTimeStamp(rs, "N_EXCHANGE_DATE"));
                    config.setExchangeRateValue(Util.getColumnDouble(rs, "N_EXCHANGE_VALUE"));
                    config.setExchangeRateUsd(Util.getColumnDouble(rs, "N_EXCHANGE_USD"));
                    config.setMonth(Util.getColumnInteger(rs, "N_MONTH"));
                    config.setYear(Util.getColumnInteger(rs, "N_YEAR"));
                    config.setCreatedAt(Util.getColumnTimeStamp(rs, "D_CREATE"));
                    config.setUpdatedAt(Util.getColumnTimeStamp(rs, "D_UPDATE"));
                    config.setCreatedBy(Util.getColumnString(rs, "S_CREATE"));
                    config.setUpdatedBy(Util.getColumnString(rs, "S_UPDATE"));
                    listConfig.add(config);
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return new BaseList<>(listConfig, listConfig.size());
    }

    public static Integer create(Date exchangeDate, int year, int month, List<ExRateObj> configs, String creator) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nResult = 0;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall(CREATE_EXCHANGE_RATE);

            for (ExRateObj config : configs) {
                cs.setDate(1, new java.sql.Date(exchangeDate.getTime()));
                cs.setObject(2, config.getExchangeValue());
                cs.setInt(3, month);
                cs.setInt(4, year);
                cs.setString(5, config.getCurrency());
                cs.setString(6, creator);
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            nResult = 1;
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            if (con != null)
                con.rollback();
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }
}
