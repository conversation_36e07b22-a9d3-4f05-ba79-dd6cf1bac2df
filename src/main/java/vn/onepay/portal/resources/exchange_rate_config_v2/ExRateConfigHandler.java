package vn.onepay.portal.resources.exchange_rate_config_v2;

import static vn.onepay.portal.Util.sendResponse;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.collections.CollectionUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.exchange_rate_config_v2.dto.ExRateConfigCreateRequest;
import vn.onepay.portal.resources.exchange_rate_config_v2.dto.ExRateObj;

public class ExRateConfigHandler implements IConstants {
    private static final Logger _LOGGER = Logger.getLogger(ExRateConfigHandler.class.getName());
    private static final Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd").create();

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> request = Util.convertRequestParamsToMap(ctx);
                sendResponse(ctx, OK, ExRateConfigDao.getListExchangeRateConfig(request));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void create(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                _LOGGER.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ ============= START POST EXCHANGE RATE CONFIG HANDLE ===========");
                String body = ctx.getBodyAsString();
                ExRateConfigCreateRequest req = gson.fromJson(body, ExRateConfigCreateRequest.class);

                if (req == null || req.getYear() == null || req.getMonth() == null || CollectionUtils.isEmpty(req.getValues())) {
                    _LOGGER.severe(() -> "ERROR: REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                boolean isRequired = req.getValues().stream().allMatch(t -> t.getCurrency() != null);
                if (!isRequired) {
                    _LOGGER.severe(() -> "ERROR: CURRENCY IS REQUIRED");
                    throw IErrors.VALIDATION_ERROR;
                }

                Optional<ExRateObj> usdOptional = req.getValues().stream().filter(t -> "USD".equals(t.getCurrency())).findFirst();
                if (!usdOptional.isPresent() || usdOptional.get().getExchangeValue() == null) {
                    _LOGGER.severe(() -> "ERROR: EXCHANGE RATE USD IS REQUIRED");
                    throw IErrors.VALIDATION_ERROR;
                }

                Integer year = req.getYear();
                Integer month = req.getMonth();
                Calendar calendar = Calendar.getInstance();
                calendar.set(year, month - 1, 1, 0, 0, 0);

                Integer nResult = ExRateConfigDao.create(calendar.getTime(), year, month, req.getValues(), req.getCreatedBy());
                Map<String, Object> res = new HashMap<>();
                if (nResult == 1) {
                    res.put("status", "SUCCESS");
                } else {
                    res.put("status", "FAIL");
                }

                sendResponse(ctx, OK, res);
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
