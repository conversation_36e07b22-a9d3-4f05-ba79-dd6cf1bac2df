package vn.onepay.portal.resources.base.dto;

//import lombok.Data;

//@Data
public class ResourcesResult {
    private int expiresIn;
    private ProfileResource resources;
    private StateResource state;
    
    public int getExpiresIn() {
        return expiresIn;
    }

    public ResourcesResult setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
        return this;
    }

    public ProfileResource getResources() {
        return resources;
    }

    public ResourcesResult setResources(ProfileResource resources) {
        this.resources = resources;
        return this;
    }

    public StateResource getState() {
        return state;
    }

    public ResourcesResult setState(StateResource state) {
        this.state = state;
        return this;
    }

    
}
