package vn.onepay.portal.resources.base;

//import lombok.Data;

import java.util.List;

//@Data
public class BaseList<K> {
	private Integer totalItems;
	private List<K> list;

	public Integer getTotalItems() {
		return totalItems;
	}

	public BaseList<K> setTotalItems(Integer totalItems) {
		this.totalItems = totalItems;
		return this;
	}

	public List<K> getList() {
		return list;
	}

	public BaseList<K> setList(List<K> list) {
		this.list = list;
		return this;
	}

	public BaseList(List<K> list, Integer totalItems) {
		this.list = list;
		this.totalItems = totalItems;
	}

	public BaseList(List<K> list) {
		this.list = list;
	}

	public BaseList() {
	}
}
