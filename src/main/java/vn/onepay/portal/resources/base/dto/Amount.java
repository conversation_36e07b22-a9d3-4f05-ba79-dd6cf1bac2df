package vn.onepay.portal.resources.base.dto;

/**
 * Created by anhkh on 13/12/2019.
 */
public class Amount {
    private Double total;
    private Double originalTotal;
    private String currency;
    private Double refund_total;

    public Double getRefund_total() {
        return refund_total;
    }

    public void setRefund_total(Double refund_total) {
        this.refund_total = refund_total;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * @return the originalTotal
     */
    public Double getOriginalTotal() {
        return originalTotal;
    }

    /**
     * @param originalTotal the originalTotal to set
     */
    public void setOriginalTotal(Double originalTotal) {
        this.originalTotal = originalTotal;
    }
}
