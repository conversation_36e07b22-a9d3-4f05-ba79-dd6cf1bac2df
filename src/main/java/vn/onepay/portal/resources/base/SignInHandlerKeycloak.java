package vn.onepay.portal.resources.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.KeyCloakClient;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.OneAmUser;
import vn.onepay.portal.resources.user.dto.User;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class SignInHandlerKeycloak implements IConstants {

    // Remove Acceptance by id
    public static void create(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String code = request.getParam(CODE);
                if (code == null || code.isEmpty()) {
                    LOGGER.log(Level.SEVERE, "[VALIDATION SIGN IN ROUTE] => AUTHORIZATION CODE EMPTY");
                    throw new Exception("[VALIDATION SIGN IN ROUTE] => AUTHORIZATION CODE EMPTY");
                }
                String redirecUri = URLEncoder.encode(Config.getString("one_am.uri_redirect", ""),
                        StandardCharsets.UTF_8.name());
                String param = "grant_type=authorization_code&code=" + code
                        + "&redirect_uri=" + redirecUri
                        + "&client_id=" + Config.getString("one_am.user", "");

                LOGGER.log(Level.SEVERE, "PARAM KEY CLOAK: " + param);
                JsonObject jsonReturn = KeyCloakClient.getToken(param);
                oneAMGetResources(ctx, jsonReturn);
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    // /**
    // * get data resources oneam
    // *
    // * @param context
    // * @param data
    // * @param headerAuthorization
    // * @param tokenOneAmParameter
    // * @param bufferData
    // * @param client
    // */
    // @SuppressWarnings("deprecation")
    private static void oneAMGetResources(RoutingContext context, JsonObject res) {
        try {
            String idToken = (String) res.getString("id_token");
            String[] chunks = idToken.split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();

            String payload = new String(decoder.decode(chunks[1]));
            Map<String, Object> jPayload = new ObjectMapper().readValue(payload, Map.class);
            OneAmUser userAM = new OneAmUser();
            userAM.setEmail((String) jPayload.get("email"));
            userAM.setLastName((String) jPayload.get("family_name"));
            userAM.setFirstName(((String) jPayload.get("given_name")));
            Map<String, Object> jAddress = nvl((Map<String, Object>) jPayload.get("address"), new HashMap<>());

            userAM.address(nvl((String) jAddress.get("formatted"), "null"));
            userAM.setCountry(nvl((String) jAddress.get("country"), ""));
            userAM.setProvince(nvl((String) jAddress.get("locality"), ""));
            userAM.setDistrict(nvl((String) jAddress.get("region"), ""));

            userAM.setMobile(nvl((String) jPayload.get("phone_number"), "null"));
            userAM.setId((String) jPayload.get("email"));
            User user = UserDao.getbyEmail(userAM.getEmail());
            
            LOGGER.info("[ USER SESSION GET FROM ONEPORTAL ] => " + user);
            LOGGER.info("[ USER SESSION GET FROM ONEAM ] => " + userAM);

            if (user != null) {
                user.setKeyCloakToken(idToken);
                String databaseUser = user.getEmail();
                String oneAmUser = userAM.getEmail();

                String hashUser = "";
                String hashOneAmUser = "";
                try {
                    hashUser = Util.hash(databaseUser);
                    hashOneAmUser = Util.hash(oneAmUser);
                } catch (NoSuchAlgorithmException e) {
                    LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                    try {
                        throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");
                    } catch (Exception e1) {
                        context.fail(e);
                    }
                } catch (UnsupportedEncodingException e) {
                    LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                    throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");

                }
                LOGGER.info("[ hashUser ] : " + databaseUser + " =====hash :" + hashUser);
                LOGGER.info("[ hashOneAmUser ] : " + oneAmUser + " =====hash :" + hashOneAmUser);
                if (!hashUser.equals(hashOneAmUser)) {
                    LOGGER.info("=====================INSERT USER============================");
                    User userData = new User();
                    String name = userAM.getLastName();
                    if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                        name += " " + userAM.getFirstName();
                    }
                    userData.setName(name);
                    userData.setEmail(userAM.getEmail());
                    userData.setAddress(userAM.getAddress());
                    userData.setPhone(userAM.getMobile());
                    userData.setS_id(userAM.getId());
                    userData.setKeyCloakToken(idToken);
                    UserDao.updateUserByEmail(userData);
                    User user2 = UserDao.getbyEmail(userAM.getEmail());
                    user2.setKeyCloakToken(idToken);
                    context.response().putHeader("Cache-Control",
                            "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                    context.response().putHeader("Pragma", "no-cache");
                    context.response().putHeader("Expires", "0");
                    context.put(HANDLER_USER_DATA_RESULT, user2);
                    context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                    context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                    context.response().putHeader("Cache-Control",
                            "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                    context.response().putHeader("Pragma", "no-cache");
                    context.response().putHeader("Expires", "0");
                    sendResponse(context, 200, userData);
                } else {

                    context.response().putHeader("Cache-Control",
                            "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                    context.response().putHeader("Pragma", "no-cache");
                    context.response().putHeader("Expires", "0");
                    context.put(HANDLER_USER_DATA_RESULT, user);
                    LOGGER.info("HANDLER_USER_DATA_RESULT: " + user);
                    context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                    context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                    context.response().putHeader("Cache-Control",
                            "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                    context.response().putHeader("Pragma", "no-cache");
                    context.response().putHeader("Expires", "0");
                    sendResponse(context, 200, user);
                }
            } else {
                User userData = new User();
                String name = userAM.getLastName();
                if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                    name += " " + userAM.getFirstName();
                }
                userData.setName(name);
                userData.setEmail(userAM.getEmail());
                userData.setAddress(userAM.getAddress());
                userData.setPhone(userAM.getMobile());
                userData.setS_id(userAM.getId());
                userData.setKeyCloakToken(idToken);
                userData = UserDao.insert(userData);

                context.response().putHeader("Cache-Control",
                        "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                context.response().putHeader("Pragma", "no-cache");
                context.response().putHeader("Expires", "0");
                context.put(HANDLER_USER_DATA_RESULT, userData);
                context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                context.response().putHeader("Cache-Control",
                        "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                context.response().putHeader("Pragma", "no-cache");
                context.response().putHeader("Expires", "0");
                sendResponse(context, 200, userData);
            }
        } catch (Exception e1) {
            context.fail(e1);
        }
    }

    private static <T> T nvl(T src, T def) {
        if (src != null)
            return src;
        else
            return def;
    }
    private final static Logger LOGGER = Logger.getLogger(SignInHandler.class.getName());
}
