package vn.onepay.portal.resources.base;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpClient;
import io.vertx.core.http.HttpClientRequest;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.ResourcesResult;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.OneAmUser;
import vn.onepay.portal.resources.user.dto.User;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class SignInHandler implements IConstants {

    //Remove Acceptance by id
    public static void create(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String code = request.getParam(CODE);
                if (code == null || code.isEmpty()) {
                    LOGGER.log(Level.SEVERE, "[VALIDATION SIGN IN ROUTE] => AUTHORIZATION CODE EMPTY");
                    throw new Exception("[VALIDATION SIGN IN ROUTE] => AUTHORIZATION CODE EMPTY");
                }

                String uri = Config.getString("one_am.token_uri", "");
                // create http client;
                HttpClient client = ctx.vertx().createHttpClient();

                JsonObject tokenParam = new JsonObject();
                tokenParam.addProperty(CODE, code);
                tokenParam.addProperty(GRANT_TYPE, "authorization_code");

                Buffer buffer = Buffer.buffer(tokenParam.toString(), "UTF-8");
                String userOneAm = Config.getString("one_am.user", "") + ":" + Config.getString("one_am.password", "");
                String headerAuthorization = "Basic " + Convert.toBase64String(userOneAm.getBytes()).replaceAll("[\r\n]", "");
                HttpClientRequest req = client.requestAbs(HttpMethod.POST, uri);
                req.putHeader("Authorization", headerAuthorization);
                req.putHeader("Accept", APPLICATION_JSON);
                req.putHeader("Content-Length", buffer.length() + "");
                LOGGER.severe("POST to oneam: " + req.absoluteURI() + " content: " + buffer);

                req.handler(httpClientResponse -> {

                    int statusCode = httpClientResponse.statusCode();
                    if (statusCode != 200) {
                        LOGGER.log(Level.INFO, "[ USER SESSION POST LOGIN FAILED 1] => RESULT :" + statusCode + " DESC : " + httpClientResponse.statusMessage());
                        try {
                            throw new Exception("[ USER SESSION POST LOGIN FAILED 1 ] => RESULT :" + statusCode + " DESC : " + httpClientResponse.statusMessage());
                        } catch (Exception e) {
                            ctx.fail(e);
                        }
                    } else {
                        httpClientResponse.bodyHandler(data -> oneAMGetResources(ctx, data.toString("UTF-8"), headerAuthorization, tokenParam, buffer, client));
                    }
                });

                req.write(buffer);
                req.end();

            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * get data resources oneam
     *
     * @param context
     * @param data
     * @param headerAuthorization
     * @param tokenOneAmParameter
     * @param bufferData
     * @param client
     */
    @SuppressWarnings( "deprecation" )
    private static void oneAMGetResources(RoutingContext context, String data, String headerAuthorization, JsonObject tokenOneAmParameter, Buffer bufferData, HttpClient client) {

        JsonParser parser = new JsonParser();
        JsonObject token = parser.parse(data).getAsJsonObject();
        String uriRes = String.format(Config.getString("one_am.resources_uri", ""),
                token.get(ACCESS_TOKEN).getAsString()) + "?"
                + CODE + "=" + tokenOneAmParameter.get(CODE).getAsString() + "&"
                + GRANT_TYPE + "=" + tokenOneAmParameter.get(GRANT_TYPE).getAsString();
        String headerRes = headerAuthorization + " " + "Bearer" + " " + token.get(ACCESS_TOKEN).getAsString();
        HttpClientRequest reqRes = client.requestAbs(HttpMethod.GET, uriRes);
        reqRes.putHeader("Authorization", headerRes);
        reqRes.putHeader("Accept", APPLICATION_JSON);
        reqRes.putHeader("Content-Length", 0 + "");
        
        reqRes.handler(httpClientResponse -> {
            int statusCode = httpClientResponse.statusCode();
            if (statusCode != 200) {
                LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED 2 ] => RESULT :" + statusCode + " DESC : " + httpClientResponse.statusMessage());
                try {
                    throw new Exception("[ USER SESSION POST LOGIN FAILED 2 ] => RESULT :" + statusCode + " DESC : " + httpClientResponse.statusMessage());
                } catch (Exception e) {
                    context.fail(e);
                }
            } else {
                httpClientResponse.bodyHandler(data1 -> {

                    try {
                    ResourcesResult resourcesResult = Util.gson.fromJson(data1.toString("UTF-8"), ResourcesResult.class);
                    User user = UserDao.get(resourcesResult.getResources().getProfile().getId());
                        LOGGER.info("[ USER SESSION GET FROM ONEPORTAL ] => " + user);
                    OneAmUser userAM = resourcesResult.getResources().getProfile();
                        LOGGER.info("[ USER SESSION GET FROM ONEAM ] => " + userAM);

                    if (user != null) {

                        String databaseUser = user.getName() + user.getEmail() + user.getAddress() + user.getPhone() + user.getS_id();
                        String oneAmUser = userAM.getLastName()  + (userAM.getFirstName()== null ? "" : ( " " +userAM.getFirstName()))  + user.getEmail() + userAM.getAddress() + userAM.getMobile() + userAM.getId();

                        String hashUser = "";
                        String hashOneAmUser = "";

                        try {
                            hashUser = Util.hash(databaseUser);
                            hashOneAmUser = Util.hash(oneAmUser);
                        } catch (NoSuchAlgorithmException e) {
                            LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                            try {
                                throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");
                            } catch (Exception e1) {
                                context.fail(e);
                            }
                        } catch (UnsupportedEncodingException e) {
                            LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                                throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");

                        }

                        if (!hashUser.equals(hashOneAmUser)) {
                            User userData = new User();
                            // userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                            String name = userAM.getLastName();
                            if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                                name += " " + userAM.getFirstName();
                            }
                            userData.setName(name);
                            userData.setEmail(userAM.getEmail());
                            userData.setAddress(userAM.getAddress());
                            userData.setPhone(userAM.getMobile());
                            userData.setS_id(userAM.getId());

                            UserDao.update(userData);
                            User user2 = UserDao.get(userAM.getId());

                            context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            context.response().putHeader("Pragma", "no-cache");
                            context.response().putHeader("Expires", "0");
                            context.put(HANDLER_USER_DATA_RESULT, user2);
                            context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                            context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                            context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            context.response().putHeader("Pragma", "no-cache");
                            context.response().putHeader("Expires", "0");
                            sendResponse(context, 200, userData);
                        } else {


                            context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            context.response().putHeader("Pragma", "no-cache");
                            context.response().putHeader("Expires", "0");
                            context.put(HANDLER_USER_DATA_RESULT, user);
                            context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                            context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                            context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            context.response().putHeader("Pragma", "no-cache");
                            context.response().putHeader("Expires", "0");
                            sendResponse(context, 200, user);
                        }
                    } else {
                        User userData = new User();
                        // userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                        String name = userAM.getLastName();
                        if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                            name += " " + userAM.getFirstName();
                        }
                        userData.setName(name);
                        userData.setEmail(userAM.getEmail());
                        userData.setAddress(userAM.getAddress());
                        userData.setPhone(userAM.getMobile());
                        userData.setS_id(userAM.getId());

                        userData = UserDao.insert(userData);



                        context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                        context.response().putHeader("Pragma", "no-cache");
                        context.response().putHeader("Expires", "0");
                        context.put(HANDLER_USER_DATA_RESULT, userData);
                        context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                        context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                        context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                        context.response().putHeader("Pragma", "no-cache");
                        context.response().putHeader("Expires", "0");
                        sendResponse(context, 200, userData);
                    }
                    } catch (Exception e1) {
                        context.fail(e1);
                    }
                });
            }
        });


        reqRes.write(bufferData);
        reqRes.end();
    }

    private final static Logger LOGGER = Logger.getLogger(SignInHandler.class.getName());
}
