package vn.onepay.portal.resources.base.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by anhkh on 6/28/16
 */
public interface IErrors {
    Map<String, ErrorException> ErrorExceptions = new HashMap<>();
    ErrorException INTERNAL_SERVER_ERROR = new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
    ErrorException VALIDATION_ERROR = new ErrorException(400, "VALIDATION_ERROR", "Invalid request", "", "There was a validation issue with your request");
    ErrorException INVALID_FILE_MUST_EXCEL = new ErrorException(500, "INVALID_FILE", "Invalid File must xls or xlsx", "", "Invalid File must xls or xlsx");
    ErrorException MERCHANT_DATA_ERROR = new ErrorException(400, "MERCHANT_DATA_ERROR", "Invalid Merchant Data", "", "There was a validation issue with your merchant data");

    ErrorException AMOUNT_REFUND_ERROR = new ErrorException(400, "AMOUNT_REFUND_ERROR", "Amount Refund Error", "", "Amount Refund Error");

    ErrorException REACH_MAX_EXCEL_ROW = new ErrorException(500, "REACH_MAX_EXCEL_ROW", "The maximum rows allowed in a worksheet may have been exceeded ", "", "The maximum rows allowed in a worksheet may have been exceeded.");

    ErrorException QUERY_ERROR = new ErrorException(500, "QUERY_ERROR", "Query Failed", "", "Query Data Failed");

    ErrorException REFUND_FAILED = new ErrorException(500, "REFUND_FAILED", "Refund Failed", "", "Refund Transaction Failed");

    ErrorException RECHARGE_FAILED = new ErrorException(500, "RECHARGE_FAILED", "Recharge Failed", "", "Recharge Transaction Failed");

    ErrorException DUPLICATE_REFUND_ERROR = new ErrorException(500, "DUPLICATE_REFUND_ERROR", "This approval has already approved|rejected", "", "This approval has already approved|rejected");

    ErrorException DUPLICATE_NOTIFY = new ErrorException(500, "DUPLICATE_NOTIFY", "This paypal notify has been added", "", "This paypal notify has been added");

    ErrorException VOID_REFUND_FAILED = new ErrorException(500, "VOID_REFUND_FAILED", "Void Refund Failed", "", "Void Refund Transaction Failed");

    ErrorException VOID_PURCHASE_FAILED = new ErrorException(500, "VOID_PURCHASE_FAILED", "Void Purchase Failed", "", "Void Purchase Transaction Failed");

    ErrorException RESOURCE_NOT_FOUND = new ErrorException(404, "RESOURCE_NOT_FOUND", "Resource Not Found", "", "Resource Not Found");

    ErrorException BIN_NOT_FOUND = new ErrorException(404, "BIN_NOT_FOUND", "Bin Number Not Found", "", "Bin Number Not Found");

    ErrorException OUT_OF_DATE_TRANSACTION_ERROR = new ErrorException(500, "OUT_OF_DATE_TRANSACTION", "Transaction is out of date", "", "Transaction is out of date");

    ErrorException SEARCH_TOO_LARGE_ERROR = new ErrorException(500, "SEARCH_TOO_LARGE_ERROR", "This search you have requested is too large.  Please select information or narrow date range  and retry.", "", "This search you have requested is too large.  Please select information or narrow date range and retry.");

    ErrorException UNAUTHORIZED = new ErrorException(401, "UNAUTHORIZED", "Unauthorized", "", "Unauthorized");
    ErrorException FORBIDDEN = new ErrorException(403, "FORBIDDEN", "Forbidden", "", "Forbidden");
    ErrorException EXPIRED_AUTHORIZATION = new ErrorException(401, "EXPIRED_AUTHORIZATION", "Authorization is expired", "", "Authorization is expired");
    ErrorException INVALID_AUTHORIZATION_ALGORITHM = new ErrorException(401, "INVALID_AUTHORIZATION_ALGORITHM", "Invalid authorization algorithm", "", "Invalid authorization algorithm");
    ErrorException INVALID_AUTHORIZATION_REGION = new ErrorException(401, "INVALID_AUTHORIZATION_REGION", "Invalid authorization region", "", "Invalid authorization region");
    ErrorException INVALID_AUTHORIZATION_SERVICE = new ErrorException(401, "INVALID_AUTHORIZATION_SERVICE", "Invalid authorization service", "", "Invalid authorization service");
    ErrorException INVALID_AUTHORIZATION_OWS_REQUEST = new ErrorException(401, "INVALID_AUTHORIZATION_OWS_REQUEST", "Invalid authorization ows request", "", "Invalid authorization ows request");
    ErrorException INVALID_AUTHORIZATION_ACCESS_KEY_ID = new ErrorException(401, "INVALID_AUTHORIZATION_ACCESS_KEY_ID", "Invalid authorization access key id", "", "Invalid authorization access key id");
    ErrorException INVALID_AUTHORIZATION_SIGNATURE = new ErrorException(401, "INVALID_AUTHORIZATION_SIGNATURE", "Invalid authorization signature", "", "Invalid authorization signature");

    ErrorException INVALID_JSON_FORMAT = new ErrorException(400, "INVALID_JSON_FORMAT", "Invalid json format", "", "Invalid json format");
    ErrorException INVALID_MERCHANT = new ErrorException(400, "INVALID_MERCHANT", "Invalid merchant", "", "Invalid merchant");
    ErrorException INVALID_AMOUNT = new ErrorException(400, "INVALID_AMOUNT", "Invalid amount", "", "Invalid amount");
    ErrorException INVALID_CURRENCY = new ErrorException(400, "INVALID_CURRENCY", "Invalid currency", "", "Invalid currency");
    ErrorException INVALID_ORDER_INFORMATION = new ErrorException(400, "INVALID_ORDER_INFORMATION", "Invalid order information", "", "Invalid order information");
    ErrorException INVALID_DELIVERY_LATITUDE = new ErrorException(400, "INVALID_DELIVERY_LATITUDE", "Invalid delivery latitude", "", "Invalid delivery latitude");
    ErrorException INVALID_DELIVERY_LONGITUDE = new ErrorException(400, "INVALID_DELIVERY_LONGITUDE", "Invalid delivery longitude", "", "Invalid delivery longitude");
    ErrorException INVALID_DELIVERY_INFORMATION = new ErrorException(400, "INVALID_DELIVERY_INFORMATION", "Invalid delivery information", "", "Invalid delivery information");
    ErrorException FINANCIAL_SERVER_ERROR = new ErrorException(500, "FINANCIAL_SERVER_ERROR", "An financial service error has occurred", "", "Resend the request at another time.");
    ErrorException INTERNATIONAL_AUTHPAYMENT_SERVER_ERROR = new ErrorException(500, "INTERNATIONAL_AUTHPAYMENT_SERVER_ERROR", "An International AuthPayment service error has occurred", "", "Resend the request at another time.");
    ErrorException DOMESTIC_SERVER_ERROR = new ErrorException(500, "DOMESTIC_SERVER_ERROR", "An domestic service error has occurred", "", "Resend the request at another time.");
    ErrorException INVALID_MOBILE_NUMBER = new ErrorException(400, "VALIDATION_ERROR", "Invalid request", "", "There was a validation issue with your request");
    ErrorException INVALID_EMAIL_ADDRESS = new ErrorException(400, "INVALID_EMAIL_ADDRESS", "Invalid email address", "", "Invalid email address");
    ErrorException INVALID_USER_PARTNER = new ErrorException(500, "INVALID_USER_PARTNER", "Invalid User", "", "Cannot find partner with this user");
    ErrorException INVALID_USER_PASSSAME = new ErrorException(400, "VALIDATION_ERROR", "Your new password must be different.", "", "Your new password must be different");
    ErrorException PASS_NOT_FOUND = new ErrorException(404, "PASS_NOT_FOUND", "The old password you entered was incorrect.", "", "The old password you entered was incorrect.");
    ErrorException INVALID_USER_EMAIL_EXISTED = new ErrorException(409, "INVALID_USER_EMAIL_EXISTED", "This email address already exists", "", "This email address already exists");
    ErrorException INVALID_USER_PHONE_EXISTED = new ErrorException(409, "INVALID_USER_PHONE_EXISTED", "This mobile number already exists", "", "This mobile number already exists");
    ErrorException INVALID_SENT_EMAIL = new ErrorException(500, "INVALID_SENT_EMAIL", "Error when sent mail", "", "Error when sent mail");
    ErrorException STATISTICS_SERVER_ERROR = new ErrorException(500, "STATISTICS_SERVER_ERROR", "An statistics service error has occurred", "", "Resend the request at another time.");
    ErrorException NO_DATA_FOUND = new ErrorException(400, "NO_DATA_FOUND", "There is no data match your request", "", "No data found");
    ErrorException INVALID_CONTRACT = new ErrorException(400, "INVALID_CONTRACT", "Invalid Contract", "", "Invalid Contract");
    ErrorException FIXED_DEPOSIT_EXISTED = new ErrorException(400, "FIXED_DEPOSIT_EXISTS", "Fixed Deposit already exists", "", "Fixed Deposit already exists");
    ErrorException BALANCE_NOT_ENOUGH = new ErrorException(400, "BALANCE_NOT_ENOUGH", "Merchant does not have enough money to refund", "", "Merchant does not have enough money to refund");
    ErrorException MERCHANT_TRANSFER_AMOUNT_LT_ZERO = new ErrorException(400, "MERCHANT_TRANSFER_AMOUNT_LT_ZERO", "Invalid transfer amount. Amount must greater than 0", "", "Invalid transfer amount. Amount must greater than 0");
    ErrorException ADV_ACCOUNT_IN_PROCESSING = new ErrorException(406, "ADV_ACCOUNT_IN_PROCESSING", "This advance account is in processing. Please try again after 60 seconds.", "", "This advance account is in processing. Please try again after 60 seconds.");
    ErrorException BANK_USED = new ErrorException(400, "BANK_USED", "This bank has been used", "", "This bank has been used");
    ErrorException TOPUP_BANK_ERROR = new ErrorException(500, "TOPUP_BANK_ERROR", "This Bank is inactive can not topup. Please try again.", "", "This Bank is disable can not topup. Please try again.");
    ErrorException TOPUP_MERCHANT_ERROR = new ErrorException(500, "TOPUP_MERCHANT_ERROR", "This Merchant is inactive can not topup. Please try again.", "", "This Merchant is inactive can not topup. Please try again.");
    ErrorException MERCHANT_FEE_INSUFFICIENT_FUND = new ErrorException(500, "MERCHANT_FEE_INSUFFICIENT_FUND", "Insufficient fund", "", "Insufficient fund");
    ErrorException MERCHANT_FEE_ACCOUNT_INACTIVE = new ErrorException(500, "MERCHANT_FEE_ACCOUNT_INACTIVE", "This Account is inActive can not approval fee", "", "This Account is inActive can not approval fee");
    ErrorException MERCHANT_FEE_ERROR = new ErrorException(500, "MERCHANT_FEE_ERROR", "This Merchant is inactive can not approval fee. Please try again.", "", "This Merchant is inactive can not approval fee. Please try again.");
    ErrorException ACCOUNT_NUMBER_NOT_MATCH = new ErrorException(500, "ACCOUNT_NUMBER_NOT_MATCH", "Account number is not match. Please check and try again.", "", "Account number is not match. Please check and try again.");
    ErrorException STATEMENT_EXISTED = new ErrorException(500, "STATEMENT_EXISTED", "Statement is existed. Please check and try again.", "", "Statement is existed. Please check and try again.");
    ErrorException INVALID_STATEMENT_STATE = new ErrorException(500, "INVALID_STATEMENT_STATE", "Statement state is invalid. Please check and try again.", "", "Statement state is invalid. Please check and try again.");
    ErrorException CREATE_STATEMENT_FAIL = new ErrorException(500, "CREATE_STATEMENT_FAIL", "Create statement fail. Please try again.", "", "Create statement fail. Please try again.");
    ErrorException STATEMENT_DATA_IMPORTED = new ErrorException(500, "STATEMENT_DATA_IMPORTED", "Statement data is imported. Please re-create statement.", "", "Statement data is imported. Please re-create statement.");
    ErrorException IMPORT_DATA_FAILED = new ErrorException(500, "IMPORT_DATA_FAILED", "Import statement data fail. Please check with technical team.", "", "Import statement data fail. Please check with technical team.");
    ErrorException REPORT_EXISTED = new ErrorException(500, "REPORT_EXISTED", "Report is existed. Please check and try again.", "", "Report is existed. Please check and try again.");
    ErrorException REPORT_DATA_NOT_FOUND = new ErrorException(500, "REPORT_DATA_NOT_FOUND", "Report data is not found. Please check and try again.", "", "Report is not found. Please check and try again.");
    ErrorException DATA_NOT_CREATED = new ErrorException(500, "DATA_NOT_CREATED", "Statement state is invalid. Please check with technical team.", "", "Statement state is invalid. Please check with technical team.");
    ErrorException LIST_IMPORT_IS_EMPTY = new ErrorException(400, "LIST_IMPORT_IS_EMPTY", "Dữ liệu không hợp lệ", "", "Dữ liệu không hợp lệ");
    ErrorException MERCHANT_PROFILE_EXISTED = new ErrorException(500, "MERCHANT_PROFILE_EXISTED", "Merchant Profile Name already exists.", "", "Merchant Profile Name already exists.");
    ErrorException DISPUTE_NOTE_EXISTED = new ErrorException(500, "DISPUTE_NOTE_EXISTED", "Dispute is not existed.", "", "Dispute is not existed.");
    ErrorException DISPUTE_ALREADY_EXIST = new ErrorException(500, "DISPUTE_ALREADY_EXIST", "Disputes already exist.", "", "Disputes already exist.");
    ErrorException DISPUTE_CASE_ALREADY_EXIST = new ErrorException(500, "DISPUTE_CASE_ALREADY_EXIST", "Dispute case already exist.", "", "Dispute case already exist.");
    ErrorException DUPLICATE = new ErrorException(500, "DUPLICATE", "DUPLICATE", "This data has been duplicate", "This data has been duplicate");
}
