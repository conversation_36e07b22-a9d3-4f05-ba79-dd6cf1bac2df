package vn.onepay.portal.resources.base;

import io.vertx.core.http.HttpMethod;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.user.dto.User;

import java.util.logging.Logger;

public class AuthenticationHandler implements IConstants {

    private static Logger logger = Logger.getLogger(AuthenticationHandler.class.getName());

    public static void create(RoutingContext rc) {
        final HttpServerRequest serverRequest = rc.request();
        if (serverRequest.method() == HttpMethod.DELETE && serverRequest.path().contains("/user/session")) {
            rc.next();
            return;
        }

        if ((serverRequest.path().equals("/") || serverRequest.path().endsWith("/app.js")) && serverRequest.method().equals(HttpMethod.GET)) {
            rc.next();
            return;
        }

        // UserSession userSession = new UserSession();
        User profile = new User();
        profile.setName(rc.user().principal().getString("name"));
        profile.setN_id(Integer.parseInt(rc.user().principal().getString("sub")));
        profile.setEmail(rc.user().principal().getString("email"));
        profile.setPhone(rc.user().principal().getString("phone"));
        profile.setAddress(rc.user().principal().getString("address"));
        profile.setS_id(rc.user().principal().getString("sid"));
        profile.setKeyCloakToken(rc.user().principal().getString("keyCloakToken"));
        // userSession.setProfile(profile);

        rc.put(X_USER_ID, profile.getN_id());
        rc.put(S_USER_ID, rc.user().principal().getString("sid"));
        logger.info("keyCloakToken: " + rc.user().principal().getString("keyCloakToken"));
        logger.info("Profile Info: " + profile);
        rc.put(HANDLER_USER_DATA_RESULT, profile);
        rc.next();
    }
}
