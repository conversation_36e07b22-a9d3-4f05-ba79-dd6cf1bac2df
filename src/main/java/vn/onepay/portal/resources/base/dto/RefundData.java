package vn.onepay.portal.resources.base.dto;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class RefundData {
    public static enum Type {

        DOMESTIC(0),

        INTERNATIONAL(1),

        QR(2),

        REFUND_CAPTURE(3);

        public Integer value;

        private Type(Integer value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public static enum NUMBER_OF_STEP_CONFIRMATION {
        ZERO(0),

        ONE(1),

        TWO(2);

        public Integer value;

        private NUMBER_OF_STEP_CONFIRMATION(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    private String merchant_id;
    private String transaction_reference;
    private String refund_reference;
    private double amount;
    private Integer type;
    private String currency;
    private String note;
    private String user_name;
    private double actualRefundAmount;
    private String stringHash;

    public double getActualRefundAmount() {
        return actualRefundAmount;
    }

    public void setActualRefundAmount(double actualRefundAmount) {
        this.actualRefundAmount = actualRefundAmount;
    }

    public RefundData() {
    }

    public String getStringHash() {
        return stringHash;
    }

    public void setStringHash(String stringHash) {
        this.stringHash = stringHash;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public RefundData(String merchant_id, String transaction_reference, String refund_reference, double amount,
            Integer type, String currency, String note, double actualRefundAmount) {
        this.merchant_id = merchant_id;
        this.transaction_reference = transaction_reference;
        this.refund_reference = refund_reference;
        this.amount = amount;
        this.type = type;
        this.currency = currency;
        this.setNote(note);
        this.actualRefundAmount = actualRefundAmount;
    }

    public String getRefund_reference() {
        return refund_reference;
    }

    public void setRefund_reference(String refund_reference) {
        this.refund_reference = refund_reference;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getTransaction_reference() {
        return transaction_reference;
    }

    public void setTransaction_reference(String transaction_reference) {
        this.transaction_reference = transaction_reference;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

}
