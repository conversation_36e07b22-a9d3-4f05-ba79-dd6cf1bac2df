package vn.onepay.portal.resources.outsource.dto;

import java.util.List;

import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartner;

public class EPartnerReturn {
    private double total;
    private List<EPartner> list;

    public double getTotal() {
        return this.total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public List<EPartner> getList() {
        return this.list;
    }

    public void setList(List<EPartner> list) {
        this.list = list;
    }
}
