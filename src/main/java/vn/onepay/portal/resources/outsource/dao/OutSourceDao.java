package vn.onepay.portal.resources.outsource.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.outsource.dto.EItem;
import vn.onepay.portal.resources.outsource.dto.EMerchant;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EAdvConfig;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EGroupCategory;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartner;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartnerMM;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartnerMerchantId;
public class OutSourceDao extends Db implements IConstants {
    private static final Logger logger = Logger.getLogger(OutSourceDao.class.getName());

    private static final String GET_PARTNER_MM_ALL = "{? = call onepartner.outsource_l_partner_mm(?)}";
    private static final String GET_PARTNER_ALL = "{? = call onefin.outsource_list_partner(?,?,?)}";
    private static final String GET_PARTNER_MM_ALL_MERCHANT_ID = "{? = call onepartner.outsource_l_partner_merchantid(?,?,?)}";
    private static final String GET_MERCHANT_ALL = "{? = call onefin.outsource_list_merchant(?,?,?,?)}";
    private static final String GET_BANK_PARTER = "{? = call onefin.outsource_bank_partner(?,?)}";
    private static final String GET_GROUP_CATEGORY = "{? = call onefin.outsource_list_group_category(?)}";
    private static final String GET_ADV_CONFIG = "{? = call onefin.OUTSOURCE_GET_ADV_CONFIG(?,?,?)}";
    

    private static List<EPartnerMM> getPartnerMM()
            throws Exception {
        List<EPartnerMM> lst = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_MM_ALL);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EPartnerMM item = new EPartnerMM();
                item.setPartnerId(rs.getInt("N_PARTNER_ID"));
                item.setNUserId(rs.getInt("n_user_id"));
                item.setSUserId(rs.getString("S_USER_ID"));
                lst.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lst;

    }

    public static List<EPartner> getPartner(int partnerId, String name)
            throws Exception {
        List<EPartnerMM> lstPartnerMM = getPartnerMM();
        List<EPartner> lstPartner = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_PARTNER_ALL);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setInt(3, partnerId);
            cs.setString(4, name);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EPartner item = new EPartner();
                item.setId(rs.getDouble("N_ID"));
                item.setName(rs.getString("S_PARTNER_NAME"));
                item.setShortName(rs.getString("S_SHORT_NAME"));
                item.setProvince(rs.getString("s_tinh"));
                item.setCategory(rs.getString("S_GROUP_NAME"));
                item.setMm(
                        lstPartnerMM.stream().filter(m -> m.getPartnerId() == item.getId()).map(EPartnerMM::getSUserId)
                                .collect(Collectors.toList()));

                EPartner itemReal = lstPartner.stream()
                        .filter(t -> t.getId() == item.getId())
                        .findAny()
                        .orElse(null);
                if (itemReal == null)
                    lstPartner.add(item);
                else {
                    if (!item.getCategory().equals("Others"))
                        itemReal.setCategory(item.getCategory());
                }
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lstPartner;

    }

    /**
    * Lấy danh sách All partner
    *
    * @param partnerId tìm theo partner id
    * @param name tìm theo partner name hoặc partner short name
    * @return trả về list danh sách
    */
    public static List<EPartnerMerchantId> getPartnerAll(int partnerId, String name)
            throws Exception {
      
        List<EPartnerMerchantId> lstPartner = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_MM_ALL_MERCHANT_ID);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setInt(3, partnerId);
            cs.setString(4, name);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EPartnerMerchantId item = new EPartnerMerchantId();
                item.setId(rs.getDouble("N_ID"));
                item.setName(rs.getString("S_PARTNER_NAME"));
                item.setShortName(rs.getString("S_SHORT_NAME"));
                item.setCategory(rs.getString("S_NGANH_NGHE"));
                item.setWebsite(rs.getString("S_WEBSITE"));
                item.setAddress_street(rs.getString("S_ADDRESS_LINE1"));
                item.setAddress_country(rs.getString("S_COUNTRY"));
                item.setAddress_state(rs.getString("S_STATE"));
                item.setAddress_province(rs.getString("S_TINH_THANH"));
                item.setAddress_postcode(rs.getString("S_POST_CODE"));                
                lstPartner.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lstPartner;

    }

    public static List<EMerchant> getMerchant(int partnerId, String merchantId, String merchantName)
            throws Exception {
        List<EMerchant> lstMerchant = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_MERCHANT_ALL);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setInt(3, partnerId);
            cs.setString(4, merchantId);
            cs.setString(5, merchantName);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            while (rs != null && rs.next()) {
                EMerchant item = new EMerchant();
                item.setPartnerId(rs.getInt("N_PARTNER_ID"));
                item.setMerchantId(rs.getString("S_MERCHANT_ID"));
                item.setMerchantName(rs.getString("S_MERCHANT_NAME"));
                if (item.getMerchantName() == null) {
                    item.setMerchantName(item.getMerchantId());
                } else {
                    if (item.getMerchantName().trim().isEmpty())
                        item.setMerchantName(item.getMerchantId());
                }
                Date activeDate = rs.getDate("ActiveDate");
                item.setActiveDate(sdf.format(activeDate));
                item.setStatus(rs.getInt("N_STATUS"));
                lstMerchant.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lstMerchant;

    }

    public static List<EItem> getBankPartner(String service)
            throws Exception {
        List<EItem> lstItem = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118();
            cs = con.prepareCall(GET_BANK_PARTER);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setString(3, service);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EItem item = new EItem();
                if(service.equals("BNPL") || service.equals("VIETQR"))
                    item.setId(rs.getString("S_PARTNER_ID").toUpperCase());
                else
                    item.setId(rs.getString("N_ID"));
                item.setName(rs.getString("S_PARTNER_NAME"));
                lstItem.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lstItem;

    }

    public static List<EGroupCategory> getGroupCategory()
            throws Exception {
        List<EGroupCategory> lst = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118();
            cs = con.prepareCall(GET_GROUP_CATEGORY);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EGroupCategory item = new EGroupCategory();
                item.setGroupSummary(rs.getString("group_summary"));
                item.setGroupDetail(rs.getString("group_detail"));
                lst.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lst;

    }

    public static List<EItem> getIssuerDD() throws Exception {
        String DD_GET_CARD_LIST = "{call PKG_IPORTAL_DIRECT_DEBIT.get_bank_list(?,?,?)}";
        ResultSet rsList = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;

        List<EItem> listBank = new ArrayList<>();

        try {
            con = getConnection118Report();
            cs = con.prepareCall(DD_GET_CARD_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();

            rsList = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            }
            while (rsList != null && rsList.next()) {
                EItem bankDto = new EItem();
                bankDto.setName(Util.getColumnString(rsList, "S_NAME"));
                bankDto.setId(Util.getColumnString(rsList, "S_ID"));
                listBank.add(bankDto);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rsList, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return listBank;
    }

    public static List<EAdvConfig> getAdvConfig(String partnerId, String merchantId)
            throws Exception {
        List<EAdvConfig> lst = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118();
            cs = con.prepareCall(GET_ADV_CONFIG);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setInt(3, Integer.parseInt(partnerId));
            cs.setString(4, merchantId);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);

            while (rs != null && rs.next()) {
                EAdvConfig item = new EAdvConfig();
                item.setAdvConfig(rs.getInt("n_id"));
                item.setContractCode(rs.getString("s_contract_code"));
                item.setUrl("payment2-advance-config/" + item.getAdvConfig());
                lst.add(item);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return lst;

    }
}
