package vn.onepay.portal.resources.acquirer_bin_group.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.acquirer_bin_group.dto.AcquirerBinGroupDto;
import vn.onepay.portal.resources.audit_trail.dao.AuditTrailDAO;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;
import vn.onepay.portal.resources.audit_trail.response.AuditTrail;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;

public class AcquirerBinGroupDao extends Db implements IConstants {

    public static Map<String, Object> insertAcqBinGroup(JsonObject mIn) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONECREDIT.PKG_ABG#INSERT(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, mIn.getString("groupName", BLANK));
            cs.setObject(4, mIn.getString("bin", BLANK));
            cs.setObject(5, mIn.getString("bankMerchantId", BLANK));
            cs.setObject(6, mIn.getString("sourceName", BLANK));
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 500) {
                throw new Exception(error);
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("status", nerror);
        result.put("message", error);
        AuditTrailRequest auditRq = new AuditTrailRequest();
        auditRq.setCode(UUID.randomUUID().toString());
        auditRq.setModule("IPORTAL_SERVICE");
        auditRq.setUserId(mIn.getString("userId", BLANK));
        auditRq.setRequestId(mIn.getString("requestId", BLANK));
        auditRq.setRealIp(mIn.getString("realIp", BLANK));
        auditRq.setSource("SYSTEM_MANAGEMENT_ACQ_BIN_GROUP");
        auditRq.setType("CREATE");
        auditRq.setAction("ACQ_BIN_GROUP_CREATED");
        String userEmail = mIn.getString("email", BLANK);
        auditRq.setActor(userEmail);
        String content = userEmail.concat(" đã thêm thành công group name : ").concat(mIn.getString("groupName", BLANK))
                .concat(" và bin : ").concat(mIn.getString("bin", BLANK));
        auditRq.setContent(content);
        auditRq.setNewValueString(mIn.toString());
        AuditTrailDAO.insertAuditTrail(auditRq);
        return result;
    }

    public static Map<String, Object> deleteAcqBinGroup(JsonObject mIn) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONECREDIT.PKG_ABG#DELETE(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, mIn.getString("groupName", BLANK));
            cs.setObject(4, mIn.getString("bin", BLANK));
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                throw new Exception(error);
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("status", nerror);
        result.put("message", error);
        AuditTrailRequest auditRq = new AuditTrailRequest();
        auditRq.setCode(UUID.randomUUID().toString());
        auditRq.setModule("IPORTAL_SERVICE");
        auditRq.setUserId(mIn.getString("userId", BLANK));
        auditRq.setRequestId(mIn.getString("requestId", BLANK));
        auditRq.setRealIp(mIn.getString("realIp", BLANK));
        auditRq.setSource("SYSTEM_MANAGEMENT_ACQ_BIN_GROUP");
        auditRq.setType("DELETE");
        auditRq.setAction("ACQ_BIN_GROUP_DELETED");
        String userEmail = mIn.getString("email", BLANK);
        auditRq.setActor(userEmail);
        String content = userEmail.concat(" đã xoá thành công group name : ").concat(mIn.getString("groupName", BLANK))
                        .concat(" và bin : ").concat(mIn.getString("bin", BLANK));
        auditRq.setContent(content);
        auditRq.setOldValueString(mIn.toString());
        AuditTrailDAO.insertAuditTrail(auditRq);
        return result;
    }

    public static Map<String, Object> searchAcquirerBinGroup(JsonObject mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<AcquirerBinGroupDto> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total;
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONECREDIT.PKG_ABG#SEARCH(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setObject(5, mIn.getString(KEYWORD));
            cs.setInt(6, Integer.parseInt(mIn.getString(PAGE)));
            cs.setInt(7, Integer.parseInt(mIn.getString(PAGESIZE)));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB PKG_ABG#SEARCH: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(buildAcquirerBinGroup(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        result.put("status", nerror);
        return result;
    }


    public static List<String> getListGroupName() throws SQLException {
        List<String> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONECREDIT.PKG_ABG#LIST_GROUP_NAME(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB PKG_ABG#LIST_GROUP_NAME: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(Util.getColumnString(rs, "S_GROUP_NAME"));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }

    private static AcquirerBinGroupDto buildAcquirerBinGroup(ResultSet rs) throws SQLException {
        return new AcquirerBinGroupDto(
                Util.getColumnInteger(rs, "R_NUM"),
                Util.getColumnString(rs, "S_GROUP_NAME"),
                Util.getColumnString(rs, "S_BIN"),
                Util.getColumnString(rs, "S_BANK_MERCHANT_ID"),
                Util.getColumnString(rs, "S_SOURCE_NAME")
        );
    }
}
