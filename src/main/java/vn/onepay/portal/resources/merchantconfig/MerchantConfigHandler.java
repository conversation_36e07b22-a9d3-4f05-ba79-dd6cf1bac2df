package vn.onepay.portal.resources.merchantconfig;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.merchantconfig.dto.BankConfigDto;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MerchantConfigHandler  {
    private static Logger logger = Logger.getLogger(MerchantConfigHandler.class.getName());

    public static void getConfigMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            int id = Integer.parseInt(request.getParam("id"));
            Map<String,Object> temp = new HashMap<>();
            try {
                List<MerchantConfigDto> config = MerchantConfigDao.getListEmail(id);
                temp.put("ListEmail",config);
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void updateInsertMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
            HttpServerRequest request = ctx.request();
            JsonObject body = ctx.getBodyAsJson();
            MerchantConfigDto config = new MerchantConfigDto();
            config.setMerchantId(body.getString("merchantId"));
            config.setEmail(body.getString("email"));
            config.setActive(body.getString("active"));
            config.setState("wait_for_approve");
            config.setService(body.getString("service"));
            int id = Integer.parseInt(request.getParam("id"));
            List<MerchantConfigDto> listConfig = MerchantConfigDao.getListEmailbyMerchant(id,config.getMerchantId());
            if (listConfig==null ||listConfig.size()==0){
                MerchantConfigDao.updatePaygate(config.getService(),id);
                }
            boolean check = true;
            for(int i = 0;listConfig.size()>i;i++){
                MerchantConfigDto findConfig = listConfig.get(i);

                    if (findConfig.getMerchantId().toLowerCase().equals(config.getMerchantId().toLowerCase())&&findConfig.getService().equals(config.getService()) && (findConfig.getState().toLowerCase().equals("wait_for_approve") || findConfig.getState().toLowerCase().equals("wait_for_approve_delete"))) {
                        temp = MerchantConfigDao.updateConfig(config, id);
                        check = false;
                        break;
                    }

            }
            if (check){
                temp = MerchantConfigDao.insertConfig(config, id);
            }
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }
    public static void deleteMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();
                MerchantConfigDto config = new MerchantConfigDto();
                config.setMerchantId(body.getString("merchantId"));
                config.setEmail(body.getString("email"));
                config.setActive("ACTIVATED");
                config.setState("wait_for_approve_delete");
                config.setService(body.getString("service"));
                int id = Integer.parseInt(request.getParam("id"));
                List<MerchantConfigDto> listConfig = MerchantConfigDao.getListEmailbyMerchant(id,config.getMerchantId());
                boolean check = true;
                for(int i = 0;listConfig.size()>i;i++){
                    MerchantConfigDto findConfig = listConfig.get(i);
                    if (findConfig.getMerchantId().toLowerCase().equals(config.getMerchantId().toLowerCase()) && findConfig.getService().equals(config.getService()) && (findConfig.getState().toLowerCase().equals("wait_for_approve")||findConfig.getState().toLowerCase().equals("wait_for_approve_delete"))){
                        temp = MerchantConfigDao.updateConfig(config,id);
                        check = false;
                        break;
                    }
                }
                if (check) temp = MerchantConfigDao.insertConfig(config,id);
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    public static void approvalMerchantConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                HttpServerRequest request = ctx.request();
                int id = Integer.parseInt(request.getParam("id"));
                JsonObject body = ctx.getBodyAsJson();
                String merchantId = body.getString("merchantId");
                String action = body.getString("action");//approve or reject
                String state = body.getString("state");
                String email = body.getString("email");
                String service = body.getString("service");
                if (action.equals("reject")){
                    temp = MerchantConfigDao.deleteConfigDraf(id,merchantId,email,state);
                }else{
                    if (state.equals("wait_for_approve")){
                    temp = MerchantConfigDao.updateStateConfig(id,merchantId,"approved","draf_approved",email, service);
                    temp = MerchantConfigDao.updateStateConfig(id,merchantId,state,"approved",email, service);
                }
                    else if (state.equals("wait_for_approve_delete")){
                    temp = MerchantConfigDao.updateStateConfig(id,merchantId,"approved","deleted",email, service);
                    temp = MerchantConfigDao.deleteConfigDraf(id,merchantId,email,"wait_for_approve_delete");
                    }
                }
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }


    public static void getMerchantIdNotInConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                int partnerId = Integer.parseInt(request.getParam("id"));
                String service = request.getParam("service");

                sendResponse(ctx, 200,MerchantConfigDao.listMerchantIdNotInConfig(partnerId, service));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getConfigBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String,Object> temp = new HashMap<>();
            try {
                List<BankConfigDto> config = MerchantConfigDao.getListBank();
                temp.put("ListBank", config);
            } catch (Exception e) {
                ctx.fail(e);
            }
            sendResponse(ctx, 200,temp);
        }, false, null);
    }

    public static void getAcceptInstrumentByMerchantId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            HttpServerRequest request = ctx.request();
            String merchantId = request.getParam("merchantId");
            Map<String, String> temp = new HashMap<>();
            String acceptInstrument;
            try {
                acceptInstrument = MerchantConfigDao.getAcceptInstrumentByMid(merchantId);
                temp.put("accept_instrument", acceptInstrument);
            } catch (Exception e) {
                e.printStackTrace();
            }
            sendResponse(ctx, 200, temp);
        }, false, null);
    }
}
