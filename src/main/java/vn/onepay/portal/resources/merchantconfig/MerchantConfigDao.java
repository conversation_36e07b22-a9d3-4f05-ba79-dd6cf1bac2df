package vn.onepay.portal.resources.merchantconfig;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantconfig.dto.BankConfigDto;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MerchantConfigDao extends Db implements IConstants {
    public static List<MerchantConfigDto> getListEmail(int id) throws Exception {
        Exception exception = null;
        List<MerchantConfigDto> mailConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_EMAIL(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4,id);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    mailConfig.add(blindEmail(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return mailConfig;
    }
    public static List<MerchantConfigDto> getListEmailbyMerchant(int id,String merchantId) throws Exception {
        Exception exception = null;
        List<MerchantConfigDto> mailConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.GET_EMAIL_BY_MERCHANT(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4,id);
            cs.setString(5,merchantId);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    mailConfig.add(blindEmail(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return mailConfig;
    }
    public static Map<String,Object> updateConfig(MerchantConfigDto config,int id) throws Exception {
        Exception exception = null;
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3,id);
            cs.setString(4,config.getMerchantId());
            cs.setString(5,config.getEmail());
            cs.setString(6,config.getActive());
            cs.setString(7,config.getService());
            cs.setString(8,config.getState());
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("error",error + nerror);
        return data;
    }
    public static Map<String,Object> updateStateConfig(int id, String merchantId, String state,String stateChange,String email, String service) throws Exception {
        Exception exception = null;
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.APPROVAL_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3,state);
            cs.setInt(4,id);
            cs.setString(5,merchantId);
            cs.setString(6,stateChange);
            cs.setString(7,email);
            cs.setString(8,service);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("error",error + nerror);
        return data;
    }
    public static Map<String,Object> deleteConfigDraf(int id, String merchantId,String email,String state) throws Exception {
        Exception exception = null;
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.DELETE_EMAIL_DRAF(?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3,id);
            cs.setString(4,merchantId);
            cs.setString(5,email);
            cs.setString(6,state);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("error",error + nerror);
        return data;
    }
    public static Map<String,Object> insertConfig(MerchantConfigDto config,int id) throws Exception {
        Exception exception = null;
        Map<String,Object> data = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.INSERT_EMAIL(?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3,id);
            cs.setString(4,config.getMerchantId());
            cs.setString(5,config.getEmail());
            cs.setString(6,config.getActive());
            cs.setString(7, config.getService());
            cs.setString(8, config.getState());
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        data.put("error",error + nerror);
        return data;
    }

    public static BaseList<List<Map>>listMerchantIdNotInConfig(Integer partnerId, String service) throws Exception{
        Exception exception = null;
        BaseList result = new BaseList();
        List<Map> merchantIdList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.get_merchant_id_not_in_config(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4,partnerId);
            cs.setString(5,service);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get get_merchant_id_not_in_config error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String,Object> merchantcf = new HashMap<>();
                    merchantcf.put("merchantId",rs.getString("S_MERCHANT_ID"));
                    merchantcf.put("service",rs.getString("S_PAYGATE"));
                    merchantIdList.add(merchantcf);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(merchantIdList);
        return result;
    }
    public static void updatePaygate(String payGate,int id) throws Exception{
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call PKG_ONEFIN2.UPDATE_INDEBTEDNESS_PAYGATE(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3,payGate);
            cs.setInt(4,id);
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.severe("DB get get_merchant_id_not_in_config error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static List<BankConfigDto> getListBank() throws Exception {
        Exception exception = null;
        List<BankConfigDto> bankConfig = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call PKG_CONFIG_MERCHANT.get_bank_list(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(3);
            int nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    bankConfig.add(blindBank(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return bankConfig;
    }

    public static String getAcceptInstrumentByMid(String merchantId) throws Exception {
        Exception exception = null;
        String acceptInstrument = "";
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call PKG_CONFIG_MERCHANT.get_accept_instrument_by_mid(?,?,?,?)}");
            cs.setString(1, merchantId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.executeQuery();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nerror == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    acceptInstrument = rs.getString("s_accept_instruments");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return acceptInstrument;
    }

    private static MerchantConfigDto blindEmail(ResultSet rs) throws SQLException {
        MerchantConfigDto config = new MerchantConfigDto();
        config.setMerchantId(rs.getString("S_MERCHANT_ID"));
        config.setCurrency(rs.getString("S_CURRENCY"));
        config.setEmail(rs.getString("S_EMAIL"));
        config.setState(rs.getString("S_STATE"));
        config.setService(rs.getString("S_PAY_CHANNEL"));
        config.setActive(rs.getString("S_ACTIVE"));
        config.setApprovedDate(Util.getColumnTimeStamp(rs, "D_APPROVE"));
        config.setCreatedDate(Util.getColumnTimeStamp(rs, "D_CREATE"));
        config.setUpdatedDate(Util.getColumnTimeStamp(rs, "D_UPDATE"));
        return config;
    }

    private static BankConfigDto blindBank(ResultSet rs) throws SQLException {
        BankConfigDto config = new BankConfigDto();
        config.setId(rs.getInt("N_ID"));
        config.setCard_list(rs.getString("S_CARD_LIST"));
        config.setSwift_code(rs.getString("S_SWIFT_CODE"));
        config.setName(rs.getString("S_NAME"));
        config.setImage(rs.getString("S_IMAGE"));
        config.setIndex(rs.getInt("N_INDEX"));
        return config;
    }

}

