package vn.onepay.portal.resources.merchantconfig.dto;

import java.sql.Timestamp;
import java.util.List;

public class MerchantConfigDto {
    private List<String> merchantIds;
    private String merchantId;
    private String currency;
    private String service;
    private String email;
    private String state;
    private String active;
    private Timestamp updatedDate;
    private Timestamp approvedDate;
    private Timestamp createdDate;

    public List<String> getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(List<String> merchantIds) {
        this.merchantIds = merchantIds;
    }

    public String getActive() {
        return active;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public Timestamp getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(Timestamp approvedDate) {
        this.approvedDate = approvedDate;
    }

    public Timestamp getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Timestamp updatedDate) {
        this.updatedDate = updatedDate;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "{" +
                ", merchantId='" + merchantId + '\'' +
                ", service='" + service + '\'' +
                ", email='" + email + '\'' +
                ", state='" + state + '\'' +
                ", active='" + active + 
                '}';
    }
}
