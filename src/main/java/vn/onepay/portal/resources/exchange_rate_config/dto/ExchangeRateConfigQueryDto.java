package vn.onepay.portal.resources.exchange_rate_config.dto;

import java.io.Serializable;

public class ExchangeRateConfigQueryDto implements Serializable {
    private Integer page;
    private Integer pageSize;
    private String fromDate;
    private String toDate;

    public ExchangeRateConfigQueryDto() {
    }

    public ExchangeRateConfigQueryDto(Integer page, Integer pageSize, String fromDate, String toDate) {
        this.page = page;
        this.pageSize = pageSize;
        this.fromDate = fromDate;
        this.toDate = toDate;
    }

    public Integer getPage() {
        return page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public String getFromDate() {
        return fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }
}
