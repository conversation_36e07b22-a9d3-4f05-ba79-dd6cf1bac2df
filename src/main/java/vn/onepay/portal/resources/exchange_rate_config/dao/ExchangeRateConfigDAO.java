package vn.onepay.portal.resources.exchange_rate_config.dao;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.exchange_rate.ExchangeRateDAO;
import vn.onepay.portal.resources.exchange_rate_config.dto.ExchangeRateConfig;
import vn.onepay.portal.resources.exchange_rate_config.dto.ExchangeRateConfigCreateCommand;
import vn.onepay.portal.resources.exchange_rate_config.dto.ExchangeRateConfigUpdateCommand;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExchangeRateConfigDAO extends Db {
    private static final Logger _LOGGER = Logger.getLogger(ExchangeRateDAO.class.getName());
    private static final String GET_LIST_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG.get_exchange_rate(?,?,?,?,?,?,?)}";
    private static final String DELETE_EXCHANGE_RATE_BY_ID = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG.delete_exchange_rate(?,?,?)}";
    private static final String UPDATE_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG.update_exchange_rate(?,?,?,?,?,?)}";
    private static final String CREATE_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG.insert_exchange_rate(?,?,?,?,?,?,?,?,?)}";
    private static final String DETAIL_EXCHANGE_RATE = "{call ONEPARTNER.P_EXCHANGE_RATE_CONFIG.get_exchange_rate_details(?,?,?,?)}";

    /**
     * @param page     Trang
     * @param pageSize Số phần tử của 1 trang
     * @return Danh sách phần cấu hình tỷ giá
     * @throws SQLException trả ra lỗi nếu có
     */
    public static BaseList<Map<String, Object>> getListExchangeRateConfig(Integer year, String page, String pageSize) throws SQLException {
        SQLException exception = null;
        BaseList<Map<String, Object>> result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_LIST_EXCHANGE_RATE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, year);
            cs.setString(5, page);
            cs.setString(6, pageSize);
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                if (rs != null) {
                    result = new BaseList<>(Util.resultSetToList(rs), cs.getInt(7));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static ExchangeRateConfig getExchangeRateConfigDetail(Integer exchangeRateId) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ExchangeRateConfig exchangeRateConfig = new ExchangeRateConfig();
        try {
            con = getConnection114();
            cs = con.prepareCall(DETAIL_EXCHANGE_RATE);
            cs.setInt(1, exchangeRateId);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 1) {
                throw new SQLException("DB get_exchange_rate : " + error);
            } else {
                if (rs.next()) {
                    exchangeRateConfig.setId(rs.getInt("n_id"));
                    exchangeRateConfig.setExchangeRateDate(rs.getDate("n_exchange_date"));
                    exchangeRateConfig.setExchangeRateValue(rs.getBigDecimal("n_exchange_value"));
                    exchangeRateConfig.setMonth(rs.getInt("n_month"));
                    exchangeRateConfig.setYear(rs.getInt("n_year"));
                }
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return exchangeRateConfig;
    }

    public static Map<String, Object> createExchangeRateConfig(ExchangeRateConfigCreateCommand command) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        String error = "";
        int nError = 0;
        try {
            con = getConnection114();
            cs = con.prepareCall(CREATE_EXCHANGE_RATE);
            cs.setDate(1, new java.sql.Date(command.getExchangeRateDate().getTime()));
            cs.setBigDecimal(2, command.getExchangeRateValue());
            cs.setInt(3, command.getMonth() == null ? 0 : command.getMonth());
            cs.setInt(4, command.getYear() == null ? 0 : command.getYear());
            cs.setString(5, command.getCreatedBy());
            cs.setInt(6, command.getConfirm());
            cs.registerOutParameter(7, OracleTypes.NUMBER);
            cs.registerOutParameter(8, OracleTypes.NUMBER);
            cs.registerOutParameter(9, OracleTypes.VARCHAR);
            cs.execute();
            error = cs.getString(9);
            nError = cs.getInt(8);
            if (nError == 0) {
                throw new SQLException("DB get_exchange_rate : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.put("error", nError);
        return result;
    }


    public static Map<String, Object> updateExchangeRateConfig(Integer exchangeRateId, BigDecimal  exchangeValue, String modifiedDate, String updater) throws SQLException {
        SQLException exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        try {
            con = getConnection114();
            cs = con.prepareCall(UPDATE_EXCHANGE_RATE);
            cs.setInt(1, exchangeRateId);
            cs.setBigDecimal(2,exchangeValue);
            cs.setString(3, updater);
            cs.setString(4, modifiedDate);
            cs.registerOutParameter(5,OracleTypes.NUMBER);
            cs.registerOutParameter(6,OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(6);
            int nError = cs.getInt(5);
            if (nError != 1) {
                throw new SQLException("DB update_exchange_rate_config : " + error);
            }
        } catch (SQLException e) {
            _LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * @param id Id của bảng cấu hình tỷ giá.
     * @return Xóa cấu hình tỷ giá.
     */
    public static Map<String, Object> deleteExchangeRateConfig(Integer id) throws Exception {
        Exception exception = null;
        Map<String, Object> result = new HashMap<>();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nError = 0;
        try {
            conn = getConnection114();
            cs = conn.prepareCall(DELETE_EXCHANGE_RATE_BY_ID);
            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            if (nError == 0) {
                logger.severe("DB get domestic transaction error: " + error);
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null) {
            throw exception;
        }
        return result;
    }


}
