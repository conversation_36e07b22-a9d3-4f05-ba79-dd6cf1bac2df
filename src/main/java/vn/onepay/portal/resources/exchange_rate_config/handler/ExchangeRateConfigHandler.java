package vn.onepay.portal.resources.exchange_rate_config.handler;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.exchange_rate_config.dao.ExchangeRateConfigDAO;
import vn.onepay.portal.resources.exchange_rate_config.dto.ExchangeRateConfigCreateCommand;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;


public class ExchangeRateConfigHandler implements IConstants {
    private ExchangeRateConfigHandler() {
    }

    private static final Logger _LOGGER = Logger.getLogger(ExchangeRateConfigHandler.class.getName());

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> request = Util.convertRequestParamsToMap(ctx);
                Integer year = Integer.valueOf(request.get("year"));
                sendResponse(ctx, 200, ExchangeRateConfigDAO.getListExchangeRateConfig(year, request.get("page"), request.get("pageSize")));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void create(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                Date exchangeDate = new Date(Long.parseLong(body.getString("exchangeDate")));
                BigDecimal exchangeValue = BigDecimal.valueOf(body.getDouble("exchangeValue"));
                Integer confirm = body.getInteger("confirm") == null ? 0 : body.getInteger("confirm");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(exchangeDate);
                Integer year = calendar.get(Calendar.YEAR);
                Integer month = calendar.get(Calendar.MONTH) + 1;
                String createdBy = body.getString("createdBy");
                ExchangeRateConfigCreateCommand command = new ExchangeRateConfigCreateCommand(exchangeDate, exchangeValue, month, year, confirm, createdBy);
                sendResponse(ctx, 200, ExchangeRateConfigDAO.createExchangeRateConfig(command));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void detail(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer exchangeRateId = Integer.valueOf(ctx.request().getParam(EXCHANGE_RARE_ID));
                sendResponse(ctx, 200, ExchangeRateConfigDAO.getExchangeRateConfigDetail(exchangeRateId));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void update(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer exchangeRateId = Integer.valueOf(ctx.request().getParam(EXCHANGE_RARE_ID));

                JsonObject body = ctx.getBodyAsJson();
                BigDecimal exchangeRateValue = BigDecimal.valueOf(body.getDouble("exchangeValue"));
                String modifiedDate = body.getString("modifiedDate");
                String updater = body.getString("updater");
                sendResponse(ctx, 200, ExchangeRateConfigDAO.updateExchangeRateConfig(exchangeRateId, exchangeRateValue, modifiedDate, updater));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void delete(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Integer exchangeRateId = Integer.valueOf(ctx.request().getParam(EXCHANGE_RARE_ID));
                sendResponse(ctx, 200, ExchangeRateConfigDAO.deleteExchangeRateConfig(exchangeRateId));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
