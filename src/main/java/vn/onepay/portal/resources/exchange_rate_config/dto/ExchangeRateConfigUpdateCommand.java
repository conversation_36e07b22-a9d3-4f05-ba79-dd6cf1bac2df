package vn.onepay.portal.resources.exchange_rate_config.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ExchangeRateConfigUpdateCommand implements Serializable {
    private Date exchangeRateDate;
    private BigDecimal exchangeRateValue;
    private Integer month;
    private Integer year;

    public Date getExchangeRateDate() {
        return exchangeRateDate;
    }

    public void setExchangeRateDate(Date exchangeRateDate) {
        this.exchangeRateDate = exchangeRateDate;
    }

    public BigDecimal getExchangeRateValue() {
        return exchangeRateValue;
    }

    public void setExchangeRateValue(BigDecimal exchangeRateValue) {
        this.exchangeRateValue = exchangeRateValue;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public ExchangeRateConfigUpdateCommand(Date exchangeRateDate, BigDecimal exchangeRateValue, Integer month, Integer year) {
        this.exchangeRateDate = exchangeRateDate;
        this.exchangeRateValue = exchangeRateValue;
        this.month = month;
        this.year = year;
    }
}
