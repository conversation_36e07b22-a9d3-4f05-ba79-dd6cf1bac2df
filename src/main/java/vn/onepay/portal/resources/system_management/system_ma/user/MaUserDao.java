package vn.onepay.portal.resources.system_management.system_ma.user;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.Res;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.dto.PartnerConfigDto;
import vn.onepay.portal.resources.system_management.role.dto.BaseRoleRes;
import vn.onepay.portal.resources.system_management.role.dto.RoleManageRes;
import vn.onepay.portal.resources.system_management.system_ma.role.MaRoleDao;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.BaseUserRes;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.MerchantConfigRes;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.PayMethodRes;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.UserData;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.UserMerchant;
import vn.onepay.portal.resources.system_management.system_ma.user_role.MaUserRoleDao;

import java.beans.Transient;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gson.Gson;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;


public class MaUserDao extends Db {
    private static final String GET_ALL_USER = "{call PKG_SYSTEM_MA.get_all_user(?,?,?)}";
    private static final String UPDATE_USER_AM = "{call update_user_am_v3(?,?,?,?,?,?,?,?)}";
    private static final String USER_INSERT_AM = "{call PKG_SYSTEM_MA.insert_user_am(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String USER_INSERT_MER = "{call PKG_SYSTEM_MA.INSERT_USER(?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String CHECK_EXIST_USER = "{call PKG_SYSTEM_MA.check_exist_user(?,?,?,?,?)}";
    private static final String GET_USER_BY_ID = "{call PKG_SYSTEM_MA.get_userAM_by_id(?,?,?,?)}";
    private static final String GET_PARTNER_BY_USER = "{call PKG_SYSTEM_MA.get_partner_by_user(?,?,?,?)}";
    private static final String GET_MERCHANT_BY_USER = "{call PKG_SYSTEM_MA.get_merchant_by_user(?,?,?,?,?)}";
    private static final String GET_MERCHANT_BY_USER_PARTNER = "{call PKG_SYSTEM_MA.get_list_id_by_user_partner(?,?,?,?)}";
    private static final String ASSIGN_PARTNER = "{call PKG_SYSTEM_MA.insert_partner_user(?,?,?,?)}";
    private static final String DELETE_PARTNER_BY_USER = "{call PKG_SYSTEM_MA.delete_partner_by_user_id(?,?,?)}";

    private static final String DELETE_MERCHANT_BY_USER = "{call PKG_SYSTEM_MA.delete_merchant_user_id(?,?,?,?)}";
    private static final String ASSIGN_MERCHANT = "{call PKG_SYSTEM_MA.insert_merchant_user(?,?,?,?,?,?,?)}";
    private static final String GET_ALL_METHOD = "{call PKG_SYSTEM_MA.get_payment_method(?,?,?)}";

    private static final String GET_ALL_MERCHANT_BY_PARTNER = "{call PKG_SYSTEM_MA.get_merchant_by_partner(?,?,?,?)}";
    private static final String GET_ALL_USER_BY_PARTNER = "{call PKG_SYSTEM_MA.get_user_by_partner(?,?,?,?)}";
    private static final String INSERT_MERCHANT_USER_PARTNER = "{call PKG_SYSTEM_MA.INSERT_MID_MERCHANT_USER(?,?,?,?,?,?,?,?)}";


    private static Logger LOGGER = Logger.getLogger(MaUserDao.class.getName());

    public static BaseList<BaseUserRes> getAllUser(List<String> ids) throws Exception {
        // Lấy ra Base user không có parent theo id
        BaseList<BaseUserRes> res = new BaseList<>();

        List<BaseUserRes> userRes = getAllBaseUser(ids);

        //ahihi - get user keycloak
        KeycloakService keycloakService = KeycloakService.getInstance();
        List<BaseUserRes> users = keycloakService.getAllUsers();
        BaseUserRes userKeyCloak = keycloakService.getUserByEmail("<EMAIL>");
        logger.info("===dataa: " + users.size() + " , Data detail: " + new Gson().toJson(users.get(0)));
        res.setList(userRes);
        return res;
    }

    public static List<BaseUserRes> getAllBaseUser(List<String> ids) throws Exception {
        List<BaseUserRes> result = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_ALL_USER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindBaseUser(rs, ids));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static BaseList<BaseRoleRes> getListRolesByUser(String id) throws Exception {
        BaseList<BaseRoleRes> res = new BaseList<>();
        List<String> roleIds = MaUserRoleDao.getRoleIds(Integer.parseInt(id));
        List<BaseRoleRes> baseRoleRes = MaRoleDao.getAllBaseRole(roleIds);
        res.setList(baseRoleRes);
        return res;
    }

    public static BaseUserRes getBaseUserById(String id) throws Exception {
        Exception exception = null;
        BaseUserRes userAm = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_USER_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total international refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    userAm = new BaseUserRes();
                    userAm.setId(Util.getColumnInteger(rs, "N_ID"));
                    userAm.setUserId(Util.getColumnString(rs, "S_ID"));
                    userAm.setLastName(Util.getColumnString(rs, "S_LAST_NAME"));
                    userAm.setFirstName(Util.getColumnString(rs, "S_FIRST_NAME"));
                    userAm.setEmail(Util.getColumnString(rs, "S_EMAIL"));
                    userAm.setPhone(Util.getColumnString(rs, "S_MOBILE"));
                    userAm.setAuthLevel(Util.getColumnInteger(rs, "N_AUTH_LEVEL"));
                    userAm.setStatus(Util.getColumnString(rs, "s_status"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "GET USER AM BY SID ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return userAm;
    }

    public static BaseUserRes bindBaseUser(ResultSet rs, List<String> listId) throws Exception {
        BaseUserRes res = new BaseUserRes();
        Integer id = Util.getColumnInteger(rs, "N_ID");
        String name = Util.getColumnString(rs, "S_NAME");
        String email = Util.getColumnString(rs, "S_EMAIL") != null ? " _ " + rs.getString("S_EMAIL") : "";
        String desc = Util.getColumnString(rs, "S_DESC");
        res.setDesc(desc);
        res.setId(id);
        res.setParent_id(0);
        res.setUserId(Util.getColumnString(rs, "S_USER_ID"));
        res.setName(name + email);
        res.setEmail(Util.getColumnString(rs, "S_EMAIL"));
        res.setPhone(Util.getColumnString(rs, "S_PHONE"));
        res.setIsExits(false);
        if (listId != null && listId.size() != 0)
            for (String idl : listId) {
                if (Integer.parseInt(idl) - id == 0) {
                    res.setIsExits(true);
                    break;
                }
            }
        return res;
    }

    public static Map update(JsonObject userReq) throws Exception {
        boolean checkUpdate = false;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map result = new HashMap();
        String oldStatus = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(UPDATE_USER_AM);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, userReq.getString("id"));
            cs.setInt(5, userReq.getInteger("authLevel"));
            cs.setString(6, userReq.getString("status"));
            cs.setString(7, userReq.getString("name"));
            cs.setString(8, userReq.getString("phone"));

            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB UPDATE error: " + error);
            } else {
                checkUpdate = true;
                oldStatus = cs.getString(3);
            }
            result.put("is_success", checkUpdate);
            result.put("old_status", oldStatus);
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer insertUser(UserData user) throws Exception {
        Integer idMerchantPortal = insertUserOneAM(user);
        user.setNId(idMerchantPortal);
        return idMerchantPortal;
    }

    public static Integer insertUserOneAM(UserData user) throws Exception {
        Integer idMerchantPortal = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(USER_INSERT_AM);
            cs.registerOutParameter(1, OracleTypes.VARCHAR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, user.getAmFirstName());
            cs.setString(6, user.getAmLastName());
            cs.setString(7, "");
            cs.setString(8, user.getPass());
            cs.setString(9, user.getPhone());
            cs.setString(10, user.getEmail());
            cs.setInt(11, user.getAuthLevel());
            cs.setString(12, user.getCreateId());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(4);
            if (nError != 201) {
                throw new Exception(error);
            } else {
                idMerchantPortal = cs.getInt(2);
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return idMerchantPortal;
    }

    public static Integer insertUserMerchant(UserData user) throws Exception {
        int returnData = 0;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(USER_INSERT_MER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, user.getSId());
            cs.setString(5, "");
            cs.setString(6, user.getName());
            cs.setString(7, user.getEmail());
            cs.setString(8, user.getPhone());
            cs.setString(9, user.getAddress());
            cs.setString(10, "");
            cs.setString(11, user.getCreateId() == null ? "" : user.getCreateId());
            cs.setString(12, user.getJobTitle() == null ? "" : user.getJobTitle());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(1);
            if (nError != 201) {
                throw new Exception("DB UPDATE USER AM error: " + error);
            } else {
                returnData = cs.getInt(2);

            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return returnData;
    }

    public static boolean checkExistEmailAndPhone(String email, String phone) throws Exception {
        boolean result = false;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(CHECK_EXIST_USER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, email);
            cs.setString(5, phone);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("CHECK EXIST EMAIL AND PHONE " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = true;
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    @Transient
    public static Res sync(Map mIn) throws Exception {
        // delete all function role
        String id = mIn.get(ID).toString();
        String rs = mIn.get("roles").toString().replace("[", "").replace("]", "");
        List<String> roles = new ArrayList<String>(Arrays.asList(rs.split(",")));
        Res res = new Res();
        boolean checkDeleteUserRole = MaUserRoleDao.deleteUserRoleByUser(Integer.parseInt(id));
        AtomicBoolean check = new AtomicBoolean(true);
        if (checkDeleteUserRole) {
            roles.stream().forEach(a -> {
                try {
                    List<RoleManageRes> resFuncs = MaRoleDao.getRoleByParents(Integer.parseInt(a));
                    if (resFuncs.size() == 0)
                        MaUserRoleDao.createUserRole(Integer.parseInt(id), Integer.parseInt(a));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Error: ", e);
                    check.set(false);
                }
            });
        }

        if (check.get()) {
            res.setValue(1);
            return res;
        } else {
            res.setValue(0);
            return res;
        }
    }

    public static List<PartnerConfigDto> getPartnerByUser(Integer id) throws Exception {
        Exception exception = null;
        List<PartnerConfigDto> partners = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_BY_USER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB PARTNER BY USER : " + error);
            } else {
                PartnerConfigDto partner;
                while (rs != null && rs.next()) {
                    partner = new PartnerConfigDto();
                    partner.setPartnerId(Util.getColumnInteger(rs, "s_partner_id"));
                    partner.setPartnerName(Util.getColumnString(rs, "s_partner_name"));
                    partner.setShortName(Util.getColumnString(rs, "s_partner_short_name"));
                    partners.add(partner);
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "GET PARTNER BY USER ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return partners;
    }

    public static Integer assignPartners(JsonObject userReq) throws Exception {
        boolean deletePartnerUser = deletePartnerUserByUser(userReq.getInteger("userId"));
        Integer numberAssign = 0;
        if (deletePartnerUser) {
            JsonArray partners = userReq.getJsonArray("partnerIds");
            for (Object partner : partners) {
                numberAssign += assignPartner(userReq.getInteger("userId"), partner.toString()) ? 1 : 0;
            }

        }
        return numberAssign;

    }

    public static boolean deletePartnerUserByUser(Integer user_id) throws Exception {
        LOGGER.log(Level.INFO, "DELETE PARTNER USER  ", user_id);
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(DELETE_PARTNER_BY_USER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, user_id);// user_id
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DELETE PARTNER BY USER : " + error);
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return true;
    }

    public static boolean assignPartner(Integer userId, String partnerId) throws Exception {
        boolean checkAssign = false;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(ASSIGN_PARTNER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, userId);
            cs.setString(4, partnerId);

            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 201) {
                throw new Exception(error);
            } else {
                checkAssign = true;
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "ASSIGN PARTNER ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return checkAssign;
    }

    public static Integer assignMerchants(JsonObject userReq) throws Exception {
        boolean deleteMerchantUser = deleteMerchantUserByUser(userReq.getInteger("userId"), userReq.getString("payMethod"));
        Integer numberAssign = 0;
        if (deleteMerchantUser) {
            JsonArray merchants = userReq.getJsonArray("merchants");
            for (int i = 0; i < merchants.size(); i++) {
                JsonObject merchant = merchants.getJsonObject(i);
                numberAssign += assignMerchant(userReq.getInteger("userId"), merchant.getString("merchantId"), merchant.getString("merchantName"),
                        merchant.getString("currencyCode"), userReq.getString("payMethod")) ? 1 : 0;
            }

        }
        return numberAssign;

    }

    public static boolean deleteMerchantUserByUser(Integer user_id, String type) throws Exception {
        LOGGER.log(Level.INFO, "DELETE MERCHANT USER  ", user_id);
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(DELETE_MERCHANT_BY_USER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, user_id);
            cs.setString(4, type);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DELETE MERCHANT BY USER ERROR : " + error);
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return true;
    }

    public static boolean assignMerchant(Integer userId, String merchantId, String merchantName, String currencyCode, String type) throws Exception {
        boolean checkAssign = false;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(ASSIGN_MERCHANT);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, userId);
            cs.setString(4, merchantId);
            cs.setString(5, merchantName);
            cs.setString(6, currencyCode);
            cs.setString(7, type);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception(error);
            } else {
                checkAssign = true;
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "ASSIGN MERCHANT ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return checkAssign;
    }

    public static List<MerchantConfigRes> getMerchantByPayMethod(String userId, String type, String keywords, Integer page) throws Exception {
        List<String> listIds = getMerchantByUserPartner(userId);
        if (listIds != null && listIds.size() > 0 && listIds.get(0) != null) {
            List<String> listIdsNoDuplicate = new ArrayList<>(new HashSet<>(listIds));
            // List of merchant id => string separate by comma
            String strListIds = String.join(",", listIdsNoDuplicate);
            if (type.equals("domestic")) {
                return Merchant12Dao.getListMerchantDomesticByListIds(keywords, page, strListIds);
            } else if (type.equals("international")) {
                return Merchant12Dao.getListMerchantOneCreditByListIds(keywords, page, strListIds);
            } else if (type.equals("billing")) {
                return Merchant12Dao.getListMerchantBillingByListIds(keywords, page, strListIds);
            } else if (type.equals("promotion")) {
                return Merchant12Dao.getListMerchantPromotionByListIds(keywords, page, strListIds);
            } else if (type.equals("mpay")) {
                return Merchant12Dao.getListMerchantMpayByListIds(keywords, page, strListIds);
            } else if (type.equals("payout")) {
                return Merchant12Dao.getListMerchantPayoutByListIds(keywords, page, strListIds);
            } else if (type.equals("paycollect")) {
                return Merchant12Dao.getListMerchantPayCollectByListIds(keywords, page, strListIds);
            } else if (type.equals("quicklink")) {
                return Merchant12Dao.getListMerchantQuickLinkByListIds(keywords, page, strListIds);
            } else if (type.equals("bnpl")) {
                return Merchant12Dao.getListMerchantBNPLByListIds(keywords, page, strListIds);
            } else if (type.equals("upos")) {
                return Merchant12Dao.getListMerchantUposByListIds(keywords, page, strListIds); // merchant Upos
            } else if (type.equals("vietqr")) {
                return Merchant12Dao.getListMerchantVietQrByListIds(keywords, page, strListIds);
            } else if (type.equals("direct-debit")) {
                return Merchant12Dao.getListMerchantDirectDebitByListIds(keywords, page, strListIds);
            } else if (type.equals("payment")) {
                return Merchant12Dao.getListMerchantPaymentByListIds( keywords, page, strListIds);
            }else {
                LOGGER.log(Level.SEVERE, "[ MERCHANT ] INVALID TYPE MERCHANT");
                throw IErrors.VALIDATION_ERROR;
            }
        } else {
            return new ArrayList<>();
        }
    }

    public static List<String> getMerchantByUserPartner(String userId) throws Exception {
        Exception exception = null;
        List<String> merchantIds = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_MERCHANT_BY_USER_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, userId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB PARTNER BY USER : " + error);
            } else {
                // String merchant;
                while (rs != null && rs.next()) {
                    // merchant = new MerchantConfigRes();
                    // merchant.setMerchantId(Util.getColumnString(rs, "s_merchant_id"));
                    // merchant.setMerchantName(Util.getColumnString(rs, "s_merchant_id"));
                    merchantIds.add(Util.getColumnString(rs, "s_merchant_id"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "GET MERCHANT BY USER PARTNER ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return merchantIds;
    }

    public static List<MerchantConfigRes> getMerchantByUser(Integer id, String type) throws Exception {
        Exception exception = null;
        List<MerchantConfigRes> merchants = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_MERCHANT_BY_USER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.setString(5, type);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB MERCHANT BY USER : " + error);
            } else {
                MerchantConfigRes merchant;
                while (rs != null && rs.next()) {
                    merchant = new MerchantConfigRes();
                    merchant.setUserId(Util.getColumnInteger(rs, "N_USER_ID"));
                    merchant.setMerchantName(Util.getColumnString(rs, "S_MERCHANT_NAME"));
                    merchant.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
                    merchant.setType(Util.getColumnString(rs, "S_TYPE"));
                    merchant.setCreateDate(Util.getColumnDate(rs, "D_CREATE_DATE"));
                    merchant.setUpdateDate(Util.getColumnDate(rs, "D_UPDATE_DATE"));
                    merchant.setCurrencyCode(Util.getColumnString(rs, "S_CURRENCY_CODE"));
                    merchants.add(merchant);
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "GET PARTNER BY USER ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return merchants;
    }

    public static List<PayMethodRes> getPayMethod() throws Exception {
        List<PayMethodRes> result = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_ALL_METHOD);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get error: " + error);
            } else {
                PayMethodRes payMethod;
                while (rs != null && rs.next()) {
                    payMethod = new PayMethodRes();
                    payMethod.setId(Util.getColumnString(rs, "S_ID"));
                    payMethod.setPaygate(Util.getColumnString(rs, "S_PAYGATE"));
                    payMethod.setName(Util.getColumnString(rs, "S_NAME"));
                    payMethod.setCreate(Util.getColumnDate(rs, "D_CREATE"));
                    payMethod.setOrder(Util.getColumnInteger(rs, "N_ORDER"));
                    result.add(payMethod);
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

// ------------------------------------------------------------------------------------------------
    public static List<Map<String,Object>> getAllMerchantByPartner(int partnerId) throws Exception {
        List<Map<String,Object>> result = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_ALL_MERCHANT_BY_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String,Object> merchant = new HashMap<>();
                    merchant.put("merchantId", Util.getColumnString(rs, "S_MERCHANT_ID"));
                    merchant.put("paygate", Util.getColumnString(rs, "S_PAYGATE"));
                    merchant.put("partnerId", Util.getColumnInteger(rs, "N_PARTNER_ID"));
                    merchant.put("merchantName", Util.getColumnString(rs, "S_NAME"));
                    result.add(merchant);
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<Map<String,Object>> getAllUserByPartner(int partnerId) throws Exception {
        List<Map<String,Object>> result = new ArrayList<>();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_ALL_USER_BY_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String,Object> merchant = new HashMap<>();
                    merchant.put("userId", Util.getColumnInteger(rs, "N_USER_ID"));
                    merchant.put("partnerId", Util.getColumnInteger(rs, "N_PARTNER_ID"));
                    merchant.put("email", Util.getColumnString(rs, "S_EMAIL"));

                    result.add(merchant);
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
    public static Integer insertMerchantUserPartner(UserMerchant userMerchant) throws Exception {
        int returnData = 0;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection114();
            cs = con.prepareCall(INSERT_MERCHANT_USER_PARTNER);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, userMerchant.getPartnerId());
            cs.setString(5, userMerchant.getMerchantId());
            cs.setString(6, userMerchant.getUserId());
            cs.setString(7, userMerchant.getPaygate());
            cs.setString(8, userMerchant.getMerchantName());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(1);
            if (nError != 200) {
                logger.log(Level.SEVERE, "INSERT_MID_MERCHANT_USER error: ", userMerchant);
                throw new Exception("DB UPDATE INSERT_MID_MERCHANT_USER error: " + error);
            } else {
                if("OK".equalsIgnoreCase(error)){
                    logger.log(Level.SEVERE, "INSERT insertMerchantUserPartner success: merchantId :" + userMerchant.getMerchantId() 
                    + " |userId: " +userMerchant.getUserId() + " |partnerId: " + userMerchant.getPartnerId());
                } else {
                    logger.log(Level.SEVERE, "NOT INSERT insertMerchantUserPartner success : merchantId :" + userMerchant.getMerchantId() 
                    + " |userId: " +userMerchant.getUserId() + " |partnerId: " + userMerchant.getPartnerId());
                }

                returnData = cs.getInt(1);
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return returnData;
    }
}
