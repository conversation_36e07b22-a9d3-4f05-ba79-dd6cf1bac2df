package vn.onepay.portal.resources.system_management.system_ma.user;

import java.util.logging.Logger;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.system_management.system_ma.user.dto.BaseUserRes;

import java.util.List;
import java.util.Map;
import org.keycloak.representations.idm.UserRepresentation;
import org.keycloak.admin.client.resource.UsersResource;

public class KeycloakService {
    private static final Logger logger = Logger.getLogger(KeycloakService.class.getName());
    private static KeycloakService instance;
    private Keycloak keycloak;

    private KeycloakService() {
        logger.info("Initializing KeycloakService...");
        try {
            String serverUrl = Config.getString("keycloak_ma.server_url", "http://localhost:8080/auth");
            String realm = Config.getString("keycloak_ma.realm", "master");
            String clientId = Config.getString("keycloak_ma.client_id", "admin-cli");
            String clientSecret = Config.getString("keycloak_ma.client_secret", "");

            logger.info("Connecting to Keycloak server: " + serverUrl);
            logger.info("Using realm: " + realm);
            logger.info("Using client ID: " + clientId);

            // ================= THÊM ĐOẠN LOG NÀY VÀO =================
            logger.info("========== CONFIG VALUES BEING USED ==========");
            logger.info("Server URL: " + serverUrl);
            logger.info("Realm: " + realm);
            logger.info("Client ID: " + clientId);
            logger.info("Client Secret is present: " + !clientSecret.isEmpty()); // Không log client secret ra nhé!
            logger.info("==============================================");
            // =========================================================

            keycloak = KeycloakBuilder.builder()
                    .serverUrl(serverUrl)
                    .realm(realm)
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .grantType("client_credentials")
                    .build();
            
            logger.info("Successfully connected to Keycloak server");
        } catch (Exception e) {
            logger.severe("Failed to initialize KeycloakService: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to initialize KeycloakService", e);
        }
    }

    public Keycloak getKeycloak() {
        return keycloak;
    }

    // singleton
    public static synchronized KeycloakService getInstance() {
        logger.info("Getting KeycloakService instance");
        if (instance == null) {
            logger.info("Creating new KeycloakService instance");
            instance = new KeycloakService();
        }
        return instance;
    }

    // Get all users from Keycloak
    public List<BaseUserRes> getAllUsers() {
        try {
            logger.info("Getting all users from Keycloak");
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> users = usersResource.list();
            logger.info("Successfully retrieved " + users.size() + " users from Keycloak");
            List<BaseUserRes> baseUserRes = users.stream().map(user -> {
                BaseUserRes baseUser = new BaseUserRes();
                // baseUser.setId(user.getId());
                baseUser.setName(user.getUsername());
                baseUser.setEmail(user.getEmail());
                // baseUser.setPhone(user.getMobile());
                baseUser.setFirstName(user.getFirstName());
                baseUser.setLastName(user.getLastName());
                baseUser.setStatus(user.isEnabled() ? "Active" : "Inactive");
                return baseUser;
            }).collect(java.util.stream.Collectors.toList());
            return baseUserRes;
        } catch (Exception e) {
            logger.severe("Failed to get all users from Keycloak: " + e.getMessage());
            throw new RuntimeException("Failed to get all users from Keycloak", e);
        }
    }

    // Get all users with pagination
    public List<UserRepresentation> getAllUsers(int firstResult, int maxResults) {
        try {
            logger.info("Getting users from Keycloak with pagination: first=" + firstResult + ", max=" + maxResults);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> users = usersResource.list(firstResult, maxResults);
            logger.info("Successfully retrieved " + users.size() + " users from Keycloak");
            return users;
        } catch (Exception e) {
            logger.severe("Failed to get users with pagination from Keycloak: " + e.getMessage());
            throw new RuntimeException("Failed to get users with pagination from Keycloak", e);
        }
    }

    // Get user by ID
    public UserRepresentation getUserById(String userId) {
        try {
            logger.info("Getting user by ID: " + userId);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            UserRepresentation user = usersResource.get(userId).toRepresentation();
            logger.info("Successfully retrieved user: " + user.getUsername());
            return user;
        } catch (Exception e) {
            logger.severe("Failed to get user by ID " + userId + ": " + e.getMessage());
            throw new RuntimeException("Failed to get user by ID: " + userId, e);
        }
    }

    // Get user by username
    public UserRepresentation getUserByUsername(String username) {
        try {
            logger.info("Getting user by username: " + username);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> users = usersResource.search(username, true);
            
            if (users.isEmpty()) {
                logger.warning("No user found with username: " + username);
                return null;
            }
            
            UserRepresentation user = users.get(0);
            logger.info("Successfully retrieved user: " + user.getUsername());
            return user;
        } catch (Exception e) {
            logger.severe("Failed to get user by username " + username + ": " + e.getMessage());
            throw new RuntimeException("Failed to get user by username: " + username, e);
        }
    }

    // Get user by email
    public UserRepresentation getUserByEmail(String email) {
        try {
            logger.info("Getting user by email: " + email);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> users = usersResource.search(null, null, null, email, 0, 1);
            
            if (users.isEmpty()) {
                logger.warning("No user found with email: " + email);
                return null;
            }
            
            UserRepresentation user = users.get(0);
            logger.info("Successfully retrieved user: " + user.getEmail());
            return user;
        } catch (Exception e) {
            logger.severe("Failed to get user by email " + email + ": " + e.getMessage());
            throw new RuntimeException("Failed to get user by email: " + email, e);
        }
    }

    // Search users by keyword (username, email, firstName, lastName)
    public List<UserRepresentation> searchUsers(String keyword) {
        try {
            logger.info("Searching users with keyword: " + keyword);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> users = usersResource.search(keyword);
            logger.info("Successfully found " + users.size() + " users matching keyword: " + keyword);
            return users;
        } catch (Exception e) {
            logger.severe("Failed to search users with keyword " + keyword + ": " + e.getMessage());
            throw new RuntimeException("Failed to search users with keyword: " + keyword, e);
        }
    }

    // Get user by custom attribute
    public List<UserRepresentation> getUsersByAttribute(String attributeName, String attributeValue) {
        try {
            logger.info("Getting users by attribute: " + attributeName + " = " + attributeValue);
            UsersResource usersResource = keycloak.realm(Config.getString("keycloak_ma.realm", "master")).users();
            List<UserRepresentation> allUsers = usersResource.list();
            
            List<UserRepresentation> matchingUsers = allUsers.stream()
                .filter(user -> {
                    Map<String, List<String>> attributes = user.getAttributes();
                    if (attributes != null && attributes.containsKey(attributeName)) {
                        List<String> values = attributes.get(attributeName);
                        return values != null && values.contains(attributeValue);
                    }
                    return false;
                })
                .collect(java.util.stream.Collectors.toList());
            
            logger.info("Successfully found " + matchingUsers.size() + " users with attribute " + attributeName + " = " + attributeValue);
            return matchingUsers;
        } catch (Exception e) {
            logger.severe("Failed to get users by attribute " + attributeName + ": " + e.getMessage());
            throw new RuntimeException("Failed to get users by attribute: " + attributeName, e);
        }
    }
}
