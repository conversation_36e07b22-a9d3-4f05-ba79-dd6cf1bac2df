package vn.onepay.portal.resources.merchant_account.dto;

import java.sql.Timestamp;
import java.util.List;

public class MerchantAccountParam {
    private Integer id;
    private Integer partnerId;
    private String partnerName;
    private String businessName;
    private String merchantAccountName;
    private String createDate;
    private String contractCode;
    private String subcontractCode;
    public String getCategory() {
        return category;
    }
    public void setCategory(String category) {
        this.category = category;
    }
    public String getDescribeCategory() {
        return describeCategory;
    }
    public void setDescribeCategory(String describeCategory) {
        this.describeCategory = describeCategory;
    }
    public String getProvince() {
        return province;
    }
    public void setProvince(String province) {
        this.province = province;
    }

    private String taxCode;
    private List<MerchantIdService> merchantIdService;
    private String activatedDate;
    private String category;
    private String describeCategory;
    private String province;
    public Integer getId() {
        return id;
    }
    public String getCreateDate() {
        return createDate;
    }
    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
    public void setId(Integer Id) {
        this.id = Id;
    }
    public Integer getPartnerId() {
        return partnerId;
    }
    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }
    public String getPartnerName() {
        return partnerName;
    }
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
    public String getBusinessName() {
        return businessName;
    }
    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }
    public String getMerchantAccountName() {
        return merchantAccountName;
    }
    public void setMerchantAccountName(String merchantAccountName) {
        this.merchantAccountName = merchantAccountName;
    }
    public String getContractCode() {
        return contractCode;
    }
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }
    public String getSubcontractCode() {
        return subcontractCode;
    }
    public void setSubcontractCode(String subcontractCode) {
        this.subcontractCode = subcontractCode;
    }
    public String getTaxCode() {
        return taxCode;
    }
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }
    public List<MerchantIdService> getMerchantIdService() {
        return merchantIdService;
    }
    public void setMerchantIdService(List<MerchantIdService> merchantIdService) {
        this.merchantIdService = merchantIdService;
    }
    public String getActivatedDate() {
        return activatedDate;
    }
    public void setActivatedDate(String activatedDate) {
        this.activatedDate = activatedDate;
    }

    @Override
    public String toString() {
        return "{" +
            "  Id='" + getId() + "'" +
            ", partnerName='" + getPartnerName() + "'" +
            ", partnerId='" + getPartnerId() + "'" +
            ", businessName='" + getBusinessName() + "'" +
            ", merchantAccountName='" + getMerchantAccountName() + "'" +
            ", contractCode='" + getContractCode() + "'" +
            ", subcontractCode='" + getSubcontractCode() + "'" +
            ", taxCode='" + getTaxCode() + "'" +
            ", merchantIdService='" + getMerchantIdService() + "'" +
            ", activatedDate='" + getActivatedDate() + "'" +
            "}";
    }

}
