package vn.onepay.portal.resources.merchant_account.dao;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.quicklink.dto.MerchantProfileDto;
import vn.onepay.portal.resources.quicklink.dto.ModelDto;
import vn.onepay.portal.resources.quicklink.dto.PartnerDto;

public class MerchantAccountDao extends Db {
    private static final String GET_ALL_MERCHANT_NAME = "{call PKG_QUICKLINK.GET_ALL_MERCHANT_NAME(?,?,?) }";
    private static final String GET_ALL_MERCHANT_ID = "{call PKG_QUICKLINK.GET_ALL_MERCHANT_ID(?,?,?) }";
    private static final String GET_ALL_PARTNER = "{call PKG_QUICKLINK.GET_ALL_PARTNER(?,?,?) }";
    private static final String GET_LIST_MERCHANT_PROFILE = "{call PKG_QUICKLINK.get_list_merchant_profile(?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String GET_PARTNER_LIST = "{call PKG_QUICKLINK.GET_PARTNER_LIST(?,?,?,?,?,?,?) }";
    private static final String GET_TOTAL_PARTNER_LIST = "{call PKG_QUICKLINK.GET_TOTAL_PARTNER_LIST(?,?,?,?,?) }";
    private static final String GET_PAYNOW_BY_ID = "{call PKG_QUICKLINK.get_paynow_by_id(?,?,?,?) }";
    private static final String GET_INSTALLMENT_BY_ID = "{call PKG_QUICKLINK.get_installment_by_id(?,?,?,?) }";
    private static final String GET_INSTALLMENT_MERCHANT_IDS = "{call MSP.P_PORTAL.installment_merchant_ids(?,?,?,?)}";
    private static final String CHECK_HAS_PAYMENT_LINK = "{call PKG_QUICKLINK.check_payment_link(?,?,?,?)";


    private static final Logger LOGGER = Logger.getLogger(MerchantAccountDao.class.getName());

    public static Map<String, Object> getAllMerchantName() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_MERCHANT_NAME);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_MERCHANT_NAME: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_NAME")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getAllMerchantId() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_MERCHANT_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_MERCHANT_ID: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getAllPartner() throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<PartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_ALL_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_PARTNER: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(new PartnerDto(rs.getInt("N_PARTNER_ID"), rs.getString("S_PARTNER_NAME")));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static List<MerchantProfileDto> getListMerchantProfile(Map mIn) throws Exception {
        Exception exception = null;
        List<MerchantProfileDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_MERCHANT_PROFILE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.get("name").toString());
            cs.setString(6, mIn.get("merchantName").toString());
            cs.setString(7, mIn.get("merchantId").toString());
            cs.setString(8, mIn.get("partnerId").toString());
            cs.setString(9, mIn.get("paymentMethod").toString());
            cs.setString(10, mIn.get("state").toString());
            cs.setString(11, mIn.get("type").toString());
            cs.setString(12, mIn.get("page").toString());
            cs.setString(13, mIn.get("page_size").toString());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_LIST_MERCHANT_PROFILE: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindMerchantProfileData(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalMerchantProfile(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_LIST_MERCHANT_PROFILE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.get("name").toString());
            cs.setString(6, mIn.get("merchantName").toString());
            cs.setString(7, mIn.get("merchantId").toString());
            cs.setString(8, mIn.get("partnerId").toString());
            cs.setString(9, mIn.get("paymentMethod").toString());
            cs.setString(10, mIn.get("state").toString());
            cs.setString(11, mIn.get("type").toString());
            cs.setString(12, mIn.get("page").toString());
            cs.setString(13, mIn.get("page_size").toString());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_LIST_MERCHANT_PROFILE: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getPartnerLists(Map mIn) throws Exception {
        Map<String, Object> dataList = new HashMap<>();
        dataList.put("list", getPartnerList(mIn));
        dataList.put("total", getTotalPartnerList(mIn));
        return dataList;
    }

    public static List<PartnerDto> getPartnerList(Map mIn) throws Exception {
        Exception exception = null;
        List<PartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(KEYWORD).toString());
            cs.setString(5, mIn.get(CONTRACT_STATUS).toString());
            cs.setString(6, mIn.get(PAGE).toString());
            cs.setString(7, mIn.get(PAGE_SIZE).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_PARTNER_LIST: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindPartner(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalPartnerList(Map mIn) throws Exception {
        Exception exception = null;
        Integer total = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_TOTAL_PARTNER_LIST);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get(KEYWORD).toString());
            cs.setString(5, mIn.get(CONTRACT_STATUS).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_TOTAL_PARTNER_LIST: " + error);
            } else {
                while (rs != null && rs.next()) {
                    total = rs.getInt("TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return total;
    }

    public static Map<String, Object> getPaynowMerchantByPartnerId(Integer partnerId) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PAYNOW_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_ALL_PAYNOW: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }

            if (result.size() > 0) {
                String merchantIds = result.stream().map(item -> item.getModelName()).collect(Collectors.joining(","));
                List<String> merchantIdsInstallment = merchantIdsInstallment112(merchantIds);
                if (merchantIdsInstallment != null && merchantIdsInstallment.size() > 0) {
                    Iterator<ModelDto> iterator = result.iterator();
                    while (iterator.hasNext()) {
                        ModelDto modelDto = iterator.next();
                        if (merchantIdsInstallment.contains(modelDto.getModelName()))
                            iterator.remove();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getInstallmentMerchantByPartnerId(Integer partnerId) throws Exception {
        Exception exception = null;
        Map<String, Object> dataList = new HashMap<>();
        List<ModelDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_INSTALLMENT_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, partnerId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_INSTALLMENT_BY_ID: " + error);
            } else {
                int count = 0;
                while (rs != null && rs.next()) {
                    count++;
                    result.add(new ModelDto(count, rs.getString("S_MERCHANT_ID")));
                }
            }
            if (result.size() > 0) {
                String merchantIds = result.stream().map(item -> item.getModelName()).collect(Collectors.joining(","));
                List<String> merchantIdsInstallment = merchantIdsInstallment112(merchantIds);
                if (merchantIdsInstallment != null && merchantIdsInstallment.size() > 0) {
                    Iterator<ModelDto> iterator = result.iterator();
                    while (iterator.hasNext()) {
                        ModelDto modelDto = iterator.next();
                        if (!merchantIdsInstallment.contains(modelDto.getModelName()))
                            iterator.remove();
                    }
                } else
                    //không tồn tại merchant id nào trong bảng msp112.tb_installment_merchant
                    result = new ArrayList<>();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        dataList.put("list", result);
        return dataList;
    }

    public static Map<String, Object> getListMerchantProfiles(Map mIn) throws Exception {
        Map<String, Object> dataList = new HashMap<>();
        dataList.put("list", getListMerchantProfile(mIn));
        dataList.put("total", getTotalMerchantProfile(mIn));
        return dataList;
    }

    private static MerchantProfileDto bindMerchantProfileData(ResultSet rs) throws SQLException {
        MerchantProfileDto model = new MerchantProfileDto();
        model.setId(rs.getInt("N_ID"));
        model.setName(rs.getString("S_NAME"));
        model.setLogo(rs.getString("S_LOGO"));
        model.setMerchantName(rs.getString("S_MERCHANT_NAME"));
        model.setAddress(rs.getString("S_ADDRESS"));
        model.setEmail(rs.getString("S_EMAIL"));
        model.setWebsite(rs.getString("S_WEBSITE"));
        List<String> fields = new ArrayList<String>(Arrays.asList(rs.getString("S_FIELDS").split(",")));
        model.setFields(fields);
        model.setCreateDate(rs.getTimestamp("D_CREATE"));
        model.setUpdateDate(rs.getTimestamp("D_UPDATE"));
        model.setCreate(rs.getString("S_CREATE"));
        model.setUpdate(rs.getString("S_UPDATE"));
        model.setPaymentMethod(rs.getString("S_PAY_METHOD"));
        model.setState(rs.getString("S_STATE"));
        model.setType(rs.getString("S_TYPE"));
        return model;
    }

    private static PartnerDto bindPartner(ResultSet rs) throws SQLException {
        PartnerDto model = new PartnerDto();
        model.setPartnerId(rs.getInt("N_ID"));
        model.setShortName(rs.getString("S_SHORT_NAME"));
        model.setPartnerName(rs.getString("S_PARTNER_NAME"));
        model.setBusinessName(rs.getString("S_BUSINESS_NAME"));
        return model;
    }


    private static List<String> merchantIdsInstallment112(String merchantIds) throws Exception {
        List<String> results = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(GET_INSTALLMENT_MERCHANT_IDS);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, merchantIds);

            cs.execute();
            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB installment_merchant_ids error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            results = new ArrayList<>();
            while (rs != null && rs.next())
                results.add(Util.getColumnString(rs, "S_MERCHANT_ID"));
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return results;
    }

    public static String getPartnerById(Integer partnerId) throws Exception {
        String partnerName = "";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Exception exception = null;
        try {
            conn = getConnection114();
            ps = conn.prepareStatement("SELECT * FROM ONEPARTNER.tbl_partner WHERE n_id = ?");
            ps.setInt(1, partnerId);
            rs = ps.executeQuery();
            if (rs != null && rs.next())
                partnerName = Util.getColumnString(rs, "S_SHORT_NAME");
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, ps, null, conn);
        }
        if (exception != null)
            throw exception;
        return partnerName;

    }

    public static Map checkHasPaymentLink(String profileIds) throws Exception {
        Map paymentLink = new HashMap();
        Connection conn = null;
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            conn = getConnection112();
            cs = conn.prepareCall(CHECK_HAS_PAYMENT_LINK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.CURSOR);
            cs.setString(4, profileIds);
            cs.execute();
            Integer nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200)
                throw new Exception("DB check_payment_link error: " + sResult);
            rs = (ResultSet) cs.getObject(3);
            while (rs != null  && rs.next())
                paymentLink.put(Util.getColumnInteger(rs, "N_PROFILE_ID"), Util.getColumnString(rs, "S_TYPE"));
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
            exception = ex;
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;

        return paymentLink;
    }


}
