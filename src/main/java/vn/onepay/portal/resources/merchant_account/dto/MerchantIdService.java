package vn.onepay.portal.resources.merchant_account.dto;

public class MerchantIdService {
    private String merchantId;
    private String currency;
    private String serviceType;
    private String mcc;
    private String activatedDate;
    private String description;
    private String updatedDate;
    public String getMerchantId() {
        return merchantId;
    }
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
    public String getCurrency() {
        return currency;
    }
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    public String getServiceType() {
        return serviceType;
    }
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
    public String getMcc() {
        return mcc;
    }
    public void setMcc(String mcc) {
        this.mcc = mcc;
    }
    public String getActivatedDate() {
        return activatedDate;
    }
    public void setActivatedDate(String activatedDate) {
        this.activatedDate = activatedDate;
    }
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    public String getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }
    @Override
    public String toString() {
        return "{" +
            " merchantId='" + getMerchantId() + "'" +
            ", currency='" + getCurrency() + "'" +
            ", serviceType='" + getServiceType() + "'" +
            ", mcc='" + getMcc() + "'" +
            ", activatedDate='" + getActivatedDate() + "'" +
            ", description='" + getDescription() + "'" +
            ", updatedDate='" + getUpdatedDate() + "'" +
            "}";
    }

}
