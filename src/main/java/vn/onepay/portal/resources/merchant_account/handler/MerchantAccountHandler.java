package vn.onepay.portal.resources.merchant_account.handler;

import com.google.gson.Gson;

import org.apache.commons.lang3.time.DateUtils;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.client.MerchantAccountClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchant_account.dto.MerchantAccountParam;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import static vn.onepay.portal.Util.sendResponse;

public class MerchantAccountHandler implements IConstants {
    private static final Logger logger = Logger.getLogger(MerchantAccountHandler.class.getName());

    // start
    public static void getPartnerList(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MerchantAccountClient.getpartnerList());
            } catch (Exception e) {
                logger.log(Level.WARNING, "getPartnerList: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getPartnerId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String idString = ctx.request().getParam("partnerId");
                sendResponse(ctx, 200, MerchantAccountClient.getPartnerById(idString));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "getPartnerId :  ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantByPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("id", request.getParam("id") != null ? request.getParam("id") : "");
                mIn.put("partnerId", request.getParam("partnerId") != null ? request.getParam("partnerId") : "");
                sendResponse(ctx, 200, MerchantAccountClient.getMerchantByPartner(mIn));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "getPartnerId :  ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void merchantAccountGetById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String idString = ctx.request().getParam("id");
                sendResponse(ctx, 200, MerchantAccountClient.merchantAccountGetById(idString));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "merchantAccountGetById:  ", e);
                ctx.fail(e);
            }
        }, false, null);

    }

    public static void deleteMerchantAccount(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String idString = ctx.request().getParam("id");
                sendResponse(ctx, MerchantAccountClient.deleteMerchantAccount(idString) == 0 ? 500 : 200);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "deleteMerchantAccount: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void createMerchantAccount(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                MerchantAccountParam accountParam = new MerchantAccountParam();
                Gson gson = new Gson();
                accountParam = gson.fromJson(ctx.getBodyAsJson().toString(), MerchantAccountParam.class);
                JsonObject res =  MerchantAccountClient.createMerchantAccount(accountParam);
                sendResponse(ctx, res.getInteger("status"),new JsonObject(res.getString("message")));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "createMerchantAccount :  ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateMerchantAccount(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                MerchantAccountParam accountParam = new MerchantAccountParam();
                Gson gson = new Gson();
                accountParam = gson.fromJson(ctx.getBodyAsJson().toString(), MerchantAccountParam.class);
                JsonObject res =  MerchantAccountClient.updateMerchantAccount(accountParam);
                sendResponse(ctx,res.getInteger("status"), new JsonObject(res.getString("message")));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "updateMerchantAccount :  ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchMerchantAccount(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("fromDate", request.getParam("fromDate") != null ? request.getParam("fromDate") : "");
                mIn.put("toDate", request.getParam("toDate") != null ? request.getParam("toDate") : "");
                mIn.put("keyword", request.getParam("keyword") != null ? request.getParam("keyword") : "");
                mIn.put("partnerId", request.getParam("partnerId") != null ? request.getParam("partnerId").trim() : "");
                mIn.put("businessName", request.getParam("businessName") != null ? request.getParam("businessName").trim() : "");
                mIn.put("merchantAccountId", request.getParam("merchantAccountId") != null ? request.getParam("merchantAccountId").trim() : "");
                mIn.put("merchantAccountName", request.getParam("merchantAccountName") != null ? request.getParam("merchantAccountName").trim() : "");
                mIn.put("contractCode", request.getParam("contractCode") != null ? request.getParam("contractCode").trim() : "");
                mIn.put("subcontractCode", request.getParam("subcontractCode") != null ? request.getParam("subcontractCode").trim() : "");
                mIn.put("category", request.getParam("category") != null ? request.getParam("category").trim() : "");
                mIn.put("province", request.getParam("province") != null ? request.getParam("province").trim() : "");
                mIn.put("taxCode", request.getParam("taxCode") != null ? request.getParam("taxCode").trim() : "");
                mIn.put("serviceType", request.getParam("serviceType") != null ? request.getParam("serviceType").trim() : "");
                mIn.put("mcc", request.getParam("mcc") != null ? request.getParam("mcc").trim() : "");
                mIn.put("merchantId", request.getParam("merchantId") != null ? request.getParam("merchantId").trim() : "");
                mIn.put(PAGE, 0);
                mIn.put(PAGESIZE, request.getParam(PAGESIZE) != null ? request.getParam(PAGESIZE).trim() : 100);
                sendResponse(ctx, 200, MerchantAccountClient.searchMerchantAccount(mIn));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "searchMerchantAccount :  ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                Map<String, Object> mIn = new HashMap<>();
                JsonObject body = ctx.getBodyAsJson();
                mIn.put("fromDate", body.getString("fromDate") != null ? body.getString("fromDate") : "");
                mIn.put("toDate", body.getString("toDate") != null ? body.getString("toDate") : "");
                mIn.put("keyword", body.getString("keyword") != null ? body.getString("keyword") : "");
                mIn.put("partnerId", body.getString("partnerId") != null ? body.getString("partnerId").trim() : "");
                mIn.put("businessName",
                        body.getString("businessName") != null ? body.getString("businessName").trim() : "");
                mIn.put("contractCode",
                        body.getString("contractCode") != null ? body.getString("contractCode").trim()
                                : "");
                mIn.put("contractCode",
                        body.getString("contractCode") != null ? body.getString("contractCode").trim() : "");
                mIn.put("category",
                        body.getString("category") != null ? body.getString("category").trim() : "");
                mIn.put("province",
                        body.getString("province") != null ? body.getString("province").trim() : "");
                mIn.put("subcontractCode",
                        body.getString("subcontractCode") != null ? body.getString("subcontractCode").trim() : "");
                mIn.put("taxCode", body.getString("taxCode") != null ? body.getString("taxCode").trim() : "");
                mIn.put("serviceType", body.getString("serviceType") != null ? body.getString("serviceType").trim() : "");
                mIn.put("mcc", body.getString("mcc") != null ? body.getString("mcc").trim() : "");
                mIn.put("merchantId", body.getString("merchantId") != null ? body.getString("merchantId").trim() : "");
                //mIn.put(JDATA, body.getString(JDATA) != null ? body.getString(JDATA).trim() : "");
                mIn.put(PAGE, 0);
                mIn.put(PAGESIZE, body.getInteger(PAGESIZE) != null ? body.getInteger(PAGESIZE) : 100);
                JsonObject json = MerchantAccountClient.searchMerchantAccount(mIn);
                int totalRows = json.getInteger("totalItems");
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "merchant_account" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("merchant_account");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
               // fileDownloadDto.setConditions(mIn.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                            QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(),
                            ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                            QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(),
                            ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD MERCHANT ACCOUNT_Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
