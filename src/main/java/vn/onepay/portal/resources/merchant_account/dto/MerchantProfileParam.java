package vn.onepay.portal.resources.merchant_account.dto;

import java.sql.Timestamp;
import java.util.List;

public class MerchantProfileParam {
    private int id;
    private String name;
    private String logo;
    private String merchantName;
    private String address;
    private String email;
    private String mobile;
    private String website;
    private List<String> fields;
    private Integer partnerId;
    private List<MerchantProfilePaymentMethod> paymentMethod;
    private String operator;

    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getLogo() {
        return logo;
    }
    public void setLogo(String logo) {
        this.logo = logo;
    }
    public String getMerchantName() {
        return merchantName;
    }
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public String getMobile() {
        return mobile;
    }
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getWebsite() {
        return website;
    }
    public void setWebsite(String website) {
        this.website = website;
    }
    public List<String> getFields() {
        return fields;
    }
    public void setFields(List<String> fields) {
        this.fields = fields;
    }
    public Integer getPartnerId() {
        return partnerId;
    }
    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }
    public List<MerchantProfilePaymentMethod> getPaymentMethod() {
        return paymentMethod;
    }
    public void setPaymentMethod(List<MerchantProfilePaymentMethod> paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    public String getOperator() {
        return operator;
    }
    public void setOperator(String operator) {
        this.operator = operator;
    }
}
