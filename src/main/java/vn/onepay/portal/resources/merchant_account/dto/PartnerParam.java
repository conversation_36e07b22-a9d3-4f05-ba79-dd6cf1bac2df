package vn.onepay.portal.resources.merchant_account.dto;

import java.sql.Timestamp;
import java.util.List;

public class PartnerParam {
    
    private String partnerName;
    private String description;
    private String traddingName;
    private String traddingId;
    private String categoryCode;
    private String goodsDescription;
    private String website;
    private String locale;
    private String timezone;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String postCode;
    private String country;
    private String contactName;
    private String phoneNumber;
    private String escalationPhoneNumber;
    private String mobilePhoneNumber;
    private String faxNumber;
    private String email;
    private Timestamp createDate;
    private Timestamp lastDate;
    private Integer active;
    private String registerName;
    private Integer id;
    private String shortName;
    private String type;
    private String mst;
    private Integer tinhthanhId;
    private Integer tinhthanhVpqgId;
    private String soDkkd;
    private Timestamp ngayDkkd;
    private Integer nganhngheId;
    private String nganhngheKhac;
    private String danhgiaRisk;
    private String sale;
    private String available; 
    

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTraddingName() {
        return this.traddingName;
    }

    public void setTraddingName(String traddingName) {
        this.traddingName = traddingName;
    }

    public String getTraddingId() {
        return this.traddingId;
    }

    public void setTraddingId(String traddingId) {
        this.traddingId = traddingId;
    }

    public String getCategoryCode() {
        return this.categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getGoodsDescription() {
        return this.goodsDescription;
    }

    public void setGoodsDescription(String goodsDescription) {
        this.goodsDescription = goodsDescription;
    }

    public String getWebsite() {
        return this.website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getLocale() {
        return this.locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getTimezone() {
        return this.timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getAddressLine1() {
        return this.addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return this.addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostCode() {
        return this.postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCountry() {
        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getContactName() {
        return this.contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEscalationPhoneNumber() {
        return this.escalationPhoneNumber;
    }

    public void setEscalationPhoneNumber(String escalationPhoneNumber) {
        this.escalationPhoneNumber = escalationPhoneNumber;
    }

    public String getMobilePhoneNumber() {
        return this.mobilePhoneNumber;
    }

    public void setMobilePhoneNumber(String mobilePhoneNumber) {
        this.mobilePhoneNumber = mobilePhoneNumber;
    }

    public String getFaxNumber() {
        return this.faxNumber;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Timestamp getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastDate() {
        return this.lastDate;
    }

    public void setLastDate(Timestamp lastDate) {
        this.lastDate = lastDate;
    }

    public Integer getActive() {
        return this.active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public String getRegisterName() {
        return this.registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getShortName() {
        return this.shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMst() {
        return this.mst;
    }

    public void setMst(String mst) {
        this.mst = mst;
    }

    public Integer getTinhthanhId() {
        return this.tinhthanhId;
    }

    public void setTinhthanhId(Integer tinhthanhId) {
        this.tinhthanhId = tinhthanhId;
    }

    public Integer getTinhthanhVpqgId() {
        return this.tinhthanhVpqgId;
    }

    public void setTinhthanhVpqgId(Integer tinhthanhVpqgId) {
        this.tinhthanhVpqgId = tinhthanhVpqgId;
    }

    public String getSoDkkd() {
        return this.soDkkd;
    }

    public void setSoDkkd(String soDkkd) {
        this.soDkkd = soDkkd;
    }

    public Timestamp getNgayDkkd() {
        return this.ngayDkkd;
    }

    public void setNgayDkkd(Timestamp ngayDkkd) {
        this.ngayDkkd = ngayDkkd;
    }

    public Integer getNganhngheId() {
        return this.nganhngheId;
    }

    public void setNganhngheId(Integer nganhngheId) {
        this.nganhngheId = nganhngheId;
    }

    public String getNganhngheKhac() {
        return this.nganhngheKhac;
    }

    public void setNganhngheKhac(String nganhngheKhac) {
        this.nganhngheKhac = nganhngheKhac;
    }

    public String getDanhgiaRisk() {
        return this.danhgiaRisk;
    }

    public void setDanhgiaRisk(String danhgiaRisk) {
        this.danhgiaRisk = danhgiaRisk;
    }

    public String getSale() {
        return this.sale;
    }

    public void setSale(String sale) {
        this.sale = sale;
    }

    public String getAvailable() {
        return this.available;
    }

    public void setAvailable(String available) {
        this.available = available;
    }

    
    // @Override
    // public String toString() {
    //     return "{" +
    //         "  Id='" + getId() + "'" +
    //         ", partnerId='" + getPartnerId() + "'" +
    //         ", businessName='" + getBusinessName() + "'" +
    //         ", merchantAccountName='" + getMerchantAccountName() + "'" +
    //         ", contractCode='" + getContractCode() + "'" +
    //         ", subcontractCode='" + getSubcontractCode() + "'" +
    //         ", taxCode='" + getTaxCode() + "'" +
    //         ", merchantIdService='" + getMerchantIdService() + "'" +
    //         "}";
    // }

}
