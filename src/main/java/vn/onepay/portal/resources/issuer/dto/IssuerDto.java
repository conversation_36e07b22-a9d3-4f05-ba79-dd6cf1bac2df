package vn.onepay.portal.resources.issuer.dto;

import java.io.Serializable;

public class IssuerDto implements Serializable {
    private Integer no;
    private Integer id;
    private String swiftCode;
    private String shortName;
    private String fullName;
    private String description;
    private String createdAt;
    private String modifiedAt;
    private String state;

    public IssuerDto(Integer no, Integer id, String swiftCode, String shortName, String fullName, String description, String createdAt, String modifiedAt, String state) {
        this.no = no;
        this.id = id;
        this.swiftCode = swiftCode;
        this.shortName = shortName;
        this.fullName = fullName;
        this.description = description;
        this.createdAt = createdAt;
        this.modifiedAt = modifiedAt;
        this.state = state;
    }

    public Integer getNo() {
        return no;
    }

    public void setNo(Integer no) {
        this.no = no;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getModifiedAt() {
        return modifiedAt;
    }

    public void setModifiedAt(String modifiedAt) {
        this.modifiedAt = modifiedAt;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
