package vn.onepay.portal.resources.issuer;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.issuer.dao.IssuerDao;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;

import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class IssuerHandler implements IConstants {
    private static final Logger _LOGGER = Logger.getLogger(IssuerHandler.class.getName());

    public static void deleteIssuer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put("id", ctx.request().getParam("id") != null
                        ? Integer.parseInt(ctx.request().getParam("id")) : 0);
                String userId = ctx.request().getHeader(X_USER_ID);
                User user = UserDao.get(userId);
                String userEmail = "";
                if (user != null){
                    userEmail = user.getEmail();
                }
                mIn.put("email", userEmail);
                mIn.put("userId", ctx.request().getHeader(X_USER_ID));
                mIn.put("requestId", ctx.request().getHeader(X_REQUEST_ID));
                mIn.put("realIp", ctx.request().getHeader(X_REAL_IP));
                sendResponse(ctx, OK, IssuerDao.deleteIssuer(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "insert or update: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insertOrUpdate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyIn = ctx.getBodyAsJson();
                String userId = ctx.request().getHeader(X_USER_ID);
                User user = UserDao.get(userId);
                String userEmail = "";
                if (user != null){
                    userEmail = user.getEmail();
                }
                bodyIn.put("email", userEmail);
                bodyIn.put("userId", ctx.request().getHeader(X_USER_ID));
                bodyIn.put("requestId", ctx.request().getHeader(X_REQUEST_ID));
                bodyIn.put("realIp", ctx.request().getHeader(X_REAL_IP));
                sendResponse(ctx, OK, IssuerDao.insertOrUpdateIssuer(bodyIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "insert or update: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchIssuer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put(PAGE, ctx.request().getParam(PAGE));
                mIn.put(PAGESIZE, ctx.request().getParam(PAGESIZE));
                mIn.put(KEYWORD, ctx.request().getParam(KEYWORD));
                sendResponse(ctx, OK, IssuerDao.searchIssuer(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
