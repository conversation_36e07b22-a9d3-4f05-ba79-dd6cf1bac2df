package vn.onepay.portal.resources.issuer.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.audit_trail.dao.AuditTrailDAO;
import vn.onepay.portal.resources.audit_trail.request.AuditTrailRequest;
import vn.onepay.portal.resources.issuer.dto.IssuerDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;

public class IssuerDao extends Db implements IConstants {

    public static Map<String, Object> deleteIssuer(JsonObject mIn) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ISSUER#DELETE(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, mIn.getInteger("id", 0));
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                throw new Exception(error);
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("status", nerror);
        result.put("message", error);
        AuditTrailRequest auditRq = new AuditTrailRequest();
        auditRq.setCode(UUID.randomUUID().toString());
        auditRq.setModule("IPORTAL_SERVICE");
        auditRq.setUserId(mIn.getString("userId", BLANK));
        auditRq.setRequestId(mIn.getString("requestId", BLANK));
        auditRq.setRealIp(mIn.getString("realIp", BLANK));
        auditRq.setSource("SYSTEM_MANAGEMENT_ISSUER_MANAGEMENT");
        auditRq.setType("DELETE");
        auditRq.setAction("ISUSSER_MANAGEMENT_DELETED");
        String userEmail = mIn.getString("email", BLANK);
        auditRq.setActor(userEmail);
        String content = userEmail.concat(" đã xoá thành công issuer id: ").concat(mIn.getInteger("id", 0).toString());
        auditRq.setContent(content);
        auditRq.setOldValueString(mIn.toString());
        AuditTrailDAO.insertAuditTrail(auditRq);
        return result;
    }

    public static Map<String, Object> insertOrUpdateIssuer(JsonObject mIn) throws Exception {
        Map<String, Object> result = new HashMap<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ISSUER#INSERT_OR_UPDATE(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setObject(3, mIn.getInteger("id", 0));
            cs.setObject(4, mIn.getString("swiftCode", BLANK));
            cs.setObject(5, mIn.getString("shortName", BLANK));
            cs.setObject(6, mIn.getString("fullName", BLANK));
            cs.setObject(7, mIn.getString("description", BLANK));
            cs.executeQuery();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            if (nerror != 200) {
                throw new Exception(error);
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("status", nerror);
        result.put("message", error);
        AuditTrailRequest auditRq = new AuditTrailRequest();
        auditRq.setCode(UUID.randomUUID().toString());
        auditRq.setModule("IPORTAL_SERVICE");
        auditRq.setUserId(mIn.getString("userId", BLANK));
        auditRq.setRequestId(mIn.getString("requestId", BLANK));
        auditRq.setRealIp(mIn.getString("realIp", BLANK));
        auditRq.setSource("SYSTEM_MANAGEMENT_ISSUER_MANAGEMENT");
        auditRq.setType(mIn.getInteger("id", 0).compareTo(0) > 0 ? "UPDATE" : "INSERT");
        auditRq.setAction(mIn.getInteger("id", 0).compareTo(0) > 0 ? "ISSUER_MANAGEMENT_UPDATED" : "ISSUER_MANAGEMENT_INSERT");
        String userEmail = mIn.getString("email", BLANK);
        auditRq.setActor(userEmail);
        String content = userEmail.concat(" đã lưu thành công issuer");
        auditRq.setContent(content);
        auditRq.setNewValueString(mIn.toString());
        AuditTrailDAO.insertAuditTrail(auditRq);
        return result;
    }

    public static Map<String, Object> searchIssuer(JsonObject mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<IssuerDto> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total;
        int nerror = 0;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ISSUER#SEARCH(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setObject(5, mIn.getString(KEYWORD));
            cs.setInt(6, Integer.parseInt(mIn.getString(PAGE)));
            cs.setInt(7, Integer.parseInt(mIn.getString(PAGESIZE)));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB pkg_issuer#search: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(buildIssuer(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        result.put("status", nerror);
        return result;
    }


    private static IssuerDto buildIssuer(ResultSet rs) throws SQLException {
        return new IssuerDto(
                Util.getColumnInteger(rs, "R_NUM"),
                Util.getColumnInteger(rs, "N_ID"),
                Util.getColumnString(rs, "S_SWIFT_CODE"),
                Util.getColumnString(rs, "S_SHORT_NAME"),
                Util.getColumnString(rs, "S_FULL_NAME"),
                Util.getColumnString(rs, "S_DESC"),
                Util.getColumnString(rs, "D_CREATE"),
                Util.getColumnString(rs, "D_UPDATE"),
                Util.getColumnString(rs, "S_STATE")
        );
    }
}
