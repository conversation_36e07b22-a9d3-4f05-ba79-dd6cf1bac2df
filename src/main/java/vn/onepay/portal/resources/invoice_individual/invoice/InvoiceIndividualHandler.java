package vn.onepay.portal.resources.invoice_individual.invoice;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class InvoiceIndividualHandler implements IConstants {

    private static final Logger _LOGGER = Logger.getLogger(InvoiceIndividualHandler.class.getName());


    public static void exportInvoiceIndividual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                try {
                    JsonObject body = ctx.getBodyAsJson();
                    if (body.getString("id") == null)
                        throw IErrors.VALIDATION_ERROR;
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("id", body.getString("id"));
                    String xUserId = ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID);
                    String fileName = "export_invoice_individual" + System.currentTimeMillis();
                    String fileHashName = Convert.hash(fileName);
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, xUserId);
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setUser(xUserId);
                    fileDownloadDto.setFile_type("invoice_individual");
                    fileDownloadDto.setFile_name(fileName);
                    fileDownloadDto.setFile_hash_name(fileHashName);
                    fileDownloadDto.setExt("zip");
                    FileDownloadDao.insert(fileDownloadDto);
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                    sendResponse(ctx, 200, fileDownloadDto);
                } catch (Exception e) {
                    _LOGGER.log(Level.FINE, "Export Receipt - export file error: ", e);
                    ctx.fail(e);
                }
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    private static boolean isEmpty(String value) {
        return value == null || value.isEmpty();
    }


    public static void searchListIndividual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put(PAGE, ctx.request().getParam(PAGE));
                mIn.put(PAGESIZE, ctx.request().getParam(PAGESIZE));
                mIn.put(FROMDATE, ctx.request().getParam(FROMDATE));
                mIn.put(TODATE, ctx.request().getParam(TODATE));
                mIn.put(STATE, ctx.request().getParam(STATE));
                mIn.put(SERVICE, ctx.request().getParam(SERVICE));
                mIn.put(TRANSACTION_VALUE, ctx.request().getParam(TRANSACTION_VALUE));
                mIn.put(AR_RECEIPT_NUMBER, ctx.request().getParam(AR_RECEIPT_NUMBER));
                mIn.put(INVOICE_NUMBER, ctx.request().getParam(INVOICE_NUMBER));
                sendResponse(ctx, OK, InvoiceIndividualDao.searchListIndividual(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void changeState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put(IDS, ctx.getBodyAsJson().getString(IDS));
                mIn.put(STATE, ctx.getBodyAsJson().getString(STATE));
                mIn.put(UPDATE, ctx.getBodyAsJson().getString(UPDATE));
                sendResponse(ctx, OK, InvoiceIndividualDao.changeState(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateSameReceiptNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                if (body.getString("data") == null || body.getString("lastReceiptNumber") == null)
                    throw IErrors.VALIDATION_ERROR;
                Integer lastReceiptNumber = Integer.parseInt(body.getString("lastReceiptNumber"));
                List<String> listUpdate = new ArrayList<>(Arrays.asList(body.getString("data").trim().split(",")));

                Util.sendResponse(ctx, 200, InvoiceIndividualDao.updateSameReceiptNumber(listUpdate, lastReceiptNumber));
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "Error in updateSameReceiptNumber", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateReceiptNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                if (body.getString("data") == null || body.getString("lastReceiptNumber") == null || body.getString("currentReceiptNumber") == null)
                    throw IErrors.VALIDATION_ERROR;
                Integer lastReceiptNumber = Integer.parseInt(body.getString("lastReceiptNumber"));
                Integer currentReceiptNumber = Integer.parseInt(body.getString("currentReceiptNumber"));
                List<String> listUpdate = new ArrayList<>(Arrays.asList(body.getString("data").trim().split(",")));

                Util.sendResponse(ctx, 200, InvoiceIndividualDao.updateReceiptNumber(listUpdate, lastReceiptNumber, currentReceiptNumber));
            } catch (Exception ex) {
                _LOGGER.log(Level.FINE, "Export Receipt - Error updateReceiptNumber", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }

    public static void updateLastReceiptNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int lastReceiptNumber = ctx.getBodyAsJson().getInteger(LAST_RECEIPT_NUMBER);
                sendResponse(ctx, OK, InvoiceIndividualDao.updateLastReceiptNumber(lastReceiptNumber));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateInvoiceMisaNumber(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put(ID, ctx.getBodyAsJson().getInteger(ID));
                mIn.put(INVOICE_NUMBER, ctx.getBodyAsJson().getString(INVOICE_NUMBER));
                mIn.put(UPDATE, ctx.getBodyAsJson().getString(UPDATE));
                sendResponse(ctx, OK, InvoiceIndividualDao.updateInvoiceMisaNumber(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void changeSettlementDate(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = new JsonObject();
                mIn.put(IDS, ctx.getBodyAsJson().getString(IDS));
                mIn.put(SETTLEMENT_TIME, ctx.getBodyAsJson().getString(SETTLEMENT_TIME));
                mIn.put(UPDATE, ctx.getBodyAsJson().getString(UPDATE));
                sendResponse(ctx, OK, InvoiceIndividualDao.changeSettlementDate(mIn));
            } catch (Exception e) {
                _LOGGER.log(Level.SEVERE, "search: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
