package vn.onepay.portal.resources.invoice_individual.invoice_transaction;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class InvoiceTransactionDao extends Db implements IConstants {
    public static List<InvoiceTransaction> getListInvTransaction(JsonObject mIn) throws SQLException {
        List<InvoiceTransaction> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_transaction_export(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, mIn.getInteger(INVOICE_ID));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB pkg_invoice_transaction_export error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(convertInvoiceTransaction(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return lists;
    }

    public static InvoiceTransaction convertInvoiceTransaction(ResultSet rs) throws SQLException {
        InvoiceTransaction invTransaction = new InvoiceTransaction();
        invTransaction.setId(Util.getColumnInteger(rs,"N_ID"));
        invTransaction.setMerchantId(Util.getColumnString(rs,"S_MERCHANT_ID"));
        invTransaction.setTransactionId(Util.getColumnString(rs,"S_TRANSACTION_ID"));
        invTransaction.setTransactionRef(Util.getColumnString(rs,"S_TRANSACTION_REF"));
        invTransaction.setOrderInfo(Util.getColumnString(rs,"S_ORDER_INFO"));
        invTransaction.setDateImport(Util.getColumnString(rs,"D_DATE_IMPORT"));
        invTransaction.setDateUpdate(Util.getColumnString(rs,"D_DATE_UPDATE"));
        invTransaction.setDateTransaction(Util.getColumnString(rs,"D_DATE_TRANSACTION"));
        invTransaction.setCurrency(Util.getColumnString(rs,"S_CURRENCY"));
        invTransaction.setAmount(Util.getColumnDouble(rs,"N_AMOUNT"));
        invTransaction.setAmountVND(Util.getColumnDouble(rs,"N_AMOUNT_VND"));
        invTransaction.setPayChannel(Util.getColumnString(rs,"S_PAY_CHANNEL"));
        invTransaction.setBankConfigId(Util.getColumnInteger(rs,"N_BANK_CONFIG_ID"));
        invTransaction.setBankCard(Util.getColumnString(rs,"S_BANK_CARD"));
        invTransaction.setResponseCode(Util.getColumnString(rs,"S_RESPONSE_CODE"));
        invTransaction.setState(Util.getColumnString(rs,"S_STATE"));
        invTransaction.setParentId(Util.getColumnString(rs,"S_ID_PARENT"));
        invTransaction.setSource(Util.getColumnString(rs,"S_SOURCE"));
        invTransaction.setTransactionType(Util.getColumnString(rs,"S_TRANSACTION_TYPE"));
        invTransaction.setAuthCode(Util.getColumnString(rs,"S_AUTH_CODE"));
        invTransaction.setAcquirer(Util.getColumnString(rs,"S_ACQUIRER"));
        invTransaction.setCardNo(Util.getColumnString(rs,"S_CARD_NO"));
        invTransaction.setBinBank(Util.getColumnString(rs,"S_BIN_BANK"));
        invTransaction.setBinCountry(Util.getColumnString(rs,"S_BIN_COUNTRY"));
        invTransaction.setBinGroupId(Util.getColumnInteger(rs,"N_BIN_GROUP_ID"));
        invTransaction.setRrn(Util.getColumnString(rs,"S_RRN"));
        invTransaction.setRrn(Util.getColumnString(rs,"S_DESC"));
        invTransaction.setFuncId(Util.getColumnInteger(rs,"N_FUNC_ID"));
        invTransaction.setExchangeRateSell(Util.getColumnDouble(rs,"N_EXCHANGE_RATE_SELL"));
        invTransaction.setExchangeRateBuy(Util.getColumnDouble(rs,"N_EXCHANGE_RATE_BUY"));
        invTransaction.setCurrencyFee(Util.getColumnString(rs,"S_CURRENCY_FEE"));
        invTransaction.setFixFee(Util.getColumnDouble(rs,"N_FIX_FEE"));
        invTransaction.setFixFeeVND(Util.getColumnDouble(rs,"N_FIX_FEE_VND"));
        invTransaction.setPercentFee(Util.getColumnDouble(rs,"N_PERCENT_FEE"));
        invTransaction.setFeeFailed(Util.getColumnDouble(rs,"N_FEE_FAILED"));
        invTransaction.setFeeFailedVND(Util.getColumnDouble(rs,"N_FEE_FAILED_VND"));
        invTransaction.setFeeFailedVND(Util.getColumnDouble(rs,"N_FEE_FAILED_VND"));
        invTransaction.setStateAdvance(Util.getColumnString(rs,"S_STATE_ADVANCE"));
        invTransaction.setStateProcess(Util.getColumnString(rs,"S_STATE_PROCESS"));
        invTransaction.setItaBank(Util.getColumnString(rs,"S_ITA_BANK"));
        invTransaction.setItaTerm(Util.getColumnString(rs,"N_ITA_TERM"));
        invTransaction.setItaPercentFee(Util.getColumnDouble(rs,"N_ITA_PERCENT_FEE"));
        invTransaction.setFeeItaADV(Util.getColumnDouble(rs,"N_FEE_ITA_ADV"));
        invTransaction.setFeeFailedADV(Util.getColumnDouble(rs,"N_FEE_FAILED_ADV"));
        invTransaction.setFeeEcomADV(Util.getColumnDouble(rs,"N_FEE_ECOM_ADV"));
        invTransaction.setTotalFeeADV(Util.getColumnDouble(rs,"N_TOTAL_FEE_ADV"));
        invTransaction.setAmountADV(Util.getColumnDouble(rs,"N_AMOUNT_ADV"));
        invTransaction.setFuncInput(Util.getColumnString(rs,"S_FUNC_INPUT"));
        invTransaction.setFuncItaId(Util.getColumnInteger(rs,"N_FUNC_ITA_ID"));
        invTransaction.setFuncItaInput(Util.getColumnString(rs,"S_FUNC_ITA_INPUT"));
        invTransaction.setPercentFeeVND(Util.getColumnDouble(rs,"N_PERCENT_FEE_VND"));
        invTransaction.setAmountAdvCurrent(Util.getColumnDouble(rs,"N_AMOUNT_ADV_CURRENT"));
        invTransaction.setCollectedFee(Util.getColumnDouble(rs,"N_COLLECTED_FEE"));
        invTransaction.setFeeItaVAT(Util.getColumnDouble(rs,"N_FEE_ITA_VAT"));
        invTransaction.setFixFeeVAT(Util.getColumnDouble(rs,"N_FIX_FEE_VAT"));
        invTransaction.setMerchantDiscountAmount(Util.getColumnDouble(rs,"N_MERCHANT_DISCOUNT_AMOUNT"));
        invTransaction.setOriginalAmount(Util.getColumnDouble(rs,"N_ORIGINAL_AMOUNT"));
        invTransaction.setPartnerDiscountAmount(Util.getColumnDouble(rs,"N_PARTNER_DISCOUNT_AMOUNT"));
        invTransaction.setPercentFeeVAT(Util.getColumnDouble(rs,"N_PERCENT_FEE_VAT"));
        invTransaction.setReceivableFee(Util.getColumnDouble(rs,"N_RECEIVABLE_FEE"));
        invTransaction.setTotalFeeVAT(Util.getColumnDouble(rs,"N_TOTAL_FEE_VAT"));
        invTransaction.setContractRelation(Util.getColumnString(rs,"S_CONTRACT_RELATION"));
        invTransaction.setData(Util.getColumnString(rs,"S_DATA"));
        invTransaction.setBankMerchantId(Util.getColumnString(rs,"S_BANK_MERCHANT_ID"));
        invTransaction.setClientId(Util.getColumnString(rs,"S_CLIENT_ID"));
        invTransaction.setCustomerName(Util.getColumnString(rs,"S_CUSTOMER_NAME"));
        invTransaction.setInsBrandId(Util.getColumnString(rs,"S_INS_BRAND_ID"));
        invTransaction.setMerchantReturnUrl(Util.getColumnString(rs,"S_MERCH_RETURN_URL"));
        invTransaction.setMcc(Util.getColumnString(rs,"S_MCC"));
        invTransaction.setScanned(Util.getColumnInteger(rs,"N_SCANNED"));
        invTransaction.setPlatform(Util.getColumnString(rs,"S_PLATFORM"));
        invTransaction.setInvoiceId(Util.getColumnInteger(rs,"N_ID_INVOICE"));
        invTransaction.setInvoiceDate(Util.getColumnString(rs,"D_INV_IMPORT_DATE"));
        invTransaction.setRowNum(Util.getColumnInteger(rs, "ROW_NUM"));
        invTransaction.setTenDKKD(Util.getColumnString(rs, "S_TEN_DKKD"));
        invTransaction.setTenDV(Util.getColumnString(rs, "S_TEN_DV"));
        invTransaction.setPartnerName(Util.getColumnString(rs, "S_PARTNER_NAME"));
        invTransaction.setSettlement(Util.getColumnString(rs, "D_SETTLEMENT"));
        invTransaction.setReceiptNumber(Util.getColumnString(rs, "S_RECEIPT_NUMBER"));
        invTransaction.setFromDate(Util.getColumnString(rs, "D_FROM_DATE"));
        invTransaction.setToDate(Util.getColumnString(rs, "D_TO_DATE"));
        invTransaction.setContractCode(Util.getColumnString(rs, "S_CONTRACT_CODE"));
        invTransaction.setAddendum(Util.getColumnString(rs, "S_ADDENDUM"));
        invTransaction.setMst(Util.getColumnString(rs, "S_MST"));
        invTransaction.setFeeSuccessADV(Util.getColumnDouble(rs, "N_FEE_SUCCESS_ADV"));
        return invTransaction;
    }
}
