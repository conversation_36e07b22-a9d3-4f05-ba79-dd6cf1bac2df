package vn.onepay.portal.resources.invoice_individual.invoice;

import java.io.Serializable;

public class InvoiceIndividual implements Serializable {
    private Integer id;
    private Integer rowNum;
    private String fromDate;
    private String toDate;
    private String service;
    private String createdDate;
    private String updatedDate;
    private String note;
    private String updatedBy;
    private String state;
    private String misaCode;
    private String misaMcc;
    private Integer count;
    private Double amount;
    private Double fee;
    private Double advanceAmount;
    private Double tax;
    private Double ecomFee;
    private Double itaFee;
    private Double discountFee;
    private String detail;
    private String merchantId;
    private String receiptNumber;
    private String misaInvoiceNumber;
    private String settleDate;
    private Double price;
    private Double vat;
    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getVat() {
        return vat;
    }

    public void setVat(Double vat) {
        this.vat = vat;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMisaCode() {
        return misaCode;
    }

    public void setMisaCode(String misaCode) {
        this.misaCode = misaCode;
    }

    public String getMisaMcc() {
        return misaMcc;
    }

    public void setMisaMcc(String misaMcc) {
        this.misaMcc = misaMcc;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Double getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(Double advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getEcomFee() {
        return ecomFee;
    }

    public void setEcomFee(Double ecomFee) {
        this.ecomFee = ecomFee;
    }

    public Double getItaFee() {
        return itaFee;
    }

    public void setItaFee(Double itaFee) {
        this.itaFee = itaFee;
    }

    public Double getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Double discountFee) {
        this.discountFee = discountFee;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public String getMisaInvoiceNumber() {
        return misaInvoiceNumber;
    }

    public void setMisaInvoiceNumber(String misaInvoiceNumber) {
        this.misaInvoiceNumber = misaInvoiceNumber;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }
}
