package vn.onepay.portal.resources.invoice_individual.invoice;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class InvoiceIndividualDao extends Db implements IConstants {
    private static String UPDATE_LAST_RECEIPT_NUMBER = "UPDATE ONESCHED.tb_mark SET s_mark = ? WHERE s_service_id = ? ";

    public static ActionDto changeSettlementDate(JsonObject mIn) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_individual_update_settlement_date(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.getString(IDS));
            cs.setString(4, mIn.getString(SETTLEMENT_TIME));
            cs.setString(5, mIn.getString(UPDATE));
            cs.execute();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            actionDto = new ActionDto();
            actionDto.setNResult(nerror);
            actionDto.setSResult(error);
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in pkg_invoice_individual_change_state", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, cs, null, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto changeState(JsonObject mIn) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_individual_change_state(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, mIn.getString(IDS));
            cs.setString(4, mIn.getString(STATE));
            cs.setString(5, mIn.getString(UPDATE));
            cs.execute();
            error = cs.getString(2);
            nerror = cs.getInt(1);
            actionDto = new ActionDto();
            actionDto.setNResult(nerror);
            actionDto.setSResult(error);
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in pkg_invoice_individual_change_state", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, cs, null, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateSameReceiptNumber(List<String> listUpdate, Integer lastReceiptNumber) throws Exception {
        ActionDto actionDto = new ActionDto();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            con.setAutoCommit(false);
            cs = con.prepareCall("{call ONEFIN.pkg_invoice_individual_update_receipt_number(?,?)}");
            lastReceiptNumber = lastReceiptNumber++;
            for (String updateItem : listUpdate) {
                cs.setString(1, StringUtils.leftPad(String.valueOf(lastReceiptNumber), 7, "0"));
                cs.setInt(2, Convert.parseInt(updateItem, 0));
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            updateLastReceiptNumber(lastReceiptNumber);
            actionDto.setNResult(200);
            actionDto.setSResult("Update Successfully!");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateSoChungTu ", ex);
            if (con != null)
                con.rollback();
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateReceiptNumber(List<String> listUpdate, Integer lastReceiptNumber, Integer currentReceiptNumber) throws Exception {
        ActionDto actionDto = new ActionDto();
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            con.setAutoCommit(false);
            cs = con.prepareCall("{call ONEFIN.pkg_invoice_individual_update_receipt_number(?,?)}");
            for (String updateItem : listUpdate) {
                currentReceiptNumber++;
                cs.setString(1, StringUtils.leftPad(String.valueOf(currentReceiptNumber), 7, "0"));
                cs.setInt(2, Convert.parseInt(updateItem, 0));
                cs.addBatch();
            }
            cs.executeBatch();
            con.commit();
            updateLastReceiptNumber(lastReceiptNumber);
            actionDto.setNResult(200);
            actionDto.setSResult("Update Successfully!");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in updateSoChungTu ", ex);
            if (con != null)
                con.rollback();
            exception = ex;
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateLastReceiptNumber(Integer lastReceiptNumber) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        PreparedStatement ps = null;
        try {
            con = getConnection114();
            ps = con.prepareStatement(UPDATE_LAST_RECEIPT_NUMBER);
            ps.setInt(1, lastReceiptNumber);
            ps.setString(2, "LAST_RECEIPT_NUMBER_INVOICE_INDIVIDUAL");
            ps.execute();
            actionDto = new ActionDto();
            actionDto.setNResult(200);
            actionDto.setSResult("OK");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in LAST_RECEIPT_NUMBER_INVOICE_INDIVIDUAL", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, ps, null, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static ActionDto updateInvoiceMisaNumber(JsonObject mIn) throws Exception {
        ActionDto actionDto = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.pkg_invoice_individual_update_misa_number(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, mIn.getInteger(ID));
            cs.setString(4, mIn.getString(INVOICE_NUMBER));
            cs.setString(5, mIn.getString(UPDATE));
            cs.execute();
            actionDto = new ActionDto();
            actionDto.setNResult(200);
            actionDto.setSResult("OK");
        } catch (Exception ex) {
            logger.log(Level.FINE, "Error in pkg_invoice_individual_update_misa_number", ex);
            exception = ex;
        } finally {
            closeConnectionDB(null, cs, null, con);
        }
        if (exception != null)
            throw exception;
        return actionDto;
    }

    public static Map<String, Object> searchListIndividual(JsonObject mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<InvoiceIndividual> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total;
        int nerror = 0;
        int lastReceiptNumber = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_individual_search(?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setString(5, mIn.getString(FROMDATE));
            cs.setString(6, mIn.getString(TODATE));
            cs.setString(7, mIn.getString(STATE));
            cs.setString(8, mIn.getString(SERVICE));
            cs.setString(9, mIn.getString(TRANSACTION_VALUE));
            cs.setString(10, mIn.getString(AR_RECEIPT_NUMBER));
            cs.setString(11, mIn.getString(INVOICE_NUMBER));
            cs.setInt(12, Integer.parseInt(mIn.getString(PAGE)));
            cs.setInt(13, Integer.parseInt(mIn.getString(PAGESIZE)));
            cs.registerOutParameter(14, OracleTypes.NUMBER);
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            lastReceiptNumber = cs.getInt(14);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB pkg_invoice_transaction_get_list_transaction error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(convertIndividual(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        result.put("lastReceiptNumber", lastReceiptNumber);
        return result;
    }

    public static List<InvoiceIndividual> getList(JsonObject mIn) throws SQLException {

        List<InvoiceIndividual> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_individual_get_list(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.getString(IDS));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB pkg_invoice_individual_get_list error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(convertIndividual(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }

        return lists;
    }

    public static InvoiceIndividual convertIndividual(ResultSet rs) throws SQLException {
        InvoiceIndividual invoiceIndividual = new InvoiceIndividual();
        invoiceIndividual.setRowNum(Util.getColumnInteger(rs, "ROW_NUM"));
        invoiceIndividual.setId(Util.getColumnInteger(rs, "N_ID"));
        invoiceIndividual.setFromDate(Util.getColumnString(rs, "D_FROM_DATE"));
        invoiceIndividual.setToDate(Util.getColumnString(rs, "D_TO_DATE"));
        invoiceIndividual.setService(Util.getColumnString(rs, "S_SERVICE"));
        invoiceIndividual.setCreatedDate(Util.getColumnString(rs, "D_CREATE"));
        invoiceIndividual.setUpdatedDate(Util.getColumnString(rs, "D_UPDATE"));
        invoiceIndividual.setNote(Util.getColumnString(rs, "S_NOTE"));
        invoiceIndividual.setUpdatedBy(Util.getColumnString(rs, "S_UPDATE"));
        invoiceIndividual.setState(Util.getColumnString(rs, "S_STATE"));
        invoiceIndividual.setMisaCode(Util.getColumnString(rs, "S_MISA_CODE"));
        invoiceIndividual.setMisaMcc(Util.getColumnString(rs, "S_MISA_MCC"));
        invoiceIndividual.setCount(Util.getColumnInteger(rs, "N_COUNT"));
        invoiceIndividual.setAmount(Util.getColumnDouble(rs, "N_AMOUNT"));
        invoiceIndividual.setFee(Util.getColumnDouble(rs, "N_FEE"));
        invoiceIndividual.setAdvanceAmount(Util.getColumnDouble(rs, "N_ADVANCE_AMOUNT"));
        invoiceIndividual.setTax(Util.getColumnDouble(rs, "N_TAX"));
        invoiceIndividual.setEcomFee(Util.getColumnDouble(rs, "N_ECOM_FEE"));
        invoiceIndividual.setItaFee(Util.getColumnDouble(rs, "N_ITA_FEE"));
        invoiceIndividual.setDiscountFee(Util.getColumnDouble(rs, "N_DISCOUNT_FEE"));
        invoiceIndividual.setDetail(Util.getColumnString(rs, "S_DETAIL"));
        invoiceIndividual.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
        invoiceIndividual.setReceiptNumber(Util.getColumnString(rs, "S_RECEIPT_NUMBER"));
        invoiceIndividual.setMisaInvoiceNumber(Util.getColumnString(rs, "S_MISA_INVOICE_NUMBER"));
        invoiceIndividual.setSettleDate(Util.getColumnString(rs, "D_SETTLEMENT"));
        invoiceIndividual.setPrice(Util.getColumnDouble(rs, "N_PRICE"));
        invoiceIndividual.setVat(Util.getColumnDouble(rs, "N_VAT"));
        return invoiceIndividual;
    }
}
