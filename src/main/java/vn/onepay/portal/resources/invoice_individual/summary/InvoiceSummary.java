package vn.onepay.portal.resources.invoice_individual.summary;

import java.io.Serializable;

public class InvoiceSummary implements Serializable {

    //    N_ID
    private Integer id;
    private Integer rowNum;
    private Integer invoiceId;
    private Integer configId;
    private Integer partnerId;
    private String contractCode;
    private String addendum;
    private String misaCode;
    private Double amountUSD;
    private Double amountVND;
    private Integer count;
    private Double amount;
    private Double fee;
    private Double advanceAmount;
    private Double tax;
    private Double ecomFee;
    private Double itaFee;
    private Double discountFee;

    private String detail;
    private String merchantId;
    private String service;
    private String tenDV;
    private String tenDKKD;
    private String partnerName;
    private String settlement;
    private String receiptNumber;
    private String fromDate;
    private String toDate;
    private String mst;
    private String advanceAccount;
    private String misaMCC;
    private Double failedFee;
    private Double successFee;

    public Double getFailedFee() {
        return failedFee;
    }

    public void setFailedFee(Double failedFee) {
        this.failedFee = failedFee;
    }

    public Double getSuccessFee() {
        return successFee;
    }

    public void setSuccessFee(Double successFee) {
        this.successFee = successFee;
    }

    public String getMisaMCC() {
        return misaMCC;
    }

    public void setMisaMCC(String misaMCC) {
        this.misaMCC = misaMCC;
    }

    public String getAdvanceAccount() {
        return advanceAccount;
    }

    public void setAdvanceAccount(String advanceAccount) {
        this.advanceAccount = advanceAccount;
    }

    public String getMst() {
        return mst;
    }

    public void setMst(String mst) {
        this.mst = mst;
    }

    public Double getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Double discountFee) {
        this.discountFee = discountFee;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public String getTenDV() {
        return tenDV;
    }

    public void setTenDV(String tenDV) {
        this.tenDV = tenDV;
    }

    public String getTenDKKD() {
        return tenDKKD;
    }

    public void setTenDKKD(String tenDKKD) {
        this.tenDKKD = tenDKKD;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getSettlement() {
        return settlement;
    }

    public void setSettlement(String settlement) {
        this.settlement = settlement;
    }

    public Double getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(Double advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getAddendum() {
        return addendum;
    }

    public void setAddendum(String addendum) {
        this.addendum = addendum;
    }

    public String getMisaCode() {
        return misaCode;
    }

    public void setMisaCode(String misaCode) {
        this.misaCode = misaCode;
    }

    public Double getAmountUSD() {
        return amountUSD;
    }

    public void setAmountUSD(Double amountUSD) {
        this.amountUSD = amountUSD;
    }

    public Double getAmountVND() {
        return amountVND;
    }

    public void setAmountVND(Double amountVND) {
        this.amountVND = amountVND;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getEcomFee() {
        return ecomFee;
    }

    public void setEcomFee(Double ecomFee) {
        this.ecomFee = ecomFee;
    }

    public Double getItaFee() {
        return itaFee;
    }

    public void setItaFee(Double itaFee) {
        this.itaFee = itaFee;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }
}
