package vn.onepay.portal.resources.invoice_individual.invoice_transaction;

import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.ExportReceiptDAO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.ExportReceiptHandler;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isEmpty;
import static vn.onepay.portal.Util.sendResponse;

public class InvoiceTransactionHandler implements IConstants {
    private final static Logger logger = Logger.getLogger(InvoiceTransactionHandler.class.getName());
    public static void exportInvoiceIndividual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                try {
                    JsonObject body = ctx.getBodyAsJson();
                    if (body.getString("id") == null)
                        throw IErrors.VALIDATION_ERROR;
                    Map<String, Object> mIn = new HashMap<>();
                    mIn.put("id", body.getString("id"));
                    mIn.put("password", body.getBoolean("password"));

                    // requestData in message
                    Map<String, Object> requestData = new HashMap<>();
                    requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                    requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                    requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                    // Initial file information
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
                    String date = sdf.format(new Date());
                    Calendar c = Calendar.getInstance();
                    c.set(Calendar.DAY_OF_MONTH, 1);
                    String fileName = "Input Misa " + date + " DS " + new SimpleDateFormat("MM.dd").format(c.getTime())
                            + (!isEmpty(body.getString("payment_channel")) ? ("-" + body.getString("payment_channel")) : "")
                            + (!isEmpty(body.getString("advance_type")) ? ("-" + body.getString("advance_type")) : "")
                            + "-" + new SimpleDateFormat("yyyy.MM.dd").format(new Date());
                    String fileHashName = "";
                    try {
                        fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                    } catch (NoSuchAlgorithmException e) {
                        ctx.fail(e);
                    } catch (UnsupportedEncodingException e) {
                        ctx.fail(e);
                    }
                    requestData.put(FILE_HASH_NAME, fileHashName);
                    requestData.put(FILE_NAME, fileName);
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                    fileDownloadDto.setFile_type("export_receipt");
                    fileDownloadDto.setFile_name(fileName);
                    fileDownloadDto.setFile_hash_name(fileHashName);
                    int totalRows = ExportReceiptDAO.exportReceiptDownload(mIn).size();
                    if (totalRows <= Config.getFileRowLevel()) {
                        fileDownloadDto.setExt("xls");
                    } else {
                        fileDownloadDto.setExt("zip");
                    }
                    FileDownloadDao.insert(fileDownloadDto);
                    if (totalRows <= Config.getFileRowLevel()) {
                        //fileDownload.setExt("csv");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                    } else {
                        //fileDownload.setExt("zip");
                        QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                    }
                    sendResponse(ctx, 200, fileDownloadDto);
                } catch (Exception e) {
                    logger.log(Level.FINE, "Export Receipt - export file error: ", e);
                    ctx.fail(e);
                }
            } catch (Exception ex) {
                logger.log(Level.FINE, "", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
