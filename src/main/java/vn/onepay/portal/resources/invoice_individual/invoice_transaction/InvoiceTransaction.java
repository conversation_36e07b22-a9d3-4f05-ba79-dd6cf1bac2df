package vn.onepay.portal.resources.invoice_individual.invoice_transaction;

import java.io.Serializable;

public class InvoiceTransaction  implements Serializable {
    private Integer id;
    private Integer invoiceId;
    private String invoiceDate;
    private String merchantId;
    private String transactionId;
    private String transactionRef;
    private String orderInfo;
    private String dateImport;
    private String dateUpdate;
    private String dateTransaction;
    private String currency;
    private Double amount;
    private Double amountVND;
    private String payChannel;
    private Integer bankConfigId;
    private String bankCard;
    private String responseCode;
    private String state;
    private String parentId;
    private String source;
    private String transactionType;
    private String authCode;
    private String acquirer;
    private String cardNo;
    private String binBank;
    private String binCountry;
    private Integer binGroupId;
    private String rrn;
    private String desc;
    private Integer funcId;
    private Double exchangeRateSell;
    private Double exchangeRateBuy;
    private String currencyFee;
    private Double fixFee;
    private Double fixFeeVND;
    private Double percentFee;
    private Double feeFailed;
    private Double feeFailedVND;
    private String stateAdvance;
    private String stateProcess;
    private String itaBank;
    private String itaTerm;
    private Double itaPercentFee;
    private Double feeItaADV;
    private Double feeFailedADV;
    private Double feeEcomADV;
    private Double totalFeeADV;
    private Double amountADV;
    private String funcInput;
    private Integer funcItaId;
    private String funcItaInput;
    private Double percentFeeVND;
    private Double amountAdvCurrent;
    private Double collectedFee;
    private Double feeItaVAT;
    private Double fixFeeVAT;
    private Double merchantDiscountAmount;
    private Double originalAmount;
    private Double partnerDiscountAmount;
    private Double percentFeeVAT;
    private Double receivableFee;
    private Double totalFeeVAT;
    private String contractRelation;
    private String data;
    private String bankMerchantId;
    private String clientId;
    private String customerName;
    private String insBrandId;
    private String merchantReturnUrl;
    private String mcc;
    private Integer scanned;
    private String platform;
    private Integer rowNum;
    private String partnerName;
    private String tenDKKD;
    private String tenDV;
    /**
     * Ngay hoach toan
     */
    private String settlement;

    /**
     * So chung tu
     */
    private String receiptNumber;


    /**
     * Thoi gian tren hoa don
     */
    private String fromDate;

    /**
     * Thoi gian tren hoa don
     */
    private String toDate;
    /**
     * So hop dong
     */
    private String contractCode;
    /**
     * So phu luc
     */
    private String addendum;

    /**
     * Mã Số Thue
     */
    private String mst;

    /**
     *  Tổng phí giao dịch thành công
     */
    private Double feeSuccessADV;;

    public Double getFeeSuccessADV() {
        return feeSuccessADV;
    }

    public void setFeeSuccessADV(Double feeSuccessADV) {
        this.feeSuccessADV = feeSuccessADV;
    }

    public String getMst() {
        return mst;
    }

    public void setMst(String mst) {
        this.mst = mst;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getAddendum() {
        return addendum;
    }

    public void setAddendum(String addendum) {
        this.addendum = addendum;
    }

    public String getSettlement() {
        return settlement;
    }

    public void setSettlement(String settlement) {
        this.settlement = settlement;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getTenDKKD() {
        return tenDKKD;
    }

    public void setTenDKKD(String tenDKKD) {
        this.tenDKKD = tenDKKD;
    }

    public String getTenDV() {
        return tenDV;
    }

    public void setTenDV(String tenDV) {
        this.tenDV = tenDV;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTransactionRef() {
        return transactionRef;
    }

    public void setTransactionRef(String transactionRef) {
        this.transactionRef = transactionRef;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getDateImport() {
        return dateImport;
    }

    public void setDateImport(String dateImport) {
        this.dateImport = dateImport;
    }

    public String getDateUpdate() {
        return dateUpdate;
    }

    public void setDateUpdate(String dateUpdate) {
        this.dateUpdate = dateUpdate;
    }

    public String getDateTransaction() {
        return dateTransaction;
    }

    public void setDateTransaction(String dateTransaction) {
        this.dateTransaction = dateTransaction;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAmountVND() {
        return amountVND;
    }

    public void setAmountVND(Double amountVND) {
        this.amountVND = amountVND;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public Integer getBankConfigId() {
        return bankConfigId;
    }

    public void setBankConfigId(Integer bankConfigId) {
        this.bankConfigId = bankConfigId;
    }

    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBinBank() {
        return binBank;
    }

    public void setBinBank(String binBank) {
        this.binBank = binBank;
    }

    public String getBinCountry() {
        return binCountry;
    }

    public void setBinCountry(String binCountry) {
        this.binCountry = binCountry;
    }

    public Integer getBinGroupId() {
        return binGroupId;
    }

    public void setBinGroupId(Integer binGroupId) {
        this.binGroupId = binGroupId;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getFuncId() {
        return funcId;
    }

    public void setFuncId(Integer funcId) {
        this.funcId = funcId;
    }

    public Double getExchangeRateSell() {
        return exchangeRateSell;
    }

    public void setExchangeRateSell(Double exchangeRateSell) {
        this.exchangeRateSell = exchangeRateSell;
    }

    public Double getExchangeRateBuy() {
        return exchangeRateBuy;
    }

    public void setExchangeRateBuy(Double exchangeRateBuy) {
        this.exchangeRateBuy = exchangeRateBuy;
    }

    public String getCurrencyFee() {
        return currencyFee;
    }

    public void setCurrencyFee(String currencyFee) {
        this.currencyFee = currencyFee;
    }

    public Double getFixFee() {
        return fixFee;
    }

    public void setFixFee(Double fixFee) {
        this.fixFee = fixFee;
    }

    public Double getFixFeeVND() {
        return fixFeeVND;
    }

    public void setFixFeeVND(Double fixFeeVND) {
        this.fixFeeVND = fixFeeVND;
    }

    public Double getPercentFee() {
        return percentFee;
    }

    public void setPercentFee(Double percentFee) {
        this.percentFee = percentFee;
    }

    public Double getFeeFailed() {
        return feeFailed;
    }

    public void setFeeFailed(Double feeFailed) {
        this.feeFailed = feeFailed;
    }

    public Double getFeeFailedVND() {
        return feeFailedVND;
    }

    public void setFeeFailedVND(Double feeFailedVND) {
        this.feeFailedVND = feeFailedVND;
    }

    public String getStateAdvance() {
        return stateAdvance;
    }

    public void setStateAdvance(String stateAdvance) {
        this.stateAdvance = stateAdvance;
    }

    public String getStateProcess() {
        return stateProcess;
    }

    public void setStateProcess(String stateProcess) {
        this.stateProcess = stateProcess;
    }

    public String getItaBank() {
        return itaBank;
    }

    public void setItaBank(String itaBank) {
        this.itaBank = itaBank;
    }

    public String getItaTerm() {
        return itaTerm;
    }

    public void setItaTerm(String itaTerm) {
        this.itaTerm = itaTerm;
    }

    public Double getItaPercentFee() {
        return itaPercentFee;
    }

    public void setItaPercentFee(Double itaPercentFee) {
        this.itaPercentFee = itaPercentFee;
    }

    public Double getFeeItaADV() {
        return feeItaADV;
    }

    public void setFeeItaADV(Double feeItaADV) {
        this.feeItaADV = feeItaADV;
    }

    public Double getFeeFailedADV() {
        return feeFailedADV;
    }

    public void setFeeFailedADV(Double feeFailedADV) {
        this.feeFailedADV = feeFailedADV;
    }

    public Double getFeeEcomADV() {
        return feeEcomADV;
    }

    public void setFeeEcomADV(Double feeEcomADV) {
        this.feeEcomADV = feeEcomADV;
    }

    public Double getTotalFeeADV() {
        return totalFeeADV;
    }

    public void setTotalFeeADV(Double totalFeeADV) {
        this.totalFeeADV = totalFeeADV;
    }

    public Double getAmountADV() {
        return amountADV;
    }

    public void setAmountADV(Double amountADV) {
        this.amountADV = amountADV;
    }

    public String getFuncInput() {
        return funcInput;
    }

    public void setFuncInput(String funcInput) {
        this.funcInput = funcInput;
    }

    public Integer getFuncItaId() {
        return funcItaId;
    }

    public void setFuncItaId(Integer funcItaId) {
        this.funcItaId = funcItaId;
    }

    public String getFuncItaInput() {
        return funcItaInput;
    }

    public void setFuncItaInput(String funcItaInput) {
        this.funcItaInput = funcItaInput;
    }

    public Double getPercentFeeVND() {
        return percentFeeVND;
    }

    public void setPercentFeeVND(Double percentFeeVND) {
        this.percentFeeVND = percentFeeVND;
    }

    public Double getAmountAdvCurrent() {
        return amountAdvCurrent;
    }

    public void setAmountAdvCurrent(Double amountAdvCurrent) {
        this.amountAdvCurrent = amountAdvCurrent;
    }

    public Double getCollectedFee() {
        return collectedFee;
    }

    public void setCollectedFee(Double collectedFee) {
        this.collectedFee = collectedFee;
    }

    public Double getFeeItaVAT() {
        return feeItaVAT;
    }

    public void setFeeItaVAT(Double feeItaVAT) {
        this.feeItaVAT = feeItaVAT;
    }

    public Double getFixFeeVAT() {
        return fixFeeVAT;
    }

    public void setFixFeeVAT(Double fixFeeVAT) {
        this.fixFeeVAT = fixFeeVAT;
    }

    public Double getMerchantDiscountAmount() {
        return merchantDiscountAmount;
    }

    public void setMerchantDiscountAmount(Double merchantDiscountAmount) {
        this.merchantDiscountAmount = merchantDiscountAmount;
    }

    public Double getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(Double originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Double getPartnerDiscountAmount() {
        return partnerDiscountAmount;
    }

    public void setPartnerDiscountAmount(Double partnerDiscountAmount) {
        this.partnerDiscountAmount = partnerDiscountAmount;
    }

    public Double getPercentFeeVAT() {
        return percentFeeVAT;
    }

    public void setPercentFeeVAT(Double percentFeeVAT) {
        this.percentFeeVAT = percentFeeVAT;
    }

    public Double getReceivableFee() {
        return receivableFee;
    }

    public void setReceivableFee(Double receivableFee) {
        this.receivableFee = receivableFee;
    }

    public Double getTotalFeeVAT() {
        return totalFeeVAT;
    }

    public void setTotalFeeVAT(Double totalFeeVAT) {
        this.totalFeeVAT = totalFeeVAT;
    }

    public String getContractRelation() {
        return contractRelation;
    }

    public void setContractRelation(String contractRelation) {
        this.contractRelation = contractRelation;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getBankMerchantId() {
        return bankMerchantId;
    }

    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getInsBrandId() {
        return insBrandId;
    }

    public void setInsBrandId(String insBrandId) {
        this.insBrandId = insBrandId;
    }

    public String getMerchantReturnUrl() {
        return merchantReturnUrl;
    }

    public void setMerchantReturnUrl(String merchantReturnUrl) {
        this.merchantReturnUrl = merchantReturnUrl;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public Integer getScanned() {
        return scanned;
    }

    public void setScanned(Integer scanned) {
        this.scanned = scanned;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }
}
