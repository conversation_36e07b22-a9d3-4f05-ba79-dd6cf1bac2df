package vn.onepay.portal.resources.invoice_individual.summary;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class InvoiceSummaryDao extends Db implements IConstants {
    public static Map<String, Object> getListSummaryByInvoiceIdAndService(Map<String, Object> mIn) throws SQLException {
        Map<String, Object> result = new HashMap<>();
        List<InvoiceSummary> lists = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String error = "";
        int total;
        int nerror = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{ call ONEFIN.pkg_invoice_summary_export(?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.registerOutParameter(4, OracleTypes.NUMBER);
            cs.setInt(5, Integer.parseInt(mIn.get(ID).toString()));
            cs.executeQuery();
            error = cs.getString(3);
            nerror = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            total = cs.getInt(4);
            if (nerror != 200) {
                logger.log(Level.SEVERE, "DB pkg_invoice_summary_export error: {0}", error);
            } else {
                while (rs != null && rs.next()) {
                    lists.add(convertInvoiceSummary(rs));
                }
            }
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        result.put("list", lists);
        result.put("totalItems", total);
        return result;
    }

    public static InvoiceSummary convertInvoiceSummary(ResultSet rs) throws SQLException {
        InvoiceSummary invoiceSummary = new InvoiceSummary();
        invoiceSummary.setRowNum(Util.getColumnInteger(rs, "ROW_NUM"));
        invoiceSummary.setId(Util.getColumnInteger(rs, "N_ID"));
        invoiceSummary.setInvoiceId(Util.getColumnInteger(rs, "N_ID_INVOICE"));
        invoiceSummary.setPartnerId(Util.getColumnInteger(rs, "N_ID_PARTNER"));
        invoiceSummary.setConfigId(Util.getColumnInteger(rs, "N_ID_CONFIG"));
        invoiceSummary.setContractCode(Util.getColumnString(rs, "S_CONTRACT_CODE"));
        invoiceSummary.setAddendum(Util.getColumnString(rs, "S_ADDENDUM"));
        invoiceSummary.setMisaCode(Util.getColumnString(rs, "S_MISA_CODE"));
        invoiceSummary.setAmountUSD(Util.getColumnDouble(rs, "N_AMOUNT_USD"));
        invoiceSummary.setAmountVND(Util.getColumnDouble(rs, "N_AMOUNT_VND"));
        invoiceSummary.setCount(Util.getColumnInteger(rs, "N_COUNT"));
        invoiceSummary.setAmount(Util.getColumnDouble(rs, "N_AMOUNT"));
        invoiceSummary.setFee(Util.getColumnDouble(rs, "N_FEE"));
        invoiceSummary.setAdvanceAmount(Util.getColumnDouble(rs, "N_ADVANCE_AMOUNT"));
        invoiceSummary.setTax(Util.getColumnDouble(rs, "N_TAX"));
        invoiceSummary.setEcomFee(Util.getColumnDouble(rs, "N_ECOM_FEE"));
        invoiceSummary.setItaFee(Util.getColumnDouble(rs, "N_ITA_FEE"));
        invoiceSummary.setDiscountFee(Util.getColumnDouble(rs, "N_DISCOUNT_FEE"));
        invoiceSummary.setDetail(Util.getColumnString(rs, "S_DETAIL"));
        invoiceSummary.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
        invoiceSummary.setService(Util.getColumnString(rs, "S_SERVICE"));
        invoiceSummary.setTenDV(Util.getColumnString(rs, "S_TEN_DV"));
        invoiceSummary.setTenDKKD(Util.getColumnString(rs, "S_TEN_DKKD"));
        invoiceSummary.setPartnerName(Util.getColumnString(rs, "S_PARTNER_NAME"));
        invoiceSummary.setSettlement(Util.getColumnString(rs, "D_SETTLEMENT"));
        invoiceSummary.setReceiptNumber(Util.getColumnString(rs, "S_RECEIPT_NUMBER"));
        invoiceSummary.setFromDate(Util.getColumnString(rs, "D_FROM_DATE"));
        invoiceSummary.setToDate(Util.getColumnString(rs, "D_TO_DATE"));
        invoiceSummary.setMst(Util.getColumnString(rs, "S_MST"));
        invoiceSummary.setAdvanceAccount(Util.getColumnString(rs, "S_ADVANCE_ACCOUNT"));
        invoiceSummary.setMisaMCC(Util.getColumnString(rs, "S_MISA_MCC"));
        invoiceSummary.setFailedFee(Util.getColumnDouble(rs, "N_FAILED_FEE"));
        invoiceSummary.setSuccessFee(Util.getColumnDouble(rs, "N_SUCCESS_FEE"));
        return invoiceSummary;
    }
}
