package vn.onepay.portal.resources.bank_mid_management.dto;

import java.util.Date;
import java.util.List;


public class MerchantBank {

    private Integer id;
    private Integer bankMidId;
    private String bankMerchantId;
    private String merchantBankKey;
    private String create;
    private String update;
    private String desc;
    private Integer priority;
    private String activeNone3ds;
    private String enableNoneCsc;
    private String payChannel;
    private Date expireDate;
    private String ciac;

    public String getCiac() {
        return ciac;
    }

    public void setCiac(String ciac) {
        this.ciac = ciac;
    }
    private String uploadDate;
    private String state;
    private String acquirerBankId;
    private List<AcquirerBankMerchant> acquirerBankMerchants;
    private String merchantId;
    private String keyName;

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getActiveNone3ds() {
        return activeNone3ds;
    }
    public void setActiveNone3ds(String activeNone3ds) {
        this.activeNone3ds = activeNone3ds;
    }
    public String getEnableNoneCsc() {
        return enableNoneCsc;
    }
    public void setEnableNoneCsc(String enableNoneCsc) {
        this.enableNoneCsc = enableNoneCsc;
    }
    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getMerchantBankKey() {
        return merchantBankKey;
    }
    public void setMerchantBankKey(String merchantBankKey) {
        this.merchantBankKey = merchantBankKey;
    }
    
    public String getCreate() {
        return create;
    }
    public void setCreate(String create) {
        this.create = create;
    }
    public String getUpdate() {
        return update;
    }
    public void setUpdate(String update) {
        this.update = update;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public Integer getPriority() {
        return priority;
    }
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    public Integer getBankMidId() {
        return bankMidId;
    }
    public void setBankMidId(Integer bankMidId) {
        this.bankMidId = bankMidId;
    }
    public String getBankMerchantId() {
        return bankMerchantId;
    }
    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }
    public Date getExpireDate() {
        return expireDate;
    }
    public void setExpireDate(Date expire) {
        this.expireDate = expire;
    }

    public String getUploadDate() {
        return uploadDate;
    }
    public void setUploadDate(String uploadedFile) {
        this.uploadDate = uploadedFile;
    }

    public String getState() {
        return state;
    }
    public void setState(String state) {
        this.state = state;
    }

    public String getAcquirerBankId() {
        return acquirerBankId;
    }
    public void setAcquirerBankId(String acquirerBankId) {
        this.acquirerBankId = acquirerBankId;
    }

    public List<AcquirerBankMerchant> getAcquirerBankMerchants() {
        return acquirerBankMerchants;
    }
    public void setAcquirerBankMerchants(List<AcquirerBankMerchant> acquirerBankMerchants) {
        this.acquirerBankMerchants = acquirerBankMerchants;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantIdsString) {
        this.merchantId = merchantIdsString;
    }

    public String getKeyName() {
        return keyName;
    }
    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }
}
