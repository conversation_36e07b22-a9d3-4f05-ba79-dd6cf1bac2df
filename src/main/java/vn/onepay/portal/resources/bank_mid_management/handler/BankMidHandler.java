package vn.onepay.portal.resources.bank_mid_management.handler;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MspClient;
import vn.onepay.portal.resources.bank_mid_management.dao.BankMidDao;
import vn.onepay.portal.resources.bank_mid_management.dto.AcquirerBankMerchant;
import vn.onepay.portal.resources.bank_mid_management.dto.MerchantBank;
import vn.onepay.portal.resources.bank_mid_management.dto.P12KeyInfo;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.config.dao.PartnerFeeConfigDAO;
import vn.onepay.portal.resources.fee.config.dto.PartnerFeeServiceFunc;
import vn.onepay.portal.resources.system_management.cyber_mpgs_management.dao.AcquirerRuleConfigDao;
import vn.onepay.portal.resources.user.UserDao;
import java.io.FileInputStream;
import java.nio.file.FileSystems;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.PrivateKey;
import java.security.Security;
import java.util.Enumeration;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import java.util.Base64;

import static vn.onepay.portal.Util.sendResponse;

/*
 * Author: DuyNP Date: 16/03/2023
 */
public class BankMidHandler implements IConstants {

    private static final String[] REQUIRED_MPGS_ACQUIRERS = {"5", "9", "11", "17"};
    private static final String[] REQUIRED_CYBERSOURCE_ACQUIRERS =
            {"2", "4", "7", "8", "10", "12", "13"};

    private BankMidHandler() {}

    private static Logger logger = Logger.getLogger(BankMidHandler.class.getName());

    public static void getBankMid(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(KEYWORD, Util.handleHttpRequest(request, KEYWORD, "").toString());
                mIn.put(PAY_CHANNEL, Util.handleHttpRequest(request, PAY_CHANNEL, "").toString());
                mIn.put(ACQUIRER, Util.handleHttpRequest(request, ACQUIRER, "").toString());
                mIn.put(CONTRACT_TYPE,
                        Util.handleHttpRequest(request, CONTRACT_TYPE, "").toString());
                mIn.put(STATUS, Util.handleHttpRequest(request, STATUS, "").toString());
                mIn.put(PAGE,
                        Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer
                        .parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "20").toString()));
                Map<String, Object> data = BankMidDao.getBankMid(mIn);

                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getBankMid: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getBankMid ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void saveBankMid(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                JsonObject data = ctx.getBodyAsJson();
                JsonObject obj = data.getJsonObject("obj");
                String userId = ctx.get(S_USER_ID);
                obj.put(USER_ID, userId);
                Integer result = BankMidDao.insertBankMid(obj);
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else if (result == 201) {
                    map.put("message", "Bank MID is exitst");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "saveBankMid: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateBankMid(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                JsonObject data = ctx.getBodyAsJson();
                JsonObject obj = data.getJsonObject("obj");
                String userId = ctx.get(S_USER_ID);
                obj.put(USER_ID, userId);
                Integer result = BankMidDao.updateBankMid(obj);
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else if (result == 201) {
                    map.put("message", "Bank MID is not exitst");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "saveBankMid: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Integer id = Integer.parseInt(request.getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, id);
                mIn.put(KEYWORD, Util.handleHttpRequest(request, KEYWORD, "").toString());
                mIn.put(PAGE,
                        Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer
                        .parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "20").toString()));
                Map<String, Object> data = BankMidDao.getMerchantBank(mIn);
                
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getMerchantBank: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantBank ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * API lưu Merchant Bank (Màn hình Admin => Bank MID Management => Chi tiết Bank Mid => Add)
     */
    public static void saveMerchantBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                Set<FileUpload> fileUploadSet = ctx.fileUploads();
                HttpServerRequest request = ctx.request();
                // Lấy dữ liệu từ form-data
                String bodyStr = ctx.request().getFormAttribute("body");
                logger.log(Level.INFO, "bodyStr saveMerchantBank: {0}", bodyStr);
                JsonObject bodyJson = null;
                if (bodyStr != null) {
                    bodyJson = new JsonObject(bodyStr);
                }
                JsonObject obj = bodyJson.getJsonObject("obj");
                Integer bankMidId = Integer.parseInt(request.getParam("id"));
                String contractType = bodyJson.getString("contractType");
                String password = obj.getString("password");
                String acquirerBankIdOld = obj.getString("acquirerBankId"); 
                JsonObject result = BankMidDao.insertMerchantBank(obj, bankMidId);
                int acquirerIdInt = Integer.parseInt(bodyJson.getString("acquirerId"));
                if (result.getInteger("code") == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    var mapMerchant = BankMidDao.getByMerchantById(bankMidId,
                            obj.getString(BANK_MERCHANT_ID).trim(), contractType);
                    if (mapMerchant.containsKey("200")) {
                        var merchantBankDetail = mapMerchant.get("200");
                        String service = merchantBankDetail.getPayChannels();
                        String bank = merchantBankDetail.getPartnerId();
                        // Nếu là hợp đồng 2B của dịch vụ QT thì insert thêm merchant bank vào
                        // module
                        // Switch Cyber/MPGS
                        if ("2B".equalsIgnoreCase(contractType) && "QT".equalsIgnoreCase(service)
                                && obj.getString(BANK_MERCHANT_ID) != null
                                && obj.getString(MERCHANT_BANK_KEY) != null) {
                            AcquirerRuleConfigDao.insertAcqBank(obj, acquirerIdInt);
                        }
                        // Nếu là hợp đồng 2B của dịch vụ QT hoặc ND thì insert thêm vào module phí
                        // chia
                        // sẻ (TB_PARTNER_FEE_SERVICE)
                        if ("2B".equalsIgnoreCase(contractType) && ("QT".equalsIgnoreCase(service)
                                || "ND".equalsIgnoreCase(service))) {
                            var mapFeeConfig = PartnerFeeConfigDAO.getPartnerFeeConfig(
                                    merchantBankDetail.getContractType(),
                                    merchantBankDetail.getPayChannels(),
                                    merchantBankDetail.getPartnerId());
                            PartnerFeeServiceFunc feeServiceFunc;
                            Integer func;
                            String ciac;
                            if ("Vietcombank".equalsIgnoreCase(bank)
                                    && "ND".equalsIgnoreCase(service)) {
                                feeServiceFunc = PartnerFeeConfigDAO.getFuncParams(200);
                                func = 200;
                                ciac = merchantBankDetail.getBankMerchantId();
                            } else {
                                feeServiceFunc = PartnerFeeConfigDAO.getFuncParams(120);
                                func = 120;
                                ciac = "";
                            }
                            if (mapFeeConfig.containsKey("200")) {
                                var feeConfig = mapFeeConfig.get("200");
                                PartnerFeeConfigDAO.insertPartnerFeeService(feeConfig.getId(),
                                        merchantBankDetail.getCurrency(),
                                        merchantBankDetail.getBankMerchantId(), func, 0, "approved",
                                        merchantBankDetail.getAcquirerId(),
                                        feeServiceFunc.getFuncParams1(),
                                        feeServiceFunc.getFuncParams2(), ciac);
                            }
                        }
                        if ("QT".equalsIgnoreCase(service) && (isRequiredMpgsAcquirer(
                                String.valueOf(acquirerIdInt))
                                || isRequiredCybersourceAcquirer(String.valueOf(acquirerIdInt)))) {
                            FileUpload fileUpload = fileUploadSet.stream().findFirst().orElse(null);
                            P12KeyInfo p12KeyInfo = null;
                            if(fileUpload != null){
                                try {
                                    logger.info("file Type: " + fileUpload.contentType());
                                    logger.info("uploadedFileName: " + fileUpload.uploadedFileName());
                                    logger.info("fileName: " + fileUpload.fileName());
                                    p12KeyInfo = readP12(FileSystems.getDefault().getPath(fileUpload.uploadedFileName()).normalize().toString(), password);
                                } catch (Exception e) {
                                    logger.log(Level.SEVERE, "ERROR TO MERCHANT ACCOUNTANT IMPORT: ", e);
                                    throw IErrors.INTERNAL_SERVER_ERROR;
                                }
                            }
                            // Nếu người dùng clone acquirer bank cũ và không tải lên file p12 mới thì sẽ bắn API lấy thông tin file P12 từ acquirer bank cũ
                            if (acquirerBankIdOld != null && p12KeyInfo == null) {
                                JsonObject acquirerBank = MspClient.getAcquirerBankMerchantKeyById(acquirerBankIdOld);
                                JsonObject soapP12 = acquirerBank.getJsonObject("SOAP_p12");
                                if (soapP12 != null) {
                                    p12KeyInfo = new P12KeyInfo(soapP12.getString("public_key"), soapP12.getString("private_key"), soapP12.getString("certificate"));
                                }
                            }
                            // Tạo bank mid bên đầu MSP
                            JsonObject body = processInputCreateBankMerchantSecretKey(obj, p12KeyInfo, acquirerIdInt, contractType);
                            JsonObject jsonReturn = MspClient.createBankMerchantSecretKey(body);
                            if (jsonReturn != null && jsonReturn.getString("ID") != null) {
                                String acquirerBankId = jsonReturn.getString("ID");
                                BankMidDao.updateAcquirerBankId(jsonReturn, acquirerBankId, result.getInteger("id"));
                            } else {
                                map.put("message", "Create Bank Merchant Secret Key failed");
                                map.put("code", 500);
                                sendResponse(ctx, 500, map);
                                return;
                            }
                        }
                    }
                    sendResponse(ctx, 200, map);
                } else if (result.getInteger("code") == 201) {
                    map.put("message", "Bank MID is exitst");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "saveBankMid: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /***
     * Update Merchant Bank Update Merchant Bank đã có (Màn hình Admin => Bank MID Management => Chi
     * tiết Bank Mid => Edit) Chú ý: Kiểm tra merchant bank này đã được cấu hình trong merchant nào
     * chưa, nếu có thì kiểm tra các giaog
     */
    public static void updateMerchantBank(RoutingContext ctx) {
        // ctx.vertx().executeBlocking(future -> {
        //     try {
        //         Map map = new HashMap<>();
        //         Set<FileUpload> fileUploadSet = ctx.fileUploads();
        //         HttpServerRequest request = ctx.request();
        //         // Lấy dữ liệu từ form-data
        //         String bodyStr = ctx.request().getFormAttribute("body");
        //         logger.log(Level.INFO, "bodyStr updateMerchantBank: {0}", bodyStr);
        //         JsonObject bodyJson = null;
        //         if (bodyStr != null) {
        //             bodyJson = new JsonObject(bodyStr);
        //         }
        //         String objStr = null;
        //         String oldObjStr = null;
        //         String contractType = null;
        //         String acquirerId = null;
        //         if (bodyJson != null) {
        //             objStr = bodyJson.containsKey("obj") ? bodyJson.getJsonObject("obj").encode() : null;
        //             oldObjStr = bodyJson.containsKey("oldObj") ? bodyJson.getJsonObject("oldObj").encode() : null;
        //             contractType = bodyJson.getString("contractType");
        //             acquirerId = bodyJson.getString("acquirerId");
        //         } else {
        //             map.put("message", "ERROR");
        //             map.put("code", 500);
        //             sendResponse(ctx, 500, map);
        //         }
        //         JsonObject obj = objStr != null ? new JsonObject(objStr) : new JsonObject();
        //         JsonObject oldObj = oldObjStr != null ? new JsonObject(oldObjStr) : new JsonObject();
        //         Integer bankMidid = Integer.parseInt(request.getParam("id"));
        //         int acquirerIdInt = Integer.parseInt(acquirerId);
        //         String payChannel = oldObj.getString("payChannel");
        //         String bankMerchantId = oldObj.getString("bankMerchantId");
        //         int id = obj.getInteger("id"); // id không đổi giữa bản ghi cũ và mới
        //         if ("2B".equalsIgnoreCase(contractType) && "QT".equals(payChannel) && !obj
        //                 .getString("bankMerchantId").equals(oldObj.getString("bankMerchantId"))) {
        //             // kiểm tra merchant bank đã được cấu hình trên chức năng tạo merchant quốc tế
        //             // (Portal) chưa?
        //             boolean isConfigured =
        //                     BankMidDao.isMerchantBankConfigured(acquirerIdInt, bankMerchantId);
        //             // Nếu merchant bank chưa được cấu hình trên chức năng tạo merchant quốc tế
        //             // (Portal), hoặc đã được cấu hình rồi
        //             // nhưng vẫn còn merchant bank tương tự chưa bị xóa trên chức năng bank mid
        //             // management thì mới được update merchant bank này
        //             if (isConfigured) {
        //                 // kiểm tra có merchant bank nào có trùng cặp n_acq_id, s_bank_merchant_id
        //                 // với bản ghi bị update này không?
        //                 boolean isExisted =
        //                         BankMidDao.hasSimilarMerchantBank(id, acquirerIdInt, bankMerchantId);
        //                 // Nếu có thì bản ghi không được phép sửa
        //                 if (!isExisted) {
        //                     map.put("message",
        //                             "Cannot update id that already configured in merchant");
        //                     map.put("code", 202);
        //                     sendResponse(ctx, 202, map);
        //                     return;
        //                 }
        //             }
        //         }
        //         Integer result = BankMidDao.updateMerchantBank(obj, bankMidid);
        //         // String userId = ctx.get(S_USER_ID);
        //         // String UserName = UserDao.get(userId).getName();
        //         // logger.log(Level.INFO, "merchant bank ids updated:" + bankMidid
        //         //         + sm.format(new Date()).toString() + "by" + UserName);
        //         // Nếu là hợp đồng 2B của dịch vụ QT thì insert thêm merchant bank vào module Switch
        //         // Cyber/MPGS
        //         if ("2B".equalsIgnoreCase(contractType) && "QT".equalsIgnoreCase(payChannel)
        //                 && obj.getString(BANK_MERCHANT_ID) != null
        //                 && obj.getString(MERCHANT_BANK_KEY) != null) {
        //             obj.put(ACQUIRERID, String.valueOf(acquirerIdInt));
        //             AcquirerRuleConfigDao.insertAcqBank(obj);
        //         }
        //         // Nếu update thành công thì bổ sung thêm bản ghi trong module phí chia sẻ
        //         // (TB_PARTNER_FEE_SERVICE)
        //         if (result == 200) {
        //             map.put("message", "OK");
        //             map.put("code", 200);
        //             String bankMerchantIdPrev = obj.getString("bankMerchantIdPrev").trim();
        //             var mapMerchant = BankMidDao.getByMerchantById(bankMidid,
        //                     obj.getString(BANK_MERCHANT_ID).trim(), contractType);
        //             if (mapMerchant.containsKey("200")) {
        //                 var merchantBankDetail = mapMerchant.get("200");
        //                 // String contractType = merchantBankDetail.getContractType();
        //                 String service = merchantBankDetail.getPayChannels();
        //                 String ciac = "";
        //                 String bank = merchantBankDetail.getPartnerId();
        //                 if ("2B".equalsIgnoreCase(contractType) && ("QT".equalsIgnoreCase(service)
        //                         || "ND".equalsIgnoreCase(service))) {
        //                     var mapFeeConfig = PartnerFeeConfigDAO.getPartnerFeeConfig(
        //                             merchantBankDetail.getContractType(),
        //                             merchantBankDetail.getPayChannels(),
        //                             merchantBankDetail.getPartnerId());
        //                     if ("Vietcombank".equalsIgnoreCase(bank)
        //                             && "ND".equalsIgnoreCase(service)) {
        //                         ciac = merchantBankDetail.getBankMerchantId();
        //                     } else {
        //                         ciac = "";
        //                     }
        //                     if (mapFeeConfig.containsKey("200")) {
        //                         var feeConfig = mapFeeConfig.get("200");
        //                         PartnerFeeConfigDAO.updatePartnerFeeService(feeConfig.getId(),
        //                                 bankMerchantIdPrev, merchantBankDetail.getBankMerchantId(),
        //                                 ciac);
        //                     }
        //                 }
        //                 if ("QT".equalsIgnoreCase(service) && (isRequiredMpgsAcquirer(
        //                         String.valueOf(acquirerIdInt))
        //                         || isRequiredCybersourceAcquirer(String.valueOf(acquirerIdInt)))) {
        //                     FileUpload fileUpload = fileUploadSet.stream().findFirst().orElse(null);
        //                     String password = obj.getString("password");
        //                     P12KeyInfo p12KeyInfo = null;
        //                     if(fileUpload != null){
        //                         try {
        //                             logger.info("file Type: " + fileUpload.contentType());
        //                             logger.info("uploadedFileName: " + fileUpload.uploadedFileName());
        //                             logger.info("fileName: " + fileUpload.fileName());
        //                             p12KeyInfo = readP12(FileSystems.getDefault().getPath(fileUpload.uploadedFileName()).normalize().toString(), password);
        //                         } catch (Exception e) {
        //                             logger.log(Level.SEVERE, "ERROR TO IMPORT P12 FILE: ", e);
        //                             throw IErrors.INTERNAL_SERVER_ERROR;
        //                         }
        //                     }
        //                     obj.put("acquirerId", String.valueOf(acquirerIdInt));
        //                     obj.put("contractType", contractType);
        //                     JsonObject body = processInputCreateBankMerchantSecretKey(obj, p12KeyInfo);
        //                     JsonObject jsonReturn = MspClient.createBankMerchantSecretKey(body);
        //                     if (jsonReturn != null && jsonReturn.getString("ID") != null) {
        //                         String acquirerBankId = jsonReturn.getString("ID");
        //                         BankMidDao.updateAcquirerBankId(jsonReturn, acquirerBankId);
        //                     } else {
        //                         map.put("message", "Create Bank Merchant Secret Key failed");
        //                         map.put("code", 500);
        //                         sendResponse(ctx, 500, map);
        //                         return;
        //                     }
        //                 }
        //             }
        //             sendResponse(ctx, 200, map);
        //         } else if (result == 201) {
        //             map.put("message", "Bank MID is not exist");
        //             map.put("code", 201);
        //             sendResponse(ctx, 201, map);
        //         } else {
        //             map.put("message", "ERROR");
        //             map.put("code", 500);
        //             sendResponse(ctx, 500, map);
        //         }
        //     } catch (Exception e) {
        //         logger.log(Level.WARNING, "saveBankMid: ", e);
        //         ctx.fail(e);
        //     }
        // }, false, null);
    }

    /**
     * API xóa merchant bank Điều kiện: Chỉ cho phép xóa các merchant bank trong các trường hợp: -
     * Merchant Bank Id chưa được cấu hình trên chức năng cấu hình merchant quốc tế (Portal) -
     * Merchant Bank Id đã được cấu hình trong cấu hình merchant nhưng vẫn còn tồn tại bản ghi
     * merchant bank khác có trùng cặp acquirerId, bankMerchantId với bản ghi này trên chức năng cấu
     * hình bank mid
     */
    public static void deleteMerchantBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            SimpleDateFormat sm = new SimpleDateFormat("DD-MM-YYYY");
            try {
                JsonObject body = ctx.getBodyAsJson();
                JsonArray ids = body.getJsonArray("ids");
                int acquirerId = Integer.parseInt(body.getString(ACQUIRERID));
                String contractType = body.getString("contractType");
                // Info
                String userId = ctx.get(S_USER_ID);
                String userName = "Quan tri vien he thong";
                JsonArray idFailed = new JsonArray();
                for (int i = 0; i < ids.size(); i++) {
                    JsonObject bankMerchantObj = ids.getJsonObject(i);
                    int id = bankMerchantObj.getInteger("id");
                    String bankMerchantId = bankMerchantObj.getString("bankMerchantId");
                    String payChannel = bankMerchantObj.getString("payChannel");
                    if ("2B".equalsIgnoreCase(contractType) && "QT".equals(payChannel)) {
                        // kiểm tra merchant bank đã được cấu hình trên chức năng tạo merchant quốc
                        // tế (Portal) chưa?
                        boolean isConfigured =
                                BankMidDao.isMerchantBankConfigured(acquirerId, bankMerchantId);
                        // Nếu merchant bank chưa được cấu hình trên chức năng tạo merchant quốc tế
                        // (Portal) hoặc đã được cấu hình rồi nhưng vẫn còn merchant bank tương tự
                        // chưa bị xóa
                        // trên chức năng bank mid management thì mới được xóa merchant bank này
                        if (isConfigured) {
                            // kiểm tra có merchant bank nào có trùng cặp n_acq_id,
                            // s_bank_merchant_id với bản ghi bị delete này không?
                            boolean isExisted = BankMidDao.hasSimilarMerchantBank(id, acquirerId,
                                    bankMerchantId);
                            // Nếu có thì bản ghi không được phép xóa
                            if (!isExisted) {
                                idFailed.add(id);
                                continue;
                            }
                        }
                    }
                    // xóa merchant bank
                    BankMidDao.deleteMerchantBankSelected(id);
                    logger.log(Level.INFO, "merchant bank id deleted: {0} by UserName:{1}",
                            new Object[] {bankMerchantObj, userName});
                }
                temp.put("idFailed", idFailed);
                logger.log(Level.INFO, "response: {0}", temp);
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAcquirer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                Map<String, Object> data = BankMidDao.getAcquirers();

                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getAcquirers: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getAcquirers ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void enableBankMerchantSecretKey(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                String acquirerBankId = body.getString("acquirerBankId");
                String merchantBankId = body.getString("merchantBankId");
                JsonObject jsonReturn = MspClient.enableBankMerchantSecretKey(acquirerBankId);
                if (jsonReturn.getString("ID") != null) {
                    BankMidDao.updateEnableAcquirerBank(acquirerBankId, merchantBankId);
                    sendResponse(ctx, 200, jsonReturn);
                } else {
                    sendResponse(ctx, 500, jsonReturn);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
            }
        }, false, null);
    }

    /**
     * API gán merchant vào acquirer bank
     */
    public static void assignMerchantToAcquirerBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                logger.log(Level.INFO, "assignMerchantToAcquirerBank: {0}", body);
                String acquirerBankId = body.getString("acquirerBankId");
                JsonObject obj = new JsonObject();
                obj.put("merchants", body.getJsonArray("merchantIds"));
                JsonArray jsonReturn = MspClient.assignMerchantToAcquirerBank(obj, acquirerBankId);
                if (jsonReturn.size() > 0) {
                    for (int i = 0; i < jsonReturn.size(); i++) {
                        JsonObject merchantObj = jsonReturn.getJsonObject(i);
                        if (merchantObj.getString("ID") != null) {
                            BankMidDao.assignMerchantToAcquirerBank(merchantObj);
                        }
                    }
                    sendResponse(ctx, 200, jsonReturn);
                } else {
                    sendResponse(ctx, 500, jsonReturn);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * API hủy gán merchant vào acquirer bank
     */
    public static void unassignMerchantToAcquirerBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String acquirerBankId = request.getParam("acquirerBankId");
                String merchantId = request.getParam("merchantId");
                logger.log(Level.INFO, "unassignMerchantToAcquirerBank: acquirerBankId: {0}, merchantId: {1}", new Object[] {acquirerBankId, merchantId});
                JsonObject jsonReturn = MspClient.unassignMerchantToAcquirerBank(acquirerBankId, merchantId);
                if (jsonReturn.getString("message") != null) {
                    JsonObject obj = new JsonObject();
                    obj.put("AcquirerBankID", acquirerBankId);
                    obj.put("MerchantID", merchantId);
                    obj.put("State", "delete");
                    BankMidDao.updateStateMerchantToAcquirerBank(obj);
                    sendResponse(ctx, 200, jsonReturn);
                } else {
                    sendResponse(ctx, 500, jsonReturn);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * API cập nhật trạng thái merchant vào acquirer bank
     */
    public static void updateStateMerchantToAcquirerBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                logger.log(Level.INFO, "updateStateMerchantToAcquirerBank: {0}", body);
                String acquirerBankId = body.getString("acquirerBankId");
                String merchantId = body.getString("merchantId");
                JsonObject obj = new JsonObject();
                obj.put("state", body.getString("state"));
                JsonObject jsonReturn = MspClient.updateStateMerchantToAcquirerBank(body, acquirerBankId, merchantId);
                if (jsonReturn.getString("ID") != null) {
                    BankMidDao.updateStateMerchantToAcquirerBank(jsonReturn);
                    sendResponse(ctx, 200, jsonReturn);
                } else {
                    sendResponse(ctx, 500, jsonReturn);
                }
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Hàm xử lý dữ liệu đầu vào để gọi API tạo bank merchant secret key bên MSP
     * 
     * @param obj body request
     * @param p12KeyInfo thông tin p12 key đọc từ file
     * @param acquirer acquirer id
     * @param contractType contract type
     * @return body để gọi API tạo bank merchant secret key bên MSP
     */
    public static JsonObject processInputCreateBankMerchantSecretKey(JsonObject obj, P12KeyInfo p12KeyInfo, int acquirer, String contractType) {
        JsonObject jDesc = (obj.getString(DESC) != null && !obj.getString(DESC).isEmpty()) ? new JsonObject(obj.getString(DESC)) : new JsonObject();
        String expireDate = obj.getString("expireDate");
        logger.log(Level.INFO, "processInputCreateBankMerchantSecretKey: {0}", obj);
        JsonObject body = new JsonObject();
        body.put("acquirer", acquirer);
        body.put("bank_merchant_id", obj.getString("bankMerchantId"));
        body.put("contract_type", contractType);
        body.put("bank_merchant_key", obj.getString("merchantBankKey"));
        if (jDesc.getString("merchant_key_id") != null
                && jDesc.getString("merchant_secret_key") != null) {
            JsonObject restKey = new JsonObject();
            restKey.put("merchant_key_id", jDesc.getString("merchant_key_id"));
            restKey.put("merchant_secret_key", jDesc.getString("merchant_secret_key"));
            body.put("REST_key", restKey);
        }
        JsonObject jwtKey = new JsonObject();
        if (jDesc.getString("org_unit_id") != null && jDesc.getString("api_identifier") != null
                && jDesc.getString("api_key") != null) {
            jwtKey.put("org_unit_id", jDesc.getString("org_unit_id"));
            jwtKey.put("api_identifier", jDesc.getString("api_identifier"));
            jwtKey.put("api_key", jDesc.getString("api_key"));
            body.put("JWT_key", jwtKey);
        }
        if (p12KeyInfo != null && p12KeyInfo.getPublicKey() != null && p12KeyInfo.getPrivateKey() != null
                && p12KeyInfo.getCertificate() != null) {
            JsonObject soapP12 = new JsonObject();
            soapP12.put("public_key", p12KeyInfo.getPublicKey());
            soapP12.put("private_key", p12KeyInfo.getPrivateKey());
            soapP12.put("certificate", p12KeyInfo.getCertificate());
            body.put("SOAP_p12", soapP12);
        }
        // Định dạng date expire từ dd/MM/yyyy sang ISO 8601
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate localDate = LocalDate.parse(expireDate, inputFormatter);
        // Gán giờ mặc định là 12:00 và chuyển sang UTC
        ZonedDateTime zonedDateTime = localDate.atTime(12, 0).atZone(ZoneOffset.UTC);
        // Định dạng đầu ra ISO 8601 (chuẩn API)
        String outputDateStr = zonedDateTime.format(DateTimeFormatter.ISO_INSTANT);
        body.put("expire", outputDateStr);
        logger.log(Level.INFO, "processInputCreateBankMerchantSecretKey result: {0}", body);
        return body;
    }

    public static void getBankMidQr(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            HttpServerRequest request = ctx.request();
            try {
                String acquirer = request.getParam("acquirer");
                Map<String, Object> data = BankMidDao.getBankMidQr(acquirer);
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getBankMidQr: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getBankMidQr ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAcquirerAuto(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                Map<String, Object> data = BankMidDao.getAcquirerAuto();
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getAcquirerAuto: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getAcquirerAuto ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBankMidCiac(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                Map<String, Object> data = BankMidDao.getBankMidCiac();
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getBankMidCiac: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getBankMidCiac ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBankMidMerchant3b(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                Map<String, Object> data = BankMidDao.getBankMidMerchant3b();
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getBankMidMerchant3b: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getBankMidMerchant3b ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getBankConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                Map<String, Object> data = BankMidDao.getBankConfig();
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getBankConfig: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getBankConfig ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }
    
    public static void updateAutoForBankConfig(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                JsonObject data = ctx.getBodyAsJson();
                JsonObject obj = data.getJsonObject("obj");
                String userId = ctx.get(S_USER_ID);
                obj.put(USER_ID, userId);
                Integer result = BankMidDao.updateAutoForBankConfig(obj);
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else if (result == 201) {
                    map.put("message", "ID is not exitst");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "updateAutoForBankConfig error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getMerchantBankCiac(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Integer id = Integer.parseInt(request.getParam("id"));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, id);
                mIn.put(KEYWORD, Util.handleHttpRequest(request, KEYWORD, "").toString());
                mIn.put(PAGE,
                        Integer.parseInt(Util.handleHttpRequest(request, PAGE, "0").toString()));
                mIn.put(PAGE_SIZE, Integer
                        .parseInt(Util.handleHttpRequest(request, PAGE_SIZE, "20").toString()));
                Map<String, Object> data = BankMidDao.getMerchantBankCiac(mIn);

                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                logger.info("RESPONSE getMerchantBankCiac: " + response.toString());
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantBankCiac ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(TOTAL, "0");
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void saveMerchantBankCiac(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                HttpServerRequest request = ctx.request();
                // Lấy dữ liệu từ form-data
                String bodyStr = ctx.getBodyAsString();
                logger.log(Level.INFO, "bodyStr saveMerchantBankCiac: {0}", bodyStr);
                JsonObject bodyJson = null;
                if (bodyStr != null && !bodyStr.isBlank()) {
                    bodyJson = new JsonObject(bodyStr);
                }
                JsonObject obj = bodyJson.getJsonObject("obj");
                Integer bankMidId = Integer.parseInt(request.getParam("id"));
                Integer result = BankMidDao.insertMerchantBankCiac(obj, bankMidId);
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else if (result == 201) {
                    map.put("message", "Bank MID is exitst");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "saveMerchantBankCiac: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateMerchantBankCiac(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map map = new HashMap<>();
                // Lấy dữ liệu từ form-data
                String bodyStr = ctx.getBodyAsString();
                logger.log(Level.INFO, "bodyStr saveMerchantBankCiac: {0}", bodyStr);
                JsonObject bodyJson = null;
                if (bodyStr != null && !bodyStr.isBlank()) {
                    bodyJson = new JsonObject(bodyStr);
                }
                JsonObject obj = bodyJson.getJsonObject("obj");
                Integer result = BankMidDao.updateMerchantBankCiac(obj);
                
                if (result == 200) {
                    map.put("message", "OK");
                    map.put("code", 200);
                    sendResponse(ctx, 200, map);
                } else if (result == 201) {
                    map.put("message", "Bank MID is not exist");
                    map.put("code", 201);
                    sendResponse(ctx, 201, map);
                } else {
                    map.put("message", "ERROR");
                    map.put("code", 500);
                    sendResponse(ctx, 500, map);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "updateMerchantBankCiac: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteMerchantBankCiac(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            Map<String, Object> temp = new HashMap<>();
            try {
                JsonObject body = ctx.getBodyAsJson();
                JsonArray ids = body.getJsonArray("ids");
                // Info
                String userId = ctx.get(S_USER_ID);
                JsonArray idFailed = new JsonArray();
                for (int i = 0; i < ids.size(); i++) {
                    JsonObject bankMerchantObj = ids.getJsonObject(i);
                    int id = bankMerchantObj.getInteger("id");
                    // xóa merchant bank
                    BankMidDao.deleteMerchantBankCiac(id);
                    logger.log(Level.INFO, "merchant bank id deleted: {0} by UserId:{1}",
                            new Object[] {bankMerchantObj, userId});
                }
                temp.put("idFailed", idFailed);
                logger.log(Level.INFO, "response: {0}", temp);
                sendResponse(ctx, 200, temp);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Hàm đọc file .p12 và lấy thông tin private key, public key, certificate
     * 
     * @param p12Path đường dẫn file .p12
     * @param password mật khẩu file .p12
     * @return P12KeyInfo chứa thông tin private key, public key, certificate
     * @throws Exception nếu có lỗi khi đọc file .p12
     */
    public static P12KeyInfo readP12(String p12Path, String password) throws Exception {
        // Load keystore từ file .p12
        P12KeyInfo p12KeyInfo = new P12KeyInfo();
        Security.addProvider(new BouncyCastleProvider());
        KeyStore keystore = KeyStore.getInstance("PKCS12", "BC");
        try (FileInputStream fis = new FileInputStream(p12Path)) {
            keystore.load(fis, password.toCharArray());
        }

        // Duyệt qua các alias
        Enumeration<String> aliases = keystore.aliases();
        while (aliases.hasMoreElements()) {
            String alias = aliases.nextElement();
            logger.log(Level.INFO, "Alias: {0}", alias);

            // Lấy Private Key (nếu có)
            if (keystore.isKeyEntry(alias)) {
                PrivateKey key = (PrivateKey) keystore.getKey(alias, password.toCharArray());
                if (key != null) {
                    logger.log(Level.INFO, "Private Key:");
                    p12KeyInfo.setPrivateKey(Base64.getEncoder().encodeToString(key.getEncoded()));
                    
                    // Lấy Certificate và Public Key chỉ khi tìm thấy Private Key
                    Certificate cert = keystore.getCertificate(alias);
                    if (cert != null) {
                        logger.log(Level.INFO, "Certificate:");
                        p12KeyInfo.setCertificate(Base64.getEncoder().encodeToString(cert.getEncoded()));
                        p12KeyInfo.setPublicKey(Base64.getEncoder().encodeToString(cert.getPublicKey().getEncoded()));
                        logger.log(Level.INFO, "Public Key:");
                    }
                    
                    // Kết thúc vòng lặp sau khi tìm thấy private key
                    break;
                }
            }
        }
        return p12KeyInfo;
    }

    public static void getMerchantByAcquirerBankId(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String acquirerBankId = request.getParam("id");
                JsonObject jsonReturn = BankMidDao.getMerchantByAcquirerBankId(acquirerBankId);
                sendResponse(ctx, 200, jsonReturn);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    /**
     * Check acquirer có phải là acquirer MPGS không?
     * 
     * @param acquirerId
     * @return true nếu acquirer có phải là acquirer MPGS, false nếu không
     */
    private static boolean isRequiredMpgsAcquirer(String acquirerId) {
        for (String id : REQUIRED_MPGS_ACQUIRERS) {
            if (id.equals(acquirerId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check acquirer có phải là acquirer CyberSource không?
     * 
     * @param acquirerId
     * @return true nếu acquirer có phải là acquirer CyberSource, false nếu không
     */
    private static boolean isRequiredCybersourceAcquirer(String acquirerId) {
        for (String id : REQUIRED_CYBERSOURCE_ACQUIRERS) {
            if (id.equals(acquirerId)) {
                return true;
            }
        }
        return false;
    }

    public static void getMerchantCiacByBankMid(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            JsonObject response = new JsonObject();
            JsonObject responseData = new JsonObject();
            try {
                HttpServerRequest request = ctx.request();
                Integer bankMidId = Integer.parseInt(request.getParam("bankMidId"));
                String caic = request.getParam("caic");
                Map<String, Object> data = BankMidDao.getMerchantCiacByBankMid(bankMidId, caic);
                responseData.put(STATE, SUCCESS);
                responseData.put(DATA, data);
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 200, response);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "[ getMerchantCiacByBankMid ]  => ERROR: ", e);
                responseData.put(STATE, FAILED);
                responseData.put(DATA, "");
                response.put(RESPONSE, responseData);
                sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        }, false, null);
    }
}
