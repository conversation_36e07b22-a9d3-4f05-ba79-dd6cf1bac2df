package vn.onepay.portal.resources.bank_mid_management.dto;

import java.io.Serializable;

public class MerchantBankDetail implements Serializable {
    private Integer bankId;
    private String bankMID;
    private String payChannels;
    private String contractType;
    private String acquirer;
    private Integer acquirerId;
    private String active;
    private String currency;
    private String bankMerchantId;
    private String merchantBankKey;
    private Integer priority;
    private String activeNone3DS;
    private String enableNoneCSC;
    private String name;
    private String groupAcquirer;
    private String partnerId;

    public MerchantBankDetail() {
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public String getBankMID() {
        return bankMID;
    }

    public void setBankMID(String bankMID) {
        this.bankMID = bankMID;
    }

    public String getPayChannels() {
        return payChannels;
    }

    public void setPayChannels(String payChannels) {
        this.payChannels = payChannels;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public Integer getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(Integer acquirerId) {
        this.acquirerId = acquirerId;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBankMerchantId() {
        return bankMerchantId;
    }

    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }

    public String getMerchantBankKey() {
        return merchantBankKey;
    }

    public void setMerchantBankKey(String merchantBankKey) {
        this.merchantBankKey = merchantBankKey;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getActiveNone3DS() {
        return activeNone3DS;
    }

    public void setActiveNone3DS(String activeNone3DS) {
        this.activeNone3DS = activeNone3DS;
    }

    public String getEnableNoneCSC() {
        return enableNoneCSC;
    }

    public void setEnableNoneCSC(String enableNoneCSC) {
        this.enableNoneCSC = enableNoneCSC;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroupAcquirer() {
        return groupAcquirer;
    }

    public void setGroupAcquirer(String groupAcquirer) {
        this.groupAcquirer = groupAcquirer;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }
}
