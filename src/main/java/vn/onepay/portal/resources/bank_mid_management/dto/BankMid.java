package vn.onepay.portal.resources.bank_mid_management.dto;

import java.util.List;

public class BankMid {
    private Integer id;
    private String bankMid;
    private String payChannels;
    private String contractType;
    private String acquirer;
    private String bankMCC;
    private String active;
    private String status;
    private String userCreated;
    private String userUpdate;
    private String create;
    private String update;
    private String desc;
    private String merchantBankIds;
    private String tid;
    private Integer acquirerId;
    private Integer partnerId;
    private String merchantAccountName;
    private String currency;
    private String cardType;
    private String partnerShortName;
    private String se;
    private String ciac;
    private String account;
    private int bankConfigId;
    private String bankName;
    private String bankCode;
    private String swiftCode;
    private String type;
    private String auto;
    private String cardTypeBank;
    

    public String getCardTypeBank() {
        return cardTypeBank;
    }

    public void setCardTypeBank(String cardTypeBank) {
        this.cardTypeBank = cardTypeBank;
    }

    public String getAuto() {
        return auto;
    }

    public void setAuto(String auto) {
        this.auto = auto;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public int getBankConfigId() {
        return bankConfigId;
    }

    public void setBankConfigId(int bankConfigId) {
        this.bankConfigId = bankConfigId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCiac() {
        return ciac;
    }

    public void setCiac(String ciac) {
        this.ciac = ciac;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
  
    public Integer getAcquirerId() {
        return acquirerId;
    }

    public void setAcquirerId(Integer acquirerId) {
        this.acquirerId = acquirerId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getMerchantAccountName() {
        return merchantAccountName;
    }

    public void setMerchantAccountName(String merchantAccountName) {
        this.merchantAccountName = merchantAccountName;
    }

    public String getSe() {
        return se;
    }

    public void setSe(String se) {
        this.se = se;
    }

    public String getBankMid() {
        return bankMid;
    }

    public void setBankMid(String bankMid) {
        this.bankMid = bankMid;
    }

    public String getpayChannels() {
        return payChannels;
    }

    public void setpayChannels(String payChannels) {
        this.payChannels = payChannels;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getBankMCC() {
        return bankMCC;
    }

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public String getUpdate() {
        return update;
    }

    public void setUpdate(String update) {
        this.update = update;
    }

    public void setBankMCC(String bankMCC) {
        this.bankMCC = bankMCC;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserCreated() {
        return userCreated;
    }

    public void setUserCreated(String userCreated) {
        this.userCreated = userCreated;
    }

    public String getUserUpdate() {
        return userUpdate;
    }

    public void setUserUpdate(String userUpdate) {
        this.userUpdate = userUpdate;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMerchantBankIds() {
        return merchantBankIds;
    }

    public void setMerchantBankIds(String merchantBankIds) {
        this.merchantBankIds = merchantBankIds;
    }

    public String getPartnerShortName() {
        return partnerShortName;
    }

    public void setPartnerShortName(String partnerShortName) {
        this.partnerShortName = partnerShortName;
    }





}
