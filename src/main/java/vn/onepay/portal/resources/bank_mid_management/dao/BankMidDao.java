package vn.onepay.portal.resources.bank_mid_management.dao;

import com.onepay.commons.util.Convert;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.bank_mid_management.dto.AcquirerBankMerchant;
import vn.onepay.portal.resources.bank_mid_management.dto.BankMid;
import vn.onepay.portal.resources.bank_mid_management.dto.MerchantBank;
import vn.onepay.portal.resources.bank_mid_management.dto.MerchantBankDetail;
import vn.onepay.portal.resources.merchantmanagement.merchantid.dto.AcquirerDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.text.DateFormat;
import java.sql.Array;

/*
 * Author: DuyNp
 * Date: 16/03/2023
 */
public class BankMidDao extends Db implements IConstants {

    private static final Logger logger = Logger.getLogger(BankMidDao.class.getName());
    private static final String G_BANK_MID = "{call onefin.PKG_BANK_MID.G_BANK_MID(?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String I_BANK_MID = "{call onefin.PKG_BANK_MID.I_BANK_MID(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String U_BANK_MID = "{call onefin.PKG_BANK_MID.U_BANK_MID(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String G_MERCHANT_BANK = "{call onefin.PKG_BANK_MID.G_MERCHANT_BANK(?,?,?,?,?,?,?,?) }";
    private static final String I_MERCHANT_BANK = "{call onefin.PKG_BANK_MID.I_MERCHANT_BANK(?,?," +
            "?,?,?,?,?,?,?,?) }";
    private static final String I_MERCHANT_BANK2 = "{call onefin.PKG_BANK_MID.I_MERCHANT_BANK2(?,?,?," +
            "?,?,?,?,?,?,?,?,?,?) }";
    private static final String U_MERCHANT_BANK = "{call onefin.PKG_BANK_MID.U_MERCHANT_BANK(?,?," +
            "?,?,?,?,?,?,?,?,?) }";
    private static final String U_MERCHANT_BANK2 = "{call onefin.PKG_BANK_MID.U_MERCHANT_BANK2(?,?," +
            "?,?,?,?,?,?,?,?,?,?) }";

    private static final String U_ACQUIRER_BANK_ID = "{call onefin.PKG_BANK_MID.U_ACQUIRER_BANK_ID(?,?,?,?) }";
    private static final String D_MERCHANT_BANK = "{call onefin.PKG_BANK_MID.D_MERCHANT_BANK(?,?,?) }";
    private static final String G_ACQUIRER = "{call onefin.PKG_BANK_MID.G_ACQUIRER(?,?,?) }";
    private static final String G_MERCHANT_BANK_DETAIL = "{call onefin.PKG_BANK_MID.get_detail_merchant_bank_id(?,?,?,?,?,?) }";
    private static final String IS_MERCHANT_BANK_CONFIGURED = "{? = call oneportal.is_merchant_bank_configured(?,?) }";
    private static final String HAS_SIMILAR_MERCHANT_BANK = "{? = call onefin.PKG_BANK_MID.has_similar_merchant_bank(?,?,?) }";
    private static final String U_ENABLE_ACQUIRER_BANK = "{call onefin.PKG_BANK_MID.U_ENABLE_ACQUIRER_BANK(?,?,?,?) }";
    private static final String I_MERCHANT_TO_ACQUIRER_BANK = "{call onefin.PKG_BANK_MID.I_MERCHANT_TO_ACQUIRER_BANK(?,?,?,?) }";
    private static final String U_MERCHANT_TO_ACQUIRER_BANK = "{call onefin.PKG_BANK_MID.U_MERCHANT_TO_ACQUIRER_BANK(?,?,?,?,?) }";
    private static final String G_MERCHANT_BY_ACQUIRER_BANK = "{call onefin.PKG_BANK_MID.G_MERCHANT_BY_ACQUIRER_BANK(?,?,?,?,?) }";
    private static final String G_BANK_MID_v2 = "{call ONEFIN.PKG_MERCHANT_ID#GET_BANK_MID(?,?,?,?,?,?,?,?,?,?,?) }";

    public static Map<String, Object> getBankMid(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> bankMids = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(G_BANK_MID_v2);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);

            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, mIn.get(KEYWORD).toString());
            cs.setString(6, mIn.get(PAY_CHANNEL).toString());
            cs.setString(7, mIn.get(ACQUIRER).toString());
            cs.setString(8, mIn.get(CONTRACT_TYPE).toString());
            cs.setString(9, mIn.get(STATUS).toString());
            cs.setInt(10, Integer.valueOf(mIn.get(PAGE).toString()));
            cs.setInt(11, Integer.valueOf(mIn.get(PAGE_SIZE).toString()));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setId(rs.getInt("N_ID"));
                    bankMid.setBankMid(rs.getString("S_BANK_MID") != null ? rs.getString("S_BANK_MID") : "");
                    bankMid.setpayChannels(rs.getString("S_PAY_CHANNELS") != null ? rs.getString("S_PAY_CHANNELS") : "");
                    bankMid.setContractType(
                            rs.getString("S_CONTRACT_TYPE") != null ? rs.getString("S_CONTRACT_TYPE") : "");
                    bankMid.setAcquirer(rs.getString("S_ACQUIRER") != null ? rs.getString("S_ACQUIRER") : "");
                    bankMid.setBankMCC(rs.getString("S_BANK_MCC") != null ? rs.getString("S_BANK_MCC") : "");
                    bankMid.setActive(rs.getString("S_ACTIVE") != null ? rs.getString("S_ACTIVE") : "");
                    bankMid.setStatus(rs.getString("S_STATUS") != null ? rs.getString("S_STATUS") : "");
                    bankMid.setUserCreated(rs.getString("S_USER_CREATE") != null ? rs.getString("S_USER_CREATE") : "");
                    bankMid.setUserUpdate(rs.getString("S_USER_UPDATE") != null ? rs.getString("S_USER_UPDATE") : "");
                    bankMid.setCreate(rs.getDate("D_CREATE") != null ? sdf.format(rs.getDate("D_CREATE")) : "");
                    bankMid.setUpdate(rs.getDate("D_UPDATE") != null ? sdf.format(rs.getDate("D_UPDATE")) : "");
                    bankMid.setTid(rs.getString("S_TID") != null ? rs.getString("S_TID") : "");
                    bankMid.setDesc(rs.getString("S_DESC") != null ? rs.getString("S_DESC") : "");
                    bankMid.setAcquirerId(rs.getString("N_ACQUIRER_ID") != null ? rs.getInt("N_ACQUIRER_ID") : null);
                    bankMid.setPartnerId(rs.getString("N_PARTNER_ID") != null ? rs.getInt("N_PARTNER_ID") : null);
                    bankMid.setMerchantAccountName(
                            rs.getString("S_MERCHANT_ACCOUNT_NAME") != null ? rs.getString("S_MERCHANT_ACCOUNT_NAME")
                                    : "");
                    bankMid.setCurrency(rs.getString("S_CURRENCY") != null ? rs.getString("S_CURRENCY") : "");
                    bankMid.setCardType(rs.getString("S_CARD_TYPE") != null ? rs.getString("S_CARD_TYPE") : "");
                    bankMid.setMerchantBankIds(rs.getString("S_BANK_MERCHANT_IDS") != null ? rs.getString("S_BANK_MERCHANT_IDS") : "");
                    bankMid.setPartnerShortName(rs.getString("S_PARTNER_SHORT_NAME") != null ? rs.getString("S_PARTNER_SHORT_NAME") : "");
                    bankMid.setSe(rs.getString("S_SE") != null ? rs.getString("S_SE") : "");
                    bankMid.setCiac(rs.getString("S_CIAC") != null ? rs.getString("S_CIAC") : "");
                    bankMid.setAuto(rs.getString("S_AUTO") != null ? rs.getString("S_AUTO") : "");
                    bankMid.setType(rs.getString("S_TYPE") != null ? rs.getString("S_TYPE") : "");
                    bankMid.setCardTypeBank(rs.getString("S_CARD_TYPE_BANK") != null ? rs.getString("S_CARD_TYPE_BANK") : "");
                    bankMids.add(bankMid);
                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", bankMids);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Integer insertBankMid(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(I_BANK_MID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, obj.getString(PAYCHANNELS));
            cs.setInt(4, obj.getInteger(ACQUIRERID));
            cs.setString(5, obj.getString(BANKMID).trim());
            cs.setString(6, obj.getString(TID) != null ? obj.getString(TID) : "");
            cs.setString(7, obj.getString(BANKMCC) != null ? obj.getString(BANKMCC) : "");
            cs.setInt(8, obj.getInteger(PARTNERTID) != null ? obj.getInteger(PARTNERTID) : -1);
            cs.setString(9, obj.getString(MERCHANT_ACCOUNT_NAME) != null ? obj.getString(MERCHANT_ACCOUNT_NAME) : "");
            cs.setString(10, obj.getString(CONTRACTTYPE));
            cs.setString(11, obj.getString(CARDTYPE));
            cs.setString(12, obj.getString(ACTIVE));
            cs.setString(13, obj.getString(STATUS));
            cs.setString(14, obj.getString(CURRENCY));
            cs.setString(15, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setString(16, obj.getString(USER_ID) != null ? obj.getString(USER_ID) : "");
            cs.setString(17, obj.getString(ACQUIRER) != null ? obj.getString(ACQUIRER) : "");
            cs.setString(18, obj.getString(SE) != null ? obj.getString(SE) : "");

            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB insertBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer updateBankMid(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(U_BANK_MID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, obj.getInteger(ID));
            cs.setString(4, obj.getString(PAYCHANNELS));
            cs.setInt(5, obj.getInteger(ACQUIRERID));
            cs.setString(6, obj.getString(BANKMID).trim());
            cs.setString(7, obj.getString(TID) != null ? obj.getString(TID) : "");
            cs.setString(8, obj.getString(BANKMCC) != null ? obj.getString(BANKMCC) : "");
            cs.setInt(9, obj.getInteger(PARTNERTID) != null ? obj.getInteger(PARTNERTID) : -1);
            cs.setString(10, obj.getString(MERCHANT_ACCOUNT_NAME) != null ? obj.getString(MERCHANT_ACCOUNT_NAME) : "");
            cs.setString(11, obj.getString(CONTRACTTYPE));
            cs.setString(12, obj.getString(CARDTYPE));
            cs.setString(13, obj.getString(ACTIVE));
            cs.setString(14, obj.getString(STATUS));
            cs.setString(15, obj.getString(CURRENCY));
            cs.setString(16, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setString(17, obj.getString(USER_ID) != null ? obj.getString(USER_ID) : "");
            cs.setString(18, obj.getString(ACQUIRER) != null ? obj.getString(ACQUIRER) : "");
            cs.setString(19, obj.getString(SE) != null ? obj.getString(SE) : "");
            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getMerchantBank(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<MerchantBank> merchantBanks = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(G_MERCHANT_BANK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, Integer.parseInt(mIn.get(ID).toString()));
            cs.setString(6, mIn.get(KEYWORD) != null ? mIn.get(KEYWORD).toString() : "");
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setInt(8, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 20));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    MerchantBank merchantBank = new MerchantBank();
                    merchantBank.setId(rs.getInt("N_ID"));
                    merchantBank.setBankMidId(rs.getInt("N_BANK_MID_ID"));
                    merchantBank.setBankMerchantId(
                            rs.getString("S_BANK_MERCHANT_ID") != null ? rs.getString("S_BANK_MERCHANT_ID") : "");
                    merchantBank.setMerchantBankKey(
                            rs.getString("S_MERCHANT_BANK_KEY") != null ? rs.getString("S_MERCHANT_BANK_KEY") : "");
                    merchantBank.setCreate(rs.getString("D_CREATE") != null ? sdf.format(rs.getDate("D_CREATE")) : "");
                    merchantBank.setUpdate(rs.getString("D_UPDATE") != null ? sdf.format(rs.getDate("D_UPDATE")) : "");
                    merchantBank.setPriority(rs.getString("N_PRIORITY") != null ? rs.getInt("N_PRIORITY") : null);
                    merchantBank.setActiveNone3ds(
                            rs.getString("S_ACTIVE_NONE_3DS") != null ? rs.getString("S_ACTIVE_NONE_3DS") : "");
                    merchantBank.setEnableNoneCsc(
                            rs.getString("S_ENABLE_NONE_CSC") != null ? rs.getString("S_ENABLE_NONE_CSC") : "");
                    merchantBank.setDesc(rs.getString("S_DESC") != null ? rs.getString("S_DESC") : "");
                    merchantBank.setPayChannel(rs.getString("S_PAY_CHANNEL") != null ? rs.getString("S_PAY_CHANNEL") : "");
                    
                    //Add Expire Date
                    merchantBank.setExpireDate(rs.getDate("D_EXPIRE") != null ? rs.getDate("D_EXPIRE") : null);
                    merchantBank.setAcquirerBankId(rs.getString("S_ACQUIRER_BANK_ID") != null ? rs.getString("S_ACQUIRER_BANK_ID") : "");
                    merchantBank.setKeyName(rs.getString("S_KEY_NAME") != null ? rs.getString("S_KEY_NAME") : "");
                    merchantBank.setMerchantId(rs.getString("S_MERCHANT_IDS") != null ? rs.getString("S_MERCHANT_IDS") : "");
                    merchantBank.setState(rs.getString("S_STATE") != null ? rs.getString("S_STATE") : "");
                    merchantBanks.add(merchantBank);

                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", merchantBanks);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    /**
     * Insert Merchant Bank (ONEFIN118.TB_MERCHANT_BANK_ID)
     * 
     * @param obj       Json chứa thông tin bank merchant để insert
     * @param bankMidId mã bank mid của bank merchant
     * @return Integer kết quả insert (200: thành công, 500: thất bại)
     */
    public static JsonObject insertMerchantBank(JsonObject obj, Integer bankMidId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        JsonObject result = new JsonObject();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(I_MERCHANT_BANK2);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, bankMidId);
            cs.setString(5, obj.getString(BANK_MERCHANT_ID).trim());
            cs.setString(6, obj.getString(MERCHANT_BANK_KEY) != null ? obj.getString(MERCHANT_BANK_KEY) : "");
            cs.setInt(7, obj.getInteger(PRIORITY));
            cs.setString(8, obj.getString(ACTIVE_NONE_3DS));
            cs.setString(9, obj.getString(ENABLE_NONE_CSC));
            cs.setString(10, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setString(11, obj.getString("payChannel") != null ? obj.getString("payChannel") : "");

            //Add Expire Date
            DateFormat sdf = new SimpleDateFormat("dd/MM/yyyy"); 
            cs.setDate(12, obj.getString("expireDate") != null ? new java.sql.Date(sdf.parse(obj.getString("expireDate")).getTime()) : null);
            cs.setString(13, obj.getString("keyName") != null ? obj.getString("keyName") : "");
            cs.execute();
            String error = cs.getString(2);
            int code = cs.getInt(1);
            if (code == 500) {
                throw new Exception("DB insertBankMid: " + error);
            }
            result.put("code", code);
            result.put("message", error);
            result.put("id", cs.getInt(3));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer updateMerchantBank(JsonObject obj, Integer bankMidId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(U_MERCHANT_BANK2);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, obj.getInteger(ID));
            cs.setInt(4, bankMidId);
            cs.setString(5, obj.getString(BANK_MERCHANT_ID).trim());
            cs.setString(6, obj.getString(MERCHANT_BANK_KEY) != null ? obj.getString(MERCHANT_BANK_KEY) : "");
            cs.setInt(7, obj.getInteger(PRIORITY));
            cs.setString(8, obj.getString(ACTIVE_NONE_3DS));
            cs.setString(9, obj.getString(ENABLE_NONE_CSC));
            cs.setString(10, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setString(11, obj.getString("payChannel") != null ? obj.getString("payChannel") :
                    "");

            //Add Expire Date
            DateFormat sdf = new SimpleDateFormat("dd/MM/yyyy"); 
            cs.setDate(12, obj.getString("expireDate") != null ? new java.sql.Date(sdf.parse(obj.getString("expireDate")).getTime()) : null);

            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void deleteMerchantBankSelected(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(D_MERCHANT_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.execute();
            String error = cs.getString(2);
            int nerror = cs.getInt(1);
            if (nerror == 0) {
                logger.log(Level.SEVERE, "DB deleteMerchantBankSelected error: {0}", error);
            }
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
    }

    public static Map<String, Object> getAcquirers() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<AcquirerDto> acquirers = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(G_ACQUIRER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    AcquirerDto acquirer = new AcquirerDto();
                    acquirer.setId(rs.getInt("N_ID"));
                    acquirer.setName(rs.getString("S_NAME") != null ? rs.getString("S_NAME") : "");
                    acquirer.setService(Util.getColumnString(rs, "S_PAY_CHANNEL"));
                    acquirers.add(acquirer);

                }
            }
            data.put("list", acquirers);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    /**
     * Lấy ra chi tiết bank merchant id 
     * @param bankId
     * @param merchantBankId
     * @param contractType
     * @return
     * @throws Exception
     */
    public static Map<String, MerchantBankDetail> getByMerchantById(Integer bankId, String merchantBankId, String contractType) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, MerchantBankDetail> result = new HashMap<>();
        MerchantBankDetail merchantBankDetail = new MerchantBankDetail();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(G_MERCHANT_BANK_DETAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankId);
            cs.setString(5, merchantBankId);
            cs.setString(6, contractType);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                if (rs != null && rs.next()) {
                    merchantBankDetail = convertMerchantBankDetail(rs);
                    result.put("200", merchantBankDetail);
                } else {
                    result.put("400", merchantBankDetail);
                }
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * Kiểm tra merchant bank đã bị cấu hình trên chức năng tạo merchant quốc tế
     * trên portal chưa
     * 
     * @param acquirerId     id acquirer
     * @param merchantBankId id merchant bank
     */
    public static boolean isMerchantBankConfigured(int acquirerId, String merchantBankId) {
        logger.log(Level.INFO, "start isMerchantBankConfigured: acquirerId {0}, merchantBankId: {1}",
                new Object[] { acquirerId, merchantBankId });
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(IS_MERCHANT_BANK_CONFIGURED);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, acquirerId);
            cs.setString(3, merchantBankId);
            cs.execute();
            int nResult = cs.getInt(1);
            if (nResult > 0)
                return true;
            else
                return false;

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            return false;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    /**
     * Kiểm tra có merchant bank nào khác có cùng thông tin acquirerId, merchant
     * bank id tương tự như merchant bank được yêu cầu xóa không
     * 
     * @param acquirerId     id acquirer
     * @param merchantBankId id merchant bank
     */
    public static boolean hasSimilarMerchantBank(int id, int acquirerId, String merchantBankId) throws Exception {
        logger.log(Level.INFO, "start hasSimilarMerchantBank: id: {0}, acquirerId {1}, merchantBankId: {2}",
                new Object[] { id, acquirerId, merchantBankId });
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(HAS_SIMILAR_MERCHANT_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.setInt(2, id);
            cs.setInt(3, acquirerId);
            cs.setString(4, merchantBankId);
            cs.execute();
            int nResult = cs.getInt(1);
            if(nResult > 0) return true;
            else return false;

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            return false;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
    }

    /**
     * Update acquirer bank id trả về từ API MSP Extend
     * @param obj Json chứa thông tin bank merchant để update
     * @param acquirerBankId acquirer bank id
     * @param isUploaded true nếu đã upload file p12, false nếu không
     * @throws Exception
     */
    public static int updateAcquirerBankId(JsonObject obj, String acquirerBankId, int merchantBankId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(U_ACQUIRER_BANK_ID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, acquirerBankId);
            cs.setInt(4, merchantBankId);
            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getBankMidQr(String acquirer) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_BANK_MID_QR(?,?,?,?) }");
            cs.setString(1, acquirer);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setBankMid(rs.getString("T_BANK_MID_ID"));
                    bankMid.setAcquirer(rs.getString("S_ACQUIRER"));
                    lstbankMid.add(bankMid);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getBankMidQr: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getAcquirerAuto() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_ACQUIRER_AUTO(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setAcquirer(rs.getString("S_ACQUIRER"));
                    bankMid.setCiac(rs.getString("S_CIAC"));
                    lstbankMid.add(bankMid);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getAcquirerAuto: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getBankMidCiac() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_BANK_MID_CIAC(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setAcquirer(rs.getString("S_ACQUIRER"));
                    bankMid.setCiac(rs.getString("S_CIAC"));
                    bankMid.setBankCode(rs.getString("S_BANK_CODE"));
                    lstbankMid.add(bankMid);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getBankMidCiac: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getBankMidMerchant3b() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_BANK_MID_3B(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setBankMid(rs.getString("S_BANK_MID"));
                    bankMid.setAcquirer(rs.getString("S_ACQUIRER"));
                    bankMid.setMerchantBankIds(rs.getString("S_BANK_MERCHANT_IDS"));
                    bankMid.setPartnerShortName(rs.getString("S_PARTNER_SHORT_NAME"));
                    lstbankMid.add(bankMid);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getBankMidMerchant3b: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Map<String, Object> getBankConfig() throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_BANK_CONFIG(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid bankMid = new BankMid();
                    bankMid.setAccount(rs.getString("S_ACCOUNT"));
                    bankMid.setBankConfigId(rs.getInt("N_BANK_CONFIG_ID"));
                    bankMid.setBankName(rs.getString("S_BANK_NAME"));
                    bankMid.setBankCode(rs.getString("S_BANK_CODE"));
                    bankMid.setSwiftCode(rs.getString("S_SWIFT_CODE"));
                    bankMid.setCardType(rs.getString("S_CARD_TYPE"));
                    bankMid.setCiac(rs.getString("S_CIAC"));
                    bankMid.setType(rs.getString("S_TYPE"));
                    bankMid.setAuto(rs.getString("S_AUTO"));
                    lstbankMid.add(bankMid);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getBankConfig: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Integer updateAutoForBankConfig(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#UPDATE_AUTO_CIAC_BANK_MID(?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, Integer.valueOf(obj.getString("id") != null ? obj.getString("id") : "0"));
            cs.setString(4, obj.getString("auto") != null ? obj.getString("auto") : "");
            cs.setString(5, obj.getString(USER_ID) != null ? obj.getString(USER_ID) : "");
            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateAutoForBankConfig: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getMerchantBankCiac(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        ResultSet rsTotal = null;
        int total = 0;
        Map<String, Object> data = new HashMap<>();
        List<MerchantBank> merchantBanks = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#G_MERCHANT_BANK_CIAC(?,?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(5, Integer.parseInt(mIn.get(ID).toString()));
            cs.setString(6, mIn.get(KEYWORD) != null ? mIn.get(KEYWORD).toString() : "");
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setInt(8, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 20));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            int nResult = cs.getInt(3);
            String sResult = cs.getString(4);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    MerchantBank merchantBank = new MerchantBank();
                    merchantBank.setId(rs.getInt("N_ID"));
                    merchantBank.setBankMidId(rs.getInt("N_BANK_MID_ID"));
                    merchantBank.setBankMerchantId(
                            rs.getString("S_BANK_MERCHANT_ID") != null ? rs.getString("S_BANK_MERCHANT_ID") : "");
                    merchantBank.setMerchantBankKey(
                            rs.getString("MERCHANT_IDS") != null ? rs.getString("MERCHANT_IDS") : "");
                    merchantBank.setCreate(rs.getString("D_CREATE") != null ? sdf.format(rs.getDate("D_CREATE")) : "");
                    merchantBank.setUpdate(rs.getString("D_UPDATE") != null ? sdf.format(rs.getDate("D_UPDATE")) : "");
                    merchantBank.setPriority(rs.getString("N_PRIORITY") != null ? rs.getInt("N_PRIORITY") : null);
                    merchantBank.setDesc(rs.getString("S_DESC") != null ? rs.getString("S_DESC") : "");
                    merchantBank.setPayChannel(rs.getString("S_PAY_CHANNEL") != null ? rs.getString("S_PAY_CHANNEL") : "");
                    merchantBank.setCiac(rs.getString("S_CIAC") != null ? rs.getString("S_CIAC") : "");
                    //Add Expire Date
                    merchantBank.setExpireDate(rs.getDate("D_EXPIRE") != null ? rs.getDate("D_EXPIRE") : null);
                    merchantBanks.add(merchantBank);

                }
                while (rsTotal != null && rsTotal.next()) {
                    total = rsTotal.getInt("TOTAL_DATA");
                }
            }
            if (rsTotal != null)
                rsTotal.close();
            data.put("list", merchantBanks);
            data.put("total", total);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    public static Integer insertMerchantBankCiac(JsonObject obj, Integer bankMidId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        DateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#I_BANK_MERCHANT_CIAC(?,?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, bankMidId);
            cs.setString(4, obj.getString(BANK_MERCHANT_ID).trim());
            cs.setString(5, obj.getString("ciac").trim());
            cs.setString(6, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setDate(7, obj.getString("expireDate") != null ? new java.sql.Date(sdf.parse(obj.getString("expireDate")).getTime()) : null);
            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB insertMerchantBankCiac error: {0} " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer updateMerchantBankCiac(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        DateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#U_BANK_MERCHANT_CIAC(?,?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, obj.getInteger(ID));
            cs.setString(4, obj.getString("ciac").trim());
            cs.setString(5, obj.getString(DESC) != null ? obj.getString(DESC) : "");
            cs.setDate(6, obj.getString("expireDate") != null ? new java.sql.Date(sdf.parse(obj.getString("expireDate")).getTime()) : null);

            cs.execute();
            String error = cs.getString(2);
            result = cs.getInt(1);
            if (result == 500) {
                throw new Exception("DB updateMerchantBankCiac error: {0} " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void deleteMerchantBankCiac(int id) throws SQLException {
        Connection con = null;
        CallableStatement cs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#D_BANK_MERCHANT_CIAC(?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 0) {
                logger.log(Level.SEVERE, "DB deleteMerchantBankCiac error: {0}", error);
            }
        } finally {
            closeConnectionDB(null, null, cs, con);
        }
    }

    /**
     * Update enable acquirer bank id
     * @param acquirerBankId acquirer bank id
     * @param merchantBankId merchant bank id
     * @throws Exception
     */
    public static int updateEnableAcquirerBank(String acquirerBankId, String merchantBankId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(U_ENABLE_ACQUIRER_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, acquirerBankId);
            cs.setString(4, merchantBankId);
            cs.execute();
            result = cs.getInt(1);
            String error = cs.getString(2);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * Update assign merchant to acquirer bank
     * @param obj Json chứa thông tin bank merchant và merchant cần được gán
     * @throws Exception
     */
    public static int assignMerchantToAcquirerBank(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(I_MERCHANT_TO_ACQUIRER_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, obj.getString("AcquirerBankID"));
            cs.setString(4, obj.getString("MerchantID"));
            cs.execute();
            result = cs.getInt(1);
            String error = cs.getString(2);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * Update state merchant to acquirer bank
     * @param obj Json chứa thông tin bank merchant và merchant cần được cập nhật trạng thái
     * @throws Exception
     */
    public static int updateStateMerchantToAcquirerBank(JsonObject obj) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int result = 0;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(U_MERCHANT_TO_ACQUIRER_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, obj.getString("AcquirerBankID"));
            cs.setString(4, obj.getString("MerchantID"));
            cs.setString(5, obj.getString("State"));
            cs.execute();
            result = cs.getInt(1);
            String error = cs.getString(2);
            if (result == 500) {
                throw new Exception("DB updateBankMid: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static JsonObject getMerchantByAcquirerBankId(String acquirerBankId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        JsonObject result = new JsonObject();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(G_MERCHANT_BY_ACQUIRER_BANK);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.CURSOR);
            cs.setString(5, acquirerBankId);
            cs.execute();
            int nResult = cs.getInt(1);
            String sResult = cs.getString(2);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                int total = cs.getInt(3);
                result.put("total", total);
                rs = (ResultSet) cs.getObject(4);
                List<AcquirerBankMerchant> acquirerBankMerchants = new ArrayList<>();
                while (rs != null && rs.next()) {
                    AcquirerBankMerchant acquirerBankMerchant = new AcquirerBankMerchant();
                    acquirerBankMerchant.setMerchantId(rs.getString("S_MERCHANT_ID"));
                    acquirerBankMerchant.setAcquirerBankId(rs.getString("S_ACQUIRER_BANK_ID"));
                    acquirerBankMerchant.setState(rs.getString("S_STATE"));
                    acquirerBankMerchant.setCreateDate(rs.getDate("D_CREATE") != null ? sdf.format(rs.getDate("D_CREATE")) : "");
                    acquirerBankMerchant.setUpdateDate(rs.getDate("D_UPDATE") != null ? sdf.format(rs.getDate("D_UPDATE")) : "");
                    acquirerBankMerchants.add(acquirerBankMerchant);
                }
                result.put("list", acquirerBankMerchants);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<String, Object> getMerchantCiacByBankMid(int bankMidId, String caic) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, Object> data = new HashMap<>();
        List<BankMid> lstbankMid = new ArrayList<>();
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall("{call ONEFIN.PKG_MERCHANT_ID#GET_MERCHANT_CIAC(?,?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, bankMidId);
            cs.setString(5, caic);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception(sResult);
            } else {
                while (rs != null && rs.next()) {
                    BankMid obj = new BankMid();
                    obj.setMerchantBankIds(rs.getString("S_MERCHANT_ID"));
                    lstbankMid.add(obj);
                }
            }
            data.put("list", lstbankMid);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR getMerchantCiacByBankMid: ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return data;
    }

    private static MerchantBankDetail convertMerchantBankDetail(ResultSet rs) throws SQLException {
        MerchantBankDetail result = new MerchantBankDetail();
        result.setBankId(rs.getInt("N_ID"));
        result.setBankMID(rs.getString("S_BANK_MID"));
        result.setPayChannels(rs.getString("S_PAY_CHANNELS"));
        result.setContractType(rs.getString("S_CONTRACT_TYPE"));
        result.setAcquirer(rs.getString("S_ACQUIRER"));
        result.setAcquirerId(rs.getInt("N_ACQUIRER_ID"));
        result.setActive(rs.getString("S_ACTIVE"));
        result.setCurrency(rs.getString("S_CURRENCY"));
        result.setBankMerchantId(rs.getString("S_BANK_MERCHANT_ID"));
        result.setMerchantBankKey(rs.getString("S_MERCHANT_BANK_KEY"));
        result.setPriority(rs.getInt("N_PRIORITY"));
        result.setActiveNone3DS(rs.getString("S_ACTIVE_NONE_3DS"));
        result.setEnableNoneCSC(rs.getString("S_ENABLE_NONE_CSC"));
        result.setName(rs.getString("S_NAME"));
        result.setGroupAcquirer(rs.getString("S_GROUP_ACQUIRER"));
        result.setPartnerId(rs.getString("S_PARTNER_ID"));
        return result;
    }
}
