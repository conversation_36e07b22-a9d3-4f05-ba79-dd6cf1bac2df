package vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.contract.ContractHandler;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.gson;
import static vn.onepay.portal.Util.sendResponse;

public class BlackListMerchantHandler implements IConstants {
    private static Logger logger = Logger.getLogger(ContractHandler.class.getName());

    public static void loadBlackListMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(KEYWORD, request.getParam(KEYWORD) == null ? BLANK : request.getParam(KEYWORD));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 20 : request.getParam(PAGE_SIZE));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : request.getParam(PAGE));
                sendResponse(ctx, 200, BlackListMerchantDao.getBlackListMerchant(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void loadBlackListMerchantById(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer blmId = request.getParam(ID) == null ? 0 : Integer.parseInt(request.getParam(ID));
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, blmId);
                sendResponse(ctx, 200, BlackListMerchantDao.getBlackListMerchantById(mIn));
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void removeBLMByID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                sendResponse(ctx, 200, BlackListMerchantDao.deleteBLMbyID(mIn));
            } catch (Exception e) {
                logger.severe("DB Remove Contract error : " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }

    // insert and update Black List Merchant
    public static void insertOrUpdateBLM(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                JsonObject body = ctx.getBodyAsJson();

                Integer id = ctx.get(X_USER_ID);
                if (id == null) {
                    logger.warning("user id not found");
                    throw IErrors.RESOURCE_NOT_FOUND;
                }

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ID, request.getParam(ID) == null ? BLANK : request.getParam(ID));
                mIn.put(BUSINESS_REGISTRATION_NAME, body.getString(BUSINESS_REGISTRATION_NAME) == null ? BLANK : body.getString(BUSINESS_REGISTRATION_NAME));
                mIn.put(BUSINESS_REGISTRATION_NUMBER, body.getString(BUSINESS_REGISTRATION_NUMBER) == null ? BLANK : body.getString(BUSINESS_REGISTRATION_NUMBER));
                mIn.put(ADDRESS, body.getString(ADDRESS) == null ? BLANK : body.getString(ADDRESS));
                mIn.put(LEGAL_REPRESENTATIVE, body.getString(LEGAL_REPRESENTATIVE) == null ? 0 : body.getString(LEGAL_REPRESENTATIVE));
                mIn.put(INDUSTRY, body.getString(INDUSTRY) == null ? BLANK : body.getString(INDUSTRY));
                mIn.put(WEBSITE, body.getString(WEBSITE) == null ? BLANK : body.getString(WEBSITE));
                mIn.put(NOTE, body.getString(NOTE) == null ? BLANK : body.getString(NOTE));
                mIn.put(USER, body.getString(USER) == null ? BLANK : body.getString(USER));
                mIn.put(BLACK_LIST_MERCHANT_SEARCH, body.getString(BLACK_LIST_MERCHANT_SEARCH) == null ? BLANK : body.getString(BLACK_LIST_MERCHANT_SEARCH));
                sendResponse(ctx, 200, BlackListMerchantDao.insertOrUpdateBLM(mIn));
            } catch (Exception e) {
                logger.severe("UPDATE Acceptance ERROR: " + e.getMessage());
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
//                Map<String, Object> bodyMap = gson.fromJson(bodyJson, Map.class);
                Map<String, Object> mIn = new HashMap<>();

                mIn.put(KEYWORD, bodyJson.getString(KEYWORD) == null ? BLANK : bodyJson.getString(KEYWORD));
                mIn.put(OFFSET, 0);
                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));


                int totalRows = BlackListMerchantDao.getBlackListMerchant(mIn).getTotalItems();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "black_list_merchant_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("black_list_merchant_");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                }else{
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    //fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    //fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
