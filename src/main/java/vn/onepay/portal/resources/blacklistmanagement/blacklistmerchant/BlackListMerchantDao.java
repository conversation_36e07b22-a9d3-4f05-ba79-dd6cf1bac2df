package vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.resources.Db;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant.dto.BlackListMerchantDto;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.ActionDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class BlackListMerchantDao extends Db implements IConstants {
    public static BaseList<BlackListMerchantDto> getBlackListMerchant(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<BlackListMerchantDto> result = new BaseList<>();
        List<BlackListMerchantDto> list = new ArrayList<>();
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            conn = getConnection114();
            cs = conn.prepareCall("{call ONEPORTAL.PKG_BLACK_LIST_MANAGEMENT.load_black_list_merchant(?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setString(5, mIn.get(KEYWORD).toString());
            cs.setInt(6, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 20));
            cs.setInt(7, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.execute();
            String error = cs.getString(4);
            int nerror = cs.getInt(3);
            rs = (ResultSet) cs.getObject(1);
            if (nerror == 0) {
                logger.severe("DB get list branch error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new BlackListMerchantDto().setId(rs.getInt("N_ID")).setBusiness_registration_name(rs.getString("S_BUSINESS_REGISTRATION_NAME"))
                            .setBusiness_registration_number(rs.getString("S_BUSINESS_REGISTRATION_NUMBER")).setAddress(rs.getString("S_ADDRESS"))
                            .setLegal_representative(rs.getString("S_LEGAL_REPRESENTATIVE")).setIndustry(rs.getString("S_INDUSTRY"))
                            .setWebsite(rs.getString("S_WEBSITE")).setNote(rs.getString("S_NOTE"))
                            .setUpdate(rs.getTimestamp("D_UPDATE")).setUser(rs.getString("S_USER")));
                }
            }
            result.setTotalItems(cs.getInt(2));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, conn);
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

//     public static ResultSet download(Connection connection, Map mIn) throws Exception {
//         Exception exception = null;
//         Connection conn = null;
//         CallableStatement cs = null;
//         ResultSet rs = null;

//         try {
//             conn = getConnection114();
//             cs = conn.prepareCall("{call ONEPORTAL.PKG_BLACK_LIST_MANAGEMENT.load_black_list_merchant(?,?,?,?,?,?,?)}");
//             cs.registerOutParameter(1, OracleTypes.CURSOR);
//             cs.registerOutParameter(2, OracleTypes.NUMBER);
//             cs.registerOutParameter(3, OracleTypes.NUMBER);
//             cs.registerOutParameter(4, OracleTypes.VARCHAR);
//             cs.setString(5, mIn.get(KEYWORD).toString());
//             cs.setInt(6, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 20));
//             cs.setInt(7, Convert.parseInt(mIn.get(PAGE).toString(), 0));
//             cs.execute();
//             String error = cs.getString(4);
//             int nerror = cs.getInt(3);
//             rs = (ResultSet) cs.getObject(1);
//             if (nerror == 0) {
//                 logger.severe("DB download black list merchant error: " + error);
//             }
//         } catch (Exception e) {
//             logger.log(Level.SEVERE, "", e);
//             exception = e;
//         } finally {
// //            closeConnectionDB(rs, null, cs, conn);
//         }
//         if (exception != null)
//             throw exception;
//         return rs;
//     }

    public static BaseList<BlackListMerchantDto> getBlackListMerchantById(Map mIn) throws Exception {
        Exception exception = null;
        BaseList<BlackListMerchantDto> result = new BaseList<>();
        List<BlackListMerchantDto> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_BLACK_LIST_MANAGEMENT.load_b_l_m_by_id(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError == 0) {
                logger.severe("DB load contract by partner id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(new BlackListMerchantDto().setId(rs.getInt("N_ID")).setBusiness_registration_name(rs.getString("S_BUSINESS_REGISTRATION_NAME"))
                            .setBusiness_registration_number(rs.getString("S_BUSINESS_REGISTRATION_NUMBER")).setAddress(rs.getString("S_ADDRESS"))
                            .setLegal_representative(rs.getString("S_LEGAL_REPRESENTATIVE")).setIndustry(rs.getString("S_INDUSTRY"))
                            .setWebsite(rs.getString("S_WEBSITE")).setNote(rs.getString("S_NOTE"))
                            .setUpdate(rs.getTimestamp("D_UPDATE")).setUser(rs.getString("S_USER")));
                }
            }
            result.setTotalItems(list.size());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // 
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        result.setList(list);
        return result;
    }

    // - Remove Onsite by id:
    public static ActionDto deleteBLMbyID(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_BLACK_LIST_MANAGEMENT.remove_b_l_m_by_id(?,?,?)}");
            cs.setInt(3, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 0) {
                logger.severe("DB insert onsite error: " + error);
            }
            result = new ActionDto().nResult(1).sResult("");

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // 
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    // insert and update Black List Merchant
    public static ActionDto insertOrUpdateBLM(Map mIn) throws Exception {
        Exception exception = null;
        ActionDto result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(
                    "{ call ONEPORTAL.PKG_BLACK_LIST_MANAGEMENT.insert_update_b_l_m(?,?,?,?,?,?,?,?,?,?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setInt(4, Convert.parseInt(mIn.get(ID).toString(), 0));
            cs.setString(5, mIn.get(BUSINESS_REGISTRATION_NAME).toString());
            cs.setString(6, mIn.get(BUSINESS_REGISTRATION_NUMBER).toString());
            cs.setString(7, mIn.get(ADDRESS).toString());
            cs.setString(8, mIn.get(LEGAL_REPRESENTATIVE).toString());
            cs.setString(9, mIn.get(INDUSTRY).toString());
            cs.setString(10, mIn.get(WEBSITE).toString());
            cs.setString(11, mIn.get(NOTE).toString());
            cs.setString(12, mIn.get(USER).toString());
            cs.setString(13, mIn.get(BLACK_LIST_MERCHANT_SEARCH).toString());
            cs.execute();
            int nError = cs.getInt(1);
            String error = cs.getString(2);
            int nId = cs.getInt(3);
            if (nError == 0) {
                logger.severe("DB update Onsite error: " + error + "| id Onsite:" + nId);
            } else {
                result = new ActionDto().nResult(nError).sResult(error).id(nId);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // 
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
