package vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant.dto;

import java.sql.Timestamp;

public class BlackListMerchantDto {
    private int id;
    private String business_registration_name;
    private String business_registration_number;
    private String address;
    private String legal_representative;
    private String industry;
    private String website;
    private String note;
    private Timestamp update;
    private String user;

    public BlackListMerchantDto() {
    }

    public BlackListMerchantDto(int id, String business_registration_name, String business_registration_number, String address, String legal_representative, String industry, String website, String note, Timestamp update, String user) {
        this.id = id;
        this.business_registration_name = business_registration_name;
        this.business_registration_number = business_registration_number;
        this.address = address;
        this.legal_representative = legal_representative;
        this.industry = industry;
        this.website = website;
        this.note = note;
        this.update = update;
        this.user = user;
    }

    public int getId() {
        return id;
    }

    public String getBusiness_registration_name() {
        return business_registration_name;
    }

    public String getBusiness_registration_number() {
        return business_registration_number;
    }

    public String getAddress() {
        return address;
    }

    public String getLegal_representative() {
        return legal_representative;
    }

    public String getIndustry() {
        return industry;
    }

    public String getWebsite() {
        return website;
    }

    public String getNote() {
        return note;
    }

    public Timestamp getUpdate() {
        return update;
    }

    public String getUser() {
        return user;
    }

    public BlackListMerchantDto setId(int id) {
        this.id = id;
        return this;
    }

    public BlackListMerchantDto setBusiness_registration_name(String business_registration_name) {
        this.business_registration_name = business_registration_name;
        return this;
    }

    public BlackListMerchantDto setBusiness_registration_number(String business_registration_number) {
        this.business_registration_number = business_registration_number;
        return this;
    }

    public BlackListMerchantDto setAddress(String address) {
        this.address = address;
        return this;
    }

    public BlackListMerchantDto setLegal_representative(String legal_representative) {
        this.legal_representative = legal_representative;
        return this;
    }

    public BlackListMerchantDto setIndustry(String industry) {
        this.industry = industry;
        return this;
    }

    public BlackListMerchantDto setWebsite(String website) {
        this.website = website;
        return this;
    }

    public BlackListMerchantDto setNote(String note) {
        this.note = note;
        return this;
    }

    public BlackListMerchantDto setUpdate(Timestamp update) {
        this.update = update;
        return this;
    }

    public BlackListMerchantDto setUser(String user) {
        this.user = user;
        return this;
    }

    @Override
    public String toString() {
        return "BlackListMerchantDto{" +
                "id=" + id +
                ", business_registration_name='" + business_registration_name + '\'' +
                ", business_registration_number='" + business_registration_number + '\'' +
                ", address='" + address + '\'' +
                ", legal_representative='" + legal_representative + '\'' +
                ", industry='" + industry + '\'' +
                ", website='" + website + '\'' +
                ", note='" + note + '\'' +
                ", update=" + update +
                ", user='" + user + '\'' +
                '}';
    }
}
