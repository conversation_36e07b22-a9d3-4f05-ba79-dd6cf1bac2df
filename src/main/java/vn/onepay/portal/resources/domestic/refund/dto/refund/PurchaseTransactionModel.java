package vn.onepay.portal.resources.domestic.refund.dto.refund;

import vn.onepay.portal.resources.domestic.transaction.dto.DomesticPurchase;

public class PurchaseTransactionModel {
    private String refund_id;
    private DomesticPurchase domesticPurchase;
    private RefundApprovalModel refundApprovalModel;
    private RefundTransactionLogModel refundTransactionLogModel;

    public String getRefund_id() {
        return refund_id;
    }

    public void setRefund_id(String refund_id) {
        this.refund_id = refund_id;
    }

    public DomesticPurchase getDomesticPurchase() {
        return domesticPurchase;
    }

    public void setDomesticPurchase(DomesticPurchase domesticPurchase) {
        this.domesticPurchase = domesticPurchase;
    }

    public RefundApprovalModel getRefundApprovalModel() {
        return refundApprovalModel;
    }

    public void setRefundApprovalModel(RefundApprovalModel refundApprovalModel) {
        this.refundApprovalModel = refundApprovalModel;
    }

    public RefundTransactionLogModel getRefundTransactionLogModel() {
        return refundTransactionLogModel;
    }

    public void setRefundTransactionLogModel(RefundTransactionLogModel refundTransactionLogModel) {
        this.refundTransactionLogModel = refundTransactionLogModel;
    }
}
