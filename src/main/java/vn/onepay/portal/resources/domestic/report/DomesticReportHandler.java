package vn.onepay.portal.resources.domestic.report;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.*;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticReportHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(DomesticReportHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void searchDomesticReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());

                JsonObject jResponse = domesticTxnSync(new JsonObject(gson.toJson(DomesticReportDao.search(mIn))));

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH DOMESTIC REPORT SEARCH: ", e);
                ctx.fail(e);
            }
        },
                false, null);
    }

    private static JsonObject domesticTxnSync(JsonObject jSearch) {
        JsonObject jResult = new JsonObject();
        jResult.mergeIn(jSearch);
        JsonObject jSycn = null;
        try {

            String jobId = "20017";
            String timeSkip = "2m";
            jSycn = OneSchedClient.synchronize2(jobId, timeSkip);
        } catch (Exception e) {
            logger.log(Level.WARNING, "DOMESTIC REPORT SYNC ERROR: ", e);
        }
        if (jSycn != null) {
            jResult.mergeIn(jSycn);
        }
        return jResult;
    }

    public static void searchDomesticReport2(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());
                String fromDate = mIn.get(FROM_DATE);
                String toDate = mIn.get(TO_DATE);
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                sendResponse(ctx, 200, DomesticReportDao2.search(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH DOMESTIC REPORT SEARCH: ", e);
                ctx.fail(e);
            }
        },
                false, null);
    }

    public static void downloadDomesticReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                Date oFromDate;
                Date oToDate;
                String downloadType = bodyJson.getString(DOWNLOADTYPE);
                try {
                    oFromDate = df.parse(bodyJson.getString(FROM_DATE).toString());
                    oToDate = df.parse(bodyJson.getString(TO_DATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    bodyJson.put(FROM_DATE, sdf.format(oFromDate));
                    bodyJson.put(TO_DATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOWNLOAD DOMESTIC TRANSACTION ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                Map<String, Object> request = bodyJson.getMap();
                Map<String, String> rqData = new HashMap<>();
                for (Map.Entry<String, Object> data : request.entrySet()) {
                    rqData.put(data.getKey(), data.getValue().toString());
                }
                Integer total = DomesticReportDao.total(rqData);
                if (EXCEL.equals(downloadType) && total > 60000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "domestic_report_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", bodyJson.getMap());
                data.put("file_name", fileName);
                data.put("row", total);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("domestic_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (total <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt(CSV.equals(downloadType) ? "csv" : "xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (total <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD DOMESTIC REPORT Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadDomesticReport2(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                Date oFromDate;
                Date oToDate;
                String downloadType = bodyJson.getString(DOWNLOADTYPE);
                try {
                    oFromDate = df.parse(bodyJson.getString(FROM_DATE).toString());
                    oToDate = df.parse(bodyJson.getString(TO_DATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    bodyJson.put(FROM_DATE, sdf.format(oFromDate));
                    bodyJson.put(TO_DATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOWNLOAD DOMESTIC 2 TRANSACTION ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                Map<String, Object> request = bodyJson.getMap();
                Map<String, String> rqData = new HashMap<>();
                for (Map.Entry<String, Object> data : request.entrySet()) {
                    rqData.put(data.getKey(), data.getValue().toString());
                }
                Integer total = DomesticReportDao2.total(rqData);
                if (EXCEL.equals(downloadType) && total > 60000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "domestic_report2_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", bodyJson.getMap());
                data.put("file_name", fileName);
                data.put("row", total);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("domestic_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (total <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt(CSV.equals(downloadType) ? "csv" : "xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (total <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD DOMESTIC REPORT Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
