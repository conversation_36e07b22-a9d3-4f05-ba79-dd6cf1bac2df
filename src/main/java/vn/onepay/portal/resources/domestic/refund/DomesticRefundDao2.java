package vn.onepay.portal.resources.domestic.refund;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.domestic.refund.dto.DomesticRefund;
import vn.onepay.portal.resources.domestic.refund.dto.RefundAmount;
import vn.onepay.portal.resources.domestic.transaction.dto.Acquirer;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticCard;
import vn.onepay.portal.resources.international.transaction.dto.CardDate;

public class DomesticRefundDao2 extends Db {
    private static final Logger LOGGER = Logger.getLogger(DomesticRefundDao2.class.getName());

    private static final String LIST_TRANSACTION_REFUND_118 = "{call PKG_RP_DOMESTIC_TXN.search_request_refund_v4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_TRANSACTION_REFUND_BY_ID_118 = "{call PKG_RP_DOMESTIC_TXN.get_request_refund_by_id_v4(?,?,?,?)}";

    public static BaseList<DomesticRefund> search(Map<String, String> mIn) throws Exception {
        BaseList<DomesticRefund> result = new BaseList<>();

        Integer totalRefundOnline = searchTotalRefund(mIn);
        LOGGER.log(Level.WARNING, "------------------START GET Domestic refund ONLINE----------------- ");
        List<DomesticRefund> listOnline = searchRefund(mIn, 1);
        LOGGER.log(Level.WARNING, "------------------END GET Domestic refund ONLINE----------------- ");
        mIn.put(OFFSET, String.valueOf(totalRefundOnline));

        List<DomesticRefund> transactionsFinal = new ArrayList<>();
        transactionsFinal.addAll(listOnline);
        LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_REFUND_ONLINE: " + listOnline.size());

        result.setList(transactionsFinal);
        result.setTotalItems(totalRefundOnline);

        return result;
    }

    public static Integer total(Map mIn) throws Exception {
        LOGGER.log(Level.WARNING, "------------------START GET TOTAL Domestic Refund Online----------------- ");
        Integer totalRefundOnline = searchTotalRefund(mIn);
        LOGGER.log(Level.WARNING, "------------------END  GET TOTAL Domestic Refund Online " + totalRefundOnline + "----------------- ");

        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_REFUND_ONLINE: " + totalRefundOnline);
        return totalRefundOnline;
    }

    private static Integer searchTotalRefund(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_TRANSACTION_REFUND_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setString(12, mIn.getOrDefault(STATUS, ""));
            cs.setString(13, mIn.getOrDefault(PAGE, "0"));
            cs.setString(14, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setString(15, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setString(16, mIn.getOrDefault(CAIC, ""));
            cs.setString(17, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(18, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(19, mIn.getOrDefault(PLATFORM, ""));
            cs.setString(20, mIn.getOrDefault(REFUND_BANK_TRANSACTION_ID, ""));
            cs.setString(21, mIn.getOrDefault(TRANSACTION_TYPE, ""));
            cs.setInt(22, Integer.parseInt(mIn.getOrDefault("confirmable", "0")));
            cs.setString(23, mIn.getOrDefault(ACTION_TYPE, ""));
            cs.setString(24, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<DomesticRefund> searchRefund(Map<String, String> mIn, Integer rows) throws Exception {
        Exception exception = null;
        List<DomesticRefund> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_TRANSACTION_REFUND_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setString(12, mIn.getOrDefault(STATUS, ""));
            cs.setString(13, mIn.getOrDefault(PAGE, "0"));
            cs.setString(14, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setString(15, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setString(16, mIn.getOrDefault(CAIC, ""));
            cs.setString(17, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(18, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(19, mIn.getOrDefault(PLATFORM, ""));
            cs.setString(20, mIn.getOrDefault(REFUND_BANK_TRANSACTION_ID, ""));
            cs.setString(21, mIn.getOrDefault(TRANSACTION_TYPE, ""));
            cs.setInt(22, Integer.parseInt(mIn.getOrDefault("confirmable", "0") )) ;
            cs.setString(23, mIn.getOrDefault(ACTION_TYPE, ""));
            cs.setString(24, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bind(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static DomesticRefund get(Integer id) throws Exception {
        DomesticRefund refund = getById(id);
        if (refund == null) {
            refund =  DomesticRefundDao.get(id);
        }
        return refund;
    }


    public static DomesticRefund getById(Integer id) throws Exception {
        DomesticRefund result = null;
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(GET_TRANSACTION_REFUND_BY_ID_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setObject(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic refund error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bind(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticRefund bind(ResultSet rs) throws Exception {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_NAME");
        String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInt("N_TRANSACTION_ID");
        int originalId = rs.getInt("N_ORIGINAL_ID");
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        Timestamp purchaseDate = Timestamp.valueOf(rs.getString("D_PURCHASE_DATE"));

        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInt("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String ip = rs.getString("S_IP");
        double total = rs.getDouble("N_AMOUNT");
        double purchaseTotal = rs.getDouble("N_PURCHASE_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");

        String cardDate = rs.getString("S_CARD_DATE");
        String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
        String cardNumber = rs.getString("S_CARD_NUMBER");
        String cardVerificationCode = String.valueOf(rs.getInt("N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");

        /** Comment round 3 */
        String transactionInfo = rs.getString("S_TRANSACTION_INFO");
        String authenticationUrl = rs.getString("S_RETURN_AUTHENTICATION_URL");
        String platform = Util.getColumnString(rs, "S_PLATFORM");
        String refundBankTransactionId = Util.getColumnString(rs, "s_refund_bank_transaction_id");
        String bankTransactionId = Util.getColumnString(rs, "s_bank_transaction_id");
        String actionType = Util.getColumnString(rs, "S_ACTION_TYPE");
        String source = Util.getColumnString(rs, "S_SOURCE");

        DomesticRefund transaction = new DomesticRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(status);
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setIp_address(ip);
        transaction.setOperator_id(operatorId);
        transaction.setTransaction_purchase_time(purchaseDate);
        transaction.setAuthentication_url(authenticationUrl);
        transaction.setTransaction_info(transactionInfo);
        transaction.setTransaction_info(transactionInfo);
        transaction.setPlatform(platform);
        transaction.setS_refund_bank_transaction_id(refundBankTransactionId);
        transaction.setS_bank_transaction_id(bankTransactionId);
        transaction.setActionType(actionType);
        transaction.setSource(source);
        
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        refundAmount.setPurchase_total(purchaseTotal);

        transaction.setAmount(refundAmount);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);


        transaction.setAcquirer(acquirer);

        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);
        transaction.setCard(card);

        transaction.setCaic(Util.getColumnString(rs, "S_CAIC"));
        transaction.setContract_type(Util.getColumnString(rs, "S_CONTRACT_TYPE"));
        transaction.setAcquirer_bank(Util.getColumnString(rs, "S_ACQUIER"));
        transaction.setConfirmable(Util.getColumnInteger(rs, "N_CONFIRMABLE"));

        return transaction;
    }
}
