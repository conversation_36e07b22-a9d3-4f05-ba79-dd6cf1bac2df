package vn.onepay.portal.resources.domestic.report;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReportConvert;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticReportDao extends Db {

    private static final String SEARCH_REPORT_DOMESTIC_118 = "{call PKG_RP_DOMESTIC_TXN.SEARCH_REPORTS_V6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionDao.class.getName());

    public static Map search(Map<String, String> mIn) throws Exception {
        LOGGER.log(Level.INFO, "Domestic Report Search Input: " + mIn);
        BaseList<DomesticReportConvert> result = new BaseList<>();
        mIn.put(OFFSET, "0");
        List<DomesticReportConvert> domesticReportConverts = getListDomesticReports(mIn);
        result.setList(domesticReportConverts);
        Map returnReport = new HashMap();
        int totalTransCount = 0;
        int totalTransFailCount = 0;
        int totalRefundCount = 0;
        int totalRefundFailCount = 0;
        int totalRefundPendingCount = 0;
        double totalTransAmount = 0;
        double totalTransOriginalAmount = 0;
        double totalTransFailAmount = 0;
        double totalRefundAmount = 0;
        double totalRefundFailAmount = 0;
        double totalRefundPendingAmount = 0;
        for (DomesticReportConvert items : domesticReportConverts) {
            totalTransCount += items.getTransaction_count();
            totalTransFailCount += items.getTransaction_fail_count();
            totalRefundCount += items.getRefund_count();
            totalRefundFailCount += items.getRefund_fail_count();
            totalRefundPendingCount += items.getRefund_pending_count();
            totalTransAmount += items.getTransaction_total();
            totalTransOriginalAmount += items.getTransaction_original_total();
            totalTransFailAmount += items.getTransaction_fail_total();
            totalRefundAmount += items.getRefund_total();
            totalRefundFailAmount += items.getRefund_fail_total();
            totalRefundPendingAmount += items.getRefund_pending_total();
        }
        returnReport.put(REPORTS, domesticReportConverts);
        returnReport.put(TOTAL_REFUND_TOTAL, totalRefundAmount);
        returnReport.put(TOTAL_REFUND_FAIL_TOTAL, totalRefundFailAmount);
        returnReport.put(TOTAL_REFUND_PENDING_TOTAL, totalRefundPendingAmount);
        returnReport.put(TOTAL_TRANSACTION_TOTAL, totalTransAmount);
        returnReport.put(TOTAL_TRANSACTION_ORIGINAL_TOTAL, totalTransOriginalAmount);
        returnReport.put(TOTAL_TRANSACTION_FAIL_TOTAL, totalTransFailAmount);
        returnReport.put(TOTAL_REFUND_COUNT, totalRefundCount);
        returnReport.put(TOTAL_REFUND_FAIL_COUNT, totalRefundFailCount);
        returnReport.put(TOTAL_REFUND_PENDING_COUNT, totalRefundPendingCount);
        returnReport.put(TOTAL_TRANSACTION_COUNT, totalTransCount);
        returnReport.put(TOTAL_TRANSACTION_FAIL_COUNT, totalTransFailCount);
        return returnReport;
    }

    public static List<DomesticReportConvert> getListDomesticReports(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<DomesticReportConvert> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_REPORT_DOMESTIC_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);            
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(VERSION, ""));
            cs.setString(6, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(7, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(8, mIn.getOrDefault(CAIC, ""));
            cs.setString(9, mIn.getOrDefault(CONTRACT_TYPE, ""));   
            cs.setString(10, mIn.getOrDefault(ACQUIRER_BANK, ""));   
            cs.setString(11, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(12, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(13, String.valueOf(mIn.getOrDefault(PLATFORM, "")));
            cs.setString(14, mIn.getOrDefault(INTERVAL, "1"));
            cs.setString(15, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindReport(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer total(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_REPORT_DOMESTIC_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);            
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.getOrDefault(VERSION, ""));
            cs.setString(6, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(7, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(8, mIn.getOrDefault(CAIC, ""));
            cs.setString(9, mIn.getOrDefault(CONTRACT_TYPE, ""));   
            cs.setString(10, mIn.getOrDefault(ACQUIRER_BANK, ""));   
            cs.setString(11, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(12, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(13, mIn.getOrDefault(PLATFORM, ""));
            cs.setString(14, mIn.getOrDefault(INTERVAL, "1"));
            cs.setString(15, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static DomesticReportConvert bindReport(ResultSet rs) throws SQLException {
        Timestamp report_date = rs.getString("TXN_DATE") == null ? null : Timestamp.valueOf(rs.getString("TXN_DATE"));
        String acquirer_name = rs.getString("ACQUIRER_NAME");
        String merchant_id = rs.getString("S_MERCHANT_ID");
        String caic = rs.getString("S_CAIC") == null ? "" : rs.getString("S_CAIC");
        String contractType = rs.getString("S_CONTRACT_TYPE");
        String acquirer_bank = rs.getString("S_ACQUIER")  == null ? "" : rs.getString("S_ACQUIER");
        String platform = rs.getString("S_PLATFORM")  == null ? "" : rs.getString("S_PLATFORM");

        Integer transaction_count = rs.getInt("transaction_count");
        Integer transaction_fail_count = rs.getInt("transaction_fail_count");
        Integer refund_count = rs.getInt("refund_count");
        Integer refund_fail_count = rs.getInt("refund_fail_count");
        Integer refund_pending_count = rs.getInt("refund_pending_count");
        
        Long transaction_total = rs.getLong("transaction_total");
        Long refund_total = rs.getLong("refund_total");
        Long transaction_fail_total = rs.getLong("transaction_fail_total");
        Long refund_fail_total = rs.getLong("refund_fail_total");
        Long refund_pending_total = rs.getLong("refund_pending_total");
        Long transaction_original_total = rs.getLong("transaction_original_total");
        
        DomesticReportConvert report = new DomesticReportConvert();
        report.setTransaction_date(report_date);
        report.setAcquirer_name(acquirer_name);
        report.setAcquirer_bank(acquirer_bank);
        report.setMerchant_id(merchant_id);
        report.setCaic(caic);
        report.setContract_type(contractType);
        report.setPlatform(platform);
        report.setTransaction_count(transaction_count);
        report.setTransaction_fail_count(transaction_fail_count);
        report.setRefund_count(refund_count);
        report.setRefund_fail_count(refund_fail_count);
        report.setRefund_pending_count(refund_pending_count);
        report.setTransaction_original_total(transaction_original_total);
        report.setTransaction_total(transaction_total);
        report.setTransaction_fail_total(transaction_fail_total);
        report.setRefund_total(refund_total);
        report.setRefund_fail_total(refund_fail_total);
        report.setRefund_pending_total(refund_pending_total);
        return report;
    }
}
