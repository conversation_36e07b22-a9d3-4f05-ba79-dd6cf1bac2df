package vn.onepay.portal.resources.domestic.refund.dto.refund;

import java.io.Serializable;

public class RefundConfirmReq implements Serializable {
    private long transactionId;
    private String merchantId;
    private String currencyCode;
    private double amount;
    private String operatorId;
    private String clientIp;
    private long refund_transactionId;
    private String commandCode; // MAMNUAL,AUTO
    private long status; //400- approved, 200-cancel
    private String additionInfo;

    public RefundConfirmReq(long transactionId) {

    }

    public RefundConfirmReq() {

    }

    public RefundConfirmReq(long transactionId, String merchantId, String currencyCode, double amount, String operatorId, String clientIp, long refund_transactionId) {
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.currencyCode = currencyCode;
        this.amount = amount;
        this.operatorId = operatorId;
        this.clientIp = clientIp;
        this.refund_transactionId = refund_transactionId;
    }

    public RefundConfirmReq(long transactionId, String merchantId, String currencyCode, double amount, String operatorId, String clientIp, long refund_transactionId, String commandCode, long status, String additionInfo) {
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.currencyCode = currencyCode;
        this.amount = amount;
        this.operatorId = operatorId;
        this.clientIp = clientIp;
        this.refund_transactionId = refund_transactionId;
        this.commandCode = commandCode;
        this.status = status;
        this.additionInfo = additionInfo;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public long getRefund_transactionId() {
        return refund_transactionId;
    }

    public void setRefund_transactionId(long refund_transactionId) {
        this.refund_transactionId = refund_transactionId;
    }

    public String getCommandCode() {
        return commandCode;
    }

    public void setCommandCode(String commandCode) {
        this.commandCode = commandCode;
    }

    public long getStatus() {
        return status;
    }

    public void setStatus(long status) {
        this.status = status;
    }

    public String getAdditionInfo() {
        return additionInfo;
    }

    public void setAdditionInfo(String additionInfo) {
        this.additionInfo = additionInfo;
    }

    @Override
    public String toString() {
        return "RefundConfirmReq{" +
                "transactionId=" + transactionId +
                ", merchantId='" + merchantId + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", amount=" + amount +
                ", operatorId='" + operatorId + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", refund_transactionId=" + refund_transactionId +
                ", commandCode='" + commandCode + '\'' +
                ", status=" + status +
                ", additionInfo='" + additionInfo + '\'' +
                '}';
    }
}
