package vn.onepay.portal.resources.domestic.transaction.dto;

import java.sql.Timestamp;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticTransactionHistory {
    private String advanced_status;
    private String currency;
    private double refund_total;
    private double total;
    private String merchant_transaction_ref;
    private String operator_id;
    private String original_id;
    private String parent_id;
    private String status;
    private String subHistories;
    private String transaction_id;
    private Timestamp transaction_time;
    private String transaction_type;
    private String desc;
    private double originalAmount; 

    public DomesticTransactionHistory() {
    }

    public DomesticTransactionHistory(DomesticTransactionHistory domesticTransactionHistory) {
        this.advanced_status = domesticTransactionHistory.advanced_status;
        this.currency = domesticTransactionHistory.currency;
        this.refund_total = domesticTransactionHistory.refund_total;
        this.total = domesticTransactionHistory.total;
        this.originalAmount = domesticTransactionHistory.originalAmount;
        this.merchant_transaction_ref = domesticTransactionHistory.merchant_transaction_ref;
        this.operator_id = domesticTransactionHistory.operator_id;
        this.original_id = domesticTransactionHistory.original_id;
        this.parent_id = domesticTransactionHistory.parent_id;
        this.status = domesticTransactionHistory.status;
        this.subHistories = domesticTransactionHistory.subHistories;
        this.transaction_id = domesticTransactionHistory.transaction_id;
        this.transaction_time = domesticTransactionHistory.transaction_time;
        this.transaction_type = domesticTransactionHistory.transaction_type;
        this.desc = domesticTransactionHistory.desc;
    }

    public DomesticTransactionHistory(String advanced_status, String currency, double refund_total, double total, String merchant_transaction_ref, String operator_id, String original_id, String parent_id, String status, String subHistories, String transaction_id, Timestamp transaction_time, String transaction_type, double originalAmount) {
        this.advanced_status = advanced_status;
        this.currency = currency;
        this.refund_total = refund_total;
        this.total = total;
        this.merchant_transaction_ref = merchant_transaction_ref;
        this.operator_id = operator_id;
        this.original_id = original_id;
        this.parent_id = parent_id;
        this.status = status;
        this.subHistories = subHistories;
        this.transaction_id = transaction_id;
        this.transaction_time = transaction_time;
        this.transaction_type = transaction_type;
        this.originalAmount = originalAmount;
    }

    public String getAdvanced_status() {
        return advanced_status;
    }

    public void setAdvanced_status(String advanced_status) {
        this.advanced_status = advanced_status;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public double getRefund_total() {
        return refund_total;
    }

    public void setRefund_total(double refund_total) {
        this.refund_total = refund_total;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }
    public double getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(double originalAmount) {
        this.originalAmount = originalAmount;
    }
    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public String getOriginal_id() {
        return original_id;
    }

    public void setOriginal_id(String original_id) {
        this.original_id = original_id;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSubHistories() {
        return subHistories;
    }

    public void setSubHistories(String subHistories) {
        this.subHistories = subHistories;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    /**
     * @return String return the desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * @param desc the desc to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

}
