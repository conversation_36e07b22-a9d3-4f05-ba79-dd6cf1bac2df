package vn.onepay.portal.resources.domestic.report.dto;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @Created 5/26/2020
 * <AUTHOR>
 */
public class DomesticReportConvert {
    private Timestamp transaction_date;
    private String acquirer_name;
    private String merchant_id;
    private String acquirer_id;
    private String caic;
    private String contract_type;
    private String acquirer_bank;
    private Integer transaction_count;
    private Integer refund_count;
    private Long transaction_total;
    private Long refund_total;

    private Long transaction_original_total = 0L;
    
    private Integer transaction_fail_count;
    private Long transaction_fail_total;

    private Integer refund_fail_count;
    private Long refund_fail_total;

    private Integer refund_pending_count;
    private Long refund_pending_total;

    private Long refund_wait_for_bank_total;
    private Long refund_wait_for_bank_amount;

    private Long refund_wait_for_onepay_total;
    private Long refund_wait_for_onepay_amount;
    private String platform;
    private String source;

    public String getSource() {
        return this.source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }
    
    public String getContract_type() {
        return contract_type;
    }
    public String getAcquirer_bank() {
        return acquirer_bank;
    }
    public void setAcquirer_bank(String acquirer_bank) {
        this.acquirer_bank = acquirer_bank;
    }
    public Long getRefund_pending_total() {
        return refund_pending_total;
    }
    public void setRefund_pending_total(Long refund_pending_total) {
        this.refund_pending_total = refund_pending_total;
    }
    public Integer getRefund_pending_count() {
        return refund_pending_count;
    }
    public void setRefund_pending_count(Integer refund_pending_count) {
        this.refund_pending_count = refund_pending_count;
    }
    public Long getRefund_fail_total() {
        return refund_fail_total;
    }
    public void setRefund_fail_total(Long refund_fail_total) {
        this.refund_fail_total = refund_fail_total;
    }
    public Integer getRefund_fail_count() {
        return refund_fail_count;
    }
    public void setRefund_fail_count(Integer refund_fail_count) {
        this.refund_fail_count = refund_fail_count;
    }
    public Long getTransaction_fail_total() {
        return transaction_fail_total;
    }
    public void setTransaction_fail_total(Long transaction_fail_total) {
        this.transaction_fail_total = transaction_fail_total;
    }
    public Integer getTransaction_fail_count() {
        return transaction_fail_count;
    }
    public void setTransaction_fail_count(Integer transaction_fail_count) {
        this.transaction_fail_count = transaction_fail_count;
    }
    public Long getTransaction_total() {
        return transaction_total;
    }
    public void setTransaction_total(Long transaction_total) {
        this.transaction_total = transaction_total;
    }
    public Long getRefund_total() {
        return refund_total;
    }
    public void setRefund_total(Long refund_total) {
        this.refund_total = refund_total;
    }
    public Integer getRefund_count() {
        return refund_count;
    }
    public void setRefund_count(Integer refund_count) {
        this.refund_count = refund_count;
    }
    public Integer getTransaction_count() {
        return transaction_count;
    }
    public void setTransaction_count(Integer transaction_count) {
        this.transaction_count = transaction_count;
    }
    public String getCaic() {
        return caic;
    }
    public void setCaic(String caic) {
        this.caic = caic;
    }
    public String getAcquirer_id() {
        return acquirer_id;
    }
    public void setAcquirer_id(String acquirer_id) {
        this.acquirer_id = acquirer_id;
    }
    public String getMerchant_id() {
        return merchant_id;
    }
    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }
    public String getAcquirer_name() {
        return acquirer_name;
    }
    public void setAcquirer_name(String acquirer_name) {
        this.acquirer_name = acquirer_name;
    }
    public Timestamp getTransaction_date() {
        return transaction_date;
    }
    public void setTransaction_date(Timestamp transaction_date) {
        this.transaction_date = transaction_date;
    }
    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

   /**
     * @return Integer return the refund_wait_for_bank_total
     */
    public Long getRefund_wait_for_bank_total() {
        return refund_wait_for_bank_total;
    }

    /**
     * @param refund_wait_for_bank_total the refund_wait_for_bank_total to set
     */
    public void setRefund_wait_for_bank_total(Long refund_wait_for_bank_total) {
        this.refund_wait_for_bank_total = refund_wait_for_bank_total;
    }

    /**
     * @return Long return the refund_wait_for_bank_amount
     */
    public Long getRefund_wait_for_bank_amount() {
        return refund_wait_for_bank_amount;
    }

    /**
     * @param refund_wait_for_bank_amount the refund_wait_for_bank_amount to set
     */
    public void setRefund_wait_for_bank_amount(Long refund_wait_for_bank_amount) {
        this.refund_wait_for_bank_amount = refund_wait_for_bank_amount;
    }

    /**
     * @return Long return the refund_wait_for_onepay_total
     */
    public Long getRefund_wait_for_onepay_total() {
        return refund_wait_for_onepay_total;
    }

    /**
     * @param refund_wait_for_onepay_total the refund_wait_for_onepay_total to set
     */
    public void setRefund_wait_for_onepay_total(Long refund_wait_for_onepay_total) {
        this.refund_wait_for_onepay_total = refund_wait_for_onepay_total;
    }

    /**
     * @return Long return the refund_wait_for_onepay_amount
     */
    public Long getRefund_wait_for_onepay_amount() {
        return refund_wait_for_onepay_amount;
    }

    /**
     * @param refund_wait_for_onepay_amount the refund_wait_for_onepay_amount to set
     */
    public void setRefund_wait_for_onepay_amount(Long refund_wait_for_onepay_amount) {
        this.refund_wait_for_onepay_amount = refund_wait_for_onepay_amount;
    }

    public Long getTransaction_original_total() {
        return transaction_original_total;
    }

    public void setTransaction_original_total(Long transaction_original_total) {
        this.transaction_original_total = transaction_original_total;
    }

}
