package vn.onepay.portal.resources.domestic.refund;

import oracle.jdbc.OracleTypes;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.base.dto.RefundData;
import vn.onepay.portal.resources.domestic.refund.dto.DomesticRefund;
import vn.onepay.portal.resources.domestic.refund.dto.RefundAmount;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReport;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticPurchase;

import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class DomesticRefundApprovalDao extends Db implements IConstants {


    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_MA_REFUND_APPROVE.search_refund(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_APPROVAL_GET_BY_ID = "{call PKG_MA_REFUND_APPROVE.get_refund_by_id(?,?,?,?)}";

    private static final Logger LOGGER = Logger.getLogger(DomesticRefundApprovalDao.class.getName());

    // public static BaseList<DomesticRefund> search(Map mIn) throws Exception {
    // LOGGER.log(Level.INFO, "query condition: " + mIn);
    // BaseList<DomesticRefund> result = new BaseList<>();
    // // get Total
    // List<String> totalApproval = getTotalTransaction(mIn);
    // LOGGER.log(Level.WARNING, "GET TOTAL International refund Approval: " +
    // Util.gson.toJson(totalApproval) + " IDS : " + String.join(",", totalApproval));
    // // get purchase By ids
    // Map<Integer, InternationalPurchase> purchaseMap =
    // InternationalTransactionDao.mapByIds(String.join(",", totalApproval), mIn);
    // Integer totalBackup = countFilterById(purchaseMap, totalApproval);
    // result.setTotalItems(totalBackup);
    //
    //
    // LOGGER.log(Level.WARNING, "International refund Approval Original Ids: " + totalApproval);
    // List<InternationalRefundApproval> refundApproval = searchTransaction(mIn);
    // // JOIN purchase INFO -------> filter != null
    // List<InternationalRefundApproval> refundApproval2 = refundApproval
    // .stream()
    // .map(approval -> {
    // return joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_transaction_id()));
    // })
    // .filter(InternationalRefund -> InternationalRefund != null)
    // .collect(Collectors.toList());
    //
    //
    // result.setList(refundApproval2);
    //
    // return result;
    // }

    public static List<DomesticReport> getDomesticReportPending(Map<String, String> mIn) throws Exception {
        List<DomesticReport> array = new ArrayList<>();
        Map<String, String> input = mIn;
        // get list wait for approval
        // convert from date, to date


        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        String fromDate = mIn.get(FROM_DATE);
        String toDate = mIn.get(TO_DATE);
        java.util.Date oFromDate;
        Date oToDate;
        try {
            oFromDate = df.parse(fromDate);
            oToDate = df.parse(toDate);
            oToDate = DateUtils.addDays(oToDate, 1);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            fromDate = sdf.format(oFromDate);
            toDate = sdf.format(oToDate);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
            throw IErrors.VALIDATION_ERROR;
        }
        input.put(FROM_DATE, fromDate);
        input.put(TO_DATE, toDate);
        input.put(PAGE_SIZE, String.valueOf(Integer.MAX_VALUE));
        List<DomesticRefund> listBackUp = DomesticRefundApprovalDao.searchTransaction(input);

        // join with purchase to reduce by acquirer
        List<String> purchaseIds = listBackUp.stream().map(domesticRefund -> domesticRefund.getOriginal_id().toString()).collect(Collectors.toList());
        Map<Integer, DomesticPurchase> purchaseMap = DomesticTransactionDao.mapByIds(String.join(",", purchaseIds), mIn);
        List<DomesticRefund> refundApproval2 = listBackUp
                .stream()
                .map(approval -> {
                    return joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_id()));
                })
                .filter(domesticRefund -> domesticRefund != null)
                .collect(Collectors.toList());

        // group by interval
        Map<LocalDate, Map<String, Map<String, Map<String, Map<String, Map<String, List<DomesticRefund>>>>>>> mapAfterGroup = refundApproval2.stream().collect(Collectors.groupingBy(e ->
        // easier way to truncate the date

        // Timestamp.from(e.getTransaction_time().toInstant().truncatedTo(mIn.get(INTERVAL).equals("1")?ChronoUnit.DAYS
        // : mIn.get(INTERVAL).equalsIgnoreCase("2")?ChronoUnit.WEEKS
        // : mIn.get(INTERVAL).equalsIgnoreCase("3")?ChronoUnit.MONTHS
        // : ChronoUnit.YEARS
        // ))

        e.getTransaction_time().toLocalDateTime().toLocalDate().with(ADJUSTERS.get(mIn.get(INTERVAL).equals("1") ? "day"
                : mIn.get(INTERVAL).equalsIgnoreCase("2") ? "week"
                        : mIn.get(INTERVAL).equalsIgnoreCase("3") ? "month"
                                : "year")),
                Collectors.groupingBy(DomesticRefund::getMerchant_id,
                        Collectors.groupingBy(o -> o.getAcquirer().getAcquirer_name(),
                                Collectors.groupingBy(o -> o.getCaic() == null ? "" : o.getCaic(),
                                        Collectors.groupingBy(o -> o.getContract_type() == null ? "" : o.getContract_type(),
                                            Collectors.groupingBy(o -> o.getAcquirer_bank() == null ? "" : o.getAcquirer_bank())))))));

        // convert to report model
        mapAfterGroup.keySet().forEach(localDate -> {
            mapAfterGroup.get(localDate).keySet().forEach(merchantId -> {
                mapAfterGroup.get(localDate).get(merchantId).keySet().forEach(acqName -> {
                    mapAfterGroup.get(localDate).get(merchantId).get(acqName).keySet().forEach(caic -> {
                        mapAfterGroup.get(localDate).get(merchantId).get(acqName).get(caic).keySet().forEach(contractType -> {
                            mapAfterGroup.get(localDate).get(merchantId).get(acqName).get(caic).get(contractType).keySet().forEach(acquirerBank -> {
                                List<DomesticRefund> list = mapAfterGroup.get(localDate).get(merchantId).get(acqName).get(caic).get(contractType).get(acquirerBank);
                                // .forEach(domesticRefund -> {
                                array.add(new DomesticReport(Timestamp.valueOf(localDate.atStartOfDay()), merchantId, acqName, null, caic, list.size(), list.stream().mapToLong(value -> new Double(value.getAmount().getTotal()).longValue()).sum(), "refund", "pending", contractType, acquirerBank));
                            });
                        });
                    });
                });
            });
        });
        return array;
    }


    static final Map<String, TemporalAdjuster> ADJUSTERS = new HashMap<>();

    static {
        ADJUSTERS.put("day", TemporalAdjusters.ofDateAdjuster(d -> d)); // identity
        ADJUSTERS.put("week", TemporalAdjusters.previousOrSame(DayOfWeek.of(1)));
        ADJUSTERS.put("month", TemporalAdjusters.firstDayOfMonth());
        ADJUSTERS.put("year", TemporalAdjusters.firstDayOfYear());
    }


    private static DomesticRefund joinApproveWithPurchase(DomesticRefund approve, DomesticPurchase purchase) {


        if (purchase == null) {
            return null;
        }

        DomesticRefund result = approve;

        result.setTransaction_purchase_time(purchase.getTransaction_time());
        result.setOrder_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());
        result.setCaic(purchase.getCaic());
        result.setContract_type(purchase.getContract_type());
        result.setAcquirer_bank(purchase.getAcquirer_bank());

        result.setCard(purchase.getCard());
        result.getAmount().setPurchase_total(purchase.getAmount().getTotal());
        // result.setIp_address(purchase.getIp_address());

        return result;
    }

    private static Integer countFilterById(Map<Integer, DomesticPurchase> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    public static List<String> getTotalTransaction(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<String> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_TRANSACTION_TOTAL_B);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(TRANSACTION_ID, ""));
            cs.setString(7, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(8, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(9, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(10, mIn.getOrDefault(STATUS, ""));
            cs.setString(11, "VND");
            cs.setInt(12, RefundData.Type.DOMESTIC.getValue());
            cs.setString(13, mIn.getOrDefault(REFUNDID, ""));
            cs.setNull(14, Types.INTEGER);
            cs.setNull(15, Types.INTEGER);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total domestic refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(Util.getColumnString(rs, "N_ORIGINAL_ID"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<DomesticRefund> searchTransaction(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<DomesticRefund> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_TRANSACTION_TOTAL_B);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(TRANSACTION_ID, ""));
            cs.setString(7, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(8, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(9, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(10, mIn.getOrDefault(STATUS, ""));
            cs.setString(11, "VND");
            cs.setInt(12, RefundData.Type.DOMESTIC.getValue());
            cs.setString(13, mIn.getOrDefault(REFUNDID, ""));
            cs.setString(14, mIn.getOrDefault(PAGE, "0"));
            cs.setString(15, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindApproval(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    public static DomesticRefund getTransactionById(Integer id) throws Exception {
        Exception exception = null;
        DomesticRefund result = new DomesticRefund();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(TRANSACTION_APPROVAL_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic refund approval by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindApproval(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticRefund bindApproval(ResultSet rs) throws Exception {
        String merchantId = Util.getColumnString(rs, "S_MERCHANT_ID");
        String orderInfo = Util.getColumnString(rs, "S_ORDER_INFO");

        String currency = Util.getColumnString(rs, "S_CURRENCY_CODE");
        int transactionId = Util.getColumnInteger(rs, "N_TRANSACTION_ID");
        int originalId = Integer.valueOf(Util.getColumnString(rs, "N_ORIGINAL_ID"));
        Timestamp date = Util.getColumnTimeStamp(rs, "D_MERCHANT_TRANSACTION_DATE");
        String transactionType = Util.getColumnString(rs, "S_TRANSACTION_TYPE");
        int status = Util.getColumnInteger(rs, "N_TRANSACTION_STATUS");
        String transactionRef = Util.getColumnString(rs, "S_MERCHANT_TRANSACTION_REF");
        double total = Util.getColumnDouble(rs, "N_AMOUNT");
        String operatorId = Util.getColumnString(rs, "S_OPERATOR_ID");


        DomesticRefund transaction = new DomesticRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(status);
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setOperator_id(operatorId);
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);

        transaction.setAmount(refundAmount);


        return transaction;
    }

}
