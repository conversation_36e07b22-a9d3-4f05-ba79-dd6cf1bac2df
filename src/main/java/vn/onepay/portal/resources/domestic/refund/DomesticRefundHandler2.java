package vn.onepay.portal.resources.domestic.refund;

import static vn.onepay.portal.Util.sendResponse;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MaClient;
import vn.onepay.portal.client.MspClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticPurchase;
import vn.onepay.portal.resources.permission.PermissionDao;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticRefundHandler2 implements IConstants {

    private static Logger logger = Logger.getLogger(DomesticRefundHandler2.class.getName());

    private static final Gson gson = new Gson();

    public static void searchDomesticRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());
                Integer userId = ctx.get(X_USER_ID);

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = mIn.get(FROM_DATE);
                String toDate = mIn.get(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL  GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(X_USER_ID, userId.toString());

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANT_ID) ? mIn.get(MERCHANT_ID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANT_ID, merchants.toString());
                    }
                }

                String finalCardNumber = FunctionUtil.isBeginIsACard(mIn.get(CARD_NUMBER)) && mIn.get(CARD_NUMBER).contains("*") ? mIn.get(CARD_NUMBER).replaceAll("\\*", "0") : mIn.get(CARD_NUMBER);
                if (FunctionUtil.isCardData(finalCardNumber)) {
                    String cardhash = FunctionUtil.oneSMHmac(finalCardNumber);
                    mIn.put(CARD_NUMBER, cardhash);
                }

                JsonObject jResponse = new JsonObject();

                JsonObject jSycn = domesticTxnSync();

                JsonObject jSearch = new JsonObject(gson.toJson(DomesticRefundDao2.search(mIn)));

                jResponse.mergeIn(jSearch);

                if (jSycn != null) {
                    jResponse.mergeIn(jSycn);
                }

                sendResponse(ctx, 200, jResponse);
            } catch (Exception e) {
                logger.warning(() -> ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static JsonObject domesticTxnSync() {
        JsonObject jSycn = null;
        try {
            String jobId = Config.getString("onesched-service.domestic_new_refund_id", "");
            jSycn = OneSchedClient.synchronize2(jobId, "0m");
        } catch (Exception e) {
            logger.log(Level.WARNING, "DOMESTIC REFUND SYNC ERROR: ", e);
        }
        return jSycn;
    }

    public static void getDomesticRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, DomesticRefundDao2.get(Integer.valueOf(id)));
            } catch (Exception e) {
                logger.warning(() -> ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START APPROVE REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject bodyJson = ctx.getBodyAsJson();
                String refundRequestId = bodyJson.getValue(ID).toString();
                String transactionId = bodyJson.getValue(TRANSACTION_ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = UUID.randomUUID().toString();
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(refundRequestId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE PATCH ] =>  VALIDATION ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = new JsonObject();


                Map<Integer, DomesticPurchase> purchaseMap = DomesticTransactionDao.mapByIds(String.join(",", transactionId), new HashMap<>());
                if (purchaseMap.isEmpty()) {
                    logger.log(Level.INFO,"Khong tim thay transactionId: " + transactionId);
                    ctx.fail(IErrors.INTERNAL_SERVER_ERROR);
                    return;
                }
                DomesticPurchase purchase = purchaseMap.get(Integer.valueOf(transactionId));
                if (purchase == null) {
                    logger.log(Level.INFO,"Khong tim thay transactionId: " + transactionId);
                    ctx.fail(IErrors.INTERNAL_SERVER_ERROR);
                    return;
                }
                if (!"300".equals(purchase.getStatus()) && !"400".equals(purchase.getStatus())) {
                    logger.log(Level.SEVERE,"TransactionId: " + transactionId + " khong phai la trang thai dung de approve refund" + purchase.getStatus());
                    ctx.fail(IErrors.INTERNAL_SERVER_ERROR);
                    return;
                }
                if ("300".equals(purchase.getStatus())) { // query purchase truoc khi refund
                    queryDomesticTransactionStatus(merchantId,transactionId, userId);
                }
                jResponse = MspClient.approveRefund(merchantId, refundRequestId, transactionRef, note, operator);
                if (jResponse == null || jResponse.isEmpty()) { // khong approve va khong query duoc
                    logger.log(Level.INFO,"Khong the Approve refund, kiem tra lai msp-exteng hoac payment2");
                    ctx.fail(IErrors.INTERNAL_SERVER_ERROR);
                    return;
                }
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END APPROVE REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void rejectRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START APPROVE REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String note = bodyJson.getString(NOTE);

                JsonObject jResponse = MspClient.rejectRefund(merchantId, transactionId, note, operator);

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END APPROVE REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void doManual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START MANUAL REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC MANUAL  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC MANUAL  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = UUID.randomUUID().toString();
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC MANUAL PATCH ] =>  VALIDATION ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.manual(merchantId, transactionId, transactionRef, note, operator);

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END MANUAL REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "MANUAL Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START UPDATE STATE REFUND ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String state = bodyJson.getString(STATE);
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String note = bodyJson.getString(NOTE);

                JsonObject jResponse = null;
                if (APPROVED.equals(state)) {
                    jResponse = MspClient.updateStateToApproved(merchantId, transactionId, note, operator);
                } else if (FAILED.equals(state)) {
                    jResponse = MspClient.updateStateToFailed(merchantId, transactionId, note, operator);
                } else {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] =>  INVALID STATE");
                    throw IErrors.VALIDATION_ERROR;
                }

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END UPDATE STATE REFUND ========");

                sendResponse(ctx, 200, jResponse);
            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void doAuto(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START AUTO REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC AUTO  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC AUTO  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = UUID.randomUUID().toString();
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC AUTO PATCH ] =>  VALIDATION ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.auto(merchantId, transactionId, transactionRef, note, operator);

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END AUTO REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "AUTO Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = ctx.getBodyAsJson();
                Integer userId = ctx.get(X_USER_ID);
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = mIn.getString(FROM_DATE);
                String toDate = mIn.getString(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(PAGE, "0");
                mIn.put(PAGE_SIZE, String.valueOf(Integer.MAX_VALUE));
                mIn.put(X_USER_ID, userId);

                if (null != mIn.getInteger(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(mIn.getInteger(X_USER_ID));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = mIn.getString(MERCHANT_ID, "");
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANT_ID, merchants.toString());
                    }
                }


                String finalCardNumber = FunctionUtil.isBeginIsACard(mIn.getString(CARD_NUMBER)) && mIn.getString(CARD_NUMBER).contains("*") ? mIn.getString(CARD_NUMBER).replaceAll("\\*", "0") : mIn.getString(CARD_NUMBER);
                if (FunctionUtil.isCardData(finalCardNumber)) {
                    String cardhash = FunctionUtil.oneSMHmac(finalCardNumber);
                    mIn.put(CARD_NUMBER, cardhash);
                }

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                int totalRows = DomesticRefundDao2.total(mIn.getMap());
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "domestic_refund_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn.getMap());
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("domestic_refund");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(mIn.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD DOMESTIC REFUND Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkAuto(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START CHECK REFUND AUTO OR MANUAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC APPROVE  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);

                JsonObject jResponse = MspClient.checkAutoManual(merchantId, transactionId);

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END CHECK REFUND AUTO OR MANUALL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "Check Domestic Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }
    private static void queryDomesticTransactionStatus(String merchantId, String transactionId, String userId) {
        try {
            JsonObject jQuery = MaClient.patchQueryDomesticTransaction(merchantId, "VND", transactionId, userId, userId, "127.0.0.1");
            if (jQuery != null && !jQuery.isEmpty()) {
                logger.log(Level.INFO,"QUERY TRANSACTION STATUS: ok");
                return;
            }
            logger.log(Level.INFO,"QUERY TRANSACTION STATUS: Error");
        } catch (Exception e) {
            logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
        }
    }
}
