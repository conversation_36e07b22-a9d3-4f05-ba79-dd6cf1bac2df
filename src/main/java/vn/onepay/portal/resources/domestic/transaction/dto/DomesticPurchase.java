package vn.onepay.portal.resources.domestic.transaction.dto;

import vn.onepay.portal.resources.base.dto.Amount;

import java.sql.Timestamp;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticPurchase {
    private int row_num;
    private int transaction_id;
    private String bank_transaction_id;
    private String transaction_type;
    private Acquirer acquirer;
    private String merchant_id;
    private DomesticCard card;
    private String merchant_transaction_ref;
    private Timestamp transaction_time;
    private Timestamp auth_time;
    private String order_info;
    private DomesticAmount amount;
    private DomesticAmount originalAmount;
    private DomesticAuthorisation authorisation;
    private String status;
    private String refund_status;
    private String transaction_info;
    private String ip_address;
    private String operator_id;
    private String advance_status;
    private Double wait_for_approval_amount;
    private String caic;
    private String contract_type;
    private String acquirer_bank;
    private String platform;
    private String source;

    public String getSource() {
        return this.source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getCaic() {
        return this.caic;
    }

    public String getAcquirer_bank() {
        return acquirer_bank;
    }

    public void setAcquirer_bank(String acquirer_bank) {
        this.acquirer_bank = acquirer_bank;
    }
    
    public String getBank_transaction_id() {
        return bank_transaction_id;
    }

    public void setBank_transaction_id(String bank_transaction_id) {
        this.bank_transaction_id = bank_transaction_id;
    }

    public String getContract_type() {
        return contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public void setCaic(String caic) {
        this.caic = caic;
    }

    public Double getWait_for_approval_amount() {
        return wait_for_approval_amount;
    }

    public void setWait_for_approval_amount(Double wait_for_approval_amount) {
        this.wait_for_approval_amount = wait_for_approval_amount;
    }

    public Timestamp getAuth_time() {
        return auth_time;
    }

    public void setAuth_time(Timestamp auth_time) {
        this.auth_time = auth_time;
    }

    public DomesticAuthorisation getAuthorisation() {
        return authorisation;
    }

    public void setAuthorisation(DomesticAuthorisation authorisation) {
        this.authorisation = authorisation;
    }

    /**
     * Getter for property 'advance_status'.
     *
     * @return Value for property 'advance_status'.
     */
    public String getAdvance_status() {
        return advance_status;
    }

    /**
     * Setter for property 'advance_status'.
     *
     * @param advance_status Value to set for property 'advance_status'.
     */
    public void setAdvance_status(String advance_status) {
        this.advance_status = advance_status;
    }

    public int getRow_num() {
        return row_num;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public DomesticCard getCard() {
        return card;
    }

    public void setCard(DomesticCard card) {
        this.card = card;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public DomesticAmount getAmount() {
        return amount;
    }

    public void setAmount(DomesticAmount amount) {
        this.amount = amount;
    }

    public DomesticAmount getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(DomesticAmount originalAmount) {
        this.originalAmount = originalAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(String refund_status) {
        this.refund_status = refund_status;
    }

    public String getTransaction_info() {
        return transaction_info;
    }

    public void setTransaction_info(String transaction_info) {
        this.transaction_info = transaction_info;
    }

    public String getIp_address() {
        return ip_address;
    }

    public void setIp_address(String ip_address) {
        this.ip_address = ip_address;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }
}
