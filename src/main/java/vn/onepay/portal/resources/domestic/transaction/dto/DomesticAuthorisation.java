package vn.onepay.portal.resources.domestic.transaction.dto;

import java.sql.Timestamp;

public class DomesticAuthorisation {
    String authorisation_url;
    Integer authorisation_code;
    String authorisation_info;
    Timestamp authorisation_time;
    String authorisation_return_url;

    public String getAuthorisation_url() {
        return authorisation_url;
    }

    public void setAuthorisation_url(String authorisation_url) {
        this.authorisation_url = authorisation_url;
    }

    public String getAuthorisation_info() {
        return authorisation_info;
    }

    public void setAuthorisation_info(String authorisation_info) {
        this.authorisation_info = authorisation_info;
    }

    public Integer getAuthorisation_code() {
        return authorisation_code;
    }

    public void setAuthorisation_code(Integer authorisation_code) {
        this.authorisation_code = authorisation_code;
    }

    public Timestamp getAuthorisation_time() {
        return authorisation_time;
    }

    public void setAuthorisation_time(Timestamp authorisation_time) {
        this.authorisation_time = authorisation_time;
    }

    public String getAuthorisation_return_url() {
        return authorisation_return_url;
    }

    public void setAuthorisation_return_url(String authorisation_return_url) {
        this.authorisation_return_url = authorisation_return_url;
    }
}
