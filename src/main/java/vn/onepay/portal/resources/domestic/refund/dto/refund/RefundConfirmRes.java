package vn.onepay.portal.resources.domestic.refund.dto.refund;

import java.util.Date;

public class RefundConfirmRes extends ServiceRes{
    private long transactionId;
    private String merchantId;
    private String currencyCode;
    private double amount;
    private String operatorId;
    private String clientIp;
    private long refund_transactionId;
    private Date transaction_date;
    private String transaction_source;
    private String merchant_transaction_ref;
    private int transaction_status;

    public RefundConfirmRes() {
    }



    public RefundConfirmRes(int status, String description, long transactionId, String merchantId, String currencyCode, double amount, String operatorId, String clientIp, long refund_transactionId, Date transaction_date, String transaction_source, String merchant_transaction_ref, int transaction_status) {
        super(status, description);
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.currencyCode = currencyCode;
        this.amount = amount;
        this.operatorId = operatorId;
        this.clientIp = clientIp;
        this.refund_transactionId = refund_transactionId;
        this.transaction_date = transaction_date;
        this.transaction_source = transaction_source;
        this.merchant_transaction_ref = merchant_transaction_ref;
        this.transaction_status = transaction_status;
    }

    public long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public long getRefund_transactionId() {
        return refund_transactionId;
    }

    public void setRefund_transactionId(long refund_transactionId) {
        this.refund_transactionId = refund_transactionId;
    }

    public Date getTransaction_date() {
        return transaction_date;
    }

    public void setTransaction_date(Date transaction_date) {
        this.transaction_date = transaction_date;
    }

    public String getTransaction_source() {
        return transaction_source;
    }

    public void setTransaction_source(String transaction_source) {
        this.transaction_source = transaction_source;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public int getTransaction_status() {
        return transaction_status;
    }

    public void setTransaction_status(int transaction_status) {
        this.transaction_status = transaction_status;
    }

    @Override
    public String toString() {
        return "RefundConfirmRes{" +
                "transactionId=" + transactionId +
                ", merchantId='" + merchantId + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", amount=" + amount +
                ", operatorId='" + operatorId + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", refund_transactionId=" + refund_transactionId +
                ", transaction_date=" + transaction_date +
                ", transaction_source='" + transaction_source + '\'' +
                ", merchant_transaction_ref='" + merchant_transaction_ref + '\'' +
                ", transaction_status=" + transaction_status +
                '}';
    }
}
