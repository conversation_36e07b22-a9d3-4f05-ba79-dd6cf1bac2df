package vn.onepay.portal.resources.domestic.report.dto;

import java.sql.Timestamp;

/**
 * @Created 5/26/2020
 * <AUTHOR>
 */
public class DomesticReport {
    private Timestamp report_date;
    private String merchant_id;
    private String acquirer_name;
    private String acquirer_id;
    private String caic;
    private String contract_type;
    private String acquirer_bank;
    private int count;
    private Long total;
    private String command;
    private String status;

    private Long purchase_success_amount;
    private Long purchase_success_total;

    private Long purchase_failed_amount;
    private Long purchase_failed_total;

    private Long refund_success_amount;
    private Long refund_success_total;

    private Long refund_failed_amount;
    private Long refund_failed_total;

    private Long refund_wfo_approval_amount;
    private Long refund_wfo_approval_total;

    private Long refund_wfb_approval_amount;
    private Long refund_wfb_approval_total;
    private String platform;

    public DomesticReport(){}

    public DomesticReport(Timestamp report_date, String merchant_id, String acquirer_name, String acquirer_id
    , String caic, int count, Long total, String command, String status, String contractType, String acquirerBank) {
        this.report_date = report_date;
        this.merchant_id = merchant_id;
        this.acquirer_name = acquirer_name;
        this.acquirer_id = acquirer_id;
        this.setCaic(caic);
        this.count = count;
        this.total = total;
        this.command = command;
        this.status = status;
        this.contract_type = contractType;
        this.acquirer_bank = acquirerBank;
    }

        public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getAcquirer_bank() {
        return acquirer_bank;
    }

    public void setAcquirer_bank(String acquirer_bank) {
        this.acquirer_bank = acquirer_bank;
    }

    public String getContract_type() {
        return contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public String getCaic() {
        return caic;
    }

    public void setCaic(String caic) {
        this.caic = caic;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Timestamp getReport_date() {
        return report_date;
    }

    public void setReport_date(Timestamp report_date) {
        this.report_date = report_date;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getAcquirer_name() {
        return acquirer_name;
    }

    public void setAcquirer_name(String acquirer_name) {
        this.acquirer_name = acquirer_name;
    }

    public String getAcquirer_id() {
        return acquirer_id;
    }

    public void setAcquirer_id(String acquirer_id) {
        this.acquirer_id = acquirer_id;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    /**
     * @return Long return the purchase_success_amount
     */
    public Long getPurchase_success_amount() {
        return purchase_success_amount;
    }

    /**
     * @param purchase_success_amount the purchase_success_amount to set
     */
    public void setPurchase_success_amount(Long purchase_success_amount) {
        this.purchase_success_amount = purchase_success_amount;
    }

    /**
     * @return Long return the purchase_success_total
     */
    public Long getPurchase_success_total() {
        return purchase_success_total;
    }

    /**
     * @param purchase_success_total the purchase_success_total to set
     */
    public void setPurchase_success_total(Long purchase_success_total) {
        this.purchase_success_total = purchase_success_total;
    }

    /**
     * @return Long return the purchase_failed_amount
     */
    public Long getPurchase_failed_amount() {
        return purchase_failed_amount;
    }

    /**
     * @param purchase_failed_amount the purchase_failed_amount to set
     */
    public void setPurchase_failed_amount(Long purchase_failed_amount) {
        this.purchase_failed_amount = purchase_failed_amount;
    }

    /**
     * @return Long return the purchase_failed_total
     */
    public Long getPurchase_failed_total() {
        return purchase_failed_total;
    }

    /**
     * @param purchase_failed_total the purchase_failed_total to set
     */
    public void setPurchase_failed_total(Long purchase_failed_total) {
        this.purchase_failed_total = purchase_failed_total;
    }

    /**
     * @return Long return the refund_success_amount
     */
    public Long getRefund_success_amount() {
        return refund_success_amount;
    }

    /**
     * @param refund_success_amount the refund_success_amount to set
     */
    public void setRefund_success_amount(Long refund_success_amount) {
        this.refund_success_amount = refund_success_amount;
    }

    /**
     * @return Long return the refund_success_total
     */
    public Long getRefund_success_total() {
        return refund_success_total;
    }

    /**
     * @param refund_success_total the refund_success_total to set
     */
    public void setRefund_success_total(Long refund_success_total) {
        this.refund_success_total = refund_success_total;
    }

    /**
     * @return Long return the refund_failed_amount
     */
    public Long getRefund_failed_amount() {
        return refund_failed_amount;
    }

    /**
     * @param refund_failed_amount the refund_failed_amount to set
     */
    public void setRefund_failed_amount(Long refund_failed_amount) {
        this.refund_failed_amount = refund_failed_amount;
    }

    /**
     * @return Long return the refund_failed_total
     */
    public Long getRefund_failed_total() {
        return refund_failed_total;
    }

    /**
     * @param refund_failed_total the refund_failed_total to set
     */
    public void setRefund_failed_total(Long refund_failed_total) {
        this.refund_failed_total = refund_failed_total;
    }

    /**
     * @return Long return the refund_wfo_approval_amount
     */
    public Long getRefund_wfo_approval_amount() {
        return refund_wfo_approval_amount;
    }

    /**
     * @param refund_wfo_approval_amount the refund_wfo_approval_amount to set
     */
    public void setRefund_wfo_approval_amount(Long refund_wfo_approval_amount) {
        this.refund_wfo_approval_amount = refund_wfo_approval_amount;
    }

    /**
     * @return Long return the refund_wfo_approval_total
     */
    public Long getRefund_wfo_approval_total() {
        return refund_wfo_approval_total;
    }

    /**
     * @param refund_wfo_approval_total the refund_wfo_approval_total to set
     */
    public void setRefund_wfo_approval_total(Long refund_wfo_approval_total) {
        this.refund_wfo_approval_total = refund_wfo_approval_total;
    }

    /**
     * @return Long return the refund_wfb_approval_amount
     */
    public Long getRefund_wfb_approval_amount() {
        return refund_wfb_approval_amount;
    }

    /**
     * @param refund_wfb_approval_amount the refund_wfb_approval_amount to set
     */
    public void setRefund_wfb_approval_amount(Long refund_wfb_approval_amount) {
        this.refund_wfb_approval_amount = refund_wfb_approval_amount;
    }

    /**
     * @return Long return the refund_wfb_approval_total
     */
    public Long getRefund_wfb_approval_total() {
        return refund_wfb_approval_total;
    }

    /**
     * @param refund_wfb_approval_total the refund_wfb_approval_total to set
     */
    public void setRefund_wfb_approval_total(Long refund_wfb_approval_total) {
        this.refund_wfb_approval_total = refund_wfb_approval_total;
    }

}
