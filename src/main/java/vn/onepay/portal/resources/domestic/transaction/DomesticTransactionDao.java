package vn.onepay.portal.resources.domestic.transaction;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.domestic.transaction.dto.*;
import vn.onepay.portal.resources.international.transaction.dto.CardDate;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticTransactionDao extends Db {

    private static final String SEARCH_TRANSACTION = "{call PKG_DOMESTIC_CARD.search_transaction_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String SEARCH_TRANSACTION_118 = "{call PKG_RP_DOMESTIC_TXN.search_transaction6(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_GET_BY_ID = "{call PKG_DOMESTIC_CARD.GET_TRANSACTION_ORDER_V2(?,?,?,?)}";
    private static final String UPDATE_PURCHASE_STATUS_BY_ID = "{call PKG_DOMESTIC_CARD.UPDATE_PURCHASE_STATUS_BY_ID(?,?,?,?)}";

    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_DOMESTIC_REFUND.get_approval_history(?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY_ONLINE = "{call PKG_DOMESTIC_CARD.GET_TRANSACTION_HIS_111(?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY_READ_ONLY = "{call PKG_DOMESTIC_CARD.GET_TRANSACTION_HIS_113(?,?,?,?)}";

    private static final String LIST_REQUEST_REFUND_HISTORY_BACKUP_118 = "{call PKG_RP_DOMESTIC_TXN.get_rr_history_118(?,?,?,?)}";

    private static final String LIST_TRANSACTION_ONLINE_BY_IDS = "{call PKG_DOMESTIC_CARD.GET_TRANSACTION_BY_IDS_2(?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_BY_MERCHANT_REF = "{call PKG_DOMESTIC_CARD.SEARCH_REFUND_BY_REF(?,?,?,?,?,?) }";

    private static final Logger logger = Logger.getLogger(DomesticTransactionDao.class.getName());

    public static BaseList<DomesticPurchase> search(Map<String, String> mIn) throws Exception {
        BaseList<DomesticPurchase> result = new BaseList<>();
        // get Total
        Integer totalOnline = getTotalTrans(mIn);
        result.setTotalItems(totalOnline);

        // get Transaction List
        List<DomesticPurchase> list = new ArrayList<>();
        List<DomesticPurchase> onlineList = searchTransaction(mIn, 0);
        list.addAll(onlineList); // add online List
        result.setList(list);

        return result;
    }

    public static Integer getTotalTransaction(Map mIn) throws Exception {
        // get Total
        return getTotalTrans(mIn);
    }

    public static Integer getTotalTrans(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION_118);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setObject(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setObject(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setObject(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setObject(8, mIn.get(FROM_DATE).toString());
            cs.setObject(9, mIn.get(TO_DATE).toString());
            cs.setObject(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setObject(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setObject(12, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setObject(13, mIn.getOrDefault(STATUS, ""));
            cs.setObject(14, mIn.getOrDefault(CAIC, ""));
            cs.setObject(15, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setObject(16, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setObject(17, mIn.getOrDefault(PLATFORM, ""));
            cs.setObject(18, mIn.getOrDefault(PAGE, "0"));
            cs.setObject(19, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setObject(20, mIn.getOrDefault(BANK_TRANSACTION_ID, ""));
            cs.setObject(21, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<DomesticPurchase> searchTransaction(Map<String, String> mIn, int row) throws Exception {
        Exception exception = null;
        List<DomesticPurchase> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION_118);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setObject(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setObject(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setObject(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setObject(8, mIn.get(FROM_DATE).toString());
            cs.setObject(9, mIn.get(TO_DATE).toString());
            cs.setObject(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setObject(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setObject(12, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setObject(13, mIn.getOrDefault(STATUS, ""));
            cs.setObject(14, mIn.getOrDefault(CAIC, ""));
            cs.setObject(15, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setObject(16, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setObject(17, mIn.getOrDefault(PLATFORM, ""));
            cs.setObject(18, mIn.getOrDefault(PAGE, "0"));
            cs.setObject(19, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setObject(20, mIn.getOrDefault(BANK_TRANSACTION_ID, ""));
            cs.setObject(21, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindPurchase(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static DomesticPurchase getTransaction(String id) throws Exception {
        Exception exception = null;
        DomesticPurchase result = new DomesticPurchase();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(TRANSACTION_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindPurchase(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static DomesticPurchase updatePurchaseStatus(String id, String status) throws Exception {
        Exception exception = null;
        DomesticPurchase result = new DomesticPurchase();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_PURCHASE_STATUS_BY_ID);
            cs.setInt(3, Convert.parseInt(id, 0));
            cs.setInt(4, Convert.parseInt(status, 0));
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB update domestic purchase by id error: " + error);
            }
            cs.close();
            cs = con.prepareCall(TRANSACTION_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic purchase by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindPurchase(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static DomesticPurchase getPurchase(String id) throws Exception {
        Exception exception = null;
        DomesticPurchase result = new DomesticPurchase();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(TRANSACTION_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic purchase by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindPurchase(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<DomesticTransactionHistory> listHistory(String id) throws Exception {
        List<DomesticTransactionHistory> transactionsFinal = new ArrayList<>();

        List<DomesticTransactionHistory> onlineHistory = getHistory(LIST_TRANSACTION_HISTORY_ONLINE, id, -1);
        List<DomesticTransactionHistory> readonlyHistory = getHistory(LIST_TRANSACTION_HISTORY_READ_ONLY, id, onlineHistory.size());
        // List<DomesticTransactionHistory> backupHistory = getHistoryBackup(id);
        List<DomesticTransactionHistory> listRequestRefundBackup18 = getRefundHistoryBackup118(id);

        transactionsFinal.addAll(onlineHistory);
        transactionsFinal.addAll(readonlyHistory);
        // transactionsFinal.addAll(backupHistory);
        transactionsFinal.addAll(listRequestRefundBackup18);

        // sort by trans time.
        transactionsFinal
                .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

        return transactionsFinal;

    }

    private static List<DomesticTransactionHistory> getHistory(String strPrpareCall, String id, int row) throws Exception {
        Exception exception = null;
        List<DomesticTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = row >= 0 ? getConnection113() : getConnection112();
            cs = con.prepareCall(strPrpareCall);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic history by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindTransactionLogHistory(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<DomesticTransactionHistory> getHistoryBackup(String id) throws Exception {
        Exception exception = null;
        List<DomesticTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_REFUND_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);


            if (nError != 200) {
                throw new Exception("DB get domestic history backup by id error: " + error);
            } else {
                List<DomesticTransactionHistory> childList = new ArrayList<>();
                List<DomesticTransactionHistory> parentList = new ArrayList<>();
                while (rs != null && rs.next()) {
                    DomesticTransactionHistory transaction = bindApprovalHistory(rs);

                    if (transaction.getParent_id() == null) {
                        parentList.add(transaction);
                    } else {
                        childList.add(transaction);
                    }
                }

                result = parentList.stream().map(parent -> {
                    DomesticTransactionHistory re = new DomesticTransactionHistory(parent);

                    return re;
                })
                        .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<DomesticTransactionHistory> getRefundHistoryBackup118(String id) throws Exception {
        Exception exception = null;
        List<DomesticTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_REQUEST_REFUND_HISTORY_BACKUP_118);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get domestic history by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindTransactionLogHistory(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<Integer, DomesticPurchase> mapByIds(String transIds, Map mIn) throws Exception {
        List<DomesticPurchase> domesticTransactions = listByIds(transIds, mIn);
        Map<Integer, DomesticPurchase> map = new HashMap<>();
        for (DomesticPurchase dt : domesticTransactions) {
            map.put(dt.getTransaction_id(), dt);
        }
        return map;
    }

    public static List<DomesticPurchase> listByIds(String ids, Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<DomesticPurchase> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(LIST_TRANSACTION_ONLINE_BY_IDS);
            logger.log(Level.INFO, "query condition: " + mIn);
            cs.setString(1, mIn.getOrDefault(KEYWORD, ""));
            cs.setString(2, ids);
            cs.setString(3, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setString(4, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(5, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setString(6, mIn.getOrDefault(CAIC, ""));
            cs.setString(7, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(8, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(9, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(10, mIn.getOrDefault(TO_DATE, ""));
            cs.registerOutParameter(11, OracleTypes.CURSOR);
            cs.registerOutParameter(12, OracleTypes.NUMBER);
            cs.registerOutParameter(13, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(13);
            int nError = cs.getInt(12);
            rs = (ResultSet) cs.getObject(11);
            if (nError != 200) {
                throw new Exception("DB get domestic by ids error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindPurchase(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static DomesticPurchase bindPurchase(ResultSet rs) throws SQLException {

        int transactionId = Util.getColumnInteger(rs, "N_TRANSACTION_ID");
        String transactionType = Util.getColumnString(rs, "S_TRANSACTION_TYPE");
        int acquirerId = Util.getColumnInteger(rs, "N_ACQUIRER_ID");
        String acquirerName = Util.getColumnString(rs, "S_ACQUIRER_NAME");
        String acquirerShortName = Util.getColumnString(rs, "S_ACQUIRER_SHORT_NAME");
        String merchantId = Util.getColumnString(rs, "S_MERCHANT_ID");
        String cardDate = Util.getColumnString(rs, "S_CARD_DATE");
        String cardHolderName = Util.getColumnString(rs, "S_CARD_HOLDER_NAME");
        String cardNumber = Util.getColumnString(rs, "S_CARD_NUMBER");
        String merchantTransactionRef = Util.getColumnString(rs, "S_MERCHANT_TRANSACTION_REF");
        String bankTransactionId = Util.getColumnString(rs,"S_BANK_TRANSACTION_ID");
        
        Timestamp merchantTransactionDate = java.sql.Timestamp.valueOf(Util.getColumnString(rs, "D_MERCHANT_TRANSACTION_DATE"));

        String orderInfo = Util.getColumnString(rs, "S_ORDER_INFO");
        double amount = Util.getColumnDouble(rs, "N_AMOUNT");
        Double originalAmount = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String currency = Util.getColumnString(rs, "S_CURRENCY_CODE");
        String status = String.valueOf(Util.getColumnInteger(rs, "N_TRANSACTION_STATUS"));
        String refundStatus = String.valueOf(Util.getColumnInteger(rs, "N_REFUND_TRANSACTION_STATUS"));
        String cardVerificationCode = String.valueOf(Util.getColumnInteger(rs, "N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = Util.getColumnString(rs, "S_CARD_VERIFICATION_INFO");
        String ipAddress = Util.getColumnString(rs, "S_IP");
        double refundAmount = Util.getColumnDouble(rs, "N_REFUND_AMOUNT");
        String advanceStatus = Util.getColumnString(rs, "S_ADVANCE_STATUS");
        String platform = Util.getColumnString(rs, "S_PLATFORM");
        String source = Util.getColumnString(rs, "S_SOURCE");
        DomesticPurchase transaction = new DomesticPurchase();
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(merchantTransactionDate);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ipAddress);
        transaction.setOrder_info(orderInfo);
        transaction.setMerchant_transaction_ref(merchantTransactionRef);
        transaction.setStatus(status);
        transaction.setRefund_status(refundStatus);
        transaction.setMerchant_id(merchantId);
        transaction.setAdvance_status(advanceStatus);
        transaction.setPlatform(platform);
        transaction.setSource(source);
        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);

        transaction.setAcquirer(acquirer);

        DomesticAmount amountData = new DomesticAmount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);
        amountData.setRefund_total(refundAmount);

        transaction.setAmount(amountData);
        transaction.setTransaction_info(Util.getColumnString(rs, "S_TRANSACTION_INFO"));

        DomesticAmount originalAmountData = new DomesticAmount();
        originalAmountData.setCurrency(currency);
        originalAmountData.setTotal(originalAmount);
        transaction.setOriginalAmount(originalAmountData);
        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }

        transaction.setAuth_time(Util.getColumnString(rs, "D_USER_AUTHENTICATION_DATE") == null ? null : java.sql.Timestamp.valueOf(Util.getColumnString(rs, "D_USER_AUTHENTICATION_DATE")));

        DomesticAuthorisation authorisation = new DomesticAuthorisation();
        authorisation.setAuthorisation_code(Util.getColumnInteger(rs, "N_USER_AUTHENTICATION_CODE"));
        authorisation.setAuthorisation_url(Util.getColumnString(rs, "S_USER_AUTHENTICATION_URl"));
        authorisation.setAuthorisation_info(Util.getColumnString(rs, "S_USER_AUTHENTICATION_INFO"));
        authorisation.setAuthorisation_return_url(Util.getColumnString(rs, "S_RETURN_AUTHENTICATION_URl"));
        authorisation.setAuthorisation_time(Util.getColumnTimeStamp(rs, "D_USER_AUTHENTICATION_DATE"));
        transaction.setAuthorisation(authorisation);

        transaction.setCaic(Util.getColumnString(rs, "S_CAIC"));
        transaction.setContract_type(Util.getColumnString(rs, "S_CONTRACT_TYPE"));
        transaction.setAcquirer_bank(Util.getColumnString(rs, "S_ACQUIER"));
        transaction.setBank_transaction_id(bankTransactionId);
        // String reg = Config.getString("domestic.bank_txn_id_reg", "");
        // Pattern p = Pattern.compile(reg);
        // Matcher m = p.matcher(transaction.getAuthorisation().getAuthorisation_info()== null ? "" : transaction.getAuthorisation().getAuthorisation_info());
        // if (null != transaction.getAuthorisation().getAuthorisation_info() && m.find()) {
        //     transaction.setBank_transaction_id(m.group(1));
        // } else {
        //     transaction.setBank_transaction_id("");
        // }

        /*
         Special case with response code
         Case status is 200 and failed, response code is 100
         the response code will be change with specific bank requirements
         */

        if (null != transaction.getStatus() && transaction.getStatus().equals("200") && null != transaction.getAdvance_status() && transaction.getAdvance_status().equals("Failed") && cardVerificationCode.equals("100")) {

            Integer authCode = rs.getInt("N_USER_AUTHENTICATION_CODE");
            String specialResponseCode = Config.getString("txncode." + acquirerId + "." + authCode, "1"); // default is 1
            card.setCard_verification_code(specialResponseCode.length() == 0 ? "1" : specialResponseCode);
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);
        return transaction;
    }

    private static DomesticTransactionHistory bindTransactionLogHistory(ResultSet rs) throws SQLException {

        String advanced_status = Util.getColumnString(rs, "S_ADVANCE_STATUS");
        double amount = Util.getColumnDouble(rs, "N_AMOUNT");
        double originalAmount = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String currency = Util.getColumnString(rs, "S_CURRENCY_CODE");
        String merchant_transaction_ref = Util.getColumnString(rs, "S_MERCHANT_TRANSACTION_REF");
        String operator_id = Util.getColumnString(rs, "s_operator_id");
        String original_id = Util.getColumnString(rs, "n_original_id");
        String status = Util.getColumnString(rs, "n_transaction_status");
        String transaction_id = Util.getColumnString(rs, "N_TRANSACTION_ID");
        Timestamp transaction_time = Util.getColumnTimeStamp(rs, "D_LOG_DATE");
        String transaction_type = Util.getColumnString(rs, "s_transaction_type");
        DomesticTransactionHistory bindTransactionLogHistory = new DomesticTransactionHistory();
        bindTransactionLogHistory.setTransaction_type(transaction_type);
        bindTransactionLogHistory.setTransaction_time(transaction_time);
        bindTransactionLogHistory.setTransaction_id(transaction_id);
        bindTransactionLogHistory.setTotal(amount);
        bindTransactionLogHistory.setOriginalAmount(originalAmount);
        bindTransactionLogHistory.setStatus(status);
        bindTransactionLogHistory.setRefund_total(amount);
        bindTransactionLogHistory.setOriginal_id(original_id);
        bindTransactionLogHistory.setOperator_id(operator_id);
        bindTransactionLogHistory.setMerchant_transaction_ref(merchant_transaction_ref);
        bindTransactionLogHistory.setTransaction_id(transaction_id);
        bindTransactionLogHistory.setCurrency(currency);
        bindTransactionLogHistory.setAdvanced_status(advanced_status);
        bindTransactionLogHistory.setDesc(Util.getColumnString(rs, "s_description"));

        return bindTransactionLogHistory;
    }

    private static DomesticTransactionHistory bindApprovalHistory(ResultSet rs) throws SQLException {

        String advanced_status = Util.getColumnString(rs, "S_ADVANCE_STATUS");
        String currency = Util.getColumnString(rs, "S_CURRENCY");
        double refund_total = Util.getColumnDouble(rs, "N_AMOUNT");
        double total = Util.getColumnDouble(rs, "N_AMOUNT");
        double originalAmount = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String merchant_transaction_ref = Util.getColumnString(rs, "S_MERCHANT_TRANS_REF");;
        String operator_id = Util.getColumnString(rs, "S_OPERATOR_ID");
        String original_id = Util.getColumnString(rs, "N_TRANS_REF_ID");
        String parent_id = Util.getColumnString(rs, "N_PARENT_ID");;
        String status = Util.getColumnString(rs, "N_STATUS");
        String transaction_id = Util.getColumnString(rs, "N_ID");
        Timestamp transaction_time = Util.getColumnString(rs, "D_CREATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_CREATE"));
        String transaction_type = Util.getColumnString(rs, "S_TRANSACTION_TYPE");
        DomesticTransactionHistory transactionHistory = new DomesticTransactionHistory();
        transactionHistory.setAdvanced_status(advanced_status);
        transactionHistory.setCurrency(currency);
        transactionHistory.setMerchant_transaction_ref(merchant_transaction_ref);
        transactionHistory.setOperator_id(operator_id);
        transactionHistory.setOriginal_id(original_id);
        transactionHistory.setParent_id(parent_id);
        transactionHistory.setRefund_total(refund_total);
        transactionHistory.setTotal(total);
        transactionHistory.setOriginalAmount(originalAmount);
        transactionHistory.setStatus(status);
        transactionHistory.setTransaction_id(transaction_id);
        transactionHistory.setTransaction_time(transaction_time);
        transactionHistory.setTransaction_type(transaction_type);
        transactionHistory.setDesc(Util.getColumnString(rs, "S_DESCRIPTION"));
        return transactionHistory;
    }
}
