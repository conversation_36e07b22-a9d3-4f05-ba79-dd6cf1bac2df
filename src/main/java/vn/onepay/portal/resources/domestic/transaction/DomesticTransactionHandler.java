package vn.onepay.portal.resources.domestic.transaction;

import com.google.gson.Gson;
import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.*;
import vn.onepay.portal.client.MaClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.client.WSClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticTransactionHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(DomesticTransactionHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void searchDomesticTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = mIn.get(FROM_DATE);
                String toDate = mIn.get(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL  GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                if (FunctionUtil.isCardData(mIn.get(CARD_NUMBER))) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARD_NUMBER));
                    mIn.put(CARD_NUMBER, cardhash);
                }

                JsonObject jResponse = domesticTxnSync(new JsonObject(gson.toJson(DomesticTransactionDao.search(mIn))), "2m");
                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static JsonObject domesticTxnSync(JsonObject jSearch, String timeSkip) {
        JsonObject jResult = new JsonObject();
        jResult.mergeIn(jSearch);
        JsonObject jSycn = null;
        try {

            String jobId = "20017";
            jSycn = OneSchedClient.synchronize2(jobId, timeSkip);
        } catch (Exception e) {
            logger.log(Level.WARNING, "DOMESTIC SYNC ERROR: ", e);
        }
        if (jSycn != null) {
            jResult.mergeIn(jSycn);
        }
        return jResult;
    }

    public static void getDomesticTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, DomesticTransactionDao.getTransaction(id));
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateDomesticTransactionStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String pattern = "###,###";
                DecimalFormat decimalFormat = new DecimalFormat(pattern);
                HttpServerRequest request = ctx.request();
                String transactionId = request.getParam("transaction_id");
                String status = request.getParam(RESPONSE);
                String merchantId = request.getParam("merchant_id");
                String transactionNumber = request.getParam("merchant_transaction_ref");
                String previousStatus = request.getParam("previous_status");
                String orderInfo = request.getParam("order_info");
                String amount = request.getParam("amount");
                String currency = request.getParam("currency");
                String transactionDate = request.getParam("transaction_date");
                StringBuilder subject = new StringBuilder("CẢNH BÁO THAY ĐỔI TRẠNG THÁI GIAO DỊCH NỘI ĐỊA ").append(transactionId);
                String host = Config.getString("email.host", "");
                Integer port = Config.getInteger("email.port", 25);
                String username = Config.getString("email.username", "");
                String password = Config.getString("email.password", "");
                String fromEmail = Config.getString("email.address", "");
                StringBuilder body = new StringBuilder("Dear Payment Team,<br>");
                body.append("Giao dịch nội địa sau đây đã được thay đổi trạng thái:<br>");
                body.append("Merchant ID: ").append(merchantId).append("<br>");
                body.append("Transaction ID: ").append(transactionId).append("<br>");
                body.append("Merchant Trans. Ref: ").append(transactionNumber).append("<br>");
                body.append("Order Info: ").append(orderInfo).append("<br>");
                body.append("Transaction Type: Purchase<br>");
                body.append("Amount: ").append(decimalFormat.format(Double.valueOf(amount))).append(" ").append(currency).append("<br>");
                body.append("Transaction Date: ").append(transactionDate).append("<br>");
                body.append("Status: ").append(previousStatus).append(" to ").append(status).append("<br>");
                String toEmail = "<EMAIL>";
                MailUtil.sendMail(host, port, username, password, fromEmail, toEmail, toEmail, subject.toString(), body.toString());
                JsonObject jUpdate = new JsonObject(gson.toJson(DomesticTransactionDao.updatePurchaseStatus(transactionId, status)));
                JsonObject jResponse = domesticTxnSync(jUpdate, "0m");
                sendResponse(ctx, 200, jResponse);
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void query(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC Transaction  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String ip = ctx.request().headers().get("X-Forwarded-For");
                if (ip == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC Transaction  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = ctx.request().getParam("id");
                JsonObject jQuery = new JsonObject(gson.toJson(MaClient.patchQueryDomesticTransaction(bodyJson.getString("merchant_id"), bodyJson.getString("currency_code"), transactionId, userId, xRequestId, ip)));
                JsonObject jResponse = domesticTxnSync(jQuery, "0m");
                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "Query Domestic Transaction: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDomesticPurchase(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, DomesticTransactionDao.getPurchase(id));
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDomesticTransactionHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {

                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, new BaseList<>(DomesticTransactionDao.listHistory(id)));
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadDomesticTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));


                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = bodyJson.getString(FROM_DATE);
                String toDate = bodyJson.getString(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL  GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                bodyJson.put(FROM_DATE, fromDate);
                bodyJson.put(TO_DATE, toDate);
                bodyJson.put(PAGE, 0);
                bodyJson.put(PAGE_SIZE, Integer.MAX_VALUE);
                if (FunctionUtil.isCardData(bodyJson.getString(CARD_NUMBER))) {
                    String cardhash = FunctionUtil.oneSMHmac(bodyJson.getString(CARD_NUMBER));
                    bodyJson.put(CARD_NUMBER, cardhash);
                }

                int totalRows = DomesticTransactionDao.getTotalTransaction(bodyJson.getMap());
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "domestic_transaction_" + date;
                String fileHashName = "";
                // Map<String, Object> data = new HashMap<>();
                // data.put("parameter", bodyJson.getMap());
                // data.put("file_name", fileName);
                // data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("domestic_transaction");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(bodyJson.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD DOMESTIC TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
