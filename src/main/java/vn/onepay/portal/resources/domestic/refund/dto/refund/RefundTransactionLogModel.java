package vn.onepay.portal.resources.domestic.refund.dto.refund;

import java.sql.Timestamp;

public class RefundTransactionLogModel {
    private int acquirer_id;
    private String acquirer_name;
    private String acquirer_short_name;
    private String merchant_id;
    private int transaction_id;
    private String merchant_transaction_ref;
    private double amount;
    private int original_id;
    private String currency_code;
    private int transaction_status;
    private String order_infor;
    private Timestamp merchant_transaction_date;
    private double purchase_amount;
    private Timestamp purchase_date;
    private String transaction_type;
    private String card_number;
    private String card_type;
    private String card_date_month;
    private String card_date_year;
    private String card_holder_name;
    private Integer card_verification_code;
    private String card_verification_info;
    private String transaction_infor;
    private String return_authentication_url;
    private String operator_id;

    public int getAcquirer_id() {
        return acquirer_id;
    }

    public void setAcquirer_id(int acquirer_id) {
        this.acquirer_id = acquirer_id;
    }

    public String getAcquirer_name() {
        return acquirer_name;
    }

    public void setAcquirer_name(String acquirer_name) {
        this.acquirer_name = acquirer_name;
    }

    public String getAcquirer_short_name() {
        return acquirer_short_name;
    }

    public void setAcquirer_short_name(String acquirer_short_name) {
        this.acquirer_short_name = acquirer_short_name;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public int getOriginal_id() {
        return original_id;
    }

    public void setOriginal_id(int original_id) {
        this.original_id = original_id;
    }

    public String getCurrency_code() {
        return currency_code;
    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
    }

    public int getTransaction_status() {
        return transaction_status;
    }

    public void setTransaction_status(int transaction_status) {
        this.transaction_status = transaction_status;
    }

    public String getOrder_infor() {
        return order_infor;
    }

    public void setOrder_infor(String order_infor) {
        this.order_infor = order_infor;
    }

    public Timestamp getMerchant_transaction_date() {
        return merchant_transaction_date;
    }

    public void setMerchant_transaction_date(Timestamp merchant_transaction_date) {
        this.merchant_transaction_date = merchant_transaction_date;
    }

    public double getPurchase_amount() {
        return purchase_amount;
    }

    public void setPurchase_amount(double purchase_amount) {
        this.purchase_amount = purchase_amount;
    }

    public Timestamp getPurchase_date() {
        return purchase_date;
    }

    public void setPurchase_date(Timestamp purchase_date) {
        this.purchase_date = purchase_date;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getCard_date_month() {
        return card_date_month;
    }

    public void setCard_date_month(String card_date_month) {
        this.card_date_month = card_date_month;
    }

    public String getCard_date_year() {
        return card_date_year;
    }

    public void setCard_date_year(String card_date_year) {
        this.card_date_year = card_date_year;
    }

    public String getCard_holder_name() {
        return card_holder_name;
    }

    public void setCard_holder_name(String card_holder_name) {
        this.card_holder_name = card_holder_name;
    }

    public Integer getCard_verification_code() {
        return card_verification_code;
    }

    public void setCard_verification_code(Integer card_verification_code) {
        this.card_verification_code = card_verification_code;
    }

    public String getCard_verification_info() {
        return card_verification_info;
    }

    public void setCard_verification_info(String card_verification_info) {
        this.card_verification_info = card_verification_info;
    }

    public String getTransaction_infor() {
        return transaction_infor;
    }

    public void setTransaction_infor(String transaction_infor) {
        this.transaction_infor = transaction_infor;
    }

    public String getReturn_authentication_url() {
        return return_authentication_url;
    }

    public void setReturn_authentication_url(String return_authentication_url) {
        this.return_authentication_url = return_authentication_url;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }
}
