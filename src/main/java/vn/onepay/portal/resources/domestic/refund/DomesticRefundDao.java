package vn.onepay.portal.resources.domestic.refund;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.domestic.refund.dto.DomesticRefund;
import vn.onepay.portal.resources.domestic.refund.dto.RefundAmount;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;
import vn.onepay.portal.resources.domestic.transaction.dto.Acquirer;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticCard;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticPurchase;
import vn.onepay.portal.resources.international.transaction.dto.CardDate;
import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DomesticRefundDao extends Db {
    private static final String LIST_TRANSACTION_REFUND = "{call PKG_DOMESTIC_CARD.search_refund_2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_REFUND_118 = "{call PKG_RP_DOMESTIC_TXN.search_refund_8(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_TRANSACTION_REFUND = "{call PKG_DOMESTIC_CARD.get_refund2(?,?,?,?)}";
    private static final String UPDATE_REFUND_STATUS_BY_ID = "{call PKG_DOMESTIC_CARD.UPDATE_REFUND_STATUS_BY_ID(?,?,?,?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(DomesticRefundDao.class.getName());

    public static BaseList<DomesticRefund> search(Map<String, String> mIn) throws Exception {
        BaseList<DomesticRefund> result = new BaseList<>();

        Integer totalRefundOnline = searchTotalRefund(mIn);
        LOGGER.log(Level.WARNING, "------------------START GET Domestic refund ONLINE----------------- ");
        List<DomesticRefund> listOnline = searchRefund(mIn, 1);
        LOGGER.log(Level.WARNING, "------------------END GET Domestic refund ONLINE----------------- ");
        mIn.put(OFFSET, String.valueOf(totalRefundOnline));

        List<DomesticRefund> transactionsFinal = new ArrayList<>();
        transactionsFinal.addAll(listOnline);
        LOGGER.log(Level.INFO, ">> ACTUAL_TOTAL_DOMESTIC_REFUND_ONLINE: " + listOnline.size());

        result.setList(transactionsFinal);
        result.setTotalItems(totalRefundOnline);

        return result;
    }


    public static Integer total(Map mIn) throws Exception {
        LOGGER.log(Level.WARNING, "------------------START GET TOTAL Domestic Refund Online----------------- ");
        Integer totalRefundOnline = searchTotalRefund(mIn);
        LOGGER.log(Level.WARNING, "------------------END  GET TOTAL Domestic Refund Online " + totalRefundOnline + "----------------- ");

        LOGGER.log(Level.INFO, ">> TOTAL_DOMESTIC_REFUND_ONLINE: " + totalRefundOnline);
        return totalRefundOnline;
    }

    public static DomesticRefund get(Integer id) throws Exception {
        DomesticRefund refund = getById(id);
        if (refund == null) {
            refund = DomesticRefundApprovalDao.getTransactionById(id);
            DomesticPurchase purchase = DomesticTransactionDao.getPurchase(refund.getOriginal_id().toString());
            joinApproveWithPurchase(refund, purchase);
        }
        return refund;
    }


    private static DomesticRefund joinApproveWithPurchase(DomesticRefund approve, DomesticPurchase purchase) {


        if (purchase == null) {
            return null;
        }

        DomesticRefund result = approve;

        result.setTransaction_purchase_time(purchase.getTransaction_time());
        result.setTransaction_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());
        result.setCaic(purchase.getCaic());
        result.setContract_type(purchase.getContract_type());

        result.setCard(purchase.getCard());
        result.getAmount().setPurchase_total(purchase.getAmount().getTotal());
        result.setIp_address(purchase.getIp_address());

        return result;
    }

    private static Integer countFilterById(Map<Integer, DomesticPurchase> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }


    private static DomesticRefund getById(Integer id) throws Exception {
        Exception exception = null;
        DomesticRefund result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_TRANSACTION_REFUND);

            cs.setInt(1, id);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                throw new Exception("DB get get domestic refund by id error: " + error);
            } else {

                while (rs != null && rs.next()) {
                    result = bind(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static DomesticRefund updateRefundStatus(String id, String originalId, String refundAmount, String status, String previousStatus) throws Exception {
        Exception exception = null;
        DomesticRefund result = new DomesticRefund();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_REFUND_STATUS_BY_ID);
            cs.setInt(3, Convert.parseInt(id, 0));
            cs.setInt(4, Convert.parseInt(status, 0));
            cs.setBigDecimal(5, new BigDecimal(refundAmount));
            cs.setInt(6, Convert.parseInt(originalId, 0));
            cs.setInt(7, Convert.parseInt(previousStatus, 0));
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB update domestic refund by id error: " + error);
            }
            cs.close();
            cs = con.prepareCall(GET_TRANSACTION_REFUND);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.setInt(1, Convert.parseInt(id, 0));
            cs.execute();
            error = cs.getString(4);
            nError = cs.getInt(3);
            rs = (ResultSet) cs.getObject(2);
            if (nError != 200) {
                throw new Exception("DB get domestic refund by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bind(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<DomesticRefund> searchRefund(Map<String, String> mIn, Integer rows) throws Exception {
        Exception exception = null;
        List<DomesticRefund> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_TRANSACTION_REFUND_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setString(12, mIn.getOrDefault(STATUS, "0"));
            cs.setString(13, mIn.getOrDefault(PAGE, "0"));
            cs.setString(14, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setString(15, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setString(16, mIn.getOrDefault(CAIC, ""));
            cs.setString(17, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(18, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(19, mIn.getOrDefault(PLATFORM, ""));
            cs.setString(20, mIn.getOrDefault(REFUND_BANK_TRANSACTION_ID, ""));
            cs.setString(21, mIn.getOrDefault(TRANSACTION_TYPE, ""));
            cs.setInt(22, Integer.parseInt(mIn.getOrDefault("confirmable", "0") )) ;
            cs.setString(23, mIn.getOrDefault(REFUND_TYPE, ""));
            cs.setString(24, mIn.getOrDefault(SOURCE, ""));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bind(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    private static Integer searchTotalRefund(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_TRANSACTION_REFUND_118);

            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(6, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(7, mIn.getOrDefault(TRANSACTION_ID, "0"));
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(MERCHANT_TRANSACTION_REF, ""));
            cs.setString(11, mIn.getOrDefault(ORDER_INFO, ""));
            cs.setString(12, mIn.getOrDefault(STATUS, "0"));
            cs.setString(13, mIn.getOrDefault(PAGE, "0"));
            cs.setString(14, mIn.getOrDefault(PAGE_SIZE, "0"));
            cs.setString(15, mIn.getOrDefault(CARD_NUMBER, ""));
            cs.setString(16, mIn.getOrDefault(CAIC, ""));
            cs.setString(17, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(18, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(19, mIn.getOrDefault(PLATFORM, ""));
            cs.setString(20, mIn.getOrDefault(REFUND_BANK_TRANSACTION_ID, ""));
            cs.setString(21, mIn.getOrDefault(TRANSACTION_TYPE, ""));
            cs.setInt(22, Integer.parseInt(mIn.getOrDefault("confirmable", "0") )) ;
            cs.setString(23, mIn.getOrDefault(REFUND_TYPE, ""));
            cs.setString(24, mIn.getOrDefault(SOURCE, ""));

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search domestic refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    /**
     * convert data from result set to transaction
     *
     * @param rs
     * @return
     * @throws SQLException
     */
    private static DomesticRefund bind(ResultSet rs) throws Exception {
        String merchantId = rs.getString("S_MERCHANT_ID");
        String orderInfo = rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");
        String acquirerName = rs.getString("S_ACQUIRER_NAME");
        String acquirerShortName = rs.getString("S_ACQUIRER_SHORT_NAME");

        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInt("N_TRANSACTION_ID");
        int originalId = rs.getInt("N_ORIGINAL_ID");
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        Timestamp purchaseDate = Timestamp.valueOf(rs.getString("D_PURCHASE_DATE"));

        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        int status = rs.getInt("N_TRANSACTION_STATUS");
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        String ip = rs.getString("S_IP");
        double total = rs.getDouble("N_AMOUNT");
        double purchaseTotal = rs.getDouble("N_PURCHASE_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");

        String cardDate = rs.getString("S_CARD_DATE");
        String cardHolderName = rs.getString("S_CARD_HOLDER_NAME");
        String cardNumber = rs.getString("S_CARD_NUMBER");
        String cardVerificationCode = String.valueOf(rs.getInt("N_CARD_VERIFICATION_CODE"));
        String cardVerificationInfo = rs.getString("S_CARD_VERIFICATION_INFO");

        /** Comment round 3 */
        String transactionInfo = rs.getString("S_TRANSACTION_INFO");
        String authenticationUrl = rs.getString("S_RETURN_AUTHENTICATION_URL");
        String platform = Util.getColumnString(rs, "S_PLATFORM");
        String refundBankTransactionId = Util.getColumnString(rs, "s_refund_bank_transaction_id");
        String bankTransactionId = Util.getColumnString(rs, "s_bank_transaction_id");
        String source = Util.getColumnString(rs, "S_SOURCE");


        DomesticRefund transaction = new DomesticRefund();
        transaction.setMerchant_id(merchantId);
        transaction.setOrder_info(orderInfo);
        transaction.setStatus(status);
        transaction.setTransaction_time(date);
        transaction.setMerchant_transaction_ref(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_id(originalId);
        transaction.setIp_address(ip);
        transaction.setOperator_id(operatorId);
        transaction.setTransaction_purchase_time(purchaseDate);
        transaction.setAuthentication_url(authenticationUrl);
        transaction.setTransaction_info(transactionInfo);
        transaction.setTransaction_info(transactionInfo);
        transaction.setPlatform(platform);
        transaction.setS_refund_bank_transaction_id(refundBankTransactionId);
        transaction.setS_bank_transaction_id(bankTransactionId);
        transaction.setSource(source);
        
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        refundAmount.setPurchase_total(purchaseTotal);

        transaction.setAmount(refundAmount);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        acquirer.setAcquirer_name(acquirerName);
        acquirer.setAcquirer_short_name(acquirerShortName);


        transaction.setAcquirer(acquirer);

        DomesticCard card = new DomesticCard();
        card.setCard_number(cardNumber);
        card.setCard_verification_code(cardVerificationCode);
        card.setCard_holder_name(cardHolderName);
        card.setCard_verification_info(cardVerificationInfo);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setMonth(cardDate.substring(0, 2));
            cardDateData.setYear(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);
        transaction.setCard(card);

        transaction.setCaic(Util.getColumnString(rs, "S_CAIC"));
        transaction.setContract_type(Util.getColumnString(rs, "S_CONTRACT_TYPE"));
        transaction.setAcquirer_bank(Util.getColumnString(rs, "S_ACQUIER"));
        transaction.setConfirmable(Util.getColumnInteger(rs, "N_CONFIRMABLE"));
        transaction.setActionType(Util.getColumnString(rs, "S_ACTION_TYPE"));

        // Check trans_type của MB
        if (acquirerId == 8) {
            try {
                JsonObject jPayment2Data = null;
                String sPayment2Data = Util.getColumnString(rs, "s_payment2_data");
                if (sPayment2Data != null) {
                    jPayment2Data = new Gson().fromJson(sPayment2Data, JsonObject.class);
                }
                if (jPayment2Data != null && jPayment2Data.getAsJsonObject("mbbank_txn_info") != null) {
                    JsonElement mbTransType = jPayment2Data.getAsJsonObject("mbbank_txn_info").get("transaction_type");
                    if (mbTransType != null) {
                        transaction.setBank_trans_type(mbTransType.getAsString());
                    }
                }
            } catch (Exception e) {
                logger.warning("DomesticRefund bind error: " + e);
            }
        }

        return transaction;
    }

}
