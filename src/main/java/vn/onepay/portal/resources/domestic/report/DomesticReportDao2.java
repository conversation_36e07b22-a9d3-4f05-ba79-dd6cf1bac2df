package vn.onepay.portal.resources.domestic.report;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReport;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;

import java.sql.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DomesticReportDao2 extends Db implements IConstants {

    private static final String SEARCH_REPORT_DOMESTIC = "{call PKG_DOMESTIC_CARD.SEARCH_REPORTS_3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionDao.class.getName());

    public static Map search(Map<String, String> mIn) throws Exception {
        LOGGER.log(Level.INFO, "Domestic Report 2 Search Input: " + mIn);
        BaseList<DomesticReport> result = new BaseList<>();
        mIn.put(OFFSET, "0");
        List<DomesticReport> onlineList = getListDomesticReports(mIn);

        result.setList(onlineList);
        Map returnReport = new HashMap();
        long totalPurchaseSuccessAmount = 0;
        long totalPurchaseFailedAmount = 0;
        long totalRefundSuccessAmount = 0;
        long totalRefundFailAmount = 0;
        long totalRefundWaitForBankAmount = 0;
        long totalRefundWaitForOnePayAmount = 0;

        int totalPurchaseSuccessCount = 0;
        int totalPurchaseFailedCount = 0;
        int totalRefundSuccessCount = 0;
        int totalRefundFailCount = 0;
        int totalRefundWaitForBankCount = 0;
        int totalRefundWaitForOnepayCount = 0;
        for (DomesticReport items : onlineList) {
            totalPurchaseSuccessCount += items.getPurchase_success_total();
            totalPurchaseFailedCount += items.getPurchase_failed_total();
            totalRefundSuccessCount += items.getRefund_success_total();
            totalRefundFailCount += items.getRefund_failed_total();
            totalRefundWaitForBankCount += items.getRefund_wfb_approval_total();
            totalRefundWaitForOnepayCount += items.getRefund_wfo_approval_total();

            totalPurchaseSuccessAmount += items.getPurchase_success_amount();
            totalPurchaseFailedAmount += items.getPurchase_failed_amount();
            totalRefundSuccessAmount += items.getRefund_success_amount();
            totalRefundFailAmount += items.getRefund_failed_amount();
            totalRefundWaitForBankAmount += items.getRefund_wfb_approval_amount();
            totalRefundWaitForOnePayAmount += items.getRefund_wfo_approval_amount();
        }
        returnReport.put(REPORTS, onlineList);
        returnReport.put("total_purchase_success_count", totalPurchaseSuccessCount);
        returnReport.put("total_purchase_failed_count", totalPurchaseFailedCount);
        returnReport.put("total_refund_success_count", totalRefundSuccessCount);
        returnReport.put("total_refund_failed_count", totalRefundFailCount);
        returnReport.put("total_refund_wait_for_bank_count", totalRefundWaitForBankCount);
        returnReport.put("total_refund_wait_for_onepay_count", totalRefundWaitForOnepayCount);


        returnReport.put("total_purchase_success_amount", totalPurchaseSuccessAmount);
        returnReport.put("total_purchase_failed_amount", totalPurchaseFailedAmount);
        returnReport.put("total_refund_success_amount", totalRefundSuccessAmount);
        returnReport.put("total_refund_failed_amount", totalRefundFailAmount);
        returnReport.put("total_refund_wait_for_onepay_amount", totalRefundWaitForOnePayAmount);
        returnReport.put("total_refund_wait_for_bank_amount", totalRefundWaitForBankAmount);
        return returnReport;

    }

    public static List<DomesticReport> getListDomesticReports(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<DomesticReport> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall(SEARCH_REPORT_DOMESTIC);

            cs.setString(1, QueryMethod.SELECT.toString());
            cs.setString(2, mIn.getOrDefault(VERSION, ""));
            cs.setString(3, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(4, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(5, mIn.getOrDefault(CAIC, ""));
            cs.setString(6, mIn.getOrDefault(CONTRACT_TYPE, ""));
            cs.setString(7, mIn.getOrDefault(ACQUIRER_BANK, ""));
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(INTERVAL, "1"));
            cs.setString(11, mIn.getOrDefault("opFilterAmount", ""));
            cs.setString(12, mIn.getOrDefault("purchaseAmount", ""));
            cs.setString(13, mIn.getOrDefault("opFilterTotal", ""));
            cs.setString(14, mIn.getOrDefault("purchaseTotal", ""));
            cs.setString(15, mIn.getOrDefault("trans_type", ""));
            cs.registerOutParameter(16, OracleTypes.CURSOR);
            cs.registerOutParameter(17, OracleTypes.NUMBER);
            cs.registerOutParameter(18, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(18);
            int nError = cs.getInt(17);
            rs = (ResultSet) cs.getObject(16);
            if (nError != 200) {
                throw new Exception("DB get search domestic 2 transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindReport(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer total(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall(SEARCH_REPORT_DOMESTIC);

            cs.setString(1, QueryMethod.TOTAL.toString());
            cs.setString(2, mIn.getOrDefault(VERSION, ""));
            cs.setString(3, mIn.getOrDefault(MERCHANT_ID, ""));
            cs.setString(4, mIn.getOrDefault(BANK_ID, ""));
            cs.setString(5, mIn.getOrDefault(CAIC, ""));
            cs.setString(6, mIn.getOrDefault(CONTRACT_TYPE, ""));  
            cs.setString(7, mIn.getOrDefault(ACQUIRER_BANK, ""));   
            cs.setString(8, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(9, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(10, mIn.getOrDefault(INTERVAL, "1"));
            cs.setString(11, mIn.getOrDefault("opFilterAmount", ""));
            cs.setString(12, mIn.getOrDefault("purchaseAmount", ""));
            cs.setString(13, mIn.getOrDefault("opFilterTotal", ""));
            cs.setString(14, mIn.getOrDefault("purchaseTotal", ""));
            cs.setString(15, mIn.getOrDefault("trans_type", ""));
            cs.registerOutParameter(16, OracleTypes.CURSOR);
            cs.registerOutParameter(17, OracleTypes.NUMBER);
            cs.registerOutParameter(18, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(18);
            int nError = cs.getInt(17);
            rs = (ResultSet) cs.getObject(16);
            if (nError != 200) {
                throw new Exception("DB get search domestic 2 transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static DomesticReport bindReport(ResultSet rs) throws SQLException {
        Timestamp report_date = rs.getString("TXN_DATE") == null ? null : Timestamp.valueOf(rs.getString("TXN_DATE"));
        String acquirer_bank = rs.getString("S_ACQUIER") == null ? "" : rs.getString("S_ACQUIER");
        String acquirer_name = rs.getString("ACQUIRER_NAME");
        String acquirer_id = rs.getString("BANK_ID");
        String merchant_id = rs.getString("S_MERCHANT_ID");
        String command = rs.getString("COMMAND");
        String status = rs.getString("S_STATUS");
        String caic = rs.getString("S_CAIC") == null ? "" : rs.getString("S_CAIC");
        String contractType = rs.getString("S_CONTRACT_TYPE");

        Long purchaseSuccessTotal = rs.getLong("purchase_success_total");
        Long purchaseSuccessAmount = rs.getLong("purchase_success_amount");

        Long purchaseFailedTotal = rs.getLong("purchase_failed_total");
        Long purchaseFailedAmount = rs.getLong("purchase_failed_amount");

        Long refundSuccessTotal = rs.getLong("refund_success_total");
        Long refundSuccessAmount = rs.getLong("refund_success_amount");

        Long refundFailedTotal = rs.getLong("refund_failed_total");
        Long refundFailedAmount = rs.getLong("refund_failed_amount");

        Long refundWfoApprovalTotal = rs.getLong("refund_wfo_approval_total");
        Long refundWfoApprovalAmount = rs.getLong("refund_wfo_approval_amount");

        Long refundWfbApprovalTotal = rs.getLong("refund_wfb_approval_total");
        Long refundWfbApprovalAmount = rs.getLong("refund_wfb_approval_amount");
        DomesticReport domesticReport = new DomesticReport();
        domesticReport.setAcquirer_bank(acquirer_bank);
        domesticReport.setReport_date(report_date);
        domesticReport.setAcquirer_name(acquirer_name);
        domesticReport.setAcquirer_id(acquirer_id);
        domesticReport.setMerchant_id(merchant_id);
        domesticReport.setCaic(caic);
        domesticReport.setCommand(command);
        domesticReport.setContract_type(contractType);
        domesticReport.setStatus(status);

        domesticReport.setPurchase_success_total(purchaseSuccessTotal);
        domesticReport.setPurchase_success_amount(purchaseSuccessAmount);

        domesticReport.setPurchase_failed_total(purchaseFailedTotal);
        domesticReport.setPurchase_failed_amount(purchaseFailedAmount);

        domesticReport.setRefund_success_total(refundSuccessTotal);
        domesticReport.setRefund_success_amount(refundSuccessAmount);

        domesticReport.setRefund_failed_total(refundFailedTotal);
        domesticReport.setRefund_failed_amount(refundFailedAmount);
        
        domesticReport.setRefund_wfo_approval_total(refundWfoApprovalTotal);
        domesticReport.setRefund_wfo_approval_amount(refundWfoApprovalAmount);
        
        domesticReport.setRefund_wfb_approval_total(refundWfbApprovalTotal);
        domesticReport.setRefund_wfb_approval_amount(refundWfbApprovalAmount);
        return domesticReport;
    }
}
