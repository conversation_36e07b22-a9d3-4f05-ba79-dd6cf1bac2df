package vn.onepay.portal.resources.domestic.refund;

import static vn.onepay.portal.Util.sendResponse;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import com.google.gson.Gson;
import io.vertx.core.http.HttpClient;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MaClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.refund.dto.refund.RefundConfirmReq;
import vn.onepay.portal.resources.permission.PermissionDao;
import vn.onepay.portal.MailUtil;

/**
 * @Created 5/19/2020
 * <AUTHOR>
 */
public class DomesticRefundHandler implements IConstants {

    private static Logger logger = Logger.getLogger(DomesticRefundHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void searchDomesticRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());
                Integer userId = ctx.get(X_USER_ID);

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = mIn.get(FROM_DATE);
                String toDate = mIn.get(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL  GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(X_USER_ID, userId.toString());

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANT_ID) ? mIn.get(MERCHANT_ID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANT_ID, merchants.toString());
                    }
                }

                String finalCardNumber = FunctionUtil.isBeginIsACard(mIn.get(CARD_NUMBER)) && mIn.get(CARD_NUMBER).contains("*") ? mIn.get(CARD_NUMBER).replaceAll("\\*", "0") : mIn.get(CARD_NUMBER);
                if (FunctionUtil.isCardData(finalCardNumber)) {
                    String cardhash = FunctionUtil.oneSMHmac(finalCardNumber);
                    mIn.put(CARD_NUMBER, cardhash);
                }

                JsonObject jResponse = new JsonObject();

                JsonObject jSycn = domesticTxnSync();

                JsonObject jSearch = new JsonObject(gson.toJson(DomesticRefundDao.search(mIn)));

                jResponse.mergeIn(jSearch);

                if (jSycn != null) {
                    jResponse.mergeIn(jSycn);
                }

                sendResponse(ctx, 200, jResponse);
                // sendResponse(ctx, 200, DomesticRefundDao.search(mIn));


            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static JsonObject domesticTxnSync() {
        JsonObject jSycn = null;
        try {

            String jobId = Config.getString("onesched-service.domestic_new_refund_id", "");
            jSycn = OneSchedClient.synchronize(jobId, "0m");
        } catch (Exception e) {
            logger.log(Level.WARNING, "DOMESTIC REFUND SYNC ERROR: ", e);
        }
        return jSycn;
    }

    public static void getDomesticRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                String id = ctx.request().getParam(ID);
                System.out.println("ID: " + id);
                sendResponse(ctx, 200, DomesticRefundDao.get(Integer.valueOf(id)));
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.log(Level.INFO,ctx.get(REQUEST_UUID) + ": "+ "[======== START UPDATE REFUND APPROVAL ========");
                JsonObject body = ctx.getBodyAsJson();
                String action = body.getString("path");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String ip = ctx.request().headers().get("X-Forwarded-For");
                if (ip == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                String currrency = bodyJson.getString(CURRENCY);
                String transactionId = bodyJson.getString(ID);
                String transactionLogId = bodyJson.getString("refund_id");
                String transactionCode = bodyJson.getString("transaction_code");
                double refundAmount = bodyJson.getDouble(TOTAL_REFUND_TOTAL);
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String status = bodyJson.getString(STATUS);
                String ip_address = bodyJson.getString("ip_address");
                RefundConfirmReq req = new RefundConfirmReq();
                req.setTransactionId(Long.parseLong(transactionId));
                req.setCommandCode(action);
                req.setCurrencyCode(currrency);
                req.setAmount(refundAmount);
                req.setRefund_transactionId(Long.parseLong(transactionLogId));
                req.setOperatorId("ONEPAY");
                req.setMerchantId(merchantId);
                req.setClientIp(ip_address);
                if (action.equals("MANNUAL")) {
                    req.setStatus(Long.parseLong(status));
                    req.setAdditionInfo(transactionCode);
                } else if (action.equals("AUTO")) {
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }

                // JsonObject jResponse = domesticTxnSync(new JsonObject(gson.toJson(MaClient.patchUpdateDomesticRefund(req, transactionLogId, userId, xRequestId, ip))), "0m");
                JsonObject jResponse = MaClient.patchUpdateDomesticRefund(req, transactionLogId, userId, xRequestId, ip);
                logger.log(Level.INFO,ctx.get(REQUEST_UUID) + ": "+ "[======== END UPDATE REFUND APPROVAL ========");
                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "Update Domestic Refund: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void patchDomesticRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.log(Level.INFO,ctx.get(REQUEST_UUID) + ": "+ "[======== START PATCH DOMESTIC REFUND ========");
                final HttpClient httpClient = ctx.vertx().createHttpClient();
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String id = ctx.request().getParam(ID);

                // RefundApproval approvalRecord = RefundApprovalDao.getRefundApprovalById(Integer.valueOf(id));
                // if (approvalRecord.getStatus().compareTo(RefundApproval.Status.REQUEST.code) != 0
                // && approvalRecord.getStatus().compareTo(RefundApproval.Status.REQUEST_ONEPAY.code) != 0) {
                // logger.log(Level.SEVERE, "[ Domestic REFUND APPROVAL] => REFUND APPROVAL DONE ALREADY");
                // throw IErrors.DUPLICATE_REFUND_ERROR;
                // }
                // logger.log(Level.INFO, "REFUND APPROVAL INFO:" + approvalRecord.toString());

                JsonObject body = ctx.getBodyAsJson();
                String action = body.getString("path");
                if (action.equals("/approve")) {

                    // JsonObject jResponse = domesticTxnSync(MaClient.approveRefundDomestic(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")), "0m");
                    JsonObject jResponse = MaClient.approveRefundDomestic(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency"));
                    logger.log(Level.INFO,ctx.get(REQUEST_UUID) + ": "+ "[======== END PATCH DOMESTIC REFUND ========");
                    sendResponse(ctx, 200, jResponse);

                } else if (action.equals("/reject")) {

                    // JsonObject jResponse = domesticTxnSync(MaClient.rejectAprovalDomestic(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")), "0m");
                    JsonObject jResponse = MaClient.rejectAprovalDomestic(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency"));
                    logger.log(Level.INFO,ctx.get(REQUEST_UUID) + ": "+ "[======== END PATCH DOMESTIC REFUND ========");
                    sendResponse(ctx, 200, jResponse);

                }

                else {
                    throw IErrors.VALIDATION_ERROR;
                }

            } catch (Exception e) {
                logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "Patch Domestic Refund: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateDomesticRefundStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String pattern = "###,###";
                DecimalFormat decimalFormat = new DecimalFormat(pattern);
                HttpServerRequest request = ctx.request();
                String transactionId = request.getParam("transaction_id");
                String originalId = request.getParam("original_id");
                String status = request.getParam(RESPONSE);
                String merchantId = request.getParam("merchant_id");
                String bankId = request.getParam("bank_id");
                String previousStatus = request.getParam("previous_status");
                String refundAmount = request.getParam("refund_amount");
                String purchaseAmount = request.getParam("purchase_amount");
                String currency = request.getParam("currency");
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                StringBuilder subject = new StringBuilder("CẢNH BÁO THAY ĐỔI TRẠNG THÁI GIAO DỊCH NỘI ĐỊA ").append(transactionId);
                String host = Config.getString("email.host", "");
                Integer port = Config.getInteger("email.port", 25);
                String username = Config.getString("email.username", "");
                String password = Config.getString("email.password", "");
                String fromEmail = Config.getString("email.address", "");
                StringBuilder body = new StringBuilder("Dear Payment Team,<br>");
                body.append("Giao dịch nội địa sau đây đã được thay đổi trạng thái:<br>");
                body.append("Merchant ID: ").append(merchantId).append("<br>");
                body.append("Transaction ID: ").append(transactionId).append("<br>");
                body.append("Bank ID: ").append(bankId).append("<br>");
                body.append("Transaction Type: Refund<br>");
                body.append("Amount: ").append(decimalFormat.format(Double.valueOf(refundAmount))).append(" ").append(currency).append("<br>");
                body.append("Transaction Date: ").append(simpleDateFormat.format(System.currentTimeMillis())).append("<br>");
                body.append("Status: ").append(previousStatus).append(" to ").append(status).append("<br><br>");
                body.append("Thông tin giao dịch gốc: <br>");
                body.append("Transaction ID: ").append(originalId).append("<br>");
                body.append("Transaction Type: Refund<br>");
                body.append("Amount: ").append(decimalFormat.format(Double.valueOf(purchaseAmount))).append(" ").append(currency).append("<br>");
                String toEmail = "<EMAIL>";
                MailUtil.sendMail(host, port, username, password, fromEmail, toEmail, toEmail, subject.toString(), body.toString());
                sendResponse(ctx, 200, DomesticRefundDao.updateRefundStatus(transactionId, originalId, refundAmount, status, previousStatus));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject mIn = ctx.getBodyAsJson();
                Integer userId = ctx.get(X_USER_ID);
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = mIn.getString(FROM_DATE).toString();
                String toDate = mIn.getString(TO_DATE).toString();
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ DOMESTIC REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }

                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(PAGE, "0");
                mIn.put(PAGE_SIZE, String.valueOf(Integer.MAX_VALUE));
                mIn.put(X_USER_ID, userId);

                if (null != mIn.getInteger(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(mIn.getInteger(X_USER_ID));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = mIn.getString(MERCHANT_ID, "");
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANT_ID, merchants.toString());
                    }
                }

                String finalCardNumber = FunctionUtil.isBeginIsACard(mIn.getString(CARD_NUMBER)) && mIn.getString(CARD_NUMBER).contains("*") ? mIn.getString(CARD_NUMBER).replaceAll("\\*", "0") : mIn.getString(CARD_NUMBER);
                if (FunctionUtil.isCardData(finalCardNumber)) {
                    String cardhash = FunctionUtil.oneSMHmac(finalCardNumber);
                    mIn.put(CARD_NUMBER, cardhash);
                }

                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                int totalRows = DomesticRefundDao.total(mIn.getMap());
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }

                // Initial file information
                long date = new Date().getTime();
                String fileName = "domestic_refund_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn.getMap());
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("domestic_refund");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(mIn.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn.getMap(), JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, ctx.get(REQUEST_UUID) + ": " + "DOWNLOAD DOMESTIC REFUND Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
