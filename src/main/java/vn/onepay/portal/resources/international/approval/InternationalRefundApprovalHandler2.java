package vn.onepay.portal.resources.international.approval;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MspClient;
import vn.onepay.portal.client.OneSchedClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval2;
import vn.onepay.portal.resources.permission.PermissionDao;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;
import static vn.onepay.portal.Util.sendResponse;

public class InternationalRefundApprovalHandler2 implements IConstants {
    private static Logger logger = Logger.getLogger(InternationalRefundApprovalHandler2.class.getName());

    private static final Gson gson = new Gson();

    public static void searchInternationalRefundApproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(X_USER_ID);

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = ctx.request().getParam(FROMDATE);
                String toDate = ctx.request().getParam(TODATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                // if (months > 12) {
                // logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL GET ] => INVALID DATE ");
                // throw IErrors.SEARCH_TOO_LARGE_ERROR;
                // }
                Map mIn = Util.convertRequestParamsToMap(ctx.request());
                mIn.put(FROMDATE, fromDate);
                mIn.put(TODATE, toDate);
                mIn.put(X_USER_ID, userId);

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANTID) ? mIn.get(MERCHANTID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANTID, merchants);
                    }
                }

                // JsonObject jResponse = new JsonObject();
                
                // JsonObject jSycn = internationalTxnSync();

                internationalTxnSync();

                BaseList<InternationalRefundApproval2> baseList = new BaseList<>();
                List<InternationalRefundApproval2> l = InternationalRefundApprovalDao2.searchRefund(mIn);
                baseList.setList(l);

                // JsonObject jSearch = new JsonObject(gson.toJson(baseList));

                // jResponse.mergeIn(jSearch);

                // if (jSycn != null) {
                //     jResponse.mergeIn(jSycn);
                // }

                sendResponse(ctx, 200, baseList);
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static JsonObject internationalTxnSync() {
        JsonObject jSycn = null;
        try {
            String jobId = Config.getString("onesched-service.international_new_refund_id", "");
            jSycn = OneSchedClient.synchronizeLong(jobId, "0m");
        } catch (Exception e) {
            logger.log(Level.WARNING, "INTERNATIONAL REFUND SYNC ERROR: ", e);
        }
        return jSycn;
    }

    public static void getInternationalRefundApproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, InternationalRefundApprovalDao.getInternationalRefundApprovalById(Integer.valueOf(id)));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void approveRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START APPROVE REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = randomMerchantTransRef(merchantId);
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  PARAM ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.approveRefund(merchantId, transactionId, transactionRef, note, operator);
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END APPROVE REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE INTERNATIONAL Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void reverseDue(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START RESEVE TO DISPUTE ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ RESEVE TO DISPUTE PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ RESEVE TO DISPUTE PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = randomMerchantTransRef(merchantId);
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ RESEVE TO DISPUTE PATCH ] =>  PARAM ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.reverseDue(merchantId, transactionId, transactionRef, note, operator);
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END RESEVE TO DISPUTE ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "RESEVE TO DISPUTE ERROR: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void rejectRefund(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START APPROVE REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String note = bodyJson.getString(NOTE);

                JsonObject jResponse = MspClient.rejectRefund(merchantId, transactionId, note, operator);
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END APPROVE REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE INTERNATIONAL Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void doManual(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START MANUAL REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL MANUAL APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL MANUAL APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = randomMerchantTransRef(merchantId);
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL MANUAL APPROVAL PATCH ] =>  PARAM ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.manual(merchantId, transactionId, transactionRef, note, operator);
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END MANUAL REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "MANUAL INTERNATIONAL REFUND: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void doAuto(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START AUTO REFUND APPROVAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL AUTO APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL AUTO APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String transactionRef = randomMerchantTransRef(merchantId);
                String note = bodyJson.getString(NOTE);

                if (StringUtils.isEmpty(transactionId) || StringUtils.isEmpty(merchantId) || StringUtils.isEmpty(transactionRef)) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL AUTO APPROVAL PATCH ] =>  PARAM ERROR");
                    throw IErrors.VALIDATION_ERROR;
                }

                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;

                JsonObject jResponse = MspClient.auto(merchantId, transactionId, transactionRef, note, operator);
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END AUTO REFUND APPROVAL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "MANUAL INTERNATIONAL REFUND: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateState(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START UPDATE STATE REFUND ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userDto = UserDao.get(userId);
                String operator = userDto != null && !StringUtils.isEmpty(userDto.getEmail())
                        ? userDto.getEmail()
                        : userId;
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String state = bodyJson.getString(STATE);
                String merchantId = bodyJson.getString(MERCHANT_ID);
                String note = bodyJson.getString(NOTE);

                JsonObject jResponse = null;
                if (APPROVED.equals(state)) {
                    jResponse = MspClient.updateStateToApproved(merchantId, transactionId, note, operator);
                } else if(FAILED.equals(state)) {
                    jResponse = MspClient.updateStateToFailed(merchantId, transactionId, note, operator);
                } else {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  INVALID STATE");
                    throw IErrors.VALIDATION_ERROR;
                }

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END UPDATE STATE REFUND ========");

                sendResponse(ctx, 200, jResponse);
            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "APPROVE INTERNATIONAL REFUND: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void checkAuto(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== START INTERNATIONAL CHECK REFUND AUTO OR MANUAL ========");
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL APPROVE  PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL APPROVE  PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                JsonObject bodyJson = ctx.getBodyAsJson();
                String transactionId = bodyJson.getValue(ID).toString();
                String merchantId = bodyJson.getString(MERCHANT_ID);

                JsonObject jResponse = MspClient.checkAutoManual(merchantId, transactionId);

                logger.info(() -> ctx.get(REQUEST_UUID) + ": " + "[======== END INTERNATIONAL CHECK REFUND AUTO OR MANUALL ========");

                sendResponse(ctx, 200, jResponse);

            } catch (Exception e) {
                logger.severe(() -> ctx.get(REQUEST_UUID) + ": " + "Check international Refund: " + e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map mIn = bodyJson.getMap();
                Integer userId = ctx.get(X_USER_ID);
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(mIn.get(FROMDATE).toString());
                    oToDate = df.parse(mIn.get(TODATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    mIn.put(FROMDATE, sdf.format(oFromDate));
                    mIn.put(TODATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.severe(() -> "[ DOWNLOAD INTERNATIONAL INSTALLMENT ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }

                mIn.put(X_USER_ID, userId);

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANTID) ? mIn.get(MERCHANTID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANTID, merchants);
                    }
                }

                // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                // if (months > 12) {
                // logger.log(Level.SEVERE, "[ DOWNLOAD INTERNATIONAL INSTALLMENT ] => INVALID DATE ");
                // throw IErrors.SEARCH_TOO_LARGE_ERROR;
                // }

                if (FunctionUtil.isCardData(mIn.get(CARDNUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARDNUMBER).toString());
                    mIn.put(CARDNUMBER, String.valueOf(cardhash));
                }

                // int totalRows = InternationalRefundApprovalDao.getTotalTransaction(mIn).size();
                // if (totalRows == 0) {
                // throw IErrors.NO_DATA_FOUND;
                // }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "international_refund_approval_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                // data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("international_refund_approval");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                fileDownloadDto.setExt("xlsx");

                FileDownloadDao.insert(fileDownloadDto);

                QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));


                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    private static String randomMerchantTransRef(String merchantId) {
        // Init refund reference
        return UUID.randomUUID().toString();
    }
}
