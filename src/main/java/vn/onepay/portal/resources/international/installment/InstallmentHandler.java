package vn.onepay.portal.resources.international.installment;


import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import vn.onepay.portal.*;
import vn.onepay.portal.client.OneSchedClient;
import java.text.ParseException;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class InstallmentHandler implements IConstants {

    private static Logger logger = Logger.getLogger(InstallmentHandler.class.getName());

    public static void getAllInstallmentBank(RoutingContext ctx) {

        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, InstallmentDao.getBank());

            } catch (Exception e) {
                logger.log(Level.SEVERE, "get Installment bank: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void insertTransRefundIta(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject body = ctx.getBodyAsJson();
                Integer userId = ctx.get(X_USER_ID);
                String userIdLogin = "";
                try {
                    userIdLogin = UserDao.get(userId).getName();
                } catch (Exception e1) {
                    logger.log(Level.SEVERE, "===ERORR GET USERID", e1);
                }
                JsonArray trans = body.getJsonArray("trans");
                Map<String, Object> response = new HashMap<>();
                List<Map<String, Object>> res = new ArrayList<>();

                if (trans != null && !trans.isEmpty() && trans.size() > 0) {
                    for (Object tran : trans) {
                        JsonObject t = (JsonObject) tran;
                        Map<String, Object> mapRes = new HashMap<>();
                        List<Map<String, Object>> configs = InstallmentDao.getIdConfigRefundIta(
                                t.getString(MERCHANT_ID), t.getString(CURRENCY),
                                t.getString(PAY_CHANNEL).equals("UPOS") ? "UPOS" : "QT");
                        // if (configs != null) {

                            if (configs.size() == 1) {
                                t.put(USER_ID, userIdLogin);
                                t.put(CONFIG_ADVANCE_ID, Integer.parseInt(configs.get(0).get(ID).toString()));
                                Map<String, Object> result = InstallmentDao.insertTransactionRefundIta(t);
                                // if (trans.size() == 1) {
                                    if (result.get(CODE).equals(200)) {

                                        mapRes.put(CODE, 200);
                                        mapRes.put(MESSAGE, result.get(MESSAGE));
                                        mapRes.put(ORIGINAL_ID, t.getString(ID));

                                    } else if (result.get(CODE).equals(406)) {

                                        mapRes.put(CODE, 406);
                                        mapRes.put(MESSAGE, result.get(MESSAGE));
                                        mapRes.put(ORIGINAL_ID, t.getString(ID));
                                    } else if (result.get(CODE).equals(410)) {
                                        mapRes.put(CODE, 410);
                                        mapRes.put(MESSAGE, result.get(MESSAGE));
                                        mapRes.put(ORIGINAL_ID, t.getString(ID));
                                    } else {
                                        mapRes.put(CODE, 407);
                                        mapRes.put(MESSAGE, "Error insert transaction refund ita! " + t.getString(ID));
                                    }
                                // } else {

                                // }
                            } else if (configs.size() > 1) {
                                mapRes.put(CODE, 409);
                                mapRes.put(MESSAGE, "Duplicate config! " + t.getString(ID));
                                // Lỗi 1 merchant có nhiều cấu hình
                                logger.log(Level.SEVERE, "Duplicate config : {0} ( {1})",
                                        new Object[] { t.getString(TRANSACTION_ID), t.getString(MERCHANT_ID) });
                            } else {
                                // mapRes.put(CODE, 408);
                                // mapRes.put(MESSAGE, "Merchant is not config yet!");
                                //chấp nhận không có config với trả góp 3 bên lưu hoàn phí
                                t.put(USER_ID, userIdLogin);
                                t.put(CONFIG_ADVANCE_ID, 0);
                                Map<String, Object> result = InstallmentDao.insertTransactionRefundIta(t);
                                // if (trans.size() == 1) {
                                if (result.get(CODE).equals(200)) {

                                    mapRes.put(CODE, 200);
                                    mapRes.put(MESSAGE, result.get(MESSAGE));
                                    mapRes.put(ORIGINAL_ID, t.getString(ID));

                                } else if (result.get(CODE).equals(406)) {

                                    mapRes.put(CODE, 406);
                                    mapRes.put(MESSAGE, result.get(MESSAGE));
                                    mapRes.put(ORIGINAL_ID, t.getString(ID));
                                } else if (result.get(CODE).equals(410)) {
                                    mapRes.put(CODE, 410);
                                    mapRes.put(MESSAGE, result.get(MESSAGE));
                                    mapRes.put(ORIGINAL_ID, t.getString(ID));
                                } else {
                                    mapRes.put(CODE, 407);
                                    mapRes.put(MESSAGE, "Error insert transaction refund ita! " + t.getString(ID));
                                }
                            }
                        // }
                        res.add(mapRes);
                    }
                }

                response.put("map_res", res);
                sendResponse(ctx, 200, response);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error inser transaction refund ita: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getInstallmenTransDetail(RoutingContext ctx) {
        logger.info("getInstallmentTransDetail() userID:" + ctx.get(S_USER_ID));
        ctx.vertx().executeBlocking(future -> {
            try {

                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String id = ctx.request().getParam(ID);

                sendResponse(ctx, 200, InstallmentDao.getInstallmentTransDetail(id));

            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchInstallmentTransaction(RoutingContext ctx) {
        logger.info("searchInstallmentTransaction() userID:" + ctx.get(S_USER_ID));
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = ctx.request().getParam(FROM_DATE);
                String toDate = ctx.request().getParam(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(KEYWORD,
                        request.getParam(PARTNER_ID) == null ? 0 : Integer.parseInt(request.getParam(PARTNER_ID)));
                mIn.put(ORDER_INFO, request.getParam(ORDER_INFO) == null ? "" : request.getParam(ORDER_INFO));
                mIn.put(CARD_NUMBER, request.getParam(CARD_NUMBER) == null ? "" : request.getParam(CARD_NUMBER));
                mIn.put(CARD_TYPE, request.getParam(CARD_TYPE) == null ? "" : request.getParam(CARD_TYPE));
                mIn.put(CURRENCY, request.getParam(CURRENCY) == null ? "" : request.getParam(CURRENCY));
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID) == null ? "" : request.getParam(ACQUIRER_ID));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID) == null ? "" : request.getParam(MERCHANT_ID));
                mIn.put(TRANSACTION_ID, request.getParam(TRANSACTION_ID) == null ? "" :request.getParam(TRANSACTION_ID));
                mIn.put(MERCHANT_TRANSACTION_REF, request.getParam(MERCHANT_TRANSACTION_REF) == null ? "" : request.getParam(MERCHANT_TRANSACTION_REF));
                mIn.put(AUTHORISATION_CODE, request.getParam(AUTHORISATION_CODE) == null ? "" : request.getParam(AUTHORISATION_CODE).replace(" ", ","));
                mIn.put(INSTALLMENT_BANK, request.getParam(INSTALLMENT_BANK) == null ? "" : request.getParam(INSTALLMENT_BANK));
                mIn.put(INSTALLMENT_STATUS, request.getParam(INSTALLMENT_STATUS) == null ? "" : request.getParam(INSTALLMENT_STATUS));
                mIn.put(INSTALLMENT_STATE, request.getParam(INSTALLMENT_STATE) == null ? "" : request.getParam(INSTALLMENT_STATE));
                mIn.put(REFUND_INSTALLMENT_STATUS, request.getParam(REFUND_INSTALLMENT_STATUS) == null ? "" : request.getParam(REFUND_INSTALLMENT_STATUS));
                mIn.put(TRANS_STATEMENT_DAY, request.getParam(TRANS_STATEMENT_DAY) == null ? "" : request.getParam(TRANS_STATEMENT_DAY));
                mIn.put(CHANNEL, request.getParam(CHANNEL) == null ? "" : request.getParam(CHANNEL));
                mIn.put(REFERRAL_PARTNER, request.getParam(REFERRAL_PARTNER) == null ? "" : request.getParam(REFERRAL_PARTNER));
                mIn.put(PAGE_SIZE,
                        request.getParam(PAGE_SIZE) == null ? 0 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(OFFSET, 0);
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);

                if (FunctionUtil.isCardData(mIn.get(CARD_NUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARD_NUMBER).toString());
                    mIn.put(CARD_NUMBER, String.valueOf(cardhash));
                }

                if(mIn.get(TRANS_STATEMENT_DAY).equals("1")){
                    addCloseStmDateConitions(mIn);
                }

                sendResponse(ctx, 200, InstallmentDao.search(mIn));

            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void patchInstallmentTrans(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userResult = UserDao.get(userId);
                String id = ctx.request().getParam(ID);

                JsonObject body = ctx.getBodyAsJson();
                String action = body.getString("path");

                DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm a");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (action.equals("/approve")) {
                    String sDate = body.getString("approveDate");
                    String sAmount = body.getString("approveAmount");
                    String sDesc = body.getString("approveDesc");
                    Date approveDate = null;
                    String sApproveDate = null;
                    String approveDesc = null;
                    if (sDate != null) {
                        if (!sDate.equals("")) {
                            try {
                                approveDate = df.parse(sDate);
                                sApproveDate = sdf.format(approveDate);
                                approveDesc = sDesc.toString();
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                                throw IErrors.VALIDATION_ERROR;
                            }
                        }
                    }

                    String transType = body.getString("transType");
                    if ("IMPORT".equals(transType)) {
                        InstallmentDao.approveIppTransaction(id, new Date(), approveDate, Double.parseDouble(sAmount), approveDesc, userResult.getEmail());
                    } else {
                        InstallmentDao.installmentStateApprove(id, sApproveDate, sAmount, approveDesc);
                    }
                } else if (action.equals("/reject")) {
                    String dbDate = body.getString("rejectDate");
                    String emailDate = body.getString("emailDate");
                    String dateTransaction = body.getString("dateTransaction");
                    String sDesc = body.getString("rejectDesc");
                    // String sAmount = body.getString("amount").replaceAll(",","");
                    String sAmount = "0";
                    Date rejectDates = null;
                    String sRejectDate = null;
                    String rejectDesc = null;
                    String rejectCode = null;
                    String rejectMessage = null;
                    if (dbDate != null) {
                        if (!dbDate.equals("")) {
                            try {
                                rejectDates = df.parse(dbDate);
                                sRejectDate = sdf.format(rejectDates);
                                rejectDesc = sDesc.toString();
                                if (rejectDesc.contains(" - ")) {
                                    rejectCode = rejectDesc.split(" - ")[0];
                                    rejectMessage = rejectDesc.split(" - ")[1];
                                }
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                                throw IErrors.VALIDATION_ERROR;
                            }
                        }
                    }

                    StringBuilder subjectEmail = new StringBuilder();
                    String merchantID = body.getString("merchant_id") == null ? "" : body.getString("merchant_id");
                    String orderRef = body.getString("order_ref") == null ? "" : body.getString("order_ref");
                    subjectEmail.append("Notify - Rejected Installment Txn - ");
                    subjectEmail.append(merchantID);
                    subjectEmail.append(" Order Ref: ");
                    subjectEmail.append(orderRef);
                    subjectEmail.append(" ");
                    subjectEmail.append("Txn ");
                    subjectEmail.append(dateTransaction);

                    // String toEmail = "<EMAIL>";
                    // String ccEmail = "<EMAIL>";
                    String toEmail = Config.getString("email.toEmail", "");
                    String ccEmail = Config.getString("email.ccEmail", "");

                    StringBuilder contentEmail = new StringBuilder();
                    contentEmail.append(HTML_DOC);
                    contentEmail.append(HTML_START);
                    contentEmail.append(HEAD_START);
                    contentEmail.append(STYLE_START);
                    contentEmail.append("body {\n" +
                            "  color: black;\n" +
                            "}");
                    contentEmail.append("table, th, td {\n" +
                            "  border: 1px solid black;\n" +
                            "  border-collapse: collapse;\n" +
                            "  padding: 10px;\n" +
                            "  text-align: center;\n" +
                            "}");
                    contentEmail.append("th {\n" +
                            "  background-color: #dbdada;\n" +
                            "}");
                    contentEmail.append(STYLE_END);
                    contentEmail.append(HEAD_END);
                    contentEmail.append(BODY_START);

                    contentEmail.append("Dear Service Support,");
                    contentEmail.append("<br>");
                    contentEmail.append("<br>");
                    contentEmail.append("The below installment transaction was rejected by ");
                    String installmentBank = body.getString("installment_bank") == null ? ""
                            : body.getString("installment_bank");
                    contentEmail.append(installmentBank);
                    contentEmail.append(".");
                    contentEmail.append("<br>");
                    contentEmail.append("Please check and send notify to Merchant.");
                    contentEmail.append("Reason: " + rejectDesc);
                    contentEmail.append("<br>");
                    contentEmail.append("<br>");
                    contentEmail.append(TABLE_START);

                    contentEmail.append(ROW_START);
                    String[] thTagList = { "No", "Merchant ID", "Date", "Transaction ID", "Order Reference",
                            "Card Number", "Card Type", "Amount", "Installment Bank", "Installment Period",
                            "Transaction State", "Authorization Code" };
                    for (int i = 0; i < thTagList.length; i++) {
                        contentEmail.append(HEADER_START);
                        contentEmail.append(thTagList[i]);
                        contentEmail.append(HEADER_END);
                    }
                    contentEmail.append(ROW_END);

                    contentEmail.append(ROW_START);
                    String transactionId = body.getString(ID) == null ? "" : body.getString(ID);
                    String cardNumber = body.getString("card_number") == null ? "" : body.getString("card_number");
                    String cardType = body.getString("card_type") == null ? "" : body.getString("card_type");
                    String amount = body.getString("amount") == null ? "" : body.getString("amount");
                    String installmentPeriod = body.getString("installment_period") == null ? ""
                            : body.getString("installment_period");
                    String transactionState = body.getString("transaction_state") == null ? ""
                            : body.getString("transaction_state");
                    String authorCode = body.getString("authorization_code") == null ? ""
                            : body.getString("authorization_code");
                    String[] tdTagValue = { "1", merchantID, emailDate, transactionId, orderRef,
                            cardNumber, cardType, amount, installmentBank, installmentPeriod, transactionState,
                            authorCode };
                    for (int i = 0; i < tdTagValue.length; i++) {
                        contentEmail.append(COLUMN_START);
                        contentEmail.append(tdTagValue[i]);
                        contentEmail.append(COLUMN_END);
                    }
                    contentEmail.append(ROW_END);

                    contentEmail.append(TABLE_END);
                    contentEmail.append(BODY_END);
                    contentEmail.append(HTML_END);

                    contentEmail.append("<br>");
                    contentEmail.append("<br>");
                    contentEmail.append("Thanks,");

                    MailUtil.sendMailWithCC(toEmail, ccEmail, subjectEmail.toString(), contentEmail.toString(), "html");
                    
                    String transType = body.getString("transType");
                    if ("IMPORT".equals(transType)) {
                        InstallmentDao.rejectIppTransaction(id, new Date(), rejectDates, rejectCode, rejectMessage, userResult.getEmail());
                    } else {
                        InstallmentDao.installmentStateReject(id, sRejectDate, sAmount, rejectDesc);
                    }
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
                try {
                    logger.log(Level.INFO, "ep dong bo ITA approval : ", Config.getString("onesched-service.ita_approval", ""));
                    OneSchedClient.synchronizeLong(Config.getString("onesched-service.ita_approval", ""), "5s");
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "error ep dong bo ITA approval : ", e);
                }
                sendResponse(ctx, 200);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Patch Installment approval: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void postApproveInstallmentTrans(RoutingContext ctx) { // approval
        ctx.vertx().executeBlocking(future -> {
            try {
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, "[ Installment APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                User userResult = UserDao.get(userId);

                JsonObject body = ctx.getBodyAsJson();
                String action = body.getString("path");
                JsonArray trans = body.getJsonArray("trans");
                Map<String, Object> response = new HashMap<>();
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm a");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (action.equals("/approve")) {
                    String sDate = body.getString("approveDate");
                    String sAmount = body.getString("approveAmount");
                    String sDesc = body.getString("approveDesc");
                    Date approveDate = null;
                    String sApproveDate = null;
                    String approveDesc = null;
                    if (sDate != null) {
                        if (!sDate.equals("")) {
                            try {
                                approveDate = df.parse(sDate);
                                sApproveDate = sdf.format(approveDate);
                                approveDesc = sDesc.toString();
                            } catch (Exception e) {
                                logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                                throw IErrors.VALIDATION_ERROR;
                            }
                        }
                    }

                    for (Object tran : trans) {
                        JsonObject t = (JsonObject) tran;
                        String sAmountClone = null;
                        if (sAmount.equals("")) {
                            sAmountClone = t.getJsonObject("amount").getString("total");
                        } else {
                            sAmountClone = sAmount;
                        }

                        String channel = t.getString("channel");
                        if ("IMPORT".equals(channel)) {
                            InstallmentDao.approveIppTransaction(t.getString("id"), new Date(), approveDate, Double.parseDouble(sAmountClone), approveDesc, userResult.getEmail());
                        } else {
                            InstallmentDao.installmentStateApprove(t.getString("id"), sApproveDate, sAmountClone, approveDesc);
                        }
                    }
                } else if (action.equals("/reject")) {

                    for (Object tran : trans) {
                        JsonObject t = (JsonObject) tran;
                        // String sAmount = t.getString("amount").replaceAll(",","");
                        String sAmount = "0";
                        String dbDate = body.getString("rejectDate");
                        String emailDate = body.getString("emailDate");
                        String dateTransaction = t.getString("dateTransaction");
                        String sDesc = body.getString("rejectDesc");
                        Date rejectDates = null;
                        String sRejectDate = null;
                        String rejectDesc = null;
                        String rejectCode = null;
                        String rejectMessage = null;
                        if (dbDate != null) {
                            if (!dbDate.equals("")) {
                                try {
                                    rejectDates = df.parse(dbDate);
                                    sRejectDate = sdf.format(rejectDates);
                                    rejectDesc = sDesc.toString();
                                    if (rejectDesc.contains(" - ")) {
                                        rejectCode = rejectDesc.split(" - ")[0];
                                        rejectMessage = rejectDesc.split(" - ")[1];
                                    }
                                } catch (Exception e) {
                                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                                    throw IErrors.VALIDATION_ERROR;
                                }
                            }
                        }

                        StringBuilder subjectEmail = new StringBuilder();
                        String merchantID = t.getString("merchant_id") == null ? "" : t.getString("merchant_id");
                        String orderRef = t.getString("order_ref") == null ? "" : t.getString("order_ref");
                        subjectEmail.append("Notify - Rejected Installment Txn - ");
                        subjectEmail.append(merchantID);
                        subjectEmail.append(" Order Ref: ");
                        subjectEmail.append(orderRef);
                        subjectEmail.append(" ");
                        subjectEmail.append("Txn ");
                        subjectEmail.append(dateTransaction);

                        // String toEmail = "<EMAIL>";
                        String toEmail = Config.getString("email.toEmail", "");
                        String ccEmail = Config.getString("email.ccEmail", "");

                        StringBuilder contentEmail = new StringBuilder();
                        contentEmail.append(HTML_DOC);
                        contentEmail.append(HTML_START);
                        contentEmail.append(HEAD_START);
                        contentEmail.append(STYLE_START);
                        contentEmail.append("body {\n" +
                                "  color: black;\n" +
                                "}");
                        contentEmail.append("table, th, td {\n" +
                                "  border: 1px solid black;\n" +
                                "  border-collapse: collapse;\n" +
                                "  padding: 10px;\n" +
                                "  text-align: center;\n" +
                                "}");
                        contentEmail.append("th {\n" +
                                "  background-color: #dbdada;\n" +
                                "}");
                        contentEmail.append(STYLE_END);
                        contentEmail.append(HEAD_END);
                        contentEmail.append(BODY_START);

                        contentEmail.append("Dear Service Support,");
                        contentEmail.append("<br>");
                        contentEmail.append("<br>");
                        contentEmail.append("The below installment transaction was rejected ");

                        // contentEmail.append(installmentBank);
                        contentEmail.append(".");
                        contentEmail.append("<br>");
                        contentEmail.append("Please check and send notify to Merchant.");
                        contentEmail.append("Reason: " + rejectDesc);
                        contentEmail.append("<br>");
                        contentEmail.append("<br>");
                        contentEmail.append(TABLE_START);

                        contentEmail.append(ROW_START);
                        String[] thTagList = { "No", "Merchant ID", "Date", "Transaction ID", "Order Reference",
                                "Card Number", "Card Type", "Amount", "Installment Bank", "Installment Period",
                                "Transaction State", "Authorization Code" };
                        for (int i = 0; i < thTagList.length; i++) {
                            contentEmail.append(HEADER_START);
                            contentEmail.append(thTagList[i]);
                            contentEmail.append(HEADER_END);
                        }
                        contentEmail.append(ROW_END);

                        contentEmail.append(ROW_START);
                        String transactionId = t.getString(ID) == null ? "" : t.getString(ID);
                        String cardNumber = t.getString("card_number") == null ? "" : t.getString("card_number");
                        String cardType = t.getString("card_type") == null ? "" : t.getString("card_type");
                        String amount = t.getString("amount") == null ? "" : t.getString("amount");
                        String installmentPeriod = t.getString("installment_period") == null ? ""
                                : t.getString("installment_period");
                        String transactionState = t.getString("transaction_state") == null ? ""
                                : t.getString("transaction_state");
                        String authorCode = t.getString("authorization_code") == null ? ""
                                : t.getString("authorization_code");
                        String installmentBank = t.getString("installment_bank") == null ? ""
                                : t.getString("installment_bank");
                        String[] tdTagValue = { "1", merchantID, emailDate, transactionId, orderRef,
                                cardNumber, cardType, amount, installmentBank, installmentPeriod, transactionState,
                                authorCode };

                        for (int i = 0; i < tdTagValue.length; i++) {
                            contentEmail.append(COLUMN_START);
                            contentEmail.append(tdTagValue[i]);
                            contentEmail.append(COLUMN_END);
                        }
                        contentEmail.append(ROW_END);

                        contentEmail.append(TABLE_END);
                        contentEmail.append(BODY_END);
                        contentEmail.append(HTML_END);

                        contentEmail.append("<br>");
                        contentEmail.append("<br>");
                        contentEmail.append("Thanks,");

                        MailUtil.sendMailWithCC(toEmail, ccEmail, subjectEmail.toString(), contentEmail.toString(),
                                "html");

                        int code = 500;
                        String channel = t.getString("channel");
                        if ("IMPORT".equals(channel)) {
                            InstallmentDao.rejectIppTransaction(t.getString(ID), new Date(), rejectDates, rejectCode, rejectMessage, userResult.getEmail());
                            code = 200;
                        } else {
                            code = InstallmentDao.installmentStateReject(t.getString(ID), sRejectDate, sAmount, rejectDesc);
                        }
                        if (code == 200) {
                            response.put("n_status", 200);
                        } else {
                            response.put("n_status", 400);
                        }
                    }
                } else {
                    throw IErrors.VALIDATION_ERROR;
                }
                try {
                    logger.log(Level.INFO, "ep dong bo ITA approval : ", Config.getString("onesched-service.ita_approval", ""));
                    OneSchedClient.synchronizeLong(Config.getString("onesched-service.ita_approval", ""), "5s");
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "error ep dong bo ITA approval : ", e);
                }
                sendResponse(ctx, 200, response);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Patch Installment approval: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        logger.info("download() userID:" + ctx.get(S_USER_ID));
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ORDER_INFO, bodyJson.getString(ORDER_INFO) == null ? "" : bodyJson.getString(ORDER_INFO));
                mIn.put(CARD_NUMBER, bodyJson.getString(CARD_NUMBER) == null ? "" : bodyJson.getString(CARD_NUMBER));
                mIn.put(CARD_TYPE, bodyJson.getString(CARD_TYPE) == null ? "" : bodyJson.getString(CARD_TYPE));
                mIn.put(CURRENCY, bodyJson.getString(CURRENCY) == null ? "" : bodyJson.getString(CURRENCY));
                mIn.put(ACQUIRER_ID, bodyJson.getString(ACQUIRER_ID) == null ? "" : bodyJson.getString(ACQUIRER_ID));
                mIn.put(MERCHANT_ID, bodyJson.getString(MERCHANT_ID) == null ? "" : bodyJson.getString(MERCHANT_ID));
                mIn.put(TRANSACTION_ID, bodyJson.getString(TRANSACTION_ID) == null ? "" :bodyJson.getString(TRANSACTION_ID));
                mIn.put(MERCHANT_TRANSACTION_REF, bodyJson.getString(MERCHANT_TRANSACTION_REF) == null ? "" : bodyJson.getString(MERCHANT_TRANSACTION_REF));
                mIn.put(AUTHORISATION_CODE, bodyJson.getString(AUTHORISATION_CODE) == null ? "" : bodyJson.getString(AUTHORISATION_CODE).replace(" ", ","));
                mIn.put(INSTALLMENT_BANK, bodyJson.getString(INSTALLMENT_BANK) == null ? "" : bodyJson.getString(INSTALLMENT_BANK));
                mIn.put(INSTALLMENT_STATUS, bodyJson.getString(INSTALLMENT_STATUS) == null ? "" : bodyJson.getString(INSTALLMENT_STATUS));
                mIn.put(INSTALLMENT_STATE, bodyJson.getString(INSTALLMENT_STATE) == null ? "" : bodyJson.getString(INSTALLMENT_STATE));
                mIn.put(TRANS_STATEMENT_DAY, bodyJson.getString(TRANS_STATEMENT_DAY) == null ? "" : bodyJson.getString(TRANS_STATEMENT_DAY));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));
                mIn.put(CHANNEL, bodyJson.getString(CHANNEL) == null ? "" : bodyJson.getString(CHANNEL));
                mIn.put(REFERRAL_PARTNER, bodyJson.getString(REFERRAL_PARTNER) == null ? "" : bodyJson.getString(REFERRAL_PARTNER));

                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(mIn.get(FROM_DATE).toString());
                    oToDate = df.parse(mIn.get(TO_DATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    mIn.put(FROM_DATE, sdf.format(oFromDate));
                    mIn.put(TO_DATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ DOWNLOAD INTERNATIONAL INSTALLMENT ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }

                if (FunctionUtil.isCardData(mIn.get(CARD_NUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARD_NUMBER).toString());
                    mIn.put(CARD_NUMBER, String.valueOf(cardhash));
                }

                //Lọc gd sát sao kê
                if(mIn.get(TRANS_STATEMENT_DAY).equals("1")){
                    addCloseStmDateConitions(mIn);

                }

                int totalRows = InstallmentDao.getTotalTransaction(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "international_installment_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("international_installment");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INSTALLMENT APPROVAL Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void exportFileBank(RoutingContext ctx) {
        logger.info("exportFileBank() userID:" + ctx.get(S_USER_ID));
        ctx.vertx().executeBlocking(future -> {
            JsonObject result = new JsonObject();
            try {
                JsonArray bodyJsonArray = ctx.getBodyAsJsonArray();
                Integer checkRes = 0;
                //loop theo từng bank
                for(int i = 0; i < bodyJsonArray.size(); i++){
                    String bank = bodyJsonArray.getJsonObject(i).getString("bank");
                    logger.info("bank:" + bank);
                    JsonArray groupItem = bodyJsonArray.getJsonObject(i).getJsonArray("groupItem");
                    logger.info("groupItem: " + groupItem.toString());
                    List<String> vcb_tgnh_ids = new ArrayList<String>();
                    List<String> ids = new ArrayList<String>();
                    for(int j = 0; j < groupItem.size(); j++){
                        //Nếu bank là Vietcombank nếu mid là TGNHMPTG
                        if(bank.equals("Vietcombank") && groupItem.getJsonObject(j).getString("mid").equals("TGNHMPTG")){
                            vcb_tgnh_ids.add(groupItem.getJsonObject(j).getString("id"));
                        }
                        else{
                            ids.add(groupItem.getJsonObject(j).getString("id"));
                        }
                    }

                    if(vcb_tgnh_ids.size() > 0){
                        InstallmentDao.exportInstallmentToBank(bank, String.join(",", vcb_tgnh_ids), true);
                    }

                    String sRes = InstallmentDao.exportInstallmentToBank(bank, String.join(",", ids), false);
                    // Integer n_res = 200;
                    if(("200").equals(sRes)){
                        checkRes++;
                    }
                    else {
                        if (sRes.contains("FILE_CREATING_EXCEPTION")){
                            result.put("message", "File was already sent to bank");
                        } else if (sRes.contains("TRANSACTION_NOT_FOUND_EXCEPTION")) {
                            result.put("message", "Transaction is invalid, can not send to bank");
                        } else {
                            result.put("message", sRes);
                        }
                    }
                }
                //Trả thêm reponse state để frontend bắt lỗi cụ thể
                //vì frontend đang có httpInterceptor xử lý error chung rồi
                //nên ko muốn overide lên đấy.
                
                if(checkRes == bodyJsonArray.size()){
                    result.put("state", "success");
                    sendResponse(ctx, 200, result);
                }
                else{
                    result.put("state", "failed");
                    sendResponse(ctx, 200, result);
                }
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEND FILE BANK INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(500, e);
            }
        }, false, null);
    }

    private static void addCloseStmDateConitions(Map<String, Object> mIn) throws Exception {
        JsonObject sJobData = InstallmentDao.getBankInstallmentData();
        // convert sJobData sang nhóm:
        // bank|cardType|bin1|day1,day2,day3;bank|cardType|bin2|day1,day2 ;
        if (!sJobData.getString("s_job_data").equals("")) {
            JSONObject jJobData = new JSONObject(sJobData.getString("s_job_data").replace("=", ":"));

            // JSONObject jData = new JSONObject(jJobData.getString("data"));
            JSONObject jData = jJobData.getJSONObject("data");
            // logger.info("log jData" + jData);
            JSONArray lstBankData = jData.getJSONArray("banks");
            List<String> groupBank = new ArrayList<String>(), groupCard = new ArrayList<String>(), groupBinDay = new ArrayList<String>();

            Iterator<Object> iteratorBank = lstBankData.iterator();
            while (iteratorBank.hasNext()) {
                JSONObject jBank = (JSONObject) iteratorBank.next();
                for (String bank : jBank.keySet()) {
                    groupBank.add(bank);
                    Iterator<Object> iteratorCardType = jBank.getJSONArray(bank).iterator();
                    // lọc lấy cardType
                    List<String> lstTempCard = new ArrayList<String>();
                    List<String> lstTempBinDayCard = new ArrayList<String>();
                    while (iteratorCardType.hasNext()) {
                        JSONObject jCardType = (JSONObject) iteratorCardType.next();
                        for (String cardType : jCardType.keySet()) {
                            // nếu bank có cardType
                            if (cardType.matches("visa|mastercard|jcb|amex|cup")) {
                                lstTempCard.add(cardType);
                                // lọc lấy binDay
                                // nếu cardType có bin
                                if (jCardType.getJSONObject(cardType).has("bins")) {
                                    List<String> lstTempBinDay = new ArrayList<String>();
                                    for (Object binDay : jCardType.getJSONObject(cardType).getJSONArray("bins")) {
                                        JSONObject jBinDay = (JSONObject) binDay;
                                        if (!jBinDay.has("l")) {
                                            // logger.info("Cau hinh theo DAU BIN:" + " SINGLE DATE");
                                            lstTempBinDay.add(jBinDay.get("b").toString() + "[" + String.join(",", getListBinDayAlert(mIn.get(TO_DATE).toString(), jBinDay.getInt("d"), jBinDay.getInt("o"))) + "]");
                                        } else {
                                            // logger.info("Cau hinh theo DAU BIN:" + " MULTI DATE");
                                            StringBuilder strBd = new StringBuilder();
                                            strBd.append(jBinDay.get("b").toString() + "["); 

                                            JSONArray jArrDateOffset = jBinDay.getJSONArray("l");
                                            Iterator<Object> itDO = jArrDateOffset.iterator();
                                            while (itDO.hasNext()) {
                                                JSONObject jCfg = (JSONObject) itDO.next();
                                                strBd.append(String.join(",", getListBinDayAlert(mIn.get(TO_DATE).toString(), jCfg.getInt("d"), jCfg.getInt("o"))));
                                                strBd.append(",");
                                            }
                                            strBd.deleteCharAt(strBd.length() - 1);     // remove last ","

                                            strBd.append("]");
                                            lstTempBinDay.add(strBd.toString());
                                        }
                                    }
                                    lstTempBinDayCard.add(String.join(";", lstTempBinDay));
                                }
                                // nếu cardType ko có bin
                                else {
                                    // logger.info("Cau hinh theo LOAI THE:" + jCardType);
                                    // logger.info("jCardType cardType" + cardType + jCardType.getJSONObject(cardType));
                                    JSONObject jDateConfig = jCardType.getJSONObject(cardType);
                                    if (!jDateConfig.has("l")) {
                                        // logger.info("Cau hinh theo LOAI THE:" + " SINGLE DATE");
                                        lstTempBinDayCard.add("[" + String.join(",", getListBinDayAlert(mIn.get(TO_DATE).toString(), jDateConfig.getInt("d"), jDateConfig.getInt("o"))) + "]");
                                    } else {
                                        // logger.info("Cau hinh theo LOAI THE:" + " MULTI DATE");
                                        JSONArray jArrDateOffset = jDateConfig.getJSONArray("l");
                                        StringBuilder strBd = new StringBuilder();
                                        strBd.append("[");
                                        Iterator<Object> itDO = jArrDateOffset.iterator();
                                        while (itDO.hasNext()) {
                                            JSONObject jCfg = (JSONObject) itDO.next();
                                            strBd.append(String.join(",",getListBinDayAlert(mIn.get(TO_DATE).toString(), jCfg.getInt("d"), jCfg.getInt("o"))));
                                            strBd.append(",");
                                        }
                                        strBd.deleteCharAt(strBd.length() - 1); // remove last ","

                                        strBd.append("]");
                                        lstTempBinDayCard.add(strBd.toString());
                                    }
                                    
                                }
                            }
                            // nếu bank này ko có cardType
                            else if (cardType.matches("d") || cardType.matches("l")){
                                // logger.info("Bank khong setup card type" + cardType + " ; jCardType= " + jCardType.toString());
                                
                                lstTempCard.add("0");
                                if (!jCardType.has("l")) {
                                    // logger.info("Cau hinh theo BANK:" + " SINGLE DATE");
                                    lstTempBinDayCard.add("[" + String.join(",", getListBinDayAlert(mIn.get(TO_DATE).toString(), jCardType.getInt("d"), jCardType.getInt("o"))) + "]");
                                } else {
                                    // logger.info("Cau hinh theo BANK:" + " MULTI DATE");
                                    JSONArray jArrDateOffset = jCardType.getJSONArray("l");
                                    StringBuilder strBd = new StringBuilder();
                                    strBd.append("[");
                                    Iterator<Object> itDO = jArrDateOffset.iterator();
                                    while (itDO.hasNext()) {
                                        JSONObject jCfg = (JSONObject) itDO.next();
                                        strBd.append(String.join(",",getListBinDayAlert(mIn.get(TO_DATE).toString(), jCfg.getInt("d"), jCfg.getInt("o"))));
                                        strBd.append(",");
                                    }
                                    strBd.deleteCharAt(strBd.length() - 1); // remove last ","

                                    strBd.append("]");
                                    lstTempBinDayCard.add(strBd.toString());
                                }
                                
                            }
                        }
                    }
                    // cả nhóm cardType này thuộc 1 bank
                    groupCard.add(String.join(",", lstTempCard));
                    groupBinDay.add(String.join(".", lstTempBinDayCard));
                }
            }

            // Lấy đc 3 chuỗi group mong muốn
            logger.info("============================== RESULT ==============================") ;
            logger.info("group bank: " + String.join(",", groupBank));
            logger.info("group card type: " + String.join("|", groupCard));
            logger.info("group bin day: " + String.join("|", groupBinDay));
            mIn.put(GROUP_BANK, String.join(",", groupBank));
            mIn.put(GROUP_CARD, String.join("|", groupCard));
            mIn.put(GROUP_BIN_DAY, String.join("|", groupBinDay));
        }
    }

    private static List<String> getListBinDayAlert(String toDate, int d, int offset) throws ParseException {
        List<String> listRet = new ArrayList<>();
        Calendar curTime = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        curTime.setTime(sdf.parse(toDate));
        /* Lay ra ngay sat sao ke dua tren to date search, ngay sao ke, offset bao nhieu ngay truoc sao ke.*/
        Calendar endTime = (Calendar) curTime.clone();
        endTime.set(Calendar.DAY_OF_MONTH, d);
        if (endTime.before(curTime)) {
            endTime.add(Calendar.MONTH, 1);
        }

        Calendar startTime = (Calendar) endTime.clone();
        startTime.add(Calendar.DAY_OF_MONTH, -1 * offset);
        startTime.add(Calendar.DAY_OF_MONTH, -1);
        for(int i = 0 ; i < offset ; i++) {
            startTime.add(Calendar.DAY_OF_MONTH, 1);
            listRet.add(String.valueOf(startTime.get(Calendar.DATE)));
        }
        
        return listRet;
    }

    public static void getAllPartnerIpp(RoutingContext ctx) {

        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, InstallmentDao.getPartnerIpp());

            } catch (Exception e) {
                logger.log(Level.SEVERE, "get Installment Partner Ipp: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}