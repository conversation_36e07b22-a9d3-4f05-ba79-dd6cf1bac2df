package vn.onepay.portal.resources.international.transaction;

import static vn.onepay.portal.Util.sendResponse;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.google.gson.Gson;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentBankDto;
import vn.onepay.portal.resources.international.transaction.dto.InternationalPurchase;
import vn.onepay.portal.resources.international.transaction.dto.InternationalTransactionHashcode;
import vn.onepay.portal.resources.payment_reconciliation.bank_fee_config_3b.dao.PaymentBankFee3BDao;
import vn.onepay.portal.resources.international.transaction.dto.AcquirerDto;
import vn.onepay.portal.MailUtil;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MspClient;

public class InternationalTransactionHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(InternationalTransactionHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void searchInternationalTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();


                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = ctx.request().getParam(FROM_DATE);
                String toDate = ctx.request().getParam(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ SEARCH INTERNATIONAL TRANSACTION ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, "[ SEARCH INTERNATIONAL TRANSACTION ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(KEYWORD, request.getParam(PARTNER_ID) == null ? 0 : Integer.parseInt(request.getParam(PARTNER_ID)));
                mIn.put(ORDER_INFO, request.getParam(ORDER_INFO) == null ? "" : request.getParam(ORDER_INFO));
                mIn.put(TRANSACTION_STATUS, request.getParam(TRANSACTION_STATUS) == null ? "" : request.getParam(TRANSACTION_STATUS));
                mIn.put(CARD_NUMBER, request.getParam(CARD_NUMBER) == null ? "" : request.getParam(CARD_NUMBER));
                mIn.put(CARD_TYPE, request.getParam(CARD_TYPE) == null ? "" : request.getParam(CARD_TYPE));
                mIn.put(CURRENCY, request.getParam(CURRENCY) == null ? "" : request.getParam(CURRENCY));
                mIn.put(TRANSACTION_TYPE, request.getParam(TRANSACTION_TYPE) == null ? "" : request.getParam(TRANSACTION_TYPE));
                mIn.put(ACQUIRER_ID, request.getParam(ACQUIRER_ID) == null ? "" : request.getParam(ACQUIRER_ID));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID) == null ? "" : request.getParam(MERCHANT_ID));
                mIn.put(TRANSACTION_ID, request.getParam(TRANSACTION_ID) == null ? "" : request.getParam(TRANSACTION_ID));
                mIn.put(MERCHANT_TRANSACTION_REF, request.getParam(MERCHANT_TRANSACTION_REF) == null ? "" : request.getParam(MERCHANT_TRANSACTION_REF));
                mIn.put(CUSTOMER_EMAIL, request.getParam(CUSTOMER_EMAIL) == null ? "" : request.getParam(CUSTOMER_EMAIL));
                mIn.put(CUSTOMER_MOBILE, request.getParam(CUSTOMER_MOBILE) == null ? "" : request.getParam(CUSTOMER_MOBILE));
                mIn.put(MERCHANT_WEBSITE, request.getParam(MERCHANT_WEBSITE) == null ? "" : request.getParam(MERCHANT_WEBSITE));
                mIn.put(FRAUD_CHECK, request.getParam(FRAUD_CHECK) == null ? "" : request.getParam(FRAUD_CHECK));
                mIn.put(AUTHORISATION_CODE, request.getParam(AUTHORISATION_CODE) == null ? "" : request.getParam(AUTHORISATION_CODE));
                mIn.put("refundType", request.getParam("refundType") == null ? "" : request.getParam("refundType"));
                mIn.put("contractType", request.getParam("contractType") == null ? "" : request.getParam("contractType"));
                mIn.put(INSTALLMENT_BANK, request.getParam(INSTALLMENT_BANK) == null ? "" : request.getParam(INSTALLMENT_BANK));
                mIn.put(INSTALLMENT_STATUS, request.getParam(INSTALLMENT_STATUS) == null ? "" : request.getParam(INSTALLMENT_STATUS));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 0 : Integer.parseInt(request.getParam(PAGE_SIZE)));
                mIn.put(PAGE, request.getParam(PAGE) == null ? 0 : Integer.parseInt(request.getParam(PAGE)));
                mIn.put(OFFSET, 0);
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put(NETWORK_TRANS_ID, request.getParam(NETWORK_TRANS_ID) == null ? "" : request.getParam(NETWORK_TRANS_ID));
                mIn.put(TOKEN_NUMBER, request.getParam(TOKEN_NUMBER) == null ? "" : request.getParam(TOKEN_NUMBER));
                mIn.put(SOURCE, request.getParam(SOURCE) == null ? "" : request.getParam(SOURCE));

                if (FunctionUtil.isCardData(mIn.get(CARD_NUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARD_NUMBER).toString());
                    mIn.put(CARD_NUMBER, String.valueOf(cardhash));
                }

                sendResponse(ctx, 200, InternationalTransactionDao.search(mIn));


            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getInternationalTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, InternationalTransactionDao.getTransaction(id));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void queryQtRefundTxn(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String transIds = ctx.request().getParam(TRANS_IDS);
                List<Object> result = new ArrayList<Object>();
                //Lấy list transref và hashcode từ transId
                List<InternationalTransactionHashcode> lstTransHash = InternationalTransactionDao.getListTransactionByIds(transIds);
                lstTransHash.forEach(value -> {
                    Map<String, Object> req = new HashMap<>();
                    Map<String, String> vpc = new HashMap<>();
                    req.put("command_id", "CONFIRM_REFUND");
                    vpc.put("vpc_Merchant", value.get_merchant_id());
                    vpc.put("vpc_MerchTxnRef", value.getTransref());
                    vpc.put("vpc_Command", "queryRefundPurchase");
                    vpc.put("vpc_Version", "2");
                    try {
                        vpc.put("vpc_SecureHash", Util.createMerchantHash(vpc, value.getHashcode(), "2"));
                        req.put("vpc", vpc);
                        String stringResult = MspClient.queryQtRefundTxn(req, vpc,  value.get_merchant_id(),value.getTransref());
                        Map<String,String> mapRes = new HashMap<String,String>();
                        String[] entries = stringResult.split("&");
                        for (String entry : entries) {
                            String[] keyValue = entry.split("=");
                            mapRes.put(keyValue[0],keyValue[1]);
                        }
                        mapRes.put("transId", value.getN_id().toString());
                        result.add(mapRes);
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        logger.log(Level.WARNING, "CREATE HASH FAILED: ", e);
                        ctx.fail(e);
                    }

                });
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getInternationalPurchase(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, InternationalTransactionDao.getPurchase(id));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateInternationalPurchaseStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String pattern = "###,###";
                DecimalFormat decimalFormat = new DecimalFormat(pattern);
                HttpServerRequest request = ctx.request();
                String transactionId = request.getParam("transaction_id");
                String status = request.getParam(RESPONSE);
                String merchantId = request.getParam(MERCHANT_ID);
                String orderInfo = request.getParam("order_info");
                String amount = request.getParam("amount");
                String currency = request.getParam("currency");
                String transactionNumber = request.getParam("transaction_number");
                String previousStatus = request.getParam("previous_response_code");
                String transactionTime = request.getParam("transaction_date");
                StringBuilder subject = new StringBuilder("CẢNH BÁO THAY ĐỔI TRẠNG THÁI GIAO DỊCH ").append(transactionId);
                String host = Config.getString("email.host", "");
                Integer port = Config.getInteger("email.port", 25);
                String username = Config.getString("email.username", "");
                String password = Config.getString("email.password", "");
                String fromEmail = Config.getString("email.address", "");
                StringBuilder body = new StringBuilder("Dear Payment Team,<br>");
                body.append("Giao dịch quốc tế sau đây đã được thay đổi trạng thái:<br>");
                body.append("Merchant ID: ").append(merchantId).append("<br>");
                body.append("Transaction ID: ").append(transactionId).append("<br>");
                body.append("Order Info: ").append(orderInfo).append("<br>");
                body.append("Transaction Type: Purchase<br>");
                body.append("Amount: ").append(decimalFormat.format(Double.valueOf(amount))).append(" ").append(currency).append("<br>");
                body.append("Transaction Number: ").append(transactionNumber).append("<br>");
                body.append("Transaction Date: ").append(transactionTime).append("<br>");
                body.append("Response Code: ").append(previousStatus).append(" to ").append(status).append("<br>");
                String toEmail = "<EMAIL>";
                MailUtil.sendMail(host, port, username, password, fromEmail, toEmail, toEmail, subject.toString(), body.toString());
                sendResponse(ctx, 200, InternationalTransactionDao.updatePurchaseStatus(transactionId, status));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateInternationalRefundStatus(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String pattern = "###,###";
                DecimalFormat decimalFormat = new DecimalFormat(pattern);
                HttpServerRequest request = ctx.request();
                String transactionId = request.getParam("transaction_id");
                String status = request.getParam(RESPONSE);
                String merchantId = request.getParam(MERCHANT_ID);
                String orderInfo = request.getParam("order_info");
                String amount = request.getParam("amount");
                String currency = request.getParam("currency");
                String transactionNumber = request.getParam("transaction_number");
                String previousStatus = request.getParam("previous_response_code");
                String transactionTime = request.getParam("transaction_date");
                String originalId = request.getParam("original_id");
                StringBuilder subject = new StringBuilder("CẢNH BÁO THAY ĐỔI TRẠNG THÁI GIAO DỊCH ").append(transactionId);
                String host = Config.getString("email.host", "");
                Integer port = Config.getInteger("email.port", 25);
                String username = Config.getString("email.username", "");
                String password = Config.getString("email.password", "");
                String fromEmail = Config.getString("email.address", "");
                InternationalPurchase tran = InternationalTransactionDao.getPurchase(originalId);
                StringBuilder body = new StringBuilder("Dear Payment Team,<br><br>");
                body.append("Giao dịch quốc tế sau đây đã được thay đổi trạng thái:<br>");
                body.append("Merchant ID: ").append(merchantId).append("<br>");
                body.append("Transaction ID: ").append(transactionId).append("<br>");
                body.append("Order Info: ").append(orderInfo).append("<br>");
                body.append("Transaction Type: Refund<br>");
                body.append("Amount: ").append(decimalFormat.format(Double.valueOf(amount))).append(" ").append(currency).append("<br>");
                body.append("Transaction Number: ").append(transactionNumber).append("<br>");
                body.append("Transaction Date: ").append(transactionTime).append("<br>");
                body.append("Response Code: ").append(previousStatus).append(" to ").append(status).append("<br><br>");
                body.append("Thông tin giao dịch gốc:<br>");
                body.append("Transaction ID: ").append(originalId).append("<br>");
                body.append("Transaction Type: Purchase<br>");
                body.append("Amount: ").append(decimalFormat.format(tran.getAmount().getTotal())).append(" ").append(currency).append("<br>");
                body.append("Transaction Number: ").append(tran.getTransaction_ref_number()).append("<br>");
                String toEmail = "<EMAIL>";
                MailUtil.sendMail(host, port, username, password, fromEmail, toEmail, toEmail, subject.toString(), body.toString());
                sendResponse(ctx, 200, InternationalTransactionDao.updateRefundStatus(transactionId, status));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }


    public static void getInternationalTransactionHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {

                String id = ctx.request().getParam(ID);

                sendResponse(ctx, 200, new BaseList<>(InternationalTransactionDao.listHistory(id)));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadInternationalTransaction(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                mIn.put(ORDER_INFO, bodyJson.getString(ORDER_INFO) == null ? "" : bodyJson.getString(ORDER_INFO));
                mIn.put(TRANSACTION_STATUS, bodyJson.getString(TRANSACTION_STATUS) == null ? "" : bodyJson.getString(TRANSACTION_STATUS));
                mIn.put(CARD_NUMBER, bodyJson.getString(CARD_NUMBER) == null ? "" : bodyJson.getString(CARD_NUMBER));
                mIn.put(CARD_TYPE, bodyJson.getString(CARD_TYPE) == null ? "" : bodyJson.getString(CARD_TYPE));
                mIn.put(CURRENCY, bodyJson.getString(CURRENCY) == null ? "" : bodyJson.getString(CURRENCY));
                mIn.put("refundType", bodyJson.getString("refundType") == null ? "" : bodyJson.getString("refundType"));
                mIn.put(TRANSACTION_TYPE, bodyJson.getString(TRANSACTION_TYPE) == null ? "" : bodyJson.getString(TRANSACTION_TYPE));
                mIn.put(ACQUIRER_ID, bodyJson.getString(ACQUIRER_ID) == null ? "" : bodyJson.getString(ACQUIRER_ID));
                mIn.put(MERCHANT_ID, bodyJson.getString(MERCHANT_ID) == null ? "" : bodyJson.getString(MERCHANT_ID));
                mIn.put(TRANSACTION_ID, bodyJson.getString(TRANSACTION_ID) == null ? "" : bodyJson.getString(TRANSACTION_ID));
                mIn.put(MERCHANT_TRANSACTION_REF, bodyJson.getString(MERCHANT_TRANSACTION_REF) == null ? "" : bodyJson.getString(MERCHANT_TRANSACTION_REF));
                mIn.put(CUSTOMER_EMAIL, bodyJson.getString(CUSTOMER_EMAIL) == null ? "" : bodyJson.getString(CUSTOMER_EMAIL));
                mIn.put(CUSTOMER_MOBILE, bodyJson.getString(CUSTOMER_MOBILE) == null ? "" : bodyJson.getString(CUSTOMER_MOBILE));
                mIn.put(MERCHANT_WEBSITE, bodyJson.getString(MERCHANT_WEBSITE) == null ? "" : bodyJson.getString(MERCHANT_WEBSITE));
                mIn.put(FRAUD_CHECK, bodyJson.getString(FRAUD_CHECK) == null ? "" : bodyJson.getString(FRAUD_CHECK));
                mIn.put(AUTHORISATION_CODE, bodyJson.getString(AUTHORISATION_CODE) == null ? "" : bodyJson.getString(AUTHORISATION_CODE));
                mIn.put(INSTALLMENT_BANK, bodyJson.getString(INSTALLMENT_BANK) == null ? "" : bodyJson.getString(INSTALLMENT_BANK));
                mIn.put(INSTALLMENT_STATUS, bodyJson.getString(INSTALLMENT_STATUS) == null ? "" : bodyJson.getString(INSTALLMENT_STATUS));
                mIn.put("contractType", bodyJson.getString("contractType") == null ? "" : bodyJson.getString("contractType"));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));
                mIn.put(NETWORK_TRANS_ID, bodyJson.getString(NETWORK_TRANS_ID) == null ? "" : bodyJson.getString(NETWORK_TRANS_ID));
                mIn.put(TOKEN_NUMBER, bodyJson.getString(TOKEN_NUMBER) == null ? "" : bodyJson.getString(TOKEN_NUMBER));
                mIn.put(SOURCE, bodyJson.getString(SOURCE) == null ? "" : bodyJson.getString(SOURCE));
                mIn.put(OFFSET, 0);

                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(mIn.get(FROM_DATE).toString());
                    oToDate = df.parse(mIn.get(TO_DATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    mIn.put(FROM_DATE, sdf.format(oFromDate));
                    mIn.put(TO_DATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ DOWNLOAD INTERNATIONAL TRANSACTION ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                if (FunctionUtil.isCardData(mIn.get(CARD_NUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARD_NUMBER).toString());
                    mIn.put(CARD_NUMBER, String.valueOf(cardhash));
                }

                int totalRows = InternationalTransactionDao.getTotalTransaction(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                String date = new SimpleDateFormat("yyyyMMddHHmmss").format(new java.util.Date());
                String fileName = "international_card_transactions_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("international_transaction");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(gson.toJson(mIn));
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchInstallmentBankReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();


                DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
                String fromDate = ctx.request().getParam(FROM_DATE);
                String toDate = ctx.request().getParam(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("type_filter", Integer.parseInt(request.getParam("type_filter")));
                mIn.put("list_bank", request.getParam("list_bank"));
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put("interval", request.getParam("interval") == null || request.getParam("interval").equals("undefined") ? 1 : request.getParam("interval"));
                // mIn.put(PAGE, request.getParam(PAGE));
                // mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 0 :
                // Integer.parseInt(request.getParam(PAGE_SIZE)));

                sendResponse(ctx, 200, InternationalTransactionDao.searchInstallmentBankReportCommon(mIn));

            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchInstallmentMerchantReport(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();


                DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
                String fromDate = ctx.request().getParam(FROM_DATE);
                String toDate = ctx.request().getParam(TO_DATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, "[ FINANCIAL TRANSACTION GET ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                Map<String, Object> mIn = new HashMap<>();
                mIn.put("type_filter", Integer.parseInt(request.getParam("type_filter")));
                mIn.put(MERCHANT_ID, request.getParam(MERCHANT_ID).length() == 0 || request.getParam(MERCHANT_ID).equals("undefined") ? null : request.getParam(MERCHANT_ID));
                mIn.put("list_bank", request.getParam("list_bank"));
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);
                mIn.put("interval", request.getParam("interval") == null || request.getParam("interval").equals("undefined") ? 1 : request.getParam("interval"));
                mIn.put("referral_partner", request.getParam("referral_partner") == null ? "" : request.getParam("referral_partner"));
                // mIn.put(PAGE, request.getParam(PAGE));
                // mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE) == null ? 0 :
                // Integer.parseInt(request.getParam(PAGE_SIZE)));

                sendResponse(ctx, 200, InternationalTransactionDao.searchInstallmentMerchantReportCommon2(mIn));

            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadInstallmentBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                Map<String, Object> mIn = new HashMap<>();

                mIn.put("type_filter", bodyJson.getString("type_filter") == null ? "" : bodyJson.getString("type_filter"));
                mIn.put("list_bank", bodyJson.getString("list_bank") == null ? "" : bodyJson.getString("list_bank"));
                mIn.put("interval", bodyJson.getString("interval") == null ? "" : bodyJson.getString("interval"));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));

                // mIn.put(PAGE, 0);
                // mIn.put(PAGE_SIZE, Integer.MAX_VALUE);

                int quantityApproved = 0, quantityReject = 0;
                double totalAmountApproved = 0.0, totalAmountReject = 0.0;
                List<InstallmentBankDto> result = InternationalTransactionDao.searchInstallmentBankReport(mIn);
                for (InstallmentBankDto data : result) {
                    quantityApproved = quantityApproved + data.getQuantity_approved_trans();
                    totalAmountApproved = totalAmountApproved + data.getTotal_approved_trans();
                    quantityReject = quantityReject + data.getQuantity_reject_trans();
                    totalAmountReject = totalAmountReject + data.getTotal_reject_trans();
                }

                mIn.put("quantityApproved", quantityApproved);
                mIn.put("totalAmountApproved", totalAmountApproved);
                mIn.put("quantityReject", quantityReject);
                mIn.put("totalAmountReject", totalAmountReject);

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = InternationalTransactionDao.getTotalInstallmentBankReport(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "installment_bank_report" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("installment_bank_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadInstallmentMerchant(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                Map<String, Object> mIn = new HashMap<>();
                mIn.put(MERCHANT_ID, bodyJson.getString(MERCHANT_ID).length() == 0 || bodyJson.getString(MERCHANT_ID).equals("undefined") ? null : bodyJson.getString(MERCHANT_ID));
                mIn.put("type_filter", bodyJson.getString("type_filter") == null ? "" : bodyJson.getString("type_filter"));
                mIn.put("list_bank", bodyJson.getString("list_bank") == null ? "" : bodyJson.getString("list_bank"));
                mIn.put("interval", bodyJson.getString("interval") == null ? "" : bodyJson.getString("interval"));
                mIn.put("referral_partner", bodyJson.getString("referral_partner") == null ? "" : bodyJson.getString("referral_partner"));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));

                mIn.put(PAGE, 0);
                mIn.put(PAGE_SIZE, Integer.MAX_VALUE);
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = InternationalTransactionDao.getTotalInstallmentMerchantReport(mIn);
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "temp_installment_merchant_report" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("installment_merchant_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xls");
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAuthorizeTransactionHistory(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String id = ctx.request().getParam(TRANSACTIONID);

                sendResponse(ctx, 200, InternationalTransactionDao.authorizeTransactionHistory(id));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAcquirer(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String tmpPayChannel = ctx.request().getParam(PAYCHANNEL);
                String payChannel = StringUtils.isBlank(tmpPayChannel) ? "QT" : tmpPayChannel;
                List<AcquirerDto> acquirerDb = InternationalTransactionDao.getAcquirers(payChannel);

                sendResponse(ctx, 200, acquirerDb);
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET ACQUIRER BY CHANNEL ERROR : ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
