package vn.onepay.portal.resources.international.report;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;

public class TransactionReportDao extends Db {
    private static final String SEARCH_TRANSACTION_REPORT = "{ call onereport.PKG_IPORTAL_INTER_REPORT.search_report_v3(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String PAYMENT_SEARCH_BIN_BANK = "{ call onereport.PKG_IPORTAL_TRANS_ACCOUNTING.search_bin_bank(?,?,?)}";
    private static final String GET_RESPONSE_CODE = "{ call onereport.PKG_IPORTAL_INTER_REPORT.get_response_code(?,?,?)}";
    private static final String BANK_MERCHANT_ID_BY_ACQ = "{ call onereport.PKG_IPORTAL_INTER_REPORT.bank_merchant_id_by_acq(?,?,?,?)}";
    private static final Logger LOGGER = Logger.getLogger(TransactionReportDao.class.getName());

    public static List<Map> searchBinBank(Map<String, Object> mIn) throws Exception {
        Exception exception = null;
        List<Map> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(PAYMENT_SEARCH_BIN_BANK);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception("DB ONEREPORT.PKG_IPORTAL_TRANS_ACCOUNTING.search_bin_bank: " + sResult);
            } else {
                // Map<String, Object> map;
                Map<String, Object> dataVN = new HashMap<>();
                dataVN.put("label", "VIET NAM");
                dataVN.put("value", "VIET NAM");
                ArrayList itemsVN = new ArrayList<>();
                Map<String, Object> mapVN;

                Map<String, Object> dataNC = new HashMap<>();
                dataNC.put("label", "NUOC NGOAI");
                dataNC.put("value", "NUOC NGOAI");
                ArrayList itemsNC = new ArrayList<>();
                Map<String, Object> mapNC;
                while (rs != null && rs.next()) {
                    if ("NUOC NGOAI".equals(Util.getColumnString(rs, "S_BIN_COUNTRY"))) {
                        mapNC = new HashMap<>();
                        mapNC.put("label", Util.getColumnString(rs, "s_bin_bank"));
                        mapNC.put("value", Util.getColumnString(rs, "s_bin_bank"));
                        itemsNC.add(mapNC);
                    } else {
                        mapVN = new HashMap<>();
                        mapVN.put("label", Util.getColumnString(rs, "s_bin_bank"));
                        mapVN.put("value", Util.getColumnString(rs, "s_bin_bank"));
                        itemsVN.add(mapVN);
                    }
                }
                dataVN.put("items", itemsVN);
                dataNC.put("items", itemsNC);
                list.add(dataVN);
                list.add(dataNC);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return list;
    }

    public static List<Map<String, Object>> getResponseCode() throws Exception {
        Exception exception = null;
        List<Map<String, Object>> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(GET_RESPONSE_CODE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);

            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception("DB ONEREPORT.PKG_IPORTAL_TRANS_ACCOUNTING.search_bin_bank: " + sResult);
            } else {

                Map<String, Object> mapRc;
                while (rs != null && rs.next()) {
                    mapRc = new HashMap<>();
                    mapRc.put("label", Util.getColumnString(rs, "S_DESC"));
                    mapRc.put("value", Util.getColumnString(rs, "S_ID"));
                    list.add(mapRc);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return list;
    }

    public static List<Map<String, Object>> searchBankMerchantIdByAcq(String acq) throws Exception {
        Exception exception = null;
        List<Map<String, Object>> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;

        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(BANK_MERCHANT_ID_BY_ACQ);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, acq);
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception("DB ONEREPORT.PKG_IPORTAL_TRANS_ACCOUNTING.search_bin_bank: " + sResult);
            } else {

                Map<String, Object> mapRc;
                while (rs != null && rs.next()) {
                    mapRc = new HashMap<>();
                    mapRc.put("label", Util.getColumnString(rs, "S_BANK_MERCHANT_ID"));
                    mapRc.put("value", Util.getColumnString(rs, "S_BANK_MERCHANT_ID"));
                    list.add(mapRc);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;

        return list;
    }

    public static InterReportResponse list(Map<String, String> mIn) throws Exception {
        InterReportResponse response = new InterReportResponse();
        List<InterReportDto> dataReports = searchTransaction(mIn);
        long totalTxn = dataReports.stream().filter(o -> o.getTotalTxnAmount() != null).mapToLong(InterReportDto::getTotalTxn).sum();
        double totalAmount = dataReports.stream().filter(o -> o.getTotalTxnAmount() != null).mapToDouble(InterReportDto::getTotalTxnAmount).sum();
        double totalApproAmount = dataReports.stream().filter(o -> o.getTotalItaApprovalAmount() != null).mapToDouble(InterReportDto::getTotalItaApprovalAmount).sum();
        Map<String, Long> totalByCurrency = dataReports.stream()
        .filter(o -> o.getTotalTxn() != null && o.getCurrency() != null)
        .collect(Collectors.groupingBy(
                InterReportDto::getCurrency,
                Collectors.summingLong(InterReportDto::getTotalTxn)
        ));
        Map<String, Double> totalAmountByCurrency = dataReports.stream()
        .filter(o -> o.getTotalTxnAmount() != null && o.getCurrency() != null)
        .collect(Collectors.groupingBy(
                InterReportDto::getCurrency,
                Collectors.summingDouble(InterReportDto::getTotalTxnAmount)
        ));
        Map<String, Double> sortedMap = totalAmountByCurrency.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1, // Keep the existing value if keys are the same
                        LinkedHashMap::new // Preserve the order
        ));

        Map<String, Double> totalApproAmountByCurrency = dataReports.stream()
        .filter(o -> o.getTotalItaApprovalAmount() != null && o.getCurrency() != null)
        .collect(Collectors.groupingBy(
                InterReportDto::getCurrency,
                Collectors.summingDouble(InterReportDto::getTotalItaApprovalAmount)
        ));
        response.setData(dataReports);
        response.setTotalTxn(totalTxn);
        response.setTotalAmount(totalAmount);
        response.setTotalBankItaAmount(totalApproAmount);
        response.setTotalByCurrency(totalByCurrency);
        response.setTotalAmountByCurrency(sortedMap);
        response.setTotalApproAmountByCurrency(totalApproAmountByCurrency);
        return response;
    }

    public static Integer getTotalTrans(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(6, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(7, mIn.getOrDefault("excludeVoid", ""));
            cs.setString(8, mIn.getOrDefault("excludeAutho", ""));
            cs.setString(9, mIn.getOrDefault(MERCHANTID, ""));
            cs.setString(10, mIn.getOrDefault(PARTNERTID, ""));
            cs.setString(11, mIn.getOrDefault(ACQUIRERID, ""));
            cs.setString(12, mIn.getOrDefault("bankMerchantId", ""));
            cs.setString(13, mIn.getOrDefault("source", ""));
            cs.setString(14, mIn.getOrDefault("binCountry", ""));
            cs.setString(15, mIn.getOrDefault("binBank", ""));
            cs.setString(16, mIn.getOrDefault("itaBank", ""));
            cs.setString(17, mIn.getOrDefault("itaPerio", ""));
            cs.setString(18, mIn.getOrDefault("transType", ""));
            cs.setString(19, mIn.getOrDefault("cardType", ""));
            cs.setString(20, mIn.getOrDefault("contractType", ""));
            cs.setString(21, mIn.getOrDefault("currency", ""));
            cs.setString(22, mIn.getOrDefault("platform", ""));
            cs.setString(23, mIn.getOrDefault("status", ""));
            cs.setString(24, mIn.getOrDefault("response_code", ""));
            cs.setString(25, mIn.getOrDefault(INTERVAL, "DD"));
            cs.setString(26, mIn.getOrDefault("advType", ""));
            cs.setString(27, mIn.getOrDefault("other", ""));
            cs.setInt(28, Integer.parseInt(mIn.getOrDefault(PAGESIZE, "100")));
            cs.setInt(29, Integer.parseInt(mIn.getOrDefault(PAGE, "0")));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            if (nError != 200) {
                throw new Exception("DB onereport.PKG_IPORTAL_TRANS_ACCOUNTING.search_trs_merchant_acc: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "INTERNATIONAL REPORT TOTAL ERROR", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    public static List<InterReportDto> searchTransaction(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<InterReportDto> reports = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(6, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(7, mIn.getOrDefault("excludeVoid", ""));
            cs.setString(8, mIn.getOrDefault("excludeAutho", ""));
            cs.setString(9, mIn.getOrDefault(MERCHANTID, ""));
            cs.setString(10, mIn.getOrDefault(PARTNERTID, ""));
            cs.setString(11, mIn.getOrDefault(ACQUIRERID, ""));
            cs.setString(12, mIn.getOrDefault("bankMerchantId", ""));
            cs.setString(13, mIn.getOrDefault("source", ""));
            cs.setString(14, mIn.getOrDefault("binCountry", ""));
            cs.setString(15, mIn.getOrDefault("binBank", ""));
            cs.setString(16, mIn.getOrDefault("itaBank", ""));
            cs.setString(17, mIn.getOrDefault("itaPerio", ""));
            cs.setString(18, mIn.getOrDefault("transType", ""));
            cs.setString(19, mIn.getOrDefault("cardType", ""));
            cs.setString(20, mIn.getOrDefault("contractType", ""));
            cs.setString(21, mIn.getOrDefault("currency", ""));
            cs.setString(22, mIn.getOrDefault("platform", ""));
            cs.setString(23, mIn.getOrDefault("status", ""));
            cs.setString(24, mIn.getOrDefault("responseCode", ""));
            cs.setString(25, mIn.getOrDefault(INTERVAL, "DD"));
            cs.setString(26, mIn.getOrDefault("advType", ""));
            cs.setString(27, mIn.getOrDefault("other", ""));
            cs.setInt(28, Integer.MAX_VALUE);
            cs.setInt(29, Integer.parseInt(mIn.getOrDefault(PAGE, "0")));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception("DB onereport.PKG_IPORTAL_INTER_REPORT.search_report: " + sResult);
            } else {
                DecimalFormat df = new DecimalFormat("0.##########");
                InterReportDto report;
                while (rs != null && rs.next()) {
                    report = new InterReportDto();
                    report.setDateType(Util.getColumnString(rs, "S_DATE_TYPE"));
                    report.setContractType(Util.getColumnString(rs, "S_CONTRACT_TYPE"));
                    report.setAcquirer(Util.getColumnString(rs, "N_ACQUIRER_ID"));
                    report.setAcquirerName(Util.getColumnString(rs, "S_ACQUIRER_NAME"));
                    report.setBankMerchantId(Util.getColumnString(rs, "S_BANK_MERCHANT_ID"));
                    report.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID"));
                    report.setMerchantName(Util.getColumnString(rs, "S_MERCHANT_NAME"));
                    String partner = (Util.getColumnString(rs, "N_PARTNER_ID") != null || Util.getColumnString(rs, "S_PARTNER_NAME") != null) ? Util.getColumnString(rs, "N_PARTNER_ID") + " - " + (Util.getColumnString(rs, "S_PARTNER_NAME") != null ? Util.getColumnString(rs, "S_PARTNER_NAME") : "") : "";
                    report.setPartnerId(partner);
                    report.setPartnerName(Util.getColumnString(rs, "S_PARTNER_NAME"));
                    report.setBinCountry(Util.getColumnString(rs, "S_BIN_COUNTRY"));
                    report.setCardType(Util.getColumnString(rs, "S_CARD_TYPE"));
                    report.setItaTime(Util.getColumnString(rs, "S_ITA_TIME"));
                    report.setItaBank(Util.getColumnString(rs, "S_ITA_BANK"));
                    report.setCurrency(Util.getColumnString(rs, "S_CURRENCY"));
                    report.setTransType(Util.getColumnString(rs, "S_TRANSACTION_TYPE"));
                    report.setResponseCode(Util.convertResponsecodeString(Util.getColumnString(rs, "S_RESPONSE_CODE")));
                    report.setAdvType(Util.getColumnString(rs, "S_TYPE_ADVANCE"));
                    report.setPlatForm(Util.getColumnString(rs, "S_PLATFORM"));
                    report.setSource(Util.getColumnString(rs, "S_SOURCE"));
                    report.setTotalTxn(Util.getColumnLong(rs, "N_TOTAL"));
                    report.setTotalTxnAmount(Util.getColumnDouble(rs, "N_AMOUNT"));
                    report.setTotalItaApprovalAmount(Util.getColumnDouble(rs, "N_BANK_ITA_AMOUNT"));

                    reports.add(report);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "INTERNATIONAL REPORT ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return reports;
    }

    public static List<Map<String, Object>> searchSumTotal(Map<String, String> mIn) throws Exception {
        Exception exception = null;
        List<Map<String, Object>> maps = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        String sResult = null;
        Integer nResult = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "SUM");
            cs.setString(5, mIn.getOrDefault(FROM_DATE, ""));
            cs.setString(6, mIn.getOrDefault(TO_DATE, ""));
            cs.setString(7, mIn.getOrDefault("excludeVoid", ""));
            cs.setString(8, mIn.getOrDefault("excludeAutho", ""));
            cs.setString(9, mIn.getOrDefault(MERCHANTID, ""));
            cs.setString(10, mIn.getOrDefault(PARTNERTID, ""));
            cs.setString(11, mIn.getOrDefault(ACQUIRERID, ""));
            cs.setString(12, mIn.getOrDefault("bankMerchantId", ""));
            cs.setString(13, mIn.getOrDefault("source", ""));
            cs.setString(14, mIn.getOrDefault("binCountry", ""));
            cs.setString(15, mIn.getOrDefault("binBank", ""));
            cs.setString(16, mIn.getOrDefault("itaBank", ""));
            cs.setString(17, mIn.getOrDefault("itaPerio", ""));
            cs.setString(18, mIn.getOrDefault("transType", ""));
            cs.setString(19, mIn.getOrDefault("cardType", ""));
            cs.setString(20, mIn.getOrDefault("contractType", ""));
            cs.setString(21, mIn.getOrDefault("currency", ""));
            cs.setString(22, mIn.getOrDefault("platform", ""));
            cs.setString(23, mIn.getOrDefault("status", ""));
            cs.setString(24, mIn.getOrDefault("responseCode", ""));
            cs.setString(25, mIn.getOrDefault(INTERVAL, "DD"));
            cs.setString(26, mIn.getOrDefault("advType", ""));
            cs.setString(27, mIn.getOrDefault("other", ""));
            cs.setInt(28, Integer.parseInt(mIn.getOrDefault(PAGE_SIZE, "100")));
            cs.setInt(29, Integer.parseInt(mIn.getOrDefault(PAGE, "0")));
            cs.execute();
            rs = (ResultSet) cs.getObject(1);
            nResult = cs.getInt(2);
            sResult = cs.getString(3);
            if (nResult != 200) {
                throw new Exception("DB onereport.PKG_IPORTAL_INTER_REPORT.search_report: " + sResult);
            } else {
                Map<String, Object> map;
                while (rs != null && rs.next()) {
                    map = new HashMap<>();
                    map.put("totalAmount", Util.getColumnLong(rs, "TOTAL_AMOUNT"));
                    map.put("totalBankItaAmount", Util.getColumnLong(rs, "TOTAL_BANK_ITA_AMOUNT"));
                    map.put("totalTxn", Util.getColumnLong(rs, "TOTALTXN"));
                    maps.add(map);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "INTERNATIONAL REPORT SUM ERROR ", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return maps;
    }

}
