package vn.onepay.portal.resources.international.report;

public class InterReportDto {
    // private Timestamp date;
    private String dateType;
    private String contractType;
    private String acquirer;
    private String acquirerName;
    private String bankMerchantId;
    private String merchantId;
    private String merchantName;
    private String partnerId;
    private String partnerName;
    private String binCountry;
    private String cardType;
    private String itaBank;
    private String itaTime;
    private String currency;
    private String transType;
    private String responseCode;
    private long totalTxn;

    private Double totalTxnAmount;
    private Double totalItaApprovalAmount;
    private String advType;
    private String platForm;
    private String source;


    /**
     * @return String return the dateType
     */
    public String getDateType() {
        return dateType;
    }

    /**
     * @param dateType the dateType to set
     */
    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    /**
     * @return String return the contractType
     */
    public String getContractType() {
        return contractType;
    }

    /**
     * @param contractType the contractType to set
     */
    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return String return the acquirer
     */
    public String getAcquirer() {
        return acquirer;
    }

    /**
     * @param acquirer the acquirer to set
     */
    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getAcquirerName() {
        return acquirerName;
    }

    public void setAcquirerName(String acquirerName) {
        this.acquirerName = acquirerName;
    }

    /**
     * @return String return the bankMerchantId
     */
    public String getBankMerchantId() {
        return bankMerchantId;
    }

    /**
     * @param bankMerchantId the bankMerchantId to set
     */
    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }

    /**
     * @return String return the merchantId
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * @param merchantId the merchantId to set
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * @return String return the merchantName
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * @param merchantName the merchantName to set
     */
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    /**
     * @return String return the partnerId
     */
    public String getPartnerId() {
        return partnerId;
    }

    /**
     * @param partnerId the partnerId to set
     */
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    /**
     * @return String return the partnerName
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     * @param partnerName the partnerName to set
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * @return String return the binCountry
     */
    public String getBinCountry() {
        return binCountry;
    }

    /**
     * @param binCountry the binCountry to set
     */
    public void setBinCountry(String binCountry) {
        this.binCountry = binCountry;
    }

    /**
     * @return String return the cardType
     */
    public String getCardType() {
        return cardType;
    }

    /**
     * @param cardType the cardType to set
     */
    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    /**
     * @return String return the itaBank
     */
    public String getItaBank() {
        return itaBank;
    }

    /**
     * @param itaBank the itaBank to set
     */
    public void setItaBank(String itaBank) {
        this.itaBank = itaBank;
    }

    /**
     * @return String return the itaTime
     */
    public String getItaTime() {
        return itaTime;
    }

    /**
     * @param itaTime the itaTime to set
     */
    public void setItaTime(String itaTime) {
        this.itaTime = itaTime;
    }

    /**
     * @return String return the currency
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * @param currency the currency to set
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * @return String return the transType
     */
    public String getTransType() {
        return transType;
    }

    /**
     * @param transType the transType to set
     */
    public void setTransType(String transType) {
        this.transType = transType;
    }

    /**
     * @return String return the responseCode
     */
    public String getResponseCode() {
        return responseCode;
    }

    /**
     * @param responseCode the responseCode to set
     */
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * @return long return the totalTxn
     */
    public Long getTotalTxn() {
        return totalTxn;
    }

    /**
     * @param totalTxn the totalTxn to set
     */
    public void setTotalTxn(long totalTxn) {
        this.totalTxn = totalTxn;
    }

    /**
     * @return long return the totalTxnAmount
     */
    public Double getTotalTxnAmount() {
        return totalTxnAmount;
    }

    /**
     * @param totalTxnAmount the totalTxnAmount to set
     */
    public void setTotalTxnAmount(Double totalTxnAmount) {
        this.totalTxnAmount = totalTxnAmount;
    }

    /**
     * @return long return the totalItaApprovalAmount
     */
    public Double getTotalItaApprovalAmount() {
        return totalItaApprovalAmount;
    }

    /**
     * @param totalItaApprovalAmount the totalItaApprovalAmount to set
     */
    public void setTotalItaApprovalAmount(Double totalItaApprovalAmount) {
        this.totalItaApprovalAmount = totalItaApprovalAmount;
    }

    /**
     * @return String return the advType
     */
    public String getAdvType() {
        return advType;
    }

    /**
     * @param advType the advType to set
     */
    public void setAdvType(String advType) {
        this.advType = advType;
    }

    /**
     * @return String return the platForm
     */
    public String getPlatForm() {
        return platForm;
    }

    /**
     * @param platForm the platForm to set
     */
    public void setPlatForm(String platForm) {
        this.platForm = platForm;
    }

    /**
     * @return String return the source
     */
    public String getSource() {
        return source;
    }

    /**
     * @param source the source to set
     */
    public void setSource(String source) {
        this.source = source;
    }

}
