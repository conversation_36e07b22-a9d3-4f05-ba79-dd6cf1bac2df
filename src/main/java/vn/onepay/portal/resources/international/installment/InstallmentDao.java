package vn.onepay.portal.resources.international.installment;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.Amount;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.international.installment.dto.InstallmentBank;
import vn.onepay.portal.resources.international.installment.dto.InstallmentTransaction;
import vn.onepay.portal.resources.international.installment.dto.InstallmentTransactionDetail;
import vn.onepay.portal.resources.international.transaction.dto.Acquirer;
import vn.onepay.portal.resources.international.transaction.dto.AuthenticationData;
import vn.onepay.portal.resources.international.transaction.dto.CardDate;
import vn.onepay.portal.resources.international.transaction.dto.InternationalCard;

public class InstallmentDao extends Db {
    private static final String SEARCH_TRANSACTION = "{call pkg_installment_approval.search_tran_installment2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    public static final String GET_BANK_DATA = "{call PKG_INSTALLMENT.get_installment_bank(?,?,?)}";
    public static final String GET_ID_CONFIG_REFUND_ITA = "{call onefin.get_id_config_refund_ita(?,?,?,?,?,?)}";
    public static final String INSERT_TRANSACTION_REFUND_ITA_112 = "{call ONEPARTNER.I_TRANSACTION_REFUND_ITA(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String INSERT_TRANSACTION_REFUND_ITA_118 = "{call ONEFIN.I_TRANSACTION_REFUND_ITA(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String GET_TRANSACTION_DETAIL = "{call pkg_installment_approval.search_ita_trans_by_id(?,?,?,?)}";
    public static final String UPDATE_ITA_STATE = "{call PKG_INSTALLMENT.update_installment_state(?,?,?,?)}";
    public static final String UPDATE_ISM_STATE = "{call PKG_INSTALLMENT.update_ism_state(?,?,?,?,?,?)}";
    public static final String UPDATE_ISM_STATE_2 = "{call PKG_INSTALLMENT.update_ism_state_2(?,?,?,?,?,?)}";
    public static final String UPDATE_ISM_STATE_WITH_REASON = "{call PKG_INSTALLMENT.update_ism_state_with_reason_2(?,?,?,?,?,?,?,?)}";
    public static final String EXPORT_INSTALLMENT_TO_BANK = "{call EXP_INSTALLMENT_TO_BANK2(?,?,?,?)}";
    public static final String EXPORT_INSTALLMENT_VCB_TGNH = "{call exp_vcb_tgnhtg_ita_stm3(?,?)}";
    public static final String GET_PAYMENT_S_DATA = "{call PKG_INSTALLMENT.get_txn_s_data(?,?,?,?)}";
    public static final String UPDATE_IPP_STATE = "{? = call onedata.u_ipp_transaction(?,?,?,?,?,?,?,?)}";
    public static final String GET_PARTNER_IPP = "{call oneportal.g_partner_ipp(?,?,?)}";

    public static BaseList<InstallmentBank> getBank() throws Exception {
        Exception exception = null;
        BaseList<InstallmentBank> result = new BaseList<>();
        List<InstallmentBank> list = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_BANK_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get gte all installment bank error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    list.add(bind(rs));
                }
            }
            result.setList(list);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    /**
     * GET CONFIG ID IN TRANSACTION REFUND ITA
     * Creator: Duynp
     * 
     * @return
     * @throws Exception
     */
    public static List<Map<String, Object>> getIdConfigRefundIta(String merchantId, String currency, String payChannel)
            throws Exception {
        Exception exception = null;
        List<Map<String, Object>> configIdList = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnectionOnefin118();
            cs = con.prepareCall(GET_ID_CONFIG_REFUND_ITA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchantId);
            cs.setString(5, currency);
            cs.setString(6, payChannel);
            cs.execute();
            String s_status = cs.getString(3);
            int n_status = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (n_status != 200) {
                throw new Exception("DB get id config refund ita: " + s_status);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> configAdv = new HashMap<>();
                    configAdv.put(ID, rs.getInt("N_ID"));
                    configIdList.add(configAdv);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return configIdList;
    }

    public static Map<String, Object> insertTransactionRefundIta(JsonObject transRefundIta) throws Exception {
        logger.log(Level.INFO, "start insert transaction refund ita: {0}", transRefundIta.getString(ID));
        Exception exception = null;
        Connection con118 = null;
        Connection con112 = null;
        CallableStatement cs118 = null;
        CallableStatement cs112 = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM d, yyyy, h:mm:ss a");
        Map<String, Object> result = new HashMap<>();
        result.put(CODE, 0);
        result.put(MESSAGE, "");

        try {
            java.util.Date date = new java.util.Date();
            if (transRefundIta.getString(DATE_TRANSACTION) != null) {
                date = dateFormat.parse(transRefundIta.getString(DATE_TRANSACTION));
            }
            // Tạo giao dịch hoàn trả trả góp 118 Onefin
            con118 = getConnectionOnefin118();
            // con118.setAutoCommit(false);
            cs118 = con118.prepareCall(INSERT_TRANSACTION_REFUND_ITA_118);
            cs118.registerOutParameter(1, OracleTypes.NUMBER);
            cs118.registerOutParameter(2, OracleTypes.VARCHAR);
            cs118.registerOutParameter(3, OracleTypes.NUMBER);
            cs118.setString(4, transRefundIta.getString(ID));
            cs118.setString(5, transRefundIta.getString(MERCHANT_ID) != null ? transRefundIta.getString(MERCHANT_ID) : "");
            cs118.setString(6, transRefundIta.getString(CURRENCY) != null ? transRefundIta.getString(CURRENCY) : "");
            cs118.setDouble(7, transRefundIta.getDouble(N_AMOUNT));
            cs118.setDouble(8, transRefundIta.getDouble(AMOUNT_TO_VND));
            cs118.setDouble(9, transRefundIta.getDouble(ITA_FEE_AMOUNT));
            cs118.setInt(10, transRefundIta.getInteger(CONFIG_ADVANCE_ID));
            cs118.setDate(11, transRefundIta.getString(DATE_TRANSACTION) != null ? new java.sql.Date(date.getTime()) : null);
            cs118.setString(12, transRefundIta.getString(ID));
            cs118.setString(13, transRefundIta.getString(STATE));
            cs118.setString(14, transRefundIta.getString(DESC));
            cs118.setString(15, transRefundIta.getString(USER_ID));
            cs118.setString(16, transRefundIta.getString(CARD_NUMBER));
            cs118.setString(17, transRefundIta.getString(CARD_TYPE));
            cs118.execute();
            String s_status_118 = cs118.getString(2);
            int n_status_118 = cs118.getInt(1);
            result.put(CODE, n_status_118);
            result.put(MESSAGE, s_status_118);
            logger.log(Level.INFO, "id: {0}, result: {1}", new Object[] {transRefundIta.getString(ID), result.toString()});
            double itaFee = cs118.getDouble(3);

            // Tạo giao dịch hoàn trả trả góp 112 Onepartner
            con112 = getConnection112();
            // con112.setAutoCommit(false);
            cs112 = con112.prepareCall(INSERT_TRANSACTION_REFUND_ITA_112);
            cs112.registerOutParameter(1, OracleTypes.NUMBER);
            cs112.registerOutParameter(2, OracleTypes.VARCHAR);
            cs112.setString(3, transRefundIta.getString(ID));
            cs112.setString(4, transRefundIta.getString(MERCHANT_ID) != null ? transRefundIta.getString(MERCHANT_ID) : "");
            cs112.setString(5, transRefundIta.getString(CURRENCY) != null ? transRefundIta.getString(CURRENCY) : "");
            cs112.setDouble(6, transRefundIta.getDouble(N_AMOUNT));
            cs112.setDouble(7, transRefundIta.getDouble(AMOUNT_TO_VND));
            cs112.setDouble(8, itaFee);
            cs112.setInt(9, transRefundIta.getInteger(CONFIG_ADVANCE_ID));
            cs112.setDate(10, transRefundIta.getString(DATE_TRANSACTION) != null ? new java.sql.Date(date.getTime()) : null);
            cs112.setString(11, transRefundIta.getString(ID));
            cs112.setString(12, transRefundIta.getString(STATE));
            cs112.setString(13, transRefundIta.getString(DESC));
            cs112.setString(14, transRefundIta.getString(USER_ID));
            cs112.setString(15, transRefundIta.getString(CARD_NUMBER));
            cs112.setString(16, transRefundIta.getString(CARD_TYPE));
            cs112.execute();
            String s_status_112 = cs112.getString(2);
            int n_status_112 = cs112.getInt(1);
            result.put(CODE, n_status_112);
            result.put(MESSAGE, s_status_112);
            logger.log(Level.INFO, "id: {0}, result: {1}", new Object[] {transRefundIta.getString(ID), result.toString()});

            if (n_status_112 != 200 && n_status_112 != 406 && n_status_118 != 200 && n_status_118 != 406) {
                throw new Exception("ERROR DB INSERT TRANSACTION REFUND ITA: 118 - " + s_status_118 + " 112 - " + s_status_112);
            }
            // con118.commit();
            // con112.commit();
        } catch (Exception e) {
            // con118.rollback();
            // con112.rollback();
            logger.log(Level.SEVERE, "ERROR DB INSERT TRANSACTION REFUND ITA: ", e);
            exception = e;
        } finally {
            // con118.setAutoCommit(true);
            // con112.setAutoCommit(true);
            closeConnectionDB(null, null, cs118, con118);
            closeConnectionDB(null, null, cs112, con112);
        }
        if (exception != null) {
            throw exception;
        }
        return result;
    }

    public static int installmentStateReject(String id, String sDate, String amount, String approveDesc)
            throws Exception {
        int code = 200;
        try {
            updateInstallmentState(id, "failed", sDate, amount, approveDesc);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ERROR UPDATE REJECT: ", e);
            code = 500;
        }
        return code;
    }


    public static void installmentStateApprove(String id, String sDate, String amount, String approveDesc)
            throws Exception {
        updateInstallmentState(id, "approved", sDate, amount, approveDesc);
    }

    private static void updateInstallmentState(String id, String state, String sDate, String amount, String approveDesc) throws Exception {
        Map<String, String> paymentData = getPaymentData(id);
        if (paymentData != null) {
            String paymentId = paymentData.get("id");
            String dataStr = paymentData.get("data");
            JsonObject jData = null;
            if (dataStr == null) {
                jData = new JsonObject();
            } else {
                jData = new JsonObject(dataStr);
            }
            logger.info("before data: " + jData.toString());
            jData.put("onepay_ita_date", new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'").format(new Date()));
            logger.info("after data: " + jData.toString());
            updateStateApproveReject(paymentId, state, sDate, amount, approveDesc, jData.toString());
        }
    }

    public static InstallmentTransactionDetail getInstallmentTransDetail(String id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        InstallmentTransactionDetail result = new InstallmentTransactionDetail();
        try {
            con = getConnection118Report();
            cs = con.prepareCall(GET_TRANSACTION_DETAIL);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get update installment status error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = (bindTransactionDetail(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static void updateStateApproveReject(String id, String state, String sDate, String amount,
            String approveDesc, String data) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_ISM_STATE_WITH_REASON);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, id);
            cs.setString(4, state);
            cs.setString(5, sDate);
            cs.setString(6, amount);
            cs.setString(7, approveDesc);
            cs.setString(8, data);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB get update installment status error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static void approveIppTransaction(String id, Date opDate, Date bankItaDate, Double amount, String approveMessage, String operator) throws Exception {
        updateStateIppTransaction(id, "approved", opDate, bankItaDate, amount, "", approveMessage, operator);
    }   

    public static void rejectIppTransaction(String id, Date opDate, Date bankItaDate, String reasonCode, String reasonMessage, String operator) throws Exception {
        updateStateIppTransaction(id, "failed", opDate, bankItaDate, null, reasonCode, reasonMessage, operator);
    }

    private static void updateStateIppTransaction(String id, String state, Date opDate, Date bankItaDate, Double amount, String reasonCode, String reasonMessage, String operator) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114OneData();
            cs = con.prepareCall(UPDATE_IPP_STATE);

            int idx = 0;
            cs.registerOutParameter(++idx, OracleTypes.VARCHAR);
            cs.setString(++idx, id);
            cs.setString(++idx, state);
            cs.setDate(++idx, opDate != null ? new java.sql.Date(opDate.getTime()) : null);
            cs.setDate(++idx, bankItaDate != null ? new java.sql.Date(bankItaDate.getTime()) : null);
            if (amount != null) {
                cs.setDouble(++idx, amount);
            } else {
                cs.setNull(++idx, Types.NUMERIC);
            }
            cs.setString(++idx, reasonCode);
            cs.setString(++idx, reasonMessage);
            cs.setString(++idx, operator);
            cs.execute();

            String error = cs.getString(1);
            if (error != null) {
                throw new Exception("update ipp transaction error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    public static BaseList<InstallmentTransaction> search(Map mIn) throws Exception {
        logger.log(Level.INFO, "query condition: " + mIn);
        BaseList<InstallmentTransaction> result = new BaseList<>();
        // get Total
        Integer totalReadOnly = getTotalTransaction(mIn);
        result.setTotalItems(totalReadOnly);

        // get Transaction List
        List<InstallmentTransaction> list = new ArrayList<>();
        List<InstallmentTransaction> onlineList = searchTransaction(mIn);
        list.addAll(onlineList); // add online List
        result.setList(list);

        return result;
    }

    public static List<InstallmentTransaction> searchTransaction(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentTransaction> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.setString(6, mIn.get(FROM_DATE).toString());
            cs.setString(7, mIn.get(TO_DATE).toString());
            cs.setString(8, mIn.get(ORDER_INFO).toString());
            cs.setString(9, mIn.get(CARD_NUMBER).toString());
            cs.setString(10, mIn.get(CARD_TYPE).toString());
            cs.setString(11, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setInt(12, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.setInt(13, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setString(14, mIn.get(CURRENCY).toString());
            cs.setString(15, mIn.get(CHANNEL).toString());
            cs.setString(16, mIn.get(TRANSACTION_ID).toString());
            cs.setString(17, mIn.get(AUTHORISATION_CODE).toString());
            cs.setString(18, mIn.get(INSTALLMENT_BANK).toString());
            cs.setString(19, mIn.get(INSTALLMENT_STATUS).toString());
            cs.setString(20, mIn.get(INSTALLMENT_STATE).toString());
            cs.setString(21, mIn.get(TRANS_STATEMENT_DAY).toString());
            if (mIn.get(GROUP_BANK) != null) {
                cs.setString(22, mIn.get(GROUP_BANK).toString());
            } else {
                cs.setString(22, "");
            }

            if (mIn.get(GROUP_CARD) != null) {
                cs.setString(23, mIn.get(GROUP_CARD).toString());
            } else {
                cs.setString(23, "");
            }

            if (mIn.get(GROUP_BIN_DAY) != null) {
                cs.setString(24, mIn.get(GROUP_BIN_DAY).toString());
            } else {
                cs.setString(24, "");
            }
            cs.setString(25, mIn.getOrDefault(REFUND_INSTALLMENT_STATUS, "").toString());
            cs.setString(26, mIn.get(REFERRAL_PARTNER).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search installment transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindTransaction(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalTransaction(Map mIn) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(SEARCH_TRANSACTION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.setString(6, mIn.get(FROM_DATE).toString());
            cs.setString(7, mIn.get(TO_DATE).toString());
            cs.setString(8, mIn.get(ORDER_INFO).toString());
            cs.setString(9, mIn.get(CARD_NUMBER).toString());
            cs.setString(10, mIn.get(CARD_TYPE).toString());
            cs.setString(11, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setInt(12, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.setInt(13, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setString(14, mIn.get(CURRENCY).toString());
            cs.setString(15, mIn.get(CHANNEL).toString());
            cs.setString(16, mIn.get(TRANSACTION_ID).toString());
            cs.setString(17, mIn.get(AUTHORISATION_CODE).toString());
            cs.setString(18, mIn.get(INSTALLMENT_BANK).toString());
            cs.setString(19, mIn.get(INSTALLMENT_STATUS).toString());
            cs.setString(20, mIn.get(INSTALLMENT_STATE).toString());
            cs.setString(21, mIn.get(TRANS_STATEMENT_DAY).toString());
            if (mIn.get(GROUP_BANK) != null) {
                cs.setString(22, mIn.get(GROUP_BANK).toString());
            } else {
                cs.setString(22, "");
            }

            if (mIn.get(GROUP_CARD) != null) {
                cs.setString(23, mIn.get(GROUP_CARD).toString());
            } else {
                cs.setString(23, "");
            }

            if (mIn.get(GROUP_BIN_DAY) != null) {
                cs.setString(24, mIn.get(GROUP_BIN_DAY).toString());
            } else {
                cs.setString(24, "");
            }
            cs.setString(25, mIn.getOrDefault(REFUND_INSTALLMENT_STATUS, "").toString());
            cs.setString(26, mIn.get(REFERRAL_PARTNER).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total installment transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static InstallmentTransaction bindTransaction(ResultSet rs) throws SQLException {
        String id = Util.getColumnString(rs, "S_SUB_ID");
        String transaction_no = Util.getColumnString(rs, "S_TRANSACTION_NO");
        Timestamp date = Util.getColumnString(rs, "D_DATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_DATE"));
        Timestamp originalDate = Util.getColumnString(rs, "D_ORIGINAL_DATE") == null ? null
                : Timestamp.valueOf(Util.getColumnString(rs, "D_ORIGINAL_DATE"));
        String merchantId = rs.getString("S_MERCHANT_ID");
        String contractType = Util.getColumnString(rs, "S_CONTRACT_TYPE");
        String acquirerid = Util.getColumnString(rs, "N_ACQUIRER_ID");
        String trans_ref = Util.getColumnString(rs, "S_TRANSACTION_REFERENCE");
        double total = Util.getColumnDouble(rs, "N_AMOUNT");
        String currency = Util.getColumnString(rs, "S_CURRENCY");
        String response = Util.getColumnString(rs, "S_RESPONSE_CODE");
        String order_ref = Util.getColumnString(rs, "S_ORDER_INFO");
        String card_number = Util.getColumnString(rs, "S_CARD_NO");
        String auth_code = Util.getColumnString(rs, "S_AUTHORISATION_CODE");
        String parentId = Util.getColumnString(rs, "N_PARENT_ID");
        Integer transactionId = rs.getInt("ONECREDIT_TRANS_ID");
        String cardType = Util.getColumnString(rs, "S_CARDTYPE");
        String channel = Util.getColumnString(rs, "S_CHANNEL");
        String referralPartner = Util.getColumnString(rs, "S_REFERRAL_PARTNER");
        String card_holder = Util.getColumnString(rs, "S_CARD_HOLDER");
        double ita_fee_amount = Util.getColumnDouble(rs, "N_ITA_FEE_AMOUNT");
        Timestamp ita_start_date = Util.getColumnString(rs, "D_ITA_START_DATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_ITA_START_DATE"));
        Timestamp onePayApproval = Util.getColumnString(rs, "D_ONEPAY_ITA_DATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_ONEPAY_ITA_DATE"));;
        Timestamp bankSend = Util.getColumnString(rs, "D_ITA_REGISTRATION") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_ITA_REGISTRATION"));
        Timestamp bankApprovalDate = Util.getColumnString(rs, "D_BANK_ITA_STATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs, "D_BANK_ITA_STATE"));

        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);

        InstallmentTransaction transaction = new InstallmentTransaction();
        transaction.setId(id);
        transaction.setTrans_no(transaction_no);
        transaction.setDate(date);
        transaction.setMerchant_id(merchantId);
        transaction.setContract_type(contractType);
        transaction.setAcquirer(acquirerid);
        transaction.setTrans_ref(trans_ref);
        transaction.setCurrency(currency);
        transaction.setChannel(channel);
        transaction.setReferralPartner(referralPartner);
        transaction.setCard_type(cardType);
        transaction.setResponse(response);
        transaction.setOrder_ref(order_ref);
        transaction.setCard_number(card_number);
        transaction.setAuth_code(auth_code);
        transaction.setAmount(amount);
        transaction.setAmount_to_vnd(Util.getColumnDouble(rs, "N_AMOUNT_TO_VND"));
        transaction.setParentId(parentId);
        transaction.setTranaction_id(transactionId == null ? "" : transactionId.toString());
        transaction.setOriginalDate(originalDate);
        transaction.setCard_holder(card_holder);
        transaction.setIta_fee_amount(ita_fee_amount);
        // INSTALLMENT
        transaction.setInstallment_bank(rs.getString("S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(rs.getString("S_INSTALLMENT_STATE"));
        transaction.setInstallment_time(rs.getString("S_INSTALLMENT_TIME"));
        transaction.setIta_start_date(ita_start_date);
        transaction.setRefund_installment_status(rs.getString("S_REFUND_ITA_STATUS"));
        transaction.setOnepayApproval(onePayApproval);
        transaction.setBankSend(bankSend);
        transaction.setBankApprovalDate(bankApprovalDate);
        return transaction;
    }

    private static InstallmentTransactionDetail bindTransactionDetail(ResultSet rs) throws SQLException {
        String cardNumber = rs.getString("S_CARD_NUMBER") == null ? "" : rs.getString("S_CARD_NUMBER");
        String cardDate = rs.getString("S_CARD_EXP") == null ? "" : rs.getString("S_CARD_EXP");
        String orderInfo = rs.getString("S_ORDER_INFO") == null ? "" : rs.getString("S_ORDER_INFO");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");
        String sAcquirerId = Util.getColumnString(rs, "S_ACQUIRER");
        String transactionRef = rs.getString("S_TRANSACTION_REFERENCE") == "" ? null
                : rs.getString("S_TRANSACTION_REFERENCE");
        String currency = rs.getString("S_CURRENCY") == null ? "" : rs.getString("S_CURRENCY");
        String authorizationCode = rs.getString("S_AUTHORISATION_CODE") == null ? ""
                : rs.getString("S_AUTHORISATION_CODE");
        String cardType = rs.getString("S_CARD_TYPE") == null ? "" : rs.getString("S_CARD_TYPE");
        String merchantId = rs.getString("S_MERCHANT_ID") == null ? "" : rs.getString("S_MERCHANT_ID");
        String authenticationType = rs.getString("S_AUTHENTICATION_TYPE") == null ? ""
                : rs.getString("S_AUTHENTICATION_TYPE");
        String authenticationState = rs.getString("S_AUTHENTICATION_STATE") == null ? ""
                : rs.getString("S_AUTHENTICATION_STATE");
        String transactionId = rs.getString("EXT_ID") == null ? "" : rs.getString("EXT_ID");
        String originalId = rs.getString("N_ORIGINAL_ID") == null ? "" : rs.getString("N_ORIGINAL_ID");
        String reason = rs.getString("S_REASON_MESSAGE") == null ? "" : rs.getString("S_REASON_MESSAGE");
        Timestamp transactionTime = Timestamp.valueOf(rs.getString("EXT_DATE")) == null ? null
                : Timestamp.valueOf(rs.getString("EXT_DATE"));
        double total = rs.getDouble("EXT_AMOUNT");
        String transactionType = rs.getString("s_transaction_type"); // todo
        String transactionRefNumber = rs.getString("EXT_REF_NUMBER");
        String responseCode = rs.getString("S_RESPONSE_CODE") == null ? "" : rs.getString("S_RESPONSE_CODE");
        String binCountry = rs.getString("S_BIN_COUNTRY");
        String verificationSecurityLevel = rs.getString("S_VERIFICATION_SECURITY_LEVEL");
        String commercialCard = rs.getString("S_COMMERCIAL_CARD");
        String cscResult = rs.getString("S_CSCRESULT_CODE");
        String enrolled3DS = rs.getString("S_3DS_ENROLLED");
        String ip = rs.getString("S_IP");
        String ipProxy = rs.getString("S_IP_PROXY");
        double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
        String eci = rs.getString("S_3DS_ECI");
        String commercialCardIndicator = rs.getString("S_COMMERCIAL_CARD_INDICATOR");

        // PP
        String status3ds = rs.getString("S_3DS_STATUS");
        String riskOverallResult = rs.getString("S_RISK_OVERALL_RESULT");
        String cardLevelIndicator = rs.getString("S_CARD_LEVEL_INDICATOR");
        
        InstallmentTransactionDetail transaction = new InstallmentTransactionDetail();
        transaction.setSubId(Util.getColumnString(rs, "s_sub_id"));
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(transactionTime);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ip);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_reference(transactionRef);
        transaction.setBin_country(binCountry);
        transaction.setCsc_result_code(cscResult);
        transaction.setMerchant_id(merchantId);
        transaction.setEnrolled_3ds(enrolled3DS);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_ref_number(transactionRefNumber);
        transaction.setVerification_security_level(verificationSecurityLevel);
        transaction.setResponse_code(responseCode);
        transaction.setIp_proxy(ipProxy);
        transaction.setEci(eci);
        transaction.setAdvance_status(rs.getString("S_ADVANCE_STATUS"));


        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        if (acquirerId == 1) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 2) {
            acquirer.setAcquirer_short_name("VietinBank");
            acquirer.setAcquirer_name("VietinBank");
        } else if (acquirerId == 3) {
            acquirer.setAcquirer_short_name("CUP");
            acquirer.setAcquirer_name("CUP");
        } else if (acquirerId == 4) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 5) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 6) {
            acquirer.setAcquirer_short_name("Paypal");
            acquirer.setAcquirer_name("Paypal");
        } else if (acquirerId == 7) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 8) {
            acquirer.setAcquirer_short_name("BIDV");
            acquirer.setAcquirer_name("BIDV");
        } else if (acquirerId == 9) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else {
            acquirer.setAcquirer_short_name(sAcquirerId);
            acquirer.setAcquirer_name(sAcquirerId);
        }

        transaction.setAcquirer(acquirer);

        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(refundTotal);

        transaction.setAmount(amount);

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);

        transaction.setAuthentication(authenticationData);

        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setCommercical_card(commercialCard);
        card.setCommercial_card_indicator(commercialCardIndicator);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);

        // PP
        // transaction.setXid(xid);
        transaction.setRiskOverAllResult(riskOverallResult);
        transaction.setStatus3ds(status3ds);
        transaction.setCardLevelIndicator(cardLevelIndicator);

        // ADAYROI
        // transaction.setCustomer_name(Util.getColumnString(rs, "S_CUSTOMER_NAME"));

        // Installment
        transaction.setInstallment_bank(Util.getColumnString(rs, "S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(Util.getColumnString(rs, "S_INSTALLMENT_STATE"));
        transaction.setInstallment_fee(Util.getColumnDouble(rs, "N_INSTALLMENT_FEE"));
        transaction.setInstallment_cus_email(Util.getColumnString(rs, "S_INSTALLMENT_CUST_EMAIL"));
        transaction.setInstallment_cus_phone(Util.getColumnString(rs, "S_INSTALLMENT_CUST_PHONE"));
        transaction.setInstallment_time(Util.getColumnString(rs, "S_INSTALLMENT_TIME"));
        // transaction.setInstallment_cancel_days(Util.getColumnInteger(rs, "N_INSTALLMENT_CANCEL_DAYS"));
        transaction.setInstallment_monthly_amount(Util.getColumnDouble(rs, "N_INSTALLMENT_MONTHLY_AMOUNT"));
        transaction.setCard_holder_name(Util.getColumnString(rs, "S_CARD_HOLDER"));
        transaction.setInstallment_amount(rs.getString("N_BANK_ITA_AMOUNT"));
        transaction.setInstallment_reason(reason);
        Timestamp installment_date = rs.getString("D_BANK_ITA_STATE") == null ? null
                : Timestamp.valueOf(rs.getString("D_BANK_ITA_STATE"));
        transaction.setInstallment_date(installment_date);

        return transaction;
    }

    private static InstallmentBank bind(ResultSet rs) throws SQLException {
        InstallmentBank b = new InstallmentBank();

        b.setName(rs.getString("S_BANK"));

        return b;
    }

    public static JsonObject getBankInstallmentData() throws Exception {
        Exception exception = null;
        Connection con = null;
        ResultSet rs = null;
        PreparedStatement ps = null;
        JsonObject res = new JsonObject();
        try {
            con = getConnection114();
            String sql = "select * from onesched.tb_scheduler where n_id=279";
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                res.put("s_job_data", rs.getString("S_JOB_DATA_MAP"));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, ps, null, con);
        }
        if (exception != null) {
            throw exception;
        }
        return res;
    }

    public static String exportInstallmentToBank(String bank, String ids, boolean vcbTgnh) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        String sRes = "";
        try {
            con = getConnection114Cdr();
            if (vcbTgnh) {
                cs = con.prepareCall(EXPORT_INSTALLMENT_VCB_TGNH);
                cs.registerOutParameter(1, OracleTypes.VARCHAR);
                cs.setString(2, ids);
                cs.execute();
            } else {
                cs = con.prepareCall(EXPORT_INSTALLMENT_TO_BANK);
                cs.registerOutParameter(1, OracleTypes.VARCHAR);
                cs.setString(2, "");
                cs.setString(3, bank);
                cs.setString(4, ids);
                cs.execute();
            }
            String sResult = cs.getString(1);
            sRes = sResult;

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) {
            throw exception;
        }
        return sRes;
    }

    public static Map<String, String> getPaymentData(String id) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        Map<String, String> response = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_PAYMENT_S_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    response = new HashMap<>();
                    response.put("id", rs.getString("S_ID"));
                    response.put("data", rs.getString("S_DATA"));
                }
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null) {
            throw exception;
        }
        return response;
    }


    public static List<Map<String, Object>> getPartnerIpp() throws Exception {
        Exception exception = null;
        List<Map<String, Object>> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_PARTNER_IPP);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get partner ipp error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    Map<String, Object> partnerIpp = new HashMap();
                    partnerIpp.put("id", rs.getString("S_ID"));
                    partnerIpp.put("name", rs.getString("S_NAME"));
                    partnerIpp.put("service_id", rs.getString("S_SERVICE_ID"));
                    result.add(partnerIpp);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }
}
