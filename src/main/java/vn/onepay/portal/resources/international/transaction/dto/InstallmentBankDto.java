package vn.onepay.portal.resources.international.transaction.dto;

/**
 * Created by tuydv on 09-5-18.
 */
public class InstallmentBankDto {

   private String date_bank;
   private String installment_bank;
   private String term_bank;
   private int quantity_approved_trans;
   private Double total_approved_trans;
   private int quantity_reject_trans;
   private Double total_reject_trans;

    public InstallmentBankDto() {
    }

    public InstallmentBankDto(String date_bank, String installment_bank, String term_bank, int quantity_approved_trans, Double total_approved_trans, int quantity_reject_trans, Double total_reject_trans) {
        this.date_bank = date_bank;
        this.installment_bank = installment_bank;
        this.term_bank = term_bank;
        this.quantity_approved_trans = quantity_approved_trans;
        this.total_approved_trans = total_approved_trans;
        this.quantity_reject_trans = quantity_reject_trans;
        this.total_reject_trans = total_reject_trans;
    }

    public String getDate_bank() {
        return date_bank;
    }

    public void setDate_bank(String  date_bank) {
        this.date_bank = date_bank;
    }

    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getTerm_bank() {
        return term_bank;
    }

    public void setTerm_bank(String term_bank) {
        this.term_bank = term_bank;
    }

    public int getQuantity_approved_trans() {
        return quantity_approved_trans;
    }

    public void setQuantity_approved_trans(int quantity_approved_trans) {
        this.quantity_approved_trans = quantity_approved_trans;
    }

    public Double getTotal_approved_trans() {
        return total_approved_trans;
    }

    public void setTotal_approved_trans(Double total_approved_trans) {
        this.total_approved_trans = total_approved_trans;
    }

    public int getQuantity_reject_trans() {
        return quantity_reject_trans;
    }

    public void setQuantity_reject_trans(int quantity_reject_trans) {
        this.quantity_reject_trans = quantity_reject_trans;
    }

    public Double getTotal_reject_trans() {
        return total_reject_trans;
    }

    public void setTotal_reject_trans(Double total_reject_trans) {
        this.total_reject_trans = total_reject_trans;
    }
}
