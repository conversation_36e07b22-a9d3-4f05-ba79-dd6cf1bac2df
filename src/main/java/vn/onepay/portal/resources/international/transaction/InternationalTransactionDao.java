package vn.onepay.portal.resources.international.transaction;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.Amount;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.international.transaction.dto.Acquirer;
import vn.onepay.portal.resources.international.transaction.dto.AcquirerDto;
import vn.onepay.portal.resources.international.transaction.dto.AuthenticationData;
import vn.onepay.portal.resources.international.transaction.dto.AvsData;
import vn.onepay.portal.resources.international.transaction.dto.CardDate;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentBankDto;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentMerchantDto;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentMerchantV2Dto;
import vn.onepay.portal.resources.international.transaction.dto.InternationalCard;
import vn.onepay.portal.resources.international.transaction.dto.InternationalPurchase;
import vn.onepay.portal.resources.international.transaction.dto.InternationalTransaction;
import vn.onepay.portal.resources.international.transaction.dto.InternationalTransactionHashcode;
import vn.onepay.portal.resources.international.transaction.dto.InternationalTransactionHistory;

public class InternationalTransactionDao extends Db implements IConstants {

    private static final String SEARCH_TRANSACTION3 = "{call PKG_REPORT_ONECREDIT.SEARCH_TRAN_MIGS_V7(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_GET_BY_ID = "{call PKG_REPORT_ONECREDIT.SEARCH_TRAN_MIGS_BY_ID_V2(?,?,?,?)}";
    private static final String TRANSACTION_GET_LIST_BY_IDS = "{call PKG_REPORT_ONECREDIT.SEARCH_LIST_TRAN_MIGS_BY_IDS(?,?,?,?)}";
    private static final String PURCHASE_GET_BY_ID = "{call PKG_REPORT_ONECREDIT.SEARCH_TRANSACTION_BY_ID_v5(?,?,?,?) }";
    private static final String UPDATE_PURCHASE_STATUS_BY_ID = "{call PKG_REPORT_ONECREDIT.UPDATE_PURCHASE_STATUS_BY_ID(?,?,?,?)";
    private static final String UPDATE_REFUND_STATUS_BY_ID = "{call PKG_REPORT_ONECREDIT.UPDATE_REFUND_STATUS_BY_ID(?,?,?,?)";

    private static final String LIST_REFUND_APPROVAL_HISTORY = "{call PKG_MA_REFUND_APPROVE.get_approval_history(?,?,?,?)}";

    private static final String LIST_TRANSACTION_HISTORY = "{call PKG_REPORT_ONECREDIT.SEARCH_TRANSACTION_HIS(?,?,?,?,?)}";

    private static final String LIST_REQUEST_REFUND_HISTORY_BACKUP_118 = "{call PKG_RP_INTERNATIONAL_TXN.get_rr_history_118(?,?,?,?)}";

    private static final String LIST_TRANSACTION_ONLINE_BY_IDS = "{call PKG_REPORT_ONECREDIT.GET_TRANSACTION_BY_IDS(?,?,?,?,?,?,?)}";

    private static final String TRANSACTION_BY_MERCHANT_REF = "{call PKG_REPORT_ONECREDIT.SEARCH_REFUND_BY_REF(?,?,?,?,?,?) }";

    //    Installment Report by Tiennv
    private static final String ISM_BANK_REPORT = "{call PKG_INSTALLMENT.ism_bank_report_3(?,?,?,?,?,?,?,?,?) }";
    private static final String ISM_MERCHANT_REPORT = "{call PKG_INSTALLMENT.ism_merchant_report_v3(?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String SEARCH_INSTALLMENT_MERCHANT_V3 = "{call PKG_REPORT_ONECREDIT.ism_merchant_report_v3(?,?,?,?,?,?,?,?,?,?) }";
    private static final String SEARCH_INSTALLMENT_MERCHANT_V2 = "{call PKG_MA_REFUND_APPROVE.ism_merchant_report_v2(?,?,?,?,?,?,?) }";

    //Authorize transaction
    private static final String LIST_AUTHORIZE_TRANSACTION_HISTORY = "{call PKG_REPORT_ONECREDIT.search_authorize_tran_his(?,?,?,?,?)}";
    private static final String LIST_CAPTURE_REFUND_APPROVAL_HISTORY = "{call PKG_MA_REFUND_APPROVE.get_capture_approval_history(?,?,?,?)}";

    private static final Logger logger = Logger.getLogger(InternationalTransactionDao.class.getName());

    public static BaseList<InternationalTransaction> search(Map mIn) throws Exception {
        BaseList<InternationalTransaction> result = new BaseList<>();
        // get Total
        Integer totalOnline = getTotalTransaction(mIn, -1);
        Integer totalReadOnly = getTotalTransaction(mIn, 1);
        result.setTotalItems(totalOnline + totalReadOnly);

        // get Transaction List
        List<InternationalTransaction> list = new ArrayList<>();
        mIn.put(OFFSET, 0);
        List<InternationalTransaction> onlineList = searchTransaction(mIn, -1);
        mIn.put(OFFSET, totalOnline);
        List<InternationalTransaction> readonlyList = searchTransaction(mIn, onlineList.size());
        list.addAll(onlineList); // add online List
        list.addAll(readonlyList);  // add readonly List
        result.setList(list);

        return result;
    }

    public static Integer getTotalTransaction(Map mIn) throws Exception {
        BaseList<InternationalTransaction> result = new BaseList<>();
        // get Total
        Integer totalOnline = getTotalTransaction(mIn, -1);
        Integer totalReadOnly = getTotalTransaction(mIn, 1);
        return totalOnline + totalReadOnly;
    }

    public static Integer getTotalTransaction(Map mIn, int row) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = row >= 0 ? getConnection113() : getConnection112();
            cs = con.prepareCall(SEARCH_TRANSACTION3);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, row);
            cs.setString(5, QueryMethod.TOTAL.toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setString(9, mIn.get(ORDER_INFO).toString());
            cs.setString(10, mIn.get(TRANSACTION_STATUS).toString());
            cs.setString(11, mIn.get(CARD_NUMBER).toString());
            cs.setString(12, mIn.get(CARD_TYPE).toString());
            cs.setString(13, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setNull(14, Types.INTEGER);
            cs.setNull(15, Types.INTEGER);
            cs.setNull(16, Types.INTEGER);
            cs.setString(17, mIn.get(CURRENCY).toString());
            cs.setString(18, mIn.get(TRANSACTION_TYPE).toString());
            cs.setString(19, mIn.get(ACQUIRER_ID).toString());
            cs.setString(20, mIn.get(TRANSACTION_ID).toString());
            cs.setString(21, mIn.get(CUSTOMER_MOBILE).toString());
            cs.setString(22, mIn.get(CUSTOMER_EMAIL).toString());
            cs.setString(23, mIn.get(MERCHANT_WEBSITE).toString());
            cs.setString(24, mIn.get(FRAUD_CHECK).toString());
            cs.setString(25, mIn.get(AUTHORISATION_CODE).toString());
            cs.setString(26, mIn.get(INSTALLMENT_BANK).toString());
            cs.setString(27, mIn.get(INSTALLMENT_STATUS).toString());
            cs.setString(28, mIn.get("refundType").toString());
            cs.setString(29, mIn.get("contractType").toString());
            cs.setString(30, mIn.get(NETWORK_TRANS_ID).toString());
            cs.setString(31, mIn.get(TOKEN_NUMBER).toString());
            cs.setString(32, mIn.get(SOURCE).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total international transaction error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<InternationalTransaction> searchTransaction(Map mIn, int row) throws Exception {
        Exception exception = null;
        List<InternationalTransaction> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = row >= 0 ? getConnection113() : getConnection112();
            cs = con.prepareCall(SEARCH_TRANSACTION3);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, row);
            cs.setString(5, QueryMethod.SELECT.toString());
            cs.setString(6, mIn.get(MERCHANT_ID).toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setString(9, mIn.get(ORDER_INFO).toString());
            cs.setString(10, mIn.get(TRANSACTION_STATUS).toString());
            cs.setString(11, mIn.get(CARD_NUMBER).toString());
            cs.setString(12, mIn.get(CARD_TYPE).toString());
            cs.setString(13, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setInt(14, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.setInt(15, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setInt(16, mIn.get(OFFSET) == null ? 0 : Convert.parseInt(mIn.get(OFFSET).toString(), 0));
            cs.setString(17, mIn.get(CURRENCY).toString());
            cs.setString(18, mIn.get(TRANSACTION_TYPE).toString());
            cs.setString(19, mIn.get(ACQUIRER_ID).toString());
            cs.setString(20, mIn.get(TRANSACTION_ID).toString());
            cs.setString(21, mIn.get(CUSTOMER_MOBILE).toString());
            cs.setString(22, mIn.get(CUSTOMER_EMAIL).toString());
            cs.setString(23, mIn.get(MERCHANT_WEBSITE).toString());
            cs.setString(24, mIn.get(FRAUD_CHECK).toString());
            cs.setString(25, mIn.get(AUTHORISATION_CODE).toString());
            cs.setString(26, mIn.get(INSTALLMENT_BANK).toString());
            cs.setString(27, mIn.get(INSTALLMENT_STATUS).toString());
            cs.setString(28, mIn.get("refundType").toString());
            cs.setString(29, mIn.get("contractType").toString());
            cs.setString(30, mIn.get(NETWORK_TRANS_ID).toString());
            cs.setString(31, mIn.get(TOKEN_NUMBER).toString());
            cs.setString(32, mIn.get(SOURCE).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international transaction error: " + error);
            } else {
                InternationalTransaction transaction;
                while (rs != null && rs.next()) {
                    transaction = bindTransaction(rs);
                    transaction.setNote(Util.getColumnString(rs,"S_NOTE"));
                    transaction.setSource(Util.getColumnString(rs,"S_SOURCE"));
                    transaction.setNetworkTransId(Util.getColumnString(rs,"S_NETWORK_TRANS_ID"));
                    transaction.setTokenNumber(Util.getColumnString(rs,"S_TOKEN_NUMBER"));

                    result.add(transaction);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static InternationalTransaction getTransaction(String id) throws Exception {
        Exception exception = null;
        InternationalTransaction result = new InternationalTransaction();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall(TRANSACTION_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindTransaction(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // close(rs, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<InternationalTransactionHashcode> getListTransactionByIds(String transIds) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHashcode> result = new ArrayList();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(TRANSACTION_GET_LIST_BY_IDS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, transIds);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get list international transaction by ids error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindListTransactionHashCode(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // close(rs, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static InternationalPurchase getPurchase(String id) throws Exception {
        Exception exception = null;
        InternationalPurchase result = new InternationalPurchase();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall(PURCHASE_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international purchase by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindPurchase(rs);
                    result.setSource(Util.getColumnString(rs,"S_SOURCE"));
                    result.setNetworkTransId(Util.getColumnString(rs,"S_NETWORK_TRANS_ID"));
                    result.setTokenNumber(Util.getColumnString(rs,"S_TOKEN_NUMBER"));
                    result.setTokenExpiry(Util.getColumnString(rs,"S_TOKEN_EXPIRY"));
                    result.setDeviceId(Util.getColumnString(rs,"S_DEVICE_ID"));
                    result.setType(Util.getColumnString(rs,"S_TYPE"));
                    result.setReason(Util.getColumnString(rs,"S_REASON"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static InternationalPurchase updatePurchaseStatus(String id, String status) throws Exception {
        Exception exception = null;
        InternationalPurchase result = new InternationalPurchase();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_PURCHASE_STATUS_BY_ID);
            cs.setInt(3, Convert.parseInt(id, 0));
            cs.setInt(4, Convert.parseInt(status, 0));
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB update international purchase by id error: " + error);
            }
            cs.close();
            cs = con.prepareCall(PURCHASE_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international purchase by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindPurchase(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static InternationalTransaction updateRefundStatus(String id, String status) throws Exception {
        Exception exception = null;
        InternationalTransaction result = new InternationalTransaction();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_REFUND_STATUS_BY_ID);
            cs.setInt(3, Convert.parseInt(id, 0));
            cs.setInt(4, Convert.parseInt(status, 0));
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError != 200) {
                throw new Exception("DB update international purchase by id error: " + error);
            }
            cs.close();
            cs = con.prepareCall(TRANSACTION_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            error = cs.getString(3);
            nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international purchase by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindTransaction(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(null, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<InternationalTransactionHistory> listHistory(String id) throws Exception {
        List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();

        List<InternationalTransactionHistory> onlineHistory = getHistory(id, -1);
        List<InternationalTransactionHistory> readonlyHistory = getHistory(id, onlineHistory.size());
        List<InternationalTransactionHistory> backupHistory = getHistoryBackup(id);
        List<InternationalTransactionHistory> backupRRHistory118 = getRefundHistoryBackup118(id);

        transactionsFinal.addAll(onlineHistory);
        transactionsFinal.addAll(readonlyHistory);
        transactionsFinal.addAll(backupHistory);
        transactionsFinal.addAll(backupRRHistory118);

        // sort by trans time.
        transactionsFinal
                .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));

        return transactionsFinal;

    }

    public static InternationalTransactionHistory getHistoryByMerhchantRef(String transactionId, String merchantId, String transactionReference) throws Exception {
        Exception exception = null;
        InternationalTransactionHistory result = new InternationalTransactionHistory();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(TRANSACTION_BY_MERCHANT_REF);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, transactionId);
            cs.setString(5, transactionReference);
            cs.setString(6, merchantId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international history by txn ref error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = (bindTransactionHistory(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<InternationalTransactionHistory> getHistory(String id, int row) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = row >= 0 ? getConnection113() : getConnection112();
            cs = con.prepareCall(LIST_TRANSACTION_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, row);
            cs.setInt(5, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international history by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindTransactionHistory(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<InternationalTransactionHistory> getHistoryBackup(String id) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_REFUND_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(id, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);

            if (nError != 200) {
                throw new Exception("DB get international history backup by id error: " + error);
            } else {

                List<InternationalTransactionHistory> childList = new ArrayList<>();
                List<InternationalTransactionHistory> parentList = new ArrayList<>();
                while (rs != null && rs.next()) {
                    InternationalTransactionHistory transaction = bindApprovalHistory(rs);

                    if (transaction.getParent_id() == null) {
                        parentList.add(transaction);
                    } else {
                        childList.add(transaction);
                    }
                }
                Map<Integer, List<InternationalTransactionHistory>> childMap = childList.stream()
                        .collect(Collectors.groupingBy(InternationalTransactionHistory::getParent_id));

                result = parentList.stream().map(parent -> {
                    InternationalTransactionHistory re = new InternationalTransactionHistory(parent);

                    if (childMap.get(parent.getTransaction_id()) != null) {
                        re.setSubHistories(childMap.get(parent.getTransaction_id()));
                    } else {
                        re.setSubHistories(new ArrayList<>());
                    }
                    re.getSubHistories().add(parent);
                    re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                    return re;
                })
                        .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<InternationalTransactionHistory> getRefundHistoryBackup118(String id) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_REQUEST_REFUND_HISTORY_BACKUP_118);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international history by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    InternationalTransactionHistory tran = bindTransactionHistory(rs);
                    tran.setIs_msp_refund(true);
                    result.add(tran);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Map<Integer, InternationalPurchase> mapByIds(String transIds, Map mIn) throws Exception {

        List<InternationalPurchase> internationalTransactions = listByIds(transIds, mIn);
        Map<Integer, InternationalPurchase> map = new HashMap<>();
        for (InternationalPurchase dt : internationalTransactions) {
            map.put(dt.getTransaction_id(), dt);
        }
        return map;
    }


    public static List<InternationalPurchase> listByIds(String ids, Map mIn) throws Exception {
        Exception exception = null;
        List<InternationalPurchase> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(LIST_TRANSACTION_ONLINE_BY_IDS);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, ids);
            cs.setString(5, mIn.get(ORDER_INFO).toString());
            cs.setString(6, mIn.get(ACQUIRER_ID).toString());
            cs.setString(7, mIn.get(CARD_NUMBER).toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international by ids error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindPurchase(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static InternationalTransaction bindTransaction(ResultSet rs) throws SQLException {
        Integer id = rs.getInt("N_ID");
        String transaction_no = Util.getColumnString(rs,"S_TRANSACTION_NO");
        Timestamp date = Util.getColumnString(rs,"D_DATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs,"D_DATE"));
        String merchantId = Util.getColumnString(rs,"S_MERCHANTID");
        String acquirerid = String.valueOf(rs.getInt("N_ACQUIRERID"));
        String trans_ref = Util.getColumnString(rs,"S_MERCHANTTRANSACTIONREFEREN");
        String trans_type = Util.getColumnString(rs,"S_TRANSACTIONTYPE");
        double total = rs.getDouble("N_AMOUNT");
//        double originalTotal = rs.getDouble("N_ORIGINAL_AMOUNT");
        double originalTotal = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT") == null ? 0 : Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String currency = Util.getColumnString(rs,"S_CURRENCY");
        String response = Util.getColumnString(rs,"S_RESPONSECODE");
        String order_ref = Util.getColumnString(rs,"S_ORDERREFERENCE");
        String source = Util.getColumnString(rs,"S_SOURCE");
        String card_number = (!Objects.isNull(source) && (Objects.equals(source, "Apple Pay") || Objects.equals(source, "Google Pay") || Objects.equals(source, "Samsung Pay")))?
            Util.getColumnString(rs,"S_CARDNO") : Util.getColumnString(rs,"S_CARD_NO");
        String card_name = Util.getColumnString(rs,"S_CARD_NAME");
        String card_phone = Util.getColumnString(rs,"S_CARD_PHONE");
        String card_email = Util.getColumnString(rs,"S_CARD_EMAIL");
        String auth_code = Util.getColumnString(rs,"S_AUTHORISATIONCODE");
        String payment_auth_id = Util.getColumnString(rs,"S_PAYMENT_AUTHENTICATION_ID");
        String batch_number = Util.getColumnString(rs,"S_BATCHNUMBER");
        Integer transactionId = rs.getInt("ONECREDIT_TRANS_ID");
        String cardType = Util.getColumnString(rs,"S_CARDTYPE");
        String operator = Util.getColumnString(rs,"S_OPERATOR");
        String contract_type = Util.getColumnString(rs,"S_CONTRACT_TYPE");
        String s_msp_id = Util.getColumnString(rs,"S_MSP_ID");
        String reason = Util.getColumnString(rs,"S_REASON");
//        String base_status = Util.getColumnString(rs,"S_STATUS");

        // Amount
        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setOriginalTotal(originalTotal);
        amount.setRefund_total(rs.getDouble("ext_refund_amount"));

        InternationalTransaction transaction = new InternationalTransaction();
        transaction.setId(id);
        transaction.setTrans_no(transaction_no);
        transaction.setDate(date);
        transaction.setMerchant_id(merchantId);
        transaction.setAcquirer(acquirerid);
        transaction.setAcquirerName(Util.getColumnString(rs, "S_ACQUIRER_NAME"));
        transaction.setTrans_ref(trans_ref);
        transaction.setTrans_type(trans_type);
        transaction.setCurrency(currency);
        transaction.setCard_type(cardType);
        transaction.setResponse(response);
        transaction.setOrder_ref(order_ref);
        transaction.setCard_number(card_number);
        transaction.setCard_name(card_name);
        transaction.setCard_email(card_email);
        transaction.setCard_phone(card_phone);
        transaction.setAuth_code(auth_code);
        transaction.setAmount(amount);
        transaction.setPayment_auth_id(payment_auth_id);
        transaction.setBatch_number(batch_number);
        transaction.setOperator(operator);
        transaction.setTranaction_id(transactionId == null ? "" : transactionId.toString());
        transaction.setContract_type(contract_type);
        transaction.setS_MSP_ID(s_msp_id);
        transaction.setReason(reason);
//        transaction.setBase_status(base_status);

        // INSTALLMENT
        //Không hiển thị thông tin installments với các loại giao dịch: Authorize và Refund Capture
        if (!Objects.equals(trans_type, AUTHORIZE) && !Objects.equals(trans_type, CAPTURE_REFUND)) {
            transaction.setInstallment_bank(Util.getColumnString(rs,"S_INSTALLMENT_BANK"));
            transaction.setInstallment_time(Util.getColumnString(rs,"S_INSTALLMENT_TIME"));
            transaction.setInstallment_status(Util.getColumnString(rs,"S_INSTALLMENT_STATE"));
            transaction.setInstallment_amount(Util.getColumnString(rs,"N_BANK_ITA_AMOUNT"));
            Timestamp installment_date = Util.getColumnString(rs,"D_BANK_ITA_STATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs,"D_BANK_ITA_STATE"));
            transaction.setInstallment_date(installment_date);
        }
        // Return.
        return transaction;
    }

    private static InternationalTransactionHashcode bindListTransactionHashCode(ResultSet rs) throws SQLException {
        Integer n_id = rs.getInt("N_ID");
        String merchant_id = Util.getColumnString(rs,"S_MERCHANT_ID");
        String transref = Util.getColumnString(rs,"TRANSREF");
        String hashcode = Util.getColumnString(rs,"HASHCODE");
        String accesscode = Util.getColumnString(rs,"ACCESSCODE");

        InternationalTransactionHashcode transHash = new InternationalTransactionHashcode();
        transHash.setN_id(n_id);
        transHash.setMerchantId(merchant_id);
        transHash.setTransref(transref);
        transHash.setHashcode(hashcode);
        transHash.setAccesscode(accesscode);

        return transHash;
    }

    private static InternationalPurchase bindPurchase(ResultSet rs) throws SQLException {

        String cardNumber = Util.getColumnString(rs,"S_CARD_NO");
        String cardName = Util.getColumnString(rs,"S_CARD_NAME");
        String cardEmail = Util.getColumnString(rs,"S_CARD_EMAIL");
        String cardPhone = Util.getColumnString(rs,"S_CARD_PHONE");
        String cardDate = Util.getColumnString(rs,"S_CARD_EXP");
        String orderInfo = Util.getColumnString(rs,"S_ORDER_INFO");
        int acquirerId = rs.getInt("N_ACQUIRER_ID");
        String transactionRef = Util.getColumnString(rs,"S_TRANSACTION_REFERENCE");
        String currency = Util.getColumnString(rs,"S_CURRENCY");
        String authorizationCode = Util.getColumnString(rs,"S_AUTHORISATION_CODE");
        String cardType = Util.getColumnString(rs,"S_CARD_TYPE");
        String merchantId = Util.getColumnString(rs,"S_MERCHANT_ID");
        String authenticationType = Util.getColumnString(rs,"S_AUTHENTICATION_TYPE");
        String authenticationState = Util.getColumnString(rs,"S_AUTHENTICATION_STATE");
        int transactionId = rs.getInt("EXT_ID");
        int originalId = rs.getInt("N_ORIGINAL_ID");
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp transactionTime = Timestamp.valueOf(Util.getColumnString(rs,"EXT_DATE"));

//        try {
//            transactionTime = new Timestamp(formatter.parse(Util.getColumnString(rs,"EXT_DATE")).getTime());
//        } catch (ParseException e) {
//            LOGGER.log(Level.SEVERE, "PARSE DATE ERROR", e);
//        }
        double total = rs.getDouble("EXT_AMOUNT");
        double originalTotal = rs.getDouble("EXT_PURCHASE_AMOUNT");
        String transactionType = Util.getColumnString(rs,"EXT_TYPE");
        String transactionStatus = Util.getColumnString(rs,"EXT_STATUS");
        String transactionRefNumber = Util.getColumnString(rs,"EXT_REF_NUMBER");
        String responseCode = Util.getColumnString(rs,"S_RESPONSE_CODE");
        String binCountry = Util.getColumnString(rs,"BIN_COUNTRY");
        String avsResultCode = Util.getColumnString(rs,"S_AVS_RESULT_CODE");
        String address = Util.getColumnString(rs,"S_ADDRESS");
        String state = Util.getColumnString(rs,"S_STATE_PROVINCE");
        String zipCode = Util.getColumnString(rs,"S_ZIP_POSTAL_CODE");
        String country = Util.getColumnString(rs,"S_COUNTRY");
        String city = Util.getColumnString(rs,"S_CITY_TOWN");
        String verificationSecurityLevel = Util.getColumnString(rs,"S_VERIFICATION_SECURITY_LEVEL");
        String commercialCard = Util.getColumnString(rs,"S_COMMERCIAL_CARD");
        String cscResult = Util.getColumnString(rs,"S_CSCRESULT_CODE");
        String enrolled3DS = Util.getColumnString(rs,"S_3DS_ENROLLED");
        String ip = Util.getColumnString(rs,"S_IP");
        String ipProxy = Util.getColumnString(rs,"S_IP_PROXY");
        String operator = Util.getColumnString(rs,"S_OPERATOR_ID");
        double refundTotal = rs.getDouble("EXT_REFUND_AMOUNT");
        boolean canVoid = Util.hasColumn(rs, "CAN_VOID") ? rs.getInt("CAN_VOID") == 1 ? true : false : false;
        boolean requiredAvs = Util.hasColumn(rs, "CAN_VOID") ? rs.getInt("N_REQUIRED_AVS") == 1 ? true : false : false;
        String eci = Util.getColumnString(rs,"S_3DS_ECI");
        String ticketNumber = Util.getColumnString(rs,"S_TICKET_NUMBER");
//        String authorizationAmount =Util.getColumnString(rs,"S_AUTHORISED_AMOUNT");
        String commercialCardIndicator = Util.getColumnString(rs,"S_COMMERCIAL_CARD_INDICATOR");

        //PP
        String status3ds = Util.getColumnString(rs,"S_3DS_STATUS");
        String riskOverallResult = Util.getColumnString(rs,"S_RISK_OVERALL_RESULT");
//        String xid = Util.getColumnString(rs,"S_3DS_XID");
        String cardLevelIndicator = Util.getColumnString(rs,"S_CARD_LEVEL_INDICATOR");


        InternationalPurchase transaction = new InternationalPurchase();
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_time(transactionTime);
        transaction.setTransaction_type(transactionType);
        transaction.setIp_address(ip);
        transaction.setOrder_info(orderInfo);
        transaction.setTransaction_reference(transactionRef);
        transaction.setBin_country(binCountry);
        transaction.setCsc_result_code(cscResult);
        transaction.setMerchant_id(merchantId);
        transaction.setOperator(operator);
        transaction.setEnrolled_3ds(enrolled3DS);
        transaction.setTransaction_status(transactionStatus);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setTransaction_ref_number(transactionRefNumber);
        transaction.setVerification_security_level(verificationSecurityLevel);
        transaction.setResponse_code(responseCode);
        transaction.setIp_proxy(ipProxy);
        transaction.setCan_void(canVoid);
        transaction.setIs_required_avs(requiredAvs);
        transaction.setEci(eci);
        transaction.setAdvance_status(Util.getColumnString(rs,"S_ADVANCE_STATUS"));
        transaction.setTicket_number(ticketNumber);
        transaction.setOperator(operator);

        Acquirer acquirer = new Acquirer();
        acquirer.setAcquirer_id(acquirerId);
        if (acquirerId == 1) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 2) {
            acquirer.setAcquirer_short_name("VietinBank");
            acquirer.setAcquirer_name("VietinBank");
        } else if (acquirerId == 3) {
            acquirer.setAcquirer_short_name("CUP");
            acquirer.setAcquirer_name("CUP");
        } else if (acquirerId == 4) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 5) {
            acquirer.setAcquirer_short_name("VietcomBank");
            acquirer.setAcquirer_name("VietcomBank");
        } else if (acquirerId == 6) {
            acquirer.setAcquirer_short_name("Paypal");
            acquirer.setAcquirer_name("Paypal");
        } else if (acquirerId == 7) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 8) {
            acquirer.setAcquirer_short_name("BIDV");
            acquirer.setAcquirer_name("BIDV");
        } else if (acquirerId == 9) {
            acquirer.setAcquirer_short_name("Sacombank");
            acquirer.setAcquirer_name("Sacombank");
        } else if (acquirerId == 10) {
            acquirer.setAcquirer_short_name("Techcombank");
            acquirer.setAcquirer_name("Techcombank");
        } else if (acquirerId == 11) {
            acquirer.setAcquirer_short_name("VPBank");
            acquirer.setAcquirer_name("VPBank");
        } else if (acquirerId == 12) {
            acquirer.setAcquirer_short_name("KBank");
            acquirer.setAcquirer_name("KBank");
        } else if (acquirerId == 13) {
            acquirer.setAcquirer_short_name("VPBank");
            acquirer.setAcquirer_name("VPBank");
        }

        transaction.setAcquirer(acquirer);

        AvsData avsData = new AvsData();
        avsData.setAddress(address);
        avsData.setCountry(country);
        avsData.setCity(city);
        avsData.setProvince(state);
        avsData.setResult_code(avsResultCode);
        avsData.setZip_code(zipCode);

        transaction.setAvs(avsData);

        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(total);
        amount.setRefund_total(refundTotal);
        amount.setOriginalTotal(originalTotal);

        transaction.setAmount(amount);

        AuthenticationData authenticationData = new AuthenticationData();
        authenticationData.setAuthentication_state(authenticationState);
        authenticationData.setAuthentication_type(authenticationType);
        authenticationData.setAuthorization_code(authorizationCode);
//        authenticationData.setAuthorization_amount(authorizationAmount);

        transaction.setAuthentication(authenticationData);


        InternationalCard card = new InternationalCard();
        card.setCard_number(cardNumber);
        card.setCard_type(cardType);
        card.setCommercical_card(commercialCard);
        card.setCommercial_card_indicator(commercialCardIndicator);
        card.setName(cardName);
        card.setEmail(cardEmail);
        card.setPhone(cardPhone);

        CardDate cardDateData = new CardDate();
        if (cardDate != null && cardDate.length() == 4) {
            cardDateData.setYear(cardDate.substring(0, 2));
            cardDateData.setMonth(cardDate.substring(2, 4));
        }

        card.setCard_date(cardDateData);

        transaction.setCard(card);

        //PP
//        transaction.setXid(xid);
        transaction.setRiskOverAllResult(riskOverallResult);
        transaction.setStatus3ds(status3ds);
        transaction.setCardLevelIndicator(cardLevelIndicator);


        // ADAYROI
        transaction.setCustomer_name(Util.getColumnString(rs, "S_CUSTOMER_NAME"));

        // Installment
        transaction.setInstallment_bank(Util.getColumnString(rs, "S_INSTALLMENT_BANK"));
        transaction.setInstallment_status(Util.getColumnString(rs, "S_INSTALLMENT_STATE"));
        transaction.setInstallment_fee(Util.getColumnDouble(rs, "N_INSTALLMENT_FEE"));
        transaction.setInstallment_cus_email(Util.getColumnString(rs, "S_INSTALLMENT_CUST_EMAIL"));
        transaction.setInstallment_cus_phone(Util.getColumnString(rs, "S_INSTALLMENT_CUST_PHONE"));
        transaction.setInstallment_time(Util.getColumnString(rs, "S_INSTALLMENT_TIME"));
        transaction.setInstallment_cancel_days(Util.getColumnInteger(rs, "N_INSTALLMENT_CANCEL_DAYS"));
        transaction.setInstallment_monthly_amount(Util.getColumnDouble(rs, "N_INSTALLMENT_MONTHLY_AMOUNT"));
        transaction.setCard_holder_name(Util.getColumnString(rs, "S_CARD_HOLDER"));
        transaction.setInstallment_amount(Util.getColumnString(rs,"N_BANK_ITA_AMOUNT"));
        Timestamp installment_date = Util.getColumnString(rs,"D_BANK_ITA_STATE") == null ? null : Timestamp.valueOf(Util.getColumnString(rs,"D_BANK_ITA_STATE"));
        transaction.setInstallment_date(installment_date);

        return transaction;
    }

    private static InternationalTransactionHistory bindTransactionHistory(ResultSet rs) throws SQLException {

        int transactionId = rs.getInt("N_ID");
        String currency = Util.getColumnString(rs,"S_CURRENCY");
        String status = Util.getColumnString(rs,"S_STATUS");
        String responseCode = Util.getColumnString(rs,"S_RESPONSE_CODE");
        int originalId = rs.getInt("N_ORIGINAL_ID");
        String operator = Util.getColumnString(rs,"S_OPERATOR_ID");
        String merchantId = Util.getColumnString(rs,"S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        double originalTotal = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT") == null ? 0 : Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String transactionType = Util.getColumnString(rs,"S_TRANSACTION_TYPE");
        String merchantTransactionRef = Util.getColumnString(rs,"S_MERCHANT_TRANSACTION_REF");
        String advanceStatus = Util.getColumnString(rs,"S_ADVANCE_STATUS");
        String financialTransactionId = Util.getColumnString(rs,"S_FINANCIAL_TRANSACTION_ID");
        String referenceNumber = Util.getColumnString(rs,"S_REFERENCE_NUMBER");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = java.sql.Timestamp.valueOf(Util.getColumnString(rs,"D_DATE"));

        InternationalTransactionHistory transactionHistory = new InternationalTransactionHistory();
        transactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        transactionHistory.setStatus(status);
        transactionHistory.setOperator_id(operator);
        transactionHistory.setOriginal_transaction_id(originalId);
        transactionHistory.setMerchant_id(merchantId);
        transactionHistory.setResponse_code(responseCode);
        transactionHistory.setTransaction_id(transactionId);
        transactionHistory.setTransaction_type(transactionType);
        transactionHistory.setTransaction_time(date);
        transactionHistory.setAdvance_status(advanceStatus);
        transactionHistory.setReference_number(referenceNumber);
        transactionHistory.setFinancial_transaction_id(financialTransactionId);
        transactionHistory.setNote(Util.getColumnString(rs,"S_NOTE"));
        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);
        amountData.setOriginalTotal(originalTotal);

        transactionHistory.setAmount(amountData);

        return transactionHistory;
    }

    private static InternationalTransactionHistory bindApprovalHistory(ResultSet rs) throws SQLException {

        int transactionId = rs.getInt("N_ID");
        int originalId = Integer.valueOf(Util.getColumnString(rs,"N_TRANS_REF_ID"));
        String currency = Util.getColumnString(rs,"S_CURRENCY");
        Integer status = rs.getInt("N_STATUS");
        String operator = Util.getColumnString(rs,"S_OPERATOR_ID");
        String merchantId = Util.getColumnString(rs,"S_MERCHANT_ID");
        double amount = rs.getDouble("N_AMOUNT");
        double originalAmount = Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT") == null ? 0 : Util.getColumnDouble(rs, "N_ORIGINAL_AMOUNT");
        String transactionType = Util.getColumnString(rs,"S_TRANSACTION_TYPE");
        String merchantTransactionRef = Util.getColumnString(rs,"S_MERCHANT_TRANS_REF");

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Timestamp date = Timestamp.valueOf(Util.getColumnString(rs,"D_CREATE"));
        String advanceStatus = Util.getColumnString(rs,"S_ADVANCE_STATUS");

        InternationalTransactionHistory internationalTransactionHistory = new InternationalTransactionHistory();
        internationalTransactionHistory.setMerchant_transaction_ref(merchantTransactionRef);
        internationalTransactionHistory.setOriginal_transaction_id(originalId);
        internationalTransactionHistory.setStatus(status.toString());
        internationalTransactionHistory.setOperator_id(operator);
        internationalTransactionHistory.setTransaction_id(transactionId);
        internationalTransactionHistory.setTransaction_type(transactionType);
        internationalTransactionHistory.setTransaction_time(date);
        internationalTransactionHistory.setMerchant_id(merchantId);
        internationalTransactionHistory.setAdvance_status(advanceStatus != null ? advanceStatus : "");
        internationalTransactionHistory.setNote(Util.getColumnString(rs, "S_NOTE"));
        Amount amountData = new Amount();
        amountData.setCurrency(currency);
        amountData.setTotal(amount);
        amountData.setOriginalTotal(originalAmount);

        internationalTransactionHistory.setAmount(amountData);

        return internationalTransactionHistory;
    }

    public static List<InstallmentBankDto> searchInstallmentBankReport(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentBankDto> installmentBankDtos = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_BANK_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "SELECT");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setInt(9, Integer.parseInt(mIn.get("interval").toString()));
//            cs.setInt(10, Integer.parseInt(mIn.get(PAGE).toString()));
//            cs.setInt(11, Integer.parseInt(mIn.get(PAGE_SIZE).toString()));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    installmentBankDtos.add(new InstallmentBankDto(
                            handleFormatDate(rs.getTimestamp("date_bank"))
                            , Util.getColumnString(rs,"installment_bank")
                            , Util.getColumnString(rs,"term_bank")
                            , rs.getInt("quantity_approved_trans")
                            , rs.getDouble("total_approved_trans")
                            , rs.getInt("quantity_reject_trans")
                            , rs.getDouble("total_reject_trans")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return installmentBankDtos;
    }

    public static List<InstallmentMerchantDto> searchInstallmentMerchantReport(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentMerchantDto> installmentMerchantDtos = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_MERCHANT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "SELECT");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(7, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(8, mIn.get(FROM_DATE).toString());
            cs.setString(9, mIn.get(TO_DATE).toString());
            cs.setInt(10, Integer.parseInt(mIn.get("interval").toString()));
            // cs.setInt(11, Integer.parseInt(mIn.get(PAGE).toString()));
            // cs.setInt(12, Integer.parseInt(mIn.get(PAGE_SIZE).toString()));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    installmentMerchantDtos.add(new InstallmentMerchantDto(handleFormatDate(rs.getTimestamp("date_bank")), rs.getString("merchant_id"), rs.getString("installment_bank"), rs.getString("referral_partner"), rs.getString("term_bank"), rs.getInt("purchase_quantity_wait_for"), rs.getDouble("purchase_total_wait_for"), rs.getInt("purchase_quantity_approved"), rs.getDouble("purchase_total_approved"), rs.getInt("purchase_quantity_failed"), rs.getDouble("purchase_total_failed"), rs.getInt("purchase_quantity_void"), rs.getDouble("purchase_total_void"), rs.getInt("refund_quantity_wait_for"), rs.getDouble("refund_total_wait_for"), rs.getInt("refund_quantity_success"), rs.getDouble("refund_total_success")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return installmentMerchantDtos;
    }
    // Hieunt Optimize code function installment merchant
    public static List<InstallmentMerchantDto> searchInstallmentMerchantReport2(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentMerchantDto> installmentMerchantDtos = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_MERCHANT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "SELECT");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(7, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(8, mIn.get(FROM_DATE).toString());
            cs.setString(9, mIn.get(TO_DATE).toString());
            cs.setInt(10, Integer.parseInt(mIn.get("interval").toString()));
            cs.setString(11, mIn.get("referral_partner").toString());
//            cs.setInt(11, Integer.parseInt(mIn.get(PAGE).toString()));
//            cs.setInt(12, Integer.parseInt(mIn.get(PAGE_SIZE).toString()));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    installmentMerchantDtos.add(new InstallmentMerchantDto(handleFormatDate(rs.getTimestamp("date_bank"))
                            , Util.getColumnString(rs,"merchant_id")
                            , Util.getColumnString(rs,"installment_bank")
                            , Util.getColumnString(rs,"referral_partner")
                            , Util.getColumnString(rs,"term_bank")
                            , rs.getInt("purchase_quantity_wait_for")
                            , rs.getDouble("purchase_total_wait_for")
                            , rs.getInt("purchase_quantity_approved")
                            , rs.getDouble("purchase_total_approved")
                            , rs.getInt("purchase_quantity_failed")
                            , rs.getDouble("purchase_total_failed")
                            , rs.getInt("purchase_quantity_void")
                            , rs.getDouble("purchase_total_void")
                            , rs.getInt("refund_quantity_wait_for")
                            , rs.getDouble("refund_total_wait_for")
                            , rs.getInt("refund_quantity_success")
                            , rs.getDouble("refund_total_success")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return installmentMerchantDtos;
    }

    public static Integer getTotalInstallmentBankReport(Map mIn) throws Exception {
        Exception exception = null;
        Integer totalItems = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_BANK_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "TOTAL");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setInt(9, Integer.parseInt(mIn.get("interval").toString()));
//            cs.setObject(10, null);
//            cs.setObject(11, null);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    totalItems = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return totalItems;
    }

    public static Integer getTotalInstallmentMerchantReport(Map mIn) throws Exception {
        Exception exception = null;
        Integer totalItems = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_MERCHANT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "TOTAL");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(7, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(8, mIn.get(FROM_DATE).toString());
            cs.setString(9, mIn.get(TO_DATE).toString());
            cs.setInt(10, Integer.parseInt(mIn.get("interval").toString()));
            cs.setString(11, mIn.get("referral_partner").toString());
            // cs.setObject(11, null);
            // cs.setObject(12, null);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    totalItems = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return totalItems;
    }
    public static Integer getTotalInstallmentMerchantReport2(Map mIn) throws Exception {
        Exception exception = null;
        Integer totalItems = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(ISM_MERCHANT_REPORT);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, "TOTAL");
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(7, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(8, mIn.get(FROM_DATE).toString());
            cs.setString(9, mIn.get(TO_DATE).toString());
            cs.setInt(10, Integer.parseInt(mIn.get("interval").toString()));
//            cs.setObject(11, null);
//            cs.setObject(12, null);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    totalItems = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return totalItems;
    }

    public static BaseList<InstallmentBankDto> searchInstallmentBankReportCommon(Map mIn) throws Exception {
        BaseList<InstallmentBankDto> installmentBankDtoBaseList = new BaseList<>();
        List<InstallmentBankDto> installmentBankDtos = new ArrayList<>();
        Integer totalItem = 0;
        installmentBankDtos = searchInstallmentBankReport(mIn);
        totalItem = getTotalInstallmentBankReport(mIn);
        installmentBankDtoBaseList.setList(installmentBankDtos);
        installmentBankDtoBaseList.setTotalItems(totalItem);
        return installmentBankDtoBaseList;
    }

    public static BaseList<InstallmentMerchantDto> searchInstallmentMerchantReportCommon(Map mIn) throws Exception {
        BaseList<InstallmentMerchantDto> installmentMerchantDtoBaseList = new BaseList<>();
        List<InstallmentMerchantDto> installmentMerchantDtos = new ArrayList<>();
        List<InstallmentMerchantDto> installmentMerchantDtosV2 = new ArrayList<>();
        List<InstallmentMerchantV2Dto> installmentMerchantV2Dtos = new ArrayList<>();
        List<InstallmentMerchantV2Dto> installmentMerchantV3Dtos = new ArrayList<>();
        Integer totalItem = 0;
        //danh sách 2 thằng còn thiếu. transaction Id là key
        installmentMerchantV2Dtos = searchInstallmentMerchantReportV2(mIn);
        StringBuilder listTranId = new StringBuilder();
        if (installmentMerchantV2Dtos.size() > 0) {
            for (int i = 0; i < installmentMerchantV2Dtos.size(); i++) {
                if (installmentMerchantV2Dtos.get(i).getTran_id() != null && !installmentMerchantV2Dtos.get(i).getTran_id().equals("")) {
                    listTranId.append(installmentMerchantV2Dtos.get(i).getTran_id());
                    listTranId.append(",");
                }
            }
        }
        //ra danh sach
        if (!listTranId.toString().equals("")) {
            mIn.put("listTranId", listTranId.toString());
            // danh sách transactionId, d
            installmentMerchantV3Dtos = searchInstallmentMerchantReportV3(mIn);
        }

        if (installmentMerchantV3Dtos.size() > 0) {
            logger.log(Level.SEVERE, "installmentMerchantV3Dtos.size(): ", installmentMerchantV3Dtos.size());
            installmentMerchantDtos = searchInstallmentMerchantReport(mIn);
            for (int i = 0; i < installmentMerchantV2Dtos.size(); i++) {
                for (int j = 0; j < installmentMerchantV3Dtos.size(); j++) {
                    if (installmentMerchantV2Dtos.get(i).getTran_id().equals(installmentMerchantV3Dtos.get(j).getTran_id())) {
                        installmentMerchantV3Dtos.get(j).setDate_bank(installmentMerchantV2Dtos.get(i).getDate_bank());
                        installmentMerchantV3Dtos.get(j).setRefund_quantity_wait_for_trans(installmentMerchantV2Dtos.get(i).getRefund_quantity_wait_for_trans());
                        installmentMerchantV3Dtos.get(j).setRefund_total_wait_for_trans(installmentMerchantV2Dtos.get(i).getRefund_total_wait_for_trans());
                        break;
                    }
                }
            }

            /*START MERGE TWO LIST*/
            List<InstallmentMerchantDto> a = installmentMerchantDtos;
            List<InstallmentMerchantV2Dto> b = installmentMerchantV3Dtos;
            Map<String, InstallmentMerchantDto> mapA = new HashMap<>();
            Map<String, InstallmentMerchantV2Dto> mapB = new HashMap<>();
            List<InstallmentMerchantDto> dtos = new ArrayList<>();
            for (InstallmentMerchantDto aa : a) {
                mapA.put(aa.getDate_bank() + aa.getMerchant_id() + aa.getInstallment_bank() + aa.getTerm_bank(), aa);
            }
            for (InstallmentMerchantV2Dto bb : b) {
                mapB.put(bb.getDate_bank() + bb.getMerchant_id() + bb.getInstallment_bank() + bb.getTerm_bank(), bb);
            }
            mapA.forEach((ak, av) -> {
                InstallmentMerchantDto dto = av;
                if (mapB.get(ak) != null) {
                    dto.setRefund_quantity_wait_for_trans(mapB.get(ak).getRefund_quantity_wait_for_trans());
                    dto.setRefund_total_wait_for_trans(mapB.get(ak).getRefund_total_wait_for_trans());
                }
                dtos.add(dto);
            });
            mapB.forEach((bk, bv) -> {
                InstallmentMerchantDto dto = new InstallmentMerchantDto();
                if (mapA.get(bk) == null) {
                    dto.setDate_bank(bv.getDate_bank());
                    dto.setMerchant_id(bv.getMerchant_id());
                    dto.setInstallment_bank(bv.getInstallment_bank());
                    dto.setTerm_bank(bv.getTerm_bank());

                    dto.setRefund_total_wait_for_trans(bv.getRefund_total_wait_for_trans());
                    dto.setRefund_quantity_wait_for_trans(bv.getRefund_quantity_wait_for_trans());

                    dtos.add(dto);
                }
            });
            /*START MERGE TWO LIST*/
            //Sort here
            SimpleDateFormat f = new SimpleDateFormat("dd-MM-yyyy");
            Collections.sort(dtos, Comparator.comparing((InstallmentMerchantDto instrument) -> {
                try {
                    return f.parse(instrument.getDate_bank());
                } catch (ParseException e) {
                    logger.log(Level.SEVERE, "Error: ", e);
                }
                return null;
            }).thenComparing(InstallmentMerchantDto::getMerchant_id)
                    .thenComparing(InstallmentMerchantDto::getInstallment_bank)
                    .thenComparing((InstallmentMerchantDto instrument) -> Integer.parseInt(instrument.getTerm_bank())));
            logger.log(Level.SEVERE, "Sort done ", "");
            installmentMerchantDtoBaseList.setList(dtos);
            installmentMerchantDtoBaseList.setTotalItems(dtos.size());
        } else {
            logger.log(Level.SEVERE, "sort in db: ", "");
            installmentMerchantDtos = searchInstallmentMerchantReport(mIn);
            installmentMerchantDtoBaseList.setList(installmentMerchantDtos);
            installmentMerchantDtoBaseList.setTotalItems(installmentMerchantDtos.size());
        }

        return installmentMerchantDtoBaseList;
    }
    //Toi uu lai 
    public static BaseList<InstallmentMerchantDto> searchInstallmentMerchantReportCommon2(Map mIn) throws Exception {
        BaseList<InstallmentMerchantDto> installmentMerchantDtoBaseList = new BaseList<>();
        List<InstallmentMerchantDto> installmentMerchantDtos = new ArrayList<>();            
            logger.log(Level.SEVERE, "sort in db: ", "");
            installmentMerchantDtos = searchInstallmentMerchantReport2(mIn);
            installmentMerchantDtoBaseList.setList(installmentMerchantDtos);
            installmentMerchantDtoBaseList.setTotalItems(installmentMerchantDtos.size());

        return installmentMerchantDtoBaseList;
    }

    public static List<InstallmentMerchantV2Dto> searchInstallmentMerchantReportV3(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentMerchantV2Dto> installmentMerchantV3Dtos = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection113();
            cs = con.prepareCall(SEARCH_INSTALLMENT_MERCHANT_V3);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get("listTranId").toString());
            cs.setInt(5, Integer.parseInt(mIn.get("type_filter").toString()));
            cs.setString(6, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(7, mIn.get("list_bank") == null ? null : mIn.get("list_bank").toString());
            cs.setString(8, mIn.get(FROM_DATE).toString());
            cs.setString(9, mIn.get(TO_DATE).toString());
            cs.setInt(10, Integer.parseInt(mIn.get("interval").toString()));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    installmentMerchantV3Dtos.add(new InstallmentMerchantV2Dto(
                            String.valueOf(rs.getInt("tran_id"))
                            , handleFormatDate(rs.getTimestamp("date_bank"))
                            , Util.getColumnString(rs,"merchant_id")
                            , Util.getColumnString(rs,"installment_bank")
                            , Util.getColumnString(rs,"term_bank")
                            , 0
                            , 0.0));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return installmentMerchantV3Dtos;
    }

    public static List<InstallmentMerchantV2Dto> searchInstallmentMerchantReportV2(Map mIn) throws Exception {
        Exception exception = null;
        List<InstallmentMerchantV2Dto> installmentMerchantV2Dtos = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(SEARCH_INSTALLMENT_MERCHANT_V2);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, mIn.get("merchant_id") == null ? null : mIn.get("merchant_id").toString());
            cs.setString(5, mIn.get(FROM_DATE).toString());
            cs.setString(6, mIn.get(TO_DATE).toString());
            cs.setInt(7, Integer.parseInt(mIn.get("interval").toString()));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international transaction by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    installmentMerchantV2Dtos.add(new InstallmentMerchantV2Dto(
                            String.valueOf(rs.getInt("tran_id"))
                            , handleFormatDate(rs.getTimestamp("date_bank"))
                            , Util.getColumnString(rs,"merchant_id")
                            , null
                            , null
                            , rs.getInt("refund_quantity_wait_for")
                            , rs.getDouble("refund_total_wait_for")));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return installmentMerchantV2Dtos;
    }

    private static String handleFormatDate(Timestamp inputData) {
        String outputData = "";
        if (inputData != null) {
            try {
                outputData = Util.formatDate(new Date(inputData.getTime()), "dd-MM-yyyy");
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Convert Date error", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }
        return outputData;
    }

    public static BaseList authorizeTransactionHistory(String transactionId) throws Exception {
        List<InternationalTransactionHistory> transactionsFinal = new ArrayList<>();
        BaseList result = new BaseList<>();

        List<InternationalTransactionHistory> onlineHistory = getAuthorizeHistory(transactionId, -1);
        List<InternationalTransactionHistory> readonlyHistory = getAuthorizeHistory(transactionId, onlineHistory.size());
        List<InternationalTransactionHistory> backupHistory = getAuthorizeHistoryBackup(transactionId);

        transactionsFinal.addAll(onlineHistory);
        transactionsFinal.addAll(readonlyHistory);
        transactionsFinal.addAll(backupHistory);

        // sort by trans time.
        transactionsFinal
                .sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
        result.setList(transactionsFinal);
//        return transactionsFinal;
        return result;
    }

    private static List<InternationalTransactionHistory> getAuthorizeHistory(String transactionId, int row) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = row >= 0 ? getConnection113() : getConnection112();
            cs = con.prepareCall(LIST_AUTHORIZE_TRANSACTION_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, row);
            cs.setInt(5, Convert.parseInt(transactionId, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get authorize international history by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindTransactionHistory(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<InternationalTransactionHistory> getAuthorizeHistoryBackup(String transactionId) throws Exception {
        Exception exception = null;
        List<InternationalTransactionHistory> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_CAPTURE_REFUND_APPROVAL_HISTORY);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, Convert.parseInt(transactionId, 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);


            if (nError != 200) {
                throw new Exception("DB get international history backup by id error: " + error);
            } else {


                List<InternationalTransactionHistory> childList = new ArrayList<>();
                List<InternationalTransactionHistory> parentList = new ArrayList<>();
                while (rs != null && rs.next()) {
                    InternationalTransactionHistory transaction = bindApprovalHistory(rs);

                    if (transaction.getParent_id() == null) {
                        parentList.add(transaction);
                    } else {
                        childList.add(transaction);
                    }
                }
                Map<Integer, List<InternationalTransactionHistory>> childMap = childList.stream()
                        .collect(Collectors.groupingBy(InternationalTransactionHistory::getParent_id));

                result = parentList.stream().map(parent -> {
                    InternationalTransactionHistory re = new InternationalTransactionHistory(parent);

                    if (childMap.get(parent.getTransaction_id()) != null) {
                        re.setSubHistories(childMap.get(parent.getTransaction_id()));
                    } else {
                        re.setSubHistories(new ArrayList<>());
                    }
                    re.getSubHistories().add(parent);
                    re.getSubHistories().sort((i1, i2) -> i2.getTransaction_time().compareTo(i1.getTransaction_time()));
                    return re;
                })
                        .sorted((i1, i2) -> i1.getTransaction_time().compareTo(i2.getTransaction_time()))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<AcquirerDto> getAcquirers(String payChannel) throws Exception {
        CallableStatement cs = null;
        ResultSet rs = null;
        Connection con = null;
        List<AcquirerDto> result = new ArrayList<>();
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call get_acquirer_by_channel(?,?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, payChannel);
            cs.execute();

            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);

            if (nResult != 200) {
                throw new Exception("DB get_acquirer_by_channel: " + sResult);
            }
            AcquirerDto dto;
            while (rs != null && rs.next()) {
                dto = new AcquirerDto();
                dto.setAcquirerId(Util.getColumnInteger(rs, "n_id"));
                dto.setAcquirerName(Util.getColumnString(rs, "s_name"));
                dto.setGroupAcquirer(Util.getColumnString(rs, "s_group_acquirer"));
                dto.setCutOff(Util.getColumnInteger(rs, "n_cutoff"));
                result.add(dto);
            }

        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }
}
