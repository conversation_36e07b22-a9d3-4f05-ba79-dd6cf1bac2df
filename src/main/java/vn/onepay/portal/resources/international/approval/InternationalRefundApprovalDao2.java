package vn.onepay.portal.resources.international.approval;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval2;

public class InternationalRefundApprovalDao2 extends Db {
    private static final Logger logger = Logger.getLogger(InternationalRefundApprovalDao2.class.getName());

    private static final String LIST_REQUEST_REFUND_118 = "{call PKG_RP_INTERNATIONAL_TXN.search_request_refund_v5(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    
    public static List<InternationalRefundApproval2> searchRefund(Map mIn) throws Exception {
        Exception exception = null;
        List<InternationalRefundApproval2> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection118Report();
            cs = con.prepareCall(LIST_REQUEST_REFUND_118);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setObject(4, QueryMethod.SELECT.toString());
            cs.setObject(5, mIn.getOrDefault(MERCHANTID, "").toString());
            cs.setObject(6, mIn.getOrDefault(TRANSACTIONID, "").toString());
            cs.setObject(7, mIn.getOrDefault(FROMDATE, "").toString());
            cs.setObject(8, mIn.getOrDefault(TODATE, "").toString());
            cs.setObject(9, mIn.getOrDefault(NETWORKTRANSACTIONID, "").toString());
            cs.setObject(10, mIn.getOrDefault(ORDERINFO, "").toString());
            cs.setObject(11, mIn.getOrDefault(AUTHCODE, "").toString());
            cs.setObject(12, mIn.getOrDefault(ACQUIRERID, "").toString());
            cs.setObject(13, mIn.getOrDefault(CARDNUMBER, "").toString());
            cs.setObject(14, mIn.getOrDefault(SOURCE, "").toString());
            cs.setObject(15, mIn.getOrDefault(TOKENNUMBER, "").toString());
            cs.setObject(16, mIn.getOrDefault(CURRENCY, "").toString());
            cs.setObject(17, mIn.getOrDefault(GATE, "").toString());
            cs.setObject(18, mIn.getOrDefault(STATUS, "").toString());
            cs.setObject(19, mIn.getOrDefault(ACTION_TYPE, "").toString());
            cs.setObject(20, mIn.getOrDefault(REVIEW_TXN, "").toString());

            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindApproval2(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static InternationalRefundApproval2 bindApproval2(ResultSet rs) throws Exception {
        InternationalRefundApproval2 transaction = new InternationalRefundApproval2();
        transaction.setRefundId(Util.getColumnString(rs, "S_ID"));
        transaction.setTransactionId(Util.getColumnString(rs, "S_PURCHASE_ID"));
        transaction.setRefundDate(Util.getColumnTimeStamp(rs, "D_DATE"));
        transaction.setPurchaseDate(Util.getColumnTimeStamp(rs, "D_ORIGINAL_DATE"));
        transaction.setMerchantIdMigs(Util.getColumnString(rs, "S_MERCHANT_MIGS"));
        transaction.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID_INVOICE"));
        transaction.setOrderRef(Util.getColumnString(rs, "S_ORDER_INFO"));
        transaction.setMerchantTransactionRef(Util.getColumnString(rs, "S_ORIGINAL_ID"));
        transaction.setRefundRef(Util.getColumnString(rs, "S_MERCHANT_TRANSACTION_REF"));
        transaction.setPurchaseAmount(Util.getColumnDouble(rs, "N_PURCHASE_AMOUNT"));
        transaction.setRefundAmount(Util.getColumnDouble(rs, "N_AMOUNT"));
        transaction.setAuthCode(Util.getColumnString(rs, "S_AUTHORISATION_CODE"));
        transaction.setRefundApproveType(Util.getColumnString(rs, "S_REFUND_APPROVE_TYPE"));
        transaction.setCurrency(Util.getColumnString(rs, "S_CURRENCY"));
        transaction.setOperator(Util.getColumnString(rs, "S_OPERATOR"));
        transaction.setType(Util.getColumnString(rs, "S_TYPE"));
        transaction.setCardType(Util.getColumnString(rs, "S_CARD_TYPE"));
        transaction.setCardNo(Util.getColumnString(rs, "S_CARD_NO"));
        transaction.setAcquirer(Util.getColumnString(rs, "S_ACQUIRER"));
        transaction.setTransactionNo(Util.getColumnString(rs, "S_TRANSACTION_NO"));
        transaction.setBinCountry(Util.getColumnString(rs, "S_BIN_COUNTRY"));
        transaction.setItaBank(Util.getColumnString(rs, "S_ITA_BANK"));
        transaction.setItaTime(Util.getColumnString(rs, "S_ITA_TIME"));
        transaction.setHashCode(Util.getColumnString(rs, "S_HASH_CODE"));
        transaction.setAccessCode(Util.getColumnString(rs, "S_ACCESS_CODE"));
        transaction.setErrorStatus((Util.getColumnInteger(rs, "ERROR_STATUS") == null || Util.getColumnInteger(rs, "ERROR_STATUS").equals(0)) ? null : Util.getColumnInteger(rs, "ERROR_STATUS"));
        transaction.setErrorMessage(Util.getColumnString(rs, "ERROR_MESSAGE"));
        transaction.setConfirmable(Util.getColumnInteger(rs, "N_CONFIRMABLE"));
        transaction.setNetworkTransId(Util.getColumnString(rs, "S_NETWORK_TRANS_ID"));
        transaction.setSource(Util.getColumnString(rs, "S_SOURCE"));
        transaction.setTokenNumber(Util.getColumnString(rs, "S_TOKEN_NUMBER"));
        transaction.setStatus(Util.getColumnString(rs, "S_STATUS"));
        transaction.setActionType(Util.getColumnString(rs, "S_ACTION_TYPE"));
        transaction.setBankMerchantId(Util.getColumnString(rs, "S_BANK_MERCHANT_ID"));
        transaction.setAcqResCode(Util.getColumnString(rs, "S_ACQ_RESPONSE_CODE"));

        return transaction;
    }
}
