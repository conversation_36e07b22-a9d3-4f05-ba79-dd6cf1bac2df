package vn.onepay.portal.resources.international.transaction.dto;

import vn.onepay.portal.resources.base.dto.Amount;

import java.sql.Timestamp;

/**
 * Created by tuydv on 09-5-18.
 */
public class InternationalTransaction {

    private Integer row_num;
    private Integer id;
    private String trans_no;
    private Timestamp date;
    private Timestamp originalDate;
    private String merchant_id;
    private String acquirer;
    private String acquirerName;
    private String trans_ref;
    private String trans_type;
    private Amount amount;
    private String  currency;
    private String contract_type;
    private String s_msp_id;

    private String response;
    private String order_ref;
    private String card_number;
    private String card_name;
    private String card_email;
    private String card_phone;
    private String auth_code;
    private String payment_auth_id;
    private String batch_number;
    private String tranaction_id;
    private String card_type;
    private String base_status;
    private String note;
    //Installment
    private String installment_status;
    private String installment_bank;
    private String installment_time;
    private Timestamp installment_date;
    private String installment_amount;
    // SAMSUNG
    private String customer_mobile;
    private String customer_email;
    private String epp;
    private String fraud_check;
    private String operator;

    // Channel UPOS/ ECOM
    private String  channel;

    //APPLE PAY
    private String source;
    private String networkTransId;
    private String tokenNumber;
    private String deviceId;
    private String tokenExpiry;
    private String type;
    private String reason;

    // ADAYROI
    private String customer_name;
    
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getNetworkTransId() {
        return networkTransId;
    }

    public void setNetworkTransId(String networkTransId) {
        this.networkTransId = networkTransId;
    }

    public String getTokenNumber() {
        return tokenNumber;
    }

    public void setTokenNumber(String tokenNumber) {
        this.tokenNumber = tokenNumber;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getTokenExpiry() {
        return tokenExpiry;
    }

    public void setTokenExpiry(String tokenExpiry) {
        this.tokenExpiry = tokenExpiry;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Timestamp getInstallment_date() {
        return installment_date;
    }

    public void setInstallment_date(Timestamp installment_date) {
        this.installment_date = installment_date;
    }

    public String getInstallment_amount() {
        return installment_amount;
    }

    public void setInstallment_amount(String installment_amount) {
        this.installment_amount = installment_amount;
    }

    public String getInstallment_time() {
      return installment_time;
    }

    public String getInstallment_status() {
      return installment_status;
    }

    public void setInstallment_status(String installment_status) {
      this.installment_status = installment_status;
    }

    public void setInstallment_time(String installment_time) {
      this.installment_time = installment_time;
    }


    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getEpp() {
        return epp;
    }

    public void setEpp(String epp) {
        this.epp = epp;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getBase_status() {
        return base_status;
    }

    public void setBase_status(String base_status) {
        this.base_status = base_status;
    }

    public Integer getRow_num() {
        return row_num;
    }

    public void setRow_num(Integer row_num) {
        this.row_num = row_num;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTrans_no() {
        return trans_no;
    }

    public void setTrans_no(String trans_no) {
        this.trans_no = trans_no;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getTrans_ref() {
        return trans_ref;
    }

    public void setTrans_ref(String trans_ref) {
        this.trans_ref = trans_ref;
    }

    public String getTrans_type() {
        return trans_type;
    }

    public void setTrans_type(String trans_type) {
        this.trans_type = trans_type;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getContract_type() {
        return this.contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public String getS_MSP_ID() {
        return this.s_msp_id;
    }

    public void setS_MSP_ID(String s_msp_id) {
        this.s_msp_id = s_msp_id;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getOrder_ref() {
        return order_ref;
    }

    public void setOrder_ref(String order_ref) {
        this.order_ref = order_ref;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getAuth_code() {
        return auth_code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public String getPayment_auth_id() {
        return payment_auth_id;
    }

    public void setPayment_auth_id(String payment_auth_id) {
        this.payment_auth_id = payment_auth_id;
    }

    public String getBatch_number() {
        return batch_number;
    }

    public void setBatch_number(String batch_number) {
        this.batch_number = batch_number;
    }

    public String getTranaction_id() {
        return tranaction_id;
    }

    public void setTranaction_id(String tranaction_id) {
        this.tranaction_id = tranaction_id;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * @return the originalDate
     */
    public Timestamp getOriginalDate() {
        return originalDate;
    }

    /**
     * @param originalDate the originalDate to set
     */
    public void setOriginalDate(Timestamp originalDate) {
        this.originalDate = originalDate;
    }
    
    /**
     * @return String return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }


    /**
     * @return String return the acquirerName
     */
    public String getAcquirerName() {
        return acquirerName;
    }

    /**
     * @param acquirerName the acquirerName to set
     */
    public void setAcquirerName(String acquirerName) {
        this.acquirerName = acquirerName;
    }

    public String getS_msp_id() {
        return s_msp_id;
    }

    public void setS_msp_id(String s_msp_id) {
        this.s_msp_id = s_msp_id;
    }

    public String getCard_name() {
        return card_name;
    }

    public void setCard_name(String card_name) {
        this.card_name = card_name;
    }

    public String getCard_email() {
        return card_email;
    }

    public void setCard_email(String card_email) {
        this.card_email = card_email;
    }

    public String getCard_phone() {
        return card_phone;
    }

    public void setCard_phone(String card_phone) {
        this.card_phone = card_phone;
    }
}
