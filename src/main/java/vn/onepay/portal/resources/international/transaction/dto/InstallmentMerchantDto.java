package vn.onepay.portal.resources.international.transaction.dto;

import java.time.LocalDate;

/**
 * Created by tuydv on 09-5-18.
 */
public class InstallmentMerchantDto {

   private String date_bank;
   private String merchant_id;
   private String installment_bank;
   private String referral_partner;
   private String term_bank;
   private Integer purchase_quantity_wait_for_trans;
   private Double purchase_total_wait_for_trans;
   private Integer purchase_quantity_approved_trans;
   private Double purchase_total_approved_trans;
   private Integer purchase_quantity_failed_trans;
   private Double purchase_total_failed_trans;
   private Integer purchase_quantity_void_trans;
   private Double purchase_total_void_trans;
   private Integer refund_quantity_wait_for_trans;
   private Double refund_total_wait_for_trans;
   private Integer refund_quantity_success_trans;
   private Double refund_total_success_trans;

    public InstallmentMerchantDto() {
    }

    public InstallmentMerchantDto(String date_bank, String merchant_id, String installment_bank, String referral_partner, String term_bank, Integer purchase_quantity_wait_for_trans, Double purchase_total_wait_for_trans, Integer purchase_quantity_approved_trans, Double purchase_total_approved_trans, Integer purchase_quantity_failed_trans, Double purchase_total_failed_trans, Integer purchase_quantity_void_trans, Double purchase_total_void_trans, Integer refund_quantity_wait_for_trans, Double refund_total_wait_for_trans, Integer refund_quantity_success_trans, Double refund_total_success_trans) {
        this.date_bank = date_bank;
        this.merchant_id = merchant_id;
        this.installment_bank = installment_bank;
        this.referral_partner = referral_partner;
        this.term_bank = term_bank;
        this.purchase_quantity_wait_for_trans = purchase_quantity_wait_for_trans;
        this.purchase_total_wait_for_trans = purchase_total_wait_for_trans;
        this.purchase_quantity_approved_trans = purchase_quantity_approved_trans;
        this.purchase_total_approved_trans = purchase_total_approved_trans;
        this.purchase_quantity_failed_trans = purchase_quantity_failed_trans;
        this.purchase_total_failed_trans = purchase_total_failed_trans;
        this.purchase_quantity_void_trans = purchase_quantity_void_trans;
        this.purchase_total_void_trans = purchase_total_void_trans;
        this.refund_quantity_wait_for_trans = refund_quantity_wait_for_trans;
        this.refund_total_wait_for_trans = refund_total_wait_for_trans;
        this.refund_quantity_success_trans = refund_quantity_success_trans;
        this.refund_total_success_trans = refund_total_success_trans;
    }

    public String getDate_bank() {
        return date_bank;
    }

    public void setDate_bank(String date_bank) {
        this.date_bank = date_bank;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getTerm_bank() {
        return term_bank;
    }

    public void setTerm_bank(String term_bank) {
        this.term_bank = term_bank;
    }

    public Integer getPurchase_quantity_wait_for_trans() {
        return purchase_quantity_wait_for_trans;
    }

    public void setPurchase_quantity_wait_for_trans(Integer purchase_quantity_wait_for_trans) {
        this.purchase_quantity_wait_for_trans = purchase_quantity_wait_for_trans;
    }

    public Double getPurchase_total_wait_for_trans() {
        return purchase_total_wait_for_trans;
    }

    public void setPurchase_total_wait_for_trans(Double purchase_total_wait_for_trans) {
        this.purchase_total_wait_for_trans = purchase_total_wait_for_trans;
    }

    public Integer getPurchase_quantity_approved_trans() {
        return purchase_quantity_approved_trans;
    }

    public void setPurchase_quantity_approved_trans(Integer purchase_quantity_approved_trans) {
        this.purchase_quantity_approved_trans = purchase_quantity_approved_trans;
    }

    public Double getPurchase_total_approved_trans() {
        return purchase_total_approved_trans;
    }

    public void setPurchase_total_approved_trans(Double purchase_total_approved_trans) {
        this.purchase_total_approved_trans = purchase_total_approved_trans;
    }

    public Integer getPurchase_quantity_failed_trans() {
        return purchase_quantity_failed_trans;
    }

    public void setPurchase_quantity_failed_trans(Integer purchase_quantity_failed_trans) {
        this.purchase_quantity_failed_trans = purchase_quantity_failed_trans;
    }

    public Double getPurchase_total_failed_trans() {
        return purchase_total_failed_trans;
    }

    public void setPurchase_total_failed_trans(Double purchase_total_failed_trans) {
        this.purchase_total_failed_trans = purchase_total_failed_trans;
    }

    public Integer getPurchase_quantity_void_trans() {
        return purchase_quantity_void_trans;
    }

    public void setPurchase_quantity_void_trans(Integer purchase_quantity_void_trans) {
        this.purchase_quantity_void_trans = purchase_quantity_void_trans;
    }

    public Double getPurchase_total_void_trans() {
        return purchase_total_void_trans;
    }

    public void setPurchase_total_void_trans(Double purchase_total_void_trans) {
        this.purchase_total_void_trans = purchase_total_void_trans;
    }

    public Integer getRefund_quantity_wait_for_trans() {
        return refund_quantity_wait_for_trans;
    }

    public void setRefund_quantity_wait_for_trans(Integer refund_quantity_wait_for_trans) {
        this.refund_quantity_wait_for_trans = refund_quantity_wait_for_trans;
    }

    public Double getRefund_total_wait_for_trans() {
        return refund_total_wait_for_trans;
    }

    public void setRefund_total_wait_for_trans(Double refund_total_wait_for_trans) {
        this.refund_total_wait_for_trans = refund_total_wait_for_trans;
    }

    public Integer getRefund_quantity_success_trans() {
        return refund_quantity_success_trans;
    }

    public void setRefund_quantity_success_trans(Integer refund_quantity_success_trans) {
        this.refund_quantity_success_trans = refund_quantity_success_trans;
    }

    public Double getRefund_total_success_trans() {
        return refund_total_success_trans;
    }

    public void setRefund_total_success_trans(Double refund_total_success_trans) {
        this.refund_total_success_trans = refund_total_success_trans;
    }

    public String getReferral_partner() {
        return referral_partner;
    }

    public void setReferral_partner(String referral_partner) {
        this.referral_partner = referral_partner;
    }
}
