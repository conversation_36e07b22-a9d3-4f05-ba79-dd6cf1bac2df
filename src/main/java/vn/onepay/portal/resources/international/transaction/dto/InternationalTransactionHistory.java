package vn.onepay.portal.resources.international.transaction.dto;

import vn.onepay.portal.resources.base.dto.Amount;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/1/16.
 */
public class InternationalTransactionHistory {


    private int transaction_id;
    private int original_transaction_id;
    private String response_code;
    private String reference_number;
    private String merchant_transaction_ref;
    private String transaction_type;
    private Amount amount;
    private String status;
    private String operator_id;
    private String merchant_id;
    private Timestamp transaction_time;
    private String advance_status;
    private String financial_transaction_id;
    private String note;

    private Integer parent_id;
    private Boolean is_msp_refund;
    //    private Integer id;
    private List<InternationalTransactionHistory> subHistories;
    public InternationalTransactionHistory() {

    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public InternationalTransactionHistory(InternationalTransactionHistory source) {
        this.transaction_id = source.transaction_id;
        this.original_transaction_id = source.original_transaction_id;
        this.merchant_transaction_ref = source.merchant_transaction_ref;
        this.transaction_type = source.transaction_type;
        this.amount = source.amount;
        this.status = source.status;
        this.operator_id = source.operator_id;
        this.transaction_time = source.transaction_time;
        this.parent_id = source.parent_id;
        this.subHistories = source.subHistories;
        this.advance_status =  source.getAdvance_status();
        this.financial_transaction_id = source.getFinancial_transaction_id();
        this.merchant_id = source.getMerchant_id();
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public List<InternationalTransactionHistory> getSubHistories() {
        return subHistories;
    }

    public void setSubHistories(List<InternationalTransactionHistory> subHistories) {
        this.subHistories = subHistories;
    }

    public String getAdvance_status() {
        return advance_status;
    }

    public void setAdvance_status(String advance_status) {
        this.advance_status = advance_status;
    }

    public int getOriginal_transaction_id() {
        return original_transaction_id;
    }

    public void setOriginal_transaction_id(int original_transaction_id) {
        this.original_transaction_id = original_transaction_id;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }

    public String getReference_number() {
        return reference_number;
    }

    public void setReference_number(String reference_number) {
        this.reference_number = reference_number;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getMerchant_transaction_ref() {
        return merchant_transaction_ref;
    }

    public void setMerchant_transaction_ref(String merchant_transaction_ref) {
        this.merchant_transaction_ref = merchant_transaction_ref;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperator_id() {
        return operator_id;
    }

    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getFinancial_transaction_id() {
        return financial_transaction_id;
    }

    public void setFinancial_transaction_id(String financial_transaction_id) {
        this.financial_transaction_id = financial_transaction_id;
    }

    /**
     * @return String return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }

    public Boolean getIs_msp_refund() {
        return is_msp_refund;
    }

    public void setIs_msp_refund(Boolean is_msp_refund) {
        this.is_msp_refund = is_msp_refund;
    }

}
