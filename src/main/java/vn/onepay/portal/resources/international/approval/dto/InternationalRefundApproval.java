package vn.onepay.portal.resources.international.approval.dto;

import vn.onepay.portal.resources.international.transaction.dto.Acquirer;
import vn.onepay.portal.resources.international.transaction.dto.AuthenticationData;
import vn.onepay.portal.resources.international.transaction.dto.InternationalCard;

import java.sql.Timestamp;

/**
 * Created by anhkh on 4/3/16.
 */
public class InternationalRefundApproval {
    private int row_num;
    private String merchant_id;
    private String order_info;
    private Acquirer acquirer;
    private int original_transaction_id;
    private int transaction_id;
    private Timestamp transaction_time;
    private String transaction_type;
    private String transaction_status;
    private String transaction_status_2;
    private String response_code;
    private String transaction_reference;
    private AuthenticationData authentication;
    private String operator;
    private RefundAmount amount;
    private boolean can_void;
    private Timestamp transaction_purchase_time;
    private InternationalCard card;
    private String csc_result_code;
    private String mid_no;
    private int n_step_approval; // 0: refund tức thời, 1: duyệt 1 lần, 2: duyệt 2 lần

    public int getN_step_approval() {
        return n_step_approval;
    }

    public void setN_step_approval(int n_step_approval) {
        this.n_step_approval = n_step_approval;
    }

    public String getTransaction_status_2() {
        return transaction_status_2;
    }

    public void setTransaction_status_2(String transaction_status_2) {
        this.transaction_status_2 = transaction_status_2;
    }

    public String getMid_no() {
        return mid_no;
    }

    public void setMid_no(String mid_no) {
        this.mid_no = mid_no;
    }

    public int getRow_num() {
        return row_num;
    }

    public String getCsc_result_code() {
        return csc_result_code;
    }

    public void setCsc_result_code(String csc_result_code) {
        this.csc_result_code = csc_result_code;
    }

    public Timestamp getTransaction_purchase_time() {
        return transaction_purchase_time;
    }

    public void setTransaction_purchase_time(Timestamp transaction_purchase_time) {
        this.transaction_purchase_time = transaction_purchase_time;
    }

    public InternationalCard getCard() {
        return card;
    }

    public void setCard(InternationalCard card) {
        this.card = card;
    }

    public void setRow_num(int row_num) {
        this.row_num = row_num;
    }

    public boolean isCan_void() {
        return can_void;
    }

    public void setCan_void(boolean can_void) {
        this.can_void = can_void;
    }

    public AuthenticationData getAuthentication() {
        return authentication;
    }

    public void setAuthentication(AuthenticationData authentication) {
        this.authentication = authentication;
    }



    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getOrder_info() {
        return order_info;
    }

    public void setOrder_info(String order_info) {
        this.order_info = order_info;
    }

    public Acquirer getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(Acquirer acquirer) {
        this.acquirer = acquirer;
    }

    public int getOriginal_transaction_id() {
        return original_transaction_id;
    }

    public void setOriginal_transaction_id(int original_transaction_id) {
        this.original_transaction_id = original_transaction_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public Timestamp getTransaction_time() {
        return transaction_time;
    }

    public void setTransaction_time(Timestamp transaction_time) {
        this.transaction_time = transaction_time;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getTransaction_status() {
        return transaction_status;
    }

    public void setTransaction_status(String transaction_status) {
        this.transaction_status = transaction_status;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }

    public String getTransaction_reference() {
        return transaction_reference;
    }

    public void setTransaction_reference(String transaction_reference) {
        this.transaction_reference = transaction_reference;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public RefundAmount getAmount() {
        return amount;
    }

    public void setAmount(RefundAmount amount) {
        this.amount = amount;
    }
}
