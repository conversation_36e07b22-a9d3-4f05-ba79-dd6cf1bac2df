package vn.onepay.portal.resources.international.transaction.dto;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/29/16.
 */
public class InternationalCard {
    private String card_number;
    private String card_type;
    private CardDate card_date;
    private String commercical_card;
    private String commercial_card_indicator;
    private String name;
    private String email;
    private String phone;

    public String getCommercical_card() {
        return commercical_card;
    }

    public void setCommercical_card(String commercical_card) {
        this.commercical_card = commercical_card;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public CardDate getCard_date() {
        return card_date;
    }

    public void setCard_date(CardDate card_date) {
        this.card_date = card_date;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getCommercial_card_indicator() {
        return commercial_card_indicator;
    }

    public void setCommercial_card_indicator(String commercial_card_indicator) {
        this.commercial_card_indicator = commercial_card_indicator;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
