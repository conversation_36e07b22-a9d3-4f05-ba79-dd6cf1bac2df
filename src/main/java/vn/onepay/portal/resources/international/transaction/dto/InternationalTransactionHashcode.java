package vn.onepay.portal.resources.international.transaction.dto;

public class InternationalTransactionHashcode {
    private Integer n_id;
    private String s_merchant_id;
    private String transref;
    private String hashcode;
    private String accesscode;

    public InternationalTransactionHashcode() {
    }

    public Integer getN_id() {
        return n_id;
    }
    public void setN_id(Integer n_id) {
        this.n_id = n_id;
    }
    public String get_merchant_id() {
        return s_merchant_id;
    }
    public void setMerchantId(String s_merchant_id) {
        this.s_merchant_id = s_merchant_id;
    }
    public String getTransref() {
        return transref;
    }
    public void setTransref(String transref) {
        this.transref = transref;
    }
    public String getHashcode() {
        return hashcode;
    }
    public void setHashcode(String hashcode) {
        this.hashcode = hashcode;
    }
    public String getAccesscode() {
        return accesscode;
    }
    public void setAccesscode(String accesscode) {
        this.accesscode = accesscode;
    }
}
