package vn.onepay.portal.resources.international.approval;

import io.vertx.core.http.HttpClient;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.MaClient;
import vn.onepay.portal.client.MspClient;
import vn.onepay.portal.client.OneCreditClient;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval2;
import vn.onepay.portal.resources.international.approval.dto.MerchantData;
import vn.onepay.portal.resources.permission.PermissionDao;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import static vn.onepay.portal.Util.sendResponse;

public class InternationalRefundApprovalHandler implements IConstants {

    private static Logger logger = Logger.getLogger(InternationalRefundApprovalHandler.class.getName());


    public static void searchInternationalRefundApproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Integer userId = ctx.get(X_USER_ID);

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                String fromDate = ctx.request().getParam(FROMDATE);
                String toDate = ctx.request().getParam(TODATE);
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL GET ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                // if (months > 12) {
                // logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL GET ] => INVALID DATE ");
                // throw IErrors.SEARCH_TOO_LARGE_ERROR;
                // }
                Map mIn = Util.convertRequestParamsToMap(ctx.request());
                mIn.put(FROMDATE, fromDate);
                mIn.put(TODATE, toDate);
                mIn.put(X_USER_ID, userId);

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANTID) ? mIn.get(MERCHANTID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANTID, merchants);
                    }
                }

                BaseList<InternationalRefundApproval2> baseList = new BaseList<>();
                List<InternationalRefundApproval2> l = new ArrayList<>();
                l.addAll(InternationalRefundApprovalDao.searchTransactionInvoice(mIn));
                l.addAll(InternationalRefundApprovalDao.searchTransactionMADM(mIn));
                l.sort((o1, o2) -> o1.getRefundDate().compareTo(o2.getRefundDate()));
                baseList.setList(l);

                sendResponse(ctx, 200, baseList);


            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getInternationalRefundApproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();

                String id = ctx.request().getParam(ID);
                sendResponse(ctx, 200, InternationalRefundApprovalDao.getInternationalRefundApprovalById(Integer.valueOf(id)));
            } catch (Exception e) {
                logger.log(Level.WARNING, "SEARCH TRANSACTION SEARCH: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void patchInternationalRefundApproval(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                final HttpClient httpClient = ctx.vertx().createHttpClient();
                String userId = ctx.get(S_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL PATCH ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                if (xRequestId == null) {
                    logger.log(Level.SEVERE, "[ INTERNATIONAL REFUND APPROVAL PATCH ] =>  REQUEST EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }
                String id = ctx.request().getParam(ID);

                // RefundApproval approvalRecord = RefundApprovalDao.getRefundApprovalById(Integer.valueOf(id));
                // if (approvalRecord.getStatus().compareTo(RefundApproval.Status.REQUEST.code) != 0
                // && approvalRecord.getStatus().compareTo(RefundApproval.Status.REQUEST_ONEPAY.code) != 0) {
                // logger.log(Level.SEVERE, "[ International REFUND APPROVAL] => REFUND APPROVAL DONE ALREADY");
                // throw IErrors.DUPLICATE_REFUND_ERROR;
                // }
                // logger.log(Level.INFO, "REFUND APPROVAL INFO:" + approvalRecord.toString());

                JsonObject body = ctx.getBodyAsJson();
                String action = body.getString("path");
                String type = body.getString("type");

                if (action.equals("/approve") && type.equalsIgnoreCase("madm")) {
                    sendResponse(ctx, 200, MaClient.approveRefundApporval(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")));
                } else if (action.equals("/reject") && type.equalsIgnoreCase("madm")) {
                    sendResponse(ctx, 200, MaClient.rejectRefundApporval(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")));
                } else if (action.equals("/approve") && type.equalsIgnoreCase("invoice")) {
                    JsonObject res = refundPurchaseMspQT(body.getJsonObject("value"), userId, xRequestId);
                    if ("0".equals(res.getString("vpc_TxnResponseCode"))) {
                        // update tran invoice
                        InternationalRefundApprovalDao.approveInvoicerefund(id, body.getJsonObject("value").getString("merchant_id"), userId);
                        sendResponse(ctx, 200);
                    } else {
                        throw IErrors.REFUND_FAILED;
                    }

                } else if (action.equals("/reject") && type.equalsIgnoreCase("invoice")) {

                    InternationalRefundApprovalDao.rejectInvoicerefund(id, body.getJsonObject("value").getString("merchant_id"), userId);
                    sendResponse(ctx, 200);
                } else if (action.equals("/manual") && type.equalsIgnoreCase("invoice")) {

                    refundManualMspQT2(ctx);

                } else if (action.equals("/manual") && type.equalsIgnoreCase("madm")) {

                    sendResponse(ctx, 200, MaClient.manualRefundApporval(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")));
                } else if (action.equals("/reverseDue") && type.equalsIgnoreCase("invoice")) {

                    InternationalRefundApprovalDao.reverseDueInvoicerefund(id, body.getJsonObject("value").getString("merchant_id"), userId);
                    sendResponse(ctx, 200);
                } else if (action.equals("/reverseDue") && type.equalsIgnoreCase("madm")) {

                    sendResponse(ctx, 200, MaClient.reverseDueRefundApporval(id, userId, xRequestId, body.getJsonObject("value").getString("merchant_id"), body.getJsonObject("value").getString("transaction_reference"), body.getJsonObject("value").getString("currency")));
                }

                else {
                    throw IErrors.VALIDATION_ERROR;
                }

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Patch International Refund: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    // private static JsonObject refundOneCredit(String CommandID, String vpcCommand, String merchantId,
    // String accessCode, String hashCode, String merchTxnRef, String orgMerchTxnRef, double amount,
    // String operatorId, String version) throws Exception {
    private static JsonObject refundOneCredit(JsonObject data, String userId, String xRequestId) throws Exception {

        // MerchantData merchantData = InternationalRefundApprovalDao.getMerchantData(data.getString("merchant_id"));
        // if(merchantData.getAccessCode() == null || merchantData.getHashCode() == null) {

        //     logger.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> MERCHANT DATA EMPTY");
        //     throw IErrors.VALIDATION_ERROR;
        // }
        String refundRef = "";
        if (xRequestId == null) {
            logger.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID OR REFUND REFERENCE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = xRequestId;
        if (data.getString("refund_reference") == null || data.getString("refund_reference").isEmpty()) {
            // Init refund reference
            refundRef = data.getString("merchant_id") + "_" + requestId;
            if (refundRef.length() > 32) {
                int largerInt = refundRef.length() - 32 + 1;
                requestId = requestId.substring(0, requestId.length() - largerInt);
                refundRef = data.getString("merchant_id") + "_" + requestId;
            }
        } else {
            // Get refund reference
            refundRef = data.getString("refund_reference");
        }

        Map req = new HashMap();
        req.put("command_id", "REFUND");
        Map vpc = new HashMap();
        vpc.put("vpc_Command", "refund");
        vpc.put("vpc_Merchant", data.getString("merchant_id"));
        vpc.put("vpc_AccessCode", data.getString("access_code"));
        vpc.put("vpc_MerchTxnRef", refundRef);
        vpc.put("vpc_OrgMerchTxnRef", data.getString("transaction_reference"));
        vpc.put("vpc_Amount", Convert.toString(Double.valueOf(data.getString("amount")) * 100, "0"));
        vpc.put("vpc_Operator", userId);
        vpc.put("vpc_Version", "2");
        vpc.put("vpc_SecureHash", Util.createMerchantHash(vpc, data.getString("hash_code"),  "2"));
        req.put("vpc", vpc);
        JsonObject res = OneCreditClient.refund(req);
        return res;

    }

    //Chuyển refund qt invoice sang msp
    private static JsonObject refundPurchaseMspQT(JsonObject data, String userId, String xRequestId) throws Exception {
        String refundRef = "";
        if (xRequestId == null) {
            logger.log(Level.SEVERE, "[ INTERNATIONAL TRANSACTION PATCH] -> X-REQUEST-ID OR REFUND REFERENCE EMPTY");
            throw IErrors.VALIDATION_ERROR;
        }
        String requestId = xRequestId;
        if (data.getString("refund_reference") == null || data.getString("refund_reference").isEmpty()) {
            // Init refund reference
            refundRef = data.getString("merchant_id") + "_" + requestId;
            if (refundRef.length() > 32) {
                int largerInt = refundRef.length() - 32 + 1;
                requestId = requestId.substring(0, requestId.length() - largerInt);
                refundRef = data.getString("merchant_id") + "_" + requestId;
            }
        } else {
            // Get refund reference
            refundRef = data.getString("refund_reference");
        }

        Map vpc = new HashMap();
        vpc.put("vpc_Command", "refund");
        vpc.put("vpc_Merchant", data.getString("merchant_id"));
        vpc.put("vpc_AccessCode", data.getString("access_code"));
        vpc.put("vpc_MerchTxnRef", refundRef);
        vpc.put("vpc_OrgMerchTxnRef", data.getString("transaction_reference"));
        vpc.put("vpc_Amount", Convert.toString(Double.valueOf(data.getString("amount")) * 100, "0"));
        vpc.put("vpc_Operator", userId);
        vpc.put("vpc_Version", "2");
        vpc.put("vpc_SecureHash", Util.createMerchantHash(vpc, data.getString("hash_code"),  "2"));
        JsonObject res = MspClient.refundPurchaseQT(vpc);

        return res;

    }


        //Chuyển refund manual qt sang msp
        public static void refundManualMspQT2(RoutingContext ctx) {
            ctx.vertx().executeBlocking(future -> {
                try {
                    String userId = ctx.get(S_USER_ID);
                    String id = ctx.request().getParam(ID);

                    JsonObject body = ctx.getBodyAsJson();
                    if (userId == null) {
                        logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] => USER ID EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                    }
                    String xRequestId = ctx.request().headers().get(X_REQUEST_ID);
                    if (xRequestId == null) {
                        logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] =>  REQUEST EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                    }
                    String amount = body.getJsonObject("value").getString("amount");
                    if (amount == null || amount.isEmpty()) {
                        logger.log(Level.SEVERE, "[ DISPUTE REFUND PATCH ] =>  AMOUNT EMPTY");
                        throw IErrors.VALIDATION_ERROR;
                    }
    
                    Map<String, String> mIn = new HashMap();
    
                    String refundRef;
                    String requestId = xRequestId;
    
                    // generate refund reference
                    if (body.getJsonObject("value").getString("refund_reference") == null || body.getJsonObject("value").getString("refund_reference").isEmpty()) {
                        // Init refund reference
                        refundRef = body.getJsonObject("value").getString("merchant_id") + "_" + requestId;
                        if (refundRef.length() > 32) {
                            int largerInt = refundRef.length() - 32 + 1;
                            requestId = requestId.substring(0, requestId.length() - largerInt);
                            refundRef = body.getJsonObject("value").getString("merchant_id") + "_" + requestId;
                        }
                    } else {
                        // Get refund reference
                        refundRef = body.getJsonObject("value").getString("refund_reference");
                    }
                    String merchantId = body.getJsonObject("value").getString("merchant_id");
                    mIn.put("vpc_Merchant", merchantId);
                    mIn.put("vpc_AccessCode", body.getJsonObject("value").getString("access_code"));
                    mIn.put("vpc_MerchTxnRef", refundRef); 
                    mIn.put("vpc_OrgMerchTxnRef", body.getJsonObject("value").getString("transaction_reference"));
                    mIn.put("vpc_Amount", Convert.toString(Double.valueOf(body.getJsonObject("value").getString("amount")) * 100, "0"));
                    // mIn.put("vpc_DisputeReason", "");
                    mIn.put("vpc_Operator", userId);
                    mIn.put("vpc_Command", "refund");
                    mIn.put("vpc_Version", "2");
                    mIn.put("vpc_Dispute", "n");
                    mIn.put("vpc_Direct", "n");
                    if (body.getJsonObject("value").containsKey("note")  && !StringUtils.isBlank(body.getJsonObject("value").getString("note"))){
                        mIn.put("vpc_Note", body.getJsonObject("value").getString("note"));
                    }

                    // MerchantData merchantData = InternationalRefundApprovalDao.getMerchantData(merchantId);
                    String merchantKey = body.getJsonObject("value").getString("hash_code");
                    if (merchantKey == null) {
                        logger.log(Level.WARNING, "Get Merchant MSP hashcode error: " + merchantId);
                        sendResponse(ctx, 500, new JsonObject());
                        return;
                    }
    
                    String secureHash = Util.createMerchantHash(mIn, merchantKey,  "2");
                    mIn.put("vpc_SecureHash", secureHash);
    
                    JsonObject mspResponse = MspClient.refundDispute(mIn);
                    String mspMessage = mspResponse.getString("vpc_Message");
                    String mspResponseCode = mspResponse.getString("vpc_TxnResponseCode") + "";
                    logger.log(Level.INFO, String.format("MspClient.refundDispute response: vpc_Message=%s | vpc_TxnResponseCode=%s"
                        , mspMessage, mspResponseCode)); 
    
                    int statusCode;
    
                    if ("0".equals(mspResponseCode)) {
                        statusCode = 200;
                    } else if ("4".equals(mspResponseCode)) {
                        statusCode = 400;
                    } else if ("15".equals(mspResponseCode)) {
                        statusCode = 400;
                    } else if ("300".equals(mspResponseCode)) {
                        statusCode = 300;
                    } else {
                        statusCode = 500;
                    } 
                    //update status 12oneinvoice.tbl_transaction
                    if ("0".equals(mspResponseCode)) {
                        InternationalRefundApprovalDao.updateInvoicerefundManual(id, merchantId);
                    } 
                    JsonObject response = new JsonObject();
                    response.put("code", statusCode);
                    response.put("message", mspMessage);
                    sendResponse(ctx, statusCode, response);
    
                } catch (Exception e) {
                    // logger.log(Level.WARNING, "DISPUTE REFUND: ", e);
                    // ctx.fail(e);
                    throw IErrors.REFUND_FAILED;
                }
            }, false, null);
        }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map mIn = bodyJson.getMap();
                Integer userId = ctx.get(X_USER_ID);
                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm a");
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(mIn.get(FROMDATE).toString());
                    oToDate = df.parse(mIn.get(TODATE).toString());
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    mIn.put(FROMDATE, sdf.format(oFromDate));
                    mIn.put(TODATE, sdf.format(oToDate));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ DOWNLOAD INTERNATIONAL INSTALLMENT ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }

                mIn.put(X_USER_ID, userId);

                if (null != mIn.get(X_USER_ID)) {
                    List<String> roles = PermissionDao.getListRoleIDbyUserId(Integer.parseInt(mIn.get(X_USER_ID).toString()));
                    String roleName = Config.getString("role_merchant.role_id", "");
                    if (roles.contains(roleName)) {
                        String merchantsConf = Config.getString("role_merchant.merchant_id", "");
                        String merchantFE = null != mIn.get(MERCHANTID) ? mIn.get(MERCHANTID).toString() : "";
                        StringBuilder merchants = new StringBuilder("");
                        if (!merchantFE.isBlank() && !merchantsConf.isBlank()) {
                            String[] merchantFEs = merchantFE.split(",");
                            String[] configuredMerchants = merchantsConf.split(",");
                            List<String> merchantSearchs = Arrays.asList(configuredMerchants).stream()
                                    .distinct()
                                    .filter(Arrays.asList(merchantFEs)::contains)
                                    .collect(Collectors.toList());
                            if (merchantFEs.length > 0 && merchantSearchs.isEmpty()) {
                                merchants.append("abc");
                            } else {
                                merchants.append(String.join(",", merchantSearchs));
                            }

                        } else if (!merchantsConf.isBlank()) {
                            merchants.append(merchantsConf);
                        } else {
                            merchants.append(merchantFE);
                        }

                        mIn.put(MERCHANTID, merchants);
                    }
                }

                // int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                // if (months > 12) {
                // logger.log(Level.SEVERE, "[ DOWNLOAD INTERNATIONAL INSTALLMENT ] => INVALID DATE ");
                // throw IErrors.SEARCH_TOO_LARGE_ERROR;
                // }

                if (FunctionUtil.isCardData(mIn.get(CARDNUMBER).toString())) {
                    String cardhash = FunctionUtil.oneSMHmac(mIn.get(CARDNUMBER).toString());
                    mIn.put(CARDNUMBER, String.valueOf(cardhash));
                }

                // int totalRows = InternationalRefundApprovalDao.getTotalTransaction(mIn).size();
                // if (totalRows == 0) {
                // throw IErrors.NO_DATA_FOUND;
                // }

                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "international_refund_approval_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                // data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("international_refund_approval");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                fileDownloadDto.setExt("xlsx");

                FileDownloadDao.insert(fileDownloadDto);

                QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));


                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD INTERNATIONAL TRANSACTION Error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

}
