package vn.onepay.portal.resources.international.report;

import java.util.List;
import java.util.Map;

public class InterReportResponse {
    private long total;
    private List<InterReportDto> data;
    private double totalAmount;
    private double totalBankItaAmount;
    private long totalTxn;
    private Map<String, Long> totalByCurrency;
    private Map<String, Double> totalAmountByCurrency;
    private Map<String, Double> totalApproAmountByCurrency;

    /**
     * @return long return the total
     */
    public long getTotal() {
        return total;
    }

    /**
     * @param total the total to set
     */
    public void setTotal(long total) {
        this.total = total;
    }

    /**
     * @return List<InterReportDto> return the data
     */
    public List<InterReportDto> getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(List<InterReportDto> data) {
        this.data = data;
    }

    /**
     * @return double return the totalAmount
     */
    public double getTotalAmount() {
        return totalAmount;
    }

    /**
     * @param totalAmount the totalAmount to set
     */
    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * @return double return the totalBankItaAmount
     */
    public double getTotalBankItaAmount() {
        return totalBankItaAmount;
    }

    /**
     * @param totalBankItaAmount the totalBankItaAmount to set
     */
    public void setTotalBankItaAmount(double totalBankItaAmount) {
        this.totalBankItaAmount = totalBankItaAmount;
    }

    /**
     * @return long return the totalTxn
     */
    public long getTotalTxn() {
        return totalTxn;
    }

    /**
     * @param totalTxn the totalTxn to set
     */
    public void setTotalTxn(long totalTxn) {
        this.totalTxn = totalTxn;
    }

    /**
     * @return Map<String, Long> return the totalByCurrency
     */
    public Map<String, Long> getTotalByCurrency() {
        return totalByCurrency;
    }

    /**
     * @param totalByCurrency the totalByCurrency to set
     */
    public void setTotalByCurrency(Map<String, Long> totalByCurrency) {
        this.totalByCurrency = totalByCurrency;
    }

    /**
     * @return Map<String, Double> return the totalAmountByCurrency
     */
    public Map<String, Double> getTotalAmountByCurrency() {
        return totalAmountByCurrency;
    }

    /**
     * @param totalAmountByCurrency the totalAmountByCurrency to set
     */
    public void setTotalAmountByCurrency(Map<String, Double> totalAmountByCurrency) {
        this.totalAmountByCurrency = totalAmountByCurrency;
    }

    /**
     * @return Map<String, Double> return the totalApproAmountByCurrency
     */
    public Map<String, Double> getTotalApproAmountByCurrency() {
        return totalApproAmountByCurrency;
    }

    /**
     * @param totalApproAmountByCurrency the totalApproAmountByCurrency to set
     */
    public void setTotalApproAmountByCurrency(Map<String, Double> totalApproAmountByCurrency) {
        this.totalApproAmountByCurrency = totalApproAmountByCurrency;
    }

}
