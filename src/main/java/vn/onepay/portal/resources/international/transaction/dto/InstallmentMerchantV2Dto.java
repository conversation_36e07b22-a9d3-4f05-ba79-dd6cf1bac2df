package vn.onepay.portal.resources.international.transaction.dto;

/**
 * Created by tuydv on 09-5-18.
 */
public class InstallmentMerchantV2Dto {

   private String tran_id;
   private String date_bank;
   private String merchant_id;
   private String installment_bank;
   private String term_bank;
   private Integer refund_quantity_wait_for_trans;
   private Double refund_total_wait_for_trans;

    public InstallmentMerchantV2Dto() {
    }

    public InstallmentMerchantV2Dto(String tran_id, String date_bank, String merchant_id, String installment_bank, String term_bank, Integer refund_quantity_wait_for_trans, Double refund_total_wait_for_trans) {
        this.tran_id = tran_id;
        this.date_bank = date_bank;
        this.merchant_id = merchant_id;
        this.installment_bank = installment_bank;
        this.term_bank = term_bank;
        this.refund_quantity_wait_for_trans = refund_quantity_wait_for_trans;
        this.refund_total_wait_for_trans = refund_total_wait_for_trans;
    }

    public InstallmentMerchantV2Dto(String tran_id, String date_bank, String merchant_id, String installment_bank, String term_bank) {
        this.tran_id = tran_id;
        this.date_bank = date_bank;
        this.merchant_id = merchant_id;
        this.installment_bank = installment_bank;
        this.term_bank = term_bank;
    }

    public InstallmentMerchantV2Dto(String tran_id, String date_bank, String merchant_id, Integer refund_quantity_wait_for_trans, Double refund_total_wait_for_trans) {
        this.tran_id = tran_id;
        this.date_bank = date_bank;
        this.merchant_id = merchant_id;
        this.refund_quantity_wait_for_trans = refund_quantity_wait_for_trans;
        this.refund_total_wait_for_trans = refund_total_wait_for_trans;
    }

    public String getTran_id() {
        return tran_id;
    }

    public void setTran_id(String tran_id) {
        this.tran_id = tran_id;
    }

    public String getDate_bank() {
        return date_bank;
    }

    public void setDate_bank(String date_bank) {
        this.date_bank = date_bank;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public Integer getRefund_quantity_wait_for_trans() {
        return refund_quantity_wait_for_trans;
    }

    public void setRefund_quantity_wait_for_trans(Integer refund_quantity_wait_for_trans) {
        this.refund_quantity_wait_for_trans = refund_quantity_wait_for_trans;
    }

    public Double getRefund_total_wait_for_trans() {
        return refund_total_wait_for_trans;
    }

    public void setRefund_total_wait_for_trans(Double refund_total_wait_for_trans) {
        this.refund_total_wait_for_trans = refund_total_wait_for_trans;
    }

    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getTerm_bank() {
        return term_bank;
    }

    public void setTerm_bank(String term_bank) {
        this.term_bank = term_bank;
    }
}
