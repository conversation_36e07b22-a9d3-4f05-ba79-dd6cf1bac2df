package vn.onepay.portal.resources.international.report;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.MultiMap;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.time.DateUtils;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.FunctionUtil;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.ParamsPool;
import static vn.onepay.portal.Util.sendResponse;

public class TransactionReportHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(TransactionReportHandler.class.getName());

    public static void search(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                String userId = ctx.request().getHeader(X_USER_ID);
                if (userId == null) {
                    logger.log(Level.SEVERE, ctx.get(REQUEST_UUID) + ": " + "[ INTERNATIONAL REPORT ERROR ] => USER ID EMPTY");
                    throw IErrors.VALIDATION_ERROR;
                }

                Map<String, String> mIn = Util.convertRequestParamsToMap(ctx.request());
                String fromDate = mIn.get(FROM_DATE);
                String toDate = mIn.get(TO_DATE);
                String advType =  mIn.get("advType");
                advType = advType.replaceAll("T 1", "T+1").replaceAll("T 2", "T+2").replaceAll("T 7", "T+7");
                mIn.put("advType", advType);
                mIn.get(TO_DATE);
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
                Date oFromDate;
                Date oToDate;
                try {
                    oFromDate = df.parse(fromDate);
                    oToDate = df.parse(toDate);
                    oToDate = DateUtils.addMinutes(oToDate, 1);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    fromDate = sdf.format(oFromDate);
                    toDate = sdf.format(oToDate);

                } catch (Exception e) {
                    logger.log(Level.SEVERE, "[ SEARCH INTERNATIONAL REPORT ]  => INVALID DATE ");
                    throw IErrors.VALIDATION_ERROR;
                }
                int months = FunctionUtil.monthsBetween(oFromDate, oToDate);
                if (months > 12) {
                    logger.log(Level.SEVERE, "[ SEARCH INTERNATIONAL REPORT ]  => INVALID DATE ");
                    throw IErrors.SEARCH_TOO_LARGE_ERROR;
                }
                logger.info("PAYMENT2 TRANSACTION ACCOUNTING 18 SEARCH REQUEST PARAMS | " + mIn);

                sendResponse(ctx, 200, TransactionReportDao.list(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "PAYMENT2 TRANSACTION ACCOUNTING 18 SEARCH ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchBinBank(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> mIn = new HashMap();
                sendResponse(ctx, 200, TransactionReportDao.searchBinBank(mIn));
            } catch (Exception e) {
                logger.log(Level.WARNING, "INTERNATION REPORT SEARCH BIN BANK ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getResponseCode(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, TransactionReportDao.getResponseCode());
            } catch (Exception e) {
                logger.log(Level.WARNING, "INTERNATION REPORT GET RESPONSE CODE : ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void searchBankMerchantIdByAcq(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                MultiMap params = request.params();
                String acq = isNull(params.get("acq")) ? "" : params.get("acq");
                sendResponse(ctx, 200, TransactionReportDao.searchBankMerchantIdByAcq(acq));
            } catch (Exception e) {
                logger.log(Level.WARNING, "INTERNATION REPORT SEARCH BANK MERCHANT ID ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void download(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject params = ctx.getBodyAsJson();
                Map<String, Object> request = params.getMap();
                Map<String, String> mIn = new HashMap<>();
                for (Map.Entry<String, Object> data : request.entrySet()) {
                    mIn.put(data.getKey(), data.getValue().toString());
                }
                String advType = isNull(params.getString("advType")) ? "" : params.getString("advType");
                advType = advType.replaceAll("T 1", "T+1").replaceAll("T 2", "T+2").replaceAll("T 7", "T+7");
                mIn.put("advType", advType);
                logger.info("INTERNATION REPORT DOWNLOAD REQUEST PARAMS | " + mIn);
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                long date = new java.util.Date().getTime();

                String fileName = "internation_report" + date;
                String fileHashName = "";
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                requestData.put(ParamsPool.FILE_EXT, ".xls");
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("export_internation_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                int totalRows = TransactionReportDao.getTotalTrans(mIn);
                if (totalRows <= Config.getFileRowLevel()) {
                    fileDownloadDto.setExt("xlsx");
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(),
                                    QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    QueueProducer.sendMessage(
                            new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(),
                                    QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }
                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "INTERNATIONAL REPORT DOWNLOAD ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static boolean isNull(Object obj) {
        return obj == null ? true : false;
    }
}
