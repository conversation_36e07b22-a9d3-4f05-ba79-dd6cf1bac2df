package vn.onepay.portal.resources.international.approval.dto;

import java.sql.Timestamp;

/**
 * Created by anhkh on 4/3/16.
 */
public class InternationalRefundApproval2 {
    private String refundId;
    private String transactionId;
    private String merchantId;
    private String merchantIdMigs;
    private String orderRef;
    private String authCode;
    private String refundApproveType;
    private String currency;
    private Double purchaseAmount;
    private Timestamp purchaseDate;
    private Timestamp refundDate;
    private Double refundAmount;
    private String operator;
    private String merchantTransactionRef;
    private String refundRef;
    private String type;
    private String itaTime;
    private String itaBank;
    private String binCountry;
    private String cardType;
    private String cardNo;
    private String transactionNo;
    private String acquirer;
    private String hashCode;
    private String accessCode;
    private Integer errorStatus;
    private String errorMessage;
    private Integer confirmable;
    private String networkTransId;
    private String source;
    private String tokenNumber;
    private String bankMerchantId;
    private String status;
    private String actionType;
    private String acqResCode;

    public String getAcqResCode() {
        return acqResCode;
    }

    public void setAcqResCode(String acqResCode) {
        this.acqResCode = acqResCode;
    }

    public String getBankMerchantId() {
        return bankMerchantId;
    }

    public void setBankMerchantId(String bankMerchantId) {
        this.bankMerchantId = bankMerchantId;
    }

    public Integer getConfirmable() {
        return confirmable;
    }

    public void setConfirmable(Integer confirmable) {
        this.confirmable = confirmable;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getAccessCode() {
        return accessCode;
    }

    public void setAccessCode(String accessCode) {
        this.accessCode = accessCode;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getRefundRef() {
        return refundRef;
    }

    public void setRefundRef(String refundRef) {
        this.refundRef = refundRef;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantIdMigs() {
        return merchantIdMigs;
    }

    public void setMerchantIdMigs(String merchantIdMigs) {
        this.merchantIdMigs = merchantIdMigs;
    }

    public String getOrderRef() {
        return orderRef;
    }

    public void setOrderRef(String orderRef) {
        this.orderRef = orderRef;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getRefundApproveType() {
        return refundApproveType;
    }

    public void setRefundApproveType(String refundApproveType) {
        this.refundApproveType = refundApproveType;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(Double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public Timestamp getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Timestamp purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public Timestamp getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Timestamp refundDate) {
        this.refundDate = refundDate;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMerchantTransactionRef() {
        return merchantTransactionRef;
    }

    public void setMerchantTransactionRef(String merchantTransactionRef) {
        this.merchantTransactionRef = merchantTransactionRef;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getItaTime() {
        return itaTime;
    }

    public void setItaTime(String itaTime) {
        this.itaTime = itaTime;
    }

    public String getItaBank() {
        return itaBank;
    }

    public void setItaBank(String itaBank) {
        this.itaBank = itaBank;
    }

    public String getBinCountry() {
        return binCountry;
    }

    public void setBinCountry(String binCountry) {
        this.binCountry = binCountry;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public Integer getErrorStatus() {
        return errorStatus;
    }

    public void setErrorStatus(Integer errorStatus) {
        this.errorStatus = errorStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getNetworkTransId() {
        return networkTransId;
    }

    public void setNetworkTransId(String networkTransId) {
        this.networkTransId = networkTransId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTokenNumber() {
        return tokenNumber;
    }

    public void setTokenNumber(String tokenNumber) {
        this.tokenNumber = tokenNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
    
}
