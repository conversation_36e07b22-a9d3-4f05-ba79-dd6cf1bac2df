package vn.onepay.portal.resources.international.merchant;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.international.merchant.dto.InternationalMerchantData;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;

public class InternationalMerchantDao extends Db implements IConstants {

    public static final String GET_MERCHANT_DATA = "{call PKG_ONECREDIT.GET_MERCHANT_DATA(?,?,?,?)}";
    public  static InternationalMerchantData getMerchantData(String id) throws Exception {
        Exception exception = null;
        InternationalMerchantData result = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_MERCHANT_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international merchant data by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindMerchantData(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static InternationalMerchantData bindMerchantData(ResultSet rs) throws SQLException {

        String accessCode = rs.getString("S_ACCESS_CODE");
        String hashCode = rs.getString("S_HASH_CODE");
        InternationalMerchantData merchantData = new InternationalMerchantData();
        merchantData.setAccessCode(accessCode);
        merchantData.setHashCode(hashCode);
        return merchantData;
    }
}
