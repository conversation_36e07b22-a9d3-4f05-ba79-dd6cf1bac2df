package vn.onepay.portal.resources.international.approval;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.base.dto.RefundData;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval2;
import vn.onepay.portal.resources.international.approval.dto.MerchantData;
import vn.onepay.portal.resources.international.approval.dto.RefundAmount;
import vn.onepay.portal.resources.international.transaction.InternationalTransactionDao;
import vn.onepay.portal.resources.international.transaction.dto.InternationalPurchase;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class InternationalRefundApprovalDao extends Db implements IConstants {
    //private static final String LIST_TRANSACTION_MADM = "{call PKG_MA_REFUND_APPROVE.search_refund_qt2(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_MADM = "{call PKG_MA_REFUND_APPROVE.search_refund_qt8(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String LIST_TRANSACTION_INVOICE = "{call PKG_INVOICE.search_refund_qt4(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    private static final String LIST_TRANSACTION_TOTAL_B = "{call PKG_MA_REFUND_APPROVE.search_refund(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String TRANSACTION_APPROVAL_GET_BY_ID = "{call PKG_MA_REFUND_APPROVE.get_refund_by_id(?,?,?,?)}";
    private static final String UPDATE_INVOICE_REFUND = "{call PKG_INVOICE.update_refund_status_invoice(?,?,?,?,?,?)}";
    private static final String UPDATE_INVOICE_REFUND_MANUAL = "{call PKG_INVOICE.update_rf_status_inv_manual(?,?,?,?)}";
    private static final String USP_UPDATE_INVOICE_REFUND = "{call PKG_INVOICE.usp_update_refund_invoice(?,?,?,?,?,?)}";
    private static final String GET_MERCHANT_DATA = "{call PKG_REPORT_ONECREDIT.get_merchant_data(?,?,?,?)}";

    private static final Logger logger = Logger.getLogger(InternationalRefundApprovalDao.class.getName());


    public static BaseList<InternationalRefundApproval> search(Map mIn) throws Exception {
        logger.log(Level.INFO, "query condition: " + mIn);
        BaseList<InternationalRefundApproval> result = new BaseList<>();
        // get Total
        List<String> totalApproval = getTotalTransaction(mIn);
        logger.log(Level.WARNING, "GET TOTAL International refund Approval: " + Util.gson.toJson(totalApproval) + " IDS : " + String.join(",", totalApproval));
        // get purchase By ids
        Map<Integer, InternationalPurchase> purchaseMap = InternationalTransactionDao.mapByIds(String.join(",", totalApproval), mIn);
        Integer totalBackup = countFilterById(purchaseMap, totalApproval);
        result.setTotalItems(totalBackup);


        logger.log(Level.WARNING, "International refund Approval  Original Ids: " + totalApproval);
        List<InternationalRefundApproval> refundApproval = searchTransaction(mIn);
        // JOIN purchase INFO -------> filter != null
        List<InternationalRefundApproval> refundApproval2 = refundApproval
                .stream()
                .map(approval -> {
                    return joinApproveWithPurchase(approval, purchaseMap.get(approval.getOriginal_transaction_id()));
                })
                .filter(InternationalRefund -> InternationalRefund != null)
                .collect(Collectors.toList());


        result.setList(refundApproval2);

        return result;
    }

    private static InternationalRefundApproval joinApproveWithPurchase(InternationalRefundApproval approve, InternationalPurchase purchase) {


        if (purchase == null) {
            return null;
        }

        InternationalRefundApproval result = approve;

        result.setTransaction_purchase_time(purchase.getTransaction_time());
        result.setOrder_info(purchase.getOrder_info());
        result.setAcquirer(purchase.getAcquirer());

        result.setCard(purchase.getCard());
        result.getAmount().setPurchase_total(purchase.getAmount().getTotal());
        // result.setIp_address(purchase.getIp_address());

        return result;
    }

    private static Integer countFilterById(Map<Integer, InternationalPurchase> totalPurchaseMap, List<String> totalApproval) {
        int result = 0;
        if (!totalApproval.isEmpty()) {
            for (String transactionId : totalApproval) {
                if (totalPurchaseMap.get(Integer.valueOf(transactionId)) != null) {
                    result++;
                }
            }
        }
        return result;
    }

    public static List<String> getTotalTransaction(Map mIn) throws Exception {
        Exception exception = null;
        List<String> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_TRANSACTION_TOTAL_B);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.TOTAL.toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.setString(6, mIn.get(TRANSACTION_ID).toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setString(9, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setString(10, mIn.get(STATUS).toString());
            cs.setString(11, mIn.get(CURRENCY).toString());
            cs.setInt(12, RefundData.Type.INTERNATIONAL.getValue());
            cs.setString(13, mIn.getOrDefault(REFUNDID, "").toString());
            cs.setNull(14, Types.INTEGER);
            cs.setNull(15, Types.INTEGER);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get total international refund approval ERROR: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(rs.getString("N_ORIGINAL_ID"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.WARNING, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    private static List<InternationalRefundApproval> searchTransaction(Map mIn) throws Exception {
        Exception exception = null;
        List<InternationalRefundApproval> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_TRANSACTION_TOTAL_B);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.get(MERCHANT_ID).toString());
            cs.setString(6, mIn.get(TRANSACTION_ID).toString());
            cs.setString(7, mIn.get(FROM_DATE).toString());
            cs.setString(8, mIn.get(TO_DATE).toString());
            cs.setString(9, mIn.get(MERCHANT_TRANSACTION_REF).toString());
            cs.setString(10, mIn.get(STATUS).toString());
            cs.setString(11, mIn.get(CURRENCY).toString());
            cs.setInt(12, RefundData.Type.INTERNATIONAL.getValue());
            cs.setString(13, mIn.getOrDefault(REFUNDID, "").toString());
            cs.setInt(14, Convert.parseInt(mIn.get(PAGE).toString(), 0));
            cs.setInt(15, Convert.parseInt(mIn.get(PAGE_SIZE).toString(), 0));
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international refund approval ERROR: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindApproval(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    public static List<InternationalRefundApproval2> searchTransactionInvoice(Map mIn) throws Exception {
        Exception exception = null;
        List<InternationalRefundApproval2> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(LIST_TRANSACTION_INVOICE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANTID, "").toString());
            cs.setString(6, mIn.getOrDefault(TRANSACTIONID, "").toString());
            cs.setString(7, mIn.getOrDefault(FROMDATE, "").toString());
            cs.setString(8, mIn.getOrDefault(TODATE, "").toString());
            cs.setString(9, mIn.getOrDefault(MERCHANTTRANSACTIONREF, "").toString());
            cs.setString(10, mIn.getOrDefault(ORDERINFO, "").toString());
            cs.setString(11, mIn.getOrDefault(ACQUIRERID, "").toString());
            cs.setString(12, mIn.getOrDefault(CARDNUMBER, "").toString());
            cs.setString(13, mIn.getOrDefault(STATUS, "").toString());
            cs.setString(14, mIn.getOrDefault(CURRENCY, "").toString());
            cs.setInt(15, RefundData.Type.INTERNATIONAL.getValue());
            cs.setString(16, mIn.getOrDefault(GATE, "").toString());
            cs.setString(17, mIn.getOrDefault(AUTHCODE, "").toString());
            cs.setInt(18, Convert.parseInt(mIn.getOrDefault("confirmable",0).toString(),0));
            cs.setString(19, mIn.getOrDefault(NETWORK_TRANSACTION_ID, "").toString());
            cs.setString(20, mIn.getOrDefault(SOURCE, "").toString());
            cs.setString(21, mIn.getOrDefault(TOKENNUMBER, "").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindApproval2(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<InternationalRefundApproval2> searchTransactionMADM(Map mIn) throws Exception {
        Exception exception = null;
        List<InternationalRefundApproval2> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(LIST_TRANSACTION_MADM);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, QueryMethod.SELECT.toString());
            cs.setString(5, mIn.getOrDefault(MERCHANTID, "").toString());
            cs.setString(6, mIn.getOrDefault(TRANSACTIONID, "").toString());
            cs.setString(7, mIn.getOrDefault(FROMDATE, "").toString());
            cs.setString(8, mIn.getOrDefault(TODATE, "").toString());
            cs.setString(9, mIn.getOrDefault(MERCHANTTRANSACTIONREF, "").toString());
            cs.setString(10, mIn.getOrDefault(ORDERINFO, "").toString());
            cs.setString(11, mIn.getOrDefault(ACQUIRERID, "").toString());
            cs.setString(12, mIn.getOrDefault(CARDNUMBER, "").toString());
            cs.setString(13, mIn.getOrDefault(STATUS, "").toString());
            cs.setString(14, mIn.getOrDefault(CURRENCY, "").toString());
            cs.setInt(15, RefundData.Type.INTERNATIONAL.getValue());
            cs.setString(16, mIn.getOrDefault(GATE, "").toString());
            cs.setString(17, mIn.getOrDefault(AUTHCODE, "").toString());
            cs.setInt(18, Convert.parseInt(mIn.get(REFUNDAPPROVETYPE).toString(), 0));
            cs.setInt(19, Convert.parseInt(mIn.getOrDefault("confirmable","0").toString(),0));
            cs.setString(20, mIn.getOrDefault(NETWORK_TRANSACTION_ID, "").toString());
            cs.setString(21, mIn.getOrDefault(SOURCE, "").toString());
            cs.setString(22, mIn.getOrDefault(TOKENNUMBER, "").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get search international refund approval error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindApproval2(rs));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static InternationalRefundApproval getInternationalRefundApprovalById(Integer id) throws Exception {

        InternationalRefundApproval approval = InternationalRefundApprovalDao.getTransactionById(Integer.valueOf(id));

        InternationalPurchase purchase = InternationalTransactionDao.getPurchase(String.valueOf(approval.getOriginal_transaction_id()));
        return joinApproveWithPurchase(approval, purchase);
    }

    private static InternationalRefundApproval getTransactionById(Integer id) throws Exception {
        Exception exception = null;
        InternationalRefundApproval result = new InternationalRefundApproval();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(TRANSACTION_APPROVAL_GET_BY_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, id);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get international refund approval by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = bindApproval(rs);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }


    public static MerchantData getMerchantData(String merchantId) throws Exception {
        Exception exception = null;
        MerchantData result = new MerchantData();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(GET_MERCHANT_DATA);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, merchantId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB get merchant data by id error: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.setAccessCode(Util.getColumnString(rs, "S_ACCESS_CODE"));
                    result.setHashCode(Util.getColumnString(rs, "S_HASH_CODE"));
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static void approveInvoicerefund(String id, String merchantId, String operator) throws Exception {
        updateInvoicerefund(id, merchantId, operator, "CA");
    }
    public static void updateInvoicerefundManual(String id, String merchantId) throws Exception {
        updateInvoiceRefundManual(id, merchantId);
    }

    public static void rejectInvoicerefund(String id, String merchantId, String operator) throws Exception {
        updateInvoicerefund(id, merchantId, operator, "RJ");
    }

    public static void manualInvoicerefund(String id, String merchantId, String operator) throws Exception {
        updateInvoicerefund(id, merchantId, operator, "CAM");
    }

    public static void reverseDueInvoicerefund(String id, String merchantId, String operator) throws Exception {
        updateInvoiceRefundReverseDue(id, merchantId, operator, "CAM");
    }

    private static void updateInvoiceRefundReverseDue(String id, String merchantId, String operator, String status) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(USP_UPDATE_INVOICE_REFUND);
            cs.setString(1, id);
            cs.setString(2, merchantId);
            cs.setString(3, operator);
            cs.setString(4, status);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(6);
            int nError = cs.getInt(5);
            if (nError != 1) {
                throw new Exception("DB update invoice refund approval by id error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    private static void updateInvoicerefund(String id, String merchantId, String operator, String status) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_INVOICE_REFUND);
            cs.setString(1, id);
            cs.setString(2, merchantId);
            cs.setString(3, operator);
            cs.setString(4, status);
            cs.registerOutParameter(5, OracleTypes.NUMBER);
            cs.registerOutParameter(6, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(6);
            int nError = cs.getInt(5);
            if (nError != 1) {
                throw new Exception("DB update invoice refund approval by id error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }
    private static void updateInvoiceRefundManual(String id, String merchantId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall(UPDATE_INVOICE_REFUND_MANUAL);
            cs.setString(1, id);
            cs.setString(2, merchantId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(4);
            int nError = cs.getInt(3);
            if (nError != 1) {
                throw new Exception("DB update invoice refund manual approval by id error: " + error);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            // closeConnectionDB(rs, null, cs, con);
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
    }

    private static InternationalRefundApproval bindApproval(ResultSet rs) throws Exception {
        String merchantId = rs.getString("S_MERCHANT_ID");
        // String orderInfo = rs.getString("S_ORDER_INFO");
        String currency = rs.getString("S_CURRENCY_CODE");
        int transactionId = rs.getInt("N_TRANSACTION_ID");
        int originalId = Integer.valueOf(rs.getString("N_ORIGINAL_ID"));
        Timestamp date = Timestamp.valueOf(rs.getString("D_MERCHANT_TRANSACTION_DATE"));
        String transactionType = rs.getString("S_TRANSACTION_TYPE");
        String status = String.valueOf(rs.getInt("N_TRANSACTION_STATUS"));
        String transactionRef = rs.getString("S_MERCHANT_TRANSACTION_REF");
        double total = rs.getDouble("N_AMOUNT");
        String operatorId = rs.getString("S_OPERATOR_ID");
        InternationalRefundApproval transaction = new InternationalRefundApproval();
        transaction.setMerchant_id(merchantId);
        // transaction.setOrder_info(orderInfo);
        transaction.setTransaction_status(status);
        transaction.setTransaction_time(date);
        transaction.setTransaction_reference(transactionRef);
        transaction.setTransaction_id(transactionId);
        transaction.setTransaction_type(transactionType);
        transaction.setOriginal_transaction_id(originalId);
        transaction.setOperator(operatorId);
        RefundAmount refundAmount = new RefundAmount();
        refundAmount.setCurrency(currency);
        refundAmount.setTotal(total);
        transaction.setAmount(refundAmount);
        return transaction;
    }


    private static InternationalRefundApproval2 bindApproval2(ResultSet rs) throws Exception {
        InternationalRefundApproval2 transaction = new InternationalRefundApproval2();
        transaction.setRefundId(Util.getColumnString(rs, "S_ID_TRAN_APPROVE"));
        transaction.setTransactionId(Util.getColumnString(rs, "S_PURCHASE_ID"));
        transaction.setRefundDate(Util.getColumnTimeStamp(rs, "D_DATE_WAIT_FOR_APPROVE"));
        transaction.setPurchaseDate(Util.getColumnTimeStamp(rs, "D_DATE_ORIGINAL"));
        transaction.setMerchantIdMigs(Util.getColumnString(rs, "S_MERCHANT_MIGS"));
        transaction.setMerchantId(Util.getColumnString(rs, "S_MERCHANT_ID_INVOICE"));
        transaction.setOrderRef(Util.getColumnString(rs, "S_ORDERREFERENCE"));
        transaction.setMerchantTransactionRef(Util.getColumnString(rs, "S_TRAN_REF_SOURCE"));
        transaction.setRefundRef(Util.getColumnString(rs, "S_MERCHANTTRANSACTIONREFEREN"));
        transaction.setPurchaseAmount(Util.getColumnDouble(rs, "N_AMOUNT_ORIGINAL"));
        transaction.setRefundAmount(Util.getColumnDouble(rs, "S_AMOUNT"));
        transaction.setAuthCode(Util.getColumnString(rs, "S_AUTHORISATION_CODE"));
        transaction.setRefundApproveType(Util.getColumnString(rs, "S_REFUND_APPROVE_TYPE"));
        transaction.setCurrency(Util.getColumnString(rs, "S_CURRENCY"));
        transaction.setOperator(Util.getColumnString(rs, "S_OPERATOR"));
        transaction.setType(Util.getColumnString(rs, "S_TYPE"));
        transaction.setCardType(Util.getColumnString(rs, "S_CARD_TYPE"));
        transaction.setCardNo(Util.getColumnString(rs, "S_CARD_NO"));
        transaction.setAcquirer(Util.getColumnString(rs, "S_ACQUIRER"));
        transaction.setTransactionNo(Util.getColumnString(rs, "S_TRANSACTION_NO"));
        transaction.setBinCountry(Util.getColumnString(rs, "S_BIN_COUNTRY"));
        transaction.setItaBank(Util.getColumnString(rs, "S_ITA_BANK"));
        transaction.setItaTime(Util.getColumnString(rs, "S_ITA_TIME"));
        transaction.setHashCode(Util.getColumnString(rs, "S_HASH_CODE"));
        transaction.setAccessCode(Util.getColumnString(rs, "S_ACCESS_CODE"));
        transaction.setErrorStatus((Util.getColumnInteger(rs, "ERROR_STATUS") == null || Util.getColumnInteger(rs, "ERROR_STATUS").equals(0)) ? null : Util.getColumnInteger(rs, "ERROR_STATUS"));
        transaction.setErrorMessage(Util.getColumnString(rs, "ERROR_MESSAGE"));
        transaction.setConfirmable(Util.getColumnInteger(rs, "N_CONFIRMABLE"));
        transaction.setNetworkTransId(Util.getColumnString(rs, "S_NETWORK_TRANS_ID"));
        transaction.setSource(Util.getColumnString(rs, "S_SOURCE"));
        transaction.setTokenNumber(Util.getColumnString(rs, "S_TOKEN_NUMBER"));
        transaction.setBankMerchantId(Util.getColumnString(rs, "S_BANK_MERCHANT_ID"));

        return transaction;
    }
}
