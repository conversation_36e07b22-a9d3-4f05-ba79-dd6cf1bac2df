package vn.onepay.portal.resources.international.installment.dto;

import vn.onepay.portal.resources.base.dto.Amount;
import java.sql.Timestamp;

/**
 * Created by tuydv on 09-5-18.
 */
public class InstallmentTransaction {
    private String id;
    private String trans_no;
    private Timestamp date;
    private Timestamp originalDate;
    private String merchant_id;
    private String acquirer;
    private String trans_ref;
    private String trans_type;
    private Amount amount;
    private String currency;

    private String response;
    private String order_ref;
    private String card_number;
    private String auth_code;
    private String parentId;
    private String tranaction_id;
    private String card_type;
    private String base_status;
    private String card_holder;
    // Installment
    private String installment_status;
    private String installment_bank;
    private String installment_time;
    private Timestamp installment_date;
    private String installment_amount;
    private String refund_installment_status;
    // SAMSUNG
    private String customer_mobile;
    private String customer_email;
    private String epp;
    private String fraud_check;
    private String operator;
    private Double amount_to_vnd;
    private Double ita_fee_amount;

    // Channel UPOS/ ECOM / IMPORT
    private String channel;
    private String referralPartner;
    private Timestamp ita_start_date;
    private Timestamp onepayApproval;
    private Timestamp bankSend;
    private Timestamp bankApprovalDate;

    private String contract_type;

    public Timestamp getIta_start_date() {
        return ita_start_date;
    }

    public void setIta_start_date(Timestamp ita_start_date) {
        this.ita_start_date = ita_start_date;
    }

    // ADAYROI
    private String customer_name;

    public String getCard_holder() {
        return card_holder;
    }

    public void setCard_holder(String card_holder) {
        this.card_holder = card_holder;
    }

    public Timestamp getInstallment_date() {
        return installment_date;
    }

    public void setInstallment_date(Timestamp installment_date) {
        this.installment_date = installment_date;
    }

    public String getInstallment_amount() {
        return installment_amount;
    }

    public void setInstallment_amount(String installment_amount) {
        this.installment_amount = installment_amount;
    }

    public String getInstallment_time() {
        return installment_time;
    }

    public String getInstallment_status() {
        return installment_status;
    }

    public void setInstallment_status(String installment_status) {
        this.installment_status = installment_status;
    }

    public void setInstallment_time(String installment_time) {
        this.installment_time = installment_time;
    }


    public String getInstallment_bank() {
        return installment_bank;
    }

    public void setInstallment_bank(String installment_bank) {
        this.installment_bank = installment_bank;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getFraud_check() {
        return fraud_check;
    }

    public void setFraud_check(String fraud_check) {
        this.fraud_check = fraud_check;
    }

    public String getCustomer_mobile() {
        return customer_mobile;
    }

    public void setCustomer_mobile(String customer_mobile) {
        this.customer_mobile = customer_mobile;
    }

    public String getCustomer_email() {
        return customer_email;
    }

    public void setCustomer_email(String customer_email) {
        this.customer_email = customer_email;
    }

    public String getEpp() {
        return epp;
    }

    public void setEpp(String epp) {
        this.epp = epp;
    }

    public Amount getAmount() {
        return amount;
    }

    public void setAmount(Amount amount) {
        this.amount = amount;
    }

    public String getBase_status() {
        return base_status;
    }

    public void setBase_status(String base_status) {
        this.base_status = base_status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTrans_no() {
        return trans_no;
    }

    public void setTrans_no(String trans_no) {
        this.trans_no = trans_no;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getTrans_ref() {
        return trans_ref;
    }

    public void setTrans_ref(String trans_ref) {
        this.trans_ref = trans_ref;
    }

    public String getTrans_type() {
        return trans_type;
    }

    public void setTrans_type(String trans_type) {
        this.trans_type = trans_type;
    }


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getReferralPartner() {
        return referralPartner;
    }

    public void setReferralPartner(String referralPartner) {
        this.referralPartner = referralPartner;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getOrder_ref() {
        return order_ref;
    }

    public void setOrder_ref(String order_ref) {
        this.order_ref = order_ref;
    }

    public String getCard_number() {
        return card_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public String getAuth_code() {
        return auth_code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getTranaction_id() {
        return tranaction_id;
    }

    public void setTranaction_id(String tranaction_id) {
        this.tranaction_id = tranaction_id;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * @return the originalDate
     */
    public Timestamp getOriginalDate() {
        return originalDate;
    }

    /**
     * @param originalDate the originalDate to set
     */
    public void setOriginalDate(Timestamp originalDate) {
        this.originalDate = originalDate;
    }

    public Double getAmount_to_vnd() {
        return amount_to_vnd;
    }

    public void setAmount_to_vnd(Double amount_to_vnd) {
        this.amount_to_vnd = amount_to_vnd;
    }

    public Double getIta_fee_amount() {
        return ita_fee_amount;
    }

    public void setIta_fee_amount(Double ita_fee_amount) {
        this.ita_fee_amount = ita_fee_amount;
    }

    public String getRefund_installment_status() {
        return this.refund_installment_status;
    }

    public void setRefund_installment_status(String refund_installment_status) {
        this.refund_installment_status = refund_installment_status;
    }

    public String getContract_type() {
        return contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    /**
     * @return String return the onepayApproval
     */
    public Timestamp getOnepayApproval() {
        return onepayApproval;
    }

    /**
     * @param onepayApproval the onepayApproval to set
     */
    public void setOnepayApproval(Timestamp onepayApproval) {
        this.onepayApproval = onepayApproval;
    }

    /**
     * @return Timestamp return the bankSend
     */
    public Timestamp getBankSend() {
        return bankSend;
    }

    /**
     * @param bankSend the bankSend to set
     */
    public void setBankSend(Timestamp bankSend) {
        this.bankSend = bankSend;
    }

    /**
     * @return Timestamp return the bankApprovalDate
     */
    public Timestamp getBankApprovalDate() {
        return bankApprovalDate;
    }

    /**
     * @param bankApprovalDate the bankApprovalDate to set
     */
    public void setBankApprovalDate(Timestamp bankApprovalDate) {
        this.bankApprovalDate = bankApprovalDate;
    }

}
