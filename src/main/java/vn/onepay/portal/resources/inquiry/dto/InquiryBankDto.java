package vn.onepay.portal.resources.inquiry.dto;

import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;

public class InquiryBankDto {
    private int no;
    private String transactionId;
    private String bankId;
    private String transactionDate;
    private String debit;
    private String credit;
    private String accountBalance;
    private String corresponsiveAccount;
    private String corresponsiveAccountName;
    private String agency;
    private String corresponsiveBankName;
    private String corresponsiveBankId;
    private String serviceBranchId;
    private String serviceBankName;
    private String channel;
    private String createDate;
    private String desc;
    private String accountNumber;
    public InquiryBankDto(){
        
    }
    public int getNo() {
        return no;
    }
    public void setNo(int no) {
        this.no = no;
    }
    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBankId() {
        return this.bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getTransactionDate() {
        return this.transactionDate;
    }

    public void setTransactionDate(Timestamp transactionDate) {
        this.transactionDate = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").format(transactionDate);;
    }

    public String getDebit() {
        return this.debit;
    }

    public void setDebit(Long debit) {
        this.debit =  new DecimalFormat("###,###,###").format(debit);
    }

    public String getCredit() {
        return this.credit;
    }

    public void setCredit(Long credit) {
        this.credit = new DecimalFormat("###,###,###").format(credit);
    }

    public String getAccountBalance() {
        return this.accountBalance;
    }

    public void setAccountBalance(Long accountBalance) {
        this.accountBalance = new DecimalFormat("###,###,###").format(accountBalance);
    }

    public String getCorresponsiveAccount() {
        return this.corresponsiveAccount;
    }

    public void setCorresponsiveAccount(String corresponsiveAccount) {
        this.corresponsiveAccount = corresponsiveAccount;
    }

    public String getCorresponsiveAccountName() {
        return this.corresponsiveAccountName;
    }

    public void setCorresponsiveAccountName(String corresponsiveAccountName) {
        this.corresponsiveAccountName = corresponsiveAccountName;
    }

    public String getAgency() {
        return this.agency;
    }

    public void setAgency(String agency) {
        this.agency = agency;
    }

    public String getCorresponsiveBankName() {
        return this.corresponsiveBankName;
    }

    public void setCorresponsiveBankName(String corresponsiveBankName) {
        this.corresponsiveBankName = corresponsiveBankName;
    }

    public String getCorresponsiveBankId() {
        return this.corresponsiveBankId;
    }

    public void setCorresponsiveBankId(String corresponsiveBankId) {
        this.corresponsiveBankId = corresponsiveBankId;
    }

    public String getServiceBranchId() {
        return this.serviceBranchId;
    }

    public void setServiceBranchId(String serviceBranchId) {
        this.serviceBranchId = serviceBranchId;
    }

    public String getServiceBankName() {
        return this.serviceBankName;
    }

    public void setServiceBankName(String serviceBankName) {
        this.serviceBankName = serviceBankName;
    }

    public String getChannel() {
        return this.channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").format(createDate);;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAccountNumber() {
        return this.accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
}
