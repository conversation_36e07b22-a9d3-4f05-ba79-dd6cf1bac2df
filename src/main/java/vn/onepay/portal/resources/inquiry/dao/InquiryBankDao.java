package vn.onepay.portal.resources.inquiry.dao;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.checkerframework.checker.units.qual.min;

import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.inquiry.dto.InquiryBankDto;

/*
 * Author: <PERSON><PERSON><PERSON>
 * Date: 06/04/2022
 */
public class InquiryBankDao extends Db implements IConstants{
    private static final Logger logger = Logger.getLogger(InquiryBankDao.class.getName());
    private static final String GET_PO_BANK_TXN = "{call ONECDR.PKG_PO_BANK.GET_TB_PO_BANK_TXN(?,?,?,?,?,?,?,?,?,?,?)}";
    private static final String GET_BANK_ID = "{call ONECDR.PKG_PO_BANK.GET_BANK_ID(?)}";
    private static final String GET_ACCOUNT_NUMBER = "{call ONECDR.PKG_PO_BANK.GET_ACCOUNT_NUMBER(?)}";

    public static BaseList<InquiryBankDto> getTransactions(Map mIn) throws Exception{
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs= null;
        ResultSet rsList = null;
        ResultSet rsTotal = null;
        BaseList<InquiryBankDto> iBaseList = new BaseList<>();
        List<InquiryBankDto> iList = new ArrayList<>();
        try {
            conn = getConnection114();
            System.out.println("sql ne : "+ mIn.toString());
            cs = conn.prepareCall(GET_PO_BANK_TXN);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.CURSOR);
            cs.setString(3,mIn.get(BANK_ID) == null ? "" :String.valueOf(mIn.get(BANK_ID)));
            cs.setString(4,mIn.get(TRANSACTION_ID) == null ? "": String.valueOf(mIn.get(TRANSACTION_ID)));
            cs.setString(5,mIn.get(ACCOUNT_ID) == null ? "" : String.valueOf(mIn.get(ACCOUNT_ID)));
            cs.setString(6,mIn.get(FROM_DATE) == null ? "" : String.valueOf(mIn.get(FROM_DATE)));
            cs.setString(7,mIn.get(TO_DATE) == null ? "" : String.valueOf(mIn.get(TO_DATE)));
            cs.setInt(8,mIn.get(PAGE_SIZE) == null ? 0 : Integer.parseInt(mIn.get(PAGE_SIZE).toString()));
            cs.setInt(9, mIn.get(PAGE) == null ? 100 : Integer.parseInt(mIn.get(PAGE).toString()));
            cs.setString(10, mIn.get(TYPE) == null ? "" :String.valueOf(mIn.get(TYPE)));
            cs.setString(11, mIn.get(DESC) == null ? "" :String.valueOf(mIn.get(DESC)));
            cs.execute();
            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            rsTotal = (ResultSet) cs.getObject(2);
            while(rsList!=null && rsList.next()){
                iList.add(bindTransaction(rsList));
            }
            while(rsTotal!=null && rsTotal.next()){
                iBaseList.setTotalItems(rsTotal.getInt("TOTAL_ITEM"));
            }
            iBaseList.setList(iList);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "getTransactions :",e);
            exception = e;
        } finally {
            closeConnectionDB(rsTotal, null, null, null);
            closeConnectionDB(rsList, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return iBaseList;
    }
    public static JsonObject getBankIdAndAccountNumber() throws Exception{
        JsonObject filterObject = new JsonObject();
        try {
            filterObject.put(BANK_ID, getBankId());
            filterObject.put(ACCOUNT,getAccountNumber());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "getBankIdAndAccountNumber : ", e);
            throw e;
        }
        return filterObject;
    }
    public static List<Map<String,String>> getBankId() throws Exception{
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs= null;
        ResultSet rsList = null;
        List<Map<String,String>> bankList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs =  conn.prepareCall(GET_BANK_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rsList = (ResultSet) cs.getObject(1);
            while(rsList != null && rsList.next()){
                Map bank_id = new HashMap<>();
                bank_id.put("bank_id", rsList.getString("S_BANK_ID"));
                bankList.add(bank_id);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "getBankId :",e);
            exception = e;
        }finally {
            closeConnectionDB(rsList, null, cs, conn);
        }
        if (exception != null)
            throw exception;
        return bankList;
    }
    public static List<Map<String,String>> getAccountNumber() throws Exception{
        Exception exception = null;
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        List<Map<String,String>> accountList = new ArrayList<>();
        try {
            conn = getConnection114();
            cs =  conn.prepareCall(GET_ACCOUNT_NUMBER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.execute();
            rs =(ResultSet)cs.getObject(1);
            while(rs != null && rs.next()){
                Map account = new HashMap();
                account.put("account", rs.getString("S_ACCOUNT_NUMBER"));
                accountList.add(account);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "getAccountNumber :",e);
            exception = e;
        }finally {
            closeConnectionDB(rs, null, cs, conn);
        }
        if (exception != null)
        throw exception;
    return accountList;
    }
    public static InquiryBankDto bindTransaction(ResultSet rs) throws Exception{
        InquiryBankDto iBankDto = new InquiryBankDto();
        iBankDto.setNo(rs.getInt("RNUM"));
        iBankDto.setTransactionId(rs.getObject("S_TRANSACTION_ID") == null|| rs.getString("S_TRANSACTION_ID").equals("null") ? "" : rs.getString("S_TRANSACTION_ID"));
        iBankDto.setBankId(rs.getObject("S_BANK_ID") == null || rs.getString("S_BANK_ID").equals("null")? "" : rs.getString("S_BANK_ID"));
        iBankDto.setTransactionDate(rs.getTimestamp("D_TRANS"));
        iBankDto.setDebit(rs.getObject("N_DEBIT") == null ? 0 : rs.getLong("N_DEBIT"));
        iBankDto.setCredit(rs.getObject("N_CREDIT") == null ? 0 : rs.getLong("N_CREDIT"));
        iBankDto.setAccountBalance(rs.getObject("N_ACCOUNT_BALANCE") == null ? 0 : rs.getLong("N_ACCOUNT_BALANCE"));
        iBankDto.setCorresponsiveAccount(rs.getObject("S_CORRESPONSIVE_ACCOUNT") == null|| rs.getString("S_CORRESPONSIVE_ACCOUNT").equals("null") ? "" : rs.getString("S_CORRESPONSIVE_ACCOUNT"));
        iBankDto.setCorresponsiveAccountName(rs.getObject("S_CORRESPONSIVE_ACCOUNT_NAME") == null || rs.getString("S_CORRESPONSIVE_ACCOUNT_NAME").equals("null")? "" : rs.getString("S_CORRESPONSIVE_ACCOUNT_NAME"));
        iBankDto.setAgency(rs.getObject("S_AGENCY") == null || rs.getString("S_AGENCY").equals("null")? "" : rs.getString("S_AGENCY"));
        iBankDto.setCorresponsiveBankName(rs.getObject("S_CORRESPONSIVE_BANK_NAME") == null || rs.getString("S_CORRESPONSIVE_BANK_NAME").equals("null")?"" : rs.getString("S_CORRESPONSIVE_BANK_NAME"));
        iBankDto.setCorresponsiveBankId(rs.getObject("S_CORRESPONSIVE_BANK_ID") == null || rs.getString("S_CORRESPONSIVE_BANK_ID").equals("null")? "" : rs.getString("S_CORRESPONSIVE_BANK_ID"));
        iBankDto.setServiceBranchId(rs.getObject("S_SERVICE_BRANCH_ID") == null || rs.getString("S_SERVICE_BRANCH_ID").equals("null")? "" : rs.getString("S_SERVICE_BRANCH_ID"));
        iBankDto.setServiceBankName(rs.getObject("S_SERVICE_BANK_NAME") == null || rs.getString("S_SERVICE_BANK_NAME").equals("null")? "" : rs.getString("S_SERVICE_BANK_NAME"));
        iBankDto.setChannel(rs.getObject("S_CHANNEL") == null || rs.getString("S_CHANNEL").equals("null")? "" : rs.getString("S_CHANNEL"));
        iBankDto.setCreateDate(rs.getTimestamp("D_CREATE"));
        iBankDto.setDesc(rs.getObject("S_DESC") == null || rs.getString("S_DESC").equals("null") ? "" : rs.getString("S_DESC"));
        iBankDto.setAccountNumber(rs.getObject("S_ACCOUNT_NUMBER") == null || rs.getString("S_ACCOUNT_NUMBER").equals("null")? "" : rs.getString("S_ACCOUNT_NUMBER"));
        return iBankDto;
    }
}
