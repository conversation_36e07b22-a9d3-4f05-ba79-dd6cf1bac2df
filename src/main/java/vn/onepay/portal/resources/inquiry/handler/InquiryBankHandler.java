package vn.onepay.portal.resources.inquiry.handler;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.inquiry.dao.InquiryBankDao;

public class InquiryBankHandler implements IConstants{
    private InquiryBankHandler() {

    }
    private static Logger logger = Logger.getLogger(InquiryBankHandler.class.getName());
    public static void getTransactions(RoutingContext ctx){
        ctx.vertx().executeBlocking(future ->{
            try {
                HttpServerRequest request = ctx.request();
                Map mIn = new HashMap<>();
                mIn.put(BANK_ID, request.getParam(BANK_ID));
                mIn.put(TYPE, request.getParam(TYPE));
                mIn.put(DESC, request.getParam(DESC));
                mIn.put(TRANSACTION_ID, request.getParam(TRANSACTION_ID));
                mIn.put(ACCOUNT_ID,request.getParam(ACCOUNT_ID));
                mIn.put(FROM_DATE, request.getParam(FROM_DATE));
                mIn.put(TO_DATE, request.getParam(TO_DATE));
                mIn.put(PAGE, request.getParam(PAGE)== null? "0":request.getParam(PAGE));
                mIn.put(PAGE_SIZE, request.getParam(PAGE_SIZE)== null? "100":request.getParam(PAGE_SIZE));
                Util.sendResponse(ctx, 200,InquiryBankDao.getTransactions(mIn));
            } catch (Exception e) {
                JsonObject response = new JsonObject().put("status", 500).put("message", "Search Transaction is Error");
                Util.sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        },false,null);
    }
    public static void getBankIdAndAccountNumber(RoutingContext ctx){
        ctx.vertx().executeBlocking(future ->{
            try {
                Util.sendResponse(ctx, 200,InquiryBankDao.getBankIdAndAccountNumber());
            } catch (Exception e) {
                JsonObject response = new JsonObject().put("status", 500).put("message", "Get bank is Error");
                Util.sendResponse(ctx, 500, response);
                ctx.fail(e);
            }
        },false,null);
    }
    public static void downloadInquiry(RoutingContext ctx){
        ctx.vertx().executeBlocking(future ->{
            try {
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID,
                        ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID,
                        ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP,
                        ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));
                Map<String, Object> mIn = new HashMap<>();
                JsonObject body = ctx.getBodyAsJson();
                String fileType = ctx.request().getParam("fileType");
                logger.info("fileType "+  fileType);
                mIn.put(BANK_ID, body.getString(BANK_ID) == null? "" : body.getString(BANK_ID));
                mIn.put(TYPE, body.getString(TYPE) == null? "" : body.getString(TYPE));
                mIn.put(DESC, body.getString(DESC) == null? "" : body.getString(DESC));
                mIn.put(TRANSACTION_ID, body.getString(TRANSACTION_ID) == null? "" : body.getString(TRANSACTION_ID));
                mIn.put(ACCOUNT_ID,body.getString(ACCOUNT_ID) == null? "" : body.getString(ACCOUNT_ID));
                mIn.put(FROM_DATE, body.getString(FROM_DATE) == null? "" : body.getString(FROM_DATE));
                mIn.put(TO_DATE, body.getString(TO_DATE) == null? "" : body.getString(TO_DATE));
                mIn.put(PAGE, "0");
                mIn.put(PAGE_SIZE, "********");
                BaseList baseList = InquiryBankDao.getTransactions(mIn);
                int totalRows =  baseList.getTotalItems();
                if (totalRows == 0) {
                    throw IErrors.NO_DATA_FOUND;
                }
                if (totalRows > 65000) {
                    throw IErrors.REACH_MAX_EXCEL_ROW;
                }
                long date = new Date().getTime();
                String fileName = "inquiry_" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("inquiry");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);

                if (totalRows <= 25000) {
                    fileDownloadDto.setExt(fileType);    
                } else {
                    fileDownloadDto.setExt("zip");
                }
                FileDownloadDao.insert(fileDownloadDto);
                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                            QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(),
                            ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY,
                            QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(),
                            ctx.request().path(), requestData));
                }
                Util.sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                ctx.fail(e);
            }
        },false,null);
    }
}
