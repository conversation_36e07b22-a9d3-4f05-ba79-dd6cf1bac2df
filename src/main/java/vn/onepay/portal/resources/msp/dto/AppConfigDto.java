package vn.onepay.portal.resources.msp.dto;

import java.sql.Timestamp;

public class AppConfigDto {
    private int app_id;
    private String app_name;
    private String app_short_name;
    private String code;
    private String description;
    private String type;
    private int index;
    private String type_config;
    public int getApp_id() {
        return app_id;
    }
    public void setApp_id(int app_id) {
        this.app_id = app_id;
    }
    public String getApp_name() {
        return app_name;
    }
    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }
    public String getApp_short_name() {
        return app_short_name;
    }
    public void setApp_short_name(String app_short_name) {
        this.app_short_name = app_short_name;
    }
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public int getIndex() {
        return index;
    }
    public void setIndex(int index) {
        this.index = index;
    }
    public String getType_config() {
        return type_config;
    }
    public void setType_config(String type_config) {
        this.type_config = type_config;
    }
    
}
