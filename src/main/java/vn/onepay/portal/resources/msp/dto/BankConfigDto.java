package vn.onepay.portal.resources.msp.dto;

import java.sql.Timestamp;

public class BankConfigDto {
    private String bank_name;
    private int n_bank_id;
    private String state;
    private String disable_type;
    private String note;
    private String short_bank_name;
    private String bank_code;
    private String swift_code;
    private String bank_name_dialog;
    private String pattern;
    private String bin;
    private String auth;
    private int index;
    private String bank_description;
    private String card_list;
    private Timestamp update;
    private String image;
    private String type_config;
    
    public String getBank_name() {
        return bank_name;
    }
    public void setBank_name(String bank_name) {
        this.bank_name = bank_name;
    }
    public int getN_bank_id() {
        return n_bank_id;
    }
    public void setN_bank_id(int n_bank_id) {
        this.n_bank_id = n_bank_id;
    }
    public String getState() {
        return state;
    }
    public void setState(String state) {
        this.state = state;
    }
    public String getDisable_type() {
        return disable_type;
    }
    public void setDisable_type(String disable_type) {
        this.disable_type = disable_type;
    }
    public String getNote() {
        return note;
    }
    public void setNote(String note) {
        this.note = note;
    }
    public String getShort_bank_name() {
        return short_bank_name;
    }
    public void setShort_bank_name(String short_bank_name) {
        this.short_bank_name = short_bank_name;
    }
    public String getBank_code() {
        return bank_code;
    }
    public void setBank_code(String bank_code) {
        this.bank_code = bank_code;
    }
    public String getSwift_code() {
        return swift_code;
    }
    public void setSwift_code(String swift_code) {
        this.swift_code = swift_code;
    }
    public String getBank_name_dialog() {
        return bank_name_dialog;
    }
    public void setBank_name_dialog(String bank_name_dialog) {
        this.bank_name_dialog = bank_name_dialog;
    }
    public String getPattern() {
        return pattern;
    }
    public void setPattern(String pattern) {
        this.pattern = pattern;
    }
    public String getBin() {
        return bin;
    }
    public void setBin(String bin) {
        this.bin = bin;
    }
    public String getAuth() {
        return auth;
    }
    public void setAuth(String auth) {
        this.auth = auth;
    }
    public int getIndex() {
        return index;
    }
    public void setIndex(int index) {
        this.index = index;
    }
    public String getBank_description() {
        return bank_description;
    }
    public void setBank_description(String bank_description) {
        this.bank_description = bank_description;
    }
    public String getCard_list() {
        return card_list;
    }
    public void setCard_list(String card_list) {
        this.card_list = card_list;
    }
    public Timestamp getUpdate() {
        return update;
    }
    public void setUpdate(Timestamp update) {
        this.update = update;
    }
    public String getImage() {
        return image;
    }
    public void setImage(String image) {
        this.image = image;
    }
    public String getType_config() {
        return type_config;
    }
    public void setType_config(String type_config) {
        this.type_config = type_config;
    }
    
}
