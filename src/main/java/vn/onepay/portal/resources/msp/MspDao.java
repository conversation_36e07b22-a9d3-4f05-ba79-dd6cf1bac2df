package vn.onepay.portal.resources.msp;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;
import vn.onepay.portal.resources.msp.dto.AppConfigDto;
import vn.onepay.portal.resources.msp.dto.BankConfigDto;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

public class MspDao extends Db implements IConstants {
    public static BaseList<BankConfigDto> getBankAll() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<BankConfigDto> result = new BaseList<>();
        List<BankConfigDto> listBankAll = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP.bank_get_all2(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST BANK error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listBankAll.add(blindBank(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listBankAll);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    public static BaseList<AppConfigDto> getAppAll() throws Exception {
        Connection conn = null;
        CallableStatement cs = null;
        ResultSet rs = null;

        BaseList<AppConfigDto> result = new BaseList<>();
        List<AppConfigDto> listAppAll = new ArrayList<>();

        try {
            conn = getConnection112();
            cs = conn.prepareCall("{ call ONEPORTAL.PKG_MSP.app_get_all(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            int nError = cs.getInt(3);
            String sError = cs.getString(4);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                logger.severe("GET LIST App error: " + nError + " - " + sError);
            } else {
                while (rs != null && rs.next()) {
                    listAppAll.add(blindApp(rs));
                }
                result.setTotalItems(cs.getInt(2));
                result.setList(listAppAll);
            }
        }  catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, conn);
        }

        return result;
    }

    private static BankConfigDto blindBank(ResultSet rs) throws SQLException {
        BankConfigDto config = new BankConfigDto();
        config.setBank_name(rs.getString("S_BANK_NAME"));
        config.setN_bank_id(rs.getInt("N_BANK_ID"));
        config.setState(rs.getString("S_STATE"));
        config.setDisable_type(rs.getString("S_DISABLE_TYPE"));
        config.setNote(rs.getString("S_NOTE"));
        config.setShort_bank_name(rs.getString("S_SHORT_BANK_NAME"));
        config.setBank_code(rs.getString("S_BANK_CODE"));
        config.setSwift_code(rs.getString("S_SWIFT_CODE"));
        config.setBank_name_dialog(rs.getString("S_BANK_NAME_DIALOG"));
        config.setPattern(rs.getString("S_PATTERN"));
        config.setBin(rs.getString("S_BIN"));
        config.setAuth(rs.getString("S_AUTH"));
        config.setIndex(rs.getInt("N_INDEX"));
        config.setBank_description(rs.getString("S_BANK_DESCRIPTION"));
        config.setCard_list(rs.getString("S_CARD_LIST"));
        config.setImage(rs.getString("S_IMAGE"));
        config.setUpdate(Util.getColumnTimeStamp(rs, "D_UPDATE"));
        config.setType_config(rs.getString("S_TYPE_CONFIG"));
        return config;
    }

    private static AppConfigDto blindApp(ResultSet rs) throws SQLException {
        AppConfigDto config = new AppConfigDto();
        config.setApp_id(rs.getInt("N_ID"));
        config.setApp_name(rs.getString("S_APP_NAME"));
        config.setApp_short_name(rs.getString("S_APP_SHORT_NAME"));
        config.setCode(rs.getString("S_CODE"));
        config.setDescription(rs.getString("S_DESCRIPTION"));
        config.setType(rs.getString("S_TYPE"));
        config.setIndex(rs.getInt("N_INDEX"));
        config.setType_config(rs.getString("S_TYPE_CONFIG"));
        return config;
    }

}

