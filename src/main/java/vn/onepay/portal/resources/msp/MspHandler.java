package vn.onepay.portal.resources.msp;

import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.resources.merchantconfig.dto.MerchantConfigDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class MspHandler  {
    private static Logger logger = Logger.getLogger(MspHandler.class.getName());

    public static void getBankAll(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MspDao.getBankAll());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getAppAll(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                sendResponse(ctx, 200, MspDao.getAppAll());
            } catch (Exception e) {
                ctx.fail(e);
            }
        }, false, null);
    }
}
