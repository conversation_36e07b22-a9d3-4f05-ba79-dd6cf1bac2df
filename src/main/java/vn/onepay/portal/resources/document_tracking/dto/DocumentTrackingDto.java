package vn.onepay.portal.resources.document_tracking.dto;

import java.sql.Timestamp;

public class DocumentTrackingDto {
    private int n_id;
    private int subDocumentId;
    private String merchantName;
    private String contractType;
    private String contractName;
    private String contractNumber;
    private String branchName;
    private String taxCode;
    private String notes;
    private Timestamp signDate;
    private Timestamp sendHN_before_date;
    private String sendHN_before_user;
    private Timestamp submission_date;
    private String submission_user;
    private Timestamp sendHCM_date;
    private String sendHCM_user;
    private Timestamp hcmReceive_date;
    private String hcmReceive_user;
    private Timestamp sendBank_date;
    private String sendBank_user;
    private Timestamp sendHN_after_date;
    private String sendHN_after_user;
    private Timestamp ms_accounting_date;
    private String ms_accounting_user;
    private Timestamp sale_date;
    private String sale_user;
    private Timestamp create_date;
    private String location;
    private Integer exportId;
    private Integer groupExport;
    private String receiverPartner;
    private int contractId;
    private String createUser;

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public int getContractId() {
        return contractId;
    }

    public void setContractId(int contractId) {
        this.contractId = contractId;
    }

    public DocumentTrackingDto() {
    }

    public DocumentTrackingDto(int n_id, String merchantName, String contractType, String notes, String location, String taxCode) {
        this.n_id = n_id;
        this.merchantName = merchantName;
        this.contractType = contractType;
        this.notes = notes;
        this.location = location;
        this.taxCode = taxCode;
    }

    public DocumentTrackingDto(int n_id, String merchantName, String contractType, String contractName, String contractNumber, String branchName, String notes, Timestamp submission_date, String submission_user, Timestamp sendBank_date, String sendBank_user, Timestamp ms_accounting_date, String ms_accounting_user, Timestamp sale_date, String sale_user, Timestamp create_date, String location) {
        this.n_id = n_id;
        this.merchantName = merchantName;
        this.contractType = contractType;
        this.contractName = contractName;
        this.contractNumber = contractNumber;
        this.branchName = branchName;
        this.notes = notes;
        this.submission_date = submission_date;
        this.submission_user = submission_user;
        this.sendBank_date = sendBank_date;
        this.sendBank_user = sendBank_user;
        this.ms_accounting_date = ms_accounting_date;
        this.ms_accounting_user = ms_accounting_user;
        this.sale_date = sale_date;
        this.sale_user = sale_user;
        this.create_date = create_date;
        this.location = location;
    }

    public DocumentTrackingDto(int n_id, String merchantName, String contractType, String contractName, String contractNumber, String branchName, String notes, Timestamp sendHN_before_date, String sendHN_before_user, Timestamp submission_date, String submission_user, Timestamp sendHCM_date, String sendHCM_user, Timestamp hcmReceive_date, String hcmReceive_user, Timestamp sendBank_date, String sendBank_user, Timestamp sendHN_after_date, String sendHN_after_user, Timestamp ms_accounting_date, String ms_accounting_user, Timestamp sale_date, String sale_user, Timestamp create_date, String location) {
        this.n_id = n_id;
        this.merchantName = merchantName;
        this.contractType = contractType;
        this.contractName = contractName;
        this.contractNumber = contractNumber;
        this.branchName = branchName;
        this.notes = notes;
        this.sendHN_before_date = sendHN_before_date;
        this.sendHN_before_user = sendHN_before_user;
        this.submission_date = submission_date;
        this.submission_user = submission_user;
        this.sendHCM_date = sendHCM_date;
        this.sendHCM_user = sendHCM_user;
        this.hcmReceive_date = hcmReceive_date;
        this.hcmReceive_user = hcmReceive_user;
        this.sendBank_date = sendBank_date;
        this.sendBank_user = sendBank_user;
        this.sendHN_after_date = sendHN_after_date;
        this.sendHN_after_user = sendHN_after_user;
        this.ms_accounting_date = ms_accounting_date;
        this.ms_accounting_user = ms_accounting_user;
        this.sale_date = sale_date;
        this.sale_user = sale_user;
        this.create_date = create_date;
        this.location = location;
    }

    public int getN_id() {
        return n_id;
    }

    public void setN_id(int n_id) {
        this.n_id = n_id;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Timestamp getSubmission_date() {
        return submission_date;
    }

    public void setSubmission_date(Timestamp submission_date) {
        this.submission_date = submission_date;
    }

    public String getSubmission_user() {
        return submission_user;
    }

    public void setSubmission_user(String submission_user) {
        this.submission_user = submission_user;
    }

    public Timestamp getSendHCM_date() {
        return sendHCM_date;
    }

    public void setSendHCM_date(Timestamp sendHCM_date) {
        this.sendHCM_date = sendHCM_date;
    }

    public String getSendHCM_user() {
        return sendHCM_user;
    }

    public void setSendHCM_user(String sendHCM_user) {
        this.sendHCM_user = sendHCM_user;
    }

    public Timestamp getHcmReceive_date() {
        return hcmReceive_date;
    }

    public void setHcmReceive_date(Timestamp hcmReceive_date) {
        this.hcmReceive_date = hcmReceive_date;
    }

    public String getHcmReceive_user() {
        return hcmReceive_user;
    }

    public void setHcmReceive_user(String hcmReceive_user) {
        this.hcmReceive_user = hcmReceive_user;
    }

    public Timestamp getSendBank_date() {
        return sendBank_date;
    }

    public void setSendBank_date(Timestamp sendBank_date) {
        this.sendBank_date = sendBank_date;
    }

    public String getSendBank_user() {
        return sendBank_user;
    }

    public void setSendBank_user(String sendBank_user) {
        this.sendBank_user = sendBank_user;
    }

    public Timestamp getSendHN_before_date() {
        return sendHN_before_date;
    }

    public void setSendHN_before_date(Timestamp sendHN_before_date) {
        this.sendHN_before_date = sendHN_before_date;
    }

    public String getSendHN_before_user() {
        return sendHN_before_user;
    }

    public void setSendHN_before_user(String sendHN_before_user) {
        this.sendHN_before_user = sendHN_before_user;
    }

    public Timestamp getSendHN_after_date() {
        return sendHN_after_date;
    }

    public void setSendHN_after_date(Timestamp sendHN_after_date) {
        this.sendHN_after_date = sendHN_after_date;
    }

    public String getSendHN_after_user() {
        return sendHN_after_user;
    }

    public void setSendHN_after_user(String sendHN_after_user) {
        this.sendHN_after_user = sendHN_after_user;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Timestamp getMs_accounting_date() {
        return ms_accounting_date;
    }

    public void setMs_accounting_date(Timestamp ms_accounting_date) {
        this.ms_accounting_date = ms_accounting_date;
    }

    public String getMs_accounting_user() {
        return ms_accounting_user;
    }

    public void setMs_accounting_user(String ms_accounting_user) {
        this.ms_accounting_user = ms_accounting_user;
    }

    public Timestamp getSale_date() {
        return sale_date;
    }

    public void setSale_date(Timestamp sale_date) {
        this.sale_date = sale_date;
    }

    public String getSale_user() {
        return sale_user;
    }

    public void setSale_user(String sale_user) {
        this.sale_user = sale_user;
    }

    /**
     * @return the taxCode
     */
    public String getTaxCode() {
        return taxCode;
    }

    /**
     * @param taxCode the taxCode to set
     */
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    /**
     * @return the signDate
     */
    public Timestamp getSignDate() {
        return signDate;
    }

    /**
     * @param signDate the signDate to set
     */
    public void setSignDate(Timestamp signDate) {
        this.signDate = signDate;
    }

    /**
     * @return the subDocumentId
     */
    public int getSubDocumentId() {
        return subDocumentId;
    }

    /**
     * @param subDocumentId the subDocumentId to set
     */
    public void setSubDocumentId(int subDocumentId) {
        this.subDocumentId = subDocumentId;
    }

    public Integer getExportId() {
        return this.exportId;
    }

    public void setExportId(Integer exportId) {
        this.exportId = exportId;
    }

    public Integer getGroupExport() {
        return this.groupExport;
    }

    public void setGroupExport(Integer groupExport) {
        this.groupExport = groupExport;
    }

    public String getReceiverPartner() {
        return receiverPartner;
    }

    public void setReceiverPartner(String receiverPartner) {
        this.receiverPartner = receiverPartner;
    }

}
