package vn.onepay.portal.resources.document_tracking.dto;

public class SubDocumentDto {
    private int id;
    private String documentNumber;
    private String branch;
    private int documentId;
    private String signDay;
    private String receiverPartner;
    private String documentName;
    private String createUser;
    private int contractId;

    public int getContractId() {
        return contractId;
    }

    public void setContractId(int contractId) {
        this.contractId = contractId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public SubDocumentDto() {}

    public SubDocumentDto(int id, String documentNumber, String branch, int documentId, String signDay,
            String receiverPartner) {
        this.id = id;
        this.documentNumber = documentNumber;
        this.branch = branch;
        this.documentId = documentId;
        this.signDay = signDay;
        this.receiverPartner = receiverPartner;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDocumentNumber() {
        return this.documentNumber;
    }

    public void setDocumentNumber(String documentNumber) {
        this.documentNumber = documentNumber;
    }

    public String getBranch() {
        return this.branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public int getDocumentId() {
        return this.documentId;
    }

    public void setDocumentId(int documentId) {
        this.documentId = documentId;
    }

    public String getSignDay() {
        return this.signDay;
    }

    public void setSignDay(String signDay) {
        this.signDay = signDay;
    }

    public String getReceiverPartner() {
        return receiverPartner;
    }

    public void setReceiverPartner(String receiverPartner) {
        this.receiverPartner = receiverPartner;
    }

}
