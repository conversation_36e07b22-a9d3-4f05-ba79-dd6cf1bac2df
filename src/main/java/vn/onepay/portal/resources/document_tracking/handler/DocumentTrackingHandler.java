package vn.onepay.portal.resources.document_tracking.handler;

import com.google.gson.Gson;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.queue.server.QueueServer;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.document_tracking.dao.DocumentTrackingDao;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.resources.document_tracking.dto.SubDocumentDto;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static vn.onepay.portal.Util.sendResponse;

public class DocumentTrackingHandler implements IConstants {

    private static final Logger logger = Logger.getLogger(vn.onepay.portal.resources.document_tracking.handler.DocumentTrackingHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void getDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                HttpServerRequest request = ctx.request();
                Map<String, Object> mIn = new HashMap<>();
                String typeSelect = ctx.request().getParam("typeSelect");
                String fromDate = ctx.request().getParam(FROM_DATE);
                String toDate = ctx.request().getParam(TO_DATE);
                mIn.put("typeSelect", typeSelect);
                mIn.put("merchantName", request.getParam("merchantName"));
                mIn.put("currentStatus", request.getParam("currentStatus"));
                mIn.put("documentType", request.getParam("documentType"));
                mIn.put("documentName", request.getParam("documentName"));
                mIn.put("documentNumber", request.getParam("documentNumber"));
                mIn.put("receiverPartner", request.getParam("receiverPartner"));
                mIn.put("email", request.getParam("email"));
                mIn.put("dateType", request.getParam("dateType"));

                if (typeSelect.equals("ALL")) {
                    mIn.put("page_size_HN", request.getParam("page_size_HN") == null ? 0 : Integer.parseInt(request.getParam("page_size_HN")));
                    mIn.put("page_size_HCM", request.getParam("page_size_HCM") == null ? 0 : Integer.parseInt(request.getParam("page_size_HCM")));
                    mIn.put("page_HN", request.getParam("page_HN") == null ? 0 : Integer.parseInt(request.getParam("page_HN")));
                    mIn.put("page_HCM", request.getParam("page_HCM") == null ? 0 : Integer.parseInt(request.getParam("page_HCM")));
                } else if (typeSelect.equals("HN")) {
                    mIn.put("page_size_HN", request.getParam("page_size_HN") == null ? 0 : Integer.parseInt(request.getParam("page_size_HN")));
                    mIn.put("page_HN", request.getParam("page_HN") == null ? 0 : Integer.parseInt(request.getParam("page_HN")));
                } else if (typeSelect.equals("HCM")) {
                    mIn.put("page_size_HCM", request.getParam("page_size_HCM") == null ? 0 : Integer.parseInt(request.getParam("page_size_HCM")));
                    mIn.put("page_HCM", request.getParam("page_HCM") == null ? 0 : Integer.parseInt(request.getParam("page_HCM")));
                }
                mIn.put(OFFSET, 0);
                mIn.put(FROM_DATE, fromDate);
                mIn.put(TO_DATE, toDate);

                sendResponse(ctx, 200, DocumentTrackingDao.getDocumentTracking(mIn, typeSelect));
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET DOCUMENT TRACKING: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void postOrPutDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                Integer id = bodyJson.getInteger("id") == null ? 0 : bodyJson.getInteger("id");
                List<LinkedHashMap<String, Object>> listmaps = bodyJson.getJsonArray("subDocuments").getList();
                List<SubDocumentDto> subDocuments = new ArrayList();
                for (LinkedHashMap<String, Object> map : listmaps) {
                    subDocuments.add(new SubDocumentDto((int) map.get("id"), (String) map.get("documentNumber"), (String) map.get("branch"), (int) map.get("documentId"), (String) map.get("signDay"), (String) map.get("receiverPartner")));
                }
                String merchantName = bodyJson.getString("merchantName");
                String contractType = bodyJson.getString("contractType");
                String taxCode = bodyJson.getString("taxCode");
                String note = bodyJson.getString("note");
                String location = bodyJson.getString("location");
                String email = bodyJson.getString("email");
                Integer nResult = 200;
                DocumentTrackingDto documentTrackingDto = new DocumentTrackingDto(id, merchantName, contractType, note, location, taxCode);

                if (id == 0) {
                    nResult = DocumentTrackingDao.postDocumentTracking(documentTrackingDto, subDocuments, email);
                } else {
                    List<SubDocumentDto> before = DocumentTrackingDao.getSubDocumentsByTrackingID(id);

                    nResult = DocumentTrackingDao.putDocumentTracking(documentTrackingDto, before, subDocuments, email);
                }

                if (nResult == 200) {
                    mIn.put("n_result", 200);
                } else {
                    mIn.put("n_result", 500);
                }
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "POST DOCUMENT TRACKING: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void deleteDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray listID = bodyJson.getJsonArray("listId");
                Map<String, Object> mIn = new HashMap<>();
                Integer n_result = 500, n_resul2 = 500;
                for (Object id : listID) {
                    n_result = DocumentTrackingDao.deleteDocumentTracking(Integer.parseInt(id.toString()), "DOC");
                }
                for (Object id : listID) {
                    n_resul2 = DocumentTrackingDao.deleteDocumentTracking(Integer.parseInt(id.toString()), "STATE");
                }
                if (n_result == 200 && n_resul2 == 200) {
                    mIn.put("n_result", 200);
                } else {
                    mIn.put("n_result", 500);
                }
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DELETE DOCUMENT TRACKING: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void postStateDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                int id = bodyJson.getString("n_id") != null ? Integer.parseInt(bodyJson.getString("n_id")) : 0;
                int subId = bodyJson.getString("sub_id") != null ? Integer.parseInt(bodyJson.getString("sub_id")) : 0;
                String state = bodyJson.getString("state");
                String email = bodyJson.getString("user");
                String[] emails = email.split("@", 0);
                String user = emails[0];
                Integer nResult;
                if (subId > 0) {
                    nResult = DocumentTrackingDao.postStateDocumentTracking(subId, state, user, state.equals("MS_ACCOUNTING"));
                } else {
                    nResult = DocumentTrackingDao.postStateDocumentTracking(id, state, user, false);
                }
                Map<String, Object> mIn = new HashMap<>();

                if (nResult == 200) {
                    mIn.put("n_result", 200);
                } else {
                    mIn.put("n_result", 500);
                }
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "POST STATE DOCUMENT TRACKING: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void downloadDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();

                Map<String, Object> mIn = new HashMap<>();

                mIn.put("search_type", bodyJson.getString("search_type") == null ? "" : bodyJson.getString("search_type"));
                mIn.put("typeDownload", bodyJson.getString("typeDownload") == null ? "" : bodyJson.getString("typeDownload"));
                mIn.put("merchantName", bodyJson.getString("merchantName") == null ? "" : bodyJson.getString("merchantName"));
                mIn.put("currentStatus", bodyJson.getString("currentStatus") == null ? "" : bodyJson.getString("currentStatus"));
                mIn.put("documentType", bodyJson.getString("documentType") == null ? "" : bodyJson.getString("documentType"));
                mIn.put("documentName", bodyJson.getString("documentName") == null ? "" : bodyJson.getString("documentName"));
                mIn.put("documentNumber", bodyJson.getString("documentNumber") == null ? "" : bodyJson.getString("documentNumber"));
                mIn.put("receiverPartner", bodyJson.getString("receiverPartner") == null ? "" : bodyJson.getString("receiverPartner"));
                mIn.put("email", bodyJson.getString("email") == null ? "" : bodyJson.getString("email"));
                mIn.put("dateType", bodyJson.getString("dateType") == null ? "" : bodyJson.getString("dateType"));
                mIn.put(FROM_DATE, bodyJson.getString(FROM_DATE) == null ? "" : bodyJson.getString(FROM_DATE));
                mIn.put(TO_DATE, bodyJson.getString(TO_DATE) == null ? "" : bodyJson.getString(TO_DATE));

                // requestData in message
                Map<String, Object> requestData = new HashMap<>();
                requestData.put(X_USER_ID, ctx.request().getHeader(X_USER_ID) == null ? "" : ctx.request().getHeader(X_USER_ID));
                requestData.put(X_REQUEST_ID, ctx.request().getHeader(X_REQUEST_ID) == null ? "" : ctx.request().getHeader(X_REQUEST_ID));
                requestData.put(X_REAL_IP, ctx.request().getHeader(X_REAL_IP) == null ? "" : ctx.request().getHeader(X_REAL_IP));

                int totalRows = 0;
                if (mIn.get("typeDownload").toString().equals("ALL")) {
                    int totalHN = DocumentTrackingDao.getTotalDocumentTrackingHN(mIn, "DOWNLOAD_TOTAL_HN");
                    int totalHCM = DocumentTrackingDao.getTotalDocumentTrackingHCM(mIn, "DOWNLOAD_TOTAL_HCM");
                    totalRows = totalHN + totalHCM;
                    if (totalRows == 0) {
                        throw IErrors.NO_DATA_FOUND;
                    }
                } else if (mIn.get("typeDownload").toString().equals("HN")) {
                    totalRows = DocumentTrackingDao.getTotalDocumentTrackingHN(mIn, "DOWNLOAD_TOTAL_HN");
                    if (totalRows == 0) {
                        throw IErrors.NO_DATA_FOUND;
                    }
                } else {
                    totalRows = DocumentTrackingDao.getTotalDocumentTrackingHCM(mIn, "DOWNLOAD_TOTAL_HCM");
                    if (totalRows == 0) {
                        throw IErrors.NO_DATA_FOUND;
                    }
                }


                // Initial file information
                long date = new java.util.Date().getTime();
                String fileName = "document_tracking_report" + date;
                String fileHashName = "";
                Map<String, Object> data = new HashMap<>();
                data.put("parameter", mIn);
                data.put("file_name", fileName);
                data.put("row", totalRows);
                try {
                    fileHashName = Convert.hash(fileName + "_" + requestData.get(X_USER_ID) + date);
                } catch (NoSuchAlgorithmException e) {
                    ctx.fail(e);
                } catch (UnsupportedEncodingException e) {
                    ctx.fail(e);
                }
                requestData.put(FILE_HASH_NAME, fileHashName);
                requestData.put(FILE_NAME, fileName);
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setUser(requestData.get(X_USER_ID).toString());
                fileDownloadDto.setFile_type("document_tracking_report");
                fileDownloadDto.setFile_name(fileName);
                fileDownloadDto.setFile_hash_name(fileHashName);
                fileDownloadDto.setConditions(bodyJson.encode());
                if (totalRows <= Config.getFileRowLevel()) {
                    if(bodyJson.getString("search_type").equals("ACCOUNTINGDATE")) {
                        fileDownloadDto.setExt("xls");
                    }
                    else {
                        fileDownloadDto.setExt("xls");
                    }
                } else {
                    fileDownloadDto.setExt("zip");
                }

                FileDownloadDao.insert(fileDownloadDto);

                if (totalRows <= Config.getFileRowLevel()) {
                    // fileDownload.setExt("csv");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueFastIn(), QueueServer.getDownloadQueueFastOut(), ctx.request().path(), requestData));
                } else {
                    // fileDownload.setExt("zip");
                    QueueProducer.sendMessage(new Message(mIn, JMSMessageCreator.MEDIUM_PRIORITY, QueueServer.getDownloadQueueSlowIn(), QueueServer.getDownloadQueueSlowOut(), ctx.request().path(), requestData));
                }

                sendResponse(ctx, 200, fileDownloadDto);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DOWNLOAD DOCUMENT TRACKING ERROR: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void getDocumentType(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> result = new HashMap<>();
                result.put("list", DocumentTrackingDao.getListDocumentType());
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DOCUMENT TYPE: ", e);
            }
        }, false, null);
    }

    public static void getSubDocumentsByTrackingID(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                int id = Integer.parseInt(ctx.request().getParam("id"));
                Map<String, Object> result = new HashMap<>();
                result.put("list", DocumentTrackingDao.getSubDocumentsByTrackingID(id));
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET SUB DOCUMENT BY TRACKING ID TYPE: ", e);
            }
        }, false, null);
    }

    public static void getReceiverPartner(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> result = new HashMap<>();
                result.put("list", DocumentTrackingDao.getListReceiverPartner());
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DOCUMENT TYPE: ", e);
            }
        }, false, null);
    }

    public static void getDocumentName(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                Map<String, Object> result = new HashMap<>();
                result.put("list", DocumentTrackingDao.getListDocumentName());
                sendResponse(ctx, 200, result);
            } catch (Exception e) {
                logger.log(Level.WARNING, "GET LIST DOCUMENT TYPE: ", e);
            }
        }, false, null);
    }

    public static void updateDocumentNote(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                Map<String, Object> mIn = new HashMap<>();
                Integer id = bodyJson.getInteger("id") == null ? 0 : bodyJson.getInteger("id");
                String note = bodyJson.getString("note");

                Integer nResult = DocumentTrackingDao.updateDocumentNote(id, note);

                if (nResult == 200) {
                    mIn.put("result", 200);
                } else {
                    mIn.put("result", 500);
                }
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "updateDocumentNote error: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }

    public static void updateMultiStateDocumentTracking(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {
                JsonObject bodyJson = ctx.getBodyAsJson();
                JsonArray listDoc = bodyJson.getJsonArray("listDoc");
                Map<String, Object> mIn = new HashMap<>();
                Integer n_result = 500;
                if (listDoc != null && !listDoc.isEmpty()) {
                    for (int i = 0; i < listDoc.size(); i++) {
                        JsonObject doc = listDoc.getJsonObject(i);
                        Integer nId = doc.getInteger("n_id");
                        Integer subId = doc.getInteger("subDocumentId");
                        String state = bodyJson.getString("state","");
                        String email = bodyJson.getString("email", "");
                        String[] emails = email.split("@", 0);
                        String user = emails[0];
                        System.out.println("user: " + user);
                        if (subId > 0) {
                            n_result = DocumentTrackingDao.postStateDocumentTracking(subId, state, user, state.equals("MS_ACCOUNTING"));
                        } else {
                            n_result = DocumentTrackingDao.postStateDocumentTracking(nId, state, user, false);
                        }
                    }
                }
                
                if (n_result == 200) {
                    mIn.put("n_result", 200);
                } else {
                    mIn.put("n_result", 500);
                }
                sendResponse(ctx, 200, mIn);
            } catch (Exception e) {
                logger.log(Level.WARNING, "DELETE DOCUMENT TRACKING: ", e);
                ctx.fail(e);
            }
        }, false, null);
    }
}
