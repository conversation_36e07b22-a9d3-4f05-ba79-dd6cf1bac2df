package vn.onepay.portal.resources.document_tracking.dao;

import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTypeDto;
import vn.onepay.portal.resources.document_tracking.dto.ReceiverPartnerDto;
import vn.onepay.portal.resources.document_tracking.dto.SubDocumentDto;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class DocumentTrackingDao extends Db implements IConstants {

    private static final String GET_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.GET_DOCUMENT_TRACKING_V2(?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String POST_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.POST_DOCUMENT_TRACKING_V2(?,?,?,?,?,?,?,?) }";
    private static final String POST_SUB_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.POST_SUB_DOCUMENT_TRACKING(?,?,?,?,?,?) }";
    private static final String PUT_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.PUT_DOCUMENT_TRACKING_V2(?,?,?,?,?,?,?) }";
    private static final String PUT_SUB_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.PUT_SUB_DOCUMENT_TRACKING(?,?,?,?,?,?) }";
    private static final String POST_FINISH_STATE = "{call PKG_ONEPORTAL2.POST_FINISH_STATE(?,?,?,?,?) }";
    private static final String UPDATE_SUB_DOCUMENT_TRACKING_ID = "{call PKG_ONEPORTAL2.UPDATE_SUB_DOCUMENT(?,?,?) }";
    private static final String DELETE_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.DELETE_DOCUMENT_TRACKING(?,?,?,?,?) }";
    private static final String DELETE_SUB_DOCUMENT_TRACKING = "{call PKG_ONEPORTAL2.DELETE_SUB_DOCUMENT(?,?,?) }";
    private static final String GET_DOCUMENT_TYPE = "{call PKG_ONEPORTAL2.GET_DOCUMENT_TYPE(?,?,?) }";
    private static final String GET_SUB_DOCUMENT_BY_TRACKING_ID = "{call PKG_ONEPORTAL2.GET_SUB_DOCUMENT(?,?,?,?) }";
    private static final String GET_RECEIVER_PARTNER = "{call PKG_ONEPORTAL2.GET_RECEIVER_PARTNER(?,?,?)}";
    private static final String GET_DOCUMENT_NAME = "{call PKG_ONEPORTAL2.GET_DOCUMENT_NAME(?,?,?)}";
    private static final String GET_DOCUMENT_TRACKING_BY_PERMISSION = "{call PKG_DOC_TRACKING#GET_BY_ROLE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final String POST_SUB_DOCUMENT_TRACKING_V2 = "{call PKG_DOC_TRACKING#POST_SUB_V2(?,?,?,?,?,?,?) }";
    private static final String DELETE_DOCUMENT_TRACKING_V2 = "{call PKG_DOC_TRACKING#DELETE_DOC(?,?,?,?,?) }";

    private static final Logger LOGGER = Logger.getLogger(DocumentTrackingDao.class.getName());

    public static void close(AutoCloseable[] objs) {
        for (AutoCloseable obj : objs)
            try {
                if (obj != null)
                    obj.close();
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "", e);
            }
    }

    public static Map<String, Object> getDocumentTracking(Map mIn, String typeSelect) throws Exception {
        LOGGER.log(Level.INFO, "query condition: " + mIn);
        Map<String, Object> dataList = new HashMap<>();
        if (typeSelect.equals("ALL")) {
            List<DocumentTrackingDto> dataHN = getDocumentTrackingHN(mIn, "SELECT");
            Integer totalHN = getTotalDocumentTrackingHN(mIn, "TOTAL_HN");
            List<DocumentTrackingDto> dataHCM = getDocumentTrackingHCM(mIn, "SELECT");
            Integer totalHCM = getTotalDocumentTrackingHCM(mIn, "TOTAL_HCM");
            dataList.put("data_HN", dataHN);
            dataList.put("total_HN", totalHN);
            dataList.put("data_HCM", dataHCM);
            dataList.put("total_HCM", totalHCM);
        } else if (typeSelect.equals("HN")) {
            List<DocumentTrackingDto> dataHN = getDocumentTrackingHN(mIn, "SELECT");
            Integer totalHN = getTotalDocumentTrackingHN(mIn, "TOTAL_HN");
            dataList.put("data_HN", dataHN);
            dataList.put("total_HN", totalHN);
        } else if (typeSelect.equals("HCM")) {
            List<DocumentTrackingDto> dataHCM = getDocumentTrackingHCM(mIn, "SELECT");
            Integer totalHCM = getTotalDocumentTrackingHCM(mIn, "TOTAL_HCM");
            dataList.put("data_HCM", dataHCM);
            dataList.put("total_HCM", totalHCM);
        }

        return dataList;
    }

    public static List<DocumentTrackingDto> getDocumentTrackingHN(Map mIn, String type) throws Exception {
        Exception exception = null;
        List<DocumentTrackingDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_TRACKING_BY_PERMISSION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type.equals("DOWNLOAD_HN") ? "DOWNLOAD_HN" : "SELECT_HN");
            cs.setString(5, mIn.get("merchantName").toString());
            cs.setString(6, mIn.get("currentStatus").toString());
            cs.setString(7, mIn.get("documentType").toString());
            cs.setString(8, mIn.get("documentName").toString());
            cs.setString(9, mIn.get("documentNumber").toString());
            cs.setString(10, mIn.get("receiverPartner").toString());
            cs.setString(11, mIn.get(FROM_DATE).toString());
            cs.setString(12, mIn.get(TO_DATE).toString());
            cs.setInt(13, type.equals("DOWNLOAD_HN") ? 0 : Integer.parseInt(mIn.get("page_size_HN").toString()));
            cs.setInt(14, type.equals("DOWNLOAD_HN") ? 0 : Integer.parseInt(mIn.get("page_HN").toString()));
            cs.setString(15, mIn.get("email").toString());
            cs.setString(16, mIn.get("dateType").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_TRACKING_HN : " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindDocumentTrackingHN(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalDocumentTrackingHN(Map mIn, String type) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_TRACKING_BY_PERMISSION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type);
            cs.setString(5, mIn.get("merchantName").toString());
            cs.setString(6, mIn.get("currentStatus").toString());
            cs.setString(7, mIn.get("documentType").toString());
            cs.setString(8, mIn.get("documentName").toString());
            cs.setString(9, mIn.get("documentNumber").toString());
            cs.setString(10, mIn.get("receiverPartner").toString());
            cs.setString(11, mIn.get(FROM_DATE).toString());
            cs.setString(12, mIn.get(TO_DATE).toString());
            cs.setInt(13, type.equals("DOWNLOAD_TOTAL_HN") ? 0 : Integer.parseInt(mIn.get("page_size_HN").toString()));
            cs.setInt(14, type.equals("DOWNLOAD_TOTAL_HN") ? 0 : Integer.parseInt(mIn.get("page_HN").toString()));
            cs.setString(15, mIn.get("email").toString());
            cs.setString(16, mIn.get("dateType").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_TOTAL_DOCUMENT_TRACKING_HN: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<DocumentTrackingDto> getDocumentTrackingHCM(Map mIn, String type) throws Exception {
        Exception exception = null;
        List<DocumentTrackingDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_TRACKING_BY_PERMISSION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type.equals("DOWNLOAD_HCM") ? "DOWNLOAD_HCM" : "SELECT_HCM");
            cs.setString(5, mIn.get("merchantName").toString());
            cs.setString(6, mIn.get("currentStatus").toString());
            cs.setString(7, mIn.get("documentType").toString());
            cs.setString(8, mIn.get("documentName").toString());
            cs.setString(9, mIn.get("documentNumber").toString());
            cs.setString(10, mIn.get("receiverPartner").toString());
            cs.setString(11, mIn.get(FROM_DATE).toString());
            cs.setString(12, mIn.get(TO_DATE).toString());
            cs.setInt(13, type.equals("DOWNLOAD_HCM") ? 0 : Integer.parseInt(mIn.get("page_size_HCM").toString()));
            cs.setInt(14, type.equals("DOWNLOAD_HCM") ? 0 : Integer.parseInt(mIn.get("page_HCM").toString()));
            cs.setString(15, mIn.get("email").toString());
            cs.setString(16, mIn.get("dateType").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_TRACKING_HCM: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result.add(bindDocumentTrackingHCM(rs));
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer getTotalDocumentTrackingHCM(Map mIn, String type) throws Exception {
        Exception exception = null;
        Integer result = 0;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_TRACKING_BY_PERMISSION);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, type);
            cs.setString(5, mIn.get("merchantName").toString());
            cs.setString(6, mIn.get("currentStatus").toString());
            cs.setString(7, mIn.get("documentType").toString());
            cs.setString(8, mIn.get("documentName").toString());
            cs.setString(9, mIn.get("documentNumber").toString());
            cs.setString(10, mIn.get("receiverPartner").toString());
            cs.setString(11, mIn.get(FROM_DATE).toString());
            cs.setString(12, mIn.get(TO_DATE).toString());
            cs.setInt(13, type.equals("DOWNLOAD_TOTAL_HCM") ? 0 : Integer.parseInt(mIn.get("page_size_HCM").toString()));
            cs.setInt(14, type.equals("DOWNLOAD_TOTAL_HCM") ? 0 : Integer.parseInt(mIn.get("page_HCM").toString()));
            cs.setString(15, mIn.get("email").toString());
            cs.setString(16, mIn.get("dateType").toString());
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_TOTAL_DOCUMENT_TRACKING_HCM: " + error);
            } else {
                while (rs != null && rs.next()) {
                    result = rs.getInt("N_TOTAL");
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static Integer postDocumentTracking(DocumentTrackingDto documentTrackingDto, List<SubDocumentDto> subDocuments, String email) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            con.setAutoCommit(false);
            cs = con.prepareCall(POST_DOCUMENT_TRACKING);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.setString(4, documentTrackingDto.getMerchantName());
            cs.setString(5, documentTrackingDto.getContractType());;
            cs.setString(6, documentTrackingDto.getNotes());
            cs.setString(7, documentTrackingDto.getLocation());
            cs.setString(8, documentTrackingDto.getTaxCode());

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result != 200) {
                throw new Exception("DB POST_DOCUMENT_TRACKING: " + error);
            }

            int docTrackingId = cs.getInt(3);

            postSubDocumentTracking(subDocuments, docTrackingId, email, con);

            con.commit();
        } catch (Exception e) {
            if (con != null)
                con.rollback();
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer postSubDocumentTracking(List<SubDocumentDto> subDocuments, int docTrackingId, String email, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        try {
            cs = con.prepareCall(POST_SUB_DOCUMENT_TRACKING_V2);
            for (SubDocumentDto doc : subDocuments) {
                cs.setString(1, doc.getDocumentNumber());
                cs.setString(2, doc.getBranch());
                cs.setInt(3, doc.getDocumentId());
                cs.setInt(4, docTrackingId);
                cs.setString(5, doc.getSignDay());
                cs.setString(6, doc.getReceiverPartner());
                cs.setString(7, email);
                cs.addBatch();
            }
            cs.executeBatch();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return 200;
    }

    public static void putSubDocumentTracking(List<SubDocumentDto> subDocuments, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        try {
            cs = con.prepareCall(PUT_SUB_DOCUMENT_TRACKING);
            for (SubDocumentDto doc : subDocuments) {
                cs.setInt(1, doc.getId());
                cs.setString(2, doc.getDocumentNumber());
                cs.setString(3, doc.getBranch());
                cs.setInt(4, doc.getDocumentId());
                cs.setString(5, doc.getSignDay());
                cs.setString(6, doc.getReceiverPartner());
                cs.addBatch();
            }
            cs.executeBatch();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
    }

    public static void deleteSubDocumentTracking(int docTrackingId, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        try {
            cs = con.prepareCall(DELETE_SUB_DOCUMENT_TRACKING);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, docTrackingId);

            cs.execute();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(null, null, cs, null);
        }
        if (exception != null)
            throw exception;
    }

    public static Integer putDocumentTracking(DocumentTrackingDto documentTrackingDto, List<SubDocumentDto> before, List<SubDocumentDto> after, String email) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nResult = 500;
        try {
            con = getConnection114();
            con.setAutoCommit(false);

            Set<Integer> idBefores = before.stream().map(SubDocumentDto::getId).collect(Collectors.toSet());
            Set<Integer> idAfters = after.stream().map(SubDocumentDto::getId).collect(Collectors.toSet());
            
            List<SubDocumentDto> listAdd = after.stream().filter(x -> !idBefores.contains(x.getId())).collect(Collectors.toList());
            
            for (SubDocumentDto subDocumentDto : before) {
                if (!idAfters.contains(subDocumentDto.getId())) 
                    deleteSubDocumentTracking(subDocumentDto.getId(), con);
            }

            postSubDocumentTracking(listAdd, documentTrackingDto.getN_id(), email, con);

            putSubDocumentTracking(after, con);

            cs = con.prepareCall(PUT_DOCUMENT_TRACKING);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, documentTrackingDto.getN_id());
            cs.setString(4, documentTrackingDto.getMerchantName());
            cs.setString(5, documentTrackingDto.getContractType());
            cs.setString(6, documentTrackingDto.getNotes());
            cs.setString(7, documentTrackingDto.getTaxCode());

            cs.execute();

            String error = cs.getString(2);
            nResult = cs.getInt(1);
            if (nResult != 200) {
                throw new Exception("DB PUT_DOCUMENT_TRACKING: " + error);
            }
            con.commit();
        } catch (Exception e) {
            if (con != null) con.rollback();
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

    public static Integer deleteDocumentTracking(int id, String typeDelete) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            cs = con.prepareCall(DELETE_DOCUMENT_TRACKING_V2);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setString(4, typeDelete);
            cs.setInt(5, id);

            cs.execute();
            String error = cs.getString(3);
            n_result = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (n_result != 200) {
                throw new Exception("DB DELETE_DOCUMENT_TRACKING: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer postStateDocumentTracking(int id, String state, String user, boolean isUpdateSub) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int n_result = 500;
        try {
            con = getConnection114();
            con.setAutoCommit(false);

            cs = con.prepareCall(POST_FINISH_STATE);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, id);
            cs.setString(4, state);
            cs.setString(5, user);

            cs.execute();
            String error = cs.getString(2);
            n_result = cs.getInt(1);
            if (n_result != 200) {
                throw new Exception("DB POST_FINISH_STATE: " + error);
            }

            if (isUpdateSub)
                updateSubDocumentTrackingId(id, con);

            con.commit();

        } catch (Exception e) {
            if (con != null)
                con.rollback();
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return n_result;
    }

    public static Integer updateSubDocumentTrackingId(int subDocId, Connection con) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        int nResult = 500;
        try {
            cs = con.prepareCall(UPDATE_SUB_DOCUMENT_TRACKING_ID);
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, subDocId);

            cs.execute();
            String error = cs.getString(2);
            nResult = cs.getInt(1);
            if (nResult != 200) {
                throw new Exception("DB POST_FINISH_STATE: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

    private static DocumentTrackingDto bindDocumentTrackingHN(ResultSet rs) throws SQLException {
        DocumentTrackingDto documentTrackingDto = new DocumentTrackingDto();
        documentTrackingDto.setN_id(rs.getInt("N_ID"));
        documentTrackingDto.setSubDocumentId(rs.getInt("SUB_DOCUMENT_ID"));
        documentTrackingDto.setMerchantName(rs.getString("MERCHANT_NAME"));
        documentTrackingDto.setContractType(rs.getString("CONTRACT_TYPE"));
        documentTrackingDto.setContractName(rs.getString("CONTRACT_NAME"));
        documentTrackingDto.setContractNumber(rs.getString("CONTRACT_NUMBER"));
        documentTrackingDto.setBranchName(rs.getString("BRANCH_NAME"));
        documentTrackingDto.setTaxCode(rs.getString("TAX_CODE"));
        documentTrackingDto.setSignDate(rs.getString("SIGN_DAY") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SIGN_DAY")));
        documentTrackingDto.setNotes(rs.getString("NOTES"));
        documentTrackingDto.setSubmission_date(rs.getString("SUBMISSION_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SUBMISSION_DATE")));
        documentTrackingDto.setSubmission_user(rs.getString("SUBMISSION_USER"));
        documentTrackingDto.setSendBank_date(rs.getString("SEND_BANK_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SEND_BANK_DATE")));
        documentTrackingDto.setSendBank_user(rs.getString("SEND_BANK_USER"));
        documentTrackingDto.setMs_accounting_date(rs.getString("MS_ACCOUNTING_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("MS_ACCOUNTING_DATE")));
        documentTrackingDto.setMs_accounting_user(rs.getString("MS_ACCOUNTING_USER"));
        documentTrackingDto.setSale_date(rs.getString("SALE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SALE_DATE")));
        documentTrackingDto.setSale_user(rs.getString("SALE_USER"));
        documentTrackingDto.setCreate_date(rs.getString("CREATE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("CREATE_DATE")));
        documentTrackingDto.setLocation(rs.getString("LOCATION"));
        documentTrackingDto.setTaxCode(rs.getString("TAX_CODE"));
        documentTrackingDto.setExportId(rs.getInt("EXPORT_ID"));
        documentTrackingDto.setGroupExport(rs.getInt("GROUP_EXPORT"));
        documentTrackingDto.setReceiverPartner(rs.getString("RECEIVER_PARTNER"));
        documentTrackingDto.setContractId(rs.getInt("CONTRACT_ID"));
        documentTrackingDto.setCreateUser(rs.getString("S_CREATE_USER"));
        return documentTrackingDto;
    }

    public static DocumentTrackingDto bindDocumentTrackingHCM(ResultSet rs) throws SQLException {
        DocumentTrackingDto documentTrackingDto = new DocumentTrackingDto();
        documentTrackingDto.setN_id(rs.getInt("N_ID"));
        documentTrackingDto.setSubDocumentId(rs.getInt("SUB_DOCUMENT_ID"));
        documentTrackingDto.setMerchantName(rs.getString("MERCHANT_NAME"));
        documentTrackingDto.setContractType(rs.getString("CONTRACT_TYPE"));
        documentTrackingDto.setContractName(rs.getString("CONTRACT_NAME"));
        documentTrackingDto.setContractNumber(rs.getString("CONTRACT_NUMBER"));
        documentTrackingDto.setBranchName(rs.getString("BRANCH_NAME"));
        documentTrackingDto.setTaxCode(rs.getString("TAX_CODE"));
        documentTrackingDto.setSignDate(rs.getString("SIGN_DAY") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SIGN_DAY")));
        documentTrackingDto.setNotes(rs.getString("NOTES"));
        documentTrackingDto.setSendHN_before_date(rs.getString("SEND_HN_BEFORE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SEND_HN_BEFORE_DATE")));
        documentTrackingDto.setSendHN_before_user(rs.getString("SEND_HN_BEFORE_USER"));
        documentTrackingDto.setSubmission_date(rs.getString("SUBMISSION_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SUBMISSION_DATE")));
        documentTrackingDto.setSubmission_user(rs.getString("SUBMISSION_USER"));
        documentTrackingDto.setSendBank_date(rs.getString("SEND_BANK_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SEND_BANK_DATE")));
        documentTrackingDto.setSendBank_user(rs.getString("SEND_BANK_USER"));
        documentTrackingDto.setSendHCM_date(rs.getString("SEND_HCM_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SEND_HCM_DATE")));
        documentTrackingDto.setSendHCM_user(rs.getString("SEND_HCM_USER"));
        documentTrackingDto.setHcmReceive_date(rs.getString("HCM_RECEIVE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("HCM_RECEIVE_DATE")));
        documentTrackingDto.setHcmReceive_user(rs.getString("HCM_RECEIVE_USER"));
        documentTrackingDto.setSendHN_after_date(rs.getString("SEND_HN_AFTER_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SEND_HN_AFTER_DATE")));
        documentTrackingDto.setSendHN_after_user(rs.getString("SEND_HN_AFTER_USER"));
        documentTrackingDto.setMs_accounting_date(rs.getString("MS_ACCOUNTING_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("MS_ACCOUNTING_DATE")));
        documentTrackingDto.setMs_accounting_user(rs.getString("MS_ACCOUNTING_USER"));
        documentTrackingDto.setSale_date(rs.getString("SALE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("SALE_DATE")));
        documentTrackingDto.setSale_user(rs.getString("SALE_USER"));
        documentTrackingDto.setCreate_date(rs.getString("CREATE_DATE") == null ? null : java.sql.Timestamp.valueOf(rs.getString("CREATE_DATE")));
        documentTrackingDto.setLocation(rs.getString("LOCATION"));
        documentTrackingDto.setTaxCode(rs.getString("TAX_CODE"));
        documentTrackingDto.setExportId(rs.getInt("EXPORT_ID"));
        documentTrackingDto.setGroupExport(rs.getInt("GROUP_EXPORT"));
        documentTrackingDto.setReceiverPartner(rs.getString("RECEIVER_PARTNER"));
        documentTrackingDto.setContractId(rs.getInt("CONTRACT_ID"));
        documentTrackingDto.setCreateUser(rs.getString("S_CREATE_USER"));
        return documentTrackingDto;
    }

    public static DocumentTrackingDto bindDocumentTracking(JsonObject rs) throws SQLException {
        DocumentTrackingDto documentTrackingDto = new DocumentTrackingDto();
        documentTrackingDto.setN_id(rs.getInteger("n_id"));
        documentTrackingDto.setMerchantName(rs.getString("merchantName"));
        documentTrackingDto.setContractType(rs.getString("contractType"));
        documentTrackingDto.setContractName(rs.getString("contractName"));
        documentTrackingDto.setContractNumber(rs.getString("contractNumber"));
        documentTrackingDto.setBranchName(rs.getString("branchName"));
        documentTrackingDto.setNotes(rs.getString("notes"));
        return documentTrackingDto;
    }

    public static List<DocumentTypeDto> getListDocumentType() throws Exception {
        Exception exception = null;
        List<DocumentTypeDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_TYPE);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_TRACKING_HN : " + error);
            } else {
                while (rs != null && rs.next()) {
                    DocumentTypeDto documentTypeDto = new DocumentTypeDto();
                    documentTypeDto.setId(rs.getInt("N_ID"));
                    documentTypeDto.setDocumentName(rs.getString("S_DOCUMENT_NAME"));
                    documentTypeDto.setType(rs.getString("S_TYPE"));
                    documentTypeDto.setOrder(rs.getInt("N_ORDER"));
                    result.add(documentTypeDto);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<SubDocumentDto> getSubDocumentsByTrackingID(int documentTrackingId) throws Exception {
        Exception exception = null;
        List<SubDocumentDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_SUB_DOCUMENT_BY_TRACKING_ID);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.setInt(4, documentTrackingId);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_TRACKING_HN : " + error);
            } else {
                while (rs != null && rs.next()) {
                    SubDocumentDto documentTypeDto = new SubDocumentDto();
                    documentTypeDto.setBranch(rs.getString("s_branch"));
                    documentTypeDto.setDocumentId(rs.getInt("n_document_id"));
                    documentTypeDto.setDocumentNumber(rs.getString("s_document_number"));
                    documentTypeDto.setSignDay(Util.formatDate(rs.getDate("d_sign_day"), "yyyy-MM-dd"));
                    documentTypeDto.setId(rs.getInt("n_id"));
                    documentTypeDto.setReceiverPartner(rs.getString("s_receiver_partner"));
                    result.add(documentTypeDto);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<ReceiverPartnerDto> getListReceiverPartner() throws Exception {
        Exception exception = null;
        List<ReceiverPartnerDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_RECEIVER_PARTNER);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_RECEIVER_PARTNER : " + error);
            } else {
                while (rs != null && rs.next()) {
                    ReceiverPartnerDto receiverPartnerDto = new ReceiverPartnerDto();
                    receiverPartnerDto.setId(rs.getInt("N_ID"));
                    receiverPartnerDto.setName(rs.getString("S_NAME"));
                    result.add(receiverPartnerDto);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static List<DocumentTypeDto> getListDocumentName() throws Exception {
        Exception exception = null;
        List<DocumentTypeDto> result = new ArrayList<>();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall(GET_DOCUMENT_NAME);
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.NUMBER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();
            String error = cs.getString(3);
            int nError = cs.getInt(2);
            rs = (ResultSet) cs.getObject(1);
            if (nError != 200) {
                throw new Exception("DB GET_DOCUMENT_NAME : " + error);
            } else {
                while (rs != null && rs.next()) {
                    DocumentTypeDto documentTypeDto = new DocumentTypeDto();
                    documentTypeDto.setDocumentName(rs.getString("S_DOCUMENT_NAME"));
                    result.add(documentTypeDto);
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return result;
    }

    public static boolean checkDocumentPermission(String email) throws Exception {
        boolean result = false;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call PKG_DOC_TRACKING#PERMISSION(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setString(3, email);
            cs.execute();
            String error = cs.getString(2);
            int nError = cs.getInt(1);
            if (nError == 200) {
                result = true;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }

    public static Integer updateDocumentNote(int docId, String note) throws Exception {
        Exception exception = null;
        CallableStatement cs = null;
        Connection con = null;
        ResultSet rs = null;
        int nResult = 500;
        try {
            con = getConnection114();
            cs = con.prepareCall("{call PKG_DOC_TRACKING#UPDATE_NOTE(?,?,?,?) }");
            cs.registerOutParameter(1, OracleTypes.NUMBER);
            cs.registerOutParameter(2, OracleTypes.VARCHAR);
            cs.setInt(3, docId);
            cs.setString(4, note);

            cs.execute();
            String error = cs.getString(2);
            nResult = cs.getInt(1);
            if (nResult != 200) {
                throw new Exception("updateDocumentNote error: " + error);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, null);
        }
        if (exception != null)
            throw exception;
        return nResult;
    }

}
