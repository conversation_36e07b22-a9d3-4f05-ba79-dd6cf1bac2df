package vn.onepay.portal.resources.bankAmountConfig;

import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.bankAmountConfig.dto.BankAmountConfigDto;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class BankAmountConfigDao extends Db {
    private static final Logger logger = Logger.getLogger(BankAmountConfigHandler.class.getName());

    public static List<BankAmountConfigDto> getBankAmountConfig() throws Exception {
        CallableStatement cs = null;
        ResultSet rs = null;
        Connection con = null;
        List<BankAmountConfigDto> result = new ArrayList<>();
        try {
            con = getConnection118Report();
            cs = con.prepareCall("{ call g_bank_amount_configs(?,?,?)}");
            cs.registerOutParameter(1, OracleTypes.CURSOR);
            cs.registerOutParameter(2, OracleTypes.INTEGER);
            cs.registerOutParameter(3, OracleTypes.VARCHAR);
            cs.execute();

            int nResult = cs.getInt(2);
            String sResult = cs.getString(3);
            rs = (ResultSet) cs.getObject(1);

            if (nResult != 200) {
                throw new Exception("DB get_banks: " + sResult);
            }
            BankAmountConfigDto dto;
            while (rs != null && rs.next()) {
                dto = new BankAmountConfigDto();
                dto.setId(Util.getColumnInteger(rs, "n_id"));
                dto.setName(Util.getColumnString(rs, "s_name"));
                dto.setCode(Util.getColumnString(rs, "S_CODE"));
                dto.setType(Util.getColumnString(rs, "s_type"));
                dto.setMin(Util.getColumnInteger(rs, "n_min"));
                dto.setMax(Util.getColumnInteger(rs, "n_max"));
                dto.setHotline(Util.getColumnString(rs, "s_hot_line"));
                result.add(dto);
            }

        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return result;
    }
}
