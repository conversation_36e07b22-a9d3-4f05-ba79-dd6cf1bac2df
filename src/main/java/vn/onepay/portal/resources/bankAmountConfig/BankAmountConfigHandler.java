package vn.onepay.portal.resources.bankAmountConfig;

import com.google.gson.Gson;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Util;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BankAmountConfigHandler {
    public static final Logger logger = Logger.getLogger(BankAmountConfigHandler.class.getName());

    private static final Gson gson = new Gson();

    public static void bankAmountConfigSearch(RoutingContext ctx) {
        ctx.vertx().executeBlocking(future -> {
            try {

                Util.sendResponse(ctx, 200, BankAmountConfigDao.getBankAmountConfig());
            } catch (Exception ex) {
                logger.log(Level.INFO, "get bank amount config error: ", ex);
                ctx.fail(ex);
            }
        }, false, null);
    }
}
