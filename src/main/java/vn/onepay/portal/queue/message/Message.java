package vn.onepay.portal.queue.message;

import javax.jms.Queue;
import java.io.Serializable;
import java.util.Map;

public class Message<T> implements Serializable {
    private T requestBody;
    private int priority;
    private Queue destinationQueue;
    private Queue forwardQueue;

    private String requestPath;
    private Map RequestData;

    private int resultCode;
    private String resultString;

    public Message() {
    }

    public Message(T requestBody, int priority, Queue destinationQueue, Queue forwardQueue, String requestPath, Map requestData) {
        this.requestBody = requestBody;
        this.priority = priority;
        this.destinationQueue = destinationQueue;
        this.forwardQueue = forwardQueue;
        this.requestPath = requestPath;
        this.RequestData = requestData;
    }

    public T getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(T requestBody) {
        this.requestBody = requestBody;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public Queue getDestinationQueue() {
        return destinationQueue;
    }

    public void setDestinationQueue(Queue destinationQueue) {
        this.destinationQueue = destinationQueue;
    }

    public Queue getForwardQueue() {
        return forwardQueue;
    }

    public void setForwardQueue(Queue forwardQueue) {
        this.forwardQueue = forwardQueue;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public Map getRequestData() {
        return RequestData;
    }

    public void setRequestData(Map requestData) {
        RequestData = requestData;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultString() {
        return resultString;
    }

    public void setResultString(String resultString) {
        this.resultString = resultString;
    }
}
