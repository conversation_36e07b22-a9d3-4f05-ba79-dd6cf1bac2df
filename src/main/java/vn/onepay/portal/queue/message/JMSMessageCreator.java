package vn.onepay.portal.queue.message;

import vn.onepay.portal.Config;

import javax.jms.Session;
import java.util.logging.Level;
import java.util.logging.Logger;

public class JMSMessageCreator {

    public static final int LOW_PRIORITY = javax.jms.Message.DEFAULT_PRIORITY - 1;
    public static final int MEDIUM_PRIORITY = javax.jms.Message.DEFAULT_PRIORITY;
    public static final int HIGN_PRIORITY = javax.jms.Message.DEFAULT_PRIORITY + 1;

    public static javax.jms.Message createMessage(Session session, Message message) {
        javax.jms.Message jmsMessage = null;
        try {
            jmsMessage = session.createObjectMessage(message);
            jmsMessage.setJMSPriority(message.getPriority());
            jmsMessage.setJMSDestination(message.getDestinationQueue());
            jmsMessage.setJMSReplyTo(message.getForwardQueue());
            jmsMessage.setJMSExpiration(Config.getQueueMessageExp());

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error", e);
        }
        return jmsMessage;
    }

    private static final Logger LOGGER = Logger.getLogger(JMSMessageCreator.class.getName());
}
