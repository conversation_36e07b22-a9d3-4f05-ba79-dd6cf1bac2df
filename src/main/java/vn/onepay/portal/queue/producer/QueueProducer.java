package vn.onepay.portal.queue.producer;

import vn.onepay.portal.queue.message.JMSMessageCreator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.queue.server.QueueServer;

import javax.jms.MessageProducer;
import javax.jms.Session;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


public class QueueProducer {
    private static Session session;

    private static Map<String, MessageProducer> mapProducer;

    public static void init(
            Session session,
            MessageProducer downloadQueueFastInProducer,
            MessageProducer downloadQueueFastOutProducer,
            MessageProducer downloadQueueSlowInProducer,
            MessageProducer downloadQueueSlowOutProducer,
            MessageProducer serviceQueueIn,
            MessageProducer serviceQueueOut) {
        try {
            mapProducer = new HashMap<>();
            mapProducer.put(QueueServer.getDownloadQueueFastIn().getQueueName(), downloadQueueFastInProducer);
            mapProducer.put(QueueServer.getDownloadQueueFastOut().getQueueName(), downloadQueueFastOutProducer);
            mapProducer.put(QueueServer.getDownloadQueueSlowIn().getQueueName(), downloadQueueSlowInProducer);
            mapProducer.put(QueueServer.getDownloadQueueSlowOut().getQueueName(), downloadQueueSlowOutProducer);
            mapProducer.put(QueueServer.getServiceQueueIn().getQueueName(), serviceQueueIn);
            mapProducer.put(QueueServer.getServiceQueueOut().getQueueName(), serviceQueueOut);
            QueueProducer.session = session;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Initial queue producer error: ", e);
        }
    }

    public static void sendMessage(Message message) {
        try {
            javax.jms.Message jmsMessage = JMSMessageCreator.createMessage(session, message);
            mapProducer.get(message.getDestinationQueue().getQueueName())
                    .send(message.getDestinationQueue(), jmsMessage);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Sending message to queue error", e);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(QueueProducer.class.getName());

}
