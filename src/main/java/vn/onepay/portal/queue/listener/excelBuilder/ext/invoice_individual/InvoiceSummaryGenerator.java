package vn.onepay.portal.queue.listener.excelBuilder.ext.invoice_individual;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.DateTimeUtil;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.invoice_individual.summary.InvoiceSummary;
import vn.onepay.portal.resources.invoice_individual.summary.InvoiceSummaryDao;
import vn.onepay.portal.utils.TemplateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InvoiceSummaryGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger LOGGER = Logger.getLogger(InvoiceSummaryGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            Map<String, Object> objectMap = InvoiceSummaryDao.getListSummaryByInvoiceIdAndService(mIn);
            List<InvoiceSummary> invoiceSummaries = (List<InvoiceSummary>) objectMap.get("list");
            this.generateRs(invoiceSummaries, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Invoice Individual Generator error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<InvoiceSummary> list, List<Map> listData) throws Exception {
        try {
            for (InvoiceSummary data : list) {
                Map item = new HashMap();
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, data.getRowNum());
                item.put(TemplateUtils.PARTNER_NAME_COLUMN, data.getPartnerName());
                item.put(TemplateUtils.TEN_DKKD_COLUMN, data.getTenDKKD());
                item.put(TemplateUtils.TEN_DV, data.getTenDV());
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());
                item.put(TemplateUtils.CONTRACT_CODE_COLUMN, data.getContractCode());
                item.put(TemplateUtils.MFR_ADDENDUM, data.getAddendum());
                item.put(TemplateUtils.MST, "");
                item.put(TemplateUtils.COUNT, data.getCount());
                item.put(TemplateUtils.AMOUNT_USD, data.getAmountUSD());
                item.put(TemplateUtils.AMOUNT_VND, data.getAmountVND());
                item.put(TemplateUtils.AMOUNT, data.getAmount());
                item.put(TemplateUtils.MFR_TOTAL_FEE, data.getFee());
                item.put(TemplateUtils.MFR_ECOM_FEE, data.getEcomFee());
                item.put(TemplateUtils.MFR_ITA_FEE, data.getItaFee());
                item.put(TemplateUtils.SERVICE, data.getService());
                item.put(TemplateUtils.FEE_VAT, data.getDiscountFee());
                Double totalFeeNotVAT = data.getFee() - data.getTax();
                item.put(TemplateUtils.TOTAL_FEE_NOT_VAT, Util.roundExcel(totalFeeNotVAT, 0));
                item.put(TemplateUtils.TOTAL_TAX, data.getTax());
                Date fromDate = DateTimeUtil.convertStringtoDate(data.getFromDate(), DateTimeUtil.DateTemplate.YYYY_MM_DD_HHmmss);
                Date toDate = DateTimeUtil.convertStringtoDate(data.getToDate(), DateTimeUtil.DateTemplate.YYYY_MM_DD_HHmmss);
                String dateFromStr = DateTimeUtil.convertDatetoString(fromDate, DateTimeUtil.DateTemplate.DD_MM_YYYY);
                String timeFromStr = DateTimeUtil.convertDatetoString(fromDate, DateTimeUtil.DateTemplate.HH_MM_SS);
                String dateToStr = DateTimeUtil.convertDatetoString(toDate, DateTimeUtil.DateTemplate.DD_MM_YYYY);
                String timeToStr = DateTimeUtil.convertDatetoString(toDate, DateTimeUtil.DateTemplate.HH_MM_SS);
                String detailPeriod = "Từ " + timeFromStr +
                        " ngày " + dateFromStr
                        + " đến " + timeToStr
                        + " ngày " + dateToStr;
                item.put(TemplateUtils.PERIOD_COLUMN, detailPeriod);
                item.put(TemplateUtils.DETAIL_COLUMN, data.getDetail());
                String settlementDateStr = "";
                if(data.getSettlement() != null){
                    Date settlementDate = DateTimeUtil.convertStringtoDate(data.getSettlement(), DateTimeUtil.DateTemplate.YYYY_MM_DD_HHmmss);
                    settlementDateStr = DateTimeUtil.convertDatetoString(settlementDate, DateTimeUtil.DateTemplate.DD_MM_YYYY);
                }
                item.put(TemplateUtils.SETTLEMENT_COLUMN, settlementDateStr);
                item.put(TemplateUtils.RECEIPT_NUMBER, data.getReceiptNumber());
                item.put(TemplateUtils.MISA_CODE_COLUMN, data.getMisaCode());
                item.put(TemplateUtils.DISCOUNT_FEE_COLUMN, data.getDiscountFee());
                item.put(TemplateUtils.MST, data.getMst());
                item.put(TemplateUtils.ADVANCE_ACCOUNT_COLUMN, data.getAdvanceAccount());
                item.put(TemplateUtils.MISA_MCC_COLUMN, data.getMisaMCC());
                String accountRevenue = "";
                if ("QT".equalsIgnoreCase(data.getService()) || "ITA".equalsIgnoreCase(data.getService())) {
                    accountRevenue = "511312";
                }
                if ("ND".equalsIgnoreCase(data.getService())) {
                    accountRevenue = "511321";
                }
                if ("QR".equalsIgnoreCase(data.getService())) {
                    accountRevenue = "511324";
                }
                if ("VIETQR".equalsIgnoreCase(data.getService())) {
                    accountRevenue = "511329";
                }
                item.put(TemplateUtils.ACCOUNT_REVENUE_COLUMN, accountRevenue);
                item.put(TemplateUtils.FEE_SUCCESS_COLUMN, data.getSuccessFee());
                item.put(TemplateUtils.MFR_FEE_FAILED, data.getFailedFee());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }
}
