package vn.onepay.portal.queue.listener.excelBuilder.ext.International;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.Status;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.installment.InstallmentDao;
import vn.onepay.portal.resources.international.installment.dto.InstallmentTransaction;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InternationalInstallmentGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);
            List<InstallmentTransaction> listOnline = InstallmentDao.searchTransaction(mIn);
            generateRs(listOnline, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<InstallmentTransaction> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(InstallmentTransaction data: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchant_id());

            // contract type
            item.put(TemplateUtils.CONTRACT_TYPE_COLUMN, data.getContract_type());

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, data.getId());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, data.getOrder_ref());

            // trans ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, data.getTrans_ref());

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, data.getCard_number());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, data.getCurrency());

            // amount
            item.put(TemplateUtils.AMOUNT_COLUMN, data.getAmount().getTotal());

            item.put("S_AUTH_CODE", data.getAuth_code());

            item.put(TemplateUtils.INSTALLMENT_BANK, data.getInstallment_bank());

            String installmentState = data.getInstallment_status();
            item.put(TemplateUtils.INSTALLMENT_STATE, Status.getStatus("installment", installmentState));

            item.put(TemplateUtils.INSTALLMENT_PERIOD, data.getInstallment_time());

            // date
            Date transactionTime = data.getDate() != null ? new Date(data.getDate().getTime()) : null;
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            item.put(TemplateUtils.ORIGINAL_DATE_COLUMN, data.getOriginalDate());

            item.put(TemplateUtils.CHANNEL_COLUMN, data.getChannel());
            item.put(TemplateUtils.REFERRAL_PARTNER_COLUMN, data.getReferralPartner());
            item.put(TemplateUtils.CARD_HOLDER, data.getCard_holder());
            item.put("D_ONEPAY_DATE", data.getOnepayApproval());
            item.put("D_BANK_SENT", data.getBankSend());
            item.put("D_BANK_DATE", data.getBankApprovalDate());
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(InternationalInstallmentGenerator.class.getName());
}
