package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.onepay.commons.util.Convert;
import java.text.SimpleDateFormat;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDto;

public class AdvanceBigMerchantGenerator implements BaseGenerator<Map> {
    private static final Logger LOGGER = Logger.getLogger(AdvanceBigMerchantGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            Map<String, Object> mOut = PaymentAdvanceDao.getAdvanceListBigMerchant(mIn);

            // sắp xếp lại
            List<AdvanceDto> advs = (List<AdvanceDto>) mOut.get("advanceList");
            // list bút toán theo các nhóm ngân hàng chuyển
            List<AdvanceDto> listAdvanceVcb = new ArrayList<>();
            List<AdvanceDto> listAdvanceTcbToTcb = new ArrayList<>();
            List<AdvanceDto> listAdvanceTcbToOther = new ArrayList<>();
            List<AdvanceDto> listAdvanceVtb = new ArrayList<>();
            List<AdvanceDto> listAdvanceVpb = new ArrayList<>();
            // total
            double totalAdvanceVcb = 0;
            double totalAdvanceTcbToTcb = 0;
            double totalAdvanceTcbToOther = 0;
            double totalAdvanceVtb = 0;
            double totalAdvanceVpb = 0;
            double tmpAmount = 0;
            double totalAdvance = 0;
            // stt bút toán
            int vcbIndex = 0;
            int tcbToTcbIndex = 0;
            int tcbToOtherIndex = 0;
            int vtbIndex = 0;
            int vpbIndex = 0;
            HashMap<String, Object> map = new HashMap<>();
            map.put("fromDate", Convert.toString(formatter.parse(mIn.get("from_date").toString()), "dd/MM/yyyy", ""));
            map.put("toDate", Convert.toString(formatter.parse(mIn.get("to_date").toString()), "dd/MM/yyyy", ""));
            for (AdvanceDto adv : advs) {
                String merchantIdsTrans = adv.getMerchantIds();
                List<String> distinctMerchantId = merchantIdsTrans != null
                        ? Arrays.asList(merchantIdsTrans.split(";,")).stream().distinct().collect(Collectors.toList())
                        : new ArrayList<>();
                String merchantIds = String.join(";",
                        distinctMerchantId.toArray(new String[distinctMerchantId.size()]));
                adv.setMerchantIds(merchantIds);
                tmpAmount = adv.getAdvanceAmountReal();// co the là N_ADVANCE_TOTAL
                adv.setAdvanceDate(adv.getAdvanceDate() != null
                        ? (Convert.toString(formatter.parse(adv.getAdvanceDate()), "dd-MM-yyyy", ""))
                        : "-");

                String strDesc = (adv.getDesc() != null ? adv.getDesc() + "" : "");
                adv.setAdvanceDescription(strDesc);

                if ("VIETCOMBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vcbIndex);
                    listAdvanceVcb.add(adv);
                    totalAdvanceVcb += tmpAmount;
                } else if ("TECHCOMBANK".equals(adv.getOnepayBankName()) && ("TECHCOMBANK".equals(adv.getBankName()))) {
                    adv.setIndex(++tcbToTcbIndex);
                    listAdvanceTcbToTcb.add(adv);
                    totalAdvanceTcbToTcb += tmpAmount;
                } else if ("TECHCOMBANK".equals(adv.getOnepayBankName())
                        && (!"TECHCOMBANK".equals(adv.getBankName()))) {
                    adv.setIndex(++tcbToOtherIndex);
                    listAdvanceTcbToOther.add(adv);
                    totalAdvanceTcbToOther += tmpAmount;
                } else if ("VIETINBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vtbIndex);
                    listAdvanceVtb.add(adv);
                    totalAdvanceVtb += tmpAmount;
                } else if ("VPBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vpbIndex);
                    listAdvanceVpb.add(adv);
                    totalAdvanceVpb += tmpAmount;
                }
            }
            totalAdvance = totalAdvanceVcb + totalAdvanceTcbToTcb + totalAdvanceTcbToOther + totalAdvanceVtb
                    + totalAdvanceVpb;
            // export
            Map<String, Object> beans = new HashMap<>();

            beans.put("listAdvanceVcb", listAdvanceVcb);
            beans.put("listAdvanceTcbToTcb", listAdvanceTcbToTcb);
            beans.put("listAdvanceTcbToOther", listAdvanceTcbToOther);
            beans.put("listAdvanceVtb", listAdvanceVtb);
            beans.put("listAdvanceVpb", listAdvanceVpb);

            map.put("totalAdvanceVcb", Math.round(totalAdvanceVcb));
            map.put("totalAdvanceTcbToTcb", Math.round(totalAdvanceTcbToTcb));
            map.put("totalAdvanceTcbToOther", Math.round(totalAdvanceTcbToOther));
            map.put("totalAdvanceTcb", Math.round(totalAdvanceTcbToTcb) + Math.round(totalAdvanceTcbToOther));
            map.put("totalAdvanceVtb", Math.round(totalAdvanceVtb));
            map.put("totalAdvanceVpb", Math.round(totalAdvanceVpb));
            map.put("totalAdvance", Math.round(totalAdvance));

            Calendar calOut = Calendar.getInstance();
            map.put("date_export", "Ngày " + calOut.get(Calendar.DATE) + " tháng " + (calOut.get(Calendar.MONTH) + 1)
                    + " năm " + calOut.get(Calendar.YEAR));
            map.put("date_from_to",
                    "Từ ngày " + Convert.toString(formatter.parse(mIn.get("from_date").toString()), "dd/MM/yyyy", "")
                            + " đến ngày "
                            + Convert.toString(formatter.parse(mIn.get("to_date").toString()), "dd/MM/yyyy", ""));
            beans.put("map", map);
            listMap.add(beans);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate ADVANCE error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }
}
