package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentPreAdvDetailDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.PrefixAdvanceDto;

public class PreAdvanceDetailGenerator implements BaseGenerator<Map> {
    private static final Logger LOGGER = Logger.getLogger(PreAdvanceDetailGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        try {
            Integer id = (Integer) mIn.get("id");
            PrefixAdvanceDto dataDto = PaymentPreAdvDetailDao.getAdvanceDetail(id);
            data.put("data", dataDto);
            listMap.add(data);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE TRANSACTION HISTORY error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }
}
