package vn.onepay.portal.queue.listener.excelBuilder.ext.shopify;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.shopify.dao.ShopifyDao;
import vn.onepay.portal.resources.shopify.dto.ShopifyTransaction;
import vn.onepay.portal.utils.TemplateUtils;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ShopifyDetailReportGenerator implements BaseGenerator<Map<String, Object>> {

    private static final SimpleDateFormat DEFAULT_DATE_TIME_FORMAT = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
  
    private synchronized String formatDate(Date date) {
        return DEFAULT_DATE_TIME_FORMAT.format(date);
    }
    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        List<ShopifyTransaction> listData = new ArrayList<>();
        try {
            List<ShopifyTransaction> resultOnly = ShopifyDao.downloadTransaction(mIn);
            listData.addAll(resultOnly);
            generateRs(listData, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<ShopifyTransaction> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (ShopifyTransaction data : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put(TemplateUtils.PARTNER_ID_COLUMN, data.getPartnerId());
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, data.getMerchantName());
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, data.getTransactionId());
            String date = formatDate(new Date(data.getTransDate().getTime()));
            item.put(TemplateUtils.TRANSACTION_DATE, date);
            item.put(TemplateUtils.CARD_NO_COLUMN, data.getCardNo());
            item.put(TemplateUtils.CARD_TYPE_COLUMN, data.getCardType());
            item.put(TemplateUtils.ORDER_INFO_COLUMN, data.getOrderRef());
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, data.getMerchTxnRef());
            item.put(TemplateUtils.AUTH_CODE_COLUMN, data.getAuthCode());
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, data.getTransType());
            item.put(TemplateUtils.CURRENCY_COLUMN, data.getCurrency());
            item.put(TemplateUtils.AMOUNT_COLUMN, data.getAmount());
            item.put(TemplateUtils.STATE_COLUMN, data.getState());
            item.put(TemplateUtils.MFR_BIN_COUNTRY, data.getBinCountry());
            item.put(TemplateUtils.MFR_BIN_BANK, data.getBinBank());
            item.put(TemplateUtils.MFR_ITA_BANK, data.getItaBank());
            item.put(TemplateUtils.ITA_TIME_COLUMN, data.getItaTime());
            item.put(TemplateUtils.MERCHANT_BANK_ID_COLUMN, data.getMerchantBankId());
            item.put(TemplateUtils.PLATFORM_COLUMN, data.getPlatform());
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ShopifyDetailReportGenerator.class.getName());
}
