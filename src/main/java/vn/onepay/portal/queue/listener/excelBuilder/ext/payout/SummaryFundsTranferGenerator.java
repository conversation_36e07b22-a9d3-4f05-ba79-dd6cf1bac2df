package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.payout.summary.SummaryFundsTranferDao;
import vn.onepay.portal.resources.payout.summary.dto.SummaryFundsTransDto;
import vn.onepay.portal.utils.TemplateUtils;

public class SummaryFundsTranferGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<SummaryFundsTransDto> fundsTransDtos = SummaryFundsTranferDao.search(mIn);
            this.generateRs(fundsTransDtos, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<SummaryFundsTransDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (SummaryFundsTransDto data : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("DATE_TYPE", data.getDateType());
                item.put("MERCHANT_ID", data.getMerchantId());
                item.put("MERCHANT_ACC", data.getAccountId());
                item.put("MERCHANT_NAME", data.getMerchantName());
                item.put("BANK_SENDER", data.getSenderSwiftCode());
                item.put("BANK_RECEIPT", data.getReceiptSwiftCode());
                item.put("NO_TRANFER", data.getCount());
                String state = data.getState().substring(0, 1).toUpperCase() + data.getState().substring(1).replaceAll("_"," ");
                item.put("STATE", state);
                item.put("TOTAL_TRANFER", data.getAmount());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(SummaryFundsTranferGenerator.class.getName());
}
