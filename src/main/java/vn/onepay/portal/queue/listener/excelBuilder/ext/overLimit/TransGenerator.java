package vn.onepay.portal.queue.listener.excelBuilder.ext.overLimit;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.overLimit.OverLimitDao;
import vn.onepay.portal.resources.overLimit.dto.TransOverLimitReq;
import vn.onepay.portal.utils.OneCreditUtil;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class TransGenerator implements BaseGenerator<TransOverLimitReq>, IConstants {
    private static final Logger LOGGER = Logger.getLogger(TransGenerator.class.getName());

    @Override
    public List<Map> generate(TransOverLimitReq mIn) {
        List<Map> listMap = new ArrayList<>();
        List<Map<String, Object>> listData = new ArrayList<>();
        try {
            listData = (List<Map<String, Object>>) OverLimitDao.searchTransaction(mIn);
            generateRs(listMap, listData, mIn.getDownloadType());
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[OVER LIMIT TRANS ERROR] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, List<Map<String, Object>> listData, String downloadType) throws Exception {
        int count = 1;
        for (Map map : listData) {
            map.put("no", count++);
            map.put("acquirer", map.get("acquirerName"));
            if ("csv".equalsIgnoreCase(downloadType)) {
                map.put("amount",
                        formatNumber(Double.parseDouble(map.get("amount").toString()), map.get("currency").toString()));
            } else {
                map.put("amount", Double.parseDouble(map.get("amount").toString()));
            }
            list.add(map);
        }

    }

    private String formatNumber(Double amount, String currency) {
        DecimalFormat formatter = new DecimalFormat("###,###,###");
        String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
        List<String> stringList = Arrays.asList(stringArray);
        if (stringList.contains(currency)) {
            formatter = new DecimalFormat("###,###,###0.00");
        }
        return formatter.format(amount);
    }
}
