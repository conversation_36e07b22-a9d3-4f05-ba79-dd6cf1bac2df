package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.merchant_account_report.PayoutMerchantAccDao;
import vn.onepay.portal.resources.payout.merchant_account_report.dto.MerchantAccBalanceDTO;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantAccBalanceGenerator implements BaseGenerator<Map<String, String>> {

    private static final Logger LOGGER = Logger.getLogger(MerchantAccBalanceGenerator.class.getName());
    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<MerchantAccBalanceDTO> dtoList = PayoutMerchantAccDao.search(mIn);
            this.generateRs(dtoList, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Account Balance error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<MerchantAccBalanceDTO> list, List<Map> listData) {
        int rowNumber = listData.size();
        try {
            Map item ;
            for (MerchantAccBalanceDTO itemReport : list) {
                item= new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("PARTNER_ID", itemReport.getPartner_name());
                item.put("MERCHANT_NAME", itemReport.getMerchant_name());
                item.put("MERCHANT_ID", itemReport.getMerchant_id());
                item.put("MERCHANT_ACC", itemReport.getMerchant_acc());
                item.put("BALANCE", Util.formatCurrencyDouble(itemReport.getBalance()));
                item.put(TemplateUtils.CREATE, itemReport.getCreated_date() == null ? "" : Util.formatDate(new Date(itemReport.getCreated_date().getTime()), "dd-MM-yyyy hh:mm a"));
                item.put(TemplateUtils.UPDATE, itemReport.getUpdated_date() == null ? "" : Util.formatDate(new Date(itemReport.getUpdated_date().getTime()), "dd-MM-yyyy hh:mm a"));
                item.put("ACCOUNT_STATE", itemReport.getAccount_state());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }
}
