package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.bank_fee.BankFeeDao;
import vn.onepay.portal.resources.payout.bank_fee.dto.BankFeeDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BankFeeGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<BankFeeDto> bankFeeDtos = BankFeeDao.search(mIn).getList();
            this.generateRs(bankFeeDtos, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Bank Fee error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<BankFeeDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        String format = "dd-MM-yyyy hh:mm a";
        try {
            for (BankFeeDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

                // Id
                item.put(TemplateUtils.ID, itemReport.getId());

                item.put(TemplateUtils.SWIFT_CODE, itemReport.getSwiftCode());

                item.put(TemplateUtils.CURRENCY, itemReport.getCurrency());

                item.put(TemplateUtils.BANK_TRANS_REF, itemReport.getBankTransId());

                item.put(TemplateUtils.DESC, itemReport.getDesc());

                // bank Name
                item.put(TemplateUtils.NAME, itemReport.getBankName());

                item.put(TemplateUtils.TYPE, capitalizeString(itemReport.getType(), ""));
                // balance before
                item.put(TemplateUtils.BEFORE_BALANCE, Util.formatCurrencyDouble(itemReport.getBalanceBefore()));

                // balance After
                item.put(TemplateUtils.AFTER_BALANCE, Util.formatCurrencyDouble(itemReport.getBalanceAfter()));

                // amount
                item.put(TemplateUtils.AMOUNT, Util.formatCurrencyDouble(itemReport.getAmount()));

                item.put(TemplateUtils.STATE, Util.capitalizeString(itemReport.getState(), ""));

                // created date
                item.put(TemplateUtils.CREATE, itemReport.getCreatedDate() == null ? "" : Util.formatDate(new Date(itemReport.getCreatedDate().getTime()), format));

                item.put(TemplateUtils.UPDATE, itemReport.getUpdatedDate() == null ? "" : Util.formatDate(new Date(itemReport.getUpdatedDate().getTime()), format));

                item.put(TemplateUtils.D_FUND, itemReport.getFundDate() == null ? "" : Util.formatDate(new Date(itemReport.getFundDate().getTime()), format));

                item.put(TemplateUtils.D_BANK, itemReport.getBankDate() == null ? "" : Util.formatDate(new Date(itemReport.getBankDate().getTime()), format));

                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
    }

    private String capitalizeString(String inputData, String defaultData) {
        String outputData = defaultData;
        if (!inputData.isEmpty()) {
            boolean foundSpace = true;
            char[] charArray = inputData.replaceAll("_", " ").toCharArray();
            for (int i = 0; i < charArray.length; i++) {
                if (Character.isLetter(charArray[i])) {
                    if (foundSpace) {
                        charArray[i] = Character.toUpperCase(charArray[i]);
                        foundSpace = false;
                    }
                } else {
                    foundSpace = true;
                }
            }
            outputData = String.valueOf(charArray);
        }
        return outputData;
    }

    private static final Logger LOGGER = Logger.getLogger(BankFeeGenerator.class.getName());
}
