package vn.onepay.portal.queue.listener.excelBuilder.ext.accountantManagement;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.ExportReceiptDAO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptDTO;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportReceiptGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(ExportReceiptGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ExportReceiptDTO> exportReceipts = ExportReceiptDAO.exportReceiptDownload(mIn);
            generateRs(exportReceipts, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate Export Receipt error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<ExportReceiptDTO> exportReceipts, List<Map> listMap) {
        Map mapData;
        int index = 1;
        for (ExportReceiptDTO item : exportReceipts) {
            mapData = new HashMap();
            mapData.put("NO", index++);
            mapData.put("N_PAYMENT_TYPE", item.getPaymentType());
            mapData.put("N_PAYMENT_METHOD", item.getPaymentMethod());
            mapData.put("N_DELIVERY_BILL", item.getDeliveryBill());
            mapData.put("N_WITH_RECEIPT", item.getWithReceipt());
            mapData.put("D_SETTLEMENT", item.getSettleDate());
            mapData.put("D_DOCUMENT", item.getDateDocument());
            mapData.put("S_RECEIPT_NUMBER", item.getReceiptNumber());
            mapData.put("D_RECEIPT", item.getDateReceipt());
            mapData.put("S_MISA_CODE", item.getMisaCode());
            mapData.put("S_DETAIL", item.getDetail());
            mapData.put("S_MISA_MCC", item.getMisaMCC());
            mapData.put("S_ACCOUNT_PAYMENT", item.getAccountPayment());
            mapData.put("S_ACCOUNT_SALE", item.getAccountSale());
            mapData.put("N_COUNT", item.getnCount());
            mapData.put("N_PRICE", item.geteCom());
            mapData.put("S_ACCOUNT_DISCOUNT", item.getAccountDiscount());
            mapData.put("N_VAT", item.getVat());
            mapData.put("N_TAX", item.geteComVat());
            mapData.put("S_ACCOUNT_TAX", item.getAccountTax());

            listMap.add(mapData);
        }
    }
}
