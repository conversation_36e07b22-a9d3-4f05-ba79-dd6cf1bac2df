package vn.onepay.portal.queue.listener.excelBuilder.ext.onoffBanks;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.on_off_banks.OnOffBankDao;
import vn.onepay.portal.resources.on_off_banks.dto.OnOffBankApproval;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class OnOffBanksGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(OnOffBanksGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<OnOffBankApproval> onOffBankApprovals = OnOffBankDao.approvalHistoryFile(mIn);
            generateRs(onOffBankApprovals, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate On Off Bank Approval History Error ", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<OnOffBankApproval> onOffBankApprovals, List<Map> listMap) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
        Map mapData;
        int index = 1;
        for (OnOffBankApproval item : onOffBankApprovals) {
            mapData = new HashMap();
            mapData.put("NO", index++);
            mapData.put("BANK_NAME", item.getBankName());
            mapData.put("BANK_ID", item.getData().getBankId());
            mapData.put("STATE", ("enable".equals(item.getData().getState()) ? "Enable" : ("disable".equals(item.getData().getState()) ? "Disable" : "")));
            mapData.put("DATE", sdf.format(item.getCreateDate()));
            mapData.put("REQUEST_USER", item.getRequestUser());
            mapData.put("APPROVER", item.getApproveUser());
            mapData.put("APPROVE_DATE", sdf.format(item.getApproveDate()));
            mapData.put("NOTE", item.getData().getNote());
            mapData.put("ACTION_TYPE", ("enable".equals(item.getData().getState()) ? "Disable" : ("disable".equals(item.getData().getState()) ? "Enable" : "")));

            listMap.add(mapData);
        }
    }
}
