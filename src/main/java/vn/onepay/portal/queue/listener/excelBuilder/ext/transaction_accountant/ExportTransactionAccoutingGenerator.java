package vn.onepay.portal.queue.listener.excelBuilder.ext.transaction_accountant;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2_transaction_accounting.Payment2TransactionAccountingDao;
import vn.onepay.portal.utils.OneCreditUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
public class ExportTransactionAccoutingGenerator implements BaseGenerator<Map<String, Object>> {
    private static final Logger LOGGER = Logger.getLogger(ExportTransactionAccoutingGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        List<Map> listData = new ArrayList<>();
        try {
            mIn.put("page_size",Integer.MAX_VALUE);
            mIn.put("page_active",0);
            listData = (List<Map>) Payment2TransactionAccountingDao.list(mIn).get("data");
            generateRs(listMap,listData);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[REPORT FULL LINK] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, List<Map> listData) throws Exception {
        int count = 1;
        for (Map map : listData) {
            map.put("count", count++);
            map.put("transactionId", Integer.parseInt(map.get("transactionId").toString()));
            map.put("acquirer", OneCreditUtil.convertAcq(map.get("acquirer").toString()));
            map.put("amount", Double.parseDouble(map.get("amount").toString()));
            Date date = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss a").parse(map.get("dateDownload").toString());
            map.put("date", date);
            list.add(map);
        }

    }

}
