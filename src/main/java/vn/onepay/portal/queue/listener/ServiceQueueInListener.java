package vn.onepay.portal.queue.listener;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.ReportFileBuilder;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.utils.RoutePool;
import vn.onepay.portal.Config;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import java.io.File;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ServiceQueueInListener extends BaseDownloadQueueInListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
        vn.onepay.portal.queue.message.Message messageData = null;
        try {
            LOGGER.log(Level.INFO,
                    "========================== BEGIN ServiceQueueInListener ==========================");
            messageData = (vn.onepay.portal.queue.message.Message) ((ObjectMessage) message).getObject();
            messageData.setDestinationQueue(messageData.getForwardQueue());

            if (messageData.getRequestPath().equals(RoutePool.ACCOUNTANT_EXPORT_STATEMENT_FILE)) {
                ReportFileBuilder.generateAccountantStmFile(messageData);
            }

            if (messageData.getRequestPath().equals(RoutePool.ACCOUNTANT_EXPORT_IB_FILE)) {
                ReportFileBuilder.generateAccountantIBFile(messageData);
            }

            if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_PDF)) {
                ReportFileBuilder.generateDownloadAdvPdfMultiple(messageData);
            }

            if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_EXCEL)) {
                ReportFileBuilder.generateDownloadAdvExcelMultiple(messageData);
            } else if (messageData.getRequestPath()
                    .equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_PAYMENT_EXPORT)) {
                ReportFileBuilder.generateDownloadFDWordMultiple(messageData);
            }

            if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_DETAIL_TRANS_EXCEL)) {
                ReportFileBuilder.generateDownloadAdvDetailTransExcelMultiple(messageData);
            }

            if (messageData.getRequestPath()
                    .equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_MULTIPLE_FILES)) {
                ReportFileBuilder.generateDownloadMultipleDisputeFiles(messageData);
            }
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "ServiceQueueInListener ERROR : ", ex);
            ex.printStackTrace();
        } finally {
            QueueProducer.sendMessage(messageData);
        }
    }

    @Override
    void writeFile(vn.onepay.portal.queue.message.Message messageData) throws Exception {
        LOGGER.log(Level.INFO, "WRITE FILE NAME: " + this.fileNameHash);
        File file = new File(this.fileNameHash);
        long fileSize = file.length();
        messageData.getRequestData().put(IConstants.FILE_SIZE, fileSize);
        messageData.setResultCode(200);
        messageData.setResultString("File Has been generated");
    }

    @Override
    String fileName(String ext) {
        return this.fileName + ext;
    }

    private static final Logger LOGGER = Logger.getLogger(ServiceQueueInListener.class.getName());
}
