package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.FixDepositPaymentDao;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.dto.FixDepositPaymentDto;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.dto.FixDepositQueryDto;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FDPaymentGenerator implements BaseGenerator<FixDepositQueryDto> {

    @Override
    public List<Map> generate(FixDepositQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<FixDepositPaymentDto> list = FixDepositPaymentDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit payment Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FixDepositPaymentDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        for (FixDepositPaymentDto itemReport : list) {

            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("id", itemReport.getId());
            item.put("partner_name", itemReport.getPartner_name());
            item.put("partner_id", itemReport.getPartner_id());
            item.put("business_reg_name", itemReport.getBusiness_reg_name());
            item.put("merchant_ids", itemReport.getMerchant_ids());
            item.put("tax_code", itemReport.getTax_code());
            item.put("contract_code", itemReport.getContract_code());
            item.put("addenum", itemReport.getAddenum());
            item.put("signed_date", itemReport.getSigned_date());
            item.put("currency", itemReport.getCurrency());
            item.put("guarantee_destination", itemReport.getGuarantee_destination());
            item.put("balance", itemReport.getBalance());
            item.put("balance1", itemReport.getGuarantee_destination() - itemReport.getBalance());
            item.put("start_date_fd", itemReport.getStart_date_fd());
            item.put("end_date_fd", itemReport.getEnd_date_fd());
            item.put("approval", itemReport.getApproval());
            item.put("amount_unhold", itemReport.getAmount_unhold());
            item.put("date_unhold", itemReport.getDate_unhold());
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FDPaymentGenerator.class.getName());
}
