package vn.onepay.portal.queue.listener.excelBuilder.ext.shopify;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.shopify.dao.ShopifyDao;
import vn.onepay.portal.resources.shopify.dto.ShopifySumary;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ShopifySummaryReportGenerator implements BaseGenerator<Map<String, Object>> {

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        List<ShopifySumary> listData = new ArrayList<>();
        try {
            List<ShopifySumary> resultOnly = ShopifyDao.downloadSummary(mIn);
            listData.addAll(resultOnly);
            generateRs(listData, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<ShopifySumary> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (ShopifySumary data : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put(TemplateUtils.PARTNER_ID_COLUMN, data.getPartnerId());
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, data.getMerchantName());
            item.put("MONTH", data.getMonth());
            item.put("YEAR", data.getYear());
            item.put("COUNT_PURCHASE", data.getCountPurchase());
            item.put("COUNT_REFUND", data.getCountRefund());
            item.put("COUNT_TOTAL", data.getCountTotal());
            item.put("AMOUNT_PURCHASE", data.getAmountPurchase());
            item.put("AMOUNT_REFUND", data.getAmountRefund());
            item.put("AMOUNT_TRANS", data.getAmountTrans());
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ShopifySummaryReportGenerator.class.getName());
}
