package vn.onepay.portal.queue.listener.excelBuilder.ext.International;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.Status;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.transaction.InternationalTransactionDao;
import vn.onepay.portal.resources.international.transaction.dto.InternationalTransaction;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InternationalTransactionalGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        List<InternationalTransaction> listData = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);
            List<InternationalTransaction> listOnline = InternationalTransactionDao.searchTransaction( mIn, -1);
            listData.addAll(listOnline);

            mIn.put(IConstants.OFFSET, listMap.size());
            List<InternationalTransaction> listReadOnly = InternationalTransactionDao.searchTransaction(mIn, listMap.size());
            listData.addAll(listReadOnly);
            generateRs(listData, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<InternationalTransaction> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(InternationalTransaction data: list) {
            Map item = new HashMap();
            rowNumber++; 
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchant_id());

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, data.getTranaction_id());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, data.getOrder_ref());

            // trans ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, data.getTrans_ref());

            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, data.getCard_number());

            // Card type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, data.getCard_type());

            // trans type
            item.put(TemplateUtils.TRANS_TYPE_COLUMN, data.getTrans_type());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, data.getCurrency());

            // amount
            // item.put(TemplateUtils.AMOUNT_COLUMN, Util.formatCurrencyAmount(data.getCurrency(),data.getAmount().getTotal()));

            // item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, Util.formatCurrencyAmount(data.getCurrency(),data.getAmount().getOriginalTotal()));
            item.put(TemplateUtils.AMOUNT_COLUMN, data.getAmount().getTotal());
            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, data.getAmount().getOriginalTotal());

            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, data.getAmount().getRefund_total());

            item.put(TemplateUtils.INSTALLMENT_BANK, data.getInstallment_bank());

            String installmentState = data.getInstallment_status();
            item.put(TemplateUtils.INSTALLMENT_STATE, Status.getStatus("installment", installmentState));

            item.put(TemplateUtils.INSTALLMENT_PERIOD, data.getInstallment_time());
            Date installmentTime = data.getInstallment_date() != null ? new Date(data.getInstallment_date().getTime()) : null;
            item.put(TemplateUtils.INSTALLMENT_DATE, installmentTime);

            item.put(TemplateUtils.INSTALLMENT_AMOUNT, data.getInstallment_amount());

            // date
            Date transactionTime = data.getDate() != null ? new Date(data.getDate().getTime()) : null;
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            item.put(TemplateUtils.ORIGINAL_DATE_COLUMN, data.getOriginalDate());

            // ontract Type
            item.put(TemplateUtils.CONTRACT_TYPE_COLUMN, data.getContract_type());

            // response code
            String responseCode = (data.getResponse() == null) ? "" : data.getResponse();
            String responseString;
            if (responseCode.equals("")) {
                responseString = "Blank";
            } else {
                if (Status.getStatus("international", responseCode).equals("")) {
                    responseString = "";
                } else {
                    responseString = responseCode + " - " + Status.getStatus("international", responseCode);
                }
            }
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, responseString);

            // Transaction State
            String transactionState ="Failed";
            if (responseCode == null) {
                transactionState = "Pending";
            } else if (responseCode.length() >= 1 && responseCode.trim().substring(0,1).equals("0")) {
                transactionState = "Successful";
            }
            item.put(TemplateUtils.STATUS_COLUMN, transactionState);
            item.put("S_ACQUIRER_NAME", data.getAcquirerName());

            // authorization code
            item.put(TemplateUtils.AUTH_CODE_COLUMN, data.getAuth_code() ==null ? "" : data.getAuth_code());


            item.put(TemplateUtils.S_NOTE, data.getNote() == null ? "" : data.getNote());

            //Apple Pay
            item.put(TemplateUtils.S_SOURCE, data.getSource() == null ? "" : data.getSource());
            item.put(TemplateUtils.S_TOKEN_NUMBER, data.getTokenNumber() == null ? "" : data.getTokenNumber());
            item.put(TemplateUtils.S_NETWORK_TRANS_ID, data.getNetworkTransId() == null ? "" : data.getNetworkTransId());


            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(InternationalTransactionalGenerator.class.getName());
}
