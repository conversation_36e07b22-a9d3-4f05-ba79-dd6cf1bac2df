package vn.onepay.portal.queue.listener.excelBuilder;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Map.Entry;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.onepay.portal.Util;

public class CreateHugeXLSFile {
    private static final Logger LOGGER = LoggerFactory.getLogger(CreateHugeXLSFile.class);

    private static final int ROW_TITLE = 3;
    private static final int ROW_COL_HEADER = 6;
    private static final String DEFAULT_FONT = "Times New Roman";

    private String fileName;
    private String fromDate;
    private String toDate;
    private String title;
    private List<String> headers;
    private List<Map<String, Object>> datas;

    public CreateHugeXLSFile(String fileName, String title, String fromDate, String todate, List<String> headers, List<Map<String, Object>> datas) {
        this.fileName = fileName;
        this.title = title;
        this.fromDate = fromDate;
        this.toDate = todate;
        this.headers = headers;
        this.datas = datas;
    }

    public CellStyle setTitleStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 18);
        font.setFontName(DEFAULT_FONT);
        font.setBold(true);
        cs.setFont(font);
        cs.setAlignment(HorizontalAlignment.CENTER);
        return cs;
    }

    public CellStyle setColHeaderStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 9);
        font.setFontName(DEFAULT_FONT);
        font.setBold(true);
        cs.setFont(font);
        cs.setFillForegroundColor((short) 0);
        cs.setAlignment(HorizontalAlignment.CENTER);
        return cs;
    }

    public CellStyle setDataStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName(DEFAULT_FONT);
        cs.setFont(font);
        cs.setAlignment(HorizontalAlignment.CENTER);
        return cs;
    }

    public CellStyle setFromToStyle(SXSSFWorkbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font = wb.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setFontName(DEFAULT_FONT);
        font.setBold(true);
        cs.setFont(font);
        cs.setAlignment(HorizontalAlignment.CENTER);
        return cs;
    }

    public void write() {
        LOGGER.info("Start writing {}", fileName);
        FileInputStream inputStream = null;
        XSSFWorkbook wbTemplate = null;
        SXSSFWorkbook wb = null;
        FileOutputStream out = null;
        try {
            inputStream = new FileInputStream("src/main/resources/template/test_template.xlsx");

            wbTemplate = new XSSFWorkbook(inputStream);

            wb = new SXSSFWorkbook(wbTemplate);
            wb.setCompressTempFiles(true);
            SXSSFSheet sh = wb.getSheetAt(0);
            sh.setDefaultColumnWidth(25);
            sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk

            createExcelTitle(sh, setTitleStyle(wb));
            createExcelColHeader(sh, setColHeaderStyle(wb));

            out = new FileOutputStream("src/main/resources/template/" + fileName);
            wb.write(out);
            LOGGER.info("Writing {} successfull", fileName);
        } catch (Exception e) {
            LOGGER.error("Writing {} fail", fileName, e);
        } finally {
            Util.closeResourse(LOGGER, inputStream, wbTemplate, wb, out);
        }
    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    private void createExcelColHeader(SXSSFSheet sh, CellStyle cs) {
        if (Objects.isNull(headers))
            return;
        Row row = sh.createRow(ROW_COL_HEADER);
        for (int i = 0; i < headers.size(); i++) {
            String celValue = Objects.toString(headers.get(i), "");
            Cell cell = row.createCell(i);
            cell.setCellValue(StringUtils.upperCase(celValue));
            cell.setCellStyle(cs);
        }
    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    private void createExcelTitle(SXSSFSheet sh, CellStyle cs) {
        Row row = sh.createRow(ROW_TITLE);
        title = Objects.toString(title, "");
        Cell cell = row.createCell(0);
        cell.setCellValue(StringUtils.upperCase(title));
        cell.setCellStyle(cs);
    }

    /**
     * <pre>
     * ghi value theo <code>${key}</code> cua <code>entry</code>. 
     * VD: <code>${today}</code>
     * </pre>
     * 
     * <AUTHOR>
     */
    public static void createExcelDetail(XSSFSheet templateSheet, Entry<String, Object> entry) {
        Cell fc = findCellKeyDetail(templateSheet, entry.getKey());
        if (fc != null)
            writeCell(fc, entry.getValue());
    }

    /**
     * <pre>
     * ghi value theo <code>${key.subkey}</code> voi subkey la key cua <code>detail</code>. 
     * VD: <code>${header.today}</code>
     * </pre>
     * 
     * <AUTHOR> thangdv
     */
    public static void createExcelDetail(XSSFSheet templateSheet, Map<String, Object> detail, String key) {
        for (Entry<String, Object> entry : detail.entrySet()) {
            Cell fc = findCellKeyDetail(templateSheet, key + "." + entry.getKey());
            if (fc == null)
                continue;
            writeCell(fc, entry.getValue());
        }
    }

    public static void deleteExcelRow(XSSFSheet templateSheet, List<String> key, int startRow) {
        Row templateRow = templateSheet.getRow(startRow);
        for (String column : key) {
            Integer cellIndex = findCollumnIndexOfKey(column, templateRow);
            if (cellIndex < 0)
                continue;
            deletecell(cellIndex, templateSheet, startRow);
        }
    }

    private static void deletecell(int columnToDelete, XSSFSheet sheet, int startRow) {
        for (int rId = startRow - 1; rId <= sheet.getLastRowNum(); rId++) {
            Row row = sheet.getRow(rId);
            for (int cID = columnToDelete; cID < row.getLastCellNum(); cID++) {
                Cell cOld = row.getCell(cID);
                if (cOld != null) {
                    row.removeCell(cOld);
                }
                Cell cNext = row.getCell(cID + 1);
                if (cNext != null) {
                    Cell cNew = row.createCell(cID, cNext.getCellType());
                    cloneCell(cNew, cNext);
                    sheet.setColumnWidth(cID, sheet.getColumnWidth(cID + 1));
                }
            }
        }
    }

    private static void cloneCell(Cell cNew, Cell cOld) {
        cNew.setCellComment(cOld.getCellComment());
        cNew.setCellStyle(cOld.getCellStyle());

        switch (cNew.getCellType()) {
            case Cell.CELL_TYPE_BOOLEAN: {
                cNew.setCellValue(cOld.getBooleanCellValue());
                break;
            }
            case Cell.CELL_TYPE_NUMERIC: {
                cNew.setCellValue(cOld.getNumericCellValue());
                break;
            }
            case Cell.CELL_TYPE_STRING: {
                cNew.setCellValue(cOld.getStringCellValue());
                break;
            }
            case Cell.CELL_TYPE_ERROR: {
                cNew.setCellValue(cOld.getErrorCellValue());
                break;
            }
            case Cell.CELL_TYPE_FORMULA: {
                cNew.setCellFormula(cOld.getCellFormula());
                break;
            }
        }

    }

    /**
     * Tao map column index va style cua tung cell trong template row
     * 
     * <AUTHOR>
     * @param wb any workbook for create cell style
     * @param templateRow template row
     */
    public static Map<Integer, CellStyle> generateCollumnStyleIndexMap(Workbook wb, Row templateRow) {
        Map<Integer, CellStyle> collumnStyleIndexMap = new HashMap<>();

        int i = 0;
        for (Cell cell : templateRow) {
            CellStyle temp = wb.createCellStyle();
            temp.cloneStyleFrom(cell.getCellStyle());
            collumnStyleIndexMap.put(i, temp);
            i++;
        }
        return collumnStyleIndexMap;
    }

    /**
     * Tao map key va vi tri cot cua no trong excel
     * 
     * <AUTHOR>
     * @param templateRow template row
     * @param keys template key
     */
    public static Map<String, Integer> generateKeyCollumnIndexMap(Row templateRow, Set<String> keys) {
        Map<String, Integer> keyCollumnIndexMap = new HashMap<>();

        for (String key : keys) {
            if (keyCollumnIndexMap.get(key) == null) {
                // tim vi tri cot cua key tuong ung trong excel
                Integer collumnI = findCollumnIndexOfKey(key, templateRow);
                if (collumnI != -1) {
                    // row.getCell
                    keyCollumnIndexMap.put(key, collumnI);
                }
            }
        }
        return keyCollumnIndexMap;
    }

    /**
     * ghi column header
     * 
     * <AUTHOR>
     * @param sh
     * @return
     */
    public static void createExcelData(Workbook wb, SXSSFSheet sh, XSSFSheet templateSheet, List<Map<String, Object>> datas, Integer startRow) {
        // Copy style cua template row xong xoa
        Row templateRow = templateSheet.getRow(startRow);
        Map<Integer, CellStyle> collumnStyleIndexMap = generateCollumnStyleIndexMap(wb, templateRow);
        if (datas.isEmpty()) {
            templateSheet.removeRow(templateRow);
            return;
        }

        Map<String, Integer> keyCollumnIndexMap = generateKeyCollumnIndexMap(templateRow, datas.get(0).keySet());

        templateSheet.removeRow(templateRow);

        for (int i = 0; i < datas.size(); i++) {
            Map<String, Object> bean = datas.get(i);

            SXSSFRow row = sh.getRow(startRow + i) == null ? sh.createRow(startRow + i) : sh.getRow(startRow + i);

            for (Map.Entry<String, Object> beanKey : bean.entrySet()) {
                Integer column = keyCollumnIndexMap.get(beanKey.getKey());
                if (column == null) continue;
                SXSSFCell cell = row.createCell(column);
                writeCell(cell, beanKey.getValue());
                // set style
                CellStyle temp = collumnStyleIndexMap.get(getCollumnIndexByKey(keyCollumnIndexMap, beanKey.getKey()));
                if (temp != null)
                    cell.setCellStyle(temp);
            }
        }
    }

    public static void createExcelDataFomatCurrency(Workbook wb, SXSSFSheet sh, XSSFSheet templateSheet, List<Map<String, Object>> datas, Integer startRow, List<String> columnFormat, List<String> currencyFormat, String columnCurrency) {
        // Copy style cua template row xong xoa
        Row templateRow = templateSheet.getRow(startRow);
        Map<Integer, CellStyle> collumnStyleIndexMap = generateCollumnStyleIndexMap(wb, templateRow);
        if (datas.isEmpty()) {
            templateSheet.removeRow(templateRow);
            return;
        }
        Map<String, Integer> keyCollumnIndexMap = generateKeyCollumnIndexMap(templateRow, datas.get(0).keySet());
        templateSheet.removeRow(templateRow);
    
        CellStyle newStyleVnd = wb.createCellStyle();
        CellStyle newStyle = wb.createCellStyle();
        SXSSFRow row;
        for (int i = 0; i < datas.size(); i++) {
            Map<String, Object> bean = datas.get(i);

            row = sh.getRow(startRow + i) == null ? sh.createRow(startRow + i) : sh.getRow(startRow + i);
            SXSSFCell cell;
            for (Map.Entry<String, Object> beanKey : bean.entrySet()) {
                Integer column = keyCollumnIndexMap.get(beanKey.getKey());
                if (column == null)
                    continue;
                cell = row.createCell(column);
                writeCell(cell, beanKey.getValue());
                // set style
                CellStyle temp = collumnStyleIndexMap.get(getCollumnIndexByKey(keyCollumnIndexMap, beanKey.getKey()));
                if (temp != null) {
    
                    if (columnFormat.contains(beanKey.getKey())) {
                        String currency = bean.get(columnCurrency) != null ? bean.get(columnCurrency).toString() : "";
                        if (!currency.isEmpty() && currencyFormat.contains(currency)) {
                            newStyle.cloneStyleFrom(temp);
                            newStyle.setDataFormat(createDataFormat(wb, "#,##0.00"));
                            cell.setCellStyle(newStyle);
                        } else {
                            newStyleVnd.cloneStyleFrom(temp);
                            newStyleVnd.setDataFormat(createDataFormat(wb, "#,##"));
                            cell.setCellStyle(newStyleVnd);
                        }
                    } else {
                        cell.setCellStyle(temp);
                    }
                }
            }
        }
    }

    private static short createDataFormat(Workbook workbook, String format) {
        DataFormat dataFormat = workbook.createDataFormat();
        return dataFormat.getFormat(format);
    }

    private static void writeCell(Cell c, Object fieldValue) {
        if (fieldValue == null) {
            c.setCellValue("");
        } else if (fieldValue instanceof String) {
            c.setCellValue(fieldValue.toString());
        } else if (fieldValue instanceof Date) {
            c.setCellValue((Date) fieldValue);
        } else if (fieldValue instanceof Timestamp) {
            Timestamp d = (Timestamp) fieldValue;
            c.setCellValue(new Date(d.getTime()));
        } else if (fieldValue instanceof Integer) {
            c.setCellValue((Integer) fieldValue);
        } else if (fieldValue instanceof Boolean) {
            c.setCellValue((Boolean) fieldValue);
        } else if (fieldValue instanceof Long) {
            c.setCellValue((Long) fieldValue);
        } else if (fieldValue instanceof Double) {
            c.setCellValue((Double) fieldValue);
        } else {
            c.setCellValue(fieldValue.toString());
        }
    }

    // tim vi tri cot cua key tuong ung trong excel
    private static Cell findCellKeyDetail(XSSFSheet sh, String key) {
        String beanKeyS = "${" + key + "}";
        for (int rowNum = sh.getFirstRowNum(); rowNum <= sh.getLastRowNum(); rowNum++) {
            Row row = sh.getRow(rowNum);
            if (row == null) {
                continue;
            }
            for (int cellNum = 0; cellNum < row.getLastCellNum(); cellNum++) {
                Cell cell = row.getCell(cellNum, MissingCellPolicy.RETURN_BLANK_AS_NULL);

                if (cell != null && cell.getStringCellValue().equals(beanKeyS)) {
                    return cell;
                }
            }
        }
        return null;
    }

    private static Integer findCollumnIndexOfKey(String key, Row templateRow) {
        String beanKeyS = "${trans." + key + "}";
        for (int cellNum = 0; cellNum < templateRow.getLastCellNum(); cellNum++) {
            Cell cell = templateRow.getCell(cellNum, MissingCellPolicy.RETURN_BLANK_AS_NULL);
            if (cell == null)
                continue;
            cell.setCellType(CellType.STRING);
            if (cell.getStringCellValue().equals(beanKeyS)) {
                return cell.getColumnIndex();
            }
        }
        return -1;
    }


    private static Integer getCollumnIndexByKey(Map<String, Integer> map, String key) {
        return map.get(key);
    }

    /**
     * @return String return the fromDate
     */
    public String getFromDate() {
        return fromDate;
    }

    /**
     * @param fromDate the fromDate to set
     */
    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    /**
     * @return String return the toDate
     */
    public String getToDate() {
        return toDate;
    }

    /**
     * @param toDate the toDate to set
     */
    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    /**
     * @return List<String> return the headers
     */
    public List<String> getHeaders() {
        return headers;
    }

    /**
     * @param headers the headers to set
     */
    public void setHeaders(List<String> headers) {
        this.headers = headers;
    }

    /**
     * @return List<String> return the datas
     */
    public List<Map<String, Object>> getDatas() {
        return datas;
    }

    /**
     * @param datas the datas to set
     */
    public void setDatas(List<Map<String, Object>> datas) {
        this.datas = datas;
    }

    /**
     * @return String return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * @return String return the fileName
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * @param fileName the fileName to set
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

}
