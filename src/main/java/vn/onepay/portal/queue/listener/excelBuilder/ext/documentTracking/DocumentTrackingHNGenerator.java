package vn.onepay.portal.queue.listener.excelBuilder.ext.documentTracking;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.document_tracking.dao.DocumentTrackingDao;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DocumentTrackingHNGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<DocumentTrackingDto> baseListHN = DocumentTrackingDao.getDocumentTrackingHN(mIn, "DOWNLOAD_HN");
            generateHN(baseListHN, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE DOCUMENT TRACKING HN ERROR", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateHN(List<DocumentTrackingDto> list, List<Map> listData ) throws Exception {

        int index = 0;
        for(DocumentTrackingDto dto: list)
        {
            Map<String, Object> item = new HashMap<>();
            index++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, index);

            // ID
            item.put("DOC_ID", dto.getSubDocumentId() > 0 ? dto.getSubDocumentId() : dto.getN_id());

            // S_TAX_CODE
            item.put("S_TAX_CODE", dto.getTaxCode());

            // SIGN DAY
            item.put("D_SIGN_DAY", handleData(dto.getSignDate()));
            item.put("SIGN_DAY", dto.getSignDate());

            // MERCHANT_NAME
            item.put("MERCHANT_NAME", dto.getMerchantName());

            // CONTRACT_TYPE
            item.put("CONTRACT_TYPE", dto.getContractType());

            // CONTRACT_NAME
            item.put("CONTRACT_NAME",dto.getContractName());

            // BRANCH_NAME
            item.put("BRANCH_NAME", dto.getBranchName());

            // CONTRACT_NUMBER
            item.put("CONTRACT_NUMBER", dto.getContractNumber());

            // SUBMISSION_DATE
            item.put("SUBMISSION_DATE", handleData(dto.getSubmission_date()));

            // SUBMISSION_USER
            item.put("SUBMISSION_USER", dto.getSubmission_user());

            // SEND_BANK_DATE
            item.put("SEND_BANK_DATE", handleData(dto.getSendBank_date()));

            // SEND_BANK_USER
            item.put("SEND_BANK_USER", dto.getSendBank_user());

            //MS_ACCOUNTING_DATE
            item.put("MS_ACCOUNTING_DATE", handleData(dto.getMs_accounting_date()));
            item.put("ACCOUNTING_DATE", dto.getMs_accounting_date());

            //MS_ACCOUNTING_USER
            item.put("MS_ACCOUNTING_USER", dto.getMs_accounting_user());

             //SALE__DATE
             item.put("SALE_DATE", handleData(dto.getSale_date()));

             //SALE_USER
             item.put("SALE_USER", dto.getSale_user());

            //NOTES
            item.put("NOTES", dto.getNotes());

            // SUB_DOCUMENT_ID
            String exportId = String.format("%02d", dto.getGroupExport()) + "." + String.format("%05d", dto.getExportId());
            item.put("EXPORT_ID", exportId);

            // RECEIVER_PARTNER
            item.put("RECEIVER_PARTNER", convertReceiverPartner(dto.getReceiverPartner()));

            // RECEIVER_PARTNER
            item.put("CREATE_DATE", handleData(dto.getCreate_date()));

            // put into list
            listData.add(item);
        }
    }

    private static String handleData(Timestamp inputData) {
        String outputData = "";
        if (inputData != null) {
            try {
                outputData = Util.formatDate(new Date(inputData.getTime()), "dd-MM-yyyy");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Convert Date error", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }
        return outputData;
    }

    private static String convertReceiverPartner(String inputData) {
        String outputData = "";
        if ("1".equals(inputData)) {
            outputData = "Kế toán";
        } else if ("2".equals(inputData)) {
            outputData = "Sale";
        } else if ("3".equals(inputData)) {
            outputData = "Risk";
        } else if ("4".equals(inputData)) {
            outputData = "SS";
        } else if ("5".equals(inputData)) {
            outputData = "MM";
        } else if ("6".equals(inputData)) {
            outputData = "Other";
        }
        return outputData;
    }

    private static final Logger LOGGER = Logger.getLogger(DocumentTrackingHNGenerator.class.getName());
}
