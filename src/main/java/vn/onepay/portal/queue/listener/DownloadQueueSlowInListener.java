package vn.onepay.portal.queue.listener;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.utils.ParamsPool;
import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.*;
import java.util.Map;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by HungDX on 22-Jan-16.
 */
public class DownloadQueueSlowInListener extends BaseDownloadQueueInListener implements MessageListener {
    @Override
    void writeFile(vn.onepay.portal.queue.message.Message messageData) throws Exception {
        FileOutputStream fos = new FileOutputStream(this.fileNameHash);
        ZipOutputStream zos = new ZipOutputStream(fos);
        String downloadType = IConstants.EXCEL;
        if (Map.class.isInstance(messageData.getRequestBody())) {
            Map body = (Map) messageData.getRequestBody();
            downloadType = body.get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL
                    : body.get(IConstants.DOWNLOADTYPE).toString();
        }
        Map data = (Map) messageData.getRequestData();
        String fileExt  = (null == data || data.get(ParamsPool.FILE_EXT) == null) ? ".xls": data.get(IConstants.FILE_EXT).toString();
        if (this.fileName.contains(".xlsx"))
            addToZipFile(this.fileName, zos);
        else
            addToZipFile(this.fileName + (IConstants.EXCEL.equals(downloadType) ? fileExt : ".csv"), zos);
        File file = new File(this.fileNameHash);
        long fileSize = file.length();
        zos.close();
        fos.close();
        messageData.getRequestData().put(IConstants.FILE_SIZE, fileSize);
        messageData.setResultCode(200);
        messageData.setResultString("File Has been generated");
    }

    private void addToZipFile(String fileName, ZipOutputStream zos) throws FileNotFoundException, IOException {

        LOGGER.info("Writing '" + fileName + "' to zip file");

        File file = new File(fileName);
        FileInputStream fis = new FileInputStream(file);
        ZipEntry zipEntry = new ZipEntry(file.getName());
        zos.putNextEntry(zipEntry);

        byte[] bytes = new byte[1024];
        int length;
        while ((length = fis.read(bytes)) >= 0) {
            zos.write(bytes, 0, length);
        }
        file.delete();
        zos.closeEntry();
        fis.close();
    }

    @Override
    String fileName(String ext) {
        return this.fileName + ext;
    }

    @Override
    public void onMessage(Message message) {
        this.onBaseMassage(message);
    }

    private static final Logger LOGGER = Logger.getLogger(DownloadQueueSlowInListener.class.getName());
}
