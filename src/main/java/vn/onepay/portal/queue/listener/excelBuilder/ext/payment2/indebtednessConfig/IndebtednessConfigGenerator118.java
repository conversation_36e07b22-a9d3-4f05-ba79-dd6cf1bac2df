package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.indebtednessConfig;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.IndebConfigDao2;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;

@SuppressWarnings("rawtypes")
public class IndebtednessConfigGenerator118 implements BaseGenerator<ConfigQueryDto> {
    public static String dateFormat = "dd-MM-yyyy";
    private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);

    public static void main(String[] args) {
        String jsonString = "{\"fromDate\":null,\"toDate\":null,\"keyword\":null,\"paymentPeriod\":\"\",\"advanceAccountTypes\":\"PAYOUT\",\"advanceBank\":\"\",\"onepayBankId\":\"\",\"state\":null,\"service\":\"\",\"formula\":null,\"advanceAmount\":null,\"contractType\":\"\",\"guaranteeAmount\":null,\"guaranteeHoldingType\":\"\",\"withoutMerchant\":\"\",\"statusConfig\":\"\",\"stateOfEffective\":null,\"merchantIDWaitForApprove\":null,\"listPartnerId\":null,\"totalPartnerId\":null,\"page\":0,\"pageSize\":*********}";
        // Parse the JSON string to a JsonObject
        JsonObject jsonObject = new JsonObject(jsonString);

        // Map the JsonObject to ConfigQueryDto
        ConfigQueryDto configQueryDto = jsonObject.mapTo(ConfigQueryDto.class);
        new IndebtednessConfigGenerator118().generate(configQueryDto);
    }

    @Override
    public List<Map> generate(ConfigQueryDto mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ConfigDto> list = IndebConfigDao2.searchConfig(mIn);
            generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<ConfigDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (ConfigDto data : list) {
            Map<String, Object> item = new HashMap<>();
            rowNumber++;

            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            item.put(TemplateUtils.ADVANCE_ACCOUNT_COLUMN, data.getAdvanceAccount());

            item.put(TemplateUtils.ADVANCE_BANK_COLUMN, data.getAdvanceBank());

            item.put(TemplateUtils.BRANCH_NAME_COLUMN, data.getBranchName());

            item.put(TemplateUtils.CONTRACT_CODE_COLUMN, data.getContractCode());

            item.put(TemplateUtils.CONTRACT_TYPE_COLUMN, data.getContractType());

            item.put(TemplateUtils.LAST_MERCHANT_NAME_COLUMN, data.getLastMerchantName());

            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());

            item.put(TemplateUtils.PARTNER_NAME_COLUMN, data.getShortName());

            item.put(TemplateUtils.PAYMENT_PERIOD_COLUMN, data.getPaymentPeriod());

            item.put(TemplateUtils.PAY_CHANNEL_COLUMN, data.getService());

            item.put(TemplateUtils.STATE_COLUMN, data.getState());
            item.put("D_FROM", data.getFromDate());
            item.put("D_TO", data.getToDate());
            item.put("S_ACTION", data.getStatus());

            String effectState = "";
            // switch (data.getEffectiveState()) {
            // case "pending":
            // effectState = "Chưa hiệu lực";
            // break;
            // case "active":
            // effectState = "Đang hiệu lực";
            // break;
            // case "expired":
            // effectState = "Hết hạn";
            // break;
            // default:
            // effectState = "";
            // break;
            // }
            item.put("S_EFFECTIVE_STATE", effectState);


            item.put(TemplateUtils.TEN_DKKD_COLUMN, data.getTenDkkd());

            item.put(TemplateUtils.ACCOUNT_TYPE, data.getAuthorizedPerson());

            item.put(TemplateUtils.GUARANTEE_NUMBER, data.getGuaranteeHoldingType());

            item.put(TemplateUtils.GUARANTEE_AMOUNT, data.getGuaranteeAmount());

            item.put("D_CREATE", data.getCreatedDate());
            item.put("N_ID", data.getId());
            item.put("S_ONEPAY_ACC_BANK_ID", data.getOnepayBankId());

            item.put("D_AUTHORIZED_EXPIRATION_DATE", data.getAuthorizedExpirationDate() != null ? ConvertMilliSecondsToFormattedDate(data.getAuthorizedExpirationDate().toString()) : null);

            List<?> listPayoutAccount = Optional.ofNullable((List<?>) data.getAdvanceAccountPayout().get("payout")).orElse(new ArrayList<>());
            if (!listPayoutAccount.isEmpty()) {
                Map<?, ?> payoutAccount = (Map<?, ?>) listPayoutAccount.get(0);
                String payoutMerchantAccount = payoutAccount.get("payout_merchant_account") + " - " + payoutAccount.get("payout_merchant_id");
                item.put("S_PAYOUT_MERCHANT_ACCOUNT", payoutMerchantAccount);
                StringBuilder payoutMerchantAmount = new StringBuilder();
                Object splitAmount = payoutAccount.get("split_amount");

                if (splitAmount instanceof Double) {
                    Double amount = (Double) splitAmount;
                    DecimalFormat formatter = new DecimalFormat("#,###");
                    String formattedAmount = formatter.format(amount);
                    payoutMerchantAmount.append(formattedAmount);
                } else {
                    payoutMerchantAmount.append(splitAmount.toString());
                }
                String splitType = payoutAccount.get("split_type").toString();
                switch (splitType) {
                    case "fix":
                        payoutMerchantAmount.append(" VND");
                        break;
                    case "percent":
                        payoutMerchantAmount.append(" %");
                        break;
                    default:
                        break;
                }
                item.put("S_PAYOUT_MERCHANT_AMOUNT", payoutMerchantAmount.toString());
            }
            listData.add(item);
        }
    }


    public static String ConvertMilliSecondsToFormattedDate(String milliSeconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(Long.parseLong(milliSeconds));
        return simpleDateFormat.format(calendar.getTime());
    }

    private static final Logger LOGGER = Logger.getLogger(IndebtednessConfigGenerator118.class.getName());
}
