package vn.onepay.portal.queue.listener.excelBuilder.ext.overLimit;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.overLimit.OverLimitDao;
import vn.onepay.portal.resources.overLimit.dto.ReportOverLimitReq;
import vn.onepay.portal.resources.overLimit.dto.ReportOverLimitRes;
import vn.onepay.portal.utils.OneCreditUtil;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ReportGenerator implements BaseGenerator<ReportOverLimitReq>, IConstants {
    private static final Logger LOGGER = Logger.getLogger(ReportGenerator.class.getName());

    @Override
    public List<Map> generate(ReportOverLimitReq mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReportOverLimitRes> searchReports = OverLimitDao.searchReport(mIn);
            Long total = searchReports.stream()
                    .filter(item -> item.getTotal() != null)
                    .mapToLong(ReportOverLimitRes::getTotal)
                    .sum();
            Map item = new HashMap();
            item.put("sumTotal", total);
            listMap.add(item);
            generateRs(listMap, searchReports);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[OVER LIMIT REPORT ERROR] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, List<ReportOverLimitRes> reports)
            throws Exception {
        int count = 1;
        Map item;
        for (ReportOverLimitRes data : reports) {
            item = new HashMap();
            item.put("NO", count);
            count++;
            item.put("S_DATE", data.getTxnDate());
            item.put("S_CONTRACT_TYPE", data.getContractType());

            item.put("N_ACQUIRER", data.getAcquirerName());
            item.put("S_BANK_MERCHANT_ID", data.getBankMerchantId());

            item.put("S_MERCHANT_ID", data.getMerchantId());

            item.put("S_FEE_CODE", data.getFeeCode());
            item.put("S_RULE_NAME", data.getRuleName());

            item.put("S_CARD_TYPE", data.getCardType());

            item.put("totalTxn", data.getTotal());

            list.add(item);
        }

    }

    private String formatNumber(Double amount, String currency) {
        DecimalFormat formatter = new DecimalFormat("###,###,###");
        String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
        List<String> stringList = Arrays.asList(stringArray);
        if (stringList.contains(currency)) {
            formatter = new DecimalFormat("###,###,###0.00");
        }
        return formatter.format(amount);
    }
}
