package vn.onepay.portal.queue.listener.excelBuilder.ext.fixedDeposit;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fixedDeposit.FixedDepositDao;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;
import vn.onepay.portal.utils.Common;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FixedDepositPdfGenerator implements BaseGenerator<FixedDepositQueryDto> {

    @Override
    public List<Map> generate(FixedDepositQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<FixedDepositDto> list = FixedDepositDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FixedDepositDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");

        for (FixedDepositDto itemReport : list) {
            long days = 0;
            if (itemReport.getOpenDate() != null && !itemReport.getOpenDate().equals("") && itemReport.getMaturityDate() != null && !itemReport.getMaturityDate().equals("")) {
                LocalDate openDate = new Timestamp(df.parse(itemReport.getOpenDate()).getTime()).toLocalDateTime().toLocalDate();
                LocalDate maturityDate = new Timestamp(df.parse(itemReport.getMaturityDate()).getTime()).toLocalDateTime().toLocalDate();
                days = Duration.between(openDate.atStartOfDay(), maturityDate.atStartOfDay()).toDays();
            }
            Map item = new HashMap();
            rowNumber++;
            item.put("s_acc_debit_number", itemReport.getAccSavingNum());
            item.put("s_acc_debit_name", itemReport.getAccSavingName());
            item.put("s_acc_debit_bank", itemReport.getAccSavingBank());
            // item.put("NO", rowNumber);
            // item.put("N_ID", itemReport.getId());
            // item.put("S_CONTRACT_CODE", itemReport.getContractCode());
            // item.put("S_FD", itemReport.getFd());
            // item.put("S_BANK", itemReport.getBank());
            // item.put("S_CURRENCY", itemReport.getCurrency());
            // item.put("N_BALANCE", itemReport.getBalance());
            // item.put("D_OPEN", itemReport.getOpenDate());
            item.put("s_date", itemReport.getSignedDate());
            // item.put("D_MATURITY", itemReport.getMaturityDate());
            // item.put("N_MATURITY_PERIOD", itemReport.getMaturityPeriod());
            // item.put("N_INTEREST_RATE", itemReport.getInterestRate());
            // item.put("N_PARTNER_ID", itemReport.getPartnerId());
            // item.put("S_BUSINESS_REG_NAME", itemReport.getBusinessRegName());
            // item.put("S_MERCHANT_IDS", itemReport.getMerchantIds());
            // item.put("S_ADV_ACCOUNTS", itemReport.getAdvAccounts());
            item.put("s_acc_credit_number", itemReport.getAccRefNum());
            item.put("s_acc_credit_name", itemReport.getAccRefName());
            item.put("s_acc_credit_bank", itemReport.getAccRefBank());
            item.put("s_amount", Common.formatCurrencyDouble(itemReport.getBalance() + itemReport.getBalance() * itemReport.getInterestRate() * days / (365 * 100)));
            String balance_amount = Long.toString((long) Math.round(itemReport.getBalance()));
            LOGGER.log(Level.INFO, "N_BALANCE: " + itemReport.getBalance());
            LOGGER.log(Level.INFO, "N_BALANCE AFTER: " + balance_amount);
            item.put("s_amount_text", ConvertMoneyNumberToString.getVND(balance_amount));
            item.put("s_content", itemReport.getNote());
            // item.put("S_STATE", itemReport.getState());
            // item.put("S_SOURCE", itemReport.getSource());
            // item.put("S_TAXNUMBER", itemReport.getTaxNumber());
            
            
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FixedDepositPdfGenerator.class.getName());
}
