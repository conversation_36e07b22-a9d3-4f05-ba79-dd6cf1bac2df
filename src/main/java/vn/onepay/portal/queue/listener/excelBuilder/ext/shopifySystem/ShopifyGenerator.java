package vn.onepay.portal.queue.listener.excelBuilder.ext.shopifySystem;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.merchantmanagement.merchantmsp.dto.ShopifyDomainDto;
import vn.onepay.portal.resources.system_management.config_merchant_msp.SystemManagerMspDao;
import vn.onepay.portal.utils.TemplateUtils;

public class ShopifyGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        // Map<String, List<ShopifyDomainDto>> data = new HashMap<>();
        List<ShopifyDomainDto> listData = (SystemManagerMspDao.downloadShopifList(mIn.get("keyword").toString())).getList();
        // SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss dd/MM/yyyy");
        int stt = 1;
        for (ShopifyDomainDto item: listData) {
            Map temp = new HashMap<>();
            // temp.put(TemplateUtils.SHOPIFY_NO, item.get());
            temp.put(TemplateUtils.SHOPIFY_NO, stt);
            temp.put(TemplateUtils.SHOPIFY_MERCHANT_ID, item.getMerchantId()); 
            temp.put(TemplateUtils.SHOPIFY_ACTIVE_DATE, item.getActiveDate());
            temp.put(TemplateUtils.SHOPIFY_DOMAIN, item.getShopifyDomain());
            temp.put(TemplateUtils.SHOPIFY_STATE, item.getState());
            temp.put(TemplateUtils.SHOPIFY_APP_ID, item.getShopfifyAppId());
            temp.put(TemplateUtils.MERCHANT_CURRENCY, item.getMerchantCurrency());
            listMap.add(temp);
            stt++;
        }

        return listMap;
    }

    public static boolean isEmptyString(String value) {
        if ("".equals(value) || value == null) {
            return true;
        }
        return false;
    }
    
}
