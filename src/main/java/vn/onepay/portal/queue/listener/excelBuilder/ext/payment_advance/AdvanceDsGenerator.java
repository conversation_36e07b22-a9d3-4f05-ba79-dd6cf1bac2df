package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onepay.commons.util.Convert;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDto;

public class AdvanceDsGenerator implements BaseGenerator<Map> {
    private static final Logger LOGGER = Logger.getLogger(AdvanceDsGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            Map<String, Object> mOut = PaymentAdvanceDao.getAdvanceList(mIn);

            // sắp xếp lại
            List<AdvanceDto> advs = (List<AdvanceDto>) mOut.get("advanceList");
            List<Map> listAdvance = new ArrayList<>();
            HashMap<String, Object> map = new HashMap<>();
            // export
            Map<String, Object> beans = new HashMap<>();
            int i = 1;
            ObjectMapper objectMapper = new ObjectMapper();
            for (AdvanceDto adv : advs) {
                adv.setNo(i++);
                Map obj = objectMapper.convertValue(adv, Map.class);
                Map<String,Object> payoutInfo = adv.getPayout();
                int payoutActive = 0;
                String bank_account = "";
                String bank_account_name = "";
                String bank_name = "";
                String payout_merchant_account = "";
                String payout_merchant_id = "";
                int split_amount = 0;
                String split_type = "";
                int payout_amount = 0;
                String bankTransPayout = "";
                Double adv_amount_real = adv.getAdvanceAmountReal();
                Double trans_tranfer_amount = 0D;

                if(!(null == payoutInfo || payoutInfo.isEmpty())){
                    payoutActive = (int) payoutInfo.get("active");
                    bank_account = payoutInfo.get("bank_account").toString();
                    bank_account_name = payoutInfo.get("bank_account_name").toString();
                    bank_name = payoutInfo.get("bank_name").toString();
                    payout_merchant_account = payoutInfo.get("payout_merchant_account").toString();
                    payout_merchant_id = payoutInfo.get("payout_merchant_id").toString();
                    split_amount = (int) payoutInfo.get("split_amount");
                    split_type = payoutInfo.get("split_type").toString();
                    payout_amount =  (int) payoutInfo.get("payout_amount");
                    bankTransPayout = "VIETINBANK";
                    trans_tranfer_amount = adv_amount_real - payout_amount;
                } 
                obj.put("active", payoutActive);
                obj.put("bank_account", bank_account);
                obj.put("bank_account_name", bank_account_name);
                obj.put("bank_name", bank_name);
                obj.put("payout_merchant_account", payout_merchant_account);
                obj.put("payout_merchant_id", payout_merchant_id);
                obj.put("split_amount", split_amount);
                obj.put("split_type", split_type);
                obj.put("payout_amount", payout_amount);
                obj.put("bankTransPayout", bankTransPayout);
                obj.put("trans_tranfer_amount", trans_tranfer_amount);

                listAdvance.add(obj);

            }

            Calendar calOut = Calendar.getInstance();
            map.put("date_export", "Ngày " + calOut.get(Calendar.DATE) + " tháng " + (calOut.get(Calendar.MONTH) + 1)
                    + " năm " + calOut.get(Calendar.YEAR));
            beans.put("listAdvance", listAdvance);
            beans.put("map", map);
            listMap.add(beans);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate ADVANCE error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }
}
