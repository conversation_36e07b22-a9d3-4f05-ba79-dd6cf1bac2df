package vn.onepay.portal.queue.listener.excelBuilder.ext.paycollect;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.pay_collect.dao.*;
import vn.onepay.portal.resources.pay_collect.dto.TransactionHistoryDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class TransactionHistoryGenerator implements BaseGenerator<Map> {

    private static final String DATE_FORMAT = "dd-MM-yyyy hh:mm aa";

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);

            List<TransactionHistoryDto> list = PayCollectDao.getTransactionHistory(mIn);
            generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE TRANSACTION HISTORY error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<TransactionHistoryDto> list, List<Map> listData) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
        int rowNumber = 0;
        Double totalAmount = 0D;
        for (int i = 0; i < list.size(); i++) {
            rowNumber++;
            Map<String, Object> item = new HashMap<>();
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("transactionId", list.get(i).getTransactionId());
            // item.put("createDate", list.get(i).getCreatedDate() == null ? "" : Util.formatDate(new Date(list.get(i).getCreatedDate().getTime()), DATE_FORMAT));
            item.put("createDate", sdf.format(list.get(i).getCreatedDate()));
            item.put("merchantId", list.get(i).getMerchantId());
            item.put("clientId", list.get(i).getClientId());
            item.put("senderName", list.get(i).getSenderName());
            item.put("senderBank", list.get(i).getSenderBank());
            item.put("accountName", list.get(i).getReceivedName());
            item.put("bankName", list.get(i).getReceivedBank());
            item.put("reference", list.get(i).getReference());
            item.put("remark", list.get(i).getRemark());
            item.put("bankTxnRef", list.get(i).getBankTxnRef());
            totalAmount += list.get(i).getAmount();
            // item.put("amount", Util.formatCurrencyDouble(list.get(i).getAmount()));
            item.put("amount", list.get(i).getAmount());
            item.put("balanceBefore", list.get(i).getBalanceBefore());
            item.put("balanceAfter", list.get(i).getBalanceAfter());
            item.put("currency", list.get(i).getCurrency());
            item.put("state", list.get(i).getState().equals("approved") ? "Successful" : "Failed");
            item.put("bankCode", list.get(i).getBankCode());
            item.put("accountNumber", list.get(i).getReceivedAccount());
            // item.put("bankDate", list.get(i).getBankDate() == null ? "" : Util.formatDate(new Date(list.get(i).getBankDate().getTime()), DATE_FORMAT));
            // item.put("updateDate", list.get(i).getUpdateDate() == null ? "" : Util.formatDate(new Date(list.get(i).getUpdateDate().getTime()), DATE_FORMAT));
            item.put("bankDate", sdf.format(list.get(i).getBankDate()));
            item.put("updateDate", sdf.format(list.get(i).getUpdateDate()));
            item.put("referalProgram", list.get(i).getReferalProgram());
            item.put("beneficiaryBankAccount", list.get(i).getBeneficiaryBankAccount());
            item.put("bankTransId", list.get(i).getBankTransId());
            listData.add(item);
        }
        HashMap<String, Object> mapAmount = new HashMap<>();
        mapAmount.put("totalAmount", Util.formatCurrencyDouble(totalAmount));
        listData.add(mapAmount);
    }

    private static final Logger LOGGER = Logger.getLogger(TransactionHistoryGenerator.class.getName());
}
