package vn.onepay.portal.queue.listener.excelBuilder.ext.quicklink;

import vn.onepay.portal.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.QuicklinkClient;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportFullLinkGenerator implements BaseGenerator<Map<String, Object>> {
    private static final Logger LOGGER = Logger.getLogger(ExportFullLinkGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            listMap = QuicklinkClient.download(mIn).getJsonArray("list").getList();
            listMap = generateList(listMap);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[REPORT FULL LINK] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private List<Map> generateList(List<Map> listData) throws Exception {
        List<Map> returnList = new ArrayList<>();
        int count = 1;
        SimpleDateFormat sdf = new SimpleDateFormat("MMM d, yyyy, hh:mm:ss a");
        SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        for (Map map : listData) {
            Map<String, Object> temp = new HashMap<>();
            temp.put("count", count);
            temp.put("merchantProfileName", map.get("merchantProfileName"));
            temp.put("name", map.get("name"));
            temp.put("statusLink", map.get("statusLink").toString().equals("payment-link") == true ? "Payment Link" : "Static Link");
            temp.put("amount", (Convert.toString((map.get("amount") != null ? Double.valueOf(map.get("amount").toString()) : 0.0), "###,###,###") + "").split("\\.")[0] + " VND");
            String dCreate = sdf2.format(sdf.parse(map.get("createDate") + ""));
            temp.put("createDate", dCreate);
            List<Map> listMethod = (List<Map>) map.get("paymentMethod");
            temp.put("paynowMerc", "");
            temp.put("installmentMerc", "");
            temp.put("bnplMerc", "");
            for (Map map2 : listMethod) {
                if (map2.get("method").equals("paynow")) {
                    temp.put("paynowMerc", map2.get("merchantId"));
                }
                if (map2.get("method").equals("installment")) {
                    temp.put("installmentMerc", map2.get("merchantId"));
                }
                if (map2.get("method").equals("bnpl")) {
                    temp.put("bnplMerc", map2.get("merchantId"));
                }
            }
            String sExpired = map.get("expiredDate") + "";
            if (sExpired != null && !sExpired.isEmpty() && !sExpired.equalsIgnoreCase("null")) {
                long dExpired = sdf.parse(sExpired).getTime();
                long now = new Date().getTime();
                if (dExpired < now) {
                    temp.put("state", "Expired");
                    temp.put("on_off", "");
                } else if (map.get("linkState").equals("approved")) {
                    temp.put("state", "Enabled");
                    temp.put("on_off", "On");
                } else {
                    temp.put("state", "Disabled");
                    temp.put("on_off", "Off");
                }
            } else {
                temp.put("state", "Expired");
                temp.put("on_off", "");

            }
            returnList.add(temp);
            count += 1;
        }
        return returnList;
    }
}
