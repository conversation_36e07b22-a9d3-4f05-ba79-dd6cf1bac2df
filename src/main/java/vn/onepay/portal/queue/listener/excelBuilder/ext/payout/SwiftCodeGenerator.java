package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.swift_code.SwiftCodeDao;
import vn.onepay.portal.resources.payout.swift_code.dto.SwiftCodeDto;
import vn.onepay.portal.resources.payout.swift_code.dto.SwiftCodeQueryDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class SwiftCodeGenerator implements BaseGenerator<SwiftCodeQueryDto> {

    @Override
    public List<Map> generate(SwiftCodeQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<SwiftCodeDto> list = SwiftCodeDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate SWIFT CODE error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<SwiftCodeDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (SwiftCodeDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.SWIFT_CODE, itemReport.getSwiftCode());
                item.put(TemplateUtils.SHORT_NAME, itemReport.getShortName());
                item.put(TemplateUtils.FULL_NAME, itemReport.getFullName());
                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());
                item.put(TemplateUtils.DATE_TEXT_COLUMN, itemReport.getDateCreate());
                item.put(TemplateUtils.DESC, itemReport.getDesc());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(SwiftCodeGenerator.class.getName());
}
