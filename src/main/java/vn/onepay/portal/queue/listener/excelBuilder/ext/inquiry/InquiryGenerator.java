package vn.onepay.portal.queue.listener.excelBuilder.ext.inquiry;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.inquiry.dao.InquiryBankDao;
import vn.onepay.portal.resources.inquiry.dto.InquiryBankDto;
import vn.onepay.portal.utils.TemplateUtils;

public class InquiryGenerator implements BaseGenerator<Map<String, Object>>{
    private static final Logger LOGGER = Logger.getLogger(InquiryGenerator.class.getName());
    @Override
    public List<Map> generate(Map<String, Object> mIn){
        List<Map> lMaps = new ArrayList<>();
        int rowNumber = lMaps.size();
      try {
          List<InquiryBankDto> iDtoList = new InquiryBankDao().getTransactions(mIn).getList();
          for (InquiryBankDto inquiryBankDto : iDtoList) {
              rowNumber ++;
              Map items = new HashMap<>();
              items.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
              items.put(TemplateUtils.TRANSACTION_ID, inquiryBankDto.getTransactionId());
              items.put(TemplateUtils.BANK_ID, inquiryBankDto.getBankId());
              items.put(TemplateUtils.ACCOUNT_NUMBER, inquiryBankDto.getAccountNumber());
              items.put(TemplateUtils.TRANSACTION_DATE, inquiryBankDto.getTransactionDate());
              items.put(TemplateUtils.DEBIT, inquiryBankDto.getDebit());
              items.put(TemplateUtils.CREDIT, inquiryBankDto.getCredit());
              items.put(TemplateUtils.ACCOUNT_BALANCE, inquiryBankDto.getAccountBalance());
              items.put(TemplateUtils.CORRESPONSIVE_ACCOUNT, inquiryBankDto.getCorresponsiveAccount());
              items.put(TemplateUtils.CORRESPONSIVE_ACCOUNT_NAME, inquiryBankDto.getCorresponsiveAccountName());
              items.put(TemplateUtils.AGENCY, inquiryBankDto.getAgency());
              items.put(TemplateUtils.CORRESPONSIVE_BANK_NAME, inquiryBankDto.getCorresponsiveBankName());
              items.put(TemplateUtils.CORRESPONSIVE_BANK_ID, inquiryBankDto.getCorresponsiveBankId());
              items.put(TemplateUtils.SERVICE_BRANCH_ID, inquiryBankDto.getServiceBranchId());
              items.put(TemplateUtils.SERVICE_BRANCH_NAME, inquiryBankDto.getServiceBankName());
              items.put(TemplateUtils.CHANNEL, inquiryBankDto.getChannel());
              items.put(TemplateUtils.CREATE_DATE, inquiryBankDto.getCreateDate());
              items.put(TemplateUtils.DESC, inquiryBankDto.getDesc());
              lMaps.add(items);
          }

      } catch (Exception e) {
        LOGGER.log(Level.SEVERE, "Generate InquiryGenerator error", e);
            throw IErrors.QUERY_ERROR;
      }
      return lMaps;
    }
    
}
