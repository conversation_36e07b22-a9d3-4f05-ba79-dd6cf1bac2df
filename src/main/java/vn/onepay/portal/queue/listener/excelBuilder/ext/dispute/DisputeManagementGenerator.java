package vn.onepay.portal.queue.listener.excelBuilder.ext.dispute;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang.StringUtils;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.client.DisputeClient;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DisputeManagementGenerator implements BaseGenerator<Map<String, Object>>, IConstants {
    private static Logger logger = Logger.getLogger(DisputeManagementGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            JsonObject disputeResp = DisputeClient.downloadDispute(mIn);
            List<Map> list = (disputeResp == null || disputeResp.getMap() == null) ? new ArrayList<>() : (List<Map>) disputeResp.getMap().get("list");
//            List<DisputeDto> list1 = gson.fromJson((JsonElement) disputeResp.getMap().get("list"), new TypeToken<List<DisputeDto>>() {
//            }.getType());
            String role = mIn.get(ROLE).toString();
            generateRs(list, listMap, role);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportDisputeManagementGenerator error", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, List<Map> listMap, String role) {
        Map mapData;
        List<Map> onepayPics = getOnepayPics(role);
        int index = 1;
        for (Map item : list) {
            mapData = new HashMap();

            mapData.put("no", index++);
            mapData.put("disputeStatus", convertDisputeStatus(item.getOrDefault("disputeStatus", "").toString()));
            mapData.put("disputeDate", item.get("disputeDate") == null ? "" : convertDateToString(item.get("disputeDate").toString(),"dd/MM/yyyy hh:mm a"));
            mapData.put("dueDate", item.get("dueDate") == null ? "" : convertDateToString(item.get("dueDate").toString(),"dd/MM/yyyy hh:mm a"));
            mapData.put("lastResponse", item.getOrDefault("lastRespone", "").toString());
            mapData.put("merchantId", item.getOrDefault("merchantId", "").toString());
            mapData.put("transId", item.getOrDefault("transactionId", "").toString());
            mapData.put("orderRef", item.getOrDefault("orderReference", "").toString());
            mapData.put("merchantTransRef", item.getOrDefault("merchantTransactionReference", "").toString());
            mapData.put("gate", convertGate(item.getOrDefault("paygate", "").toString()));
            mapData.put("channel", convertChannel(item.getOrDefault("paygate", "").toString()));
            mapData.put("acq", item.getOrDefault("acquirerDownload", "").toString());
            mapData.put("cardType", item.getOrDefault("cardTypeDownload", "").toString());
            mapData.put("cardNumber", item.getOrDefault("cardNumber", "").toString());
            mapData.put("authCode", item.getOrDefault("authorisationCode", "").toString());
            mapData.put("transactionAmount", Double.valueOf(item.getOrDefault("transactionAmount", "0").toString()));
            mapData.put("disputeAmount", Double.valueOf(item.getOrDefault("disputeAmount", "0").toString()));
            mapData.put("disputeReason", item.getOrDefault("reasonDownload", ""));
            mapData.put("disputeCode", item.getOrDefault("codeDownload", ""));
            mapData.put("disputeStage", item.getOrDefault("stageDownload", ""));
            mapData.put("outcome", item.getOrDefault("outcomeDownload", ""));
            mapData.put("lastUpdate", item.get("lastUpdate") == null ? "" : convertDateToString(item.get("lastUpdate").toString(),"dd/MM/yyyy hh:mm a"));
            mapData.put("merchantChannel", item.getOrDefault("merchantChannel", ""));
            mapData.put("partnerName", item.getOrDefault("partnerName", ""));
            mapData.put("transactionDate", item.get("transactionDate") == null ? "" : convertDateToString(item.get("transactionDate").toString(),"dd/MM/yyyy hh:mm a"));
            mapData.put("transactionType", item.getOrDefault("transactionType", ""));
            mapData.put("transactionState", item.getOrDefault("transactionStatus", ""));
            mapData.put("transactionCurrency", item.getOrDefault("transactionCurrency",""));
            mapData.put("disputeCurrency", item.getOrDefault("disputeCurrency",""));
            mapData.put("refundCurrency", item.getOrDefault("transactionCurrency",""));
            mapData.put("refundAmount", item.getOrDefault("refundAmount","0"));
            mapData.put("onepayPic", convertToPicName(item, onepayPics));
            mapData.put("MID", item.getOrDefault("sMid",""));
            mapData.put("fraudInves", item.getOrDefault("fraudInves",""));
            mapData.put("caseId", item.getOrDefault("sCaseID",""));
            mapData.put("fileStatus", getFileStatus(item));

            listMap.add(mapData);
        }
    }

    private String getFileStatus(Map item) {
        return !isKbank(item)? ""
            : StringUtils.isEmpty((String) item.get("nFileID")) ? "Chưa có file" : "Đã gửi";
    }

    private boolean isKbank(Map item) {
        String acquirer = (String) item.get("acquirer");
        return "12".equals(acquirer) || "kbank".equalsIgnoreCase(acquirer);
    }

    private String convertDisputeStatus(String status) {
        if (status.equals("created")) return "Created";
        else if (status.equals("need_merchant_response")) return "Need Merchant Response";
        else if (status.equals("waiting_for_onepay_review")) return "Waiting for OnePay review";
        else if (status.equals("resolved")) return "Resolved";
        else if (status.equals("dispute_reminded")) return "Dispute Reminded";
        else return "";
    }

    private String convertGate(String channel) {
        if (channel.equals("QT")) return "International";
        else if (channel.equals("ND")) return "Domestic";
        else return "Mobile App";
    }

    private String convertChannel(String channel) {
        if (channel.equals("QT")) return "INT";
        else if (channel.equals("ND")) return "DOM";
        else return "APP";
    }

    private List<Map> getOnepayPics(String role){
        JsonObject allOnePayPics = DisputeClient.getAllOnePayPics(role);
        List<Map> list = (allOnePayPics == null || allOnePayPics.getMap() == null) ? new ArrayList<>() : (List<Map>)allOnePayPics.getMap().get("list");
        return list;
    }

    private String convertToPicName(Map item, List<Map> onepayPics ){
        Object onepayPicCode = item.get("onepayPic");
        if (onepayPicCode == null) return "";
        else {
            Map findByIdPic = onepayPics.stream()
                .filter(pic -> pic.get("id").toString().equals(onepayPicCode.toString()))
                .findFirst()
                .orElse(null);
            if(findByIdPic == null || findByIdPic.get("name") == null) return "";
            else return findByIdPic.get("name").toString();
        }
    }

    private LocalDateTime convertToDate(String date){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM d, yyyy, h:mm:ss a");
        return LocalDateTime.parse(date, formatter);
    }

    private String convertDateToString(String dateStr, String format){
        LocalDateTime date = convertToDate(dateStr);
        DateTimeFormatter formatDateTime = DateTimeFormatter.ofPattern(format);
        return formatDateTime.format(date);
    }
}
