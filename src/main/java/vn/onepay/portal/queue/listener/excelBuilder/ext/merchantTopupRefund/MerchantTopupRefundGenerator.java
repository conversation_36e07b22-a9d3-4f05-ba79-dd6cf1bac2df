package vn.onepay.portal.queue.listener.excelBuilder.ext.merchantTopupRefund;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.merchantTopupRefund.MerchantTopupRefundDao;
import vn.onepay.portal.resources.payment2.merchantTopupRefund.dto.MerchantTopupRefundDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantTopupRefundGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<MerchantTopupRefundDto> list = MerchantTopupRefundDao.search(mIn).getList();
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant topup refund Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<MerchantTopupRefundDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat dfhhmmss = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");

        for (MerchantTopupRefundDto itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("trans_topup_id", itemReport.getTrans_topup_id());
            item.put("state", itemReport.getState());
            item.put("partner_id", itemReport.getPartner_id());
            item.put("partner_name", itemReport.getPartner_name());
            item.put("merchant_ids", itemReport.getMerchant_ids());
            item.put("pay_channel", itemReport.getPay_channels());
            item.put("advance_account", itemReport.getAdvance_account());
            item.put("adv_config_id", itemReport.getAdv_config_id());
            item.put("source", itemReport.getSource());
            item.put("currency", itemReport.getCurrency());
            item.put("amount", itemReport.getAmount());
            item.put("trans_type", itemReport.getTrans_type());
            item.put("created_date", itemReport.getDate_created() != null ? dfhhmmss.format(itemReport.getDate_created()) : "");
            item.put("updated_date", itemReport.getDate_updated() != null ? dfhhmmss.format(itemReport.getDate_updated()) : "");
            item.put("transfer_date", itemReport.getDate_transfer() != null ? df.format(itemReport.getDate_transfer()) : "");
            item.put("date_advance_create", itemReport.getDate_advance_create() != null ? df.format(itemReport.getDate_advance_create()) : "");
            item.put("advance_id", itemReport.getAdvance_id());
            item.put("merchant_bank_account", itemReport.getMerchant_bank_account());
            item.put("onepay_bank_name", itemReport.getOnepay_bank_name());
            item.put("onepay_bank_account", itemReport.getOnepay_bank_account());
            item.put("bank_topup_ref", itemReport.getBank_topup_ref());
            item.put("bank_topup_content", itemReport.getBank_topup_content());
            item.put("desc", itemReport.getDesc());
            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantTopupRefundGenerator.class.getName());
}
