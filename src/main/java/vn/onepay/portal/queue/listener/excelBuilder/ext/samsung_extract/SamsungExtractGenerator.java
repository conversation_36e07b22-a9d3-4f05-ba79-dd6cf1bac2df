package vn.onepay.portal.queue.listener.excelBuilder.ext.samsung_extract;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.advance_transaction.AdvanceTransactionDao;
import vn.onepay.portal.resources.payment_advance.advance_transaction.dto.SamsungExtractDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SamsungExtractGenerator implements BaseGenerator<Map<String, Object>> {

    private static Logger logger = Logger.getLogger(SamsungExtractGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        List<SamsungExtractDto> unextractedTranList = new ArrayList<>();
        try {
            Map dataMap = new HashMap<>();
            dataMap = AdvanceTransactionDao.getSamsungExtract(mIn);
            Map<String, String> productCodeMap = AdvanceTransactionDao.getSamsungProductCode();
            unextractedTranList = (List<SamsungExtractDto>) dataMap.get("listDetail");
            if (unextractedTranList != null) {
                while (!unextractedTranList.isEmpty()) {
                    List<SamsungExtractDto> cloneTransactionList = new ArrayList<>(unextractedTranList);
                    unextractedTranList = new ArrayList<>();
                    List<Map<String, Object>> extractedTranList = new ArrayList<>(); // list giao dich da tach
                    for (SamsungExtractDto mtranParent : cloneTransactionList) {
                        // Map<String, Object> mBuf = null;
                        // mBuf = new HashMap<>();

                        String orderInfo = mtranParent.getOrderInfo();
                        String sDate = mtranParent.getMerchantTransactionDate();
                        String transactionId = mtranParent.getTransactionId();
                        String transactionType = mtranParent.getTransactionType();
                        int paymentPeriod = getPaymentPeriod(mtranParent.getData(), mtranParent.getPayChannel());
                        String merchantTransRef = mtranParent.getMerchantTransRef();
                        String epp = "";
                        String paymentMode = getPaymentMode(mtranParent.getPayChannel());
                        String tranType = mtranParent.getTransactionType();
                        String poCode = getPoCode(mtranParent.getRequestBody(), mtranParent.getOrderInfo());
                        String storeId = getStoreId(mtranParent.getOrderInfo());
                        String cardType = mtranParent.getCardType();
                        Double amount = mtranParent.getAmount();
                        Map tran = new HashMap();
                        tran.put("s_order_info", orderInfo);
                        tran.put("s_date", sDate);
                        tran.put("s_transaction_id", transactionId);
                        tran.put("s_transaction_type", transactionType);
                        tran.put("s_payment_period", paymentPeriod);
                        tran.put("s_merchant_trans_ref", merchantTransRef);
                        tran.put("s_po_code", poCode);
                        tran.put("s_store_id", storeId);
                        tran.put("s_card_type", cardType);
                        tran.put("payment_mode", paymentMode);
                        tran.put("unit_price", amount);
                        tran.put("product_category", "IM");
                        if (mtranParent.getMerchantId().equals("OP_SAMSUNGQL")) {
                            extractedTranList.add(tran);
                            continue;
                        }
                        String urlReturn = getUrlReturn(mtranParent.getReturnUrl(),
                        mtranParent.getRawData());
                        if (urlReturn != null && urlReturn.length() > 0)
                            urlReturn = java.net.URLDecoder.decode(urlReturn, StandardCharsets.UTF_8.name());
                        String patternEpp = ".*(vnepp)\\/([^\\/]+)\\/.*";
                        Pattern ptEpp = Pattern.compile(patternEpp);
                        Matcher mEpp = ptEpp.matcher(urlReturn);
                        if (mEpp.find()) {
                            epp = mEpp.group(2);
                        } else {
                            String patternEppV2 = ".*(v2)\\/([^\\/]+)\\/.*";
                            Pattern ptEppV2 = Pattern.compile(patternEppV2);
                            Matcher mEppV2 = ptEppV2.matcher(urlReturn);
                            if (mEppV2.find()) {
                                epp = mEppV2.group(2);
                            }
                        }
                        // get product - tách từ phần param
                        String patternParam = "^.*\\?([^?]+)";
                        Pattern r = Pattern.compile(patternParam);
                        Matcher m = r.matcher(urlReturn);
                        if (m.find()) {
                            String strparams = m.group(1);
                            strparams = urlReturn.substring(urlReturn.indexOf('?') + 1);
                            if (strparams != null && strparams.length() > 0) {
                                String[] params = strparams.split("&");
                                Map mBuf = null;
                                for (String strparam : params) {
                                    String[] param = strparam.split("=");
                                    if (param != null && param.length > 1) {
                                        if ("product_code".equals(param[0])) {
                                            if (mBuf != null)
                                                extractedTranList.add(mBuf);
                                            mBuf = new HashMap();
                                            mBuf.put("s_order_info", orderInfo);
                                            mBuf.put("s_transaction_id", transactionId);
                                            mBuf.put("s_payment_period", paymentPeriod);
                                            mBuf.put("s_merchant_trans_ref", merchantTransRef);
                                            mBuf.put("s_transaction_type", tranType);
                                            mBuf.put("s_card_type", (mtranParent.getCardType().replaceAll("MSBQR",
                                                    "MOBILE BANKING / E-WALLET")));
                                            mBuf.put("s_date", sDate);
                                            mBuf.put("epp", epp);
                                            mBuf.put("payment_mode", paymentMode);
                                            mBuf.put("s_po_code", poCode);
                                            mBuf.put(param[0], param[1]);
                                        }
                                        if (mBuf != null && ("product_quantity".equals(param[0])
                                                || "product_category".equals(param[0])
                                                || "company_code".equals(param[0]))) {
                                            mBuf.put(param[0], param[1]);
                                        }
                                        if ("unit_price".equals(param[0])) {
                                            mBuf.put(param[0], param[1]);
                                            // thu tu param lan luot la product_code -> product_quantity ->
                                            // product_category -> unit_price ->
                                            // company_code => khi key la unit_price
                                            // thi he thong da kiem tra product_category truoc day roi
                                            if ((mBuf.get("product_category") == null
                                                    || mBuf.get("product_category").toString().isEmpty())
                                                    && productCodeMap.containsKey(mBuf.get("product_code")))
                                                mBuf.put("product_category",
                                                        productCodeMap.get(mBuf.get("product_code")));

                                        }
                                    }
                                }
                                if (mBuf != null)
                                    extractedTranList.add(mBuf);
                                else {
                                    mBuf = new HashMap<>();
                                    mBuf.put("s_order_info", orderInfo);
                                    mBuf.put("s_transaction_id", transactionId);
                                    mBuf.put("s_payment_period", paymentPeriod);
                                    mBuf.put("s_merchant_trans_ref", merchantTransRef);
                                    mBuf.put("s_transaction_type", tranType);
                                    mBuf.put("s_card_type", (mtranParent.getCardType().replaceAll("MSBQR",
                                            "MOBILE BANKING / E-WALLET")));
                                    mBuf.put("s_date", sDate);
                                    mBuf.put("epp", epp);
                                    mBuf.put("payment_mode", paymentMode);
                                    mBuf.put("s_po_code", poCode);
                                    extractedTranList.add(mBuf);
                                }
                            }
                        }

                    }
                    generateRs(extractedTranList, listMap);
                }

            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Export Adjust Advance Approval Error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map<String, Object>> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (Map<String, Object> data : list) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("s_order_info", data.get("s_order_info"));
            item.put("s_transaction_id", data.get("s_transaction_id"));
            Date date = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").parse(data.get("s_date").toString());
            item.put("s_date", date);
            item.put("epp", data.get("epp"));
            item.put("payment_mode", data.get("payment_mode"));
            item.put("product_code", data.get("product_code"));
            item.put("product_quantity", data.get("product_quantity"));
            item.put("product_category", data.get("product_category"));
            item.put("unit_price",
                    Double.parseDouble(data.get("unit_price") != null ? data.get("unit_price").toString() : "0"));
            item.put("company_code", data.get("company_code"));
            item.put("s_transaction_type", data.get("s_transaction_type"));
            item.put("s_card_type", data.get("s_card_type"));
            item.put("s_merchant_trans_ref", data.get("s_merchant_trans_ref"));
            item.put("s_payment_period", data.get("s_payment_period"));
            item.put("s_po_code", data.get("s_po_code"));
            item.put("s_store_id", data.get("s_store_id"));
            listData.add(item);

        }
    }

    private String getPaymentMode(String payChannel) {
        String paymentMode = "";
        switch (payChannel) {
            case "QT":
                paymentMode = "International";
                break;
            case "ND":
                paymentMode = "Domestic";
                break;
            case "QR":
                paymentMode = "QR";
                break;
            case "BNPL":
                paymentMode = "BNPL";
                break;
        }
        return paymentMode;
    }

    private String getUrlReturn(String returnUrl, String inputData) {
        if (inputData != null) {
            try {
                String rawData = new String(Base64.getDecoder().decode(String.valueOf(inputData)));
                JsonObject outObject = new JsonObject(rawData);
                return outObject.getString("vpc_ReturnURL");
            } catch (Exception e) {
                logger.log(Level.SEVERE, "QR URL RETURN IS INVALID: ", e);
                return "";
            }
        } else {
            return returnUrl;
        }
    }

    private static int getPaymentPeriod(String data, String payChannel) {
        if("BNPL".equals(payChannel)) {
            JsonObject jData = new JsonObject(data);
            return jData.getInteger("tenure");
        }
        return 0;
    }

    private static String getStoreId(String orderInfo) {
        if (orderInfo != null) {
            int underscoreIndex = orderInfo.indexOf("_");
            return (underscoreIndex > 0) ? orderInfo.substring(0, underscoreIndex) : orderInfo;
        }
        return "";
    }

    private static String getPoCode(String requestBody, String orderInfo) {
        if (requestBody != null) {
            JsonObject requestBodyObj = new JsonObject(requestBody);
            return requestBodyObj.getString("note");
        } else {
            if (orderInfo != null) {
                int underscoreIndex = orderInfo.indexOf("_");
                return (underscoreIndex > 0) ? orderInfo.substring(0, underscoreIndex) : orderInfo;
            }
        }
        return "";
    }
}