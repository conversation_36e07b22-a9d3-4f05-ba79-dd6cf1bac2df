package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import org.springframework.stereotype.Component;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.bank.ConfigBankDao;
import vn.onepay.portal.resources.payout.bank.dto.BankDto;
import vn.onepay.portal.resources.payout.merchant_config.PayoutMerchantConfigDao;
import vn.onepay.portal.resources.payout.merchant_config.dto.MerchantConfigDTO;
import vn.onepay.portal.utils.TemplateUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class BankConfigGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<BankDto> bankDtos = ConfigBankDao.searchBank(mIn);
            this.generateRs(bankDtos, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Config error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<BankDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (BankDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;

                item.put(TemplateUtils.ROW_NUMBER_COLUMN, itemReport.getPriority());

                item.put(TemplateUtils.BANK_NAME_COLUMN, itemReport.getName());

                item.put(TemplateUtils.SWIFT_CODE, itemReport.getSwiftCode());

                item.put(TemplateUtils.BALANCE_TEXT_COLUMN, itemReport.getCurrency() + " " + String.format("%,d", itemReport.getBalance()));

                item.put(TemplateUtils.INTERNER_TEXT_COLUMN, itemReport.getCurrency() + " " + String.format("%,d", itemReport.getInternerCost()));

                item.put(TemplateUtils.EXTERNER_TEXT_COLUMN, itemReport.getCurrency() + " " + String.format("%,d", itemReport.getExternerCost()));

                item.put(TemplateUtils.VOLUMN_PER_DAY_COLUMN, (itemReport.getVolumnPerDay() != null && itemReport.getVolumnPerDay() != 0L) ? (itemReport.getCurrency() + " " + String.format("%,d", itemReport.getVolumnPerDay())) : "UNLIMITED");

                item.put(TemplateUtils.BANK_ACC_ID_COLUMN, itemReport.getBankAccId());

                item.put(TemplateUtils.BANK_ACC_NAME_COLUMN, itemReport.getBankAccName());

                item.put(TemplateUtils.CONTRACT_CODE_COLUMN, itemReport.getContractCode());

                item.put(TemplateUtils.CONTRACT_SIGNED_DATE_COLUMN, itemReport.getContractSignedDate() != null ? itemReport.getContractSignedDate().split(" ")[0] : "");

                item.put(TemplateUtils.ACTIVE_DATE_COLUMN, itemReport.getActiveDate() != null ? sdf.parse(itemReport.getActiveDate()) : null);

                item.put(TemplateUtils.DEACTIVE_DATE_COLUMN, itemReport.getDeactiveDate() != null ? sdf.parse(itemReport.getDeactiveDate()) : null);

                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());

                item.put(TemplateUtils.DESC, itemReport.getDesc());

                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final Logger LOGGER = Logger.getLogger(BankConfigGenerator.class.getName());
}
