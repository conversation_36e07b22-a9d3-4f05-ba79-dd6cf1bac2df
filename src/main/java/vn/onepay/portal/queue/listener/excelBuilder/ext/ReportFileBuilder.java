package vn.onepay.portal.queue.listener.excelBuilder.ext;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.time.DateUtils;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.onepay.commons.util.Convert;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import net.sf.jsqlparser.util.TablesNamesFinder;
import vn.onepay.portal.Config;
import vn.onepay.portal.DateTimeUtil;
import vn.onepay.portal.DateTimeUtil.DateTemplate;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ReportBuilder;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalInstallmentBankGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalInstallmentGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalInstallmentMerchantGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalRefundApprovalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalRefundApprovalGenerator2;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.InternationalTransactionalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.International.report.InternationalReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.accountant.AccountantExplanationGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.accountant.AccountantReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.accountantManagement.ExportReceiptDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.accountantManagement.ExportReceiptGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.acquirer_rule_group.AcquirerRuleGroupGenerate;
import vn.onepay.portal.queue.listener.excelBuilder.ext.adjust_advance_approval.AdjustAdvanceApprovalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.adjust_advance_approval.AdjustAdvanceBigMerchantApprovalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.advance_payment.PaymentAdvanceTransactionGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.billing.GenerateBillingDebtClearingFile;
import vn.onepay.portal.queue.listener.excelBuilder.ext.blackListMerchant.BlackListMerchantGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.dispute.DisputeManagementGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.documentTracking.DocumentTrackingHCMGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.documentTracking.DocumentTrackingHNGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.domestic.DomesticRefundGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.domestic.DomesticRefundGenerator2;
import vn.onepay.portal.queue.listener.excelBuilder.ext.domestic.DomesticReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.domestic.DomesticReportGenerator2;
import vn.onepay.portal.queue.listener.excelBuilder.ext.domestic.DomesticTransactionalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.exchange_rate.ExchangeRateGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.fixedDeposit.FixedDepositGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.fixedDeposit.UNCPdfGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.inquiry.InquiryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.invoice_individual.InvoiceSummaryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.invoice_individual.InvoiceTransactionGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.leadProvider.LeadProviderConsumerGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.leadProvider.LeadProviderReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchantFee.ExportInstallmentFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchantTopupRefund.MerchantTopupRefundGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchantTransfer.MerchantTransferGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_account.MerchantAccountGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_config_view.qr_paygate.ExportQrPaygateGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_history.MerchantHistoryGenerate;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_history.constant.MerchantDownloadTypeEnum;
import vn.onepay.portal.queue.listener.excelBuilder.ext.missing_trans.MissingTransactionGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.onoffBanks.OnOffBanksGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.overLimit.ReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.overLimit.TransGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.paycollect.PayCollectReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.paycollect.TransactionHistoryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.dailyFeeReport.DailyFeeReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.indebtednessConfig.IndebtednessConfigGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.indebtednessConfig.IndebtednessConfigGenerator118;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport.MonthlyFeeReportAdvDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport.MonthlyFeeReportAdvGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport.MonthlyFeeReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceBigMerchantDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceBigMerchantDsGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceBigMerchantGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceDsGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.AdvanceGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.FDPaymentDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.FDPaymentGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.FDPaymentTransactionDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.GuaranteeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.PreAdvanceBigMerchantDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.PreAdvanceDetailGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance.UNCPdfGurantee;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_reconciliation.bank_fee.PaymentBankFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_reconciliation.end_user.Payment2EndUserFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payment_reconciliation.merchant_fee.PaymentMerchantFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankBalanceEnquiryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankConfigGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankMapGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankTopUpGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.BankTransferReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.FundTransHistoryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantAccBalanceGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantBalanceEnquiryGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantConfigGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantFeeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantTopUpGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.MerchantTransferReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.SummaryFundsTranferGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.payout.SwiftCodeGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment.QrPaymentPurchaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment.QrPaymentRefundApproval2Generator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment.QrPaymentRefundApprovalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment.QrPaymentRefundGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment.QrReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.quicklink.ExportFullLinkGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation.CDRFileGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation.ReconciliationBNPLGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation.ReconciliationGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation.ReconciliationInterGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation.ReconciliationThirdGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation_upos.ReconciliationUposGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.refundApproval.RefundApprovalGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.roleManagement.RoleManagementGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.samsung_extract.SamsungExtractGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.service_suport.ss_trans_management.ExportSSTransManagementGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.service_suport.ss_trans_management.ExportSSTransManagementGeneratorPayment;
import vn.onepay.portal.queue.listener.excelBuilder.ext.shopify.ShopifyDetailReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.shopify.ShopifySummaryReportGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.shopifySystem.ShopifyGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.transaction_accountant.ExportTransactionAccoutingGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.transaction_accountant.TransactionAccoutingGenerator18;
import vn.onepay.portal.queue.listener.excelBuilder.ext.vinhome_extract.VinhomeExtractGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.volumeAlert.ConfigRuleGenerator;
import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.resources.accountantManagement.merchantManagement.MerchantManagementDAO;
import vn.onepay.portal.resources.accountantManagement.statement.StatementDAO;
import vn.onepay.portal.resources.accountantManagement.statement.StatementHandler;
import vn.onepay.portal.resources.auto_confirm_refund.report.ReportConfirmRefundDao;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.exchange_rate.dto.ExchangeRateQueryDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;
import vn.onepay.portal.resources.invoice_individual.invoice.InvoiceIndividual;
import vn.onepay.portal.resources.invoice_individual.invoice.InvoiceIndividualDao;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDao;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDto;
import vn.onepay.portal.resources.onepay_registed_email.HomepageRegisteredEmailsGenerator;
import vn.onepay.portal.resources.overLimit.dto.ReportOverLimitReq;
import vn.onepay.portal.resources.overLimit.dto.TransOverLimitReq;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.FixDepositPaymentDao;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.dto.FixDepositQueryDto;
import vn.onepay.portal.resources.payment2.guaranteeManage.dto.GuaranteeManageQueryDto;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDetailDto;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.PrefixAdvanceDto;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EReportItem;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EUposReportItem;
import vn.onepay.portal.resources.payout.bank_balance_enquiry.dto.BankBalanceQueryDto;
import vn.onepay.portal.resources.payout.bank_map.dto.BankMapQueryDto;
import vn.onepay.portal.resources.payout.merchant_balance_enquiry.dto.MerchantBalanceQueryDto;
import vn.onepay.portal.resources.payout.swift_code.dto.SwiftCodeQueryDto;
import vn.onepay.portal.resources.query_sql.dao.QuerySqlDAO;
import vn.onepay.portal.resources.reconciliation.cdr.dto.ReconciliationQueryDto;
import vn.onepay.portal.resources.reconciliation.cdr.upos.dto.ReconciliationQueryUposDownloadDto;
import vn.onepay.portal.resources.reconciliation.cdr_international.dto.ReconciliationQueryInterDto;
import vn.onepay.portal.resources.reconciliation.cdr_third_source.dto.ReconQueryThirdDto;
import vn.onepay.portal.resources.refundApproval.dto.RefundApprovalQueryDto;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.TemplateUtils;

public class ReportFileBuilder implements IConstants {
    private ReportFileBuilder() {}

    private static DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static DateFormat dfs = new SimpleDateFormat("dd-MM-yy HH:mm a");

    public static void generateInternatinalTransactionalFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INTERNATIONAL TRANSACTION FILE--------------------");
            List<Map> listMap = new InternationalTransactionalGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("HEADER", "FROM " + dfs.format(df.parse(fromDate)) + " TO " + dfs.format(df.parse(toDate)));
            // ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_INTERNATIONAL_TRANSACTION_FILE)
            // .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
            // .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
            // .exportExcel2();
            List<String> columnFormat = new ArrayList<>();
            columnFormat.add("ORIGINAL_AMOUNT");
            columnFormat.add("AMOUNT");
            columnFormat.add("N_TOTAL_REFUND");
            String[] stringArray = {"USD", "SGD", "MYR", "TWD", "CNY", "THB", "TOTAL USD", "TOTAL SGD", "TOTAL MYR", "TOTAL TWD", "TOTAL CNY", "TOTAL THB"};
            List<String> currencyFormat = Arrays.asList(stringArray);
            ReportBuilder.newInstance()
                    .template(TemplateUtils.TEMPLATE_INTERNATIONAL_TRANSACTION_FILE)
                    .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    // .setListHiddenColumn(columnInactive)
                    .setStart(7)
                    .build()
                    .exportExcelFormatAmount(columnFormat, currencyFormat, "S_CURRENCY");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transaction file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate international transaction file successfully: ");
    }

    public static void generateInternationalInstallmentlFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INTERNATIONAL INSTALLMENT FILE--------------------");
            List<Map> listMap = new InternationalInstallmentGenerator().generate(messageData.getRequestBody());

            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String range = dfs.format(df.parse(fromDate)) + " - " + dfs.format(df.parse(toDate));
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_INTERNATIONAL_INSTALLMENT_FILE)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("RANGE_DATE", range).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international installment file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate international installment file successfully: ");
    }

    public static void generateInternationalRefundApporvalFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INTERNATIONAL REFUND APPROVAL FILE--------------------");
            List<Map> listMap = new InternationalRefundApprovalGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            detailMap.put("RANGE_DATE",
                    "FROM " + dfs.format(df.parse(fromDate)) + " TO " + dfs.format(df.parse(toDate)));
            List<String> columnFormat = new ArrayList<>();
            columnFormat.add("N_AMOUNT_ORIGINAL");
            columnFormat.add("S_AMOUNT");
            String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
            List<String> currencyFormat = Arrays.asList(stringArray);
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_INTERNATIONAL_REFUND_APPROVAL_FILE_2)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcelFormatAmount(columnFormat, currencyFormat, "S_CURRENCY");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international refund approval file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate international refund approval file successfully: ");
    }

    public static void generateInternationalRefundApporvalFile2(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INTERNATIONAL REFUND APPROVAL FILE--------------------");
            List<Map> listMap = new InternationalRefundApprovalGenerator2().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            detailMap.put("RANGE_DATE",
                    "FROM " + dfs.format(df.parse(fromDate)) + " TO " + dfs.format(df.parse(toDate)));
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_INTERNATIONAL_REFUND_APPROVAL_FILE_NEW)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international refund approval file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate international refund approval file successfully: ");
    }

    public static void generateBlackListMerchantFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE BLACK LIST MERCHANT FILE--------------------");
            List<Map> listMap = new BlackListMerchantGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_BLACK_LIST_MERCHANT_FILE)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate BLACK LIST MERCHANT file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate BLACK LIST MERCHANT file successfully: ");
    }

    public static void generateDomesticTransactionalFile(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC TRANSACTION FILE--------------------");
            List<Map> listMap = new DomesticTransactionalGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE);
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE);
            detailMap.put("RANGE_DATE",
                    "FROM " + dfs.format(df.parse(fromDate)) + " TO " + dfs.format(df.parse(toDate)));
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_DOMESTIC_TRANSACTION_FILE)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic transaction file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic transaction file successfully: ");
    }

    public static void generateDomesticRefundFile(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC REFUND FILE--------------------");
            List<Map> listMap = new DomesticRefundGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE);
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE);
            detailMap.put("RANGE_DATE",
                    "FROM " + dfs.format(df.parse(fromDate)) + " - " + dfs.format(df.parse(toDate)));
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_DOMESTIC_REFUND_APPROVAL)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Refund file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Refund file successfully: ");
    }

    public static void generateDomesticRefundFile2(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC REFUND FILE--------------------");
            List<Map> listMap = new DomesticRefundGenerator2().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE);
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE);
            detailMap.put("RANGE_DATE",
                    "FROM " + dfs.format(df.parse(fromDate)) + " - " + dfs.format(df.parse(toDate)));
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_DOMESTIC_REFUND_APPROVAL)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Refund file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Refund file successfully: ");
    }

    public static void generateMerchantAccountFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE MERCHANT ACCOUNT FILE--------------------");
            List<Map> listMap = new MerchantAccountGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            Object obj1 = messageData.getRequestBody().get(IConstants.FR_DATE);
            Object obj2 = messageData.getRequestBody().get(IConstants.T_DATE);
            String fromDate = String.valueOf(obj1);
            String toDate = String.valueOf(obj2);
            // String toDate = messageData.getRequestBody().get(IConstants.TO_DATE);
            // String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE);
            detailMap.put("RANGE_DATE", "Từ ngày " + fromDate + " - đến " + toDate);
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_MERCHANT_ACCOUNT)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", listMap).setBeanValue("detailMap", detailMap).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Account file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Merchant Account file successfully: ");
    }

    public static void generateInquiryFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export Inquiry file--------------------");
            List<Map> lMaps = new InquiryGenerator().generate(messageData.getRequestBody());
            Object obj1 = messageData.getRequestBody().get(IConstants.FR_DATE);
            Object obj2 = messageData.getRequestBody().get(IConstants.T_DATE);
            String fromDate = String.valueOf(obj1);
            String toDate = String.valueOf(obj2);
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("RANGE_DATE", "Từ ngày " + fromDate + " - đến " + toDate);
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_INQUIRY)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", lMaps).setBeanValue("detailMap", lMaps).build().exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateInquiryFile file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
    }

    public static void generateDomesticReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC REPORT FILE--------------------");
            List<Map> listMap = new DomesticReportGenerator().generate(messageData.getRequestBody());

            // DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            //
            // DateFormat dfs = new SimpleDateFormat("dd-MM-yy");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String downloadType = messageData.getRequestBody().get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL
                    : messageData.getRequestBody().get(IConstants.DOWNLOADTYPE).toString();;
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String template = TemplateUtils.TEMPLATE_DOMESTIC_REPORT_FILE;
            switch (messageData.getRequestBody().get("version").toString()) {
                case "successful":
                    template = TemplateUtils.TEMPLATE_DOMESTIC_REPORT_FILE_SS;
                    break;
                case "fail":
                    template = TemplateUtils.TEMPLATE_DOMESTIC_REPORT_FILE_FA;
                    break;
                case "pending":
                    template = TemplateUtils.TEMPLATE_DOMESTIC_REPORT_FILE_PE;
                    break;
            }
            if (IConstants.CSV.equals(downloadType)) {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add("DATE");
                listHeader.add("S_MERCHANT_ID");
                listHeader.add("S_BANK_ID");
                listHeader.add("S_CAIC");
                listHeader.add("S_CONTRACT_TYPE");
                listHeader.add("S_CURRENCY");
                listHeader.add("N_PURCHASE");
                // listHeader.add("N_PURCHASE_FAIL");
                listHeader.add("N_REFUND");
                // listHeader.add("N_REFUND_FAIL");
                // listHeader.add("N_REFUND_PENDING");
                listHeader.add("N_TOTAL_PURCHASE");
                // listHeader.add("N_TOTAL_PURCHASE_FAIL");
                listHeader.add("N_TOTAL_REFUND");
                // listHeader.add("N_TOTAL_REFUND_FAIL");
                // listHeader.add("N_TOTAL_REFUND_PENDING");
                String fileTitle = "domestic_report";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                String fileHeader = "No,Date,Merchant ID,Bank,CIAC,Contract type,Currency,No Purchase,No Refund,Total Purchase,Total Refund";
                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsv(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            } else {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Report File successfully: ");
    }

    public static void generateDomesticReport2File(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC REPORT 2 FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String downloadType = messageData.getRequestBody().get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL
                    : messageData.getRequestBody().get(IConstants.DOWNLOADTYPE).toString();

            List<Map> listMap = new DomesticReportGenerator2().generate(messageData.getRequestBody());

            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String template = TemplateUtils.TEMPLATE_DOMESTIC_REPORT_FILE2;
            if (IConstants.CSV.equals(downloadType)) {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add("DATE");
                listHeader.add("S_MERCHANT_ID");
                listHeader.add("S_BANK_ID");
                listHeader.add("S_CAIC");
                listHeader.add("S_CONTRACT_TYPE");
                listHeader.add("S_CURRENCY");
                listHeader.add("N_PURCHASE");
                listHeader.add("N_TOTAL_PURCHASE");

                listHeader.add("N_PURCHASE_FAIL");
                listHeader.add("N_TOTAL_PURCHASE_FAIL");

                listHeader.add("N_REFUND");
                listHeader.add("N_TOTAL_REFUND");

                listHeader.add("N_REFUND_FAIL");
                listHeader.add("N_TOTAL_REFUND_FAIL");

                listHeader.add("N_REFUND_WFB");
                listHeader.add("N_TOTAL_REFUND_WFB");

                listHeader.add("N_REFUND_WFO");
                listHeader.add("N_TOTAL_REFUND_WFO");

                String fileTitle = "Domestic Report2";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                String fileHeader = "No,Date,Merchant ID,Bank,CIAC,Contract type,Currency,No. Purchase Successful,Total. Purchase Successful,No. Purchase Fail,Total. Purchase Fail,No. Refund Successful,Total. Refund Successful,No. Refund Fail,Total. Refund Fail,No. Refund Wait for Bank,Total. Refund Wait for Bank,No. Refund Wait for ONEPAY,Total. Refund Wait for ONEPAY";
                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsv(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            } else {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Report File successfully: ");
    }

    public static void generateVolumeAlertConfigRuleFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE VOLUME ALERT CONFIG RULE FILE--------------------");
            List<Map> listMap = new ConfigRuleGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String exportFileName = Config.getFileExportLocation() + "/"
                    + messageData.getRequestData().get(IConstants.FILE_HASH_NAME).toString();
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_VOLUME_ALERT_CONFIG_RULE_FILE)
                    .exportFileName(exportFileName).setBeanValue("listMap", listMap).setBeanValue("detail", detailMap)
                    .setStart(7).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate GENERATE VOLUME ALERT CONFIG RULE FILE Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate GENERATE VOLUME ALERT CONFIG RULE FILE successfully: ");
    }

    public static void generateInstallmentBankReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INSTALLMENT BANK REPORT FILE--------------------");
            List<Map> listMap = new InternationalInstallmentBankGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            Integer quantityApproved = Integer
                    .parseInt(messageData.getRequestBody().get("quantityApproved").toString());
            Double totalAmountApproved = Double
                    .parseDouble(messageData.getRequestBody().get("totalAmountApproved").toString());
            Integer quantityReject = Integer.parseInt(messageData.getRequestBody().get("quantityReject").toString());
            Double totalAmountReject = Double
                    .parseDouble(messageData.getRequestBody().get("totalAmountReject").toString());
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            detailMap.put("quantityApproved", quantityApproved);
            detailMap.put("totalAmountApproved", totalAmountApproved);
            detailMap.put("quantityReject", quantityReject);
            detailMap.put("totalAmountReject", totalAmountReject);
            String template = TemplateUtils.TEMPLATE_INTERNATIONAL_BANK_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Report File successfully: ");
    }

    public static void generateInstallmentMerchantReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE INSTALLMENT MERCHANT REPORT FILE--------------------");
            List<Map> listMap = new InternationalInstallmentMerchantGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String template = TemplateUtils.TEMPLATE_INTERNATIONAL_MERCHANT_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Domestic Report File successfully: ");
    }

    public static void generateQrPaymentRefundApprovalReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE QR PAYMENT REFUND APPROVAL REPORT FILE--------------------");
            List<Map> listMap = new QrPaymentRefundApprovalGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_QR_PAYMENT_REFUND_APPROVAL_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment refund approval Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate qr payment refund approval Report File successfully: ");
    }

    public static void generateQrPaymentRefundApproval2ReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE QR PAYMENT REFUND APPROVAL 2 REPORT FILE--------------------");
            List<Map> listMap = new QrPaymentRefundApproval2Generator().generate(messageData.getRequestBody());
            Map detailMap = new HashMap();
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_QR_PAYMENT_REFUND_APPROVAL2_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment refund approval 2 Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate qr payment refund approval Report 2 File successfully: ");
    }

    public static void generateQrPaymentPurchaseReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE QR PAYMENT PURCHASE REPORT FILE--------------------");
            List<Map> listMap = new QrPaymentPurchaseGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_QR_PAYMENT_PURCHASE_SEARCH_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment purchase search Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate qr payment search purchase Report File successfully: ");
    }

    public static void generateQrPaymentRefundReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE QR PAYMENT REFUND REPORT FILE--------------------");
            List<Map> listMap = new QrPaymentRefundGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_QR_PAYMENT_REFUND_SEARCH_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr refund search Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate qr refund purchase Report File successfully: ");
    }

    public static void generateQrReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE QR REPORT FILE--------------------");
            List<Map> listMap = new QrReportGenerator().generate(messageData.getRequestBody());

            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String downloadType = messageData.getRequestBody().get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL
                    : messageData.getRequestBody().get(IConstants.DOWNLOADTYPE).toString();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String template = TemplateUtils.TEMPLATE_QR_REPORT_FILE;
            if (IConstants.CSV.equals(downloadType)) {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add("DATE");
                listHeader.add("S_MERCHANT_ID");
                listHeader.add("S_BANK_ID");
                listHeader.add("S_CAIC");
                listHeader.add("S_CONTRACT_TYPE");
                listHeader.add("S_CURRENCY");
                listHeader.add("N_PURCHASE");
                // listHeader.add("N_PURCHASE_FAIL");
                listHeader.add("N_REFUND");
                // listHeader.add("N_REFUND_FAIL");
                // listHeader.add("N_REFUND_PENDING");
                listHeader.add("N_TOTAL_PURCHASE");
                // listHeader.add("N_TOTAL_PURCHASE_FAIL");
                listHeader.add("N_TOTAL_REFUND");
                // listHeader.add("N_TOTAL_REFUND_FAIL");
                // listHeader.add("N_TOTAL_REFUND_PENDING");
                String fileTitle = "domestic_report";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                String fileHeader = "No,Date,Merchant ID,Bank,CIAC,Contract type,Currency,No Purchase,No Refund,Total Purchase,Total Refund";
                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsv(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            } else {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Qr Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Qr Report File successfully: ");
    }

    public static void generateDocumentTrackingReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE DOCUMENT TRACKING REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String searchType = messageData.getRequestBody().get("search_type").toString().toUpperCase();
            String template = searchType.equals("ACCOUNTINGDATE") ? TemplateUtils.TEMPLATE_DOCUMENT_TRACKING_V2_FILE
                    : TemplateUtils.TEMPLATE_DOCUMENT_TRACKING_FILE;

            if (messageData.getRequestBody().get("typeDownload").toString().equals("ALL")) {
                List<Map> listMapHN = new DocumentTrackingHNGenerator().generate(messageData.getRequestBody());
                List<Map> listMapHCM = new DocumentTrackingHCMGenerator().generate(messageData.getRequestBody());
                if (searchType.equals("ACCOUNTINGDATE")) {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMapHN).setBeanValue("listMapHCM", listMapHCM)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                } else {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMapHN).setBeanValue("listMapHCM", listMapHCM)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                }
            } else if (messageData.getRequestBody().get("typeDownload").toString().equals("HN")) {
                List<Map> listMapHN = new DocumentTrackingHNGenerator().generate(messageData.getRequestBody());
                List<Map<String, Object>> listMap = new ArrayList<>();
                if (searchType.equals("ACCOUNTINGDATE")) {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMapHN).setBeanValue("listMapHCM", listMap)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                } else {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMapHN).setBeanValue("listMapHCM", listMap)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                }
            } else {
                List<Map> listMapHCM = new DocumentTrackingHCMGenerator().generate(messageData.getRequestBody());
                List<Map<String, Object>> listMap = new ArrayList<>();
                if (searchType.equals("ACCOUNTINGDATE")) {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMap).setBeanValue("listMapHCM", listMapHCM)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                } else {
                    ReportBuilder.newInstance().template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .setBeanValue("listMapHN", listMap).setBeanValue("listMapHCM", listMapHCM)
                            .setBeanValue("detail", detailMap).build().exportExcel();
                }
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE DOCUMENT TRACKING ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE DOCUMENT TRACKING SUCCESS: ");
    }

    public static void generateMerchantTransferReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MERCHANT TRANSFER REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_MERCHANT_TRANSFER_FILE;

            List<Map> listMap = new MerchantTransferGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE MERCHANT TRANSFER ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE MERCHANT TRANSFER SUCCESS: ");
    }

    public static void generateMerchantTopupRefundReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MERCHANT TOPUP REFUND REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_MERCHANT_TOPUP_REFUND_FILE;

            List<Map> listMap = new MerchantTopupRefundGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE MERCHANT TOPUP REFUND ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE MERCHANT TOPUP REFUND SUCCESS: ");
    }

    public static void generateFixedDepositPaymentReportFile(Message<FixDepositQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FIXED DEPOSIT PAYMENT REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().getFromDate().toString();
            String toDate = messageData.getRequestBody().getToDate().toString();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_FIXED_DEPOSIT_PAYMENT_FILE;

            List<Map> listMap = new FDPaymentGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateDomesticStatisticFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE DOMESTIC STATISTIC FILE--------------------");

            List<Map<String, Object>> listDataMap = new ArrayList<>();
            JsonObject jsonObject = new JsonObject(messageData.getRequestBody().get("data").toString());
            JsonArray arrayDate = new JsonArray(messageData.getRequestBody().get("dateFrom").toString());
            List<String> templateSheetNameList = new ArrayList<>();
            List<String> sheetNameList = new ArrayList<>();
            String splitBy = messageData.getRequestBody().get("splitBy").toString();
            String transType = messageData.getRequestBody().get("transType").toString();
            // String base64Image =
            // messageData.getRequestBody().get("image").toString().split(",")[1];
            // byte[] imageBytes = null;
            // if (base64Image != null && !base64Image.isEmpty()) {
            // imageBytes = Base64.getDecoder().decode(base64Image);
            // }

            String pathTemplate = TemplateUtils.TEMPLATE_DOMESTIC_STATISTIC_FILE
                    + jsonObject.getJsonArray("chartStatisticDownload").size() + "_temp_chart.xls";
            LinkedHashMap dataWithDay;
            Map<String, Object> transaction;
            Map<String, Object> detailMap;
            List<Map> listMap;
            List listData;
            Map<String, Object> itemClient;
            Map item;
            int index = 0;
            Date fromDate;
            Date toDate;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfDisplay;
            if (!Objects.equals(MONTHLY, splitBy)) {
                sdfDisplay = new SimpleDateFormat("ddMMyyyy");
            } else {
                sdfDisplay = new SimpleDateFormat("MMyyyy");
            }
            for (Object data : jsonObject.getJsonArray("chartStatisticDownload").getList()) {
                dataWithDay = (LinkedHashMap<?, ?>) data;
                if (dataWithDay == null) {
                    continue;
                } else {
                    Optional<?> first = dataWithDay.keySet().stream().findFirst();
                    templateSheetNameList.add("Domestic" + (index + 1));
                    JsonObject date = arrayDate.getJsonObject(index);
                    fromDate = sdf.parse(date.getString("fromDate"));
                    toDate = sdf.parse(date.getString("toDate"));
                    if (!Objects.equals(MONTHLY, splitBy)) {
                        sheetNameList.add(sdfDisplay.format(fromDate) + "_" + sdfDisplay.format(toDate));
                    } else {
                        sheetNameList.add(sdfDisplay.format(fromDate));
                    }
                    transaction = new HashMap<>();
                    detailMap = new HashMap<>();
                    detailMap.put(FROM_DATE.toUpperCase(), date.getString("fromDate"));
                    detailMap.put(TO_DATE.toUpperCase(), date.getString("toDate"));
                    switch (transType) {
                        case QT: {
                            detailMap.put(HEADER, INTERNATIONAL_HEADER);
                            break;
                        }
                        case ND: {
                            detailMap.put(HEADER, DOMESTIC_HEADER);
                            break;
                        }
                        default:
                            break;
                    }

                    listMap = new ArrayList<>();
                    listData = (List<?>) dataWithDay.get(first.get());
                    int no = 1;
                    for (int j = 0; j < listData.size(); j++) {
                        itemClient = new HashMap<>();
                        item = (Map) listData.get(j);
                        itemClient.put("NO", String.valueOf(no));
                        itemClient.put("S_MERCHANT_ID", item.get("merchantId"));
                        itemClient.put("DATE", item.get("date"));
                        itemClient.put("TRANS_REF", item.get("transRef"));
                        itemClient.put("S_ORDER_INFO", item.get("orderRef"));
                        itemClient.put("S_CARD_NO", item.get("cardNo"));
                        itemClient.put("N_BANK_ID", item.get("bankId"));
                        itemClient.put("S_RESPONSE_CODE", item.get("responseCode"));
                        itemClient.put("N_AMOUNT",
                                Convert.toString(Long.parseLong(item.get("amount").toString()), "###,###,###,###,###")
                                        + " VND");
                        itemClient.put("S_STATUS", item.get("state"));
                        listMap.add(itemClient);
                        no++;
                    }
                    transaction.put("listMap", listMap);
                    transaction.put("detail", detailMap);
                    listDataMap.add(transaction);
                }
                index++;
            }
            // templateSheetNameList.add("Chart");
            // sheetNameList.add("Biểu đồ");

            Map<String, Object> transactionChart = new HashMap<>();
            transactionChart.put("chart", sheetNameList);
            listDataMap.add(transactionChart);
            ReportBuilder.newInstance().template(pathTemplate)
                    .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                    .setListBeanValue(listDataMap)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    // .setImage(imageBytes)
                    .build().exportExcelMultilSheet();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "DOMESTIC STATISTIC ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "DOMESTIC STATISTIC SUCCESS: ");
    }

    public static void generateFixedDepositPaymentDetailReportFile(Message<Long> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FIXED DEPOSIT PAYMENT DETAIL REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            List<Map<String, Object>> listDataMap = new ArrayList<>();
            String template = TemplateUtils.TEMPLATE_FIXED_DEPOSIT_DETAIL_PAYMENT_FILE;
            List<Map> listMap = new FDPaymentDetailGenerator().generate(messageData.getRequestBody());
            long totalAmount = 0;
            for (Map map : listMap) {
                totalAmount += map.get("amount") == null ? 0 : (double) map.get("amount");
            }
            detailMap.put("total_amount", totalAmount);
            Map<String, Object> transaction = new HashMap<>();
            transaction.put("listMap", listMap);
            transaction.put("detail", detailMap);
            transaction.put("start", 8);
            listDataMap.add(transaction);
            Map<String, Object> transactionDetail = new HashMap<>();
            List<Map> listMapDetail = new FDPaymentTransactionDetailGenerator().generate(messageData.getRequestBody());
            transactionDetail.put("listMap", listMapDetail);
            transactionDetail.put("detail", detailMap);
            transactionDetail.put("start", 8);
            listDataMap.add(transactionDetail);
            List<String> templateSheetNameList = new ArrayList<>();
            templateSheetNameList.add("TRANSACTION");
            templateSheetNameList.add("DETAIL");
            List<String> sheetNameList = new ArrayList<>();
            sheetNameList.add("Tổng hợp");
            sheetNameList.add("Chi tiết");
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListBeanValue(listDataMap)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    .build().exportExcelMultilSheet();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "FIXED DEPOSIT PAYMENT DETAIL ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "FIXED DEPOSIT PAYMENT DETAIL SUCCESS: ");
    }

    public static void generateDownloadFDWordSingle(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FD WORD FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            Map<String, Object> dataMap = new HashMap<>();

            Date date = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            String today = formatter.format(date);

            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            String type = (String) mIn.get("type");
            String id = (String) selectedRows.get(0).get("id");
            JsonObject dataFDDAO = FixDepositPaymentDao.getFDSendEmail(id);
            JsonArray listFD = dataFDDAO.getJsonArray("LIST");
            String template = "";
            if (null != type && "un_fd".equalsIgnoreCase(type)) {
                template = TemplateUtils.FILE_NAME_ATACK_GIAI_KHOANH;
            } else {
                template = TemplateUtils.FILE_NAME_ATACK_KHOANH_GIU;
            }
            if (listFD.size() > 0) {
                JsonObject data;
                LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                NumberFormat currentLocale = NumberFormat.getInstance();
                for (Object object : listFD) {
                    data = (JsonObject) object;
                    StringBuilder dateDownload = new StringBuilder("ngày ").append(localDate.getDayOfMonth());
                    dateDownload.append(" tháng ").append(localDate.getMonthValue());
                    dateDownload.append(" năm ").append(localDate.getYear());
                    dataMap.put("fileDownloadDate", today);
                    dataMap.put("dateDownload", dateDownload.toString());
                    dataMap.put("contractCode", data.getString(CONTRACT_CODE));
                    dataMap.put("dateCreate", data.getString("CREATE"));
                    dataMap.put("nameDKKD", data.getString(TEN_DKKD));
                    dataMap.put("nameDV", data.getString("TEN_DV"));
                    dataMap.put("balance", currentLocale.format(data.getDouble(BALANCE)));
                    dataMap.put("totalHoldAmount", currentLocale.format(data.getDouble("TOTAL_HOLD_AMOUNT")));
                    if (null != type && "fd".equalsIgnoreCase(type)) {
                        dataMap.put("balanceText",
                                ConvertMoneyNumberToString.getVND(String.format("%.0f", data.getDouble(BALANCE))));
                    } else {
                        dataMap.put("balanceText",
                                ConvertMoneyNumberToString.getVND(String.format("%.0f", data.getDouble(BALANCE))));
                        dataMap.put("bank", data.getString("ADV_ACC_BANK_ID"));
                        dataMap.put("taikhoantamung", data.getString(ADVANCE_ACCOUNT));
                    }

                    ReportBuilder.newInstance()
                            .template(template)
                            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                            .build()
                            .exportWord(template, dataMap);
                }
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate download fd single file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate download fd single successfully");
    }

    public static void generateDownloadFDWordMultiple(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FD Word multi FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            String type = (String) mIn.get("type");
            List<String> ids = new ArrayList<>();
            for (LinkedHashMap obj : selectedRows) {
                ids.add((String) obj.get("id"));
            }
            String stringIds = String.join(",", ids);
            JsonObject dataFDDAO = FixDepositPaymentDao.getFDSendEmail(stringIds);
            JsonArray listFD = dataFDDAO.getJsonArray("LIST");
            String template = "";
            if (null != type && "un_fd".equalsIgnoreCase(type)) {
                template = TemplateUtils.FILE_NAME_ATACK_GIAI_KHOANH;
            } else {
                template = TemplateUtils.FILE_NAME_ATACK_KHOANH_GIU;
            }
            List<String> listFilePath = new ArrayList<>();
            if (listFD.size() > 0) {
                Map<String, Object> dataMap = new HashMap<>();
                Date date = new Date();
                SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                String today = formatter.format(date);
                JsonObject data;
                LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                NumberFormat currentLocale = NumberFormat.getInstance();
                for (Object object : listFD) {
                    data = (JsonObject) object;
                    StringBuilder dateDownload = new StringBuilder("ngày ").append(localDate.getDayOfMonth());
                    dateDownload.append(" tháng ").append(localDate.getMonthValue());
                    dateDownload.append(" năm ").append(localDate.getYear());
                    dataMap.put("fileDownloadDate", today);
                    dataMap.put("dateDownload", dateDownload.toString());
                    dataMap.put("contractCode", data.getString(CONTRACT_CODE));
                    dataMap.put("dateCreate", data.getString("CREATE"));
                    dataMap.put("nameDKKD", data.getString(TEN_DKKD));
                    dataMap.put("nameDV", data.getString("TEN_DV"));
                    dataMap.put("balance", currentLocale.format(data.getDouble(BALANCE)));
                    dataMap.put("totalHoldAmount", currentLocale.format(data.getDouble("TOTAL_HOLD_AMOUNT")));
                    dataMap.put("balanceText",
                            ConvertMoneyNumberToString.getVND(String.format("%.0f", data.getDouble(BALANCE))));
                    String fileName = "";
                    if (null != type && "fd".equalsIgnoreCase(type)) {
                        fileName = "khoanh_giu_";
                    } else {
                        fileName = "giai_khoanh_";
                        dataMap.put("bank", data.getString("ADV_ACC_BANK_ID"));
                        dataMap.put("taikhoantamung", data.getString(ADVANCE_ACCOUNT));
                    }
                    String filePathString = Config.getFileExportLocation() + File.separator + fileName
                            + customExportFileNameFD(data.getString("TEN_DV"), data.getString("s_pay_channel"))
                            + ".docx";
                    Files.deleteIfExists(Paths.get(filePathString));

                    ReportBuilder.newInstance()
                            .exportFileName(filePathString)
                            .template(template)
                            .build()
                            .exportWord(template, dataMap);
                    listFilePath.add(filePathString);
                }
                if (selectedRows.size() > 1) {
                    addToZipFile(listFilePath, messageData);
                }
            }

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate download fd multi file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate download fd multi file successfully");
    }

    public static void generateDownloadMultipleDisputeFiles(Message<Map<String, Object>> messageData) {
        try {
        LOGGER.log(Level.INFO, "----------START GENERATE MULTIPLE FILES IN DOWNLOAD DISPUTE----------");

        Map<String, Object> mIn = messageData.getRequestBody();
        List<Map<String, Object>> selectedRows = (ArrayList<Map<String, Object>>) mIn.get("listFileDto");
        List<String> listFilePath = new ArrayList<>();
        for (Map<String, Object> file : selectedRows) {
            String cdr_file_id = file.get(IConstants.CDR_FILE_ID).toString();

            String file_name = file.get(IConstants.CDR_FILE_NAME).toString();
            String source_name = file.get(IConstants.SOURCE_NAME).toString();
            String service_name = file.get(IConstants.SERVICE_NAME).toString();

            String d_cdr = file.get(IConstants.CDR_FILE_D_CDR).toString();
            String d_import = file.get(IConstants.CDR_FILE_D_IMPORT).toString();
            String d_create = file.get(IConstants.CDR_FILE_D_CREATE).toString();

            String s_header = file.get(IConstants.CDR_FILE_HEADER) == null ? ""
                    : file.get(IConstants.CDR_FILE_HEADER).toString();
            Object download_data_only = file.get(IConstants.DOWNLOAD_DATA_ONLY);
            boolean useTemplate = download_data_only != null ? !((boolean) download_data_only) : true;
            String file_ext = FilenameUtils.getExtension(file_name);
            file_ext = file_ext.isEmpty() ? "txt" : file_ext;

            String template = TemplateUtils.TEMPLATE_INFO_CDR_FILE_RECONCILIATION;
            Map<String, String> detailMap = new HashMap<>();
            detailMap.put("FILE_NAME", file_name);
            detailMap.put("SOURCE_NAME", source_name);
            detailMap.put("SERVICE_NAME", service_name);
            detailMap.put("D_CDR", d_cdr);
            detailMap.put("D_IMPORT", d_import);
            detailMap.put("D_CREATE", d_create);

            List<Map> listMap = new CDRFileGenerator().generate2(cdr_file_id);
            if (useTemplate) {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(file_name)
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(9).build()
                        .exportExcel2();
            } else {
                List<Map> listM = new ArrayList<Map>();
                Map mapHeader = new HashMap();
                boolean lineContainsHeader = true;

                String line1 = String.valueOf((listMap.get(0)).get("S_LINE"));
                if (!line1.equals(s_header) && !s_header.isEmpty()) {
                    mapHeader.put("S_LINE", s_header.replace("\"", ""));
                    listM.add(mapHeader);
                    lineContainsHeader = false;
                }

                if (lineContainsHeader) {
                    listM = listMap;
                } else {
                    for (int i = 0; i < listMap.size(); i++) {
                        listM.add(listMap.get(i));
                    }
                }

                ReportBuilder.newInstance()
                        .exportFileName(file_name)
                        .setStart(9).build()
                        .exportDataOnly(file_name, listM, file_ext);
                
                listFilePath.add(file_name);
            }
        }
        addToZipFile(listFilePath, messageData);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "GENERATE MULTIPLE FILES DISPUTE ERROR: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE MULTIPLE FILES DISPUTE SUCCESSFULLY");
    }

    public static String customExportFileNameFD(String partner, String payChannels) {
        return "FD_" +
                partner + "_" +
                payChannels + "_" +
                System.currentTimeMillis();
    }

    public static void generateFixedDepositReportFile(Message<FixedDepositQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE FIXED DEPOSIT REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().getFromDate();
            String toDate = messageData.getRequestBody().getToDate();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_FIXED_DEPOSIT_FILE;

            List<Map> listMap = new FixedDepositGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateFixedDepositReportFilePdf(Message<FixedDepositQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE FIXED DEPOSIT REPORT FILE--------------------");
            List<Map> listMap = new UNCPdfGenerator().generate(messageData.getRequestBody());
            String template = TemplateUtils.TEMPLATE_UNC_JRXML;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    // .setParameters(parameters)
                    .setListMap(listMap).build().exportPdf();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateFixedDeposit2ReportFile(Message<GuaranteeManageQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FIXED DEPOSIT 2 REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().getFromDate().toString();
            String toDate = messageData.getRequestBody().getToDate().toString();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_FIXED_DEPOSIT_FILE;

            List<Map> listMap = new GuaranteeGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateFixedDeposit2ReportFileSelected(Message<GuaranteeManageQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FIXED DEPOSIT 2 REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            // TOTO: download selected
            String fromDate = messageData.getRequestBody().getFromDate() == null ? ""
                    : messageData.getRequestBody().getFromDate();
            String toDate = messageData.getRequestBody().getToDate() == null ? ""
                    : messageData.getRequestBody().getToDate();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_FIXED_DEPOSIT_FILE;

            List<Map> listMap = new GuaranteeGenerator().generateSelected(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateFixedDeposit2ReportFilePdf(Message<GuaranteeManageQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FIXED DEPOSIT 2 REPORT FILE--------------------");
            List<Map> listMap = new UNCPdfGurantee().generate(messageData.getRequestBody());
            String template = TemplateUtils.TEMPLATE_UNC_JRXML;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    // .setParameters(parameters)
                    .setListMap(listMap).build().exportPdf();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE FIXED DEPOSIT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE FIXED DEPOSIT SUCCESS: ");
    }

    public static void generateRefundApprovalReportFile(Message<RefundApprovalQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE REFUND APPROVAL REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().getFromDate();
            String toDate = messageData.getRequestBody().getToDate();
            detailMap.put("RANGE_DATE", "From " + fromDate + " To " + toDate);
            String template = TemplateUtils.TEMPLATE_REFUND_APPROVAL_FILE;
            if ("QT".equals(messageData.getRequestBody().getPayChannel())
                    || "CUP".equals(messageData.getRequestBody().getPayChannel())) {
                template = TemplateUtils.TEMPLATE_REFUND_APPROVAL_FILE_QT;
            } else if ("ND".equals(messageData.getRequestBody().getPayChannel())) {
                template = TemplateUtils.TEMPLATE_REFUND_APPROVAL_FILE_ND;
            } else if ("MPAY".equals(messageData.getRequestBody().getPayChannel())) {
                template = TemplateUtils.TEMPLATE_REFUND_APPROVAL_FILE_MPAY;
            }

            List<Map> listMap = new RefundApprovalGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE REFUND APPROVAL ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE REFUND APPROVAL SUCCESS: ");
    }

    public static void generateCDRFileDownload(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CDR FILE--------------------");
            String cdr_file_id = messageData.getRequestBody().get(IConstants.CDR_FILE_ID).toString();

            String file_name = messageData.getRequestBody().get(IConstants.CDR_FILE_NAME).toString();
            String source_name = messageData.getRequestBody().get(IConstants.SOURCE_NAME).toString();
            String service_name = messageData.getRequestBody().get(IConstants.SERVICE_NAME).toString();

            String d_cdr = messageData.getRequestBody().get(IConstants.CDR_FILE_D_CDR).toString();
            String d_import = messageData.getRequestBody().get(IConstants.CDR_FILE_D_IMPORT).toString();
            String d_create = messageData.getRequestBody().get(IConstants.CDR_FILE_D_CREATE).toString();
            String s_header = messageData.getRequestBody().get(IConstants.CDR_FILE_HEADER) == null ? ""
                    : messageData.getRequestBody().get(IConstants.CDR_FILE_HEADER).toString();
            Object download_data_only = messageData.getRequestBody().get(IConstants.DOWNLOAD_DATA_ONLY);
            boolean useTemplate = download_data_only != null ? !((boolean) download_data_only) : true;
            String file_ext = FilenameUtils.getExtension(file_name);
            file_ext = file_ext.isEmpty() ? "txt" : file_ext;
            String template = TemplateUtils.TEMPLATE_INFO_CDR_FILE_RECONCILIATION;
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FILE_NAME", file_name);
            detailMap.put("SOURCE_NAME", source_name);
            detailMap.put("SERVICE_NAME", service_name);
            detailMap.put("D_CDR", d_cdr);
            detailMap.put("D_IMPORT", d_import);
            detailMap.put("D_CREATE", d_create);

            List<Map> listMap = new CDRFileGenerator().generate(cdr_file_id);

            if (useTemplate) {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(9).build()
                        .exportExcel2();
            } else {
                List<Map> listM = new ArrayList<>();
                Map<String, Object> mapHeader = new HashMap<>();
                boolean lineContainsHeader = true;

                String line1 = String.valueOf((listMap.get(0)).get("S_LINE"));
                if (!line1.equals(s_header) && !s_header.isEmpty()) {
                    mapHeader.put("S_LINE", s_header.replaceAll("\"", ""));
                    listM.add(mapHeader);
                    lineContainsHeader = false;
                }

                if (lineContainsHeader) {
                    listM = listMap;
                } else {
                    for (int i = 0; i < listMap.size(); i++) {
                        listM.add(listMap.get(i));
                    }
                }

                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setStart(9).build()
                        .exportDataOnly(file_name, listM, file_ext);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generateCDRFileDownload2(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CDR FILE 2--------------------");
            String cdr_file_id = messageData.getRequestBody().get(IConstants.CDR_FILE_ID).toString();

            String file_name = messageData.getRequestBody().get(IConstants.CDR_FILE_NAME).toString();
            String source_name = messageData.getRequestBody().get(IConstants.SOURCE_NAME).toString();
            String service_name = messageData.getRequestBody().get(IConstants.SERVICE_NAME).toString();

            String d_cdr = messageData.getRequestBody().get(IConstants.CDR_FILE_D_CDR).toString();
            String d_import = messageData.getRequestBody().get(IConstants.CDR_FILE_D_IMPORT).toString();
            String d_create = messageData.getRequestBody().get(IConstants.CDR_FILE_D_CREATE).toString();

            String s_header = messageData.getRequestBody().get(IConstants.CDR_FILE_HEADER) == null ? ""
                    : messageData.getRequestBody().get(IConstants.CDR_FILE_HEADER).toString();
            Object download_data_only = messageData.getRequestBody().get(IConstants.DOWNLOAD_DATA_ONLY);
            boolean useTemplate = download_data_only != null ? !((boolean) download_data_only) : true;
            String file_ext = FilenameUtils.getExtension(file_name);
            file_ext = file_ext.isEmpty() ? "txt" : file_ext;

            String template = TemplateUtils.TEMPLATE_INFO_CDR_FILE_RECONCILIATION;
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FILE_NAME", file_name);
            detailMap.put("SOURCE_NAME", source_name);
            detailMap.put("SERVICE_NAME", service_name);
            detailMap.put("D_CDR", d_cdr);
            detailMap.put("D_IMPORT", d_import);
            detailMap.put("D_CREATE", d_create);

            List<Map> listMap = new CDRFileGenerator().generate2(cdr_file_id);
            if (useTemplate) {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(9).build()
                        .exportExcel2();
            } else {
                List<Map> listM = new ArrayList<>();
                Map<String, Object> mapHeader = new HashMap<>();
                boolean lineContainsHeader = true;

                String line1 = String.valueOf((listMap.get(0)).get("S_LINE"));
                if (!line1.equals(s_header) && !s_header.isEmpty()) {
                    mapHeader.put("S_LINE", s_header.replace("\"", ""));
                    listM.add(mapHeader);
                    lineContainsHeader = false;
                }

                if (lineContainsHeader) {
                    listM = listMap;
                } else {
                    for (int i = 0; i < listMap.size(); i++) {
                        listM.add(listMap.get(i));
                    }
                }

                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setStart(9).build()
                        .exportDataOnly(file_name, listM, file_ext);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE 2 ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE 2 SUCCESS: ");
    }

    public static void generateReconciliationDownload(Message<ReconciliationQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE RECONCILIATION FILE--------------------");
            ReconciliationQueryDto dto = messageData.getRequestBody();
            String template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION;
            if ("AGRIBANK-DIRECT-ST".equals(dto.getP_service())) {
                template = TemplateUtils.TEMPLATE_AGRIBANK_DIRECT_ST_FILE_RECONCILIATION;
            }
            if ("CUP-ALL".equals(dto.getP_service())) {
                template = TemplateUtils.TEMPLATE_CUP_ALL_FILE_RECONCILIATION;
            }
            if ("BIDV-PAYCOLLECT".equals(dto.getP_service()) || "VPB-PAYCOLLECT2".equals(dto.getP_service()) || "VCB-PAYCOLLECT".equals(dto.getP_service())|| "MSB-PAYCOLLECT".equals(dto.getP_service())) {
                template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_PAYCOLLECT;
            }
            if ("MOMO-MPAY-PM".equals(dto.getP_service())) {
                template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_SOURCE;
            }
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("fromDate", dto.getV_from_date());
            detailMap.put("toDate", dto.getV_to_date());
            detailMap.put("sourceLeft", dto.getP_source_left());
            detailMap.put("sourceRight", dto.getP_source_right());
            detailMap.put("service", dto.getP_service());
            detailMap.put("pan", dto.getP_info());
            detailMap.put("state", dto.getP_state());
            Long amt_left = 0L;
            Long amt_right = 0L;
            try {
                if (dto.getTotal_amt_source_left() != null)
                    amt_left = Long.parseLong(dto.getTotal_amt_source_left());
                if (dto.getTotal_amt_source_right() != null)
                    amt_right = Long.parseLong(dto.getTotal_amt_source_right());
            } catch (Exception e) {

            }
            detailMap.put("sumTransAmountLeft", "VND" + Convert.toString(amt_left, "###,##0.##"));
            detailMap.put("sumTransAmountRight", "VND" + Convert.toString(amt_right, "###,##0.##"));

            List<Map> listMap = new ReconciliationGenerator().generate(dto);
            detailMap.put("trans", listMap);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("Params", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generateReconciliationInterDownload(Message<ReconciliationQueryInterDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE RECONCILIATION INTERNATIONAL FILE--------------------");
            ReconciliationQueryInterDto dto = messageData.getRequestBody();
            String template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_INTER;
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("fromDate", dto.getV_from_date());
            detailMap.put("toDate", dto.getV_to_date());
            detailMap.put("source1", dto.getP_source_left());
            detailMap.put("source2", dto.getP_source_right());
            detailMap.put("service", dto.getP_service());
            detailMap.put("pan", dto.getP_info());
            detailMap.put("state", dto.getP_state());
            Long amt_left = 0L;
            Long amt_right = 0L;
            try {
                if (dto.getTotal_amt_source_left() != null){
                    amt_left = Long.parseLong(dto.getTotal_amt_source_left());
                    if(dto.getTotal_amt_refund_left() != null){
                        amt_left = amt_left - Long.parseLong(dto.getTotal_amt_refund_left());
                    }
                }
                if (dto.getTotal_amt_source_right() != null){
                    amt_right = Long.parseLong(dto.getTotal_amt_source_right());
                    if(dto.getTotal_amt_refund_right() != null){
                        amt_right = amt_right- Long.parseLong(dto.getTotal_amt_refund_right());
                    }
                }
            } catch (Exception e) {

            }
            detailMap.put("sumTransAmount1", "VND" + Convert.toString(amt_left, "###,##0.##"));
            detailMap.put("sumTransAmount2", "VND" + Convert.toString(amt_right, "###,##0.##"));

            List<Map> listMap = new ReconciliationInterGenerator().generate(dto);
            detailMap.put("trans", listMap);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("Params", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generateReconciliationThirdDownload(Message<ReconQueryThirdDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE RECONCILIATION FILE THIRD--------------------");
            ReconQueryThirdDto dto = messageData.getRequestBody();
            String template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_THIRD;
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("fromDate", dto.getV_from_date());
            detailMap.put("toDate", dto.getV_to_date());
            detailMap.put("sourceLeft", dto.getP_source_left());
            detailMap.put("sourceRight", dto.getP_source_right());
            detailMap.put("service", dto.getP_service());
            detailMap.put("pan", dto.getP_info());
            detailMap.put("state", dto.getP_state());

            detailMap.put("source_ob", Util.nvl(dto.getSource_ob(), ""));
            detailMap.put("source_os", Util.nvl(dto.getSource_os(), ""));
            detailMap.put("source_bs", Util.nvl(dto.getSource_bs(), ""));

            Long amt_left = 0L;
            Long amt_right = 0L;
            try {
                if (dto.getTotal_amt_source_left() != null)
                    amt_left = Long.parseLong(dto.getTotal_amt_source_left());
                if (dto.getTotal_amt_source_right() != null)
                    amt_right = Long.parseLong(dto.getTotal_amt_source_right());
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "GENERATE CDR FILE THIRD PARSE EXCEPTION: ", e);
            }
            detailMap.put("sumTransAmountLeft", "VND" + Convert.toString(amt_left, "###,##0.##"));
            detailMap.put("sumTransAmountRight", "VND" + Convert.toString(amt_right, "###,##0.##"));

            List<Map> listMap = new ReconciliationThirdGenerator().generate(dto);
            detailMap.put("trans", listMap);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("Params", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE THIRD ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE THIRD SUCCESS: ");
    }

    public static void generateReconciliationUposDownload(Message<ReconciliationQueryUposDownloadDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE RECONCILIATION FILE--------------------");
            ReconciliationQueryUposDownloadDto dto = messageData.getRequestBody();
            String template = "";
            if (dto.getP_service() != null && dto.getP_service().contains("REFUND"))
                template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_UPOS_REFUND;
            else
                template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_UPOS;
            // if ("AGRIBANK-DIRECT-ST".equals(dto.getP_service())) {
            // template = TemplateUtils.TEMPLATE_AGRIBANK_DIRECT_ST_FILE_RECONCILIATION;
            // }
            // if ("CUP-ALL".equals(dto.getP_service())) {
            // template = TemplateUtils.TEMPLATE_CUP_ALL_FILE_RECONCILIATION;
            // }
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("fromDate", dto.getV_from_date());
            detailMap.put("toDate", dto.getV_to_date());
            detailMap.put("sourceLeft", dto.getP_source_left());
            detailMap.put("sourceRight", dto.getP_source_right());
            detailMap.put("service", dto.getP_service());
            // detailMap.put("pan", dto.getP_info());
            detailMap.put("state", dto.getP_state());

            String dateTypeString;
            if ("1".equalsIgnoreCase(dto.getDate_type()))
                dateTypeString = "Settlement Date";
            else
                dateTypeString = "Transaction Date";

            String totalLeftPurchase = dto.getTotal_left_purchase() != null || !dto.getTotal_left_purchase().isEmpty()
                    ? dto.getTotal_left_purchase()
                    : "0";
            String totalLeftRefund = dto.getTotal_left_refund() != null || !dto.getTotal_left_refund().isEmpty()
                    ? dto.getTotal_left_refund()
                    : "0";
            String totalAmtLeftPurchase = dto.getTotal_amt_left_purchase() != null
                    || !dto.getTotal_amt_left_purchase().isEmpty() ? dto.getTotal_amt_left_purchase() : "0";
            String totalAmtLeftRefund = dto.getTotal_amt_left_refund() != null
                    || !dto.getTotal_amt_left_refund().isEmpty() ? dto.getTotal_amt_left_refund() : "0";

            String totalPurchaseMatch = dto.getTotal_purchase_match() != null
                    || !dto.getTotal_purchase_match().isEmpty() ? dto.getTotal_purchase_match() : "0";
            String totalPurchaseNotMatch = dto.getTotal_purchase_not_match() != null
                    || !dto.getTotal_purchase_not_match().isEmpty() ? dto.getTotal_purchase_not_match() : "0";
            String totalRefundMatch = dto.getTotal_refund_match() != null || !dto.getTotal_refund_match().isEmpty()
                    ? dto.getTotal_refund_match()
                    : "0";
            String totalRefundNotMatch = dto.getTotal_refund_not_match() != null
                    || !dto.getTotal_refund_not_match().isEmpty() ? dto.getTotal_refund_not_match() : "0";

            String totalRightPurchase = dto.getTotal_right_purchase() != null
                    || !dto.getTotal_right_purchase().isEmpty() ? dto.getTotal_right_purchase() : "0";
            String totalRightRefund = dto.getTotal_right_refund() != null || !dto.getTotal_right_refund().isEmpty()
                    ? dto.getTotal_right_refund()
                    : "0";
            String totalAmtRightPurchase = dto.getTotal_amt_right_purchase() != null
                    || !dto.getTotal_amt_right_purchase().isEmpty() ? dto.getTotal_amt_right_purchase() : "0";
            String totalAmtRightRefund = dto.getTotal_amt_right_refund() != null
                    || !dto.getTotal_amt_right_refund().isEmpty() ? dto.getTotal_amt_right_refund() : "0";

            String totalSettlementLeft = dto.getTotal_settlement_left() != null
                    || !dto.getTotal_settlement_left().isEmpty() ? dto.getTotal_settlement_left() : "0";
            String totalAmtSettlementLeft = dto.getTotal_amt_settlement_left() != null
                    || !dto.getTotal_amt_settlement_left().isEmpty() ? dto.getTotal_amt_settlement_left() : "0";
            String totalSettlementRight = dto.getTotal_settlement_right() != null
                    || !dto.getTotal_settlement_right().isEmpty() ? dto.getTotal_settlement_right() : "0";
            String totalAmtSettlementRight = dto.getTotal_amt_settlement_right() != null
                    || !dto.getTotal_amt_settlement_right().isEmpty() ? dto.getTotal_amt_settlement_right() : "0";

            // format amount
            totalAmtLeftPurchase = "VND " + Convert.toString(Long.parseLong(totalAmtLeftPurchase), "###,##0.##");
            totalAmtLeftRefund = "VND " + Convert.toString(Long.parseLong(totalAmtLeftRefund), "###,##0.##");
            totalAmtRightPurchase = "VND " + Convert.toString(Long.parseLong(totalAmtRightPurchase), "###,##0.##");
            totalAmtSettlementLeft = "VND " + Convert.toString(Long.parseLong(totalAmtSettlementLeft), "###,##0.##");
            totalAmtSettlementRight = "VND " + Convert.toString(Long.parseLong(totalAmtSettlementRight), "###,##0.##");

            detailMap.put("dateTypeString", dateTypeString);

            detailMap.put("totalLeftPurchase", totalLeftPurchase);
            detailMap.put("totalLeftRefund", totalLeftRefund);
            detailMap.put("totalAmtLeftPurchase", totalAmtLeftPurchase);
            detailMap.put("totalAmtLeftRefund", totalAmtLeftRefund);

            detailMap.put("totalPurchaseMatch", totalPurchaseMatch);
            detailMap.put("totalPurchaseNotMatch", totalPurchaseNotMatch);
            detailMap.put("totalRefundMatch", totalRefundMatch);
            detailMap.put("totalRefundNotMatch", totalRefundNotMatch);

            detailMap.put("totalRightPurchase", totalRightPurchase);
            detailMap.put("totalRightRefund", totalRightRefund);
            detailMap.put("totalAmtRightPurchase", totalAmtRightPurchase);
            detailMap.put("totalAmtRightRefund", totalAmtRightRefund);

            detailMap.put("totalSettlementLeft", totalSettlementLeft);
            detailMap.put("totalAmtSettlementLeft", totalAmtSettlementLeft);
            detailMap.put("totalSettlementRight", totalSettlementRight);
            detailMap.put("totalAmtSettlementRight", totalAmtSettlementRight);

            Long amt_left = 0L;
            Long amt_right = 0L;
            try {
                if (dto.getTotal_amt_source_left() != null)
                    amt_left = Long.parseLong(dto.getTotal_amt_source_left());
                if (dto.getTotal_amt_source_right() != null)
                    amt_right = Long.parseLong(dto.getTotal_amt_source_right());
            } catch (Exception e) {

            }
            detailMap.put("sumTransAmountLeft", "VND" + Convert.toString(amt_left, "###,##0.##"));
            detailMap.put("sumTransAmountRight", "VND" + Convert.toString(amt_right, "###,##0.##"));

            List<Map> listMap = new ReconciliationUposGenerator().generate(dto);
            detailMap.put("trans", listMap);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("Params", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generateBNPLReconciliationDownload(Message<ReconciliationQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE RECONCILIATION FILE--------------------");
            ReconciliationQueryDto dto = messageData.getRequestBody();
            String template = TemplateUtils.TEMPLATE_COMMON_FILE_RECONCILIATION_BNPL;
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("fromDate", dto.getV_from_date());
            detailMap.put("toDate", dto.getV_to_date());
            detailMap.put("sourceLeft", dto.getP_source_left());
            detailMap.put("sourceRight", dto.getP_source_right());
            detailMap.put("service", dto.getP_service());
            detailMap.put("pan", dto.getP_info());
            detailMap.put("state", dto.getP_state());
            Long amt_left = 0L;
            Long amt_right = 0L;
            Long trans_amt_left = 0L;
            Long trans_amt_right = 0L;
            try {
                if (dto.getTotal_amt_source_left() != null)
                    amt_left = Long.parseLong(dto.getTotal_amt_source_left());
                if (dto.getTotal_amt_source_right() != null)
                    amt_right = Long.parseLong(dto.getTotal_amt_source_right());
                if (dto.getTotal_trans_amt_source_left() != null)
                    trans_amt_left = Long.parseLong(dto.getTotal_trans_amt_source_left());
                if (dto.getTotal_trans_amt_source_right() != null)
                    trans_amt_right = Long.parseLong(dto.getTotal_trans_amt_source_right());
            } catch (Exception e) {

            }
            detailMap.put("sumAmountLeft", "VND" + Convert.toString(amt_left, "###,##0.##"));
            detailMap.put("sumAmountRight", "VND" + Convert.toString(amt_right, "###,##0.##"));
            detailMap.put("sumTransAmountLeft", "VND" + Convert.toString(trans_amt_left, "###,##0.##"));
            detailMap.put("sumTransAmountRight", "VND" + Convert.toString(trans_amt_right, "###,##0.##"));
            List<Map> listMap = new ReconciliationBNPLGenerator().generate(dto);
            detailMap.put("trans", listMap);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("Params", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generateConfig(Message<ConfigQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CONFIG FILE--------------------");
            List<?> listMap = new IndebtednessConfigGenerator().generate(messageData.getRequestBody());
            LOGGER.log(Level.INFO, "Found: " + listMap.size());
            Map<String, Object> detailMap = new HashMap<>();
            String exportFileName = Config.getFileExportLocation() + "/"
                    + messageData.getRequestData().get(IConstants.FILE_HASH_NAME).toString();
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_CONFIG).exportFileName(exportFileName)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Config file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Config file successfully: ");
    }

    public static void generateConfig118(Message<ConfigQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CONFIG FILE 118--------------------");
            List<?> listMap = new IndebtednessConfigGenerator118().generate(messageData.getRequestBody());
            LOGGER.log(Level.INFO, "Found: " + listMap.size());
            Map<String, Object> detailMap = new HashMap<>();
            String exportFileName = messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString();
            LOGGER.log(Level.INFO, "File Name: " + exportFileName);
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_CONFIG).exportFileName(exportFileName)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Config file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Config file successfully: ");
    }

    public static void generateTransactionHistoryReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE TRANSACTION HISTORY REPORT FILE--------------------");
            List<Map> listMap = new TransactionHistoryGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            detailMap.put("HEADER", "FROM " + fromDate + " TO " + toDate);
            if (!listMap.isEmpty()) {
                Map mapAmount = listMap.get(listMap.size() - 1);
                detailMap.putAll(mapAmount);
                listMap.remove(listMap.size() - 1);
            } else {
                detailMap.put("totalAmount", 0);
            }
            String template = TemplateUtils.TEMPLATE_PAY_COLLECT_TRANSACTION_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE TRANSACTION HISTORY Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE TRANSACTION HISTORY successfully: ");
    }

    public static void generatePayCollectReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE PAY COLLECT REPORT FILE--------------------");
            List<Map> listMap = new PayCollectReportGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            detailMap.put("HEADER", "FROM " + fromDate + " TO " + toDate);
            if (!listMap.isEmpty()) {
                Map mapCount = listMap.get(listMap.size() - 2);
                Map mapAmount = listMap.get(listMap.size() - 1);
                detailMap.putAll(mapCount);
                detailMap.putAll(mapAmount);
                listMap.remove(listMap.size() - 2);
                listMap.remove(listMap.size() - 1);
            } else {
                detailMap.put("totalCount", 0);
                detailMap.put("totalAmount", 0);
            }
            String template = TemplateUtils.TEMPLATE_PAY_COLLECT_REPORT_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString()).setStart(7)
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel2();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE CDR FILE ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE CDR FILE SUCCESS: ");
    }

    public static void generatePayoutMerchantConfigReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT MERCHANT CONFIG REPORT FILE--------------------");
            List<Map> listMap = new MerchantConfigGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_CONFIG_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout merchant config Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate payout merchant config Report File successfully: ");
    }

    public static void generatePayoutBankConfigReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT BANK CONFIG REPORT FILE--------------------");
            List<Map> listMap = new BankConfigGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_CONFIG_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout bank config Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate payout bank config Report File successfully: ");
    }

    public static void generatePayoutBankTopUpReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT BANK TOPUP REPORT FILE--------------------");
            List<Map> listMap = new BankTopUpGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString() + " 00:00 AM";
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString() + " 11:59 PM";
            String typeDownload = messageData.getRequestBody().get(IConstants.TYPE_DOWNLOAD).toString();
            if (typeDownload.equals("xls")) {
                detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
                String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_TOPUP_FILE;
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                        .exportExcel2();
            } else {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add(TemplateUtils.ID);
                listHeader.add(TemplateUtils.SWIFT_CODE);
                listHeader.add(TemplateUtils.NAME);
                listHeader.add(TemplateUtils.BANK_TXN_REF);
                listHeader.add(TemplateUtils.BEFORE_BALANCE);
                listHeader.add(TemplateUtils.AFTER_BALANCE);
                listHeader.add(TemplateUtils.AMOUNT);
                listHeader.add(TemplateUtils.CURRENCY);
                listHeader.add(TemplateUtils.STATE);
                listHeader.add(TemplateUtils.STYPE);
                listHeader.add(TemplateUtils.CREATE);
                listHeader.add(TemplateUtils.UPDATE);
                listHeader.add("D_FUND");
                listHeader.add(TemplateUtils.BANK_TOPUP);

                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("D_DATE", "dd/MM/yyyy HH:mm:ss");
                mapDate.put("D_SETTLEMENT", "dd/MM/yyyy");
                Map parameters = new HashMap<>();
                String fileTitle = "BANK TOPUP";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                String fileHeader = "No,OP Transaction ID,Swift Code,Bank Name,Bank Trans Ref,Balance Before,Topup Amount,Balance After,Currency ,State,Type,Topup Bank Date,Create Date,Update Date,Fund Date,";
                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setParameters(parameters).build()
                        .exportCsvFile(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout bank topup Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Bank topUp Report File successfully: ");
    }

    public static void generatePayoutMerchantAccBalanceFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT MERCHANT ACCOUNT BALANCE FILE--------------------");
            List<Map> listMap = new MerchantAccBalanceGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_ACC_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout merchant account balance file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate  Merchant account balance File successfully: ");
    }

    public static void generatePayoutMerchantTopUpReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT MERCHANT TOPUP REPORT FILE--------------------");
            List<Map> listMap = new MerchantTopUpGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String typeDownload = messageData.getRequestBody().get(IConstants.TYPE_DOWNLOAD).toString();
            if (typeDownload.equals("xls")) {
                detailMap.put("RANGE_DATE", "FROM " + fromDate + " 00:00 AM " + toDate + " 11:59 PM");
                String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_TOPUP_FILE;
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                        .exportExcel2();
            } else {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add(TemplateUtils.ID);
                listHeader.add(TemplateUtils.TO_MERCHANT);
                listHeader.add(TemplateUtils.BUSINESS_NAME);
                listHeader.add(TemplateUtils.TO_ACCOUNT);
                listHeader.add(TemplateUtils.BANK_NAME);
                listHeader.add(TemplateUtils.BANK_TXN_REF);
                listHeader.add(TemplateUtils.BEFORE_BALANCE);
                listHeader.add(TemplateUtils.AMOUNT);
                listHeader.add(TemplateUtils.AFTER_BALANCE);
                listHeader.add(TemplateUtils.CURRENCY);
                listHeader.add(TemplateUtils.STATE);
                listHeader.add(TemplateUtils.STYPE);
                listHeader.add(TemplateUtils.CREATE);
                listHeader.add(TemplateUtils.UPDATE);
                listHeader.add("D_MERCHANT_TOPUP");
                listHeader.add("D_FUND");
                listHeader.add("S_PV_NO");
                listHeader.add("BANK_TOPUP");
                listHeader.add(TemplateUtils.OPERATOR);

                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("D_DATE", "dd/MM/yyyy HH:mm:ss");
                mapDate.put("D_SETTLEMENT", "dd/MM/yyyy");
                Map parameters = new HashMap<>();
                String fileTitle = "MERCHANT TOPUP";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                String fileHeader = "No,Transaction Id,Merchant Name,Business Name,Merchant Account,Bank Name,Bank Trans Ref,Balance Before,Topup Amount ,Balance After,Currency,State,Source,Topup Merchant Date,Create Date,Update Date,Fund Date,PV No.,Bank Topup,Operator";
                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setParameters(parameters).build()
                        .exportCsvFile(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout merchant topUp Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate  Merchant TopUp Report File successfully: ");
    }

    public static void generatePayoutSummaryFundTranferReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Summary FundTranfer Report File--------------------");
            List<Map> listMap = new SummaryFundsTranferGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            detailMap.put("TIME_INTERVAL", messageData.getRequestBody().get("time_interval_convert").toString());
            String template = TemplateUtils.SUMMARY_FUNDS_TRANFER_TEMPLATE_SRC;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Summary FundTranfer Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Summary FundTranfer Report File successfully: ");
    }

    public static void generatePayoutBankTranfersReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Bank Transfer Report File--------------------");
            List<Map> listMap = new BankTransferReportGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            Date fromDate = (Date) messageData.getRequestBody().get(IConstants.FROM_DATE);
            Date toDate = (Date) messageData.getRequestBody().get(IConstants.TO_DATE);
            detailMap.put("FROM_DATE", DateTimeUtil.convertDatetoString(fromDate, DateTemplate.DD_MM_YYYY_hh_mm_ss_a));
            detailMap.put("TO_DATE", DateTimeUtil.convertDatetoString(DateUtils.addSeconds(toDate, -1),
                    DateTemplate.DD_MM_YYYY_hh_mm_ss_a));

            String typeDownload = messageData.getRequestBody().get(IConstants.TYPE_DOWNLOAD).toString();
            if (typeDownload.equals("xls")) {

                String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_TRANSFER_FILE;
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
            } else {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add("S_DATE_TYPE");
                listHeader.add("S_BANK_NAME");
                listHeader.add("S_SWIFT_CODE");
                listHeader.add("N_OPEN_BALANCE");
                listHeader.add("N_CLOSE_BALANCE");
                listHeader.add("N_INCREASE_TOTAL");
                listHeader.add("N_INCREASE_AMOUNT");
                listHeader.add("N_DECREASE_TOTAL");
                listHeader.add("N_DECREASE_AMOUNT");
                listHeader.add("N_TOPUP_TOTAL");
                listHeader.add("N_TOPUP_AMOUNT");
                listHeader.add("N_REVERSAL_TOTAL");
                listHeader.add("N_REVERSAL_AMOUNT");
                listHeader.add("N_APPROVED_TOTAL");
                listHeader.add("N_APPROVED_AMOUNT");
                listHeader.add("N_PENDING_TOTAL");
                listHeader.add("N_PENDING_AMOUNT");
                listHeader.add("N_FAILED_TOTAL");
                listHeader.add("N_FAILED_AMOUNT");
                listHeader.add("N_WAIT_REVERSAL_TOTAL");
                listHeader.add("N_WAIT_REVERSAL_AMOUNT");
                listHeader.add("N_REVERTED_TOTAL");
                listHeader.add("N_REVERTED_AMOUNT");
                listHeader.add("N_FEE_TOTAL");
                listHeader.add("N_FEE_AMOUNT");

                Map<String, String> mapDate = new HashMap<>();
                Map parameters = new HashMap<>();
                String fileTitle = "BANK TRANSFER REPORT";
                String fileDate = "FROM DATE " + detailMap.get("FROM_DATE") + " TO DATE " + detailMap.get("TO_DATE");
                String fileHeader = "No,Date Type,Bank Name,Swift Code,Bank Opening Balance(1),Bank Closing Balance(2),Total Increase Transaction(4),Total Increase Amount(4),Total Decrease Transaction(5),Total Decrease Amount(6),Topup Number(7),Topup Amount(8),Reversal Number(9),	Reversal Amount(10),Approved Number(11),Approved Amount(12),Pending Number(13),Pending Amount(14),Failed Number(15),Failed Amount(16),Waiting For Reversal Number(17),Waiting For Reversal Amount(18),Reverted Number(19),Reverted Amount(20),Fee Number(21),Fee Amount(22),";
                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setParameters(parameters).build()
                        .exportCsvFile(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Bank Transfer Report Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Bank Fee Report File successfully: ");
    }

    public static void generatePayoutMerchantTranfersReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Merchant Transfer Report File--------------------");
            List<Map> listMap = new MerchantTransferReportGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            Date fromDate = (Date) messageData.getRequestBody().get(IConstants.FROMDATE);
            Date toDate = (Date) messageData.getRequestBody().get(IConstants.TODATE);
            detailMap.put("FROM_DATE", DateTimeUtil.convertDatetoString(fromDate, DateTemplate.DD_MM_YYYY_hh_mm_ss_a));
            detailMap.put("TO_DATE", DateTimeUtil.convertDatetoString(DateUtils.addSeconds(toDate, -1),
                    DateTemplate.DD_MM_YYYY_hh_mm_ss_a));

            String typeDownload = messageData.getRequestBody().get(IConstants.TYPE_DOWNLOAD).toString();
            if (typeDownload.equals("xls")) {

                String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_TRANSFER_FILE;
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
            } else {
                ArrayList<String> listHeader = new ArrayList<String>();
                listHeader.add("S_DATE_TYPE");
                listHeader.add("S_PARTNER_NAME");
                listHeader.add("S_MERCHANT_NAME");
                listHeader.add("S_MERCHANT_ACCOUNT");
                listHeader.add("S_MERCHANT_ID");
                listHeader.add("N_OPEN_BALANCE");
                listHeader.add("N_CLOSE_BALANCE");
                listHeader.add("N_INCREASE_TOTAL");
                listHeader.add("N_INCREASE_AMOUNT");
                listHeader.add("N_DECREASE_TOTAL");
                listHeader.add("N_DECREASE_AMOUNT");
                listHeader.add("N_TOPUP_TOTAL");
                listHeader.add("N_TOPUP_AMOUNT");
                listHeader.add("N_REVERSAL_TOTAL");
                listHeader.add("N_REVERSAL_AMOUNT");
                listHeader.add("N_APPROVED_TOTAL");
                listHeader.add("N_APPROVED_AMOUNT");
                listHeader.add("N_PENDING_TOTAL");
                listHeader.add("N_PENDING_AMOUNT");
                listHeader.add("N_FAILED_TOTAL");
                listHeader.add("N_FAILED_AMOUNT");
                listHeader.add("N_WAIT_REVERSAL_TOTAL");
                listHeader.add("N_WAIT_REVERSAL_AMOUNT");
                listHeader.add("N_REVERTED_TOTAL");
                listHeader.add("N_REVERTED_AMOUNT");
                listHeader.add("N_FEE_TOTAL");
                listHeader.add("N_FEE_AMOUNT");

                Map<String, String> mapDate = new HashMap<>();
                Map parameters = new HashMap<>();
                String fileTitle = "MERCHANT TRANSFER REPORT";
                String fileDate = "FROM DATE " + detailMap.get("FROM_DATE") + " TO DATE " + detailMap.get("TO_DATE");
                String fileHeader = "No,Date Type,Partner Name,Merchant Name,Merchant Account,Merchant ID,Merchant Opening Balance(1),Merchant Closing Balance(2),Total Increase Transaction(4),Total Increase Amount(4),Total Decrease Transaction(5),Total Decrease Amount(6),Topup Number(7),Topup Amount(8),Reversal Number(9),	Reversal Amount(10),Approved Number(11),Approved Amount(12),Pending Number(13),Pending Amount(14),Failed Number(15),Failed Amount(16),Waiting For Reversal Number(17),Waiting For Reversal Amount(18),Reverted Number(19),Reverted Amount(20),Fee Number(21),Fee Amount(22),";
                ReportBuilder.newInstance()
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setParameters(parameters).build()
                        .exportCsvFile(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Bank Transfer Report Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Bank Fee Report File successfully: ");
    }

    public static void generatePayoutBankFeeReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Bank Fee Report File--------------------");
            List<Map> listMap = new BankFeeGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_FEE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Bank Fee Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Bank Fee Report File successfully: ");
    }

    public static void generatePayoutMerchantFeeReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Merchant Fee Report File--------------------");
            List<Map> listMap = new MerchantFeeGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " 00:00 AM " + toDate + " 11:59 PM");
            String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_FEE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Merchant Fee Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Merchant Fee Report File successfully: ");
    }

    public static void generatePayoutBankBalanceEnquiryReportFile(Message<BankBalanceQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Bank Balance Enquiry Report File--------------------");
            List<Map> listMap = new BankBalanceEnquiryGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_BALANCE_ENQUIRY_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(8).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Bank Fee Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Bank Fee Report File successfully: ");
    }

    public static void generatePayoutMerchantBalanceEnquiryReportFile(Message<MerchantBalanceQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Merchant Balance Enquiry Report File--------------------");
            List<Map> listMap = new MerchantBalanceEnquiryGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_MERCHANT_BALANCE_ENQUIRY_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(8).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Merchant Fee Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Merchant Fee Report File successfully: ");
    }

    public static void generatePayoutSwiftCodeReportFile(Message<SwiftCodeQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Swift Code Report File--------------------");
            List<Map> listMap = new SwiftCodeGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_SWIFT_CODE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(8).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Swift Code Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Swift Code File successfully: ");
    }

    public static void generatePayoutBankMapReportFile(Message<BankMapQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Payout Bank Map Report File--------------------");
            List<Map> listMap = new BankMapGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_MAP_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(8).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Payout Bank Map Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Payout Bank Map File successfully: ");
    }

    public static void generatePayoutFundsTransHistoryReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE PAYOUT FUNDS TRANS HISTORY REPORT FILE--------------------");
            List<Map> listMap = new FundTransHistoryGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String downloadType = messageData.getRequestBody().get(IConstants.DOWNLOADTYPE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_PAYOUT_FUNDS_TRANS_TOPUP_FILE;
            if (IConstants.CSV.equals(downloadType)) {
                ArrayList<String> listHeader = new ArrayList<>();
                listHeader.add(TemplateUtils.FUNDS_MERCHANT_ID);
                listHeader.add(TemplateUtils.MERCHANT_ACCOUNT);
                listHeader.add(TemplateUtils.MERCHANT_NAME);
                listHeader.add(TemplateUtils.MERCHANT_TRANS_ID);
                listHeader.add(TemplateUtils.TRANSACTION_ID);
                listHeader.add(TemplateUtils.BANK_TRANS_ID);
                listHeader.add("CREATE_DATE");
                listHeader.add("FUND_DATE");
                listHeader.add(TemplateUtils.UPDATE_DATE);
                listHeader.add("CURRENCY");
                listHeader.add(TemplateUtils.BALANCE_BEFORE);
                listHeader.add(TemplateUtils.BALANCE_AFTER);
                listHeader.add(TemplateUtils.RECEIVED_ACCOUNT);
                listHeader.add(TemplateUtils.RECEIVED_ACCOUNT_NAME);
                listHeader.add(TemplateUtils.RECEIVED_BANK);
                listHeader.add("AMOUNT");
                listHeader.add(TemplateUtils.REMARK);
                listHeader.add("STATE");
                listHeader.add(TemplateUtils.BANK_NAME);
                listHeader.add(TemplateUtils.PO_BANK_NUMBER);
                listHeader.add(TemplateUtils.RESPONSE_CODE);
                listHeader.add(TemplateUtils.BALANCE_BANK_BEFORE);
                listHeader.add(TemplateUtils.BALANCE_BANK_AFTER);
                listHeader.add(TemplateUtils.OPERATOR);

                String fileTitle = "FUNDS TRANSFER TRANSACTION HISTORY";
                String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;
                StringBuilder fileHeader = new StringBuilder(
                        "No,Merchant ID,Merchant Account,Merchant Name,Merchant Fund Transfer ID,OP Transaction ID,Bank Trans ID,Create Date,");
                fileHeader.append(
                        "Fund Transfer Date,Update Date,Currency,Merchant Opening Balance,Merchant Closing Balance,Beneficiary Account,Beneficiary Account Name,Beneficiary Bank,Amount,");
                fileHeader.append(
                        "Remark,State,PayOut  Bank,PayOut Bank Number,Response Code,Bank Opening Balance,Bank Closing Balance,Operator");
                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsv(fileTitle, fileDate, fileHeader.toString(), listHeader, listMap, mapDate);
            } else {
                ReportBuilder.newInstance().template(template)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                        .exportExcel2();
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate payout Funds Trans History Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Funds Trans History File successfully: ");
    }

    public static void generateLeadProviderConsumerFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START generate Lead Provider Consumer File--------------------");
            List<Map> listMap = new LeadProviderConsumerGenerator().generate(messageData.getRequestBody());
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String template = TemplateUtils.LEAD_PROVIDER_CONSUMER_TEMPLATE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("DATE", "FROM " + fromDate + " TO " + toDate)
                    .setStart(7).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Lead Provider Consumer File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Lead Provider Consumer File successfully: ");
    }

    public static void generateLeadProviderReportFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START generate Lead Provider Report File--------------------");
            List<Map> listMap = new LeadProviderReportGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            String template = TemplateUtils.LEAD_PROVIDER_REPORT_TEMPLATE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate generate Lead Provider Report File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate generate Lead Provider Report File successfully: ");
    }

    public static void generateAccountantStmFile(Message<Map<String, Object>> messageData) {
        try {
            Map<String, Object> mIn = messageData.getRequestBody();
            List<Map<String, Object>> listStm = (List<Map<String, Object>>) mIn.get("list_stm");
            List<String> listFilePath = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            for (Map<String, Object> stm : listStm) {
                long stmId = ((BigDecimal) stm.get("N_ID")).longValue();
                String fileName = (String) stm.get("S_NAME");
                String filePathString = Config.getFileExportLocation() + File.separator + fileName + ".xls";

                Date statementDate = new Date(((Timestamp) listStm.get(0).get("D_STATEMENT")).getTime());
                String date = sdf.format(statementDate);

                Files.deleteIfExists(Paths.get(filePathString));
                List<Map<String, Object>> listStmDetail = StatementDAO.listStatementDetail(stmId);

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("date", date);
                detailMap.put("fee", stm.get("N_FEE"));
                detailMap.put("amount", stm.get(N_AMOUNT));
                detailMap.put(TOTAL, stm.get("N_TOTAL_AMOUNT"));
                int exportType = ((BigDecimal) stm.get(N_EXPORT_TYPE)).intValue();
                List<Map<String, Object>> listMap;
                List<Map<String, Object>> listMap1 = new ArrayList<>();
                if (exportType == 2 || exportType == 4 || exportType == 6) {
                    listMap = listStmDetail.stream().filter(t -> "TCB".equals(t.get("S_BANK_CODE")))
                            .collect(Collectors.toList());
                    listMap1 = listStmDetail.stream().filter(t -> !"TCB".equals(t.get("S_BANK_CODE")))
                            .collect(Collectors.toList());
                    // Double total1 = listMap.stream().map(t -> ((BigDecimal)
                    // t.get(N_AMOUNT)).doubleValue()).filter(Objects::nonNull)
                    // .mapToDouble(Double::doubleValue)
                    // .sum();
                    //
                    // Double total2 = listMap1.stream().map(t -> ((BigDecimal)
                    // t.get(N_AMOUNT)).doubleValue()).filter(Objects::nonNull)
                    // .mapToDouble(Double::doubleValue)
                    // .sum();
                    // detailMap.put("total1", total1);
                    // detailMap.put("total2", total2);

                    // int index = listMap.size() + 1;
                    double total1 = 0;
                    double total2 = 0;
                    for (int i = 0; i < listMap.size(); i++) {
                        Map<String, Object> t = listMap.get(i);
                        t.put("index", i + 1);
                        total1 += ((BigDecimal) t.get(N_AMOUNT)).doubleValue();
                    }

                    for (int i = 0; i < listMap1.size(); i++) {
                        Map<String, Object> t = listMap1.get(i);
                        t.put("index", i + 1);
                        total2 += ((BigDecimal) t.get(N_AMOUNT)).doubleValue();
                    }
                    detailMap.put("total1", total1);
                    detailMap.put("total2", total2);
                } else {
                    listMap = listStmDetail;
                    for (int i = 0; i < listMap.size(); i++) {
                        listMap.get(i).put("index", i + 1);
                    }
                }

                String template = getTemplate(exportType);
                ReportBuilder.newInstance().template(template).exportFileName(filePathString)
                        .setBeanValue("listMap", listMap).setBeanValue("listMap1", listMap1)
                        .setBeanValue("detail", detailMap).build().exportExcel();
                listFilePath.add(filePathString);
            }

            addToZipFile(listFilePath, messageData);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Accountant Statement File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.info("Generate Accountant Statement File successfully");
    }

    public static void generateAccountantIBFile(Message<Map<String, Object>> messageData) {
        try {
            JsonArray merchantIdArr = new JsonArray(Config.getString("accoutant.merchant_id", ""));
            Map<String, Object> mIn = messageData.getRequestBody();
            String date = (String) mIn.get("date");
            String source = (String) mIn.get("source");
            String session = (String) mIn.get("session");
            String dateRegex = date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$3/$2/$1");
            String dateIb = date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$1.$2.$3");
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("date", dateRegex);

            List<Map<String, Object>> listStmDetail = StatementDAO.listStmDetailForIB(date, source, session);

            if (listStmDetail == null || listStmDetail.isEmpty())
                throw IErrors.RESOURCE_NOT_FOUND;

            List<Map<String, Object>> listVCB = new ArrayList<>();
            List<Map<String, Object>> listTCB = new ArrayList<>();
            List<Map<String, Object>> listTCBOther = new ArrayList<>();
            List<Map<String, Object>> listVTB = new ArrayList<>();
            List<Map<String, Object>> listOtherStm = new ArrayList<>();
            List<Map<String, Object>> listInVPB = new ArrayList<>();
            List<Map<String, Object>> listOutVPB = new ArrayList<>();

            for (Map<String, Object> t : listStmDetail) {
                int exportType = ((BigDecimal) t.get(N_EXPORT_TYPE)).intValue();
                String bankCode = (String) t.get("S_BANK_CODE");
                switch (exportType) {
                    case 1:
                    case 3:
                    case 5:
                        listVCB.add(t);
                        break;
                    case 2:
                    case 4:
                    case 6:
                        if ("TCB".equals(bankCode)) {
                            listTCB.add(t);
                        } else {
                            listTCBOther.add(t);
                        }
                        break;
                    case 7:
                        listVTB.add(t);
                        break;
                    case 8:
                        listOtherStm.add(t);
                        break;
                    case 9:
                        t.computeIfAbsent("S_BANK_BRANCH", map -> "ALL");
                        t.computeIfAbsent("S_BANK_PROVINCE", map -> "ALL");
                        if ("VPBank".equals(bankCode)) {
                            t.put("S_CHARGES", "OUR");
                            listInVPB.add(t);
                        } else {
                            t.put("S_CHARGES", "BEN");
                            listOutVPB.add(t);
                        }
                        break;
                    default:
                        break;
                }
            }
            for (int i = 0; i < merchantIdArr.size(); i++) {
                String[] merchantIdLimit = merchantIdArr.getString(i).split(";");
                listVCB = splitIB(listVCB, merchantIdLimit[0], Double.parseDouble(merchantIdLimit[1]));
            }

            listInVPB = splitIB(listInVPB, **********.0);
            listOutVPB = splitIB(listOutVPB, **********.0);

            for (Map<String, Object> stm : listOtherStm) {
                String acqBank = (String) stm.get("S_ACQ_BANK");

                if ("VCB".equals(acqBank))
                    listVCB.add(stm);
                if ("VTB".equals(acqBank))
                    listVTB.add(stm);
                if ("VPB".equals(acqBank))
                    listVTB.add(stm);
                if ("TCB".equals(acqBank)) {
                    String bankCode = (String) stm.get("S_BANK_CODE");
                    if (bankCode.equals("TCB"))
                        listTCB.add(stm);
                    else
                        listTCBOther.add(stm);
                }
            }

            List<String> listFilePath = new ArrayList<>();

            if (listVCB != null && !listVCB.isEmpty()) {
                for (int i = 0; i < listVCB.size(); i++) {
                    listVCB.get(i).put("index", i + 1);
                }
                String filePathString = Config.getFileExportLocation() + "/FileUpIB-" + dateIb + "-3a.F-VCB.xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_1, filePathString, listVCB, detailMap);
                listFilePath.add(filePathString);
            }

            if (listOutVPB != null && !listOutVPB.isEmpty()) {
                for (int i = 0; i < listOutVPB.size(); i++) {
                    listOutVPB.get(i).put("index", i + 1);
                }
                String nguyen = "C".equals(session) ? "PM" : "AM";
                String filePathString = Config.getFileExportLocation() + "/Bang ke Paycollect ngoai VPB " + dateIb + " "
                        + nguyen + ".xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_5, filePathString, listOutVPB, detailMap);
                listFilePath.add(filePathString);
            }

            if (listInVPB != null && !listInVPB.isEmpty()) {
                for (int i = 0; i < listInVPB.size(); i++) {
                    listInVPB.get(i).put("index", i + 1);
                }
                String nguyen = "C".equals(session) ? "PM" : "AM";
                String filePathString = Config.getFileExportLocation() + "/Bang ke Paycollect trong VPB " + dateIb + " "
                        + nguyen + ".xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_5, filePathString, listInVPB, detailMap);
                listFilePath.add(filePathString);
            }

            if (listTCB != null && !listTCB.isEmpty()) {
                splitMaxValueTCB(listTCB);

                String filePathString = Config.getFileExportLocation() + "/FileUpIB-" + dateIb + "-3b.F-TCB.xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_2, filePathString, listTCB, detailMap);
                listFilePath.add(filePathString);
            }

            if (listTCBOther != null && !listTCBOther.isEmpty()) {
                for (int i = 0; i < listTCBOther.size(); i++) {
                    Map<String, Object> data = listTCBOther.get(i);
                    String sBankCode = data.get("S_BANK_CODE") == null ? "" : (String) data.get("S_BANK_CODE");
                    String sBankBranch = data.get("S_BANK_BRANCH") == null ? "" : (String) data.get("S_BANK_BRANCH");

                    int m = sBankCode.lastIndexOf("-");
                    String bankName = sBankCode.substring(0, m).trim();
                    String bankCode = sBankCode.substring(m + 1).trim();
                    if (!sBankBranch.isBlank()) {
                        int n = sBankBranch.lastIndexOf("-");
                        bankCode = sBankBranch.substring(n + 1).trim();
                    }

                    data.put("bank_code", bankCode);
                    data.put("bank_name", bankName);
                }

                splitMaxValueTCB(listTCBOther);

                String filePathString = Config.getFileExportLocation() + "/FileUpIB-" + dateIb + "-3c.F-TCB-Khac.xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_3, filePathString, listTCBOther, detailMap);
                listFilePath.add(filePathString);
            }

            if (listVTB != null && !listVTB.isEmpty()) {
                for (int i = 0; i < listVTB.size(); i++) {
                    listVTB.get(i).put("index", i + 1);
                }
                String filePathString = Config.getFileExportLocation() + "/FileUpIB-" + dateIb + "-3d.F-VTB.xls";
                buildExcelFile(TemplateUtils.TEMPLATE_STATEMENT_IB_FILE_4, filePathString, listVTB, detailMap);
                listFilePath.add(filePathString);
            }

            addToZipFile(listFilePath, messageData);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Accountant IB File Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.info("Generate Accountant IB File successfully");
    }

    private static List<Map<String, Object>> splitIB(List<Map<String, Object>> input, double limit) {
        List<Map<String, Object>> output = new ArrayList<>();
        for (Map<String, Object> map : input) {
            double currentAmount = ((BigDecimal) map.get(N_AMOUNT)).doubleValue();
            String json = new Gson().toJson(map);
            while (true) {
                Map<String, Object> mapCopy = new Gson().fromJson(
                        json, new TypeToken<Map<String, Object>>() {}.getType());
                if (currentAmount <= limit) {
                    mapCopy.put(N_AMOUNT, currentAmount);
                    output.add(mapCopy);
                    break;
                } else {
                    currentAmount -= limit;
                    mapCopy.put(N_AMOUNT, limit);
                    output.add(mapCopy);
                }
            }
        }
        return output;
    }

    private static List<Map<String, Object>> splitIB(List<Map<String, Object>> input, String merchantId, double limit) {
        List<Map<String, Object>> output = new ArrayList<>();
        for (Map<String, Object> map : input) {
            if (!map.get("S_MERCHANT_ID").toString().equals(merchantId))
                continue;
            double currentAmount = ((BigDecimal) map.get(N_AMOUNT)).doubleValue();
            String json = new Gson().toJson(map);
            while (true) {
                Map<String, Object> mapCopy = new Gson().fromJson(
                        json, new TypeToken<Map<String, Object>>() {}.getType());
                if (currentAmount <= limit) {
                    mapCopy.put(N_AMOUNT, currentAmount);
                    output.add(mapCopy);
                    break;
                } else {
                    currentAmount -= limit;
                    mapCopy.put(N_AMOUNT, limit);
                    output.add(mapCopy);
                }
            }
        }
        return output;
    }

    private static void splitMaxValueTCB(List<Map<String, Object>> listData) {
        long maxAmt = 20000000000L;
        List<Map<String, Object>> listRemove = new ArrayList<>();
        List<Map<String, Object>> listAdd = new ArrayList<>();

        for (int i = 0; i < listData.size(); i++) {
            Map<String, Object> data = listData.get(i);
            long amount = ((BigDecimal) data.get(N_AMOUNT)).longValue();
            long count = Math.floorDiv(amount, maxAmt);

            if (count > 0) {
                listRemove.add(data);
                Map<String, Object> cloneData1 = new HashMap<>();
                cloneData1.putAll(data);
                cloneData1.put(N_AMOUNT, maxAmt);
                listAdd.addAll(Collections.nCopies((int) count, cloneData1));

                Map<String, Object> cloneData2 = new HashMap<>();
                cloneData2.putAll(data);
                cloneData2.put(N_AMOUNT, amount - count * maxAmt);
                listAdd.add(cloneData2);
            }
        }

        if (listRemove.size() > 0) {
            listData.removeAll(listRemove);
            listData.addAll(listAdd);
        }

        for (int i = 0; i < listData.size(); i++) {
            String ref = "AX" + Strings.padStart(String.valueOf(i + 1), 2, '0');
            listData.get(i).put("index", ref);
        }
    }

    public static void generateAccountantReportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE STATEMENT REPORT FILE--------------------");

            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("id", messageData.getRequestBody().get("id").toString());
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            Calendar clFrom = Calendar.getInstance();
            Calendar clTo = Calendar.getInstance();
            SimpleDateFormat dfFrom = new SimpleDateFormat("dd-MM-yyyy");
            Date dFrom = dfFrom.parse(fromDate);
            Date dTo = dfFrom.parse(toDate);
            clFrom.setTime(dFrom);
            clTo.setTime(dTo);
            if (clFrom.get(Calendar.WEEK_OF_YEAR) == clTo.get(Calendar.WEEK_OF_YEAR)) {
                detailMap.put("WEEK", "Tuần " + clFrom.get(Calendar.WEEK_OF_YEAR));
            } else {
                detailMap.put("WEEK",
                        "Tuần " + clFrom.get(Calendar.WEEK_OF_YEAR) + " - " + clTo.get(Calendar.WEEK_OF_YEAR));
            }
            String template = TemplateUtils.TEMPLATE_ACCOUNTANT_REPORT_FILE;
            List<Map> listMapReport = new AccountantReportGenerator().generate(messageData.getRequestBody());
            Map<String, Object> mIn = new HashMap<>();
            mIn.put("id", messageData.getRequestBody().get("id").toString());
            mIn.put("vcbTotal",
                    (double) listMapReport.get(0).get("vcbIb") - (double) listMapReport.get(0).get("vcbTotal"));
            mIn.put("vtbTotal",
                    (double) listMapReport.get(0).get("vtbIb") - (double) listMapReport.get(0).get("vtbTotal"));
            mIn.put("tcbTotal",
                    (double) listMapReport.get(0).get("tcbIb") - (double) listMapReport.get(0).get("tcbTotal"));
            List<Map> listMapExplanation = new AccountantExplanationGenerator().generate(mIn);
            List<Map> reportConvertedByDate = (List<Map>) listMapReport.get(0).get("reportConverted");
            List<Map> explanationConvertedByName = (List<Map>) listMapExplanation.get(0).get("explanationConverted");
            Map reportDetail = listMapReport.get(0);
            Map explanationDetail = listMapExplanation.get(0);
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMapReport", reportDetail).setBeanValue("reportConverted", reportConvertedByDate)
                    .setBeanValue("explanationDetail", explanationDetail)
                    .setBeanValue("explanationConverted", explanationConvertedByName)
                    .setBeanValue("detailMap", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE STATEMENT REPORT ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE STATEMENT REPORT SUCCESS: ");
    }

    private static void buildExcelFile(String template, String filePath, List<Map<String, Object>> listData,
            Map<String, Object> detailMap) {
        boolean isEncrypted = Config.getBoolean("accountant.ib_file.encrypt", "true");
        String password = Config.getString("accountant.ib_file.password", "op123456");
        ReportBuilder.newInstance().template(template).exportFileName(filePath).setBeanValue("detail", detailMap)
                .setBeanValue("listMap", listData).build().exportExcelWithPassword(isEncrypted, password);
    }

    private static void addToZipFile(List<String> listFilePath, Message<Map<String, Object>> messageData)
            throws Exception {
        Exception ex = null;
        FileOutputStream fos = null;
        ZipOutputStream zos = null;
        FileInputStream fis = null;
        try {
            LOGGER.info("Writing t to zip file");
            String fName = Config.getFileExportLocation() + "/" + messageData.getRequestData().get(FILE_HASH_NAME);
            File zipFile = new File(fName);
            fos = new FileOutputStream(fName);
            zos = new ZipOutputStream(fos);

            for (String fileName : listFilePath) {
                File file = new File(fileName);
                fis = new FileInputStream(file);
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }
                zos.closeEntry();
                if (!file.delete()) {
                    LOGGER.warning("File not deleted " + file.getName());
                }
                fis.close();
            }

            zos.close();
            fos.close();
            long fileSize = zipFile.length();
            messageData.getRequestData().put(IConstants.FILE_SIZE, fileSize);
            messageData.setResultCode(200);
            messageData.setResultString("File Has been generated");
        } catch (Exception e) {
            ex = e;
            Util.closeResourse(LOGGER, fos, zos, fis);
        }
        if (ex != null)
            throw ex;
    }

    private static String getTemplate(int exportType) {
        String template = "";
        switch (exportType) {
            case 1:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_1;
                break;
            case 2:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_2;
                break;
            case 3:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_3;
                break;
            case 4:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_4;
                break;
            case 5:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_5;
                break;
            case 6:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_6;
                break;
            case 7:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_7;
                break;
            case 8:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_8;
                break;
            case 9:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_9;
                break;
            case 10:
                template = TemplateUtils.TEMPLATE_ACCOUNTANT_STATEMENT_FILE_10;
                break;
            default:
                break;
        }
        return template;
    }

    public static void generateRoleManagementFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START generate role management file--------------------");
            List<Map> listMap = new RoleManagementGenerator().generate(messageData.getRequestBody());
            String template = TemplateUtils.ROLE_MANAGEMENT;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setStart(1).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate role management file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate role management file successfully: ");
    }

    public static void generateDebtClearanceManagementFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START generate role management file--------------------");
            List<Map> listMap = new GenerateBillingDebtClearingFile().generate(messageData.getRequestBody());
            String fromDate = messageData.getRequestBody().get(IConstants.FROMDATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TODATE).toString();
            String template = TemplateUtils.TEMPLATE_DEBT_CLEARANCE_FILE;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("date", "FROM " + fromDate + " TO " + toDate)
                    .setStart(7).build().exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateDebtClearanceManagementFile file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDebtClearanceManagementFile file successfully: ");
    }

    public static void generateAccountantStmSummaryFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START generateAccountantStmSummaryFile--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            String date = (String) mIn.get("date");
            String dateRegex = date.replaceFirst("(\\d{4})(\\d{2})(\\d{2})", "$3/$2/$1");
            String source = (String) mIn.get(SOURCE);
            Map<String, Object> resultMap;
            if ("OF1".equals(source)) {
                resultMap = StatementHandler.getSummaryStm(date);
            } else {
                resultMap = StatementHandler.getSummaryStm2(date);
            }
            List<Map<String, Object>> listDetail = (List<Map<String, Object>>) resultMap.get("detail");
            listDetail.sort(Comparator.comparing(t -> (Integer) t.get(ParamsPool.ORDER_NUMBER)));
            for (int i = 0; i < listDetail.size(); i++) {
                listDetail.get(i).put(NUMBER, i + 1);
            }
            Map<String, Object> totalMap = (Map<String, Object>) resultMap.get("total");
            Map<String, Object> fileMap = (Map<String, Object>) resultMap.get("file");
            Map<String, Object> diffMap = (Map<String, Object>) resultMap.get("diff");

            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("from_date", dateRegex);
            detailMap.put("to_date", dateRegex);

            String template = "OF1".equals(source) ? TemplateUtils.TEMPLATE_STATEMENT_SUMMARY
                    : TemplateUtils.TEMPLATE_PAYCOLLECT_SUMMARY;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("common", detailMap).setBeanValue("listDetail", listDetail)
                    .setBeanValue("total", totalMap).setBeanValue("file", fileMap).setBeanValue("diff", diffMap).build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateAccountantStmSummaryFile file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateAccountantStmSummaryFile file successfully: ");
    }

    public static void generateExportReceiptFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export receipt file--------------------");
            List<Map> listMap = new ExportReceiptGenerator().generate(messageData.getRequestBody());
            boolean isEncrypted = false;
            if (messageData.getRequestBody().containsKey("password")) {
                isEncrypted = (boolean) messageData.getRequestBody().get("password");
            }
            String password = Config.getString("accountant.receipt_file.password", "op123456");

            String template = TemplateUtils.EXPORT_RECEIPT;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).build().exportExcelWithPassword(isEncrypted, password);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportReceiptFile file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportReceiptFile file successfully");
    }

    public static void generateExportReceiptDetailFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export receipt file--------------------");
            List<Map> listMap = new ExportReceiptDetailGenerator().generate(messageData.getRequestBody());
            boolean isEncrypted = false;
            if (messageData.getRequestBody().containsKey("password")) {
                isEncrypted = (boolean) messageData.getRequestBody().get("password");
            }
            String password = Config.getString("accountant.receipt_file.password", "op123456");

            String template = TemplateUtils.EXPORT_RECEIPT;
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).build().exportExcelWithPassword(isEncrypted, password);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportReceiptFile file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportReceiptFile file successfully");
    }

    public static void generateOnOffBanksApprovalFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START on off banks approval file--------------------");
            List<Map> listMap = new OnOffBanksGenerator().generate(messageData.getRequestBody());

            String template = TemplateUtils.APPROVAL_HISTORY;
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateOnOffBanksApprovalFile Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateOnOffBanksApprovalFile successfully");
    }

    public static void generateExportQrPaygateFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export QR paygate file--------------------");
            List<Map> listMap = new ExportQrPaygateGenerator().generate(messageData.getRequestBody());
            String template = TemplateUtils.TEMPLATE_QR_PAYGATE_FILE;

            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportQrPaygateFile file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportQrPaygateFile file successfully");
    }

    public static void generatePaymentMerchantFeeFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export PAYMENT MERCHANT FEE file--------------------");
            List<Map> listMap = new PaymentMerchantFeeGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            String serviceLink = parameterPost.get("serviceLink").toString();
            Map<String, Object> detailMap = new HashMap<>();
            String fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_MERCHANT_FEE_2B_FILE;
            if (serviceLink.equals("payout")) {
                fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_MERCHANT_FEE_PAYOUT_FILE;
            } else {
                fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_MERCHANT_FEE_2B_FILE;
            }
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generatePaymentMerchantFeeFile file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generatePaymentMerchantFeeFile file successfully");
    }

    public static void generatePayment2EndUserFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export PAYMENT MERCHANT FEE file--------------------");
            List<Map> listMap = new Payment2EndUserFeeGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            String serviceLink = parameterPost.get("serviceLink").toString();
            Map<String, Object> detailMap = new HashMap<>();
            String fileTemplate = TemplateUtils.TEMPLATE_PAYMENT2_END_USER_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generatePayment2EndUserFile file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generatePayment2EndUserFile file successfully");
    }

    public static void generatePaymentBankFeeFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export PAYMENT BANK FEE file--------------------");
            List<Map> listMap = new PaymentBankFeeGenerator().generate(messageData.getRequestBody());
            LOGGER.log(Level.INFO,
                    "--------------------listMap generatePaymentBankFeeFile--------------------" + listMap);
            Map<String, Object> detailMap = new HashMap<>();
            String fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_PARTNER_FEE_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(7)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generatePaymentBankFeeFile file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generatePaymentBankFeeFile file successfully");
    }

    public static void generateAdvanceTransactionFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export PAYMENT MERCHANT FEE file--------------------");
            List<Map> listMap = new PaymentAdvanceTransactionGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("from_date"));
            detailMap.put("TO_DATE", parameterPost.get("to_date"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("from_date") + " đến " + parameterPost.get("to_date"));
            String fileTemplate = TemplateUtils.TEMPLATE_ADVANCE_TRANSACTION_P3_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generatePaymentMerchantFeeFile file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generatePaymentMerchantFeeFile file successfully");
    }

    public static void generateInstallmentFee(Message messageData) {
        try {
            LOGGER.log(Level.INFO, "------------------START EXPORT INSTALLMENT FEE-------------------------------");
            List<Map> listMap = new ExportInstallmentFeeGenerator()
                    .generate((Map<String, Object>) messageData.getRequestBody());
            String fileTemplate = TemplateUtils.TEMPLATE_INSTALLMENT_FEE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setStart(7)
                    .build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateInstallmentFee", e);
        }
        LOGGER.log(Level.INFO, "generateInstallmentFee file successfully");
    }

    public static void generateItaFeeMerchant3B(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "------------------START EXPORT INSTALLMENT FEE 3B-------------------------------");
            Map<String, Object> messageDataRequestBody = messageData.getRequestBody();
            List<Map> listMap = (List<Map>) messageDataRequestBody.get("data");
            List<Map> datas = new ArrayList<>();
            Integer index = 1;
            for (Map dt : listMap) {
                dt.put("index", index++);
                datas.add(dt);
            }
            String fileTemplate = TemplateUtils.TEMPLATE_INSTALLMENT_FEE_MERCHANT_3B;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", datas)
                    .setStart(7)
                    .build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateInstallmentFee", e);
        }
        LOGGER.log(Level.INFO, "generateInstallmentFee file successfully");
    }

    public static void generateItaFeeBank3B(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "------------------START EXPORT INSTALLMENT FEE 3B-------------------------------");
            Map<String, Object> messageDataRequestBody = messageData.getRequestBody();
            List<Map> listMap = (List<Map>) messageDataRequestBody.get("data");
            List<Map> datas = new ArrayList<>();
            Integer index = 1;
            for (Map dt : listMap) {
                dt.put("index", index++);
                datas.add(dt);
            }
            String fileTemplate = TemplateUtils.TEMPLATE_INSTALLMENT_FEE_BANK_3B;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", datas)
                    .setStart(7)
                    .build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateInstallmentFee", e);
        }
        LOGGER.log(Level.INFO, "generateInstallmentFee file successfully");
    }

    public static void generateExportFullLinkFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "------------------START EXPORT FULL LINK FILE-------------------------------");
            List<Map> listMap = new ExportFullLinkGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("fromDate"));
            detailMap.put("TO_DATE", parameterPost.get("toDate"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("fromDate") + " đến " + parameterPost.get("toDate"));
            String fileTemplate = TemplateUtils.TEMPLATE_FULL_LINK_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateExportFullLinkFile", e);
        }
        LOGGER.log(Level.INFO, "generateExportFullLinkFile file successfully");
    }

    public static void generateListMerchantAccountFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list merchant account file--------------------");
            List<Map<String, Object>> listMap = MerchantManagementDAO.getListMerchantAccountant();
            int index = 1;
            for (Map<String, Object> map : listMap) {
                map.put("index", index++);
            }
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_MERCHANT_ACCOUNTANT)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).build().exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateListMerchantAccountFile file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListMerchantAccountFile file successfully");
    }

    public static void generateExchangeRateFile(Message<ExchangeRateQueryDto> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCHANGE RATE FILE--------------------");
            List<Map> listMap = new ExchangeRateGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().getFromDate();
            String toDate = messageData.getRequestBody().getToDate();
            detailMap.put("HEADER", "FROM " + fromDate + " - " + toDate);
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_EXCHANGE_RATE_FILE)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(7).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Exchange rate file error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Exchange rate file successfully: ");
    }

    public static void generateExportMonthlyFeeReport(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT FILE--------------------");
            List<Map> listOfListMap = new MonthlyFeeReportGenerator().generate(messageData.getRequestBody());
            List<Map> listMapEcom = new ArrayList<>();
            List<Map> listMapPos = new ArrayList<>();

            for (Map<String, Object> listMap : listOfListMap) {
                if (listMap.containsKey("listMapEcom")) {
                    listMapEcom = (List<Map>) listMap.get("listMapEcom");
                }
                if (listMap.containsKey("listMapPos")) {
                    listMapPos = (List<Map>) listMap.get("listMapPos");
                }
            }
            List<Map<String, Object>> listBean = new ArrayList<>();
            Map<String, Object> listDataMapEcom = new HashMap<>();
            listDataMapEcom.put("listMapEcom", listMapEcom);
            Map<String, Object> listDataMapPos = new HashMap<>();
            listDataMapPos.put("listMapPos", listMapPos);

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = (String) messageData.getRequestBody().get(MFR_FROM_DATE);
            String toDate = (String) messageData.getRequestBody().get(MFR_TO_DATE);
            detailMap.put("S_FROM", fromDate);
            detailMap.put("S_TO", toDate);
            listDataMapEcom.put("detail", detailMap);
            listDataMapPos.put("detail", detailMap);

            // process footer ecom
            Double f_countSuccessEcom = 0.0;
            Double f_countFailedEcom = 0.0;
            Double f_originAmountUsdEcom = 0.0;
            Double f_originAmountVndEcom = 0.0;
            Double f_partnerDiscountAmountEcom = 0.0;
            Double f_merchantDiscountAmountEcom = 0.0;
            Double f_totalAmountVNDEcom = 0.0;
            Double f_feeSuccessEcom = 0.0;
            Double f_feeFailedEcom = 0.0;
            Double f_ecomFeeEcom = 0.0;
            Double f_feeItaEcom = 0.0;
            Double f_discountFeeEcom = 0.0;
            Double f_feeMonthEcom = 0.0;
            Double f_feeTotalAllEcom = 0.0;
            Double f_totalFeeCollectedEcom = 0.0;
            Double f_totalFeeReceivableEcom = 0.0;
            Double f_advanceAmountEcom = 0.0;
            Map<String, Object> footerEcom = new HashMap<>();
            if (!listMapEcom.isEmpty()) {
                for (Map<String, Object> item : listMapEcom) {
                    f_countSuccessEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_SUCCESS)));
                    f_countFailedEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_FAILED)));
                    f_originAmountUsdEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_USD)));
                    f_originAmountVndEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_VND)));
                    f_partnerDiscountAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT)));
                    f_merchantDiscountAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT)));
                    f_totalAmountVNDEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_VND)));
                    f_feeSuccessEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_SUCCESS)));
                    f_feeFailedEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_FAILED)));
                    f_ecomFeeEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ECOM)));
                    f_feeItaEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ITA)));
                    f_discountFeeEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_DISCOUNT_FEE)));
                    f_feeMonthEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_MONTH)));
                    f_feeTotalAllEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_TOTAL)));
                    f_totalFeeCollectedEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED)));
                    f_totalFeeReceivableEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE)));
                    f_advanceAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ADVANCE_AMOUNT)));
                }
            }
            footerEcom.put("countSuccess", f_countSuccessEcom);
            footerEcom.put("countFailed", f_countFailedEcom);
            footerEcom.put("originAmountUsd", f_originAmountUsdEcom);
            footerEcom.put("originAmountVnd", f_originAmountVndEcom);
            footerEcom.put("partnerDiscountAmount", f_partnerDiscountAmountEcom);
            footerEcom.put("merchantDiscountAmount", f_merchantDiscountAmountEcom);
            footerEcom.put("totalAmountVND", f_totalAmountVNDEcom);
            footerEcom.put("feeSuccess", f_feeSuccessEcom);
            footerEcom.put("feeFailed", f_feeFailedEcom);
            footerEcom.put("feeEcom", f_ecomFeeEcom);
            footerEcom.put("feeIta", f_feeItaEcom);
            footerEcom.put("discountFee", f_discountFeeEcom);
            footerEcom.put("feeMonth", f_feeMonthEcom);
            footerEcom.put("feeTotalAll", f_feeTotalAllEcom);
            footerEcom.put("totalFeeCollected", f_totalFeeCollectedEcom);
            footerEcom.put("totalFeeReceivable", f_totalFeeReceivableEcom);
            footerEcom.put("advanceAmount", f_advanceAmountEcom);
            listDataMapEcom.put("footerEcom", footerEcom);

            // process footer pos
            Double f_countSuccessPos = 0.0;
            Double f_countFailedPos = 0.0;
            Double f_originAmountUsdPos = 0.0;
            Double f_originAmountVndPos = 0.0;
            Double f_partnerDiscountAmountPos = 0.0;
            Double f_merchantDiscountAmountPos = 0.0;
            Double f_totalAmountVNDPos = 0.0;
            Double f_feeSuccessPos = 0.0;
            Double f_feeFailedPos = 0.0;
            Double f_ecomFeePos = 0.0;
            Double f_feeItaPos = 0.0;
            Double f_discountFeePos = 0.0;
            Double f_feeMonthPos = 0.0;
            Double f_feeTotalAllPos = 0.0;
            Double f_totalFeeCollectedPos = 0.0;
            Double f_totalFeeReceivablePos = 0.0;
            Double f_advanceAmountPos = 0.0;
            Map<String, Object> footerPos = new HashMap<>();
            if (!listMapPos.isEmpty()) {
                for (Map<String, Object> item : listMapPos) {
                    f_countSuccessPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_SUCCESS)));
                    f_countFailedPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_FAILED)));
                    f_originAmountUsdPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_USD)));
                    f_originAmountVndPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_VND)));
                    f_partnerDiscountAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT)));
                    f_merchantDiscountAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT)));
                    f_totalAmountVNDPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_VND)));
                    f_feeSuccessPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_SUCCESS)));
                    f_feeFailedPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_FAILED)));
                    f_ecomFeePos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ECOM)));
                    f_feeItaPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ITA)));
                    f_discountFeePos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_DISCOUNT_FEE)));
                    f_feeMonthPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_MONTH)));
                    f_feeTotalAllPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_TOTAL)));
                    f_totalFeeCollectedPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED)));
                    f_totalFeeReceivablePos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE)));
                    f_advanceAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ADVANCE_AMOUNT)));
                }
            }
            footerPos.put("countSuccess", f_countSuccessPos);
            footerPos.put("countFailed", f_countFailedPos);
            footerPos.put("originAmountUsd", f_originAmountUsdPos);
            footerPos.put("originAmountVnd", f_originAmountVndPos);
            footerPos.put("partnerDiscountAmount", f_partnerDiscountAmountPos);
            footerPos.put("merchantDiscountAmount", f_merchantDiscountAmountPos);
            footerPos.put("totalAmountVND", f_totalAmountVNDPos);
            footerPos.put("feeSuccess", f_feeSuccessPos);
            footerPos.put("feeFailed", f_feeFailedPos);
            footerPos.put("feeEcom", f_ecomFeePos);
            footerPos.put("feeIta", f_feeItaPos);
            footerPos.put("discountFee", f_discountFeePos);
            footerPos.put("feeMonth", f_feeMonthPos);
            footerPos.put("feeTotalAll", f_feeTotalAllPos);
            footerPos.put("totalFeeCollected", f_totalFeeCollectedPos);
            footerPos.put("totalFeeReceivable", f_totalFeeReceivablePos);
            footerPos.put("advanceAmount", f_advanceAmountPos);
            listDataMapPos.put("footerPos", footerPos);

            String exportType = (String) messageData.getRequestBody().get(MFR_EXPORT_TYPE);
            String template = "";
            List templateSheetNameList = new ArrayList<>();
            List<String> sheetNameList = new ArrayList<>();

            switch (exportType) {
                case "ALL": {
                    template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT;
                    listBean.add(listDataMapEcom);
                    listBean.add(listDataMapPos);
                    templateSheetNameList.add("ECOM_TEMPLATE_SHEET");
                    templateSheetNameList.add("POS_TEMPLATE_SHEET");
                    sheetNameList.add("ECOM_STANDARD");
                    sheetNameList.add("POS_STANDARD");
                    break;
                }
                case "POS": {
                    template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_ONLY_POS;
                    listBean.add(listDataMapPos);
                    templateSheetNameList.add("POS_TEMPLATE_SHEET");
                    sheetNameList.add("POS_STANDARD");
                    break;
                }
                case "POS-BY-PAY-CHANNEL": {
                    template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_ONLY_POS_BY_PAY_CHANNEL;
                    listBean.add(listDataMapPos);
                    templateSheetNameList.add("POS_TEMPLATE_SHEET");
                    sheetNameList.add("POS_STANDARD");
                    break;
                }
                case "PAYCOLLECT": {
                    template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_PAYCOLLECT;
                    listBean.add(listDataMapEcom);
                    templateSheetNameList.add("PC_TEMPLATE_SHEET");
                    sheetNameList.add("PC");
                    break;
                }
                case "VIETQR": {
                    template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_PAYCOLLECT;
                    listBean.add(listDataMapEcom);
                    templateSheetNameList.add("VIETQR_TEMPLATE_SHEET");
                    sheetNameList.add("VIETQR");
                    break;
                }
            }

            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListBeanValue(listBean)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    .build().exportExcelMultilSheet();

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportMonthlyFeeReport file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportMonthlyFeeReport file successfully");
    }

    public static void generateExportDailyFeeReport(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE DAILY FEE REPORT FILE--------------------");
            List<Map> listMap = new DailyFeeReportGenerator().generate(messageData.getRequestBody());

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = (String) messageData.getRequestBody().get(MFR_FROM_DATE);
            String toDate = (String) messageData.getRequestBody().get(MFR_TO_DATE);
            detailMap.put("S_FROM", fromDate);
            detailMap.put("S_TO", toDate);

            List<Map> listMapEcom = new ArrayList<>();
            List<Map> listMapPos = new ArrayList<>();

            for (Map<String, Object> listMapItem : listMap) {
                if (listMapItem.containsKey("listMapEcom")) {
                    listMapEcom = (List<Map>) listMapItem.get("listMapEcom");
                }
                if (listMapItem.containsKey("listMapPos")) {
                    listMapPos = (List<Map>) listMapItem.get("listMapPos");
                }
            }

            List<Map<String, Object>> listBean = new ArrayList<>();
            Map<String, Object> listDataMapEcom = new HashMap<>();
            listDataMapEcom.put("listMapEcom", listMapEcom);
            Map<String, Object> listDataMapPos = new HashMap<>();
            listDataMapPos.put("listMapPos", listMapPos);

            // process footer ecom
            Double f_countSuccessEcom = 0.0;
            Double f_countFailedEcom = 0.0;
            Double f_originAmountUsdEcom = 0.0;
            Double f_originAmountVndEcom = 0.0;
            Double f_partnerDiscountAmountEcom = 0.0;
            Double f_merchantDiscountAmountEcom = 0.0;
            Double f_totalAmountVNDEcom = 0.0;
            Double f_feeSuccessEcom = 0.0;
            Double f_feeFailedEcom = 0.0;
            Double f_ecomFeeEcom = 0.0;
            Double f_feeItaEcom = 0.0;
            Double f_discountFeeEcom = 0.0;
            Double f_feeTotalAllEcom = 0.0;
            Double f_totalFeeCollectedEcom = 0.0;
            Double f_totalFeeReceivableEcom = 0.0;
            Double f_advanceAmountEcom = 0.0;
            Map<String, Object> footerEcom = new HashMap<>();
            if (!listMapEcom.isEmpty()) {
                for (Map<String, Object> item : listMapEcom) {
                    f_countSuccessEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_SUCCESS)));
                    f_countFailedEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_FAILED)));
                    f_originAmountUsdEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_USD)));
                    f_originAmountVndEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_VND)));
                    f_partnerDiscountAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT)));
                    f_merchantDiscountAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT)));

                    f_totalAmountVNDEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_VND)));
                    f_feeSuccessEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_SUCCESS)));
                    f_feeFailedEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_FAILED)));
                    f_ecomFeeEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ECOM)));
                    f_feeItaEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ITA)));
                    f_discountFeeEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_DISCOUNT_FEE)));
                    f_feeTotalAllEcom += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_TOTAL)));
                    f_totalFeeCollectedEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED)));
                    f_totalFeeReceivableEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE)));
                    f_advanceAmountEcom += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ADVANCE_AMOUNT)));
                }
            }
            footerEcom.put("countSuccess", f_countSuccessEcom);
            footerEcom.put("countFailed", f_countFailedEcom);
            footerEcom.put("originAmountUsd", f_originAmountUsdEcom);
            footerEcom.put("originAmountVnd", f_originAmountVndEcom);
            footerEcom.put("partnerDiscountAmount", f_partnerDiscountAmountEcom);
            footerEcom.put("merchantDiscountAmount", f_merchantDiscountAmountEcom);
            footerEcom.put("totalAmountVND", f_totalAmountVNDEcom);
            footerEcom.put("feeSuccess", f_feeSuccessEcom);
            footerEcom.put("feeFailed", f_feeFailedEcom);
            footerEcom.put("feeEcom", f_ecomFeeEcom);
            footerEcom.put("feeIta", f_feeItaEcom);
            footerEcom.put("discountFee", f_discountFeeEcom);
            footerEcom.put("feeTotalAll", f_feeTotalAllEcom);
            footerEcom.put("totalFeeCollected", f_totalFeeCollectedEcom);
            footerEcom.put("totalFeeReceivable", f_totalFeeReceivableEcom);
            footerEcom.put("advanceAmount", f_advanceAmountEcom);
            listDataMapEcom.put("footerEcom", footerEcom);

            // process footer pos
            Double f_countSuccessPos = 0.0;
            Double f_countFailedPos = 0.0;
            Double f_originAmountUsdPos = 0.0;
            Double f_originAmountVndPos = 0.0;
            Double f_partnerDiscountAmountPos = 0.0;
            Double f_merchantDiscountAmountPos = 0.0;
            Double f_totalAmountVNDPos = 0.0;
            Double f_feeSuccessPos = 0.0;
            Double f_feeFailedPos = 0.0;
            Double f_ecomFeePos = 0.0;
            Double f_feeItaPos = 0.0;
            Double f_discountFeePos = 0.0;
            Double f_feeTotalAllPos = 0.0;
            Double f_totalFeeCollectedPos = 0.0;
            Double f_totalFeeReceivablePos = 0.0;
            Double f_advanceAmountPos = 0.0;
            Map<String, Object> footerPos = new HashMap<>();
            if (!listMapPos.isEmpty()) {
                for (Map<String, Object> item : listMapPos) {
                    f_countSuccessPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_SUCCESS)));
                    f_countFailedPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_COUNT_FAILED)));
                    f_originAmountUsdPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_USD)));
                    f_originAmountVndPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ORIGIN_AMOUNT_VND)));
                    f_partnerDiscountAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT)));
                    f_merchantDiscountAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT)));

                    f_totalAmountVNDPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_VND)));
                    f_feeSuccessPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_SUCCESS)));
                    f_feeFailedPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_FAILED)));
                    f_ecomFeePos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ECOM)));
                    f_feeItaPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_ITA)));
                    f_discountFeePos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_DISCOUNT_FEE)));
                    f_feeTotalAllPos += Double.parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_FEE_TOTAL)));
                    f_totalFeeCollectedPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED)));
                    f_totalFeeReceivablePos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE)));
                    f_advanceAmountPos += Double
                            .parseDouble(filterStringNumber(item.get(TemplateUtils.MFR_ADVANCE_AMOUNT)));
                }
            }
            footerPos.put("countSuccess", f_countSuccessPos);
            footerPos.put("countFailed", f_countFailedPos);
            footerPos.put("originAmountUsd", f_originAmountUsdPos);
            footerPos.put("originAmountVnd", f_originAmountVndPos);
            footerPos.put("partnerDiscountAmount", f_partnerDiscountAmountPos);
            footerPos.put("merchantDiscountAmount", f_merchantDiscountAmountPos);
            footerPos.put("totalAmountVND", f_totalAmountVNDPos);
            footerPos.put("feeSuccess", f_feeSuccessPos);
            footerPos.put("feeFailed", f_feeFailedPos);
            footerPos.put("feeEcom", f_ecomFeePos);
            footerPos.put("feeIta", f_feeItaPos);
            footerPos.put("discountFee", f_discountFeePos);
            footerPos.put("feeTotalAll", f_feeTotalAllPos);
            footerPos.put("totalFeeCollected", f_totalFeeCollectedPos);
            footerPos.put("totalFeeReceivable", f_totalFeeReceivablePos);
            footerPos.put("advanceAmount", f_advanceAmountPos);
            listDataMapPos.put("footerPos", footerPos);

            String template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT;
            List templateSheetNameList = new ArrayList<>();
            List<String> sheetNameList = new ArrayList<>();
            listBean.add(listDataMapEcom);
            listBean.add(listDataMapPos);
            templateSheetNameList.add("ECOM_TEMPLATE_SHEET");
            templateSheetNameList.add("POS_TEMPLATE_SHEET");
            sheetNameList.add("ECOM_STANDARD");
            sheetNameList.add("POS_STANDARD");

            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListBeanValue(listBean)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    .build().exportExcelMultilSheet();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportDailyFeeReport file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportDailyFeeReport file successfully");
    }

    public static String filterStringNumber(Object data) {
        return data.toString().isEmpty() ? "0" : data.toString();
    }

    public static void generateExportExcelInterReport(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------- START EXPORT INTERNATIONAL REPORT ---------------");
            List<Map> listMap = new InternationalReportGenerator().generate(messageData.getRequestBody());
            Map<String, String> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            String downloadType = messageData.getRequestBody().get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL : messageData.getRequestBody().get(IConstants.DOWNLOADTYPE).toString();

            detailMap.put("FROM_DATE", fromDate);
            detailMap.put("TO_DATE", toDate);
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            List<String> columnList = new ArrayList<String>(Arrays.asList(messageData.getRequestBody().get(COLUMN_LIST).toString().split(",")));

            List<String> columnActive = new ArrayList<String>(Arrays.asList(messageData.getRequestBody().get("other").toString().split(",")));
            columnActive.add("NO");
            columnActive.add("S_DATE");
            columnActive.add("totalTxn");
            columnActive.add("totalTxnAmount");
            columnActive.add("totalItaApprovalAmount");
            List<String> columnInactive = new ArrayList<>(columnList);
            columnInactive.removeAll(columnActive);
            String template = TemplateUtils.TEMPLATE_INTER_REPORT_FILE;
            List<String> columnFormat = new ArrayList<>();
            columnFormat.add("totalTxnAmount");
            columnFormat.add("totalItaApprovalAmount");
            String[] stringArray = {"USD", "SGD", "MYR", "TWD", "CNY", "THB", "TOTAL USD", "TOTAL SGD", "TOTAL MYR", "TOTAL TWD", "TOTAL CNY", "TOTAL THB"};
            List<String> currencyFormat = Arrays.asList(stringArray);
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setListHiddenColumn(columnInactive)
                    .setStart(6)
                    .build()
                    .exportExcelFormatAmount(columnFormat, currencyFormat, "S_CURRENCY");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE INTER REPORT ERROR", e);
        }
        LOGGER.log(Level.INFO, "GENERATE INTER REPORT SUCCESS");
    }

    public static void generateExportExcelTransactionAccounting(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "------------------START EXPORT TRANSACTION ACCOUNTING FILE-------------------------------");
            List<Map> listMap = new ExportTransactionAccoutingGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("fDate"));
            detailMap.put("TO_DATE", parameterPost.get("tDate"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("fDate") + " đến " + parameterPost.get("tDate"));
            String fileTemplate = TemplateUtils.TEMPLATE_TRANSACTION_ACCOUNTING_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateTransactionAccountingFile", e);
        }
        LOGGER.log(Level.INFO, "generateTransactionAccountingFile file successfully");
    }

    public static void generateExportExcelOverLimitTrans(Message<TransOverLimitReq> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "------------------ START EXPORT OVER LIMIT TRANS FILE -------------------------------");
            List<Map> listMap = new TransGenerator().generate(messageData.getRequestBody());

            TransOverLimitReq parameterPost = messageData.getRequestBody();
            String fileTemplate = TemplateUtils.TEMPLATE_OVER_LIMIT_TRANS_FILE;

            if ("csv".equalsIgnoreCase(messageData.getRequestBody().getDownloadType())) {
                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("FROM_DATE", parameterPost.getFromDate());
                detailMap.put("TO_DATE", parameterPost.getToDate());
                detailMap.put("HEADER", "Từ " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
                ArrayList<String> listHeader = new ArrayList<>();
                // listHeader.add("no");
                listHeader.add("acquirer");
                listHeader.add("bankMerchantId");
                listHeader.add("merchantId");
                listHeader.add("transactionId");
                listHeader.add("transactionType");
                listHeader.add("cardNumber");
                listHeader.add("cardType");
                listHeader.add("date");
                listHeader.add("currency");
                listHeader.add("amount");
                listHeader.add("orderInfo");
                listHeader.add("transactionRef");
                listHeader.add("authCode");
                listHeader.add("responseCode");
                listHeader.add("acqResponseCode");
                listHeader.add("ruleName");
                listHeader.add("feeCode");
                listHeader.add("desc");
                listHeader.add("contractType");
                listHeader.add("source");
                String fileTitle = "TRANSACTION";
                String fileDate = "FROM DATE " + parameterPost.getFromDate() + " TO DATE " + parameterPost.getToDate();
                String fileHeader = "No,Acquirer,MPGS/CBS ID,Merchant ID,Transaction ID,Transaction Type,Card Number,Card Type,Date,Currency,Amount,Order Info,Trans Ref,Auth Code,Response Code,ACQ Response Code,Rule Name,Fee Code,Description,Contract Type,Source";
                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(fileTemplate)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsv(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate);
            } else {
                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("FROM_DATE", parameterPost.getFromDate());
                detailMap.put("TO_DATE", parameterPost.getToDate());
                detailMap.put("HEADER", "Từ " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
                List<String> columnFormat = new ArrayList<>();
                columnFormat.add("amount");
                columnFormat.add("itaAmount");
                String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
                List<String> currencyFormat = Arrays.asList(stringArray);

                ReportBuilder.newInstance()
                        .template(fileTemplate)
                        .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap)
                        .setBeanValue("detail", detailMap)
                        .setStart(3)
                        .setMaxCols(50)
                        .build()
                        .exportExcelFormatAmount(columnFormat, currencyFormat, "currency");
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Over limit trans download", e);
        }
        LOGGER.log(Level.INFO, "Over limit trans download file successfully");
    }

    public static void generateExportExcelOverLimitReport(Message<ReportOverLimitReq> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "------------------ START EXPORT OVER LIMIT REPORT FILE -------------------------------");
            List<Map> listMap = new ReportGenerator().generate(messageData.getRequestBody());

            ReportOverLimitReq parameterPost = messageData.getRequestBody();
            String fileTemplate = TemplateUtils.TEMPLATE_OVER_LIMIT_REPORT_FILE;
            Long sumTotal = null;
            for (Map map : listMap) {
                if (map.containsKey("sumTotal")) {
                    sumTotal = (Long) map.get("sumTotal");
                    listMap.remove(map);
                    break;
                }
            }
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.getFromDate());
            detailMap.put("TO_DATE", parameterPost.getToDate());
            detailMap.put("HEADER", "Từ " + parameterPost.getFromDate() + " đến " + parameterPost.getToDate());
            detailMap.put("sumTotal", sumTotal);
            ReportBuilder.newInstance().template(fileTemplate)
            .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
            .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Over limit report download", e);
        }
        LOGGER.log(Level.INFO, "Over limit report download file successfully");
    }

    public static void generateExportExcelTransactionAccounting18(Message<Map<String, Object>> messageData) throws Exception {

            LOGGER.log(Level.INFO,
                    "------------------START EXPORT TRANSACTION ACCOUNTING 18 FILE-------------------------------");
            List<Map> listMap = new TransactionAccoutingGenerator18().generate(messageData.getRequestBody());
            Map<String, Object> mIn = (Map<String, Object>) messageData.getRequestBody();
            List<String> columnInactive = new ArrayList<>();
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            List<String> columnActive = new ArrayList<String>(
                    Arrays.asList(mIn.get(COLUMN_ACTIVE).toString().split(",")));
            Map<String, Object> parameterPost = messageData.getRequestBody();
            String fileTemplate = TemplateUtils.TEMPLATE_TRANSACTION_ACCOUNTING_18_FILE;
            for (String column : columnList) {
                int count = 0;
                for (String columnAct : columnActive) {
                    if (column.compareTo(columnAct) == 0) {
                        count++;
                    }
                }
                if (count == 0) {
                    columnInactive.add(column);
                }
            }
            if ("csv".equalsIgnoreCase(messageData.getRequestBody().get(DOWNLOADTYPE).toString())) {
                Map<String, String> mapColumnHeader = new HashMap<String, String>();
                mapColumnHeader.put("no", "No.");
                mapColumnHeader.put("acquirer", "Acquirer");
                mapColumnHeader.put("merchantId", "Merchant ID");
                mapColumnHeader.put("transactionId", "Transaction ID");
                mapColumnHeader.put("transactionType", "Trans. Type");
                mapColumnHeader.put("cardNumber", "Card Number");
                mapColumnHeader.put("cardType", "Card Type");
                mapColumnHeader.put("date", "Date");
                mapColumnHeader.put("currency", "Currency");
                mapColumnHeader.put("amount", "Amount");
                mapColumnHeader.put("orderInfo", "Order Info");
                mapColumnHeader.put("transactionRef", "Trans Ref");
                mapColumnHeader.put("transactionNo", "Org Ref*");
                mapColumnHeader.put("authCode", "Auth Code");
                mapColumnHeader.put("rrn", "RRN");
                mapColumnHeader.put("responseCode", "Response Code");
                mapColumnHeader.put("operator", "Operator");
                mapColumnHeader.put("binCountry", "BIN Country");
                mapColumnHeader.put("binBank", "Bin Bank");
                mapColumnHeader.put("itaBank", "ITA Bank");
                mapColumnHeader.put("itaTime", "ITA Time");
                mapColumnHeader.put("advanceType", "Adv Type");
                mapColumnHeader.put("platform", "Referral Partner");
                mapColumnHeader.put("bankMerchantId", "Merchant Bank ID");
                mapColumnHeader.put("contractType", "Contract Type");
                mapColumnHeader.put("source", "Source");
                mapColumnHeader.put("tokenNumber", "Token Number");
                mapColumnHeader.put("itaState", "ITA Status");
                mapColumnHeader.put("itaDate", "Approval ITA Date");
                mapColumnHeader.put("itaAmount", "Approval ITA Amount");
                mapColumnHeader.put("parentId", "Original Transaction ID");
                mapColumnHeader.put("refundType", "Refund Type");
                mapColumnHeader.put("acqResponseCode", "ACQ Response Code");
                mapColumnHeader.put("originalAuthCode", "Original Auth Code");
                // Only skip column that are active in browser. This medthod keeps their order
                columnActive.add("currency");
                columnList.add(7, "currency");
                columnList.retainAll(columnActive);
                columnList.remove("no");
                String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
                String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("FROM_DATE", parameterPost.get("fDate"));
                detailMap.put("TO_DATE", parameterPost.get("tDate"));
                detailMap.put("HEADER", "Từ " + parameterPost.get("fDate") + " đến " + parameterPost.get("tDate"));
                ArrayList<String> listHeader = new ArrayList<>();

                String fileTitle = "TRANSACTION";
                String fileDate = "FROM DATE " + parameterPost.get("fDate") + " TO DATE " + parameterPost.get("tDate");

                Map<String, String> mapDate = new HashMap<>();
                mapDate.put("DATE", "dd/MM/yyyy");
                ReportBuilder.newInstance().template(fileTemplate)
                        .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                        .exportCsvSpecsColumns(fileTitle, fileDate, listHeader, listMap, mapDate, mapColumnHeader,
                                columnList);

            } else {

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("FROM_DATE", parameterPost.get("fDate"));
                detailMap.put("TO_DATE", parameterPost.get("tDate"));
                detailMap.put("HEADER", "Từ " + parameterPost.get("fDate") + " đến " + parameterPost.get("tDate"));
                List<String> columnFormat = new ArrayList<>();
                columnFormat.add("amount");
                columnFormat.add("itaAmount");
                String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
                List<String> currencyFormat = Arrays.asList(stringArray);
                if (columnActive.size() > 0) {
                    ReportBuilder.newInstance()
                            .template(fileTemplate)
                            .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                            .setBeanValue("listMap", listMap)
                            .setBeanValue("detail", detailMap)
                            .setListHiddenColumn(columnInactive)
                            .setStart(6)
                            .setMaxCols(50)
                            .build()
                            .exportExcelFormatAmount(columnFormat, currencyFormat, "currency");
                } else {
                    ReportBuilder.newInstance()
                            .template(fileTemplate)
                            .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                            .setBeanValue("listMap", listMap)
                            .setBeanValue("detail", detailMap)
                            .setStart(6)
                            .setMaxCols(50)
                            .build()
                            .exportExcelFormatAmount(columnFormat, currencyFormat, "currency");
                }
            }

        LOGGER.log(Level.INFO, "generateTransactionAccountingFile 18 file successfully");
    }

    public static void generateDownloadAdvExcelSingle(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE EXCEL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            Integer reportId = (Integer) selectedRows.get(0).get(MFR_ID_MONTHLY_REPORT);

            String queryMethod = (String) mIn.get(MFR_QUERY_METHOD);

            Map<String, Object> rIn = new HashMap<>();
            rIn.put(MFR_ID_MONTHLY_REPORT, reportId);
            rIn.put(MFR_QUERY_METHOD, queryMethod);
            rIn.put(MFR_EXPORT_TYPE, "excel");

            List<Map> listMap = new ArrayList<>();
            listMap = new MonthlyFeeReportAdvGenerator().generate(rIn);

            List<Map> trans = new ArrayList<>();
            String template = "";
            Map header = new HashMap<>();
            Map total = new HashMap<>();
            Map footer = new HashMap<>();
            Map fm = new HashMap<>(); // fee-month

            for (Map map : listMap) {
                template = (String) map.get("template");
                if (template == null) {
                    LOGGER.log(Level.WARNING, "Teamplate fee month (id: " + reportId
                            + ") isn't config yet || Monthly report have no transactions");
                    throw IErrors.INTERNAL_SERVER_ERROR;
                } else {
                    trans = (List<Map>) map.get("trans");
                    template = "templates/payment2/detail/" + (String) map.get("template") + ".xls";
                    header = (Map) map.get("header");
                    total = (Map) map.get("total");
                    footer = (Map) map.get("footer");
                    fm = (Map) map.get("fm");
                }
            }

            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("trans", trans)
                    .setBeanValue("header", header)
                    .setBeanValue("fm", fm)
                    .setBeanValue("total", total)
                    .setBeanValue("footer", footer)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvExcel file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvExcel file successfully");
    }

    public static void generateDownloadAdvExcelMultiple(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE EXCEL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            String queryMethod = (String) mIn.get(MFR_QUERY_METHOD);
            List<String> listFilePath = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            for (LinkedHashMap row : selectedRows) {

                Integer reportId = (Integer) row.get(MFR_ID_MONTHLY_REPORT);
                JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                String merchantIds = monthlyFeeReport.getString(MFR_MERCHANT_IDS);
                Long fromDateTime = monthlyFeeReport.getLong(MFR_FROM_DATE_TIME);
                Long toDateTime = monthlyFeeReport.getLong(MFR_TO_DATE_TIME);
                Long toDateTimeView = monthlyFeeReport.getLong(MFR_TO_DATE_TIME_VIEW);
                String payChannels = monthlyFeeReport.getString(MFR_PAY_CHANNELS);
                String receiptType = monthlyFeeReport.getString(MFR_RECEIPT_TYPE);

                Map<String, Object> rIn = new HashMap<>();
                rIn.put(MFR_ID_MONTHLY_REPORT, reportId);
                rIn.put(MFR_EXPORT_TYPE, "excel");
                rIn.put(MFR_QUERY_METHOD, queryMethod);

                String filePathString = Config.getFileExportLocation() + File.separator
                        + customExportFileNameMonthlyFeeReport("BBDS_", receiptType, merchantIds, fromDateTime,
                                toDateTime, toDateTimeView, payChannels)
                        + ".xls";
                Files.deleteIfExists(Paths.get(filePathString));

                List<Map> listMap = new ArrayList<>();
                listMap = new MonthlyFeeReportAdvGenerator().generate(rIn);

                List<Map> trans = new ArrayList<>();
                String template = "";
                Map header = new HashMap<>();
                Map total = new HashMap<>();
                Map footer = new HashMap<>();
                Map fm = new HashMap<>();

                for (Map map : listMap) {
                    template = (String) map.get("template");
                    if (template == null) {
                        LOGGER.log(Level.WARNING, "Teamplate fee month (id: " + reportId + ") isn't config yet");
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    } else {
                        trans = (List<Map>) map.get("trans");
                        template = "templates/payment2/detail/" + (String) map.get("template") + ".xls";
                        header = (Map) map.get("header");
                        total = (Map) map.get("total");
                        footer = (Map) map.get("footer");
                        fm = (Map) map.get("fm");
                    }
                }

                ReportBuilder.newInstance().template(template)
                        .exportFileName(filePathString)
                        .setBeanValue("trans", trans)
                        .setBeanValue("header", header)
                        .setBeanValue("fm", fm)
                        .setBeanValue("total", total)
                        .setBeanValue("footer", footer)
                        .build()
                        .exportExcel();
                listFilePath.add(filePathString);
            }
            if (selectedRows.size() > 1) {
                addToZipFile(listFilePath, messageData);
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvExcel file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvExcel file successfully");
    }

    public static void generateDownloadAdvPdfSingle(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE PDF FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            String queryMethod = (String) mIn.get(MFR_QUERY_METHOD);

            Integer reportId = (Integer) selectedRows.get(0).get(MFR_ID_MONTHLY_REPORT);

            Map<String, Object> rIn = new HashMap<>();
            rIn.put(MFR_ID_MONTHLY_REPORT, reportId);
            rIn.put(MFR_QUERY_METHOD, queryMethod);
            rIn.put(MFR_EXPORT_TYPE, "pdf");

            List<Map> listMap = new ArrayList<>();
            listMap = new MonthlyFeeReportAdvGenerator().generate(rIn);

            String template = "";
            Map parameters = new HashMap<>();
            List<Map> fields = new ArrayList<>();
            for (Map map : listMap) {
                template = (String) map.get("template");
                if (template == null) {
                    LOGGER.log(Level.WARNING, "Teamplate fee month (id: " + reportId + ") isn't config yet");
                    throw IErrors.INTERNAL_SERVER_ERROR;
                } else {
                    template = "templates/payment2/detail/" + template + ".jrxml";
                    parameters = (Map) map.get("params");
                    fields = (List<Map>) map.get("fields");
                }
            }
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListMap(fields)
                    .setParameters(parameters)
                    .build()
                    .exportPdf2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvPdf file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvPdf file successfully");
    }

    public static void generateDownloadAdvPdfMultiple(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE PDF FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            String queryMethod = (String) mIn.get(MFR_QUERY_METHOD);
            ArrayList<MonthlyFeeReportDto> listParams = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            List<String> listFilePath = new ArrayList<>();
            for (LinkedHashMap row : selectedRows) {
                Integer reportId = (Integer) row.get(MFR_ID_MONTHLY_REPORT);
                JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                String merchantIds = monthlyFeeReport.getString(MFR_MERCHANT_IDS);
                Long fromDateTime = monthlyFeeReport.getLong(MFR_FROM_DATE_TIME);
                Long toDateTime = monthlyFeeReport.getLong(MFR_TO_DATE_TIME);
                Long toDateTimeView = monthlyFeeReport.getLong(MFR_TO_DATE_TIME_VIEW);
                String payChannels = monthlyFeeReport.getString(MFR_PAY_CHANNELS);
                String receiptType = monthlyFeeReport.getString(MFR_RECEIPT_TYPE);

                Map<String, Object> rIn = new HashMap<>();
                rIn.put(MFR_ID_MONTHLY_REPORT, reportId);
                rIn.put(MFR_QUERY_METHOD, queryMethod);
                rIn.put(MFR_EXPORT_TYPE, "pdf");

                String filePathString = Config.getFileExportLocation() + File.separator
                        + customExportFileNameMonthlyFeeReport("BBDS_", receiptType, merchantIds, fromDateTime,
                                toDateTime, toDateTimeView, payChannels)
                        + ".pdf";
                Files.deleteIfExists(Paths.get(filePathString));

                List<Map> listMap = new ArrayList<>();
                listMap = new MonthlyFeeReportAdvGenerator().generate(rIn);

                String template = "";
                Map parameters = new HashMap<>();
                List<Map> fields = new ArrayList<>();
                for (Map map : listMap) {
                    template = (String) map.get("template");
                    if (template == null) {
                        LOGGER.log(Level.WARNING, "Teamplate fee month (id: " + reportId + ") isn't config yet");
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    } else {
                        template = "templates/payment2/detail/" + (String) map.get("template") + ".jrxml";
                        parameters = (Map) map.get("params");
                        fields = (List<Map>) map.get("fields");
                    }
                }
                ReportBuilder.newInstance().template(template)
                        .exportFileName(filePathString)
                        .setListMap(fields)
                        .setParameters(parameters)
                        .build()
                        .exportPdf2();
                listFilePath.add(filePathString);
            }

            addToZipFile(listFilePath, messageData);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvPdf file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvPdf file successfully");
    }

    public static String[] intersectionMerchantIdsWithConfig(String merchantIds) {
        String[] merchantIdArr = merchantIds.split(",");
        String[] specialMerchantIds = getSpecialMerchantIdsFromConfig().split(",");
        String[] intersectionMerchantIds = Arrays.stream(merchantIdArr)
                .distinct()
                .filter(x -> Arrays.asList(specialMerchantIds).contains(x))
                .toArray(String[]::new);
        return intersectionMerchantIds;
    }

    public static String filterSpecialMerchantIds(String merchantIds) {
        String returnMerchantIds = null;
        if (merchantIds != null) {
            String[] merchantIdArr = merchantIds.split(",");
            String[] intersectionMerchantIds = intersectionMerchantIdsWithConfig(merchantIds);
            returnMerchantIds = intersectionMerchantIds.length <= 0
                    ? Util.convertObjectArrayToString(merchantIdArr, ",")
                    : Util.convertObjectArrayToString(intersectionMerchantIds, ",");
        }
        return returnMerchantIds;
    }

    public static String getSpecialMerchantIdsFromConfig() {
        return Config.getString("payment2.special_merchant_ids", "");
    }

    public static String customExportFileNameMonthlyFeeReport(String prefixFileName, String receiptType,
            String merchantIds, Long fromDateTime, Long toDateTime, Long toDateTimeView, String payChannels) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy");
        // filter for special merchant ids
        String returnMerchantIds = filterSpecialMerchantIds(merchantIds);

        // process time by cutoff config
        Date fromDate = null, toDate = null;
        String formatedFromDate = null, formatedToDate = null;
        String formatedMerchantIds = null;
        String formatedPayChannels = null;

        if (fromDateTime != null && toDateTimeView != null) {
            if (receiptType.contains("by_advance_date")) {
                fromDate = new Date(fromDateTime);
                toDate = new Date(toDateTimeView);
            }
            if (receiptType.contains("by_transaction_date")) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(fromDateTime);
                int hoursFromDate = calendar.get(Calendar.HOUR_OF_DAY);
                // cutoff 24h
                if (hoursFromDate == 0) {
                    fromDate = new Date(fromDateTime);
                    toDate = new Date(toDateTimeView);
                }
                // cutoff 17h
                if (hoursFromDate == 17) {
                    long plusHours = 7 * 60 * 60 * 1000; // 7 hours
                    fromDate = new Date(fromDateTime + plusHours);
                    toDate = new Date(toDateTime);
                }
            }
        }
        formatedFromDate = fromDate == null ? "" : (sdf.format(fromDate) + "_");
        formatedToDate = toDate == null ? "" : (sdf.format(toDate) + "_");
        formatedMerchantIds = returnMerchantIds == null ? "" : (returnMerchantIds.replaceAll(",", "_") + "_");
        formatedPayChannels = payChannels == null ? "" : (payChannels + "_");

        return prefixFileName + formatedMerchantIds + formatedFromDate + formatedToDate + formatedPayChannels
                + System.currentTimeMillis();
    }

    public static void generateDownloadAdvDetailTransExcelSingle(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE DETAIL TRANSACTION EXCEL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            List<String> listFilePath = new ArrayList<>();
            Integer reportId = (Integer) selectedRows.get(0).get(MFR_ID_MONTHLY_REPORT);
            JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
            Long fromDateTime = monthlyFeeReport.getLong(MFR_FROM_DATE_TIME);
            Long toDateTime = monthlyFeeReport.getLong(MFR_TO_DATE_TIME);
            String typeAdvances = monthlyFeeReport.getString(MFR_TYPE_ADVANCES);

            Map<String, Object> rIn = new HashMap<>();
            rIn.put(MFR_ID_MONTHLY_REPORT, reportId);
            rIn.put(MFR_FROM_DATE_TIME, fromDateTime);
            rIn.put(MFR_TO_DATE_TIME, toDateTime);

            // process header
            // Map<String, Object> header = new HashMap<>();
            // header.put("S_TYPE_ADVANCES", typeAdvances);

            List<Map> listMap = new MonthlyFeeReportAdvDetailGenerator().generate(rIn);
            List<Map> listMapParent = new ArrayList<>();
            List<Map> listMapChilds = new ArrayList<>();
            String specialMerchantId = null;
            boolean isObjectFee = false;
            List<Map<String, Object>> listBean = new ArrayList<>();

            for (Map<String, Object> listMapItem : listMap) {
                if (listMapItem.containsKey("listMapParent")) {
                    listMapParent = (List<Map>) listMapItem.get("listMapParent");
                }
                if (listMapItem.containsKey("listMapChilds")) {
                    listMapChilds = (List<Map>) listMapItem.get("listMapChilds");
                }
                if (listMapItem.containsKey("specialMerchantId")) {
                    specialMerchantId = (String) listMapItem.get("specialMerchantId");
                }
                if (listMapItem.containsKey("isObjectFee")) {
                    isObjectFee = (boolean) listMapItem.get("isObjectFee");
                }
            }

            Map<String, Object> listDataParent = new HashMap<>();
            listDataParent.put("listMapParent", listMapParent);
            Map<String, Object> listDataChilds = new HashMap<>();
            listDataChilds.put("listMapChilds", listMapChilds);

            // process footer parent
            Map<String, Object> footerParent = new HashMap<>();
            Double amountAfterSumParent = 0.0;
            Double totalFeeSumParent = 0.0;
            Double advanceAmountSumParent = 0.0;
            Double advanceAmountPart1SumParent = 0.0;
            for (Map<String, Object> item : listMapParent) {
                amountAfterSumParent += item.get(TemplateUtils.MFR_AMOUNT_AFTER) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_AMOUNT_AFTER);
                totalFeeSumParent += item.get(TemplateUtils.MFR_TOTAL_FEE) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE);
                advanceAmountSumParent += item.get(TemplateUtils.MFR_ADV_AMOUNT) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT);
                advanceAmountPart1SumParent += item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1);
            }
            footerParent.put(TemplateUtils.MFR_AMOUNT_AFTER_SUM, amountAfterSumParent);
            footerParent.put(TemplateUtils.MFR_TOTAL_FEE_SUM, totalFeeSumParent);
            footerParent.put(TemplateUtils.MFR_ADV_AMOUNT_SUM, advanceAmountSumParent);
            footerParent.put(TemplateUtils.MFR_ADV_AMOUNT_PART1_SUM, advanceAmountPart1SumParent);
            listDataParent.put("footerParent", footerParent);

            // process footer childs
            Map<String, Object> footerChilds = new HashMap<>();
            Double amountAfterSumChilds = 0.0;
            Double totalFeeSumChilds = 0.0;
            Double advanceAmountSumChilds = 0.0;
            Double advanceAmountPart1SumChilds = 0.0;
            Double advanceAmountPart2SumChilds = 0.0;
            Double totalCollectedFeeSumChilds = 0.0;
            Double totalReceivableFeeSumChilds = 0.0;
            for (Map<String, Object> item : listMapChilds) {
                amountAfterSumChilds += item.get(TemplateUtils.MFR_AMOUNT_AFTER) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_AMOUNT_AFTER);
                totalFeeSumChilds += item.get(TemplateUtils.MFR_TOTAL_FEE) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE);
                advanceAmountSumChilds += item.get(TemplateUtils.MFR_ADV_AMOUNT) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT);
                advanceAmountPart1SumChilds += item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1);
                advanceAmountPart2SumChilds += item.get(TemplateUtils.MFR_ADV_AMOUNT_PART2) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT_PART2);
                totalCollectedFeeSumChilds += item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE_COLLECTED);
                totalReceivableFeeSumChilds += item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE) == null ? 0.0
                        : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE);
            }
            footerChilds.put(TemplateUtils.MFR_AMOUNT_AFTER_SUM, amountAfterSumChilds);
            footerChilds.put(TemplateUtils.MFR_TOTAL_FEE_SUM, totalFeeSumChilds);
            footerChilds.put(TemplateUtils.MFR_ADV_AMOUNT_SUM, advanceAmountSumChilds);
            footerChilds.put(TemplateUtils.MFR_ADV_AMOUNT_PART1_SUM, advanceAmountPart1SumChilds);
            footerChilds.put(TemplateUtils.MFR_ADV_AMOUNT_PART2_SUM, advanceAmountPart2SumChilds);
            footerChilds.put(TemplateUtils.MFR_TOTAL_FEE_COLLECTED_SUM, totalCollectedFeeSumChilds);
            footerChilds.put(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE_SUM, totalReceivableFeeSumChilds);
            listDataChilds.put("footerChilds", footerChilds);

            String template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_ADV_DETAIL_TRANS;
            List templateSheetNameList = new ArrayList<>();
            List<String> sheetNameList = new ArrayList<>();
            if (specialMerchantId != null) {
                template += "_" + specialMerchantId.toLowerCase();
                templateSheetNameList.add("TEMPLATE-PARENT");
                templateSheetNameList.add("TEMPLATE-CHILDS");
                sheetNameList.add("CHI TIET THANG - " + specialMerchantId);
                sheetNameList.add("CHI TIET THANG - CHI NHANH");
                listBean.add(listDataParent);
                listBean.add(listDataChilds);
            } else {
                templateSheetNameList.add("TEMPLATE");
                sheetNameList.add("CHI TIET THANG");
                listBean.add(listDataChilds);
            }
            if (isObjectFee) {
                template += "_object_fee";
            }
            template += ".xls";

            LOGGER.info("TEMPLATE DETAIL TRANS: " + template);

            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListBeanValue(listBean)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    .build().exportExcelMultilSheet();

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvDetailTransExcel file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvDetailTransExcel file successfully");
    }

    public static void generateDownloadAdvDetailTransExcelMultiple(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE MONTHLY FEE REPORT ADVANCE DETAIL TRANSACTION EXCEL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            ArrayList<LinkedHashMap> selectedRows = (ArrayList<LinkedHashMap>) mIn.get(MFR_SELECTED_ROWS);
            List<String> listFilePath = new ArrayList<>();
            for (LinkedHashMap row : selectedRows) {
                Integer reportId = (Integer) row.get(MFR_ID_MONTHLY_REPORT);
                JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);
                Long fromDateTime = monthlyFeeReport.getLong(MFR_FROM_DATE_TIME);
                Long toDateTime = monthlyFeeReport.getLong(MFR_TO_DATE_TIME);
                Long toDateTimeView = monthlyFeeReport.getLong(MFR_TO_DATE_TIME_VIEW);
                String typeAdvances = monthlyFeeReport.getString(MFR_TYPE_ADVANCES);
                String payChannels = monthlyFeeReport.getString(MFR_PAY_CHANNELS);
                String merchantIds = monthlyFeeReport.getString(MFR_MERCHANT_IDS);
                String receiptType = monthlyFeeReport.getString(MFR_RECEIPT_TYPE);

                mIn.put(MFR_ID_MONTHLY_REPORT, reportId);
                mIn.put(MFR_FROM_DATE_TIME, fromDateTime);
                mIn.put(MFR_TO_DATE_TIME, toDateTime);
                // process header
                // Map<String, Object> header = new HashMap<>();
                // header.put("S_TYPE_ADVANCES", row.get(MFR_TYPE_ADVANCE));

                String filePathString = Config.getFileExportLocation() + File.separator
                        + customExportFileNameMonthlyFeeReport("BB_CHI TIET GD_", receiptType, merchantIds,
                                fromDateTime, toDateTime, toDateTimeView, payChannels)
                        + ".xls";
                Files.deleteIfExists(Paths.get(filePathString));

                List<Map> listMap = new MonthlyFeeReportAdvDetailGenerator().generate(mIn);
                List<Map> listMapParent = new ArrayList<>();
                List<Map> listMapChilds = new ArrayList<>();
                String specialMerchantId = null;
                List<Map<String, Object>> listBean = new ArrayList<>();

                for (Map<String, Object> listMapItem : listMap) {
                    if (listMapItem.containsKey("listMapParent")) {
                        listMapParent = (List<Map>) listMapItem.get("listMapParent");
                    }
                    if (listMapItem.containsKey("listMapChilds")) {
                        listMapChilds = (List<Map>) listMapItem.get("listMapChilds");
                    }
                    if (listMapItem.containsKey("specialMerchantId")) {
                        specialMerchantId = (String) listMapItem.get("specialMerchantId");
                    }
                }

                Map<String, Object> listDataParent = new HashMap<>();
                listDataParent.put("listMapParent", listMapParent);
                Map<String, Object> listDataChilds = new HashMap<>();
                listDataChilds.put("listMapChilds", listMapChilds);

                // process footer parent
                Map<String, Object> footerParent = new HashMap<>();
                Double amountAfterSumParent = 0.0;
                Double totalFeeSumParent = 0.0;
                Double advanceAmountSumParent = 0.0;
                Double advanceAmountPart1SumParent = 0.0;
                for (Map<String, Object> item : listMapParent) {
                    amountAfterSumParent += item.get(TemplateUtils.MFR_AMOUNT_AFTER) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_AMOUNT_AFTER);
                    totalFeeSumParent += item.get(TemplateUtils.MFR_TOTAL_FEE) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE);
                    advanceAmountSumParent += item.get(TemplateUtils.MFR_ADV_AMOUNT) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT);
                    advanceAmountPart1SumParent += item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1);
                }
                footerParent.put(TemplateUtils.MFR_AMOUNT_AFTER_SUM, amountAfterSumParent);
                footerParent.put(TemplateUtils.MFR_TOTAL_FEE_SUM, totalFeeSumParent);
                footerParent.put(TemplateUtils.MFR_ADV_AMOUNT_SUM, advanceAmountSumParent);
                footerParent.put(TemplateUtils.MFR_ADV_AMOUNT_PART1_SUM, advanceAmountPart1SumParent);
                listDataParent.put("footerParent", footerParent);

                // process footer childs
                Map<String, Object> footerChilds = new HashMap<>();
                Double amountAfterSumChilds = 0.0;
                Double totalFeeSumChilds = 0.0;
                Double advanceAmountSumChilds = 0.0;
                Double advanceAmountPart1SumChilds = 0.0;
                for (Map<String, Object> item : listMapChilds) {
                    amountAfterSumChilds += item.get(TemplateUtils.MFR_AMOUNT_AFTER) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_AMOUNT_AFTER);
                    totalFeeSumChilds += item.get(TemplateUtils.MFR_TOTAL_FEE) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_TOTAL_FEE);
                    advanceAmountSumChilds += item.get(TemplateUtils.MFR_ADV_AMOUNT) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT);
                    advanceAmountPart1SumChilds += item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1) == null ? 0.0
                            : (Double) item.get(TemplateUtils.MFR_ADV_AMOUNT_PART1);
                }
                footerChilds.put(TemplateUtils.MFR_AMOUNT_AFTER_SUM, amountAfterSumChilds);
                footerChilds.put(TemplateUtils.MFR_TOTAL_FEE_SUM, totalFeeSumChilds);
                footerChilds.put(TemplateUtils.MFR_ADV_AMOUNT_SUM, advanceAmountSumChilds);
                footerChilds.put(TemplateUtils.MFR_ADV_AMOUNT_PART1_SUM, advanceAmountPart1SumChilds);
                listDataChilds.put("footerChilds", footerChilds);

                String template = TemplateUtils.TEMPLATE_MONTHLY_FEE_REPORT_ADV_DETAIL_TRANS;
                List templateSheetNameList = new ArrayList<>();
                List<String> sheetNameList = new ArrayList<>();
                if (specialMerchantId != null) {
                    template += "_" + specialMerchantId.toLowerCase() + ".xls";
                    templateSheetNameList.add("TEMPLATE-PARENT");
                    templateSheetNameList.add("TEMPLATE-CHILDS");
                    sheetNameList.add("CHI TIET THANG - " + specialMerchantId);
                    sheetNameList.add("CHI TIET THANG - CHI NHANH");
                    listBean.add(listDataParent);
                    listBean.add(listDataChilds);
                } else {
                    template += ".xls";
                    templateSheetNameList.add("TEMPLATE");
                    sheetNameList.add("CHI TIET THANG");
                    listBean.add(listDataChilds);
                }
                LOGGER.info("TEMPLATE DETAIL TRANS: " + template);

                ReportBuilder.newInstance()
                        .template(template)
                        .exportFileName(filePathString)
                        .setListBeanValue(listBean)
                        .setSheetNameList(sheetNameList)
                        .setTemplateSheetNameList(templateSheetNameList)
                        .build().exportExcelMultilSheet();
                listFilePath.add(filePathString);
            }
            addToZipFile(listFilePath, messageData);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDownloadAdvDetailTransExcel file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDownloadAdvDetailTransExcel file successfully");
    }

    public static void generateExportSSTransManagement(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE FILE CSV FOR SS GENERAL SEARCH--------------------");
            List<Map> listMap = new ExportSSTransManagementGenerator().generate(messageData.getRequestBody());
            Map<String, Object> mIn = messageData.getRequestBody();
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            List<String> columnActive = new ArrayList<String>(
                    Arrays.asList(mIn.get(COLUMN_ACTIVE).toString().split(",")));
            Map<String, String> mapColumnHeader = new HashMap<String, String>();
            mapColumnHeader.put("no", "No.");
            mapColumnHeader.put("gate", "Gate");
            mapColumnHeader.put("partnerName", "Partner Name");
            mapColumnHeader.put("referralPartner", "Platform");
            mapColumnHeader.put("acquirer", "Acquirer");
            mapColumnHeader.put("merchantId", "Merchant ID");
            mapColumnHeader.put("invoiceId", "Invoice ID");
            mapColumnHeader.put("transId", "Trans. ID");
            mapColumnHeader.put("paymentId", "Payment ID");
            mapColumnHeader.put("orderRef", "Order Ref.");
            mapColumnHeader.put("merchantTransRef", "Merchant Trans. Ref");
            mapColumnHeader.put("qrId", "QR ID");
            mapColumnHeader.put("qrChannel", "Channel");
            mapColumnHeader.put("cardType", "Card List");
            mapColumnHeader.put("cardNumber", "Card Number");
            mapColumnHeader.put("source", "Source");
            mapColumnHeader.put("binCountry", "BIN Country");
            mapColumnHeader.put("issuer", "Issuer");
            mapColumnHeader.put("installmentBank", "Installment Bank");
            mapColumnHeader.put("installmentPeriod", "Installment Period");
            mapColumnHeader.put("promotionCode", "Promotion Code");
            mapColumnHeader.put("promotionName", "Promotion Name");
            mapColumnHeader.put("originalAmount", "Original Amount");
            mapColumnHeader.put("transAmount", "Trans. Amount");
            mapColumnHeader.put("originalDate", "Original Date");
            mapColumnHeader.put("transDate", "Trans. Date");
            mapColumnHeader.put("transType", "Trans. Type");
            mapColumnHeader.put("authenState", "Authentication State");
            mapColumnHeader.put("responseCode", "Response Code");
            mapColumnHeader.put("invoiceState", "Invoice State");
            mapColumnHeader.put("transState", "Trans. State");
            mapColumnHeader.put("authoCode", "Authorization Code");
            mapColumnHeader.put("installmentStatus", "Installment Status");
            mapColumnHeader.put("inteType", "Integration Type");
            mapColumnHeader.put("theme", "Theme");
            mapColumnHeader.put("merchantChannel", "Merchant Channel");
            mapColumnHeader.put("reply", "Acquirer Code");
            mapColumnHeader.put("replyMessage", "Acquirer Message");
            mapColumnHeader.put("reasonCode", "Gateway Code");
            mapColumnHeader.put("authenticationId", "Authentication ID");
            mapColumnHeader.put("networkTransId", "Network Trans ID");
            mapColumnHeader.put("requestId", "Request ID");
            mapColumnHeader.put("cardVerificationInfo", "Card Verification Info");
            mapColumnHeader.put("providerMessage", "Provider Message");
            mapColumnHeader.put("currency", "Currency");
            mapColumnHeader.put("quicklinkId", "Quicklink Id");
            mapColumnHeader.put("tokenNumber", "Token Number");
            mapColumnHeader.put("bankTransId", "Bank Trans ID");

            // Only skip column that are active in browser. This medthod keeps their order
            columnList.retainAll(columnActive);
            columnList.remove("no");

            Map<String, Object> detailMap = new HashMap<>();
            String fromDate = messageData.getRequestBody().get(IConstants.FROM_DATE).toString();
            String toDate = messageData.getRequestBody().get(IConstants.TO_DATE).toString();
            detailMap.put("RANGE_DATE", "FROM " + fromDate + " TO " + toDate);
            String template = TemplateUtils.TEMPLATE_PAYOUT_FUNDS_TRANS_TOPUP_FILE;
            ArrayList<String> listHeader = new ArrayList<>();

            String fileTitle = "TRANSACTION MANAGEMENT";
            String fileDate = "FROM DATE " + fromDate + " TO DATE " + toDate;

            Map<String, String> mapDate = new HashMap<>();
            mapDate.put("DATE", "dd/MM/yyyy");
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                    .exportCsvSpecsColumns(fileTitle, fileDate, listHeader, listMap, mapDate, mapColumnHeader,
                            columnList);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "FAILED GENERATE FILE CSV FOR SS GENERAL SEARCH: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO,
                "-------------------- SUCCESSFULLY GENERATE FILE CSV FOR SS GENERAL SEARCH--------------------");
    }

    public static void generateExportSSTransManagementPayment(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START export SS Trans Management Payment file--------------------");
            List<Map> listData = new ExportSSTransManagementGeneratorPayment().generate(messageData.getRequestBody());
            Map<String, Object> mIn = messageData.getRequestBody();
            List<String> columnInactive = new ArrayList<>();
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            List<String> columnActive = new ArrayList<String>(
                    Arrays.asList(mIn.get(COLUMN_ACTIVE).toString().split(",")));
            for (String colum : columnList) {
                int count = 0;
                for (String columAct : columnActive) {
                    if (colum.compareTo(columAct) == 0) {
                        count++;
                    }
                }
                if (count == 0) {
                    columnInactive.add(colum);
                }
            }
            String template = TemplateUtils.TEMPLATE_SS_TRANS_MANAGEMENT_FILE_PAYMENT;
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listData)
                    .setBeanValue("dateRanger", mIn.get(FROM_DATE) + " - " + mIn.get(TO_DATE))
                    .setStart(6)
                    .build()
                    .exportExcel2();

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportSSTransManagementPayment file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportSSTransManagementPaymet file successfully");
    }

    public static void generatePreAdvanceDetail(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "----------------START GENERATE PREFIX ADVANCE DETAIL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            String type = (String) mIn.get("type");
            List<Map> listMap = new PreAdvanceDetailGenerator().generate(mIn);
            if (listMap.isEmpty()) {
                LOGGER.log(Level.SEVERE, "listMap is empty");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            PrefixAdvanceDto dataDto = (PrefixAdvanceDto) listMap.get(0).get("data");
            Map<String, Object> detailMap = new HashMap<>();
            DateFormat dfPreAdvance = new SimpleDateFormat("dd/MM/yyyy");

            // process header
            detailMap.put("partnerName", dataDto.getPartnerName());
            detailMap.put("contractId", dataDto.getContractCode());
            detailMap.put("taxCode", dataDto.getTaxCode());
            detailMap.put("twLastTotal",
                    Double.parseDouble(dataDto.getTwLastTotal() == null ? "0" : dataDto.getTwLastTotal()));
            detailMap.put("advanceId", dataDto.getAdvanceId());
            detailMap.put("advType", dataDto.getAdvType());
            detailMap.put("createDate", dfPreAdvance.format(new Date(dataDto.getCreateDate().getTime())));
            detailMap.put("advanceConfigCode", dataDto.getAdvanceConfigCode());
            detailMap.put("transferBank", dataDto.getTransferBank());
            detailMap.put("transferDate",
                    null != dataDto.getTransferDate()
                            ? dfPreAdvance.format(new Date(dataDto.getTransferDate().getTime()))
                            : "");
            detailMap.put("accountType", dataDto.getAccountType());
            detailMap.put("status", dataDto.getStatus());
            detailMap.put("receiveBank", dataDto.getReceiveBank());
            detailMap.put("accountNumber", dataDto.getAccountNumber());
            detailMap.put("transferContent", dataDto.getTransferContent());

            List<?> listPayout = (List<?>) Optional.ofNullable(dataDto.getLastTotal().get("payout")).orElse(new ArrayList<>());
            if (listPayout.size() > 0) {
                detailMap.put("payout", listPayout.get(0));
            }

            List<String> listTimeHeaderSummary = new ArrayList<>();
            List<String> listTimeHeaderInt = new ArrayList<>();
            List<String> listTimeHeaderDom = new ArrayList<>();
            List<String> listTimeHeaderQr = new ArrayList<>();
            List<String> listTimeHeaderSms = new ArrayList<>();
            List<String> listTimeHeaderBl = new ArrayList<>();
            List<String> listTimeHeaderBnpl = new ArrayList<>();
            List<String> listTimeHeaderUpos = new ArrayList<>();
            List<String> listTimeHeaderPc = new ArrayList<>();
            List<String> listTimeHeaderVietQr = new ArrayList<>();
            List<String> listTimeHeaderDd = new ArrayList<>();

            // International
            List<Map> listIntSum = new ArrayList<>(); // process level 1
            List<Map> titleTKIntSum = new ArrayList<>();
            List<Map> contentTKIntSum = new ArrayList<>();
            List<Map> titleLKIntSum = new ArrayList<>();
            List<Map> contentLKIntSum = new ArrayList<>();
            List<Map> titleBSIntSum = new ArrayList<>();
            List<Map> contentBSIntSum = new ArrayList<>();
            List<Map> titleNKIntSum = new ArrayList<>();
            List<Map> contentNKIntSum = new ArrayList<>();
            if (null != dataDto.getInternational() && dataDto.getInternational().size() > 0) {
                Map intSum = (Map) dataDto.getInternational().get("summary");
                Map groupIntSum = (Map) dataDto.getInternational().get("group");
                Date fromDateIntSum = null;
                Date toDateIntSum = null;
                if (!"".equals((String) intSum.get("from"))) {
                    fromDateIntSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) intSum.get("from"));
                }
                if (!"".equals((String) intSum.get("view_to"))) {
                    toDateIntSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) intSum.get("view_to"));
                }
                StringBuilder timeQt = new StringBuilder("Quốc tế : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateIntSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateIntSum));
                listTimeHeaderSummary.add(timeQt.toString());
                listTimeHeaderInt.add(timeQt.toString());
                listIntSum.add(intSum);

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A1")) { // process level 2
                        Map titleTKIntSumNode = (Map) groupIntSum.get("A1");
                        Date fromDateTKIntSumNode = null;
                        Date toDateTKIntSumNode = null;
                        if (!"".equals((String) titleTKIntSumNode.get("from"))) {
                            fromDateTKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKIntSumNode.get("view_to"))) {
                            toDateTKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKIntSumNode) + ")";
                        titleTKIntSumNode.put("time", time);
                        titleTKIntSum.add(titleTKIntSumNode);
                        contentTKIntSum = (List<Map>) titleTKIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A2")) { // process level 2
                        Map titleLKIntSumNode = (Map) groupIntSum.get("A2");
                        Date fromDateLKIntSumNode = null;
                        Date toDateLKIntSumNode = null;
                        if (!"".equals((String) titleLKIntSumNode.get("from"))) {
                            fromDateLKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKIntSumNode.get("view_to"))) {
                            toDateLKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKIntSumNode) + ")";
                        titleLKIntSumNode.put("time", time);
                        titleLKIntSum.add(titleLKIntSumNode);
                        contentLKIntSum = (List<Map>) titleLKIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A3")) { // process level 2
                        Map titleBSIntSumNode = (Map) groupIntSum.get("A3");
                        Date fromDateBSIntSumNode = null;
                        Date toDateBSIntSumNode = null;
                        if (!"".equals((String) titleBSIntSumNode.get("from"))) {
                            fromDateBSIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSIntSumNode.get("view_to"))) {
                            toDateBSIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSIntSumNode) + ")";
                        titleBSIntSumNode.put("time", time);
                        titleBSIntSum.add(titleBSIntSumNode);
                        contentBSIntSum = (List<Map>) titleBSIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A4")) { // process level 2
                        Map titleNKIntSumNode = (Map) groupIntSum.get("A4");
                        Date fromDateNKIntSumNode = null;
                        Date toDateNKIntSumNode = null;
                        if (!"".equals((String) titleNKIntSumNode.get("from"))) {
                            fromDateNKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKIntSumNode.get("view_to"))) {
                            toDateNKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKIntSumNode) + ")";
                        titleNKIntSumNode.put("time", time);
                        titleNKIntSum.add(titleNKIntSumNode);
                        contentNKIntSum = (List<Map>) titleNKIntSumNode.get("list");
                    }
                }
            }

            // Domestic
            List<Map> listDomSum = new ArrayList<>(); // process level 1
            List<Map> titleTKDomSum = new ArrayList<>();
            List<Map> contentTKDomSum = new ArrayList<>();
            List<Map> titleLKDomSum = new ArrayList<>();
            List<Map> contentLKDomSum = new ArrayList<>();
            List<Map> titleBSDomSum = new ArrayList<>();
            List<Map> contentBSDomSum = new ArrayList<>();
            if (null != dataDto.getDomestic() && dataDto.getDomestic().size() > 0) {
                Map domSum = (Map) dataDto.getDomestic().get("summary");
                Map groupDomSum = (Map) dataDto.getDomestic().get("group");
                Date fromDateDomSum = null;
                Date toDateDomSum = null;
                if (!"".equals((String) domSum.get("from"))) {
                    fromDateDomSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) domSum.get("from"));
                }
                if (!"".equals((String) domSum.get("view_to"))) {
                    toDateDomSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) domSum.get("view_to"));
                }
                StringBuilder timeNd = new StringBuilder("Nội địa: Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateDomSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateDomSum));
                listTimeHeaderSummary.add(timeNd.toString());
                listTimeHeaderDom.add(timeNd.toString());
                listDomSum.add(domSum);

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B1")) { // process level 2
                        Map titleTKDomSumNode = (Map) groupDomSum.get("B1");
                        Date fromDateTKDomSumNode = null;
                        Date toDateTKDomSumNode = null;
                        if (!"".equals((String) titleTKDomSumNode.get("from"))) {
                            fromDateTKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKDomSumNode.get("view_to"))) {
                            toDateTKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKDomSumNode) + ")";
                        titleTKDomSumNode.put("time", time);
                        titleTKDomSum.add(titleTKDomSumNode);
                        contentTKDomSum = (List<Map>) titleTKDomSumNode.get("list");
                    }
                }

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B2")) { // process level 2
                        Map titleLKDomSumNode = (Map) groupDomSum.get("B2");
                        Date fromDateLKDomSumNode = null;
                        Date toDateLKDomSumNode = null;
                        if (!"".equals((String) titleLKDomSumNode.get("from"))) {
                            fromDateLKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKDomSumNode.get("view_to"))) {
                            toDateLKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKDomSumNode) + ")";
                        titleLKDomSumNode.put("time", time);
                        titleLKDomSum.add(titleLKDomSumNode);
                        contentLKDomSum = (List<Map>) titleLKDomSumNode.get("list");
                    }
                }

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B3")) { // process level 2
                        Map titleBSDomSumNode = (Map) groupDomSum.get("B3");
                        Date fromDateBSDomSumNode = null;
                        Date toDateBSDomSumNode = null;
                        if (!"".equals((String) titleBSDomSumNode.get("from"))) {
                            fromDateBSDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSDomSumNode.get("view_to"))) {
                            toDateBSDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSDomSumNode) + ")";
                        titleBSDomSumNode.put("time", time);
                        titleBSDomSum.add(titleBSDomSumNode);
                        contentBSDomSum = (List<Map>) titleBSDomSumNode.get("list");
                    }
                }
            }

            // QR
            List<Map> listQrSum = new ArrayList<>(); // process level 1
            List<Map> titleTKQrSum = new ArrayList<>();
            List<Map> contentTKQrSum = new ArrayList<>();
            List<Map> titleLKQrSum = new ArrayList<>();
            List<Map> contentLKQrSum = new ArrayList<>();
            List<Map> titleBSQrSum = new ArrayList<>();
            List<Map> contentBSQrSum = new ArrayList<>();
            if (null != dataDto.getApp() && dataDto.getApp().size() > 0) {
                Map qrSum = (Map) dataDto.getApp().get("summary");
                Map groupQrSum = (Map) dataDto.getApp().get("group");
                Date fromDateQrSum = null;
                Date toDateQrSum = null;
                if (!"".equals((String) qrSum.get("from"))) {
                    fromDateQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) qrSum.get("from"));
                }
                if (!"".equals((String) qrSum.get("view_to"))) {
                    toDateQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) qrSum.get("view_to"));
                }
                StringBuilder timeQR = new StringBuilder("Ví,App: Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateQrSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateQrSum));
                listTimeHeaderSummary.add(timeQR.toString());
                listTimeHeaderQr.add(timeQR.toString());
                listQrSum.add(qrSum);

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C1")) { // process level 2
                        Map titleTKQrSumNode = (Map) groupQrSum.get("C1");
                        Date fromDateTKQrSumNode = null;
                        Date toDateTKQrSumNode = null;
                        if (!"".equals((String) titleTKQrSumNode.get("from"))) {
                            fromDateTKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKQrSumNode.get("view_to"))) {
                            toDateTKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKQrSumNode) + ")";
                        titleTKQrSumNode.put("time", time);
                        titleTKQrSum.add(titleTKQrSumNode);
                        contentTKQrSum = (List<Map>) titleTKQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C2")) { // process level 2
                        Map titleLKQrSumNode = (Map) groupQrSum.get("C2");
                        Date fromDateLKQrSumNode = null;
                        Date toDateLKQrSumNode = null;
                        if (!"".equals((String) titleLKQrSumNode.get("from"))) {
                            fromDateLKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKQrSumNode.get("view_to"))) {
                            toDateLKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKQrSumNode) + ")";
                        titleLKQrSumNode.put("time", time);
                        titleLKQrSum.add(titleLKQrSumNode);
                        contentLKQrSum = (List<Map>) titleLKQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C3")) { // process level 2
                        Map titleBSQrSumNode = (Map) groupQrSum.get("C3");
                        Date fromDateBSQrSumNode = null;
                        Date toDateBSQrSumNode = null;
                        if (!"".equals((String) titleBSQrSumNode.get("from"))) {
                            fromDateBSQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSQrSumNode.get("view_to"))) {
                            toDateBSQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSQrSumNode) + ")";
                        titleBSQrSumNode.put("time", time);
                        titleBSQrSum.add(titleBSQrSumNode);
                        contentBSQrSum = (List<Map>) titleBSQrSumNode.get("list");
                    }
                }
            }

            // Sms
            List<Map> listSmsSum = new ArrayList<>(); // process level 1
            List<Map> titleTKSmsSum = new ArrayList<>();
            List<Map> contentTKSmsSum = new ArrayList<>();
            List<Map> titleLKSmsSum = new ArrayList<>();
            List<Map> contentLKSmsSum = new ArrayList<>();
            List<Map> titleBSSmsSum = new ArrayList<>();
            List<Map> contentBSSmsSum = new ArrayList<>();
            if (null != dataDto.getSms() && dataDto.getSms().size() > 0) {
                Map smsSum = dataDto.getSms().containsKey("summary") ? (Map) dataDto.getSms().get("summary") : null;
                Map groupSmsSum = dataDto.getSms().containsKey("group") ? (Map) dataDto.getSms().get("group") : null;
                Date fromDateSmsSum = null;
                Date toDateSmsSum = null;
                if (!"".equals((String) smsSum.get("from"))) {
                    fromDateSmsSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) smsSum.get("from"));
                }
                if (!"".equals((String) smsSum.get("view_to"))) {
                    toDateSmsSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) smsSum.get("view_to"));
                }
                StringBuilder timeSms = new StringBuilder("SMS: Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateSmsSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateSmsSum));
                listTimeHeaderSummary.add(timeSms.toString());
                listTimeHeaderSms.add(timeSms.toString());
                listSmsSum.add(smsSum);

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D1")) { // process level 2
                        Map titleTKSmsSumNode = (Map) groupSmsSum.get("D1");
                        Date fromDateTKSmsSumNode = null;
                        Date toDateTKSmsSumNode = null;
                        if (!"".equals((String) titleTKSmsSumNode.get("from"))) {
                            fromDateTKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKSmsSumNode.get("view_to"))) {
                            toDateTKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKSmsSumNode) + ")";
                        titleTKSmsSumNode.put("time", time);
                        titleTKSmsSum.add(titleTKSmsSumNode);
                        contentTKSmsSum = (List<Map>) titleTKSmsSumNode.get("list");
                    }
                }

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D2")) { // process level 2
                        Map titleLKSmsSumNode = (Map) groupSmsSum.get("D2");
                        Date fromDateLKSmsSumNode = null;
                        Date toDateLKSmsSumNode = null;
                        if (!"".equals((String) titleLKSmsSumNode.get("from"))) {
                            fromDateLKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKSmsSumNode.get("view_to"))) {
                            toDateLKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKSmsSumNode) + ")";
                        titleLKSmsSumNode.put("time", time);
                        titleLKSmsSum.add(titleLKSmsSumNode);
                        contentLKSmsSum = (List<Map>) titleLKSmsSumNode.get("list");
                    }
                }

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D3")) { // process level 2
                        Map titleBSSmsSumNode = (Map) groupSmsSum.get("D3");
                        Date fromDateBSSmsSumNode = null;
                        Date toDateBSSmsSumNode = null;
                        if (!"".equals((String) titleBSSmsSumNode.get("from"))) {
                            fromDateBSSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSSmsSumNode.get("view_to"))) {
                            toDateBSSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSSmsSumNode) + ")";
                        titleBSSmsSumNode.put("time", time);
                        titleBSSmsSum.add(titleBSSmsSumNode);
                        contentBSSmsSum = (List<Map>) titleBSSmsSumNode.get("list");
                    }
                }
            }

            // BILING
            List<Map> listBlSum = new ArrayList<>(); // process level 1
            List<Map> titleTKBlSum = new ArrayList<>();
            List<Map> contentTKBlSum = new ArrayList<>();
            List<Map> titleLKBlSum = new ArrayList<>();
            List<Map> contentLKBlSum = new ArrayList<>();
            List<Map> titleBSBlSum = new ArrayList<>();
            List<Map> contentBSBlSum = new ArrayList<>();
            List<Map> titleNKBlSum = new ArrayList<>();
            List<Map> contentNKBlSum = new ArrayList<>();
            if (null != dataDto.getBiling() && dataDto.getBiling().size() > 0) {
                Map blSum = (Map) dataDto.getBiling().get("summary");
                Map groupBlSum = (Map) dataDto.getBiling().get("group");
                Date fromDateBlSum = null;
                Date toDateBlSum = null;
                if (!"".equals((String) blSum.get("from"))) {
                    fromDateBlSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) blSum.get("from"));
                }
                if (!"".equals((String) blSum.get("view_to"))) {
                    toDateBlSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) blSum.get("view_to"));
                }
                StringBuilder timeBl = new StringBuilder("Biling : Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateBlSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateBlSum));
                listTimeHeaderSummary.add(timeBl.toString());
                listTimeHeaderBl.add(timeBl.toString());
                listBlSum.add(blSum);

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J1")) { // process level 2
                        Map titleTKBlSumNode = (Map) groupBlSum.get("J1");
                        Date fromDateTKBlSumNode = null;
                        Date toDateTKBlSumNode = null;
                        if (!"".equals((String) titleTKBlSumNode.get("from"))) {
                            fromDateTKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKBlSumNode.get("view_to"))) {
                            toDateTKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKBlSumNode) + ")";
                        titleTKBlSumNode.put("time", time);
                        titleTKBlSum.add(titleTKBlSumNode);
                        contentTKBlSum = (List<Map>) titleTKBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J2")) { // process level 2
                        Map titleLKBlSumNode = (Map) groupBlSum.get("J2");
                        Date fromDateLKBlSumNode = null;
                        Date toDateLKBlSumNode = null;
                        if (!"".equals((String) titleLKBlSumNode.get("from"))) {
                            fromDateLKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKBlSumNode.get("view_to"))) {
                            toDateLKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKBlSumNode) + ")";
                        titleLKBlSumNode.put("time", time);
                        titleLKBlSum.add(titleLKBlSumNode);
                        contentLKBlSum = (List<Map>) titleLKBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J3")) { // process level 2
                        Map titleBSBlSumNode = (Map) groupBlSum.get("J3");
                        Date fromDateBSBlSumNode = null;
                        Date toDateBSBlSumNode = null;
                        if (!"".equals((String) titleBSBlSumNode.get("from"))) {
                            fromDateBSBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSBlSumNode.get("view_to"))) {
                            toDateBSBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSBlSumNode) + ")";
                        titleBSBlSumNode.put("time", time);
                        titleBSBlSum.add(titleBSBlSumNode);
                        contentBSBlSum = (List<Map>) titleBSBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J4")) { // process level 2
                        Map titleNKBlSumNode = (Map) groupBlSum.get("J4");
                        Date fromDateNKBlSumNode = null;
                        Date toDateNKBlSumNode = null;
                        if (!"".equals((String) titleNKBlSumNode.get("from"))) {
                            fromDateNKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKBlSumNode.get("view_to"))) {
                            toDateNKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKBlSumNode) + ")";
                        titleNKBlSumNode.put("time", time);
                        titleNKBlSum.add(titleNKBlSumNode);
                        contentNKBlSum = (List<Map>) titleNKBlSumNode.get("list");
                    }
                }
            }

            // BNPL
            List<Map> listBnplSum = new ArrayList<>(); // process level 1
            List<Map> titleTKBnplSum = new ArrayList<>();
            List<Map> contentTKBnplSum = new ArrayList<>();
            List<Map> titleLKBnplSum = new ArrayList<>();
            List<Map> contentLKBnplSum = new ArrayList<>();
            List<Map> titleBSBnplSum = new ArrayList<>();
            List<Map> contentBSBnplSum = new ArrayList<>();
            List<Map> titleNKBnplSum = new ArrayList<>();
            List<Map> contentNKBnplSum = new ArrayList<>();
            if (null != dataDto.getBnpl() && dataDto.getBnpl().size() > 0) {
                Map bnplSum = (Map) dataDto.getBnpl().get("summary");
                Map groupBnplSum = (Map) dataDto.getBnpl().get("group");
                Date fromDateBnplSum = null;
                Date toDateBnplSum = null;
                if (!"".equals((String) bnplSum.get("from"))) {
                    fromDateBnplSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) bnplSum.get("from"));
                }
                if (!"".equals((String) bnplSum.get("view_to"))) {
                    toDateBnplSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) bnplSum.get("view_to"));
                }
                StringBuilder timeBnpl = new StringBuilder("BNPL : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateBnplSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateBnplSum));
                listTimeHeaderSummary.add(timeBnpl.toString());
                listTimeHeaderBnpl.add(timeBnpl.toString());
                listBnplSum.add(bnplSum);

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K1")) { // process level 2
                        Map titleTKBnplSumNode = (Map) groupBnplSum.get("K1");
                        Date fromDateTKBnplSumNode = null;
                        Date toDateTKBnplSumNode = null;
                        if (!"".equals((String) titleTKBnplSumNode.get("from"))) {
                            fromDateTKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKBnplSumNode.get("view_to"))) {
                            toDateTKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKBnplSumNode) + ")";
                        titleTKBnplSumNode.put("time", time);
                        titleTKBnplSum.add(titleTKBnplSumNode);
                        contentTKBnplSum = (List<Map>) titleTKBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K2")) { // process level 2
                        Map titleLKBnplSumNode = (Map) groupBnplSum.get("K2");
                        Date fromDateLKBnplSumNode = null;
                        Date toDateLKBnplSumNode = null;
                        if (!"".equals((String) titleLKBnplSumNode.get("from"))) {
                            fromDateLKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKBnplSumNode.get("view_to"))) {
                            toDateLKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKBnplSumNode) + ")";
                        titleLKBnplSumNode.put("time", time);
                        titleLKBnplSum.add(titleLKBnplSumNode);
                        contentLKBnplSum = (List<Map>) titleLKBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K3")) { // process level 2
                        Map titleBSBnplSumNode = (Map) groupBnplSum.get("K3");
                        Date fromDateBSBnplSumNode = null;
                        Date toDateBSBnplSumNode = null;
                        if (!"".equals((String) titleBSBnplSumNode.get("from"))) {
                            fromDateBSBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSBnplSumNode.get("view_to"))) {
                            toDateBSBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSBnplSumNode) + ")";
                        titleBSBnplSumNode.put("time", time);
                        titleBSBnplSum.add(titleBSBnplSumNode);
                        contentBSBnplSum = (List<Map>) titleBSBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K4")) { // process level 2
                        Map titleNKBnplSumNode = (Map) groupBnplSum.get("K4");
                        Date fromDateNKBnplSumNode = null;
                        Date toDateNKBnplSumNode = null;
                        if (!"".equals((String) titleNKBnplSumNode.get("from"))) {
                            fromDateNKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKBnplSumNode.get("view_to"))) {
                            toDateNKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKBnplSumNode) + ")";
                        titleNKBnplSumNode.put("time", time);
                        titleNKBnplSum.add(titleNKBnplSumNode);
                        contentNKBnplSum = (List<Map>) titleNKBnplSumNode.get("list");
                    }
                }
            }

            // UPOS
            List<Map> listUposSum = new ArrayList<>(); // process level 1
            List<Map> titleTKUposSum = new ArrayList<>();
            List<Map> contentTKUposSum = new ArrayList<>();
            List<Map> titleLKUposSum = new ArrayList<>();
            List<Map> contentLKUposSum = new ArrayList<>();
            List<Map> titleBSUposSum = new ArrayList<>();
            List<Map> contentBSUposSum = new ArrayList<>();
            List<Map> titleNKUposSum = new ArrayList<>();
            List<Map> contentNKUposSum = new ArrayList<>();
            if (null != dataDto.getUpos() && dataDto.getUpos().size() > 0) {
                Map uposSum = (Map) dataDto.getUpos().get("summary");
                Map groupUposSum = (Map) dataDto.getUpos().get("group");
                Date fromDateUposSum = null;
                Date toDateUposSum = null;
                if (!"".equals((String) uposSum.get("from"))) {
                    fromDateUposSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) uposSum.get("from"));
                }
                if (!"".equals((String) uposSum.get("view_to"))) {
                    toDateUposSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) uposSum.get("view_to"));
                }
                StringBuilder timeUpos = new StringBuilder("UPOS : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateUposSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateUposSum));
                listTimeHeaderSummary.add(timeUpos.toString());
                listTimeHeaderUpos.add(timeUpos.toString());
                listUposSum.add(uposSum);

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L1")) { // process level 2
                        Map titleTKUposSumNode = (Map) groupUposSum.get("L1");
                        Date fromDateTKUposSumNode = null;
                        Date toDateTKUposSumNode = null;
                        if (!"".equals((String) titleTKUposSumNode.get("from"))) {
                            fromDateTKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKUposSumNode.get("view_to"))) {
                            toDateTKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKUposSumNode) + ")";
                        titleTKUposSumNode.put("time", time);
                        titleTKUposSum.add(titleTKUposSumNode);
                        contentTKUposSum = (List<Map>) titleTKUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L2")) { // process level 2
                        Map titleLKUposSumNode = (Map) groupUposSum.get("L2");
                        Date fromDateLKUposSumNode = null;
                        Date toDateLKUposSumNode = null;
                        if (!"".equals((String) titleLKUposSumNode.get("from"))) {
                            fromDateLKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKUposSumNode.get("view_to"))) {
                            toDateLKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKUposSumNode) + ")";
                        titleLKUposSumNode.put("time", time);
                        titleLKUposSum.add(titleLKUposSumNode);
                        contentLKUposSum = (List<Map>) titleLKUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L3")) { // process level 2
                        Map titleBSUposSumNode = (Map) groupUposSum.get("L3");
                        Date fromDateBSUposSumNode = null;
                        Date toDateBSUposSumNode = null;
                        if (!"".equals((String) titleBSUposSumNode.get("from"))) {
                            fromDateBSUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSUposSumNode.get("view_to"))) {
                            toDateBSUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSUposSumNode) + ")";
                        titleBSUposSumNode.put("time", time);
                        titleBSUposSum.add(titleBSUposSumNode);
                        contentBSUposSum = (List<Map>) titleBSUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L4")) { // process level 2
                        Map titleNKUposSumNode = (Map) groupUposSum.get("L4");
                        Date fromDateNKUposSumNode = null;
                        Date toDateNKUposSumNode = null;
                        if (!"".equals((String) titleNKUposSumNode.get("from"))) {
                            fromDateNKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKUposSumNode.get("view_to"))) {
                            toDateNKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKUposSumNode) + ")";
                        titleNKUposSumNode.put("time", time);
                        titleNKUposSum.add(titleNKUposSumNode);
                        contentNKUposSum = (List<Map>) titleNKUposSumNode.get("list");
                    }
                }
            }

            // PAYCOLLECT
            List<Map> listPcSum = new ArrayList<>(); // process level 1
            List<Map> titleTKPcSum = new ArrayList<>();
            List<Map> contentTKPcSum = new ArrayList<>();
            List<Map> titleLKPcSum = new ArrayList<>();
            List<Map> contentLKPcSum = new ArrayList<>();
            List<Map> titleBSPcSum = new ArrayList<>();
            List<Map> contentBSPcSum = new ArrayList<>();
            List<Map> titleNKPcSum = new ArrayList<>();
            List<Map> contentNKPcSum = new ArrayList<>();
            if (null != dataDto.getPaycollect() && dataDto.getPaycollect().size() > 0) {
                Map pcSum = (Map) dataDto.getPaycollect().get("summary");
                Map groupPcSum = (Map) dataDto.getPaycollect().get("group");
                Date fromDatePcSum = null;
                Date toDatePcSum = null;
                if (!"".equals((String) pcSum.get("from"))) {
                    fromDatePcSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) pcSum.get("from"));
                }
                if (!"".equals((String) pcSum.get("view_to"))) {
                    toDatePcSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) pcSum.get("view_to"));
                }
                StringBuilder timePc = new StringBuilder("PC : Từ ").append(formatDateHHMMSSDDMMYYYY(fromDatePcSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDatePcSum));
                listTimeHeaderSummary.add(timePc.toString());
                listTimeHeaderPc.add(timePc.toString());
                listPcSum.add(pcSum);

                if (isValidMap(groupPcSum)) {
                    if (groupPcSum.containsKey("M1")) { // process level 2
                        Map titleTKPcSumNode = (Map) groupPcSum.get("M1");
                        Date fromDateTKPcSumNode = null;
                        Date toDateTKPcSumNode = null;
                        if (!"".equals((String) titleTKPcSumNode.get("from"))) {
                            fromDateTKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKPcSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKPcSumNode.get("view_to"))) {
                            toDateTKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKPcSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKPcSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKPcSumNode) + ")";
                        titleTKPcSumNode.put("time", time);
                        titleTKPcSum.add(titleTKPcSumNode);
                        contentTKPcSum = (List<Map>) titleTKPcSumNode.get("list");
                    }
                }

                if (isValidMap(groupPcSum)) {
                    if (groupPcSum.containsKey("M2")) { // process level 2
                        Map titleLKPcSumNode = (Map) groupPcSum.get("M2");
                        Date fromDateLKPcSumNode = null;
                        Date toDateLKPcSumNode = null;
                        if (!"".equals((String) titleLKPcSumNode.get("from"))) {
                            fromDateLKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKPcSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKPcSumNode.get("view_to"))) {
                            toDateLKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKPcSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKPcSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKPcSumNode) + ")";
                        titleLKPcSumNode.put("time", time);
                        titleLKPcSum.add(titleLKPcSumNode);
                        contentLKPcSum = (List<Map>) titleLKPcSumNode.get("list");
                    }
                }

                if (isValidMap(groupPcSum)) {
                    if (groupPcSum.containsKey("M3")) { // process level 2
                        Map titleBSPcSumNode = (Map) groupPcSum.get("M3");
                        Date fromDateBSPcSumNode = null;
                        Date toDateBSPcSumNode = null;
                        if (!"".equals((String) titleBSPcSumNode.get("from"))) {
                            fromDateBSPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSPcSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSPcSumNode.get("view_to"))) {
                            toDateBSPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSPcSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSPcSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSPcSumNode) + ")";
                        titleBSPcSumNode.put("time", time);
                        titleBSPcSum.add(titleBSPcSumNode);
                        contentBSPcSum = (List<Map>) titleBSPcSumNode.get("list");
                    }
                }

                if (isValidMap(groupPcSum)) {
                    if (groupPcSum.containsKey("M4")) { // process level 2
                        Map titleNKPcSumNode = (Map) groupPcSum.get("M4");
                        Date fromDateNKPcSumNode = null;
                        Date toDateNKPcSumNode = null;
                        if (!"".equals((String) titleNKPcSumNode.get("from"))) {
                            fromDateNKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKPcSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKPcSumNode.get("view_to"))) {
                            toDateNKPcSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKPcSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKPcSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKPcSumNode) + ")";
                        titleNKPcSumNode.put("time", time);
                        titleNKPcSum.add(titleNKPcSumNode);
                        contentNKPcSum = (List<Map>) titleNKPcSumNode.get("list");
                    }
                }
            }

            // VIETQR
            List<Map> listVietQrSum = new ArrayList<>(); // process level 1
            List<Map> titleTKVietQrSum = new ArrayList<>();
            List<Map> contentTKVietQrSum = new ArrayList<>();
            List<Map> titleLKVietQrSum = new ArrayList<>();
            List<Map> contentLKVietQrSum = new ArrayList<>();
            List<Map> titleBSVietQrSum = new ArrayList<>();
            List<Map> contentBSVietQrSum = new ArrayList<>();
            List<Map> titleNKVietQrSum = new ArrayList<>();
            List<Map> contentNKVietQrSum = new ArrayList<>();
            if (null != dataDto.getVietqr() && dataDto.getVietqr().size() > 0) {
                Map vietQrSum = (Map) dataDto.getVietqr().get("summary");
                Map groupVietQrSum = (Map) dataDto.getVietqr().get("group");
                Date fromDateVietQrSum = null;
                Date toDateVietQrSum = null;
                if (!"".equals((String) vietQrSum.get("from"))) {
                    fromDateVietQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) vietQrSum.get("from"));
                }
                if (!"".equals((String) vietQrSum.get("view_to"))) {
                    toDateVietQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) vietQrSum.get("view_to"));
                }
                StringBuilder timeVietQr = new StringBuilder("VIETQR : Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateVietQrSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateVietQrSum));
                listTimeHeaderSummary.add(timeVietQr.toString());
                listTimeHeaderVietQr.add(timeVietQr.toString());
                listVietQrSum.add(vietQrSum);

                if (isValidMap(groupVietQrSum)) {
                    if (groupVietQrSum.containsKey("N1")) { // process level 2
                        Map titleTKVietQrSumNode = (Map) groupVietQrSum.get("N1");
                        Date fromDateTKVietQrSumNode = null;
                        Date toDateTKVietQrSumNode = null;
                        if (!"".equals((String) titleTKVietQrSumNode.get("from"))) {
                            fromDateTKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKVietQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKVietQrSumNode.get("view_to"))) {
                            toDateTKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKVietQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKVietQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKVietQrSumNode) + ")";
                        titleTKVietQrSumNode.put("time", time);
                        titleTKVietQrSum.add(titleTKVietQrSumNode);
                        contentTKVietQrSum = (List<Map>) titleTKVietQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupVietQrSum)) {
                    if (groupVietQrSum.containsKey("N2")) { // process level 2
                        Map titleLKVietQrSumNode = (Map) groupVietQrSum.get("N2");
                        Date fromDateLKVietQrSumNode = null;
                        Date toDateLKVietQrSumNode = null;
                        if (!"".equals((String) titleLKVietQrSumNode.get("from"))) {
                            fromDateLKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKVietQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKVietQrSumNode.get("view_to"))) {
                            toDateLKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKVietQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKVietQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKVietQrSumNode) + ")";
                        titleLKVietQrSumNode.put("time", time);
                        titleLKVietQrSum.add(titleLKVietQrSumNode);
                        contentLKVietQrSum = (List<Map>) titleLKVietQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupVietQrSum)) {
                    if (groupVietQrSum.containsKey("N3")) { // process level 2
                        Map titleBSVietQrSumNode = (Map) groupVietQrSum.get("N3");
                        Date fromDateBSVietQrSumNode = null;
                        Date toDateBSVietQrSumNode = null;
                        if (!"".equals((String) titleBSVietQrSumNode.get("from"))) {
                            fromDateBSVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSVietQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSVietQrSumNode.get("view_to"))) {
                            toDateBSVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSVietQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSVietQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSVietQrSumNode) + ")";
                        titleBSVietQrSumNode.put("time", time);
                        titleBSVietQrSum.add(titleBSVietQrSumNode);
                        contentBSVietQrSum = (List<Map>) titleBSVietQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupVietQrSum)) {
                    if (groupVietQrSum.containsKey("N4")) { // process level 2
                        Map titleNKVietQrSumNode = (Map) groupVietQrSum.get("N4");
                        Date fromDateNKVietQrSumNode = null;
                        Date toDateNKVietQrSumNode = null;
                        if (!"".equals((String) titleNKVietQrSumNode.get("from"))) {
                            fromDateNKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKVietQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKVietQrSumNode.get("view_to"))) {
                            toDateNKVietQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKVietQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKVietQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKVietQrSumNode) + ")";
                        titleNKVietQrSumNode.put("time", time);
                        titleNKVietQrSum.add(titleNKVietQrSumNode);
                        contentNKVietQrSum = (List<Map>) titleNKVietQrSumNode.get("list");
                    }
                }
            }

            // DIRECT DEBIT
            List<Map> listDdSum = new ArrayList<>(); // process level 1
            List<Map> titleTKDdSum = new ArrayList<>();
            List<Map> contentTKDdSum = new ArrayList<>();
            List<Map> titleLKDdSum = new ArrayList<>();
            List<Map> contentLKDdSum = new ArrayList<>();
            List<Map> titleBSDdSum = new ArrayList<>();
            List<Map> contentBSDdSum = new ArrayList<>();
            List<Map> titleNKDdSum = new ArrayList<>();
            List<Map> contentNKDdSum = new ArrayList<>();
            if (null != dataDto.getDd() && dataDto.getDd().size() > 0) {
                Map ddSum = (Map) dataDto.getDd().get("summary");
                Map groupDdSum = (Map) dataDto.getDd().get("group");
                Date fromDateDdSum = null;
                Date toDateDdSum = null;
                if (!"".equals((String) ddSum.get("from"))) {
                    fromDateDdSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) ddSum.get("from"));
                }
                if (!"".equals((String) ddSum.get("view_to"))) {
                    toDateDdSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) ddSum.get("view_to"));
                }
                StringBuilder timeDd = new StringBuilder("DIRECT DEBIT : Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateDdSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateDdSum));
                listTimeHeaderSummary.add(timeDd.toString());
                listTimeHeaderDd.add(timeDd.toString());
                listDdSum.add(ddSum);

                if (isValidMap(groupDdSum)) {
                    if (groupDdSum.containsKey("O1")) { // process level 2
                        Map titleTKDdSumNode = (Map) groupDdSum.get("O1");
                        Date fromDateTKDdSumNode = null;
                        Date toDateTKDdSumNode = null;
                        if (!"".equals((String) titleTKDdSumNode.get("from"))) {
                            fromDateTKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKDdSumNode.get("view_to"))) {
                            toDateTKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKDdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKDdSumNode) + ")";
                        titleTKDdSumNode.put("time", time);
                        titleTKDdSum.add(titleTKDdSumNode);
                        contentTKDdSum = (List<Map>) titleTKDdSumNode.get("list");
                    }
                }

                if (isValidMap(groupDdSum)) {
                    if (groupDdSum.containsKey("O2")) { // process level 2
                        Map titleLKDdSumNode = (Map) groupDdSum.get("O2");
                        Date fromDateLKDdSumNode = null;
                        Date toDateLKDdSumNode = null;
                        if (!"".equals((String) titleLKDdSumNode.get("from"))) {
                            fromDateLKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKDdSumNode.get("view_to"))) {
                            toDateLKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKDdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKDdSumNode) + ")";
                        titleLKDdSumNode.put("time", time);
                        titleLKDdSum.add(titleLKDdSumNode);
                        contentLKDdSum = (List<Map>) titleLKDdSumNode.get("list");
                    }
                }

                if (isValidMap(groupDdSum)) {
                    if (groupDdSum.containsKey("O3")) { // process level 2
                        Map titleBSDdSumNode = (Map) groupDdSum.get("O3");
                        Date fromDateBSDdSumNode = null;
                        Date toDateBSDdSumNode = null;
                        if (!"".equals((String) titleBSDdSumNode.get("from"))) {
                            fromDateBSDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSDdSumNode.get("view_to"))) {
                            toDateBSDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSDdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSDdSumNode) + ")";
                        titleBSDdSumNode.put("time", time);
                        titleBSDdSum.add(titleBSDdSumNode);
                        contentBSDdSum = (List<Map>) titleBSDdSumNode.get("list");
                    }
                }

                if (isValidMap(groupDdSum)) {
                    if (groupDdSum.containsKey("O4")) { // process level 2
                        Map titleNKDdSumNode = (Map) groupDdSum.get("O4");
                        Date fromDateNKDdSumNode = null;
                        Date toDateNKDdSumNode = null;
                        if (!"".equals((String) titleNKDdSumNode.get("from"))) {
                            fromDateNKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKDdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKDdSumNode.get("view_to"))) {
                            toDateNKDdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKDdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKDdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKDdSumNode) + ")";
                        titleNKDdSumNode.put("time", time);
                        titleNKDdSum.add(titleNKDdSumNode);
                        contentNKDdSum = (List<Map>) titleNKDdSumNode.get("list");
                    }
                }
            }

            // Balance - Don vi du co
            List<Map> titleDvdcSum = new ArrayList<>(); // process level 1
            List<Map> contentDvdcSum = new ArrayList<>();
            if (null != dataDto.getBalance() && dataDto.getBalance().size() > 0) {
                Map dvdcSum = (Map) dataDto.getBalance().get("summary");
                Map groupDvdcSum = (Map) dataDto.getBalance().get("group");
                // StringBuilder timeDvdc = new StringBuilder("Từ ").append(new
                // SimpleDateFormat("HH:mm:ss
                // MM/dd/yyyy").parse((String) dvdcSum.get("from"))).append(" Đến
                // ").append(formatDateHHMMSSDDMMYYYY(new SimpleDateFormat("HH:mm:ss
                // MM/dd/yyyy").parse((String)
                // dvdcSum.get("to"))));
                // detailMap.put("time_dvdc", timeDvdc);
                titleDvdcSum.add(dvdcSum);
                if (isValidMap(groupDvdcSum)) {
                    for (Object keyName : groupDvdcSum.keySet()) {
                        Map contentDvdcSumNode = (Map) groupDvdcSum.get(keyName.toString());
                        contentDvdcSum.add(contentDvdcSumNode);
                    }
                }
            }

            // Monthly fee - Phi duy tri hang thang
            List<Map> listPdttSum = new ArrayList<>();
            List<Map> titlePdttSum = new ArrayList<>();
            if (null != dataDto.getMonthlyFee() && dataDto.getMonthlyFee().containsKey("summary")) {
                Map pdttSum = (Map) dataDto.getMonthlyFee().get("summary");
                titlePdttSum.add(pdttSum);
            }
            List<Map> groupPdttSum = null != dataDto.getMonthlyFee() ? (List<Map>) dataDto.getMonthlyFee().get("group")
                    : null;
            if (groupPdttSum != null) {
                listPdttSum.addAll(groupPdttSum);
            }

            // Est Advance - Tam ung tam tinh
            List<Map> listTuttSum = new ArrayList<>();
            if (null != dataDto.getEstAdvance() && dataDto.getEstAdvance().containsKey("summary")) {
                Map tuttSum = (Map) dataDto.getEstAdvance().get("summary");
                listTuttSum.add(tuttSum);
            }

            // Auto hold - Khoanh tu dong
            List<Map> listKtdSum = new ArrayList<>(); // process level 1
            List<Map> titleRefundKtdSum = new ArrayList<>();
            List<Map> contentRefundKtdSum = new ArrayList<>();
            List<Map> titlePayoutKtdSum = new ArrayList<>();
            List<Map> contentPayoutKtdSum = new ArrayList<>();
            if (null != dataDto.getAutoHold() && dataDto.getAutoHold().size() > 0) {
                Map ktdSum = (Map) dataDto.getAutoHold().get("summary");
                Map groupKtdSum = (Map) dataDto.getAutoHold().get("group");
                listKtdSum.add(ktdSum);

                if (isValidMap(groupKtdSum)) {
                    if (groupKtdSum.containsKey("H1")) { // process level 2
                        Map titleRefundKtdSumNode = (Map) groupKtdSum.get("H1");
                        Date fromDateRefundKtdSumNode = null;
                        Date toDateRefundKtdSumNode = null;
                        if (!"".equals((String) titleRefundKtdSumNode.get("from"))) {
                            fromDateRefundKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleRefundKtdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleRefundKtdSumNode.get("view_to"))) {
                            toDateRefundKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleRefundKtdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateRefundKtdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateRefundKtdSumNode) + ")";
                        titleRefundKtdSumNode.put("time", time);
                        titleRefundKtdSum.add(titleRefundKtdSumNode);
                        contentRefundKtdSum = (List<Map>) titleRefundKtdSumNode.get("list");
                    }
                }

                if (isValidMap(groupKtdSum)) {
                    if (groupKtdSum.containsKey("H2")) { // process level 2
                        Map titlePayoutKtdSumNode = (Map) groupKtdSum.get("H2");
                        Date fromDatePayoutKtdSumNode = null;
                        Date toDatePayoutKtdSumNode = null;
                        if (!"".equals((String) titlePayoutKtdSumNode.get("from"))) {
                            fromDatePayoutKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titlePayoutKtdSumNode.get("from"));
                        }
                        if (!"".equals((String) titlePayoutKtdSumNode.get("view_to"))) {
                            toDatePayoutKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titlePayoutKtdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDatePayoutKtdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDatePayoutKtdSumNode) + ")";
                        titlePayoutKtdSumNode.put("time", time);
                        titlePayoutKtdSum.add(titlePayoutKtdSumNode);
                        contentPayoutKtdSum = (List<Map>) titlePayoutKtdSumNode.get("list");
                    }
                }
            }

            // Manual adjust - Dieu chinh thu cong
            List<Map> titleDctcSum = new ArrayList<>();
            if (null != dataDto.getManualAdjust() && dataDto.getManualAdjust().containsKey("summary")) {
                Map dctcSum = (Map) dataDto.getManualAdjust().get("summary");
                titleDctcSum.add(dctcSum);
            }

            Map groupDctcSum = null != dataDto.getManualAdjust() ? (Map) dataDto.getManualAdjust().get("group") : null;

            List<Map> contentDctcSum = new ArrayList<>();
            if (isValidMap(groupDctcSum)) {
                for (Object keyName : groupDctcSum.keySet()) {
                    Map contentDctcSumNode = (Map) groupDctcSum.get(keyName.toString());
                    contentDctcSum.add(contentDctcSumNode);
                }
            }

            // Total last - Tong cuoi
            List<Map> totalSum = new ArrayList<>();
            if (null != dataDto.getLastTotal() && dataDto.getLastTotal().size() > 0) {
                Map totalSumNode = (Map) dataDto.getLastTotal();
                totalSum.add(totalSumNode);
            }

            String template = "";
            switch (type) {
                case "ALL": {
                    template = TemplateUtils.TEMPLATE_SUMMARY_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "QT": {
                    template = TemplateUtils.TEMPLATE_QT_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "ND": {
                    template = TemplateUtils.TEMPLATE_ND_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "QR": {
                    template = TemplateUtils.TEMPLATE_VI_APP_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "SMS": {
                    template = TemplateUtils.TEMPLATE_SMS_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BL": {
                    template = TemplateUtils.TEMPLATE_BILING_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BNPL": {
                    template = TemplateUtils.TEMPLATE_BNPL_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "UPOS": {
                    template = TemplateUtils.TEMPLATE_UPOS_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "PC": {
                    template = TemplateUtils.TEMPLATE_PC_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "VIETQR": {
                    template = TemplateUtils.TEMPLATE_VIETQR_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "DD": {
                    template = TemplateUtils.TEMPLATE_DD_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BALANCE": {
                    template = TemplateUtils.TEMPLATE_DVDC_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
            }
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listTimeHeaderSummary", listTimeHeaderSummary)
                    // International
                    .setBeanValue("listTimeHeaderInt", listTimeHeaderInt)
                    .setBeanValue("listIntSum", listIntSum)
                    .setBeanValue("titleTKIntSum", titleTKIntSum)
                    .setBeanValue("contentTKIntSum", contentTKIntSum)
                    .setBeanValue("titleLKIntSum", titleLKIntSum)
                    .setBeanValue("contentLKIntSum", contentLKIntSum)
                    .setBeanValue("titleBSIntSum", titleBSIntSum)
                    .setBeanValue("contentBSIntSum", contentBSIntSum)
                    .setBeanValue("titleNKIntSum", titleNKIntSum)
                    .setBeanValue("contentNKIntSum", contentNKIntSum)
                    // Domestic
                    .setBeanValue("listTimeHeaderDom", listTimeHeaderDom)
                    .setBeanValue("listDomSum", listDomSum)
                    .setBeanValue("titleTKDomSum", titleTKDomSum)
                    .setBeanValue("contentTKDomSum", contentTKDomSum)
                    .setBeanValue("titleLKDomSum", titleLKDomSum)
                    .setBeanValue("contentLKDomSum", contentLKDomSum)
                    .setBeanValue("titleBSDomSum", titleBSDomSum)
                    .setBeanValue("contentBSDomSum", contentBSDomSum)
                    // Qr
                    .setBeanValue("listTimeHeaderQr", listTimeHeaderQr)
                    .setBeanValue("listQrSum", listQrSum)
                    .setBeanValue("titleTKQrSum", titleTKQrSum)
                    .setBeanValue("contentTKQrSum", contentTKQrSum)
                    .setBeanValue("titleLKQrSum", titleLKQrSum)
                    .setBeanValue("contentLKQrSum", contentLKQrSum)
                    .setBeanValue("titleBSQrSum", titleBSQrSum)
                    .setBeanValue("contentBSQrSum", contentBSQrSum)
                    // Sms
                    .setBeanValue("listTimeHeaderSms", listTimeHeaderSms)
                    .setBeanValue("listSmsSum", listSmsSum)
                    .setBeanValue("titleTKSmsSum", titleTKSmsSum)
                    .setBeanValue("contentTKSmsSum", contentTKSmsSum)
                    .setBeanValue("titleLKSmsSum", titleLKSmsSum)
                    .setBeanValue("contentLKSmsSum", contentLKSmsSum)
                    .setBeanValue("titleBSSmsSum", titleBSSmsSum)
                    .setBeanValue("contentBSSmsSum", contentBSSmsSum)
                    // Biling
                    .setBeanValue("listTimeHeaderBl", listTimeHeaderBl)
                    .setBeanValue("listBlSum", listBlSum)
                    .setBeanValue("titleTKBlSum", titleTKBlSum)
                    .setBeanValue("contentTKBlSum", contentTKBlSum)
                    .setBeanValue("titleLKBlSum", titleLKBlSum)
                    .setBeanValue("contentLKBlSum", contentLKBlSum)
                    .setBeanValue("titleBSBlSum", titleBSBlSum)
                    .setBeanValue("contentBSBlSum", contentBSBlSum)
                    .setBeanValue("titleNKBlSum", titleNKBlSum)
                    .setBeanValue("contentNKBlSum", contentNKBlSum)
                    // Bnpl
                    .setBeanValue("listTimeHeaderBnpl", listTimeHeaderBnpl)
                    .setBeanValue("listBnplSum", listBnplSum)
                    .setBeanValue("titleTKBnplSum", titleTKBnplSum)
                    .setBeanValue("contentTKBnplSum", contentTKBnplSum)
                    .setBeanValue("titleLKBnplSum", titleLKBnplSum)
                    .setBeanValue("contentLKBnplSum", contentLKBnplSum)
                    .setBeanValue("titleBSBnplSum", titleBSBnplSum)
                    .setBeanValue("contentBSBnplSum", contentBSBnplSum)
                    .setBeanValue("titleNKBnplSum", titleNKBnplSum)
                    .setBeanValue("contentNKBnplSum", contentNKBnplSum)
                    // Upos
                    .setBeanValue("listTimeHeaderUpos", listTimeHeaderUpos)
                    .setBeanValue("listUposSum", listUposSum)
                    .setBeanValue("titleTKUposSum", titleTKUposSum)
                    .setBeanValue("contentTKUposSum", contentTKUposSum)
                    .setBeanValue("titleLKUposSum", titleLKUposSum)
                    .setBeanValue("contentLKUposSum", contentLKUposSum)
                    .setBeanValue("titleBSUposSum", titleBSUposSum)
                    .setBeanValue("contentBSUposSum", contentBSUposSum)
                    .setBeanValue("titleNKUposSum", titleNKUposSum)
                    .setBeanValue("contentNKUposSum", contentNKUposSum)
                    // Paycollect
                    .setBeanValue("listTimeHeaderPc", listTimeHeaderPc)
                    .setBeanValue("listPcSum", listPcSum)
                    .setBeanValue("titleTKPcSum", titleTKPcSum)
                    .setBeanValue("contentTKPcSum", contentTKPcSum)
                    .setBeanValue("titleLKPcSum", titleLKPcSum)
                    .setBeanValue("contentLKPcSum", contentLKPcSum)
                    .setBeanValue("titleBSPcSum", titleBSPcSum)
                    .setBeanValue("contentBSPcSum", contentBSPcSum)
                    .setBeanValue("titleNKPcSum", titleNKPcSum)
                    .setBeanValue("contentNKPcSum", contentNKPcSum)
                    // Vietqr
                    .setBeanValue("listTimeHeaderVietQr", listTimeHeaderVietQr)
                    .setBeanValue("listVietQrSum", listVietQrSum)
                    .setBeanValue("titleTKVietQrSum", titleTKVietQrSum)
                    .setBeanValue("contentTKVietQrSum", contentTKVietQrSum)
                    .setBeanValue("titleLKVietQrSum", titleLKVietQrSum)
                    .setBeanValue("contentLKVietQrSum", contentLKVietQrSum)
                    .setBeanValue("titleBSVietQrSum", titleBSVietQrSum)
                    .setBeanValue("contentBSVietQrSum", contentBSVietQrSum)
                    .setBeanValue("titleNKVietQrSum", titleNKVietQrSum)
                    .setBeanValue("contentNKVietQrSum", contentNKVietQrSum)
                    // Direct debit
                    .setBeanValue("listTimeHeaderDd", listTimeHeaderDd)
                    .setBeanValue("listDdSum", listDdSum)
                    .setBeanValue("titleTKDdSum", titleTKDdSum)
                    .setBeanValue("contentTKDdSum", contentTKDdSum)
                    .setBeanValue("titleLKDdSum", titleLKDdSum)
                    .setBeanValue("contentLKDdSum", contentLKDdSum)
                    .setBeanValue("titleBSDdSum", titleBSDdSum)
                    .setBeanValue("contentBSDdSum", contentBSDdSum)
                    .setBeanValue("titleNKDdSum", titleNKDdSum)
                    .setBeanValue("contentNKDdSum", contentNKDdSum)
                    // Balance
                    .setBeanValue("titleDvdcSum", titleDvdcSum)
                    .setBeanValue("contentDvdcSum", contentDvdcSum)
                    // Monthly fee
                    .setBeanValue("listPdttSum", listPdttSum)
                    .setBeanValue("titlePdttSum", titlePdttSum)
                    // Est advance
                    .setBeanValue("listTuttSum", listTuttSum)
                    // Auto hold
                    .setBeanValue("listKtdSum", listKtdSum)
                    .setBeanValue("titleRefundKtdSum", titleRefundKtdSum)
                    .setBeanValue("contentRefundKtdSum", contentRefundKtdSum)
                    .setBeanValue("titlePayoutKtdSum", titlePayoutKtdSum)
                    .setBeanValue("contentPayoutKtdSum", contentPayoutKtdSum)
                    // Manual adjust
                    .setBeanValue("titleDctcSum", titleDctcSum)
                    .setBeanValue("contentDctcSum", contentDctcSum)
                    // Total last
                    .setBeanValue("totalSum", totalSum)
                    .build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate prefix advance detail file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate prefix advance detail File successfully: ");
    }

    public static void generatePreAdvanceBigMerchantDetail(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "----------------START GENERATE PREFIX ADVANCE DETAIL FILE--------------------");
            Map<String, Object> mIn = messageData.getRequestBody();
            String type = (String) mIn.get("type");
            List<Map> listMap = new PreAdvanceBigMerchantDetailGenerator().generate(mIn);
            if (listMap.isEmpty()) {
                LOGGER.log(Level.SEVERE, "listMap is empty");
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
            PrefixAdvanceDto dataDto = (PrefixAdvanceDto) listMap.get(0).get("data");
            Map<String, Object> detailMap = new HashMap<>();
            DateFormat dfPreAdvance = new SimpleDateFormat("dd/MM/yyyy");

            // process header
            detailMap.put("partnerName", dataDto.getPartnerName());
            detailMap.put("contractId", dataDto.getContractCode());
            detailMap.put("taxCode", dataDto.getTaxCode());
            detailMap.put("twLastTotal",
                    Double.parseDouble(dataDto.getTwLastTotal() == null ? "0" : dataDto.getTwLastTotal()));
            detailMap.put("advanceId", dataDto.getAdvanceId());
            detailMap.put("advType", dataDto.getAdvType());
            detailMap.put("createDate", dfPreAdvance.format(new Date(dataDto.getCreateDate().getTime())));
            detailMap.put("advanceConfigCode", dataDto.getAdvanceConfigCode());
            detailMap.put("transferBank", dataDto.getTransferBank());
            detailMap.put("transferDate",
                    null != dataDto.getTransferDate()
                            ? dfPreAdvance.format(new Date(dataDto.getTransferDate().getTime()))
                            : "");
            detailMap.put("accountType", dataDto.getAccountType());
            detailMap.put("status", dataDto.getStatus());
            detailMap.put("receiveBank", dataDto.getReceiveBank());
            detailMap.put("accountNumber", dataDto.getAccountNumber());
            detailMap.put("transferContent", dataDto.getTransferContent());

            List<String> listTimeHeaderSummary = new ArrayList<>();
            List<String> listTimeHeaderInt = new ArrayList<>();
            List<String> listTimeHeaderDom = new ArrayList<>();
            List<String> listTimeHeaderQr = new ArrayList<>();
            List<String> listTimeHeaderSms = new ArrayList<>();
            List<String> listTimeHeaderBl = new ArrayList<>();
            List<String> listTimeHeaderBnpl = new ArrayList<>();
            List<String> listTimeHeaderUpos = new ArrayList<>();

            // International
            List<Map> listIntSum = new ArrayList<>(); // process level 1
            List<Map> titleTKIntSum = new ArrayList<>();
            List<Map> contentTKIntSum = new ArrayList<>();
            List<Map> titleLKIntSum = new ArrayList<>();
            List<Map> contentLKIntSum = new ArrayList<>();
            List<Map> titleBSIntSum = new ArrayList<>();
            List<Map> contentBSIntSum = new ArrayList<>();
            List<Map> titleNKIntSum = new ArrayList<>();
            List<Map> contentNKIntSum = new ArrayList<>();
            if (null != dataDto.getInternational() && dataDto.getInternational().size() > 0) {
                Map intSum = (Map) dataDto.getInternational().get("summary");
                Map groupIntSum = (Map) dataDto.getInternational().get("group");
                Date fromDateIntSum = null;
                Date toDateIntSum = null;
                if (!"".equals((String) intSum.get("from"))) {
                    fromDateIntSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) intSum.get("from"));
                }
                if (!"".equals((String) intSum.get("view_to"))) {
                    toDateIntSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) intSum.get("view_to"));
                }
                StringBuilder timeQt = new StringBuilder("Quốc tế : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateIntSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateIntSum));
                listTimeHeaderSummary.add(timeQt.toString());
                listTimeHeaderInt.add(timeQt.toString());
                listIntSum.add(intSum);

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A1")) { // process level 2
                        Map titleTKIntSumNode = (Map) groupIntSum.get("A1");
                        Date fromDateTKIntSumNode = null;
                        Date toDateTKIntSumNode = null;
                        if (!"".equals((String) titleTKIntSumNode.get("from"))) {
                            fromDateTKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKIntSumNode.get("view_to"))) {
                            toDateTKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKIntSumNode) + ")";
                        titleTKIntSumNode.put("time", time);
                        titleTKIntSum.add(titleTKIntSumNode);
                        contentTKIntSum = (List<Map>) titleTKIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A2")) { // process level 2
                        Map titleLKIntSumNode = (Map) groupIntSum.get("A2");
                        Date fromDateLKIntSumNode = null;
                        Date toDateLKIntSumNode = null;
                        if (!"".equals((String) titleLKIntSumNode.get("from"))) {
                            fromDateLKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKIntSumNode.get("view_to"))) {
                            toDateLKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKIntSumNode) + ")";
                        titleLKIntSumNode.put("time", time);
                        titleLKIntSum.add(titleLKIntSumNode);
                        contentLKIntSum = (List<Map>) titleLKIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A3")) { // process level 2
                        Map titleBSIntSumNode = (Map) groupIntSum.get("A3");
                        Date fromDateBSIntSumNode = null;
                        Date toDateBSIntSumNode = null;
                        if (!"".equals((String) titleBSIntSumNode.get("from"))) {
                            fromDateBSIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSIntSumNode.get("view_to"))) {
                            toDateBSIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSIntSumNode) + ")";
                        titleBSIntSumNode.put("time", time);
                        titleBSIntSum.add(titleBSIntSumNode);
                        contentBSIntSum = (List<Map>) titleBSIntSumNode.get("list");
                    }
                }

                if (isValidMap(groupIntSum)) {
                    if (groupIntSum.containsKey("A4")) { // process level 2
                        Map titleNKIntSumNode = (Map) groupIntSum.get("A4");
                        Date fromDateNKIntSumNode = null;
                        Date toDateNKIntSumNode = null;
                        if (!"".equals((String) titleNKIntSumNode.get("from"))) {
                            fromDateNKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKIntSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKIntSumNode.get("view_to"))) {
                            toDateNKIntSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKIntSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKIntSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKIntSumNode) + ")";
                        titleNKIntSumNode.put("time", time);
                        titleNKIntSum.add(titleNKIntSumNode);
                        contentNKIntSum = (List<Map>) titleNKIntSumNode.get("list");
                    }
                }
            }

            // Domestic
            List<Map> listDomSum = new ArrayList<>(); // process level 1
            List<Map> titleTKDomSum = new ArrayList<>();
            List<Map> contentTKDomSum = new ArrayList<>();
            List<Map> titleLKDomSum = new ArrayList<>();
            List<Map> contentLKDomSum = new ArrayList<>();
            List<Map> titleBSDomSum = new ArrayList<>();
            List<Map> contentBSDomSum = new ArrayList<>();
            if (null != dataDto.getDomestic() && dataDto.getDomestic().size() > 0) {
                Map domSum = (Map) dataDto.getDomestic().get("summary");
                Map groupDomSum = (Map) dataDto.getDomestic().get("group");
                Date fromDateDomSum = null;
                Date toDateDomSum = null;
                if (!"".equals((String) domSum.get("from"))) {
                    fromDateDomSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) domSum.get("from"));
                }
                if (!"".equals((String) domSum.get("view_to"))) {
                    toDateDomSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) domSum.get("view_to"));
                }
                StringBuilder timeNd = new StringBuilder("Nội địa: Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateDomSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateDomSum));
                listTimeHeaderSummary.add(timeNd.toString());
                listTimeHeaderDom.add(timeNd.toString());
                listDomSum.add(domSum);

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B1")) { // process level 2
                        Map titleTKDomSumNode = (Map) groupDomSum.get("B1");
                        Date fromDateTKDomSumNode = null;
                        Date toDateTKDomSumNode = null;
                        if (!"".equals((String) titleTKDomSumNode.get("from"))) {
                            fromDateTKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKDomSumNode.get("view_to"))) {
                            toDateTKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKDomSumNode) + ")";
                        titleTKDomSumNode.put("time", time);
                        titleTKDomSum.add(titleTKDomSumNode);
                        contentTKDomSum = (List<Map>) titleTKDomSumNode.get("list");
                    }
                }

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B2")) { // process level 2
                        Map titleLKDomSumNode = (Map) groupDomSum.get("B2");
                        Date fromDateLKDomSumNode = null;
                        Date toDateLKDomSumNode = null;
                        if (!"".equals((String) titleLKDomSumNode.get("from"))) {
                            fromDateLKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKDomSumNode.get("view_to"))) {
                            toDateLKDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKDomSumNode) + ")";
                        titleLKDomSumNode.put("time", time);
                        titleLKDomSum.add(titleLKDomSumNode);
                        contentLKDomSum = (List<Map>) titleLKDomSumNode.get("list");
                    }
                }

                if (isValidMap(groupDomSum)) {
                    if (groupDomSum.containsKey("B3")) { // process level 2
                        Map titleBSDomSumNode = (Map) groupDomSum.get("B3");
                        Date fromDateBSDomSumNode = null;
                        Date toDateBSDomSumNode = null;
                        if (!"".equals((String) titleBSDomSumNode.get("from"))) {
                            fromDateBSDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDomSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSDomSumNode.get("view_to"))) {
                            toDateBSDomSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSDomSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSDomSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSDomSumNode) + ")";
                        titleBSDomSumNode.put("time", time);
                        titleBSDomSum.add(titleBSDomSumNode);
                        contentBSDomSum = (List<Map>) titleBSDomSumNode.get("list");
                    }
                }
            }

            // QR
            List<Map> listQrSum = new ArrayList<>(); // process level 1
            List<Map> titleTKQrSum = new ArrayList<>();
            List<Map> contentTKQrSum = new ArrayList<>();
            List<Map> titleLKQrSum = new ArrayList<>();
            List<Map> contentLKQrSum = new ArrayList<>();
            List<Map> titleBSQrSum = new ArrayList<>();
            List<Map> contentBSQrSum = new ArrayList<>();
            if (null != dataDto.getApp() && dataDto.getApp().size() > 0) {
                Map qrSum = (Map) dataDto.getApp().get("summary");
                Map groupQrSum = (Map) dataDto.getApp().get("group");
                Date fromDateQrSum = null;
                Date toDateQrSum = null;
                if (!"".equals((String) qrSum.get("from"))) {
                    fromDateQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) qrSum.get("from"));
                }
                if (!"".equals((String) qrSum.get("view_to"))) {
                    toDateQrSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) qrSum.get("view_to"));
                }
                StringBuilder timeQR = new StringBuilder("Ví,App: Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateQrSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateQrSum));
                listTimeHeaderSummary.add(timeQR.toString());
                listTimeHeaderQr.add(timeQR.toString());
                listQrSum.add(qrSum);

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C1")) { // process level 2
                        Map titleTKQrSumNode = (Map) groupQrSum.get("C1");
                        Date fromDateTKQrSumNode = null;
                        Date toDateTKQrSumNode = null;
                        if (!"".equals((String) titleTKQrSumNode.get("from"))) {
                            fromDateTKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKQrSumNode.get("view_to"))) {
                            toDateTKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKQrSumNode) + ")";
                        titleTKQrSumNode.put("time", time);
                        titleTKQrSum.add(titleTKQrSumNode);
                        contentTKQrSum = (List<Map>) titleTKQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C2")) { // process level 2
                        Map titleLKQrSumNode = (Map) groupQrSum.get("C2");
                        Date fromDateLKQrSumNode = null;
                        Date toDateLKQrSumNode = null;
                        if (!"".equals((String) titleLKQrSumNode.get("from"))) {
                            fromDateLKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKQrSumNode.get("view_to"))) {
                            toDateLKQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKQrSumNode) + ")";
                        titleLKQrSumNode.put("time", time);
                        titleLKQrSum.add(titleLKQrSumNode);
                        contentLKQrSum = (List<Map>) titleLKQrSumNode.get("list");
                    }
                }

                if (isValidMap(groupQrSum)) {
                    if (groupQrSum.containsKey("C3")) { // process level 2
                        Map titleBSQrSumNode = (Map) groupQrSum.get("C3");
                        Date fromDateBSQrSumNode = null;
                        Date toDateBSQrSumNode = null;
                        if (!"".equals((String) titleBSQrSumNode.get("from"))) {
                            fromDateBSQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSQrSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSQrSumNode.get("view_to"))) {
                            toDateBSQrSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSQrSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSQrSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSQrSumNode) + ")";
                        titleBSQrSumNode.put("time", time);
                        titleBSQrSum.add(titleBSQrSumNode);
                        contentBSQrSum = (List<Map>) titleBSQrSumNode.get("list");
                    }
                }
            }

            // Sms
            List<Map> listSmsSum = new ArrayList<>(); // process level 1
            List<Map> titleTKSmsSum = new ArrayList<>();
            List<Map> contentTKSmsSum = new ArrayList<>();
            List<Map> titleLKSmsSum = new ArrayList<>();
            List<Map> contentLKSmsSum = new ArrayList<>();
            List<Map> titleBSSmsSum = new ArrayList<>();
            List<Map> contentBSSmsSum = new ArrayList<>();
            if (null != dataDto.getSms() && dataDto.getSms().size() > 0) {
                Map smsSum = dataDto.getSms().containsKey("summary") ? (Map) dataDto.getSms().get("summary") : null;
                Map groupSmsSum = dataDto.getSms().containsKey("group") ? (Map) dataDto.getSms().get("group") : null;
                Date fromDateSmsSum = null;
                Date toDateSmsSum = null;
                if (!"".equals((String) smsSum.get("from"))) {
                    fromDateSmsSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) smsSum.get("from"));
                }
                if (!"".equals((String) smsSum.get("view_to"))) {
                    toDateSmsSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) smsSum.get("view_to"));
                }
                StringBuilder timeSms = new StringBuilder("SMS: Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateSmsSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateSmsSum));
                listTimeHeaderSummary.add(timeSms.toString());
                listTimeHeaderSms.add(timeSms.toString());
                listSmsSum.add(smsSum);

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D1")) { // process level 2
                        Map titleTKSmsSumNode = (Map) groupSmsSum.get("D1");
                        Date fromDateTKSmsSumNode = null;
                        Date toDateTKSmsSumNode = null;
                        if (!"".equals((String) titleTKSmsSumNode.get("from"))) {
                            fromDateTKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKSmsSumNode.get("view_to"))) {
                            toDateTKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKSmsSumNode) + ")";
                        titleTKSmsSumNode.put("time", time);
                        titleTKSmsSum.add(titleTKSmsSumNode);
                        contentTKSmsSum = (List<Map>) titleTKSmsSumNode.get("list");
                    }
                }

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D2")) { // process level 2
                        Map titleLKSmsSumNode = (Map) groupSmsSum.get("D2");
                        Date fromDateLKSmsSumNode = null;
                        Date toDateLKSmsSumNode = null;
                        if (!"".equals((String) titleLKSmsSumNode.get("from"))) {
                            fromDateLKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKSmsSumNode.get("view_to"))) {
                            toDateLKSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKSmsSumNode) + ")";
                        titleLKSmsSumNode.put("time", time);
                        titleLKSmsSum.add(titleLKSmsSumNode);
                        contentLKSmsSum = (List<Map>) titleLKSmsSumNode.get("list");
                    }
                }

                if (isValidMap(groupSmsSum)) {
                    if (groupSmsSum.containsKey("D3")) { // process level 2
                        Map titleBSSmsSumNode = (Map) groupSmsSum.get("D3");
                        Date fromDateBSSmsSumNode = null;
                        Date toDateBSSmsSumNode = null;
                        if (!"".equals((String) titleBSSmsSumNode.get("from"))) {
                            fromDateBSSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSSmsSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSSmsSumNode.get("view_to"))) {
                            toDateBSSmsSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSSmsSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSSmsSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSSmsSumNode) + ")";
                        titleBSSmsSumNode.put("time", time);
                        titleBSSmsSum.add(titleBSSmsSumNode);
                        contentBSSmsSum = (List<Map>) titleBSSmsSumNode.get("list");
                    }
                }
            }

            // BILING
            List<Map> listBlSum = new ArrayList<>(); // process level 1
            List<Map> titleTKBlSum = new ArrayList<>();
            List<Map> contentTKBlSum = new ArrayList<>();
            List<Map> titleLKBlSum = new ArrayList<>();
            List<Map> contentLKBlSum = new ArrayList<>();
            List<Map> titleBSBlSum = new ArrayList<>();
            List<Map> contentBSBlSum = new ArrayList<>();
            List<Map> titleNKBlSum = new ArrayList<>();
            List<Map> contentNKBlSum = new ArrayList<>();
            if (null != dataDto.getBiling() && dataDto.getBiling().size() > 0) {
                Map blSum = (Map) dataDto.getBiling().get("summary");
                Map groupBlSum = (Map) dataDto.getBiling().get("group");
                Date fromDateBlSum = null;
                Date toDateBlSum = null;
                if (!"".equals((String) blSum.get("from"))) {
                    fromDateBlSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) blSum.get("from"));
                }
                if (!"".equals((String) blSum.get("view_to"))) {
                    toDateBlSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) blSum.get("view_to"));
                }
                StringBuilder timeBl = new StringBuilder("Biling : Từ ").append(formatDateHHMMSSDDMMYYYY(fromDateBlSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateBlSum));
                listTimeHeaderSummary.add(timeBl.toString());
                listTimeHeaderBl.add(timeBl.toString());
                listBlSum.add(blSum);

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J1")) { // process level 2
                        Map titleTKBlSumNode = (Map) groupBlSum.get("J1");
                        Date fromDateTKBlSumNode = null;
                        Date toDateTKBlSumNode = null;
                        if (!"".equals((String) titleTKBlSumNode.get("from"))) {
                            fromDateTKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKBlSumNode.get("view_to"))) {
                            toDateTKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKBlSumNode) + ")";
                        titleTKBlSumNode.put("time", time);
                        titleTKBlSum.add(titleTKBlSumNode);
                        contentTKBlSum = (List<Map>) titleTKBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J2")) { // process level 2
                        Map titleLKBlSumNode = (Map) groupBlSum.get("J2");
                        Date fromDateLKBlSumNode = null;
                        Date toDateLKBlSumNode = null;
                        if (!"".equals((String) titleLKBlSumNode.get("from"))) {
                            fromDateLKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKBlSumNode.get("view_to"))) {
                            toDateLKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKBlSumNode) + ")";
                        titleLKBlSumNode.put("time", time);
                        titleLKBlSum.add(titleLKBlSumNode);
                        contentLKBlSum = (List<Map>) titleLKBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J3")) { // process level 2
                        Map titleBSBlSumNode = (Map) groupBlSum.get("J3");
                        Date fromDateBSBlSumNode = null;
                        Date toDateBSBlSumNode = null;
                        if (!"".equals((String) titleBSBlSumNode.get("from"))) {
                            fromDateBSBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSBlSumNode.get("view_to"))) {
                            toDateBSBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSBlSumNode) + ")";
                        titleBSBlSumNode.put("time", time);
                        titleBSBlSum.add(titleBSBlSumNode);
                        contentBSBlSum = (List<Map>) titleBSBlSumNode.get("list");
                    }
                }

                if (isValidMap(groupBlSum)) {
                    if (groupBlSum.containsKey("J4")) { // process level 2
                        Map titleNKBlSumNode = (Map) groupBlSum.get("J4");
                        Date fromDateNKBlSumNode = null;
                        Date toDateNKBlSumNode = null;
                        if (!"".equals((String) titleNKBlSumNode.get("from"))) {
                            fromDateNKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBlSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKBlSumNode.get("view_to"))) {
                            toDateNKBlSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBlSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKBlSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKBlSumNode) + ")";
                        titleNKBlSumNode.put("time", time);
                        titleNKBlSum.add(titleNKBlSumNode);
                        contentNKBlSum = (List<Map>) titleNKBlSumNode.get("list");
                    }
                }
            }

            // BNPL
            List<Map> listBnplSum = new ArrayList<>(); // process level 1
            List<Map> titleTKBnplSum = new ArrayList<>();
            List<Map> contentTKBnplSum = new ArrayList<>();
            List<Map> titleLKBnplSum = new ArrayList<>();
            List<Map> contentLKBnplSum = new ArrayList<>();
            List<Map> titleBSBnplSum = new ArrayList<>();
            List<Map> contentBSBnplSum = new ArrayList<>();
            List<Map> titleNKBnplSum = new ArrayList<>();
            List<Map> contentNKBnplSum = new ArrayList<>();
            if (null != dataDto.getBnpl() && dataDto.getBnpl().size() > 0) {
                Map bnplSum = (Map) dataDto.getBnpl().get("summary");
                Map groupBnplSum = (Map) dataDto.getBnpl().get("group");
                Date fromDateBnplSum = null;
                Date toDateBnplSum = null;
                if (!"".equals((String) bnplSum.get("from"))) {
                    fromDateBnplSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) bnplSum.get("from"));
                }
                if (!"".equals((String) bnplSum.get("view_to"))) {
                    toDateBnplSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) bnplSum.get("view_to"));
                }
                StringBuilder timeBnpl = new StringBuilder("BNPL : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateBnplSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateBnplSum));
                listTimeHeaderSummary.add(timeBnpl.toString());
                listTimeHeaderBnpl.add(timeBnpl.toString());
                listBnplSum.add(bnplSum);

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K1")) { // process level 2
                        Map titleTKBnplSumNode = (Map) groupBnplSum.get("K1");
                        Date fromDateTKBnplSumNode = null;
                        Date toDateTKBnplSumNode = null;
                        if (!"".equals((String) titleTKBnplSumNode.get("from"))) {
                            fromDateTKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKBnplSumNode.get("view_to"))) {
                            toDateTKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKBnplSumNode) + ")";
                        titleTKBnplSumNode.put("time", time);
                        titleTKBnplSum.add(titleTKBnplSumNode);
                        contentTKBnplSum = (List<Map>) titleTKBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K2")) { // process level 2
                        Map titleLKBnplSumNode = (Map) groupBnplSum.get("K2");
                        Date fromDateLKBnplSumNode = null;
                        Date toDateLKBnplSumNode = null;
                        if (!"".equals((String) titleLKBnplSumNode.get("from"))) {
                            fromDateLKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKBnplSumNode.get("view_to"))) {
                            toDateLKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKBnplSumNode) + ")";
                        titleLKBnplSumNode.put("time", time);
                        titleLKBnplSum.add(titleLKBnplSumNode);
                        contentLKBnplSum = (List<Map>) titleLKBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K3")) { // process level 2
                        Map titleBSBnplSumNode = (Map) groupBnplSum.get("K3");
                        Date fromDateBSBnplSumNode = null;
                        Date toDateBSBnplSumNode = null;
                        if (!"".equals((String) titleBSBnplSumNode.get("from"))) {
                            fromDateBSBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSBnplSumNode.get("view_to"))) {
                            toDateBSBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSBnplSumNode) + ")";
                        titleBSBnplSumNode.put("time", time);
                        titleBSBnplSum.add(titleBSBnplSumNode);
                        contentBSBnplSum = (List<Map>) titleBSBnplSumNode.get("list");
                    }
                }

                if (isValidMap(groupBnplSum)) {
                    if (groupBnplSum.containsKey("K4")) { // process level 2
                        Map titleNKBnplSumNode = (Map) groupBnplSum.get("K4");
                        Date fromDateNKBnplSumNode = null;
                        Date toDateNKBnplSumNode = null;
                        if (!"".equals((String) titleNKBnplSumNode.get("from"))) {
                            fromDateNKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBnplSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKBnplSumNode.get("view_to"))) {
                            toDateNKBnplSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKBnplSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKBnplSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKBnplSumNode) + ")";
                        titleNKBnplSumNode.put("time", time);
                        titleNKBnplSum.add(titleNKBnplSumNode);
                        contentNKBnplSum = (List<Map>) titleNKBnplSumNode.get("list");
                    }
                }
            }

            // UPOS
            List<Map> listUposSum = new ArrayList<>(); // process level 1
            List<Map> titleTKUposSum = new ArrayList<>();
            List<Map> contentTKUposSum = new ArrayList<>();
            List<Map> titleLKUposSum = new ArrayList<>();
            List<Map> contentLKUposSum = new ArrayList<>();
            List<Map> titleBSUposSum = new ArrayList<>();
            List<Map> contentBSUposSum = new ArrayList<>();
            List<Map> titleNKUposSum = new ArrayList<>();
            List<Map> contentNKUposSum = new ArrayList<>();
            if (null != dataDto.getUpos() && dataDto.getUpos().size() > 0) {
                Map uposSum = (Map) dataDto.getUpos().get("summary");
                Map groupUposSum = (Map) dataDto.getUpos().get("group");
                Date fromDateUposSum = null;
                Date toDateUposSum = null;
                if (!"".equals((String) uposSum.get("from"))) {
                    fromDateUposSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) uposSum.get("from"));
                }
                if (!"".equals((String) uposSum.get("view_to"))) {
                    toDateUposSum = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy").parse((String) uposSum.get("view_to"));
                }
                StringBuilder timeUpos = new StringBuilder("UPOS : Từ ")
                        .append(formatDateHHMMSSDDMMYYYY(fromDateUposSum))
                        .append(" Đến ").append(formatDateHHMMSSDDMMYYYY(toDateUposSum));
                listTimeHeaderSummary.add(timeUpos.toString());
                listTimeHeaderUpos.add(timeUpos.toString());
                listUposSum.add(uposSum);

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L1")) { // process level 2
                        Map titleTKUposSumNode = (Map) groupUposSum.get("L1");
                        Date fromDateTKUposSumNode = null;
                        Date toDateTKUposSumNode = null;
                        if (!"".equals((String) titleTKUposSumNode.get("from"))) {
                            fromDateTKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleTKUposSumNode.get("view_to"))) {
                            toDateTKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleTKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateTKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateTKUposSumNode) + ")";
                        titleTKUposSumNode.put("time", time);
                        titleTKUposSum.add(titleTKUposSumNode);
                        contentTKUposSum = (List<Map>) titleTKUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L2")) { // process level 2
                        Map titleLKUposSumNode = (Map) groupUposSum.get("L2");
                        Date fromDateLKUposSumNode = null;
                        Date toDateLKUposSumNode = null;
                        if (!"".equals((String) titleLKUposSumNode.get("from"))) {
                            fromDateLKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleLKUposSumNode.get("view_to"))) {
                            toDateLKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleLKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateLKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateLKUposSumNode) + ")";
                        titleLKUposSumNode.put("time", time);
                        titleLKUposSum.add(titleLKUposSumNode);
                        contentLKUposSum = (List<Map>) titleLKUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L3")) { // process level 2
                        Map titleBSUposSumNode = (Map) groupUposSum.get("L3");
                        Date fromDateBSUposSumNode = null;
                        Date toDateBSUposSumNode = null;
                        if (!"".equals((String) titleBSUposSumNode.get("from"))) {
                            fromDateBSUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleBSUposSumNode.get("view_to"))) {
                            toDateBSUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleBSUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateBSUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateBSUposSumNode) + ")";
                        titleBSUposSumNode.put("time", time);
                        titleBSUposSum.add(titleBSUposSumNode);
                        contentBSUposSum = (List<Map>) titleBSUposSumNode.get("list");
                    }
                }

                if (isValidMap(groupUposSum)) {
                    if (groupUposSum.containsKey("L4")) { // process level 2
                        Map titleNKUposSumNode = (Map) groupUposSum.get("L4");
                        Date fromDateNKUposSumNode = null;
                        Date toDateNKUposSumNode = null;
                        if (!"".equals((String) titleNKUposSumNode.get("from"))) {
                            fromDateNKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKUposSumNode.get("from"));
                        }
                        if (!"".equals((String) titleNKUposSumNode.get("view_to"))) {
                            toDateNKUposSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleNKUposSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateNKUposSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateNKUposSumNode) + ")";
                        titleNKUposSumNode.put("time", time);
                        titleNKUposSum.add(titleNKUposSumNode);
                        contentNKUposSum = (List<Map>) titleNKUposSumNode.get("list");
                    }
                }
            }

            // Balance - Don vi du co
            List<Map> titleDvdcSum = new ArrayList<>(); // process level 1
            List<Map> contentDvdcSum = new ArrayList<>();
            if (null != dataDto.getBalance() && dataDto.getBalance().size() > 0) {
                Map dvdcSum = (Map) dataDto.getBalance().get("summary");
                Map groupDvdcSum = (Map) dataDto.getBalance().get("group");
                // StringBuilder timeDvdc = new StringBuilder("Từ ").append(new
                // SimpleDateFormat("HH:mm:ss
                // MM/dd/yyyy").parse((String) dvdcSum.get("from"))).append(" Đến
                // ").append(formatDateHHMMSSDDMMYYYY(new SimpleDateFormat("HH:mm:ss
                // MM/dd/yyyy").parse((String)
                // dvdcSum.get("to"))));
                // detailMap.put("time_dvdc", timeDvdc);
                titleDvdcSum.add(dvdcSum);
                if (isValidMap(groupDvdcSum)) {
                    for (Object keyName : groupDvdcSum.keySet()) {
                        Map contentDvdcSumNode = (Map) groupDvdcSum.get(keyName.toString());
                        contentDvdcSum.add(contentDvdcSumNode);
                    }
                }
            }

            // Monthly fee - Phi duy tri hang thang
            List<Map> listPdttSum = new ArrayList<>();
            if (null != dataDto.getMonthlyFee() && dataDto.getMonthlyFee().containsKey("summary")) {
                Map pdttSum = (Map) dataDto.getMonthlyFee().get("summary");
                listPdttSum.add(pdttSum);
            }

            // Est Advance - Tam ung tam tinh
            List<Map> listTuttSum = new ArrayList<>();
            if (null != dataDto.getEstAdvance() && dataDto.getEstAdvance().containsKey("summary")) {
                Map tuttSum = (Map) dataDto.getEstAdvance().get("summary");
                listTuttSum.add(tuttSum);
            }

            // Auto hold - Khoanh tu dong
            List<Map> listKtdSum = new ArrayList<>(); // process level 1
            List<Map> titleRefundKtdSum = new ArrayList<>();
            List<Map> contentRefundKtdSum = new ArrayList<>();
            List<Map> titlePayoutKtdSum = new ArrayList<>();
            List<Map> contentPayoutKtdSum = new ArrayList<>();
            if (null != dataDto.getAutoHold() && dataDto.getAutoHold().size() > 0) {
                Map ktdSum = (Map) dataDto.getAutoHold().get("summary");
                Map groupKtdSum = (Map) dataDto.getAutoHold().get("group");
                listKtdSum.add(ktdSum);

                if (isValidMap(groupKtdSum)) {
                    if (groupKtdSum.containsKey("H1")) { // process level 2
                        Map titleRefundKtdSumNode = (Map) groupKtdSum.get("H1");
                        Date fromDateRefundKtdSumNode = null;
                        Date toDateRefundKtdSumNode = null;
                        if (!"".equals((String) titleRefundKtdSumNode.get("from"))) {
                            fromDateRefundKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleRefundKtdSumNode.get("from"));
                        }
                        if (!"".equals((String) titleRefundKtdSumNode.get("view_to"))) {
                            toDateRefundKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titleRefundKtdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDateRefundKtdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDateRefundKtdSumNode) + ")";
                        titleRefundKtdSumNode.put("time", time);
                        titleRefundKtdSum.add(titleRefundKtdSumNode);
                        contentRefundKtdSum = (List<Map>) titleRefundKtdSumNode.get("list");
                    }
                }

                if (isValidMap(groupKtdSum)) {
                    if (groupKtdSum.containsKey("H2")) { // process level 2
                        Map titlePayoutKtdSumNode = (Map) groupKtdSum.get("H2");
                        Date fromDatePayoutKtdSumNode = null;
                        Date toDatePayoutKtdSumNode = null;
                        if (!"".equals((String) titlePayoutKtdSumNode.get("from"))) {
                            fromDatePayoutKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titlePayoutKtdSumNode.get("from"));
                        }
                        if (!"".equals((String) titlePayoutKtdSumNode.get("view_to"))) {
                            toDatePayoutKtdSumNode = new SimpleDateFormat("HH:mm:ss MM/dd/yyyy")
                                    .parse((String) titlePayoutKtdSumNode.get("view_to"));
                        }
                        String time = "(Từ " + formatDateHHMMSSDDMMYYYY(fromDatePayoutKtdSumNode) + " đến "
                                + formatDateHHMMSSDDMMYYYY(toDatePayoutKtdSumNode) + ")";
                        titlePayoutKtdSumNode.put("time", time);
                        titlePayoutKtdSum.add(titlePayoutKtdSumNode);
                        contentPayoutKtdSum = (List<Map>) titlePayoutKtdSumNode.get("list");
                    }
                }
            }

            // Manual adjust - Dieu chinh thu cong
            List<Map> titleDctcSum = new ArrayList<>();
            if (null != dataDto.getManualAdjust() && dataDto.getManualAdjust().containsKey("summary")) {
                Map dctcSum = (Map) dataDto.getManualAdjust().get("summary");
                titleDctcSum.add(dctcSum);
            }

            Map groupDctcSum = null != dataDto.getManualAdjust() ? (Map) dataDto.getManualAdjust().get("group") : null;

            List<Map> contentDctcSum = new ArrayList<>();
            if (isValidMap(groupDctcSum)) {
                for (Object keyName : groupDctcSum.keySet()) {
                    Map contentDctcSumNode = (Map) groupDctcSum.get(keyName.toString());
                    contentDctcSum.add(contentDctcSumNode);
                }
            }

            // Total last - Tong cuoi
            List<Map> totalSum = new ArrayList<>();
            if (null != dataDto.getLastTotal() && dataDto.getLastTotal().size() > 0) {
                Map totalSumNode = (Map) dataDto.getLastTotal();
                totalSum.add(totalSumNode);
            }

            String template = "";
            switch (type) {
                case "ALL": {
                    template = TemplateUtils.TEMPLATE_SUMMARY_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "QT": {
                    template = TemplateUtils.TEMPLATE_QT_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "ND": {
                    template = TemplateUtils.TEMPLATE_ND_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "QR": {
                    template = TemplateUtils.TEMPLATE_VI_APP_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "SMS": {
                    template = TemplateUtils.TEMPLATE_SMS_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BL": {
                    template = TemplateUtils.TEMPLATE_BILING_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BNPL": {
                    template = TemplateUtils.TEMPLATE_BNPL_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "UPOS": {
                    template = TemplateUtils.TEMPLATE_UPOS_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
                case "BALANCE": {
                    template = TemplateUtils.TEMPLATE_DVDC_PREFIX_ADVANCE_REPORT_FILE;
                    break;
                }
            }
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listTimeHeaderSummary", listTimeHeaderSummary)
                    // International
                    .setBeanValue("listTimeHeaderInt", listTimeHeaderInt)
                    .setBeanValue("listIntSum", listIntSum)
                    .setBeanValue("titleTKIntSum", titleTKIntSum)
                    .setBeanValue("contentTKIntSum", contentTKIntSum)
                    .setBeanValue("titleLKIntSum", titleLKIntSum)
                    .setBeanValue("contentLKIntSum", contentLKIntSum)
                    .setBeanValue("titleBSIntSum", titleBSIntSum)
                    .setBeanValue("contentBSIntSum", contentBSIntSum)
                    .setBeanValue("titleNKIntSum", titleNKIntSum)
                    .setBeanValue("contentNKIntSum", contentNKIntSum)
                    // Domestic
                    .setBeanValue("listTimeHeaderDom", listTimeHeaderDom)
                    .setBeanValue("listDomSum", listDomSum)
                    .setBeanValue("titleTKDomSum", titleTKDomSum)
                    .setBeanValue("contentTKDomSum", contentTKDomSum)
                    .setBeanValue("titleLKDomSum", titleLKDomSum)
                    .setBeanValue("contentLKDomSum", contentLKDomSum)
                    .setBeanValue("titleBSDomSum", titleBSDomSum)
                    .setBeanValue("contentBSDomSum", contentBSDomSum)
                    // Qr
                    .setBeanValue("listTimeHeaderQr", listTimeHeaderQr)
                    .setBeanValue("listQrSum", listQrSum)
                    .setBeanValue("titleTKQrSum", titleTKQrSum)
                    .setBeanValue("contentTKQrSum", contentTKQrSum)
                    .setBeanValue("titleLKQrSum", titleLKQrSum)
                    .setBeanValue("contentLKQrSum", contentLKQrSum)
                    .setBeanValue("titleBSQrSum", titleBSQrSum)
                    .setBeanValue("contentBSQrSum", contentBSQrSum)
                    // Sms
                    .setBeanValue("listTimeHeaderSms", listTimeHeaderSms)
                    .setBeanValue("listSmsSum", listSmsSum)
                    .setBeanValue("titleTKSmsSum", titleTKSmsSum)
                    .setBeanValue("contentTKSmsSum", contentTKSmsSum)
                    .setBeanValue("titleLKSmsSum", titleLKSmsSum)
                    .setBeanValue("contentLKSmsSum", contentLKSmsSum)
                    .setBeanValue("titleBSSmsSum", titleBSSmsSum)
                    .setBeanValue("contentBSSmsSum", contentBSSmsSum)
                    // Biling
                    .setBeanValue("listTimeHeaderBl", listTimeHeaderBl)
                    .setBeanValue("listBlSum", listBlSum)
                    .setBeanValue("titleTKBlSum", titleTKBlSum)
                    .setBeanValue("contentTKBlSum", contentTKBlSum)
                    .setBeanValue("titleLKBlSum", titleLKBlSum)
                    .setBeanValue("contentLKBlSum", contentLKBlSum)
                    .setBeanValue("titleBSBlSum", titleBSBlSum)
                    .setBeanValue("contentBSBlSum", contentBSBlSum)
                    .setBeanValue("titleNKBlSum", titleNKBlSum)
                    .setBeanValue("contentNKBlSum", contentNKBlSum)
                    // Bnpl
                    .setBeanValue("listTimeHeaderBnpl", listTimeHeaderBnpl)
                    .setBeanValue("listBnplSum", listBnplSum)
                    .setBeanValue("titleTKBnplSum", titleTKBnplSum)
                    .setBeanValue("contentTKBnplSum", contentTKBnplSum)
                    .setBeanValue("titleLKBnplSum", titleLKBnplSum)
                    .setBeanValue("contentLKBnplSum", contentLKBnplSum)
                    .setBeanValue("titleBSBnplSum", titleBSBnplSum)
                    .setBeanValue("contentBSBnplSum", contentBSBnplSum)
                    .setBeanValue("titleNKBnplSum", titleNKBnplSum)
                    .setBeanValue("contentNKBnplSum", contentNKBnplSum)
                    // Upos
                    .setBeanValue("listTimeHeaderUpos", listTimeHeaderUpos)
                    .setBeanValue("listUposSum", listUposSum)
                    .setBeanValue("titleTKUposSum", titleTKUposSum)
                    .setBeanValue("contentTKUposSum", contentTKUposSum)
                    .setBeanValue("titleLKUposSum", titleLKUposSum)
                    .setBeanValue("contentLKUposSum", contentLKUposSum)
                    .setBeanValue("titleBSUposSum", titleBSUposSum)
                    .setBeanValue("contentBSUposSum", contentBSUposSum)
                    .setBeanValue("titleNKUposSum", titleNKUposSum)
                    .setBeanValue("contentNKUposSum", contentNKUposSum)
                    // Balance
                    .setBeanValue("titleDvdcSum", titleDvdcSum)
                    .setBeanValue("contentDvdcSum", contentDvdcSum)
                    // Monthly fee
                    .setBeanValue("listPdttSum", listPdttSum)
                    // Est advance
                    .setBeanValue("listTuttSum", listTuttSum)
                    // Auto hold
                    .setBeanValue("listKtdSum", listKtdSum)
                    .setBeanValue("titleRefundKtdSum", titleRefundKtdSum)
                    .setBeanValue("contentRefundKtdSum", contentRefundKtdSum)
                    .setBeanValue("titlePayoutKtdSum", titlePayoutKtdSum)
                    .setBeanValue("contentPayoutKtdSum", contentPayoutKtdSum)
                    // Manual adjust
                    .setBeanValue("titleDctcSum", titleDctcSum)
                    .setBeanValue("contentDctcSum", contentDctcSum)
                    // Total last
                    .setBeanValue("totalSum", totalSum)
                    .build().exportExcel();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate prefix advance detail file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate prefix advance detail File successfully: ");
    }

    public static String formatDateHHMMSSDDMMYYYY(Date date) {
        if (date == null)
            return "";
        DateFormat dfTransferContent = new SimpleDateFormat("HH:mm:ss dd/MM/yyyy");
        String dmy = dfTransferContent.format(date);
        return dmy;
    }

    public static boolean isValidMap(Map map) {
        if (map == null || map.isEmpty()) {
            return false;
        }
        return true;
    }

    public static void generateAdvanceDetail(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance detail file--------------------");
            List<Map> listMap = new AdvanceDetailGenerator().generate(messageData.getRequestBody());
            String template = (String) messageData.getRequestBody().get("template");
            Map<String, Object> mData = listMap.get(0);
            List<AdvanceDetailDto> lAdvanceDetail = (List<AdvanceDetailDto>) mData.get("lstDetail");
            Long totalObjectFeeTrans = (Long) mData.get("totalObjectFeeTrans");
            Map<String, Object> mHeader = (Map<String, Object>) mData.get("header");
            Map<String, Object> header = new HashMap<>();
            String partnerName = (String) mHeader.get("S_TEN_DKKD");
            String advanceDate = (String) mHeader.get("ADVANCE_FROM_TO");
            header.put("advanceDate", advanceDate);
            header.put("partnerName", partnerName);

            Map<String, Object> total = new HashMap<>();
            Double totalAmount = 0.0;
            Double totalAmountVnd = 0.0;
            Double totalFixFeeVnd = 0.0;
            Double totalFixFeeVat = 0.0;
            Double totalFeePerFix = 0.0;
            Double totalFeePerFixVat = 0.0;
            Double totalFeeIta = 0.0;
            Double totalFeeItaVat = 0.0;
            Double totalFeeAdv = 0.0;
            Double totalFeeVat = 0.0;
            Double totalAdvanceAmount = 0.0;
            Double totalAdvanceCurrentAmount = 0.0;
            Double totalPercentFeeAmount = 0.0;
            Double totalPercentFeeVAT = 0.0;
            Double totalDisbursedAmount = 0.0;
            Double totalOrderAmount = 0.0;
            if (lAdvanceDetail != null) {
                totalAmount = (Double) mData.get("totalAmount");
                totalAmountVnd = (Double) mData.get("totalAmountVnd");
                totalFixFeeVnd = (Double) mData.get("totalFixFeeVND");
                totalFixFeeVat = (Double) mData.get("totalFixFeeVAT");
                totalFeePerFix = (Double) mData.get("totalFeePerFix");
                totalFeePerFixVat = (Double) mData.get("totalFeePerFixVAT");
                totalFeeIta = (Double) mData.get("totalFeeIta");
                totalFeeItaVat = (Double) mData.get("totalFeeItaVat");
                totalFeeAdv = (Double) mData.get("totalFeeAdv");
                totalFeeVat = (Double) mData.get("totalFeeAdvVAT");
                totalAdvanceAmount = (Double) mData.get("totalAmountAdv");
                totalAdvanceCurrentAmount = (Double) mData.get("totalAmountAdvCurrent");
                totalPercentFeeAmount = (Double) mData.get("totalPercentFeeAmount");
                totalPercentFeeVAT = (Double) mData.get("totalPercentFeeVAT");
                totalDisbursedAmount = (Double) mData.get("totalDisbursedAmount");
                totalOrderAmount = (Double) mData.get("totalOrderAmount");
            }
            total.put("totalAmount", totalAmount);
            total.put("totalAmountVnd", totalAmountVnd);
            total.put("totalFixFeeVnd", totalFixFeeVnd);
            total.put("totalFixFeeVat", totalFixFeeVat);
            total.put("totalFeePerFix", totalFeePerFix);
            total.put("totalFeePerFixVat", totalFeePerFixVat);
            total.put("totalFeeIta", totalFeeIta);
            total.put("totalFeeItaVat", totalFeeItaVat);
            total.put("totalFeeAdv", totalFeeAdv);
            total.put("totalFeeVat", totalFeeVat);
            total.put("totalAdvanceAmount", totalAdvanceAmount);
            total.put("totalAdvanceCurrentAmount", totalAdvanceCurrentAmount);
            total.put("totalPercentFeeAmount", totalPercentFeeAmount);
            total.put("totalPercentFeeAmountVat", totalPercentFeeVAT);
            total.put("totalDisbursedAmount", totalDisbursedAmount);
            total.put("totalOrderAmount", totalOrderAmount);

            String templateURI = "";
            if (totalObjectFeeTrans > 0L) {
                switch (template) {
                    case "full":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_FULL_OBJECT_FEE;
                        break;
                    case "qt":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QT_OBJECT_FEE;
                        break;
                    case "qr/nd":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QR_ND_OBJECT_FEE;
                        break;
                    case "dd":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QR_ND_OBJECT_FEE;
                        break;
                    case "bnpl":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_BNPL;
                        break;
                    case "pc":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_PAYCOLLECT;
                        break;
                }
            } else {
                switch (template) {
                    case "full":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_FULL;
                        break;
                    case "qt":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QT;
                        break;
                    case "qr/nd":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QR_ND;
                        break;
                    case "dd":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_DD;
                        break;
                    case "bnpl":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_BNPL;
                        break;
                    case "pc":
                        templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_PAYCOLLECT;
                        break;
                }
            }

            ReportBuilder.newInstance()
                    .template(templateURI)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("trans", lAdvanceDetail)
                    .setBeanValue("header", header)
                    .setBeanValue("total", total)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvanceDetail file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvanceDetail file successfully");
    }

    public static void generateAdvanceBigMerchantDetail(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance detail file--------------------");
            List<Map> listMap = new AdvanceBigMerchantDetailGenerator().generate(messageData.getRequestBody());
            String template = (String) messageData.getRequestBody().get("template");
            Map<String, Object> mData = listMap.get(0);
            List<AdvanceDetailDto> lAdvanceDetail = (List<AdvanceDetailDto>) mData.get("lstDetail");
            Map<String, Object> mHeader = (Map<String, Object>) mData.get("header");
            Map<String, Object> header = new HashMap<>();
            String partnerName = (String) mHeader.get("S_TEN_DKKD");
            String advanceDate = (String) mHeader.get("ADVANCE_FROM_TO");
            header.put("advanceDate", advanceDate);
            header.put("partnerName", partnerName);

            Map<String, Object> total = new HashMap<>();
            Double totalAmount = 0.0;
            Double totalAmountVnd = 0.0;
            Double totalFixFeeVnd = 0.0;
            Double totalFixFeeVat = 0.0;
            Double totalFeePerFix = 0.0;
            Double totalFeePerFixVat = 0.0;
            Double totalFeeIta = 0.0;
            Double totalFeeItaVat = 0.0;
            Double totalFeeAdv = 0.0;
            Double totalFeeVat = 0.0;
            Double totalAdvanceAmount = 0.0;
            Double totalAdvanceCurrentAmount = 0.0;
            Double totalPercentFeeAmount = 0.0;
            Double totalPercentFeeVAT = 0.0;
            Double totalDisbursedAmount = 0.0;
            Double totalOrderAmount = 0.0;
            if (lAdvanceDetail != null) {
                totalAmount = (Double) mData.get("totalAmount");
                totalAmountVnd = (Double) mData.get("totalAmountVnd");
                totalFixFeeVnd = (Double) mData.get("totalFixFeeVND");
                totalFixFeeVat = (Double) mData.get("totalFixFeeVAT");
                totalFeePerFix = (Double) mData.get("totalFeePerFix");
                totalFeePerFixVat = (Double) mData.get("totalFeePerFixVAT");
                totalFeeIta = (Double) mData.get("totalFeeIta");
                totalFeeItaVat = (Double) mData.get("totalFeeItaVat");
                totalFeeAdv = (Double) mData.get("totalFeeAdv");
                totalFeeVat = (Double) mData.get("totalFeeAdvVAT");
                totalAdvanceAmount = (Double) mData.get("totalAmountAdv");
                totalAdvanceCurrentAmount = (Double) mData.get("totalAmountAdvCurrent");
                totalPercentFeeAmount = (Double) mData.get("totalPercentFeeAmount");
                totalPercentFeeVAT = (Double) mData.get("totalPercentFeeVAT");
                totalDisbursedAmount = (Double) mData.get("totalDisbursedAmount");
                totalOrderAmount = (Double) mData.get("totalOrderAmount");
            }
            total.put("totalAmount", totalAmount);
            total.put("totalAmountVnd", totalAmountVnd);
            total.put("totalFixFeeVnd", totalFixFeeVnd);
            total.put("totalFixFeeVat", totalFixFeeVat);
            total.put("totalFeePerFix", totalFeePerFix);
            total.put("totalFeePerFixVat", totalFeePerFixVat);
            total.put("totalFeeIta", totalFeeIta);
            total.put("totalFeeItaVat", totalFeeItaVat);
            total.put("totalFeeAdv", totalFeeAdv);
            total.put("totalFeeVat", totalFeeVat);
            total.put("totalAdvanceAmount", totalAdvanceAmount);
            total.put("totalAdvanceCurrentAmount", totalAdvanceCurrentAmount);
            total.put("totalPercentFeeAmount", totalPercentFeeAmount);
            total.put("totalPercentFeeAmountVat", totalPercentFeeVAT);
            total.put("totalDisbursedAmount", totalDisbursedAmount);
            total.put("totalOrderAmount", totalOrderAmount);

            String templateURI = "";
            switch (template) {
                case "full":
                    templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_FULL;
                    break;
                case "qt":
                    templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QT;
                    break;
                case "qr/nd":
                    templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_QR_ND;
                    break;
                case "bnpl":
                    templateURI = TemplateUtils.TEMPLATE_ADVANCE_DETAIL_BNPL;
                    break;
            }
            ReportBuilder.newInstance()
                    .template(templateURI)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("trans", lAdvanceDetail)
                    .setBeanValue("header", header)
                    .setBeanValue("total", total)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvanceDetail file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvanceDetail file successfully");
    }

    public static void generateAdvance(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance file--------------------");
            List<Map> listMap = new AdvanceGenerator().generate(messageData.getRequestBody());
            Map<String, Object> mData = listMap.get(0);
            List<Map<String, Object>> listAdvanceVcb = (List<Map<String, Object>>) mData.get("listAdvanceVcb");
            List<Map<String, Object>> listAdvanceTcbToTcb = (List<Map<String, Object>>) mData
                    .get("listAdvanceTcbToTcb");
            List<Map<String, Object>> listAdvanceTcbToOther = (List<Map<String, Object>>) mData
                    .get("listAdvanceTcbToOther");
            List<Map<String, Object>> listAdvanceVtb = (List<Map<String, Object>>) mData.get("listAdvanceVtb");
            List<Map<String, Object>> listAdvanceVpb = (List<Map<String, Object>>) mData.get("listAdvanceVpb");
            List<Map<String, Object>> listPayout = (List<Map<String, Object>>) mData.get("listPayout");

            HashMap<String, Object> map = (HashMap<String, Object>) mData.get("map");
            ReportBuilder.newInstance()
                    .template(TemplateUtils.TEMPLATE_ADVANCE_LIST_2)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listAdvanceVcb", listAdvanceVcb)
                    .setBeanValue("listAdvanceTcbToTcb", listAdvanceTcbToTcb)
                    .setBeanValue("listAdvanceTcbToOther", listAdvanceTcbToOther)
                    .setBeanValue("listAdvanceVtb", listAdvanceVtb)
                    .setBeanValue("listAdvanceVpb", listAdvanceVpb)
                    .setBeanValue("listPayout", listPayout)
                    .setBeanValue("map", map)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvance file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvance file successfully");
    }

    public static void generateAdvanceDS(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance file--------------------");
            List<Map> listMap = new AdvanceDsGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            Map<String, Object> mData = listMap.get(0);
            List<Map<String, Object>> listAdvance = (List<Map<String, Object>>) mData.get("listAdvance");
            HashMap<String, Object> map = (HashMap<String, Object>) mData.get("map");
            detailMap.put("rangeDate", map.get("date_export"));
            // ReportBuilder.newInstance()
            // .template(TemplateUtils.TEMPLATE_ADVANCE_DS_LIST)
            // .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
            // .setBeanValue("listAdvance", listAdvance)
            // .setBeanValue("map", map)
            // .build()
            // .exportExcel();
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_ADVANCE_DS_LIST_2)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listAdvance).setBeanValue("detail", detailMap).setStart(9).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvance file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvance file successfully");
    }

    public static void generateMissingTransactionFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export MISSING TRANSACTION file--------------------");
            List<Map> listMap = new MissingTransactionGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("from_date"));
            detailMap.put("TO_DATE", parameterPost.get("to_date"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("from_date") + " đến " + parameterPost.get("to_date"));
            String fileTemplate = TemplateUtils.TEMPLATE_MISSING_TRANSACTION_P3_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setBeanValue("detail", detailMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateMissingTransactionFile file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateMissingTransactionFile file successfully");
    }

    public static void generateAdjustAdvanceApprovalFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export ADJUST ADVANCE APPROVAL file--------------------");

            List<Map> listMap = new AdjustAdvanceApprovalGenerator().generate(messageData.getRequestBody());

            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("fromDate"));
            detailMap.put("TO_DATE", parameterPost.get("toDate"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("fromDate") + " đến " + parameterPost.get("toDate"));
            String fileTemplate = TemplateUtils.TEMPLATE_ADJUST_ADVANCE_APPROVAL_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listMap", listMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate adjust advance approval file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate adjust advance approval file successfully");
    }

    public static void generateAdjustAdvanceBigMerchantApprovalFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export ADJUST ADVANCE APPROVAL file--------------------");

            List<Map> listMap = new AdjustAdvanceBigMerchantApprovalGenerator().generate(messageData.getRequestBody());
            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("fromDate"));
            detailMap.put("TO_DATE", parameterPost.get("toDate"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("fromDate") + " đến " + parameterPost.get("toDate"));
            String fileTemplate = TemplateUtils.TEMPLATE_ADJUST_ADVANCE_APPROVAL_FILE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listMap", listMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate adjust advance approval file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate adjust advance approval file successfully");
    }

    public static void generateVinhomeExtractFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START EXPORT VINHOME EXTRACT FILE--------------------");

            Map<String, Object> parameterPost = messageData.getRequestBody();
            JsonObject extractedDataFile = new JsonObject(parameterPost.get("data").toString());
            JsonArray extractedTransList = extractedDataFile.getJsonArray("list");

            List<Map> listMap = new VinhomeExtractGenerator().generate(extractedTransList);

            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("from_date"));
            detailMap.put("TO_DATE", parameterPost.get("to_date"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("from_date") + " đến " + parameterPost.get("to_date"));
            String fileTemplate = TemplateUtils.TEMPLATE_VINHOME_EXTRACT_FILE;
            System.out.println("from_date: " + parameterPost.get("from_date"));
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listMap", listMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate vinhome extract file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate vinhome extract file successfully");
    }

    public static void generateSamsungExtractFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START EXPORT SAMSUNG EXTRACT FILE--------------------");

            List<Map> listMap = new SamsungExtractGenerator().generate(messageData.getRequestBody());

            Map<String, Object> parameterPost = messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("FROM_DATE", parameterPost.get("from_date"));
            detailMap.put("TO_DATE", parameterPost.get("to_date"));
            detailMap.put("HEADER", "Từ " + parameterPost.get("from_date") + " đến " + parameterPost.get("to_date"));
            String fileTemplate = TemplateUtils.TEMPLATE_SAMSUNG_EXTRACT_FILE;
            System.out.println("from_date: " + parameterPost.get("from_date"));
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("detail", detailMap)
                    .setBeanValue("listMap", listMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generate samsung extract file error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generate samsung extract file successfully");
    }

    public static void generateDispute(Message messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export dispute--------------------");
            Map<String, Object> mIn = (Map<String, Object>) messageData.getRequestBody();
            String template = "";
            String title = "";
            if (DISPUTE_SERVICE_SUPPORT.equals(mIn.get(FILE_TYPE))) {
                template = TemplateUtils.TEMPLATE_SS_DISPUTE_MANAGEMENT;
                title = DISPUTE_SERVICE_SUPPORT_TITLE;
                mIn.put(ROLE, SS_DISPUTE_ROLE);
            } else if (DISPUTE_RISK_INT.equals(mIn.get(FILE_TYPE))) {
                template = TemplateUtils.TEMPLATE_DISPUTE_MANAGEMENT;
                title = DISPUTE_RISK_INT_TITLE;
                mIn.put(ROLE, RISK_DISPUTE_INT_ROLE);
            } else if (DISPUTE_RISK_DOM.equals(mIn.get(FILE_TYPE))) {
                template = TemplateUtils.TEMPLATE_DISPUTE_MANAGEMENT;
                title = DISPUTE_RISK_DOM_TITLE;
                mIn.put(ROLE, RISK_DISPUTE_DOM_ROLE);
            }
            List<Map> listData = new DisputeManagementGenerator()
                    .generate((Map<String, Object>) messageData.getRequestBody());
            List<String> columnInactive = new ArrayList<>();
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            List<String> columnActive = new ArrayList<String>(
                    Arrays.asList(mIn.get(COLUMN_ACTIVE).toString().split(",")));
            for (String colum : columnList) {
                int count = 0;
                for (String columnAct : columnActive) {
                    if (colum.compareTo(columnAct) == 0) {
                        count++;
                    }
                }
                if (count == 0) {
                    columnInactive.add(colum);
                    if ("channel".equalsIgnoreCase(colum))
                        columnInactive.add("gate");
                }
            }

            if (columnActive != null && columnActive.size() > 0) {
                ReportBuilder.newInstance()
                        .template(template)
                        .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listData)
                        .setBeanValue("dateRanger", mIn.get(FROM_DATE) + " - " + mIn.get(TO_DATE))
                        .setBeanValue("title", title)
                        .setListHiddenColumn(columnInactive)
                        .setStart(3)
                        .setRestyleNumber(true)
                        .setMaxCols(50)
                        .build()
                        .exportExcel2();
            } else {
                ReportBuilder.newInstance()
                        .template(template)
                        .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                        .setBeanValue("listMap", listData)
                        .setBeanValue("dateRanger", mIn.get(FROM_DATE) + " - " + mIn.get(TO_DATE))
                        .setBeanValue("title", title)
                        .setStart(3)
                        .setRestyleNumber(true)
                        .setMaxCols(50)
                        .build()
                        .exportExcel2();
            }
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateDispute file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateDispute file successfully");
    }

    public static void shopifyListFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE DAILY FEE REPORT FILE--------------------");
            List<Map> listMap = new ShopifyGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance()
                    .template(TemplateUtils.TEMPLATE_SHOPIFY_LIST_FILE)
                    .exportFileName(messageData.getRequestData().get(FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setStart(8)
                    .build()
                    .exportExcel2();

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExportShopifyList file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExportShopifyList file successfully");
    }

    public static void generateExportShopifyFile(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE SHOPIFY REPORT FILE--------------------");
            Map<String, Object> mIn = (Map<String, Object>) messageData.getRequestBody();
            Map<String, Object> detailMap = new HashMap<>();
            List<Map<String, Object>> listDataMap = new ArrayList<>();
            String template = TemplateUtils.TEMPLATE_SUMMARY_REPORT_REPORT_FILE;
            List<Integer> listStart = new ArrayList<>();
            List<Map> listMap = new ArrayList<>();
            long totalAmount = 0;
            // sheet 1
            Map<String, Object> transactionSummaryVnd = new HashMap<>();
            messageData.getRequestBody().put("currency", "VND");
            List<Map> listMapSummaryVnd = new ShopifySummaryReportGenerator().generate(messageData.getRequestBody());
            transactionSummaryVnd.put("listMap", listMapSummaryVnd);
            transactionSummaryVnd.put("dateRanger", "Từ ngày " + mIn.get(FROM_DATE) + " đến ngày " + mIn.get(TO_DATE));
            transactionSummaryVnd.put("start", 7);
            int sumSLGD = 0;
            double sumGTDG = 0.0;
            for (Map map : listMapSummaryVnd) {
                sumSLGD += Integer.parseInt(map.get("COUNT_PURCHASE") + "")
                        + Integer.parseInt(map.get("COUNT_REFUND") + "");
                sumGTDG += Double.parseDouble(map.get("AMOUNT_PURCHASE") + "")
                        - Double.parseDouble(map.get("AMOUNT_REFUND") + "");
            }
            transactionSummaryVnd.put("sumSLGD", sumSLGD);
            transactionSummaryVnd.put("sumGTDG", sumGTDG);
            listDataMap.add(transactionSummaryVnd);

            // sheet 2
            Map<String, Object> transactionSummaryUsd = new HashMap<>();
            messageData.getRequestBody().put("currency", "USD");
            List<Map> listMapSummaryUsd = new ShopifySummaryReportGenerator().generate(messageData.getRequestBody());
            transactionSummaryUsd.put("listMap", listMapSummaryUsd);
            transactionSummaryUsd.put("dateRanger", "Từ ngày " + mIn.get(FROM_DATE) + " đến ngày " + mIn.get(TO_DATE));
            transactionSummaryUsd.put("start", 7);
            sumSLGD = 0;
            sumGTDG = 0.0;
            for (Map map : listMapSummaryUsd) {
                sumSLGD += Integer.parseInt(map.get("COUNT_PURCHASE") + "")
                        + Integer.parseInt(map.get("COUNT_REFUND") + "");
                sumGTDG += Double.parseDouble(map.get("AMOUNT_PURCHASE") + "")
                        - Double.parseDouble(map.get("AMOUNT_REFUND") + "");
            }
            transactionSummaryUsd.put("sumSLGD", sumSLGD);
            transactionSummaryUsd.put("sumGTDG", sumGTDG);
            listDataMap.add(transactionSummaryUsd);

            // sheet 3
            Map<String, Object> transactionDetail = new HashMap<>();
            List<Map> listMapDetail = new ShopifyDetailReportGenerator().generate(messageData.getRequestBody());
            transactionDetail.put("listMap", listMapDetail);
            transactionDetail.put("dateRanger", "Từ ngày " + mIn.get(FROM_DATE) + " đến ngày " + mIn.get(TO_DATE));
            transactionDetail.put("start", 6);
            listDataMap.add(transactionDetail);

            List templateSheetNameList = new ArrayList<>();
            templateSheetNameList.add("sheet1");
            templateSheetNameList.add("sheet2");
            templateSheetNameList.add("sheet3");
            List<String> sheetNameList = new ArrayList<>();
            sheetNameList.add("summary vnd");
            sheetNameList.add("summary usd");
            sheetNameList.add("transaction detail");
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setListBeanValue(listDataMap)
                    .setSheetNameList(sheetNameList)
                    .setTemplateSheetNameList(templateSheetNameList)
                    .build().exportExcelMultilSheet();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "SHOPIFY ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "SHOPIFY SUCCESS: ");
    }

    public static void generateAdvanceBigMerchant(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance file--------------------");
            List<Map> listMap = new AdvanceBigMerchantGenerator().generate(messageData.getRequestBody());
            Map<String, Object> mData = listMap.get(0);
            List<Map<String, Object>> listAdvanceVcb = (List<Map<String, Object>>) mData.get("listAdvanceVcb");
            List<Map<String, Object>> listAdvanceTcbToTcb = (List<Map<String, Object>>) mData
                    .get("listAdvanceTcbToTcb");
            List<Map<String, Object>> listAdvanceTcbToOther = (List<Map<String, Object>>) mData
                    .get("listAdvanceTcbToOther");
            List<Map<String, Object>> listAdvanceVtb = (List<Map<String, Object>>) mData.get("listAdvanceVtb");
            List<Map<String, Object>> listAdvanceVpb = (List<Map<String, Object>>) mData.get("listAdvanceVpb");
            HashMap<String, Object> map = (HashMap<String, Object>) mData.get("map");
            ReportBuilder.newInstance()
                    .template(TemplateUtils.TEMPLATE_ADVANCE_LIST)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listAdvanceVcb", listAdvanceVcb)
                    .setBeanValue("listAdvanceTcbToTcb", listAdvanceTcbToTcb)
                    .setBeanValue("listAdvanceTcbToOther", listAdvanceTcbToOther)
                    .setBeanValue("listAdvanceVtb", listAdvanceVtb)
                    .setBeanValue("listAdvanceVpb", listAdvanceVpb)
                    .setBeanValue("map", map)
                    .build()
                    .exportExcel();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvance file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvance file successfully");
    }

    public static void generateAdvanceDSBigMerchant(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START export list advance file--------------------");
            List<Map> listMap = new AdvanceBigMerchantDsGenerator().generate(messageData.getRequestBody());
            Map<String, Object> detailMap = new HashMap<>();
            Map<String, Object> mData = listMap.get(0);
            List<Map<String, Object>> listAdvance = (List<Map<String, Object>>) mData.get("listAdvance");
            HashMap<String, Object> map = (HashMap<String, Object>) mData.get("map");
            detailMap.put("rangeDate", map.get("date_export"));
            // ReportBuilder.newInstance()
            // .template(TemplateUtils.TEMPLATE_ADVANCE_DS_LIST)
            // .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
            // .setBeanValue("listAdvance", listAdvance)
            // .setBeanValue("map", map)
            // .build()
            // .exportExcel();
            ReportBuilder.newInstance().template(TemplateUtils.TEMPLATE_ADVANCE_DS_LIST)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listAdvance).setBeanValue("detail", detailMap).setStart(9).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateListAdvance file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateListAdvance file successfully");
    }

    public static void generateRegisteredEmails(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "===START EXPORT LIST WEBSITE ONEPAY REGISTERED EMAILS===");
            List<Map> listMap = new HomepageRegisteredEmailsGenerator().generate(messageData.getRequestBody());
            ReportBuilder.newInstance()
                    .template(TemplateUtils.TEMPLATE_REGISTERED_EMAIL)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateRegisteredEmails file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateRegisteredEmails file successfully");
    }

    public static void generateUPOSAnalysisReport(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE UPOS ANALYSIS REPORT FILE--------------------");

            Map mIn = messageData.getRequestBody();
            String jsonData = mIn.get("data").toString();
            String displayColumn = mIn.get("displayColumn").toString();
            String termsColumn = mIn.get("termsColumn").toString();
            JsonObject jsonObjectFilter = (JsonObject) mIn.get("jsonObjectFilter");

            String fromDate = mIn.get("fromDate").toString();
            String toDate = mIn.get("toDate").toString();
            

            Type listType = new TypeToken<List<EUposReportItem>>() {}.getType();

            GsonBuilder builder = new GsonBuilder();
            builder.setDateFormat("MMM dd, yyyy HH:mm:ss");
            builder.serializeSpecialFloatingPointValues();
            Gson gson = builder.create();

            List<EUposReportItem> lstResultUpos = gson.fromJson(jsonData, listType);

            String fileTemplate = TemplateUtils.UPOS_ANALYSIS_TEMPLATE;

            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("dataUPOS", lstResultUpos)
                    .build()
                    .excelUPOSAnalysis(fromDate, toDate);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateUPOSAnalysisReport file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateUPOSAnalysisReport file successfully");
    }

    public static void generateOPFeeRevenueReport(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE OP FEE REVENUE REPORT FILE--------------------");

            Map mIn = messageData.getRequestBody();
            String jsonData = mIn.get("data").toString();
            String gridViewType = mIn.get("gridViewType").toString();
            String displayColumn = mIn.get("displayColumn").toString();
            String termsColumn = mIn.get("termsColumn").toString();
            JsonObject jsonObjectFilter = (JsonObject) mIn.get("jsonObjectFilter");

            String fromDate = mIn.get("fromDate").toString();
            String toDate = mIn.get("toDate").toString();
            String service = mIn.get("service").toString();
            String bankPartnerName = mIn.get("bankPartnerName").toString();
            String transactionType = mIn.get("transactionType").toString();
            String province = mIn.get("province").toString();
            String contractRelation = mIn.get("contractRelation").toString();
            String mcc = mIn.get("mcc").toString();
            String categoryStan = mIn.get("categoryStan").toString();
            String category = mIn.get("category").toString();
            String cardType = mIn.get("cardType").toString();
            String mpgsId = mIn.get("mpgsId").toString();
            String binCountry = mIn.get("binCountry").toString();
            String currency = mIn.get("currency").toString();
            String sale = mIn.get("sale").toString();
            String saleProvince = mIn.get("saleProvince").toString();
            String transactionDurationType = mIn.get("transactionDurationType").toString();
            String period = mIn.get("period").toString();
            String exchangeRate = mIn.get("exchangeRate").toString();
            String excelTitle = mIn.get("excelTitle").toString();
            String vat = mIn.get("vat").toString();

            Type listType = new TypeToken<List<EReportItem>>() {}.getType();

            GsonBuilder builder = new GsonBuilder();
            builder.setDateFormat("MMM dd, yyyy HH:mm:ss");
            builder.serializeSpecialFloatingPointValues();
            Gson gson = builder.create();

            List<EReportItem> lstResult = gson.fromJson(jsonData, listType);

            Map<String, Object> detailMap = new HashMap<>();
            String fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_OP_REVENUE;
            if (gridViewType.equals("column"))
                fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_OP_REVENUE_NONE_GROUP;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("data", lstResult)
                    .build()
                    .excelOPRevenue(gridViewType, displayColumn, termsColumn, fromDate, toDate, service,
                            bankPartnerName,
                            transactionType, province, contractRelation, mcc,
                            categoryStan, category, cardType, mpgsId, binCountry, currency,
                            sale, saleProvince, transactionDurationType, period,
                            exchangeRate, excelTitle, vat);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateOPFeeRevenueReport file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateOPFeeRevenueReport file successfully");
    }

    public static void generateOPFeeTransactionReportNew(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE OP FEE TRANSACTION REPORT FILE NEW --------------------");
            String[] stringArray = { "USD", "SGD", "MYR", "TWD", "CNY", "THB" };
            List<String> currencyFormat = Arrays.asList(stringArray);
            Map mIn = messageData.getRequestBody();
            String fileTemplate = TemplateUtils.TEMPLATE_PAYMENT_OP_TRANSACTION;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("data", mIn)
                    .build()
                    .excelOPTransactionReportNew(currencyFormat);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateOPFeeTransactionReport file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateOPFeeTransactionReport file successfully");
    }

    // CONFIRM REFUND
    public static void generateExcelReportConfirmRefund(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCEL REPORT CONFIRM REFUND--------------------");
            Map<String, Object> params = messageData.getRequestBody();
            JsonObject parseParams = Util.parseJsonObject(params);
            List<JsonObject> reports = ReportConfirmRefundDao.search(parseParams).getJsonArray("list").getList();
            List<Map<String, Object>> mapReports = Util.parseListMap(reports);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
            Map<String, Object> lastestReportDate = ReportConfirmRefundDao.getLastestReportDate().getMap();
            String lastestReportDateStr = formatter.format(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli((Long) lastestReportDate.get("lastestReportDate")), ZoneId.systemDefault()));
            lastestReportDate.put("lastestReportDate", lastestReportDateStr);
            String template = "templates/payment2/report_confirm_refund/temp_report_confirm_refund.xls";
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("reports", mapReports)
                    .setBeanValue("lastestReportDate", lastestReportDate)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExcelReportConfirmRefund file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExcelReportConfirmRefund file successfully");
    }

    public static void generateExcelReportConfirmRefundDetailTrans(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCEL REPORT CONFIRM REFUND DETAIL TRANS--------------------");
            Map<String, Object> params = messageData.getRequestBody();
            JsonObject parseParams = Util.parseJsonObject(params);
            String reportId = parseParams.getString("reportId");
            String type = parseParams.getString("type");
            JsonObject requestRefundTrans = ReportConfirmRefundDao.getRequestRefundTransaction(reportId, null, type,
                    null, null);
            List<JsonObject> trans = requestRefundTrans.getJsonArray("list").getList();
            List<Map<String, Object>> mapTrans = Util.parseListMapWithTimestamp(trans);
            Map<String, Object> mapHeader = requestRefundTrans.getJsonObject("total").getMap();
            String template = "templates/payment2/report_confirm_refund/temp_report_confirm_refund_detail_trans.xls";
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("trans", mapTrans)
                    .setBeanValue("header", mapHeader)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExcelReportConfirmRefundDetailTrans file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExcelReportConfirmRefundDetailTrans file successfully");
    }

    public static void generateExcelInstallmentMerchantEquals(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCEL INSTALLMENT MERCHANT--------------------");

            List<JsonObject> trans = (List<JsonObject>) messageData.getRequestBody().get("data");
            String template = "templates/temp_installment_merchant.xls";
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", trans)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExcelInstallmentMerchantEquals file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExcelInstallmentMerchantEquals file successfully");
    }

    public static void generateExcelFeeDataItaMerchant(Message<Map<String, Object>> messageData) {
        try {
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCEL PAYMENT FEE DATA ITA--------------------");
            List<Map> trans = (List<Map>) messageData.getRequestBody().get("data");
            String template = "templates/temp_fee_data_ita_merchant.xls";
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", trans)
                    .build()
                    .exportExcel();
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "generateExcelPaymentFeeDataItaMerchant file Error: ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "generateExcelPaymentFeeDataItaMerchant file successfully");
    }

    public static void generateDownloadInvoiceIndividual(Message<Map<String, Object>> messageData) {
        try {
            JsonObject mIn = new JsonObject();
            mIn.put(IDS, messageData.getRequestBody().get("id"));
            List<InvoiceIndividual> listData = InvoiceIndividualDao.getList(mIn);
            String templateDailyReport = "templates/invoice_individual/daily_report_invoice_individual.xls";
            String templateInputMisa = "templates/invoice_individual/export_invoice_individual_misa.xls";
            LOGGER.log(Level.INFO,
                    "--------------------START GENERATE EXCEL INVOICE INDIVIDUAL--------------------");
            List<String> listFilePath = new ArrayList<>();
            for (InvoiceIndividual item : listData) {
                Map<String, Object> inputItem = new HashMap<>();
                inputItem.put("id", item.getId());
                List<Map> maps = new InvoiceSummaryGenerator().generate(inputItem);
                List<Map> trans = new InvoiceTransactionGenerator().generate(inputItem);
                Date fromDate = DateTimeUtil.convertStringtoDate(item.getFromDate(), DateTemplate.YYYY_MM_DD_HHmmss);
                String fromDateStr = DateTimeUtil.convertDatetoString(fromDate, DateTemplate.DD_MM_YYYY);
                String fromDateStr2 = DateTimeUtil.convertDatetoString(fromDate, DateTemplate.DD_MM_YYYY_2);
                String fromDateTimeStr2 = DateTimeUtil.convertDatetoString(fromDate, DateTemplate.HH_MM_SS);
                Date toDate = DateTimeUtil.convertStringtoDate(item.getToDate(), DateTemplate.YYYY_MM_DD_HHmmss);
                String toDateStr = DateTimeUtil.convertDatetoString(toDate, DateTemplate.DD_MM_YYYY);
                String toDateStr2 = DateTimeUtil.convertDatetoString(toDate, DateTemplate.DD_MM_YYYY_2);
                String toDateTimeStr2 = DateTimeUtil.convertDatetoString(toDate, DateTemplate.HH_MM_SS);
                String service = item.getService();
                Map times = new HashMap<>();
                times.put("D_FROM_DATE", fromDateStr);
                times.put("D_TO_DATE", toDateStr);
                times.put("S_TIME_FROM", fromDateTimeStr2);
                times.put("S_TIME_TO", toDateTimeStr2);
                String filePathString = Config.getFileExportLocation() + "Daily Report_" + fromDateStr2 + "_" + toDateStr2 + "_" + service + ".xls";
                String filePathStringMisa = Config.getFileExportLocation() + "Input Misa_" + toDateStr2 + "_" + service + ".xls";
                ReportBuilder.newInstance().template(templateDailyReport)
                        .exportFileName(filePathString)
                        .setBeanValue("listMap", maps)
                        .setBeanValue("times", times)
                        .setBeanValue("transactionMap", trans)
                        .build()
                        .exportExcel();
                ReportBuilder.newInstance().template(templateInputMisa)
                        .exportFileName(filePathStringMisa)
                        .setBeanValue("listMap", maps)
                        .build()
                        .exportExcel();
                listFilePath.add(filePathString);
                listFilePath.add(filePathStringMisa);
            }
            addToZipFile(listFilePath, messageData);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, ": ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE EXCEL INVOICE INDIVIDUAL file successfully");
    }

    public static void generateMerchantHistory(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO,"--------------------START GENERATE MERCHANT HISTORY FILE--------------------");
            Map<String, String> body =  messageData.getRequestBody();
            String template = TemplateUtils.TEMPLATE_MERCHANT_HISTORY_FILE;
            String type = body.get("type");
            List<Map> listMap = new MerchantHistoryGenerate().generate(messageData.getRequestBody());
            if (MerchantDownloadTypeEnum.SWITCH.name().equals(type))
                template = TemplateUtils.TEMPLATE_MERCHANT_SWITCH_HISTORY_FILE;
            Map detailMap = new HashMap();
            detailMap.put("FILE_NAME", messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(1).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE MERCHANT HISTORY ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE MERCHANT HISTORY SUCCESS: ");
    }

    public static void generateAcquirerRuleGroupFile(Message<Map<String, String>> messageData) {
        try {
            LOGGER.log(Level.INFO,"--------------------START GENERATE ACQUIRER RULE GROUP FILE--------------------");
            Map<String, String> body =  messageData.getRequestBody();
            String template = "";
            if (Integer.valueOf(body.getOrDefault("searchOptionType", "1")) == 0) {
                template = TemplateUtils.TEMPLATE_ACQUIRER_RULE_GROUP_MERCHANT_FILE;
            } else {
                template = TemplateUtils.TEMPLATE_ACQUIRER_RULE_GROUP_FILE;
            }
            List<Map> listMap = new AcquirerRuleGroupGenerate().generate(messageData.getRequestBody());
            Map detailMap = new HashMap();
            detailMap.put("FILE_NAME", messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString());
            ReportBuilder.newInstance().template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).setStart(1).build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE ACQUIRER RULE GROUP ERROR: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "GENERATE ACQUIRER RULE GROUP SUCCESS: ");
    }

    /**
     * Hàm tạo file excel export danh sách phí trả góp referral partner
     */
    public static void generateReferralPartnerInstallmentFee(Message messageData) {
        try {
            LOGGER.log(Level.INFO, "------------------START EXPORT REFERRAL PARTNER INSTALLMENT FEE-------------------------------");
            LOGGER.log(Level.INFO, "messageData: " + messageData.getRequestBody());
            List<Map> listMap = new ExportInstallmentFeeGenerator()
                    .generate((Map<String, Object>) messageData.getRequestBody());
                    LOGGER.log(Level.INFO, "listMap: " + listMap);
            String fileTemplate = TemplateUtils.TEMPLATE_INSTALLMENT_FEE;
            ReportBuilder.newInstance()
                    .template(fileTemplate)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("listMap", listMap)
                    .setStart(7)
                    .build()
                    .exportExcel2();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "generateInstallmentFee", e);
        }
        LOGGER.log(Level.INFO, "generateInstallmentFee file successfully");
    }

    public static void generateCsvReportQuerySql(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE QUERY SQL REPORT FILE--------------------");
            LocalDateTime nowStart = LocalDateTime.now();
            Map<String, Object> detailMap = messageData.getRequestBody();
            String querySelect = detailMap.get("querySelect").toString();
            String schemaGr = detailMap.get("schemaGr").toString();
            long fetch = Long.valueOf(detailMap.get("fetch").toString());
            int queryTimeOut = Integer.valueOf(detailMap.get("queryTimeOut").toString());
            Integer page = Integer.valueOf(detailMap.get("page").toString());
            Integer pageSize = Integer.valueOf(detailMap.get("pageSize").toString());
            String postgres = detailMap.get("postgres").toString();
            String type = "";
            String userEmail = detailMap.get("userEmail").toString();
            boolean flag = false;
            if("YES".equalsIgnoreCase(postgres)){
                flag = true;
            }
            Set<String> tableNames = TablesNamesFinder.findTables(querySelect);
            tableNames = tableNames.stream()
                    .map(String::toUpperCase)
                    .collect(Collectors.toSet());
            List<Map<String, Object>> listData = QuerySqlDAO.dowloadQuerySql(userEmail, querySelect, schemaGr, fetch, queryTimeOut, page, pageSize,type,tableNames,flag);
            Set<String> columns = listData.get(0).keySet();
            String strHeaderColumns = String.join(", ", columns);
            
            List<Map> listMap = (List<Map>) (List<?>) listData;

            detailMap.put("FROM_DATE", "");
            detailMap.put("TO_DATE", "");

            String template = TemplateUtils.TEMPLATE_PAYOUT_BANK_CONFIG_FILE;

            ArrayList<String> listHeader = new ArrayList<String>();
            Iterator<String> iterator = columns.iterator();
            while (iterator.hasNext()) {
                listHeader.add(iterator.next());
            }
            
            String fileTitle = " ";
            String fileDate = " ";
            String fileHeader = String.join(", ", columns);
            Map<String, String> mapDate = new HashMap<>();
            mapDate.put("DATE", "dd/MM/yyyy");
            ReportBuilder.newInstance().template(template)
                .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                .setBeanValue("listMap", listMap).setBeanValue("detail", detailMap).build()
                .exportCsvRn(fileTitle, fileDate, fileHeader, listHeader, listMap, mapDate); 
            LocalDateTime nowEnd = LocalDateTime.now();
            long secondsTime = Duration.between(nowStart, nowEnd).toSeconds();
            LOGGER.log(Level.SEVERE, "process time export query sql (s): ", secondsTime);
            // if (secondsTime > 60) {
            //     String toEmail = Config.getString("email.dowloadQuerySql", "");
            //     String fHashName = Config.getFileExportLocation() + "/" + messageData.getRequestData().get(FILE_HASH_NAME);
            //     String fName = Config.getFileExportLocation() + "/" + messageData.getRequestData().get(FILE_HASH_NAME);
            //     String contentEmail = "Dear Team, <br/><br/>";
            //     contentEmail += "Export file name : " + fName + "<br/><br/>";
            //     contentEmail += "Thanks,<br/>";
            //     contentEmail += "OnePAY JSC";
            //     contentEmail += "<br/><br/> P/S: change file name attack to .csv befor open";
            //     MailUtil.sendMailAttachmentFile(toEmail, "Export file sql query", contentEmail, fHashName, fName);
            // }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Query sql Report file Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate Query sql Report File successfully: ");
    }



    private static final Logger LOGGER = Logger.getLogger(ReportFileBuilder.class.getName());

}
