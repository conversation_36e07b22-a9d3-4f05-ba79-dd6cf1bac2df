package vn.onepay.portal.queue.listener.excelBuilder.ext.roleManagement;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.leadProvider.LeadProviderReportGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantmanagement.merchantinfo.dto.PartnerDto;
import vn.onepay.portal.resources.role_management.RoleManagementDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RoleManagementGenerator implements BaseGenerator<Map<String, Object>> {
    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<PartnerDto> partnerDtos = RoleManagementDao.getPartner();
            generateRs(partnerDtos, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate Role Management error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<PartnerDto> partnerDtos, List<Map> listData) {
        Map map;
        int rowNum = 0;
        for (PartnerDto data : partnerDtos) {
            map = new HashMap();
            map.put("NO", ++rowNum);
            map.put("EMAIL", data.getEmail());
            map.put("MERCHANT_NAME", data.getShortName());
            listData.add(map);
        }
    }

    private static final Logger logger = Logger.getLogger(RoleManagementGenerator.class.getName());
}
