package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_reconciliation.bank_fee;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_reconciliation.bank_fee.dao.PaymentBankFeeDao;
import vn.onepay.portal.resources.payment_reconciliation.bank_fee.dto.PaymentPartnerFeeDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentBankFeeGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(PaymentBankFeeGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<PaymentPartnerFeeDto> listMap = new ArrayList<>();
        List<Map> list = new ArrayList<>();
        try {
            mIn.put("type", "DOWNLOAD");
            Map<String, Object> data = PaymentBankFeeDao.getBankPartnerFee(mIn);
            listMap = (List<PaymentPartnerFeeDto>) data.get("list");
            int no = 1;
            for (PaymentPartnerFeeDto partnerFeeDto : listMap) {
                Map map = new HashMap<>();
                map.put("NO", no);
                map.put("S_PARTNER_NAME", partnerFeeDto.getPartnerName());
                if (partnerFeeDto.getPartnerService() != null) {
                    if (partnerFeeDto.getPartnerService().equals("PARTNER")) {
                        map.put("S_PARTNER_SERVICE", "Referral Program");
                    } else if (partnerFeeDto.getPartnerService().equals("PLATFORM")) {
                        map.put("S_PARTNER_SERVICE", "Referral Platform");
                    } else if (partnerFeeDto.getPartnerService().equals("DD")) {
                        map.put("S_PARTNER_SERVICE", "Direct Debit");
                    } else {
                        map.put("S_PARTNER_SERVICE", partnerFeeDto.getPartnerService());
                    }
                } else {
                    map.put("S_PARTNER_SERVICE", "");
                }
                map.put("S_PARTNER_CONTRACT_TYPE", partnerFeeDto.getPartnerContractType());
                if (partnerFeeDto.getPartnerContractType() != null) {
                    if (partnerFeeDto.getPartnerContractType().equals("2B")) {
                        map.put("S_PARTNER_CONTRACT_TYPE", "2B");
                    } else if (partnerFeeDto.getPartnerContractType().equals("3B")) {
                        map.put("S_PARTNER_CONTRACT_TYPE", "3B");
                    } else {
                        map.put("S_PARTNER_CONTRACT_TYPE", "");
                    }
                } else {
                    map.put("S_PARTNER_CONTRACT_TYPE", "");
                }
                if (partnerFeeDto.getPartnerFeeType() != null) {
                    if (partnerFeeDto.getPartnerFeeType().equals("RETURN")) {
                        map.put("S_PARTNER_FEE_TYPE", "Phải trả");
                    } else if (partnerFeeDto.getPartnerFeeType().equals("COLLECT")) {
                        map.put("S_PARTNER_FEE_TYPE", "Phải thu");
                    } else {
                        map.put("S_PARTNER_FEE_TYPE", "");
                    }
                } else {
                    map.put("S_PARTNER_FEE_TYPE", "");
                }
                if (partnerFeeDto.getPartnerStatus() != null) {
                    if (partnerFeeDto.getPartnerStatus().equals("INACTIVE")) {
                        map.put("S_PARTNER_STATUS", "Chờ duyệt");
                    } else if (partnerFeeDto.getPartnerStatus().equals("ACTIVE")) {
                        map.put("S_PARTNER_STATUS", "Đã duyệt");
                    } else if (partnerFeeDto.getPartnerStatus().equals("NOT_CONFIG")) {
                        map.put("S_PARTNER_STATUS", "Chưa cấu hình");
                    } else {
                        map.put("S_PARTNER_STATUS", partnerFeeDto.getPartnerStatus());
                    }
                } else {
                    map.put("S_PARTNER_STATUS", "");
                }
                map.put("D_UPDATE", partnerFeeDto.getPartnerDateUpdate());
                no++;
                list.add(map);
            }
            System.out.println(list);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportGenerator Bank Fee error", e);
            throw IErrors.QUERY_ERROR;
        }
        return list;
    }

   
}
