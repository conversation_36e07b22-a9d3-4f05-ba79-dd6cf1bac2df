package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.indebtednessConfig;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.IndebConfigDao;
import vn.onepay.portal.resources.fee.indebconfig.IndebConfigDao2;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigQueryDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class IndebtednessConfigGenerator implements BaseGenerator<ConfigQueryDto> {
    @Override
    public List<Map> generate(ConfigQueryDto mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ConfigDto> list = IndebConfigDao.searchConfig(mIn);
            generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }
    private void generateRs(List<ConfigDto> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(ConfigDto data: list) {
            Map item = new HashMap();
            rowNumber++;

            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            item.put(TemplateUtils.ADVANCE_ACCOUNT_COLUMN, data.getAdvanceAccount());

            item.put(TemplateUtils.ADVANCE_BANK_COLUMN, data.getAdvanceBank());

            item.put(TemplateUtils.BRANCH_NAME_COLUMN, data.getBranchName());

            item.put(TemplateUtils.CONTRACT_CODE_COLUMN, data.getContractCode());

            item.put(TemplateUtils.CONTRACT_TYPE_COLUMN, data.getContractType());

            item.put(TemplateUtils.LAST_MERCHANT_NAME_COLUMN, data.getLastMerchantName());

            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());

            item.put(TemplateUtils.PARTNER_NAME_COLUMN, data.getPartnerName());

            item.put(TemplateUtils.PAYMENT_PERIOD_COLUMN, data.getPaymentPeriod());

            item.put(TemplateUtils.PAY_CHANNEL_COLUMN, data.getService());

            item.put(TemplateUtils.STATE_COLUMN, data.getState());
            item.put("D_FROM", data.getFromDate());
            item.put("D_TO", data.getToDate());
            item.put("S_ACTION", data.getStatus());

            String effectState = "";
            switch (data.getEffectiveState()){
                case "pending": effectState = "Chưa hiệu lực"; break;
                case "active": effectState = "Đang hiệu lực"; break;
                case "expired": effectState = "Hết hạn"; break;
                default: effectState = ""; break;
            }
            item.put("S_EFFECTIVE_STATE", effectState);


            item.put(TemplateUtils.TEN_DKKD_COLUMN, data.getTenDkkd());

            item.put(TemplateUtils.ACCOUNT_TYPE, data.getAccountType());

            item.put(TemplateUtils.GUARANTEE_NUMBER, data.getGuaranteeHoldingType());

            item.put(TemplateUtils.GUARANTEE_AMOUNT, data.getGuaranteeAmount());

            item.put("D_CREATE", data.getCreatedDate());
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(IndebtednessConfigGenerator.class.getName());
}
