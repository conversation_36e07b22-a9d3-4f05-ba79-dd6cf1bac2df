package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.funds_trans_history.dao.FundsTransHistoryDao;
import vn.onepay.portal.resources.payout.funds_trans_history.dto.FundsTransHistoryDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.sql.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FundTransHistoryGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<FundsTransHistoryDto> fundsTrans = new ArrayList<>();
            List<FundsTransHistoryDto> onlineList = FundsTransHistoryDao.getFundsTransactionHistory(mIn, -1);
            mIn.put("offset", "0");
            List<FundsTransHistoryDto> readonlyList = FundsTransHistoryDao.getFundsTransactionHistory(mIn, onlineList.size());
            fundsTrans.addAll(onlineList); // add online List
            fundsTrans.addAll(readonlyList);  // add readonly List
            this.generateRs(fundsTrans, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Funds trans History error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FundsTransHistoryDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (FundsTransHistoryDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.FUNDS_MERCHANT_ID, itemReport.getMerchantId());
                item.put(TemplateUtils.MERCHANT_ACCOUNT, itemReport.getMerchantAccount());
                item.put(TemplateUtils.MERCHANT_NAME, itemReport.getMerchantName());
                item.put(TemplateUtils.MERCHANT_TRANS_ID, itemReport.getMerchantTransId());
                item.put(TemplateUtils.TRANSACTION_ID, itemReport.getTransactionId());
                item.put(TemplateUtils.BANK_TRANS_ID, itemReport.getBankTransactionId());
                item.put("CREATE_DATE", itemReport.getTransactionDate() == null ? "" : Util.formatDate(new Date(itemReport.getTransactionDate().getTime()), "dd-MM-yyyy hh:mm a"));
                item.put("FUND_DATE", itemReport.getFundDate() == null ? "" : Util.formatDate(new Date(itemReport.getFundDate().getTime()), "dd-MM-yyyy hh:mm a"));
                item.put(TemplateUtils.UPDATE_DATE, itemReport.getTransactionDate() == null ? "" : Util.formatDate(new Date(itemReport.getUpdatedDate().getTime()), "dd-MM-yyyy hh:mm a"));
                item.put(TemplateUtils.BALANCE_BEFORE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceBefore()));
                item.put(TemplateUtils.BALANCE_AFTER, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceAfter()));
                item.put(TemplateUtils.RECEIVED_ACCOUNT, itemReport.getReceivedAccountNumber());
                item.put(TemplateUtils.RECEIVED_ACCOUNT_NAME, itemReport.getReceivedAccountName());
                item.put(TemplateUtils.RECEIVED_BANK, itemReport.getReceivedBank());
                item.put("AMOUNT", Util.formatCurrencyDouble(itemReport.getAmount()));
                item.put("CURRENCY", itemReport.getCurrency());
                item.put(TemplateUtils.REMARK, itemReport.getRemark());
                item.put(TemplateUtils.BANK_NAME, itemReport.getSenderBankName());
                item.put(TemplateUtils.PO_BANK_NUMBER, itemReport.getSenderBankAcc());
                StringBuilder responseCode = new StringBuilder(itemReport.getBankCode() == null ? "" : itemReport.getBankCode());
                responseCode.append(" - ").append(itemReport.getBankMsg() == null ? "" : itemReport.getBankMsg());
                item.put(TemplateUtils.RESPONSE_CODE, responseCode.toString());
                item.put(TemplateUtils.BALANCE_BANK_BEFORE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceBankBefore()));
                item.put(TemplateUtils.BALANCE_BANK_AFTER, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceBankAfter()));
                String state = itemReport.getState().substring(0, 1).toUpperCase() + itemReport.getState().substring(1);
                item.put("STATE", state.replaceAll("_"," "));
                item.put("OPERATOR", itemReport.getOperator() == null ? "" : itemReport.getOperator());

                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FundTransHistoryGenerator.class.getName());
}
