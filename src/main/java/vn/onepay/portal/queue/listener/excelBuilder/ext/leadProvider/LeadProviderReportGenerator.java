package vn.onepay.portal.queue.listener.excelBuilder.ext.leadProvider;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.lead_provider.dao.LeadProviderDao;
import vn.onepay.portal.resources.lead_provider.dto.ReportConsumerDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class LeadProviderReportGenerator implements BaseGenerator<Map<String, Object>> {

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReportConsumerDto> reportConsumerDtos = LeadProviderDao.getListReportConsumer(mIn);
            this.generateRs(reportConsumerDtos, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate Lead Provider Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReportConsumerDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (ReportConsumerDto data : list) {
                Map item = new HashMap();
                rowNumber++;

                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("T_DATE", data.gettDate() == null ? "" : Util.formatDate(new Date(data.gettDate().getTime()), "dd/MM/yyyy"));
                item.put("T_BANK", data.gettBank());
                item.put("T_PENDING", data.gettPending());
                item.put("T_REJECTED", data.gettRejected());
                item.put("T_APPROVED", data.gettApproved());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger logger = Logger.getLogger(LeadProviderReportGenerator.class.getName());
}
