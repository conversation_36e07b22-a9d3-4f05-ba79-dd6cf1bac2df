package vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation_upos;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.reconciliation.cdr.upos.CdrUposDAO;
import vn.onepay.portal.resources.reconciliation.cdr.upos.dto.ReconciliationLineUposDownloadDto;
import vn.onepay.portal.resources.reconciliation.cdr.upos.dto.ReconciliationQueryUposDownloadDto;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ReconciliationUposGenerator implements BaseGenerator<ReconciliationQueryUposDownloadDto> {

    @Override
    public List<Map> generate(ReconciliationQueryUposDownloadDto reconciliationQueryDto) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReconciliationLineUposDownloadDto> baseListExtend = CdrUposDAO.downloadListReconciliationLineUpos(reconciliationQueryDto);
            this.generateRs(baseListExtend, listMap, reconciliationQueryDto);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReconciliationLineUposDownloadDto> baseListExtend, List<Map> listData,
        ReconciliationQueryUposDownloadDto queryDto) throws Exception {
        int rowNumber = 0;
        for (ReconciliationLineUposDownloadDto itemReport : baseListExtend) {
            Map rowData = new HashMap();
            rowNumber++;

            rowData.put("index", rowNumber);
            String transType1 = itemReport.getS_trans_type_1() == null ? "" : itemReport.getS_trans_type_1();
            String transType2 = itemReport.getS_trans_type_2() == null ? "" : itemReport.getS_trans_type_2();
            String transDate1 = itemReport.getD_trans_date_1() == null ? "" : itemReport.getD_trans_date_1().toString();
            String transDate2 = itemReport.getD_trans_date_2() == null ? "" : itemReport.getD_trans_date_2().toString();
            String updateDate1 = itemReport.getD_update_1() == null ? "" : itemReport.getD_update_1().toString();
            String updateDate2 = itemReport.getD_update_2() == null ? "" : itemReport.getD_update_2().toString();
            String transStatus1 = itemReport.getS_trans_status_1() == null ? "" : itemReport.getS_trans_status_1();
            String transStatus2 = itemReport.getS_trans_status_2() == null ? "" : itemReport.getS_trans_status_2();
            String settlementStatus1 = itemReport.getSettlement_status_1() == null ? "" : itemReport.getSettlement_status_1();
            String settlementStatus2 = itemReport.getSettlement_status_2() == null ? "" : itemReport.getSettlement_status_2();
            String transId1 = itemReport.getS_trans_id_1() == null ? "" : itemReport.getS_trans_id_1();
            String transId2 = itemReport.getS_trans_id_2() == null ? "" : itemReport.getS_trans_id_2();
            String transAuthCode1 = itemReport.getAuth_code_1() == null ? "" : itemReport.getAuth_code_1();
            String transAuthCode2 = itemReport.getAuth_code_2() == null ? "" : itemReport.getAuth_code_2();
            String amount1 = itemReport.getN_amount_1() == null ? "" : itemReport.getN_amount_1();
            String amount2 = itemReport.getN_amount_2() == null ? "" : itemReport.getN_amount_2();
            String bankTransRef1 = itemReport.getBank_trans_ref_1() == null ? "" : itemReport.getBank_trans_ref_1();
            String bankTransRef2 = itemReport.getBank_trans_ref_2() == null ? "" : itemReport.getBank_trans_ref_2();
            String cardType1 = itemReport.getCard_type_1() == null ? "" : itemReport.getCard_type_1();
            String cardType2 = itemReport.getCard_type_2() == null ? "" : itemReport.getCard_type_2();
            String mcc1 = itemReport.getMcc_1() == null ? "" : itemReport.getMcc_1();
            String mcc2 = itemReport.getMcc_2() == null ? "" : itemReport.getMcc_2();
            String binCountry1 = itemReport.getBin_country_1() == null ? "" : itemReport.getBin_country_1();
            String binCountry2 = itemReport.getBin_country_2() == null ? "" : itemReport.getBin_country_2();
            String tid1 = itemReport.getTid1() == null ? "" : itemReport.getTid1();
            String tid2 = itemReport.getTid2() == null ? "" : itemReport.getTid2();
            String mid1 = itemReport.getMid1() == null ? "" : itemReport.getMid1();
            String mid2 = itemReport.getMid2() == null ? "" : itemReport.getMid2();
            String merchantName1 = itemReport.getMerchantName1() == null ? "" : itemReport.getMerchantName1();
            String merchantName2 = itemReport.getMerchantName2() == null ? "" : itemReport.getMerchantName2();

            String settlementTime1 = itemReport.getSettlement_date_1() == null ? "" : itemReport.getSettlement_date_1().toString();
            String settlementTime2 = itemReport.getSettlement_date_2() == null ? "" : itemReport.getSettlement_date_2().toString();

            String mdrAmount1 = itemReport.getMdr_amount1() ==  null ? "" : itemReport.getMdr_amount1();
            String mdrAmount2 = itemReport.getMdr_amount2() ==  null ? "" : itemReport.getMdr_amount2();

            String netSettlementAmount1 = itemReport.getNet_settlement_amount1() == null ? "" : itemReport.getNet_settlement_amount1();
            String netSettlementAmount2 = itemReport.getNet_settlement_amount2() == null ? "" : itemReport.getNet_settlement_amount2();

            String cdrResult1 = itemReport.getS_result_code_1()== null ? "" : itemReport.getS_result_code_1();
            String cdrResult2 = itemReport.getS_result_code_2()== null ? "" : itemReport.getS_result_code_2();

            String traceNumber1 = itemReport.getTrace_number_1()== null ? "" : itemReport.getTrace_number_1();
            String traceNumber2 = itemReport.getTrace_number_2()== null ? "" : itemReport.getTrace_number_2();

            String bankProcessStatus1 = itemReport.getBank_process_status_1()== null ? "" : itemReport.getBank_process_status_1();
            String bankProcessStatus2 = itemReport.getBank_process_status_2()== null ? "" : itemReport.getBank_process_status_2();
            String bankProcessDate1 = itemReport.getBank_process_date_1() == null ? "" : itemReport.getBank_process_date_1().toString();
            String bankProcessDate2 = itemReport.getBank_process_date_2() == null ? "" : itemReport.getBank_process_date_2().toString(); 
            String bankReasonReject1 = itemReport.getBank_reason_reject_1()== null ? "" : itemReport.getBank_reason_reject_1();
            String bankReasonReject2 = itemReport.getBank_reason_reject_2()== null ? "" : itemReport.getBank_reason_reject_2();

            String cdrResult = "";
            if (cdrResult1 != null) {
                cdrResult = cdrResult1;
            }
            cdrResult = cdrResult + "|";
            if (cdrResult2 != null) {
                cdrResult = cdrResult + cdrResult2;
            }

            // if ("1".equalsIgnoreCase(queryDto.getDate_type())) dateTypeString = "Settlement Date:";
            // else dateTypeString = "Transaction Date:";

            // String totalLeftPurchase = queryDto.getTotal_left_purchase()!= null ?  queryDto.getTotal_left_purchase() : "0";
            // String totalLeftRefund = queryDto.getTotal_left_refund()!= null ?  queryDto.getTotal_left_purchase() : "0";
            // String totalAmtLeftPurchase = queryDto.getTotal_purchase_not_match()!= null ? queryDto.getTotal_purchase_not_match() : "0";
            // String totalAtmLeftRefund = queryDto.getTotal_amt_left_refund()!= null ? queryDto.getTotal_amt_left_refund() : "0";

            // String totalPurchaseMatch = queryDto.getTotal_purchase_match()!= null ?  queryDto.getTotal_purchase_match() : "0";
            // String totalPurchaseNotMatch = queryDto.getTotal_amt_settlement_left()!= null ?  queryDto.getTotal_amt_settlement_left() : "0";
            // String totalRefundMatch = queryDto.getTotal_refund_match()!= null ?  queryDto.getTotal_refund_match() : "0";
            // String totalRefundNotMatch = queryDto.getTotal_refund_not_match()!= null ?  queryDto.getTotal_refund_not_match(): "0";

            // String totalRightPurchase = queryDto.getTotal_right_purchase()!= null ?  queryDto.getTotal_right_purchase() : "0";
            // String totalRightRefund = queryDto.getTotal_amt_right_refund()!= null ?  queryDto.getTotal_left_purchase() : "0";
            // String totalAmtRightPurchase = queryDto.getTotal_amt_right_purchase()!= null ?  queryDto.getTotal_amt_right_purchase() : "0";
            // String totalAtmRightRefund = queryDto.getTotal_amt_right_refund()!= null ?  queryDto.getTotal_amt_right_refund(): "0";

            // String totalSettlementLeft = queryDto.getTotal_settlement_left()!= null ?  queryDto.getTotal_settlement_left() : "0";
            // String totalAmtSettlementLeft = queryDto.getTotal_amt_settlement_left()!= null ?  queryDto.getTotal_amt_settlement_left() : "0";
            // String totalSettlementRight = queryDto.getTotal_settlement_right()!= null ?  queryDto.getTotal_settlement_right() : "0";
            // String totalAmtSettlementRight = queryDto.getTotal_amt_settlement_right()!= null ?  queryDto.getTotal_amt_settlement_right(): "0";

            rowData.put("result", cdrResult);
            rowData.put("transType1", transType1);
            rowData.put("transType2", transType2);
            rowData.put("transDate1", transDate1);
            rowData.put("transDate2", transDate2);
            rowData.put("updateDate1", updateDate1);
            rowData.put("updateDate2", updateDate2);
            rowData.put("transStatus1", transStatus1);
            rowData.put("transStatus2", transStatus2);
            rowData.put("settlementStatus1", settlementStatus1.isEmpty() ? "" : settlementStatus1.replaceAll("_", " "));
            rowData.put("settlementStatus2", settlementStatus2.isEmpty() ? "" : settlementStatus2.replaceAll("_", " "));
            rowData.put("transId1", transId1);
            rowData.put("transId2", transId2);
            rowData.put("transAuthCode1", transAuthCode1);
            rowData.put("transAuthCode2", transAuthCode2);
            rowData.put("amount1", amount1);
            rowData.put("amount2", amount2);
            rowData.put("bankTransRef1", bankTransRef1);
            rowData.put("bankTransRef2", bankTransRef2);
            rowData.put("cardType1", cardType1);
            rowData.put("cardType2", cardType2);
            rowData.put("mcc1", mcc1);
            rowData.put("mcc2", mcc2);
            rowData.put("binCountry1", binCountry1);
            rowData.put("binCountry2", binCountry2);
            rowData.put("tid1", tid1);
            rowData.put("tid2", tid2);
            rowData.put("mid1", mid1);
            rowData.put("mid2", mid2);
            rowData.put("merchantName1", merchantName1);
            rowData.put("merchantName2", merchantName2);
            rowData.put("transType1", transType1);
            rowData.put("transType2", transType2);
            rowData.put("transType1", transType1);
            rowData.put("transType2", transType2);
            rowData.put("transType1", transType1);
            rowData.put("transType2", transType2);
            rowData.put("transType1", transType1);
            rowData.put("transType2", transType2);
            rowData.put("settlementTime1", settlementTime1);
            rowData.put("settlementTime2", settlementTime2);
            rowData.put("mdrAmount1", mdrAmount1);
            rowData.put("mdrAmount2", mdrAmount2);
            rowData.put("netSettlementAmount1", netSettlementAmount1);
            rowData.put("netSettlementAmount2", netSettlementAmount2);
            rowData.put("traceNumber1", traceNumber1);
            rowData.put("traceNumber2", traceNumber2);
            rowData.put("bankProcessStatus1", bankProcessStatus1);
            rowData.put("bankProcessStatus2", bankProcessStatus2);
            rowData.put("bankProcessDate1", bankProcessDate1);
            rowData.put("bankProcessDate2", bankProcessDate2);
            rowData.put("bankReasonReject1", bankReasonReject1);
            rowData.put("bankReasonReject2", bankReasonReject2);

            // rowData.put("dateTypeString", dateTypeString);

            // rowData.put("totalLeftPurchase", totalLeftPurchase);
            // rowData.put("totalLeftRefund", totalLeftRefund);
            // rowData.put("totalAmtLeftPurchase", totalAmtLeftPurchase);
            // rowData.put("totalAtmLeftRefund", totalAtmLeftRefund);

            // rowData.put("totalPurchaseMatch", totalPurchaseMatch);
            // rowData.put("totalPurchaseNotMatch", totalPurchaseNotMatch);
            // rowData.put("totalRefundMatch", totalRefundMatch);
            // rowData.put("totalRefundNotMatch", totalRefundNotMatch);
            // rowData.put("totalSettlementLeft", totalSettlementLeft);
            // rowData.put("totalAmtSettlementLeft", totalAmtSettlementLeft);
            // rowData.put("totalSettlementRight", totalSettlementRight);
            // rowData.put("totalAmtSettlementRight", totalAmtSettlementRight);

            // rowData.put("totalRightPurchase", totalRightPurchase);
            // rowData.put("totalRightRefund", totalRightRefund);
            // rowData.put("totalAmtRightPurchase", totalAmtRightPurchase);
            // rowData.put("totalAtmRightRefund", totalAtmRightRefund);

            listData.add(rowData);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ReconciliationUposGenerator.class.getName());
}
