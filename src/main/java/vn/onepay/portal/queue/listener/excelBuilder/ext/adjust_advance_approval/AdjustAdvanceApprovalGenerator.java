package vn.onepay.portal.queue.listener.excelBuilder.ext.adjust_advance_approval;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDetailDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDetailDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
public class AdjustAdvanceApprovalGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(AdjustAdvanceApprovalGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        List<AdvanceDetailDto> list = new ArrayList<>();
        try {
            Map dataMap = new HashMap<>();          
            dataMap =  PaymentAdvanceDetailDao.getAdvanceTransactionApproval(mIn);           
            list =  (List<AdvanceDetailDto>) dataMap.get("lstDetail");
            generateRs(list,listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Export Adjust Advance Approval Error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<AdvanceDetailDto> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(AdvanceDetailDto data: list) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("ID_IMPORT", data.getId());
            item.put("ID_ADVANCE", data.getIdAdvance());
            item.put("TRANSACTION_ID", data.getTransId());
            item.put("TRANSACTION_REF", data.getTransRef());
            item.put("ORDER_INFO", data.getOrderRef());
            item.put("BANK_CARD", data.getCardType());
            item.put("DATE_TRANSACTION", data.getTransDate());
            item.put("MERCHANT_ID", data.getMerchantId());
            item.put("TRANSACTION_TYPE", data.getTransType()); 
            item.put("AMOUNT", data.getAmount());
            item.put("CURRENCY", data.getCurrency());
            item.put("EXCHANGE_RATE", data.getExchangeRate());
            item.put("AMOUNT_VND", data.getAmountVND());
            item.put("MERCHANT_DISCOUNT_AMOUNT", data.getAmountMerchantDiscount());
            item.put("PARTNER_DISCOUNT_AMOUNT", data.getAmountPartnerDiscount());                     
            item.put("ITA_BANK", data.getItaBank());
            item.put("ITA_TERM", data.getItaTerm());
            item.put("BIN_COUNTRY", data.getBinCountry());
            item.put("RESPONSE_CODE", data.getResponseCode());
            item.put("STATE_ADVANCE", data.getState());
            item.put("FIX_FEE_VND", data.getFixFee());
            item.put("FIX_FEE_VAT", data.getFixFeeVAT());
            item.put("PERCENT_FEE", data.getPercentFee());
            item.put("PERCENT_FEE_AMOUNT", data.getPercentFeeAmount());
            item.put("PERCENT_FEE_VAT", data.getPercentFeeAmountVat());
            item.put("FEE_PER_FIX", data.getFeeTotalPerFix());
            item.put("FEE_PER_FIX_VAT", data.getFeeTotalPerFixVAT());
            item.put("ITA_PERCENT_FEE", data.getFeeITAPercent());
            item.put("FEE_ITA_ADV", data.getFeeITA());
            item.put("FEE_ITA_VAT", data.getFeeITAVAT());
            item.put("TOTAL_FEE_ADV", data.getFeeTotal());
            item.put("TOTAL_FEE_VAT", data.getFeeTotalVAT());
            item.put("AMOUNT_ADV", data.getAdvanceAmount());
            item.put("AMOUNT_ADV_CURRENT", data.getAdvanceCurrentAmount());
            item.put("TYPE", data.getTypeAdj());
            item.put("TEN_DV", data.getPartnerName());
            item.put("STATE", data.getStateAdvance());
            item.put("ADJUST_USER", data.getUserName()); 
            item.put("TYPE_TRANSACTION", data.getTypeTrans());
            item.put("ADVANCE_TYPE", data.getAdvanceType().replaceAll("_", " "));   
            item.put("DESC_ADVANCE_TRANSACTION", data.getDescAdvanceTransaction());   
            item.put("D_ADVANCE", data.getdAdvance());            
            listData.add(item);
        }
    }
}

