package vn.onepay.portal.queue.listener.excelBuilder.ext.International;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.approval.InternationalRefundApprovalDao;
import vn.onepay.portal.resources.international.approval.dto.InternationalRefundApproval2;

public class InternationalRefundApprovalGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {

            List<InternationalRefundApproval2> approvalList = new ArrayList<>();
            approvalList.addAll(InternationalRefundApprovalDao.searchTransactionInvoice(mIn));
            approvalList.addAll(InternationalRefundApprovalDao.searchTransactionMADM(mIn));
            approvalList.sort((o1, o2) -> o1.getRefundDate().compareTo(o2.getRefundDate()));
            generate(approvalList, listMap);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generate(List<InternationalRefundApproval2> trans, List<Map> listData) throws Exception {
        for (int i = 0; i < trans.size(); i++) {
            InternationalRefundApproval2 rs = trans.get(i);
            // put into list
            Map item = new HashMap();
            // row number
            item.put("NO", i + 1);

            item.put("S_ID_TRAN_APPROVE", rs.getRefundId());

            item.put("S_MERCHANT_ID_INVOICE", rs.getMerchantId());

            item.put("S_MERCHANT_MIGS", rs.getMerchantIdMigs());

            item.put("S_ORDERREFERENCE", rs.getOrderRef());

            item.put("S_AUTHORISATION_CODE", rs.getAuthCode());

            item.put("S_CARD_TYPE", rs.getCardType());

            item.put("S_CARD_NO", rs.getCardNo());

            item.put("S_TRANSACTION_NO", rs.getTransactionNo());

            item.put("acquirer", rs.getAcquirer());

            item.put("S_CURRENCY", rs.getCurrency());

            item.put("N_AMOUNT_ORIGINAL", rs.getPurchaseAmount());

            item.put("D_DATE_ORIGINAL", rs.getPurchaseDate());

            item.put("S_AMOUNT", rs.getRefundAmount());

            item.put("D_DATE_WAIT_FOR_APPROVE", rs.getRefundDate());

            item.put("S_OPERATOR", rs.getOperator());

            item.put("STATUS", "Waiting for Onepay's approval");

            item.put("S_MERCHANTTRANSACTIONREFEREN", rs.getMerchantTransactionRef());

            item.put("S_BIN_COUNTRY", rs.getBinCountry());

            item.put("S_ITA_TIME", rs.getItaTime());

            item.put("S_ITA_BANK", rs.getItaBank());

            item.put("S_TYPE", rs.getType());

            item.put("S_REFUND_APPROVE_TYPE", rs.getRefundApproveType());

            item.put("S_SOURCE", rs.getSource());

            item.put("S_TOKEN_NUMBER", rs.getTokenNumber());

            item.put("S_BANK_MERCHANT_ID", rs.getBankMerchantId());

            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(InternationalRefundApprovalGenerator.class.getName());
}
