package vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.qr.approval.QrRefundApprovalDao2;
import vn.onepay.portal.resources.qr.approval.dto.QrRefundApproval;
import vn.onepay.portal.utils.PropsUtil;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class QrPaymentRefundApproval2Generator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<QrRefundApproval> qrRefundApprovals = QrRefundApprovalDao2.search(mIn);
            generateRs(qrRefundApprovals.getList(),listMap);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment refund approval 2 transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<QrRefundApproval> list, List<Map> listData ) throws Exception {

        int index = 0;
        for(QrRefundApproval dto: list)
        {
            Map item = new HashMap();
            index++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, index);

            // merchant Id
            item.put(TemplateUtils.ID_COLUMN, dto.getId());
            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, dto.getMerchantId());

            // merchant name
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, dto.getMerchantName());

            // onecomMerchantId
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, dto.getOnecomMerchantId());

            // bank
            String instrumentType = PropsUtil.get(dto.getInstrumentType(), dto.getInstrumentType()).isEmpty() ? dto.getInstrumentType() : PropsUtil.get(dto.getInstrumentType(), dto.getInstrumentType());
            item.put(TemplateUtils.BANK_COLUMN, instrumentType);

            item.put(TemplateUtils.APP_NAME_COLUMN, dto.getAppName());
            item.put(TemplateUtils.MASKING_COLUMN, dto.getMasking());

            // merchantTransId
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, dto.getTransactionId());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, dto.getOrderInfo());

            // purchase date
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, dto.getPurchaseDate());

            // Original Amount
            item.put("N_ORIGINAL_AMOUNT", dto.getOriginalAmount());

            // Payment Amount
            item.put("N_PAYMENT_AMOUNT", dto.getPaymentAmount());

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, dto.getRefundDate());

            // Original Refund Amount
            item.put("N_ORIGINAL_REFUND_AMOUNT", dto.getOriginalRefundAmount());

            String status = dto.getStatus();
            if(status != null) {
                if (status.equals("approved")) status = "Successful";
                if (status.equals("pending")) status = "Pending";
                if (status.equals("failed")) status = "Waiting for OnePAY's Approval";
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);

            // bankTransId
            item.put(TemplateUtils.BANK_REF_COLUMN, dto.getBankTransId());

            // customerTransId
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, dto.getCustomerTransId());

            // acqCode
            item.put(TemplateUtils.ACQ_CODE_COLUMN, dto.getAcqCode());
            // acqCode
            item.put(TemplateUtils.QR_ID, dto.getQrId());

            // cardNumber
            item.put(TemplateUtils.CARD_NO_COLUMN, dto.getCardNumber());

            // "S_INSTRUMENT_BRAND_ID"
            item.put("S_INSTRUMENT_BRAND_ID", dto.getInstrumentBrand() == null || dto.getInstrumentBrand().equals("") ? "" : dto.getInstrumentBrand().toUpperCase());

            // "S_INSTRUMENT_NAME"
            item.put("S_INSTRUMENT_NAME", dto.getInstrumentName());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, dto.getCurrency());

            //channel
            item.put(TemplateUtils.CHANNEL_COLUMN, dto.getChannel());

            // Refund Amount 
            item.put("N_REFUND_AMOUNT", dto.getRefundAmount());

            // Qr Type
            item.put("S_QR_TYPE", "static".equalsIgnoreCase(dto.getQrType())  ? "Static" : "Dynamic");

            // Merchant channel
            item.put("S_MERCHANT_CHANNEL", "upos".equalsIgnoreCase(dto.getMerchantChannel()) ? "Upos" : "Ecom");

            //Action Type
            item.put(TemplateUtils.ACTION_TYPE, dto.getActionType());

            //Source
            item.put("S_SOURCE", dto.getSource());

            // put into list
            listData.add(item);
        }

    }
    private static final Logger LOGGER = Logger.getLogger(QrPaymentRefundApprovalGenerator.class.getName());
}
