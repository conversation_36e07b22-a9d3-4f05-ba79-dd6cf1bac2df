package vn.onepay.portal.queue.listener.excelBuilder.ext.refundApproval;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.refundApproval.RefundApprovalDao;
import vn.onepay.portal.resources.refundApproval.dto.BaseListExtend;
import vn.onepay.portal.resources.refundApproval.dto.RefundApprovalDto;
import vn.onepay.portal.resources.refundApproval.dto.RefundApprovalQueryDto;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RefundApprovalGenerator implements BaseGenerator<RefundApprovalQueryDto> {

    @Override
    public List<Map> generate(RefundApprovalQueryDto query) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseListExtend<RefundApprovalDto> baseListExtend = RefundApprovalDao.search(query);
            this.generateRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Refund Approval Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(BaseListExtend<RefundApprovalDto> baseListExtend, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");

        for (RefundApprovalDto itemReport: baseListExtend.getList()) {
            Map<String, Object> itemBalance = baseListExtend.getBalances().get(itemReport.getAdvanceAccount());
            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("S_TRANSACTION_ID", itemReport.getId());
            item.put("S_ORIGINAL_ID", itemReport.getOriginalId());
            item.put("S_MERCHANT_ID", itemReport.getMerchantId());
            item.put("S_PAY_CHANNEL", itemReport.getPayChannel());
            item.put("S_ADVANCE_ACCOUNT", itemReport.getAdvanceAccount());
            item.put("N_BALANCE", itemReport.getAccountBalance());
            item.put("S_CURRENCY", itemReport.getCurrency());
            item.put("N_ORIGINAL_AMOUNT", itemReport.getOriginalAmount());
            item.put("N_REFUNDED_AMOUNT", itemReport.getRefundedAmount());
            item.put("S_ORIGINAL_DATE", itemReport.getOriginalDate());
            item.put("N_AMOUNT", itemReport.getAmount());
            item.put("S_DATE", itemReport.getDate());
            item.put("N_FEE", itemReport.getFee());
            item.put("S_FEE_CURRENCY", itemReport.getFeeCurrency());
            item.put("N_EXCHANGE_RATE", itemReport.getExchangeRate());
            item.put("S_MAIL_STATE", itemReport.getMailState());

            item.put("S_ORDER_REF", itemReport.getOrderRef());
            item.put("S_AUTH_CODE", itemReport.getAuthCode());
            item.put("S_CARD_NUMBER", itemReport.getCardNumber());
            item.put("S_CARD_TYPE", itemReport.getCardType());
            item.put("S_ACQUIRER", itemReport.getAcquirer());
            item.put("S_BIN_COUNTRY", itemReport.getBinCountry());
            item.put("S_ITA_TIME", itemReport.getItaTime());
            item.put("S_ITA_BANK", itemReport.getItaBank());

            item.put("S_CIAC", itemReport.getCiac());
            item.put("S_BANK_ID", itemReport.getBankId());

            item.put("S_MPAY_CARD_TYPE", itemReport.getmPayCardType());
            item.put("S_MPAY_BANK_TRANS_ID", itemReport.getmPayBankTransId());
            item.put("S_MPAY_MERCHANT_TRANS_ID", itemReport.getmPayMerchantTransId());
            item.put("S_MPAY_APP", itemReport.getmPayAppName());
            item.put("S_MPAY_CHANNEL", itemReport.getmPayChannel());
            item.put("S_MPAY_QR_ID", itemReport.getmPayQrId());
            item.put("S_MPAY_MASKING", itemReport.getmPayMasking());

            item.put("S_ACTION", itemReport.getAction());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(RefundApprovalGenerator.class.getName());
}
