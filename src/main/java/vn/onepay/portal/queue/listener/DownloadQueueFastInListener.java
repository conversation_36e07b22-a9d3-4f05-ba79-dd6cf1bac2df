package vn.onepay.portal.queue.listener;

import vn.onepay.portal.IConstants;

import javax.jms.Message;
import javax.jms.MessageListener;
import java.io.File;
import java.util.logging.Level;
import java.util.logging.Logger;


public class DownloadQueueFastInListener  extends BaseDownloadQueueInListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
        onBaseMassage(message);
    }

    @Override
    public void writeFile(vn.onepay.portal.queue.message.Message messageData) throws Exception {

        LOGGER.log(Level.INFO, "WRITE FILE NAME: " + this.fileNameHash);
        File file = new File(this.fileNameHash);
        LOGGER.log(Level.INFO, "FILE: ", file.toString());
        long fileSize = file.length();
        messageData.getRequestData().put(IConstants.FILE_SIZE, fileSize);
        messageData.setResultCode(200);
        messageData.setResultString("File Has been generated");
    }

    @Override
    String fileName(String ext) {
        return this.fileNameHash;
    }

    private static final Logger LOGGER = Logger.getLogger(DownloadQueueFastInListener.class.getName());
}
