package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDao;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDto;
import vn.onepay.portal.utils.TemplateUtils;

public class MonthlyFeeReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        List<Map> listMapEcom = new ArrayList<>();
        long indexEcom = 1;
        List<Map> listMapPos = new ArrayList<>();
        long indexPos = 1;
        List<MonthlyFeeReportDto> listData = (List<MonthlyFeeReportDto>) (MonthlyFeeReportDao.list(mIn)).get("data");

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        for (MonthlyFeeReportDto item: listData) {
            Map temp = new HashMap<>();
            temp.put(TemplateUtils.MFR_PARTNER_NAME, item.getPartnerName());
            temp.put(TemplateUtils.MFR_BUSINESS_NAME, item.getBusinessName());
            temp.put(TemplateUtils.MFR_MERCHANT_NAME, item.getMerchantName());
            temp.put(TemplateUtils.MFR_MERCHANT_IDS, item.getMerchantIds());
            temp.put(TemplateUtils.MFR_CONTRACT, item.getContractCode());
            temp.put(TemplateUtils.MFR_ADDENDUM, item.getAddendum());
            temp.put(TemplateUtils.MFR_TAX_CODE, item.getTaxCode());
            temp.put(TemplateUtils.MFR_ACCOUNT_NUMBERS, item.getAccountNumbers());
            temp.put(TemplateUtils.MFR_COUNT_SUCCESS, Long.parseLong(item.getCountSuccess()));
            temp.put(TemplateUtils.MFR_COUNT_FAILED, Double.parseDouble(item.getCountFailed()));
            temp.put(TemplateUtils.MFR_ORIGIN_AMOUNT_USD, Double.parseDouble(item.getOriginAmountUsd()));
            temp.put(TemplateUtils.MFR_ORIGIN_AMOUNT_VND, Double.parseDouble(item.getOriginAmountVnd()));
            temp.put(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT, Double.parseDouble(item.getPartnerDiscountAmount()));
            temp.put(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT, Double.parseDouble(item.getMerchantDiscountAmount()));
            temp.put(TemplateUtils.MFR_TOTAL_VND, Double.parseDouble(item.getAmountTotalVnd()));
            temp.put(TemplateUtils.MFR_FEE_SUCCESS, Double.parseDouble(item.getFeeSuccess()));
            temp.put(TemplateUtils.MFR_FEE_FAILED, Double.parseDouble(item.getFeeFailed()));
            temp.put(TemplateUtils.MFR_FEE_ECOM, Double.parseDouble(item.getFeeEcom()));
            temp.put(TemplateUtils.MFR_FEE_ITA, Double.parseDouble(item.getFeeIta()));
            temp.put(TemplateUtils.MFR_DISCOUNT_FEE, item.getDiscountFee());
            temp.put(TemplateUtils.MFR_FEE_MONTH, item.getFeeMonth());
            temp.put(TemplateUtils.MFR_FEE_TOTAL, Double.parseDouble(item.getTotalFee()));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_COLLECTED, Double.parseDouble(item.getTotalFeeCollected()));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE, Double.parseDouble(item.getTotalFeeReceivable()));
            temp.put(TemplateUtils.MFR_ADVANCE_AMOUNT, Double.parseDouble(item.getAdvanceAmount()));    
            
            String payChannels = item.getPayChannels();
            temp.put(TemplateUtils.MFR_PAY_CHANNELS, filterPayChannel(payChannels));
            temp.put(TemplateUtils.MFR_TYPE_ADVANCE, item.getTypeAdvance());
            String receiptType = item.getReceiptType();
            temp.put(TemplateUtils.MFR_RECEIPT_TYPE, receiptType);
            temp.put(TemplateUtils.MFR_RECEIPT_STATE, item.getReceiptState());

            //process period from-to
            Date fromDate = null, toDate = null;
            Long fromDateTime = item.getFromDateTime();
            Long toDateTime = item.getToDateTime();
            Long toDateTimeView = item.getToDateTimeView();
            if (fromDateTime != null && toDateTime != null) {
                if (receiptType.contains("by_advance_date")) {
                    fromDate = new Date(fromDateTime);
                    toDate = new Date(toDateTimeView);
                }
                if (receiptType.contains("by_transaction_date")) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTimeInMillis(fromDateTime);
                    int hoursFromDate = calendar.get(Calendar.HOUR_OF_DAY);
                    // cutoff 24h
                    if (hoursFromDate == 0) {
                        fromDate = new Date(fromDateTime);
                        toDate = new Date(toDateTimeView);
                    }
                    // cutoff 17h
                    if (hoursFromDate == 17) {
                        long plusHours = 7*60*60*1000; //7 hours
                        fromDate = new Date(fromDateTime + plusHours);
                        toDate = new Date(toDateTime);
                    }
                }
            }
            String formatedFromDate = fromDate == null ? "" : sdf.format(fromDate);
            String formatedToDate = toDate == null ? "" : sdf.format(toDate);
            String period = formatedFromDate + " - " + formatedToDate;
            temp.put(TemplateUtils.MFR_PERIOD_SUMMARY, period);

            //process for ecom & pos
            if (payChannels != null) {
                if (payChannels.contains("UPOS")) {
                    temp.put(TemplateUtils.MFR_ROW_NUM, indexPos++);
                    listMapPos.add(temp);
                } else {
                    temp.put(TemplateUtils.MFR_ROW_NUM, indexEcom++);
                    listMapEcom.add(temp);
                }
            } else {
                temp.put(TemplateUtils.MFR_ROW_NUM, indexEcom++);
                listMapEcom.add(temp);
            }
        }
        Map<String, Object> mapData = new HashMap();
        mapData.put("listMapEcom", listMapEcom);
        mapData.put("listMapPos", listMapPos);
        listMap.add(mapData);

        return listMap;
    }

    public static String filterPayChannel(String payChannel) {
        switch (payChannel) {
            case "PC":
                return "Paycollect";
            case "DD":
                return "Direct Debit";
            default:
                return payChannel;
        }
    }

    public static boolean isEmptyString(String value) {
        if ("".equals(value) || value == null) {
            return true;
        }
        return false;
    }
    
}
