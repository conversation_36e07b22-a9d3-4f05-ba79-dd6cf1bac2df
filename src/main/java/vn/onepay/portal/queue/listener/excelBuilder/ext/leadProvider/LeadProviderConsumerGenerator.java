package vn.onepay.portal.queue.listener.excelBuilder.ext.leadProvider;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.lead_provider.dao.LeadProviderDao;
import vn.onepay.portal.resources.lead_provider.dto.ConsumerDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class LeadProviderConsumerGenerator implements BaseGenerator<Map<String, Object>> {

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ConsumerDto> consumerDtos = LeadProviderDao.getListConsumer(mIn);
            this.generateRs(consumerDtos, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate Lead Provider Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ConsumerDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (ConsumerDto data : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("T_PARTNER", data.getPartner());
                item.put("T_MERCHANT_NAME", data.getMerchantName());
                item.put("T_MERCHANT_ID", data.getMerchantId());
                item.put("T_FULL_NAME", data.getFullName());
                item.put("T_BIRTHDAY", data.getBirthday());
                item.put("T_MOBILE_NUMBER", data.getMobileNumber());
                item.put("T_EMAIL", data.getEmail());
                item.put("T_PERSONAL_ID", data.getPersonalId());
                item.put("T_BANK", data.getBankCode());
                item.put("T_CARD_TYPE", handleCardType(data.getCardType(), ""));
                item.put("T_SIGN_UP_DATE", Util.formatDate(new Date(data.getCreatedDate().getTime()), "dd/MM/yyyy hh:mm a"));
                item.put("T_APPROVED_DATE", data.getApprovedDate() == null ? "" : Util.formatDate(new Date(data.getApprovedDate().getTime()), "dd/MM/yyyy hh:mm a"));
                item.put("T_APPROVE_STATE", Util.capitalizeString(data.getApproveState(), ""));
                item.put("T_SIGN_UP_STATE", Util.capitalizeString(data.getSignUpState(), ""));
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private String handleCardType(String inputData, String defaultData) {
        String outputData = null;
        if(inputData != null && !inputData.equals("")) {
            if(inputData.equals("onlinePlus")) {
                outputData = "Online Plus";
            } else {
                outputData = defaultData;
            }
        }
        return outputData;
    } 

    private static final Logger logger = Logger.getLogger(LeadProviderConsumerGenerator.class.getName());
}
