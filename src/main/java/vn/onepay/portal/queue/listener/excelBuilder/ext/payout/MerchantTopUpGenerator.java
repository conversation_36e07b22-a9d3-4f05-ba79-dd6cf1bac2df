package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.merchant_topup.PayoutMerchantTopupDao;
import vn.onepay.portal.resources.payout.merchant_topup.dto.MerchantTopupDTO;
import vn.onepay.portal.resources.payout.merchant_topup.dto.PartnerDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantTopUpGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<MerchantTopupDTO> topUpMerchant = PayoutMerchantTopupDao.search(mIn);
            List<PartnerDto> partnerDtos = PayoutMerchantTopupDao.getPartnerList();
            this.generateRs(topUpMerchant, partnerDtos, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant TopUp error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<MerchantTopupDTO> list, List<PartnerDto> partnerDtos, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (MerchantTopupDTO itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.ID, itemReport.getId());
                // Merchant Name
                item.put(TemplateUtils.TO_MERCHANT, itemReport.getMerchantName());

                // Business name
                item.put(TemplateUtils.BUSINESS_NAME, handleBusinessName(partnerDtos, itemReport.getMerchantId()));


                // merchant account
                item.put(TemplateUtils.TO_ACCOUNT, itemReport.getTo_account_id());
                // Bank name
                item.put(TemplateUtils.BANK_NAME, itemReport.getBankName());
                // bank trans id
                item.put(TemplateUtils.BANK_TXN_REF, itemReport.getBank_trans_id());
                // balance before
                item.put(TemplateUtils.BEFORE_BALANCE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState())) ? "-" : Util.formatCurrencyDouble(itemReport.getBanlance_before() == null ? 0 : Double.parseDouble(itemReport.getBanlance_before())));
                // amount
                item.put(TemplateUtils.AMOUNT, Util.formatCurrencyDouble(itemReport.getAmount() == null ? 0 : Double.parseDouble(itemReport.getAmount())));
                // balance After
                item.put(TemplateUtils.AFTER_BALANCE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState())) ? "-" : Util.formatCurrencyDouble(itemReport.getBalance_after() == null ? 0 : Double.parseDouble(itemReport.getBalance_after())));

                // currency
                item.put(TemplateUtils.CURRENCY, itemReport.getCurrency());

                // state
                String status = itemReport.getState().substring(0, 1).toUpperCase() + itemReport.getState().substring(1);
                item.put(TemplateUtils.STATE, status);

                // type
                String type = itemReport.getSource();
                if ("adv".equalsIgnoreCase(type)) {
                    type = "From Advance";
                } else if ("reversal".equalsIgnoreCase(type)) {
                    type = "From Reversal";
                } else if ("topup".equalsIgnoreCase(type)) {
                    type = "Merchant Funds Transfer";
                }
                item.put(TemplateUtils.STYPE, type);

                // created date
                item.put(TemplateUtils.CREATE, itemReport.getCreated_date() == null ? "" : Util.formatDate(new Date(itemReport.getCreated_date().getTime()), "dd-MM-yyyy hh:mm a"));

                item.put(TemplateUtils.UPDATE, itemReport.getUpdated_date() == null ? "" : Util.formatDate(new Date(itemReport.getUpdated_date().getTime()), "dd-MM-yyyy hh:mm a"));

                item.put("D_FUND", itemReport.getFundDate() == null ? "" : Util.formatDate(new Date(itemReport.getFundDate().getTime()), "dd-MM-yyyy hh:mm a"));

                item.put("D_MERCHANT_TOPUP", itemReport.getTopup_date() == null ? "" : Util.formatDate(new Date(itemReport.getTopup_date().getTime()), "dd-MM-yyyy hh:mm a"));

                Boolean isBankTopup = itemReport.getBank_topup();
                String bankTopup = "No";
                if (null != isBankTopup && isBankTopup) {
                    bankTopup = "Yes";
                }
                item.put("BANK_TOPUP", bankTopup);
                item.put("S_PV_NO", itemReport.getPvNo());
                // Merchant Name
                item.put(TemplateUtils.OPERATOR, itemReport.getOperator());
                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static String handleBusinessName(List<PartnerDto> partnerDtos, String merchantId) {
        String businessName = null;
        if (!partnerDtos.isEmpty()) {
            for (int i = 0; i < partnerDtos.size(); i++) {
                if (partnerDtos.get(i).getMerchantId().equals(merchantId)) {
                    businessName = partnerDtos.get(i).getBusinessName();
                    break;
                }
            }
        }

        return businessName == null ? "" : businessName;
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantTopUpGenerator.class.getName());
}
