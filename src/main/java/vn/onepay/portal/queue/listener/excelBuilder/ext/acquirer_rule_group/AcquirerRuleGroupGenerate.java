package vn.onepay.portal.queue.listener.excelBuilder.ext.acquirer_rule_group;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.refundApproval.RefundApprovalGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.refundApproval.dto.BaseListExtend;
import vn.onepay.portal.resources.system_management.cyber_mpgs_management.dao.AcquirerRuleConfigDao;
import vn.onepay.portal.resources.system_management.cyber_mpgs_management.dto.AcqRuleGroupReportDto;
import java.util.logging.Logger;

public class AcquirerRuleGroupGenerate implements BaseGenerator<Map<String, String>> {
    private static final Logger LOGGER = Logger.getLogger(RefundApprovalGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, String> query) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseListExtend<AcqRuleGroupReportDto> baseListExtend = AcquirerRuleConfigDao.search(query);
            this.generateRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Acquirer Rule Group Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(BaseListExtend<AcqRuleGroupReportDto> baseListExtend, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (AcqRuleGroupReportDto itemReport : baseListExtend.getList()) {
            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("ACQ_ID", itemReport.getId());
            item.put("ACQ_BACKUP_ID", itemReport.getIdBackup());
            item.put("S_GROUP_NAME", itemReport.getAcqRuleGroupName());
            item.put("S_SCRIPT_NAME", itemReport.getNameScript());
            item.put("S_TYPE", itemReport.getType());
            item.put("S_MERCHANT_IDS", itemReport.getListMids());
            item.put("S_RULE_CONFIGS", itemReport.getListRules());
            listData.add(item);
        }
    }
}
