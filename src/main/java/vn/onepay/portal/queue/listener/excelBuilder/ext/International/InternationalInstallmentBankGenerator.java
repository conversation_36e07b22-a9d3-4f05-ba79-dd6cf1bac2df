package vn.onepay.portal.queue.listener.excelBuilder.ext.International;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.transaction.InternationalTransactionDao;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentBankDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InternationalInstallmentBankGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);
            List<InstallmentBankDto> list = InternationalTransactionDao.searchInstallmentBankReportCommon(mIn).getList();
            generateRs(list, listMap);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<InstallmentBankDto> list, List<Map> listData ) throws Exception {

        int rowNumber = listData.size();
        for(InstallmentBankDto data: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_BANK, data.getDate_bank());

            // bank
            item.put(TemplateUtils.INSTALLMENT_BANK_REPORT, data.getInstallment_bank());

            // term
            item.put(TemplateUtils.TERM_BANK, data.getTerm_bank());

            // quantity approved
            item.put(TemplateUtils.QUANTITY_APPROVED, data.getQuantity_approved_trans());

            // total amount approved
            item.put(TemplateUtils.TOTAL_APPROVED, data.getTotal_approved_trans());

            // quantity reject
            item.put(TemplateUtils.QUANTITY_REJECT, data.getQuantity_reject_trans());

            // total amount reject
            item.put(TemplateUtils.TOTAL_REJECT, data.getTotal_reject_trans());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(InternationalInstallmentBankGenerator.class.getName());
}
