package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDto;

public class AdvanceBigMerchantDsGenerator implements BaseGenerator<Map> {
    private static final Logger LOGGER = Logger.getLogger(AdvanceBigMerchantDsGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            Map<String, Object> mOut = PaymentAdvanceDao.getAdvanceListBigMerchant(mIn);

            // sắp xếp lại
            List<AdvanceDto> advs = (List<AdvanceDto>) mOut.get("advanceList");
            List<Map> listAdvance = new ArrayList<>();
            HashMap<String, Object> map = new HashMap<>();
            // export
            Map<String, Object> beans = new HashMap<>();
            int i = 1;
            ObjectMapper objectMapper = new ObjectMapper();
            for (AdvanceDto adv : advs) {
                adv.setNo(i++);
                Map obj = objectMapper.convertValue(adv, Map.class);
                listAdvance.add(obj);

            }
            Calendar calOut = Calendar.getInstance();
            map.put("date_export", "Ngày " + calOut.get(Calendar.DATE) + " tháng " + (calOut.get(Calendar.MONTH) + 1)
                    + " năm " + calOut.get(Calendar.YEAR));
            beans.put("listAdvance", listAdvance);
            beans.put("map", map);
            listMap.add(beans);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate ADVANCE error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }
}
