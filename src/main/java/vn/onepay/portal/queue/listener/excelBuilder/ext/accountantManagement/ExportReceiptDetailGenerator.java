package vn.onepay.portal.queue.listener.excelBuilder.ext.accountantManagement;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.ExportReceiptDAO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptDTO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptDetailDTO;
import vn.onepay.portal.resources.accountantManagement.exportReceipt.dto.ExportReceiptGeneralDTO;
import vn.onepay.portal.resources.base.dto.IErrors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportReceiptDetailGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(ExportReceiptDetailGenerator.class.getName());
    private static double RANK_AMOUNT = ********;//20 triệu
    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            ExportReceiptDTO exportReceipts = ExportReceiptDAO.exportReceiptDetailDownload(mIn);
            ExportReceiptDTO exportReceiptsA = (ExportReceiptDTO)exportReceipts.clone();
            List<ExportReceiptDTO> exportReceiptDTOS = new ArrayList<>();
            List<ExportReceiptDTO> exportReceiptDTOSResult = new ArrayList<>();
            Integer id = Integer.parseInt(mIn.get("id").toString());
            ExportReceiptGeneralDTO exportReceiptDetail = ExportReceiptDAO.getReceiptDetailGeneral(id);
            ExportReceiptDTO receiptDTO = exportReceiptDetail.getExportReceipt();
            List<ExportReceiptDetailDTO> receiptDetails = exportReceiptDetail.getReceiptDetails();
            double sumTotal = 0;
            double sumEcom = 0;
            double sumIns = 0;
            String fromDate = receiptDetails.get(0).getDateFrom();
            if(receiptDTO.getInvoiceFee() > RANK_AMOUNT) { //tách hóa đơn > 20.000.000
                for(int i = 0; i < receiptDetails.size(); i++){
                    ExportReceiptDetailDTO dto = receiptDetails.get(i);
                    sumTotal += dto.geteCom() + dto.getInstFee();
                    sumEcom += dto.geteCom();
                    sumIns += dto.getInstFee();
                    if(sumTotal <= RANK_AMOUNT){
//                        System.out.println("sumTotal= "+ Util.formatCurrencyDouble(sumTotal) + " sumEcom= " + Util.formatCurrencyDouble(sumEcom) + " sumIns= "+ Util.formatCurrencyDouble(sumIns));
                        exportReceiptsA.seteCom(sumEcom);
                        exportReceiptsA.setInstFee(sumIns);
                        exportReceiptsA.seteComVat(sumEcom - sumEcom / 1.1);
                        exportReceiptsA.setInstFeeVat(sumIns - sumIns / 1.1);
                        if(i == receiptDetails.size()-1){
                            exportReceiptsA.setDetail(bindDetail(fromDate,dto.getDateTo(),exportReceipts.getContractCode()) + " - Ecom trả góp");
                            exportReceiptsA.setInstDetail(exportReceiptsA.getDetail() + " - Trả góp");
                            exportReceiptDTOS.add(exportReceiptsA);
                        }
                    }else{
                        exportReceiptsA.setDetail(bindDetail(fromDate,dto.getDateTo(),exportReceipts.getContractCode()) + " - Ecom trả góp");
                        exportReceiptsA.setInstDetail(exportReceiptsA.getDetail() + " - Trả góp");
                        exportReceiptDTOS.add(exportReceiptsA);
                        exportReceiptsA = (ExportReceiptDTO) exportReceipts.clone();
                        sumTotal = 0;
                        sumEcom = 0;
                        sumIns = 0;
                        fromDate = dto.getDateTo();
                        sumTotal += dto.geteCom() + dto.getInstFee();
                        sumEcom += dto.geteCom();
                        sumIns += dto.getInstFee();
                        exportReceiptsA.seteCom(sumEcom);
                        exportReceiptsA.setInstFee(sumIns);
                        exportReceiptsA.seteComVat(sumEcom-sumEcom / 1.1);
                        exportReceiptsA.setInstFeeVat(sumIns-sumIns / 1.1);
                    }
                }
            }
            ExportReceiptDTO last = exportReceiptDTOS.get(exportReceiptDTOS.size()-1);
            if(exportReceiptDTOS.size() > 1){
                exportReceiptDTOS.remove(exportReceiptDTOS.size()-1);
                sumEcom = 0;
                sumIns = 0;
                double sumEcomVat = 0;
                double sumInsVat = 0;
                for(ExportReceiptDTO item : exportReceiptDTOS) {
                    sumEcom += item.geteCom();
                    sumIns += item.getInstFee();
                    sumEcomVat += item.geteComVat();
                    sumInsVat += item.getInstFeeVat();
                }
                last.seteCom(receiptDTO.geteCom() - sumEcom);
                last.setInstFee(receiptDTO.getInstFee() - sumIns);
                last.setInstFeeVat(receiptDTO.getInstFeeVat() - sumInsVat);
                last.seteComVat(receiptDTO.geteComVat() - sumEcomVat);
                exportReceiptDTOS.add(last);
            }
            for (ExportReceiptDTO item : exportReceiptDTOS) {
                exportReceiptDTOSResult.add(item);
                if (item.getInstFee() != null && item.getInstFee() > 0) {
                    ExportReceiptDTO eCom = (ExportReceiptDTO) item.clone();
                    eCom.setDetail(eCom.getInstDetail());
                    eCom.seteCom(item.getInstFee());
                    eCom.seteComVat(item.getInstFeeVat());
                    eCom.setMisaMCC(item.getInstMCC());
                    eCom.setAccountSale(item.getInstAccountSale());
                    eCom.setMerchantId("");
                    exportReceiptDTOSResult.add(eCom);
                }
            }
            generateRs(exportReceiptDTOSResult, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate Export Receipt error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<ExportReceiptDTO> exportReceipts, List<Map> listMap) {
        Map mapData;
        int index = 1;
        for (ExportReceiptDTO item : exportReceipts) {
            mapData = new HashMap();
            mapData.put("NO", index++);
            mapData.put("N_PAYMENT_TYPE", item.getPaymentType());
            mapData.put("N_PAYMENT_METHOD", item.getPaymentMethod());
            mapData.put("N_DELIVERY_BILL", item.getDeliveryBill());
            mapData.put("N_WITH_RECEIPT", item.getWithReceipt());
            mapData.put("D_SETTLEMENT", item.getSettleDate());
            mapData.put("D_DOCUMENT", item.getDateDocument());
            mapData.put("S_RECEIPT_NUMBER", item.getReceiptNumber());
            mapData.put("D_RECEIPT", item.getDateReceipt());
            mapData.put("S_MISA_CODE", item.getMisaCode());
            mapData.put("S_DETAIL", item.getDetail());
            mapData.put("S_MISA_MCC", item.getMisaMCC());
            mapData.put("S_ACCOUNT_PAYMENT", item.getAccountPayment());
            mapData.put("S_ACCOUNT_SALE", item.getAccountSale());
            mapData.put("N_COUNT", item.getnCount());
            mapData.put("N_PRICE", item.geteCom());
            mapData.put("S_ACCOUNT_DISCOUNT", item.getAccountDiscount());
            mapData.put("N_VAT", item.getVat());
            mapData.put("N_TAX", item.geteComVat());
            mapData.put("S_ACCOUNT_TAX", item.getAccountTax());

            listMap.add(mapData);
        }
    }
    private String bindDetail(String fromDate, String toDate, String contractCode){
        return "Phí XLGD và phí thanh toán theo HĐ số " + contractCode + " từ ngày " + fromDate + " đến ngày " + toDate;
    }
}
