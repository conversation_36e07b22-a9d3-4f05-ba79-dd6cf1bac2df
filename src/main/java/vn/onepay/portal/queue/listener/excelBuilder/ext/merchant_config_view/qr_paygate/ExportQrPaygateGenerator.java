package vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_config_view.qr_paygate;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchant_config_view.qr_paygate.dao.QrPaygateDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportQrPaygateGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(ExportQrPaygateGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put("type", "DOWNLOAD");
            Map<String,Object> data = QrPaygateDao.searchQrPaygate(mIn);
            List<Map<String,Object>> list = (List<Map<String,Object>>) data.get("list");
            generateRs(list, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportQrPaygateGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map<String,Object>> list, List<Map> listMap) {
        Map mapData;
        int index = 1;
        for (Map<String,Object> item : list) {
            mapData = new HashMap();
            mapData.put("no", index++);
            mapData.put("merchantId", item.get("merchantId"));
            mapData.put("merchantName", item.get("merchantName"));
            mapData.put("merchantState", item.get("merchantState"));
            mapData.put("partnerName", item.get("partnerName"));
            mapData.put("mcc", item.get("mcc"));
            mapData.put("city", item.get("city"));
            mapData.put("category", item.get("category"));
            mapData.put("appActive", item.get("appActive"));
            
            listMap.add(mapData);
        }
    }
}
