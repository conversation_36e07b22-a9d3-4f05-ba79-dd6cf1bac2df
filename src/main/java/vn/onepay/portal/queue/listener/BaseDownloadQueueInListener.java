package vn.onepay.portal.queue.listener;

import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.ReportFileBuilder;
import vn.onepay.portal.queue.listener.wordBuilder.ExportWordFileBuilder;
import vn.onepay.portal.queue.producer.QueueProducer;
import vn.onepay.portal.utils.ParamsPool;
import vn.onepay.portal.utils.RoutePool;

import javax.jms.Message;
import javax.jms.ObjectMessage;
import static vn.onepay.portal.utils.RoutePool.DOWNLOAD_MULTIPLE_FILES;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public abstract class BaseDownloadQueueInListener {

    @SuppressWarnings("unchecked")
    public void onBaseMassage(Message message) {
        LOGGER.log(Level.INFO, "MESSAGE: {0}", message);
        vn.onepay.portal.queue.message.Message messageData = new vn.onepay.portal.queue.message.Message<>();
        try {
            LOGGER.log(Level.INFO, "========================== BEGIN GENERATE FILE ==========================");
            messageData = (vn.onepay.portal.queue.message.Message) ((ObjectMessage) message).getObject();
            // Set forward queue after finish generate file
            messageData.setDestinationQueue(messageData.getForwardQueue());

            this.fileNameHash = Config.getFileExportLocation() + File.separator
                    + messageData.getRequestData().get(IConstants.FILE_HASH_NAME);
            this.fileName = Config.getFileExportLocation() + File.separator
                    + messageData.getRequestData().get(IConstants.FILE_NAME);

            String downloadType = IConstants.EXCEL;
            if (messageData.getRequestBody() instanceof Map) {

                Map body = (Map) messageData.getRequestBody();
                String bodyString = body.toString();
                if (bodyString.length() < 1000) {
                    LOGGER.log(Level.INFO, "MESSAGR : {0}", body);
                }
                downloadType = body.get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL
                        : body.get(IConstants.DOWNLOADTYPE).toString();
                LOGGER.log(Level.INFO, "fileName : {0}",
                        fileName((IConstants.EXCEL.equals(downloadType) ? ".xls" : ".csv")));
                LOGGER.log(Level.INFO, "downloadType : {0}", downloadType);
            }
            Map data = (Map) messageData.getRequestData();
            String fileExt = (null == data || data.get(ParamsPool.FILE_EXT) == null) ? ".xls" : data.get(IConstants.FILE_EXT).toString();
            if (this.fileName.contains(".xlsx"))
                messageData.getRequestData().put(IConstants.FILE_NAME_FINAL, this.fileName);
            else
                messageData.getRequestData().put(IConstants.FILE_NAME_FINAL,
                        fileName((IConstants.EXCEL.equals(downloadType) ? fileExt : ".csv")));
            generateFile(messageData);
            writeFile(messageData);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "DOWNLOAD FILE ERROR : ", e);

            File file = new File(this.fileNameHash);
            if (file.exists()) {
                try {
                    Files.delete(file.toPath());
                } catch (IOException e1) {
                    LOGGER.log(Level.SEVERE, "Error Delete File Error", e);
                }
            }
            messageData.getRequestData().put(IConstants.FILE_SIZE, 0);
            messageData.setResultCode(500);
            messageData.setResultString(" File Generated Failed");
        } finally {
            // next
            QueueProducer.sendMessage(messageData);
        }
    }

    @SuppressWarnings("unchecked")
    private void generateFile(vn.onepay.portal.queue.message.Message messageData) throws Exception {
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.INTERNATIONAL_TRANSACTIONAL_DOWNLOAD)) {
            ReportFileBuilder.generateInternatinalTransactionalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INSTALLMENT_APPROVAL_DOWNLOAD)) {
            ReportFileBuilder.generateInternationalInstallmentlFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.INTERNATIONAL_REFUND_APPROVAL_DOWNLOAD)) {
            ReportFileBuilder.generateInternationalRefundApporvalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INTERNATIONAL_REFUND_APPROVAL_DOWNLOAD_V2)) {
            ReportFileBuilder.generateInternationalRefundApporvalFile2(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_TRANSACTIONAL_DOWNLOAD)) {
            ReportFileBuilder.generateDomesticTransactionalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_STATISTIC)
                || messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INTERNATIONAL_STATISTIC)) {
            ReportFileBuilder.generateDomesticStatisticFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_REFUND_APPROVAL_DOWNLOAD)) {
            ReportFileBuilder.generateDomesticRefundFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_REFUND_APPROVAL_DOWNLOAD_V2)) {
            ReportFileBuilder.generateDomesticRefundFile2(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_REPORT_DOWNLOAD)) {
            ReportFileBuilder.generateDomesticReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOMESTIC_REPORT_DOWNLOAD2)) {
            ReportFileBuilder.generateDomesticReport2File(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_REPORT_SEARCH_DOWNLOAD)) {
            ReportFileBuilder.generateQrReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.BLACK_LIST_MERCHANT_DOWNLOAD)) {
            ReportFileBuilder.generateBlackListMerchantFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.VOLUME_ALERT_CONFIG_RULE_DOWNLOAD)) {

            ReportFileBuilder.generateVolumeAlertConfigRuleFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INDEBTEDNESS_CONFIG)) {
            ReportFileBuilder.generateConfig(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INDEBTEDNESS_CONFIG_118)) {
            ReportFileBuilder.generateConfig118(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INSTALLMENT_BANK_DOWNLOAD)) {

            ReportFileBuilder.generateInstallmentBankReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.INSTALLMENT_MERCHANT_DOWNLOAD)) {

            ReportFileBuilder.generateInstallmentMerchantReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_REFUND_APPROVAL_DOWNLOAD)) {

            ReportFileBuilder.generateQrPaymentRefundApprovalReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_APPROVAL_REFUND2_DOWNLOAD)) {

            ReportFileBuilder.generateQrPaymentRefundApproval2ReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_PURCHASE_SEARCH_DOWNLOAD)) {

            ReportFileBuilder.generateQrPaymentPurchaseReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_REFUND_SEARCH_DOWNLOAD)) {

            ReportFileBuilder.generateQrPaymentRefundReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOCUMENT_TRACKING_DOWNLOAD)) {

            ReportFileBuilder.generateDocumentTrackingReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.MERCHANT_TRANSFER_DOWNLOAD)) {

            ReportFileBuilder.generateMerchantTransferReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.MERCHANT_TOPUP_REFUND_DOWNLOAD)) {
            ReportFileBuilder.generateMerchantTopupRefundReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_PAYMENT_DOWNLOAD)) {
            ReportFileBuilder.generateFixedDepositPaymentReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_PAYMENT_DETAIL_DOWNLOAD)) {
            ReportFileBuilder.generateFixedDepositPaymentDetailReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_PAYMENT_EXPORT)) {
            ReportFileBuilder.generateDownloadFDWordSingle(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_DOWNLOAD)) {

            ReportFileBuilder.generateFixedDepositReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT_DOWNLOAD_PDF)) {

            ReportFileBuilder.generateFixedDepositReportFilePdf(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT2_DOWNLOAD)) {

            ReportFileBuilder.generateFixedDeposit2ReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT2_DOWNLOAD_SELECTED)) {

            ReportFileBuilder.generateFixedDeposit2ReportFileSelected(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.FIXED_DEPOSIT2_DOWNLOAD_PDF)) {

            ReportFileBuilder.generateFixedDeposit2ReportFilePdf(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.REFUND_APPROVAL_DOWNLOAD)) {

            ReportFileBuilder.generateRefundApprovalReportFile(messageData);
        }

        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_CDR_FILE_LINE_DOWNLOAD)) {
            ReportFileBuilder.generateCDRFileDownload(messageData);
        }

        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_CDR_FILE_LINE_DOWNLOAD2)) {
            ReportFileBuilder.generateCDRFileDownload2(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_LIST_LINE_DOWNLOAD)) {
            ReportFileBuilder.generateReconciliationDownload(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_LIST_LINE_DOWNLOAD_INTER)) {
            ReportFileBuilder.generateReconciliationInterDownload(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_DOWNLOAD_THIRD)) {
            ReportFileBuilder.generateReconciliationThirdDownload(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.CONTRACT_EXPORT)) {

            ExportWordFileBuilder.generateContractExportFile(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.CONTRACT_EXPORT)) {

            ExportWordFileBuilder.generateContractExportFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_BNPL_LIST_LINE_DOWNLOAD)) {
            ReportFileBuilder.generateBNPLReconciliationDownload(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.TRANSACTION_HISTORY_DOWNLOAD)) {

            ReportFileBuilder.generateTransactionHistoryReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAY_COLLECT_REPORT_DOWNLOAD)) {

            ReportFileBuilder.generatePayCollectReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_CONFIG_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutMerchantConfigReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_CONFIG_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutBankConfigReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_TOP_UP_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutBankTopUpReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_ACC_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutMerchantAccBalanceFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_TOP_UP_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutMerchantTopUpReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_FUND_TRANS_HIS_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutFundsTransHistoryReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_SUMMARY_FUNDTRANFER_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutSummaryFundTranferReportFile(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_FEE_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutBankFeeReportFile(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_TRANFER_REPORT)) {
            ReportFileBuilder.generatePayoutBankTranfersReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_TRANFER_REPORT)) {
            ReportFileBuilder.generatePayoutMerchantTranfersReportFile(messageData);
        } else if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_FEE_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutMerchantFeeReportFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_BALANCE_ENQUIRY_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutBankBalanceEnquiryReportFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.PAYOUT_MERCHANT_BALANCE_ENQUIRY_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutMerchantBalanceEnquiryReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_SWIFT_CODE_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutSwiftCodeReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.PAYOUT_BANK_MAP_DOWNLOAD)) {
            ReportFileBuilder.generatePayoutBankMapReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.LEAD_PROVIDER_CONSUMER_DOWNLOAD)) {
            System.out.println(messageData.getRequestPath());
            ReportFileBuilder.generateLeadProviderConsumerFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.LEAD_PROVIDER_REPORT_DOWNLOAD)) {
            System.out.println(messageData.getRequestPath());
            ReportFileBuilder.generateLeadProviderReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.ACCOUNTANT_EXPORT_REPORT_DOWNLOAD)) {
            ReportFileBuilder.generateAccountantReportFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_ROLE_MANAGEMENT_FILE)) {
            ReportFileBuilder.generateRoleManagementFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DEBT_CLEARANCE_REPORT_DOWNLOAD)) {
            ReportFileBuilder.generateDebtClearanceManagementFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.ACCOUNT_SUMMARY_STATEMENT)) {
            ReportFileBuilder.generateAccountantStmSummaryFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_RECEIPT_DOWNLOAD)) {
            ReportFileBuilder.generateExportReceiptFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_RECEIPT_DETAIL_DOWNLOAD)) {
            ReportFileBuilder.generateExportReceiptDetailFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.ON_OFF_BANKS_APPROVAL_HISTORY_FILE)) {
            ReportFileBuilder.generateOnOffBanksApprovalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QR_PAYGATE_DOWNLOAD)) {
            ReportFileBuilder.generateExportQrPaygateFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.SS_TRANS_MANAGEMENT_DOWNLOAD)) {
            ReportFileBuilder.generateExportSSTransManagement(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.SS_TRANS_MANAGEMENT_DOWNLOAD_PAYMENT)) {
            ReportFileBuilder.generateExportSSTransManagementPayment(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ALL_LINK)) {
            ReportFileBuilder.generateExportFullLinkFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.LIST_MERCHANT_ACCOUNT)) {
            ReportFileBuilder.generateListMerchantAccountFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_EXCEL_MONTHLY_FEE_REPORT)) {
            ReportFileBuilder.generateExportMonthlyFeeReport(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_EXCEL_DAILY_FEE_REPORT)) {
            ReportFileBuilder.generateExportDailyFeeReport(messageData);
        }
        if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_EXCEL)) {
            ReportFileBuilder.generateDownloadAdvExcelSingle(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_TRANSACTION_ACCOUNTING)) {
            ReportFileBuilder.generateExportExcelTransactionAccounting(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_TRANSACTION_ACCOUNTING_18)) {
            ReportFileBuilder.generateExportExcelTransactionAccounting18(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_INTERNATIONAL_REPORT_18)) {
            ReportFileBuilder.generateExportExcelInterReport(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_TRANSACTION_OVERLIMIT)) {
            ReportFileBuilder.generateExportExcelOverLimitTrans(messageData);
        } else if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_REPORT_OVERLIMIT)) {
            ReportFileBuilder.generateExportExcelOverLimitReport(messageData);
        }
        if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_PDF)) {
            ReportFileBuilder.generateDownloadAdvPdfSingle(messageData);
        }
        if (messageData.getRequestPath().equals(RoutePool.MONTHLY_FEE_REPORT_DOWNLOAD_ADVANCE_DETAIL_TRANS_EXCEL)) {
            ReportFileBuilder.generateDownloadAdvDetailTransExcelSingle(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_MERCHANT_FEE_INSTALLMENT_EXPORT)) {
            ReportFileBuilder.generateInstallmentFee(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_MERCHANT_FEE_3B_INSTALLMENT_EXPORT)) {
            ReportFileBuilder.generateItaFeeMerchant3B(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_BANK_FEE_3B_INSTALLMENT_EXPORT)) {
            ReportFileBuilder.generateItaFeeBank3B(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_MERCHANT_FEE_DOWNLOAD)) {
            ReportFileBuilder.generatePaymentMerchantFeeFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT2_END_USER_DOWNLOAD)) {
            ReportFileBuilder.generatePayment2EndUserFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_BANK_FEE_DOWNLOAD)) {
            ReportFileBuilder.generatePaymentBankFeeFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_ADVANCE_TRANSACTION_DOWNLOAD)) {
            ReportFileBuilder.generateAdvanceTransactionFile(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.MERCHANT_ACCOUNT_DOWNLOAD)) {
            ReportFileBuilder.generateMerchantAccountFile(messageData);
        }

        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE_TRANSACTION_DETAIL)) {
            ReportFileBuilder.generateAdvanceDetail(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_PRE_ADVANCE_DETAIL)) {
            ReportFileBuilder.generatePreAdvanceDetail(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE)) {
            ReportFileBuilder.generateAdvance(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE_DS)) {
            ReportFileBuilder.generateAdvanceDS(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXCHANGE_RATE_DOWNLOAD)) {
            ReportFileBuilder.generateExchangeRateFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_MISSING_TRANSACTION_DOWNLOAD)) {
            ReportFileBuilder.generateMissingTransactionFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_ADJUST_ADVANCE_APPROVAL)) {
            ReportFileBuilder.generateAdjustAdvanceApprovalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_VINHOME_EXTRACT)) {
            ReportFileBuilder.generateVinhomeExtractFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_SAMSUNG_EXTRACT)) {
            ReportFileBuilder.generateSamsungExtractFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_DISPUTE)) {
            ReportFileBuilder.generateDispute(messageData);
        }

        if (messageData.getRequestPath().startsWith((Config.getUriPrefix() + RoutePool.INQUIRT_BANK_DOWNLOAD))) {
            ReportFileBuilder.generateInquiryFile(messageData);
        }

        if (messageData.getRequestPath().startsWith((Config.getUriPrefix() + RoutePool.SHOPIFY_LIST_DOWNLOAD))) {
            ReportFileBuilder.shopifyListFile(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.RECONCILIATION_LIST_LINE_UPOS_DOWNLOAD)) {
            ReportFileBuilder.generateReconciliationUposDownload(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_SHOPIFY)) {
            ReportFileBuilder.generateExportShopifyFile(messageData);
        }

        // ADVANCE BIG MERCHANT
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_PRE_ADVANCE_BIG_MERCHANT_DETAIL)) {
            ReportFileBuilder.generatePreAdvanceBigMerchantDetail(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE_BIG_MERCHANT_TRANSACTION_DETAIL)) {
            ReportFileBuilder.generateAdvanceBigMerchantDetail(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE_BIG_MERCHANT)) {
            ReportFileBuilder.generateAdvanceBigMerchant(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_ADVANCE_DS_BIG_MERCHANT)) {
            ReportFileBuilder.generateAdvanceDSBigMerchant(messageData);
        }
        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.URI_PAYMENT_ADJUST_ADVANCE_BIG_MERCHANT_APPROVAL)) {
            ReportFileBuilder.generateAdjustAdvanceBigMerchantApprovalFile(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_HOMEPAGE_REGISTERED_EMAIL)) {
            ReportFileBuilder.generateRegisteredEmails(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_EXCEL_OP_REVENUE_REPORT)) {
            ReportFileBuilder.generateOPFeeRevenueReport(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWLOAD_UPOS_ANALYSIS_EXCEL_REPORT)) {
            ReportFileBuilder.generateUPOSAnalysisReport(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_EXCEL_OP_TRANSACTION_REPORT)) {
            ReportFileBuilder.generateOPFeeTransactionReportNew(messageData);
        }
        // CONFIRM REFUND
        if (messageData.getRequestPath().equals(RoutePool.EXPORT_EXCEL_REPORT_CONFIRM_REFUND)) {
            ReportFileBuilder.generateExcelReportConfirmRefund(messageData);
        }
        if (messageData.getRequestPath().equals(RoutePool.EXPORT_EXCEL_REPORT_CONFIRM_REFUND_DETAIL_TRANS)) {
            ReportFileBuilder.generateExcelReportConfirmRefundDetailTrans(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_INSTALL_MERCHANT_EQUALS)
                || messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_INSTALL_MERCHANT_MATCH)
                || messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_INSTALL_MERCHANT_NOT_MATCH)) {
            ReportFileBuilder.generateExcelInstallmentMerchantEquals(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_FEE_DATA_ITA_MERCHANT)) {
            ReportFileBuilder.generateExcelFeeDataItaMerchant(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.EXPORT_INVOICE_INDIVIDUAL)) {
            ReportFileBuilder.generateDownloadInvoiceIndividual(messageData);
        }
        // Hotswitch Acq
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.MERCHANT_ACQUIRER_RULE_GROUP_DOWNLOAD)) {
            ReportFileBuilder.generateMerchantHistory(messageData);
        }
        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.ACQUIRER_RULE_GROUP_DOWNLOAD)) { // ACQUIRER_RULE_GROUP 
            ReportFileBuilder.generateAcquirerRuleGroupFile(messageData);
        }



        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.DOWNLOAD_MULTIPLE_FILES)) {
            ReportFileBuilder.generateDownloadMultipleDisputeFiles(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.CONTRACT_EXPORT_BY_TEMPLATE)) {
            ExportWordFileBuilder.generateContractFromTemplateDocx(messageData);
        }

        if (messageData.getRequestPath()
                .equals(Config.getUriPrefix() + RoutePool.REFERRAL_PARTNER_INSTALLMENT_FEE_EXPORT)) {
            ReportFileBuilder.generateReferralPartnerInstallmentFee(messageData);
        }

        if (messageData.getRequestPath().equals(Config.getUriPrefix() + RoutePool.QUERY_SQL_DOWNLOAD)) {
             ReportFileBuilder.generateCsvReportQuerySql(messageData);
        }
    }

    abstract void writeFile(vn.onepay.portal.queue.message.Message messageData) throws Exception;

    abstract String fileName(String ext);

    protected String fileNameHash;
    protected String fileName;

    private static final Logger LOGGER = Logger.getLogger(BaseDownloadQueueInListener.class.getName());
}
