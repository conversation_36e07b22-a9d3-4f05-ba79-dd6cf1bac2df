package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.dailyFeeReport;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.daily_fee_report.DailyFeeReportDao;
import vn.onepay.portal.resources.daily_fee_report.DailyFeeReportDto;
import vn.onepay.portal.utils.TemplateUtils;

public class DailyFeeReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        List<Map> listMapEcom = new ArrayList<>();
        long indexEcom = 1;
        List<Map> listMapPos = new ArrayList<>();
        long indexPos = 1;
        List<DailyFeeReportDto> listData = (List<DailyFeeReportDto>) (DailyFeeReportDao.list(mIn)).get("data");
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss dd/MM/yyyy");
        for (DailyFeeReportDto item: listData) {
            Map temp = new HashMap<>();
            temp.put(TemplateUtils.MFR_PARTNER_NAME, item.getPartnerName());
            temp.put(TemplateUtils.MFR_BUSINESS_NAME, item.getBusinessName());
            temp.put(TemplateUtils.MFR_MERCHANT_NAME, item.getMerchantName());
            // temp.put(TemplateUtils.MFR_SHORT_NAME, item.getPartnerName()); //partnerShortName
            // temp.put(TemplateUtils.MFR_MERCHANT_NAME, item.getMerchantName());
            temp.put(TemplateUtils.MFR_MERCHANT_IDS, item.getMerchantIds());
            temp.put(TemplateUtils.MFR_CONTRACT, item.getContractCode());
            temp.put(TemplateUtils.MFR_ADDENDUM, item.getAddendum());
            temp.put(TemplateUtils.MFR_TAX_CODE, item.getTaxCode());
            temp.put(TemplateUtils.MFR_ACCOUNT_NUMBERS, item.getAccountNumbers());
            temp.put(TemplateUtils.MFR_COUNT_SUCCESS, Long.parseLong(item.getCountSuccess()));
            temp.put(TemplateUtils.MFR_COUNT_FAILED, Double.parseDouble(item.getCountFailed()));
            temp.put(TemplateUtils.MFR_ORIGIN_AMOUNT_USD, Double.parseDouble(item.getOriginAmountUsd()));
            temp.put(TemplateUtils.MFR_ORIGIN_AMOUNT_VND, Double.parseDouble(item.getOriginAmountVnd()));
            temp.put(TemplateUtils.MFR_PARTNER_DISCOUNT_AMOUNT, Double.parseDouble(item.getPartnerDiscountAmount()));
            temp.put(TemplateUtils.MFR_MERCHANT_DISCOUNT_AMOUNT, Double.parseDouble(item.getMerchantDiscountAmount()));
            temp.put(TemplateUtils.MFR_TOTAL_VND, Double.parseDouble(item.getAmountTotalVnd()));
            temp.put(TemplateUtils.MFR_FEE_SUCCESS, Double.parseDouble(item.getFeeSuccess()));
            temp.put(TemplateUtils.MFR_FEE_FAILED, Double.parseDouble(item.getFeeFailed()));
            temp.put(TemplateUtils.MFR_FEE_ECOM, Double.parseDouble(item.getFeeEcom()));
            temp.put(TemplateUtils.MFR_FEE_ITA, Double.parseDouble(item.getFeeIta()));
            temp.put(TemplateUtils.MFR_DISCOUNT_FEE, Double.parseDouble(item.getDiscountFee()));
            temp.put(TemplateUtils.MFR_FEE_TOTAL, Double.parseDouble(item.getTotalFee()));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_COLLECTED, Double.parseDouble(item.getTotalFeeCollected()));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE, Double.parseDouble(item.getTotalFeeReceivable()));
            temp.put(TemplateUtils.MFR_ADVANCE_AMOUNT, Double.parseDouble(item.getAdvanceAmount()));

            //process period
            StringBuilder periodSummaryBuilder = new StringBuilder();
            String periodSummary = "";
            String fromDateQT = item.getFromDateQT() == null ? "" : sdf.format(item.getFromDateQT());
            String toDateQT = item.getToDateQT() == null ? "" : sdf.format(item.getToDateQT());
            String periodQT = "QT:"+fromDateQT+" - "+toDateQT;

            String fromDateND = item.getFromDateND() == null ? "" : sdf.format(item.getFromDateND());
            String toDateND = item.getToDateND() == null ? "" :sdf.format(item.getToDateND());
            String periodND = "ND:"+fromDateND+" - "+toDateND;

            String fromDateQR = item.getFromDateQR() == null ? "" : sdf.format(item.getFromDateQR());
            String toDateQR = item.getToDateQR() == null ? "" : sdf.format(item.getToDateQR());
            String periodQR = "QR:"+fromDateQR+" - "+toDateQR;
            
            String fromDateSMS = item.getFromDateSMS() == null ? "" : sdf.format(item.getFromDateSMS());
            String toDateSMS = item.getToDateSMS() == null ? "" : sdf.format(item.getToDateSMS());
            String periodSMS = "SMS:"+fromDateSMS+" - "+toDateSMS;  

            String fromDateBL = item.getFromDateBL() == null ? "" : sdf.format(item.getFromDateBL());
            String toDateBL = item.getToDateBL() == null ? "" : sdf.format(item.getToDateBL());
            String periodBL = "BL:"+fromDateBL+" - "+toDateBL; 

            String fromDateBNPL = item.getFromDateBNPL() == null ? "" : sdf.format(item.getFromDateBNPL());
            String toDateBNPL = item.getToDateBNPL() == null ? "" : sdf.format(item.getToDateBNPL());
            String periodBNPL = "BNPL:"+fromDateBNPL+" - "+toDateBNPL; 

            if (!(isEmptyString(fromDateQT) && isEmptyString(toDateQT))) {
                periodSummaryBuilder.append(periodQT).append(";");
            } 
            if (!(isEmptyString(fromDateND) && isEmptyString(toDateND))) {
                periodSummaryBuilder.append(periodND).append(";");
            }
            if (!(isEmptyString(fromDateQR) && isEmptyString(toDateQR))) {
                periodSummaryBuilder.append(periodQR).append(";");
            }
            if (!(isEmptyString(fromDateSMS) && isEmptyString(toDateSMS))) {
                periodSummaryBuilder.append(periodSMS).append(";");
            }
            if (!(isEmptyString(fromDateBL) && isEmptyString(toDateBL))) {
                periodSummaryBuilder.append(periodBL).append(";");
            }
            if (!(isEmptyString(fromDateBNPL) && isEmptyString(toDateBNPL))) {
                periodSummaryBuilder.append(periodBNPL).append(";");
            }
            periodSummary = periodSummaryBuilder.toString();
            String payChannels = item.getPayChannels();

            temp.put(TemplateUtils.MFR_PERIOD_SUMMARY, periodSummary);
            temp.put(TemplateUtils.MFR_PAY_CHANNELS, payChannels);
            temp.put(TemplateUtils.MFR_TYPE_ADVANCE, item.getTypeAdvance());
            temp.put(TemplateUtils.MFR_RECEIPT_TYPE, item.getReceiptType());
            temp.put(TemplateUtils.MFR_RECEIPT_STATE, item.getReceiptState());

            //process for ecom & pos
            if (payChannels != null) {
                if (payChannels.contains("UPOS")) {
                    temp.put(TemplateUtils.MFR_ROW_NUM, indexPos++);
                    listMapPos.add(temp);
                } else {
                    temp.put(TemplateUtils.MFR_ROW_NUM, indexEcom++);
                    listMapEcom.add(temp);
                }
            } else {
                temp.put(TemplateUtils.MFR_ROW_NUM, indexEcom++);
                listMapEcom.add(temp);
            }
        }
        Map<String, Object> mapData = new HashMap();
        mapData.put("listMapEcom", listMapEcom);
        mapData.put("listMapPos", listMapPos);
        listMap.add(mapData);

        return listMap;
    }

    public static boolean isEmptyString(String value) {
        if ("".equals(value) || value == null) {
            return true;
        }
        return false;
    }
    
}
