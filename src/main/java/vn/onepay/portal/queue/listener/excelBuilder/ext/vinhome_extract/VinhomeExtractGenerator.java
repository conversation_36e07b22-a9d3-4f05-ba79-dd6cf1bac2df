package vn.onepay.portal.queue.listener.excelBuilder.ext.vinhome_extract;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;
import io.vertx.core.json.JsonArray;

public class VinhomeExtractGenerator  {

    private static Logger logger = Logger.getLogger(VinhomeExtractGenerator.class.getName());


    public List<Map> generate(JsonArray extractedTransList) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
                List<Map<String, Object>> extractedTrans = new ArrayList<>(); // list giao dich da tach
                for (Object trans : extractedTransList) {
                    JsonObject transTemp = new JsonObject(trans.toString());
                    Map<String, Object> mBuf = null;
                    mBuf = new HashMap<>();
                    mBuf.put("S_MERCHANT_ID", transTemp.getString("mid"));
                    mBuf.put("S_TRANSACTION_ID", transTemp.getString("S_TRANSACTION_ID"));
                    mBuf.put("S_TRANSACTION_REF", transTemp.getString("S_TRANSACTION_REF"));
                    mBuf.put("S_ORDER_INFO", transTemp.getString("S_ORDER_INFO"));
                    mBuf.put("D_DATE_TRANSACTION", transTemp.getString("D_DATE_TRANSACTION"));
                    mBuf.put("S_CURRENCY", transTemp.getString("S_CURRENCY"));
                    mBuf.put("N_AMOUNT", transTemp.getDouble("N_AMOUNT"));
                    mBuf.put("N_AMOUNT_VND", transTemp.getDouble("N_AMOUNT_VND"));
                    mBuf.put("S_PAY_CHANNEL", transTemp.getString("S_PAY_CHANNEL"));
                    mBuf.put("S_RESPONSE_CODE", transTemp.getString("S_RESPONSE_CODE"));
                    mBuf.put("S_STATE", transTemp.getString("S_STATE"));
                    mBuf.put("S_ID_PARENT", transTemp.getString("S_ID_PARENT"));
                    mBuf.put("S_TRANSACTION_TYPE", transTemp.getString("S_TRANSACTION_TYPE"));
                    mBuf.put("S_AUTH_CODE", transTemp.getString("S_AUTH_CODE"));
                    mBuf.put("S_CARD_NO", transTemp.getString("S_CARD_NO"));
                    mBuf.put("S_BIN_COUNTRY", transTemp.getString("S_BIN_COUNTRY"));
                    mBuf.put("S_BIN_BANK", transTemp.getString("S_BIN_BANK"));
                    mBuf.put("S_CONTRACT_RELATION", transTemp.getString("S_CONTRACT_RELATION"));
                    mBuf.put("S_RRN", transTemp.getString("S_RRN"));
                    mBuf.put("S_CARD_TYPE", transTemp.getString("S_CARD_TYPE"));
                    mBuf.put("S_RETURN_URL", transTemp.getString("S_RETURN_URL"));
                    mBuf.put("S_BANK_ID", transTemp.getString("S_BANK_ID"));
                    mBuf.put("S_BANK_MERCHANT_ID", transTemp.getString("S_BANK_MERCHANT_ID"));
                    mBuf.put("S_ACQUIRER", transTemp.getString("S_ACQUIRER"));
                    mBuf.put("N_BANK_CONFIG_ID", transTemp.getLong("N_BANK_CONFIG_ID"));
                    mBuf.put("S_TRANS_CARD_TYPE", transTemp.getString("S_TRANS_CARD_TYPE"));
                    mBuf.put("S_TRANS_PAYGATE", transTemp.getString("S_TRANS_PAYGATE"));
                    extractedTrans.add(mBuf);
                }
                generateRs(extractedTrans, listMap);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Export Adjust Advance Approval Error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map<String, Object>> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (Map<String, Object> data : list) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("S_MERCHANT_ID", data.get("S_MERCHANT_ID"));
            item.put("S_TRANSACTION_ID", data.get("S_TRANSACTION_ID"));
            item.put("S_TRANSACTION_REF", data.get("S_TRANSACTION_REF"));
            item.put("S_ORDER_INFO", data.get("S_ORDER_INFO"));
            item.put("D_DATE_TRANSACTION", data.get("D_DATE_TRANSACTION"));
            item.put("S_CURRENCY", data.get("S_CURRENCY"));
            item.put("N_AMOUNT", data.get("N_AMOUNT"));
            item.put("N_AMOUNT_VND", data.get("N_AMOUNT_VND"));
            item.put("S_PAY_CHANNEL", data.get("S_PAY_CHANNEL"));
            item.put("S_RESPONSE_CODE", data.get("S_RESPONSE_CODE"));
            item.put("S_STATE", data.get("S_STATE"));
            item.put("S_ID_PARENT", data.get("S_ID_PARENT"));
            item.put("S_TRANSACTION_TYPE", data.get("S_TRANSACTION_TYPE"));
            item.put("S_AUTH_CODE", data.get("S_AUTH_CODE"));
            item.put("S_CARD_NO", data.get("S_CARD_NO"));
            item.put("S_BIN_COUNTRY", data.get("S_BIN_COUNTRY"));
            item.put("S_BIN_BANK", data.get("S_BIN_BANK"));
            item.put("S_CONTRACT_RELATION", data.get("S_CONTRACT_RELATION"));
            item.put("S_RRN", data.get("S_RRN"));
            item.put("S_CARD_TYPE", data.get("S_CARD_TYPE"));
            item.put("S_RETURN_URL", data.get("S_RETURN_URL"));
            item.put("S_BANK_ID", data.get("S_BANK_ID"));
            item.put("S_BANK_MERCHANT_ID", data.get("S_BANK_MERCHANT_ID"));
            item.put("S_ACQUIRER", data.get("S_ACQUIRER"));
            item.put("N_BANK_CONFIG_ID", data.get("N_BANK_CONFIG_ID"));
            item.put("S_TRANS_CARD_TYPE", data.get("S_TRANS_CARD_TYPE"));
            item.put("S_TRANS_PAYGATE", data.get("S_TRANS_PAYGATE"));
            item.put("S_VALID_AMOUNT", "MATCH");
            listData.add(item);
            
        }
    }
    
}
