package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.merchant_balance_enquiry.MerchantBalanceEnquiryDao;
import vn.onepay.portal.resources.payout.merchant_balance_enquiry.dto.MerchantBalanceDto;
import vn.onepay.portal.resources.payout.merchant_balance_enquiry.dto.MerchantBalanceQueryDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantBalanceEnquiryGenerator implements BaseGenerator<MerchantBalanceQueryDto> {

    @Override
    public List<Map> generate(MerchantBalanceQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<MerchantBalanceDto> list = MerchantBalanceEnquiryDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate MERCHANT BALANCE ENQUIRY error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<MerchantBalanceDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (MerchantBalanceDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchantId());
                item.put(TemplateUtils.MERCHANT_NAME_COLUMN, itemReport.getMerchantName());
                item.put(TemplateUtils.S_MERCHANT_ACCOUNT, itemReport.getMerchantAccount());
                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());
                item.put(TemplateUtils.ACCOUNT_STATE_COLUMN, itemReport.getAccountState());
                item.put(TemplateUtils.BALANCE_TEXT_COLUMN, "VND " + Util.formatCurrencyDouble(itemReport.getBalance()));
                item.put(TemplateUtils.DATE_COLUMN, itemReport.getDate());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantBalanceEnquiryGenerator.class.getName());
}
