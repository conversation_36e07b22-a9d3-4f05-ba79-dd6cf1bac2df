package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.payout.bank_transfer_report.BankTransferReportDao;
import vn.onepay.portal.resources.payout.bank_transfer_report.dto.BankTransferReportDto;
import vn.onepay.portal.resources.payout.bank_transfer_report.dto.BankTransferReportRes;
import vn.onepay.portal.utils.TemplateUtils;

public class BankTransferReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            BankTransferReportRes fundsTransDtos = BankTransferReportDao.searchBankTransferReport((Date)  mIn.get("from_date")
            , (Date) mIn.get("to_date")
            , mIn.get("time_interval").toString()
            , mIn.get("time_interval_convert").toString()
            , mIn.get("bank_sender").toString());
            this.generateRs(fundsTransDtos.getList(), listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<BankTransferReportDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (BankTransferReportDto data : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("S_DATE_TYPE", data.getDate_type());
                item.put("S_BANK_NAME", data.getBank_name());
                item.put("S_SWIFT_CODE", data.getSwift_code());
                item.put("N_OPEN_BALANCE", data.getBank_opening_balance());
                item.put("N_CLOSE_BALANCE", data.getBank_closing_balance());
                item.put("N_INCREASE_TOTAL", data.getTotal_increase_transaction());
                item.put("N_INCREASE_AMOUNT", data.getTotal_increase_amount());
                item.put("N_DECREASE_TOTAL", data.getTotal_decrease_transaction());
                item.put("N_DECREASE_AMOUNT", data.getTotal_decrease_amount());
                item.put("N_TOPUP_TOTAL", data.getTopup_number());
                item.put("N_TOPUP_AMOUNT", data.getTopup_amount());
                item.put("N_REVERSAL_TOTAL", data.getReversal_number());
                item.put("N_REVERSAL_AMOUNT", data.getReversal_amount());
                item.put("N_APPROVED_TOTAL", data.getApproved_number());
                item.put("N_APPROVED_AMOUNT", data.getApproved_amount());
                item.put("N_PENDING_TOTAL", data.getPending_number());
                item.put("N_PENDING_AMOUNT", data.getPending_amount());
                item.put("N_FAILED_TOTAL", data.getFailed_number());
                item.put("N_FAILED_AMOUNT", data.getFailed_amount());
                item.put("N_WAIT_REVERSAL_TOTAL", data.getWfr_number());
                item.put("N_WAIT_REVERSAL_AMOUNT", data.getWfr_amount());
                item.put("N_REVERTED_TOTAL", data.getReverted_number());
                item.put("N_REVERTED_AMOUNT", data.getReverted_amount());
                item.put("N_FEE_TOTAL", data.getFee_number());
                item.put("N_FEE_AMOUNT", data.getFee_amount());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(BankTransferReportGenerator.class.getName());
}
