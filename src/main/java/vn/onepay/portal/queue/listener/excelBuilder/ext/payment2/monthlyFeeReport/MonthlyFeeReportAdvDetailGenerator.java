package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDao;
import vn.onepay.portal.utils.TemplateUtils;

public class MonthlyFeeReportAdvDetailGenerator implements BaseGenerator<Map>, IConstants {

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        List<Map> listMapParent = new ArrayList<>();
        Integer rowNumParent = 0;
        List<Map> listMapChilds = new ArrayList<>();
        Integer rowNumChilds = 0;
        // list
        List<JsonObject> listData = MonthlyFeeReportDao.getAdvanceDetailMonthlyFeeReport(mIn);
        // report information
        Integer reportId = Integer.parseInt(mIn.get(MFR_ID_MONTHLY_REPORT).toString());
        JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);

        SimpleDateFormat sdfTransPeriods = new SimpleDateFormat("dd/MM/yyyy");
        String specialMerchantId = null;
        boolean isObjectFee = false;
        for (JsonObject item : listData) {
            Map temp = new HashMap<>();
            temp.put(TemplateUtils.MFR_PAYMENT_VOUCHER, item.getString("PAYMENT_VOUCHER"));
            temp.put(TemplateUtils.MFR_MERCHANT_ID, item.getString("MERCHANT_ID"));
            temp.put(TemplateUtils.MFR_ACQUIRER, item.getString("ACQUIRER"));
            temp.put(TemplateUtils.MFR_TRANS_ID, item.getString("TRANSACTION_ID"));
            temp.put(TemplateUtils.MFR_TRANS_DATE, item.getString("TRANSACTION_DATE"));
            temp.put(TemplateUtils.MFR_CARD_NO, item.getString("CARD_NO"));
            temp.put(TemplateUtils.MFR_CARD_TYPE, item.getString("CARD_TYPE"));
            temp.put(TemplateUtils.MFR_STATE, item.getString("STATE"));
            temp.put(TemplateUtils.MFR_ORDER_INFO, item.getString("ORDER_INFO"));
            temp.put(TemplateUtils.MFR_TRANS_REF, item.getString("TRANS_REF"));
            temp.put(TemplateUtils.MFR_AUTH_CODE, item.getString("AUTH_CODE"));
            temp.put(TemplateUtils.MFR_RRN, item.getString("RRN"));
            temp.put(TemplateUtils.MFR_TRANS_TYPE, item.getString("TRANSACTION_TYPE"));
            temp.put(TemplateUtils.MFR_CURRENCY, item.getString("CURRENCY"));
            temp.put(TemplateUtils.MFR_AMOUNT, item.getDouble("AMOUNT"));
            temp.put(TemplateUtils.MFR_RESPONSE_CODE, item.getString("RESPONSE_CODE"));
            temp.put(TemplateUtils.MFR_OPERATOR, item.getString("OPERATOR"));
            temp.put(TemplateUtils.MFR_BIN_COUNTRY, item.getString("BIN_COUNTRY"));
            temp.put(TemplateUtils.MFR_BIN_BANK, item.getString("BIN_BANK"));
            temp.put(TemplateUtils.MFR_ITA_BANK, item.getString("ITA_BANK"));
            temp.put(TemplateUtils.MFR_ITA_TERM, item.getString("ITA_TERM"));
            temp.put(TemplateUtils.MFR_PAY_CHANNEL, filterPayChannel(item.getString("PAY_CHANNEL")));
            temp.put(TemplateUtils.MFR_CARD_BIN, item.getString("CARD_BIN"));
            temp.put(TemplateUtils.MFR_EXCHANGE_RATE, item.getDouble("EXCHANGE_RATE"));
            temp.put(TemplateUtils.MFR_AMOUNT_AFTER, item.getDouble("AMOUNT_AFTER"));
            temp.put(TemplateUtils.MFR_FEE_FIX, item.getDouble("FEE_FIX"));
            temp.put(TemplateUtils.MFR_PERCENT_FEE, item.getDouble("PERCENT_FEE") / 100); // format percentage in excel
                                                                                          // auto multiple by 100
            temp.put(TemplateUtils.MFR_ITA_FEE, item.getDouble("ITA_FEE") / 100);
            temp.put(TemplateUtils.MFR_ECOM_FEE, item.getDouble("ECOM_FEE"));
            temp.put(TemplateUtils.MFR_TOTAL_FEE, item.getDouble("TOTAL_FEE"));
            temp.put(TemplateUtils.MFR_TOTAL_ITA_FEE, item.getDouble("TOTAL_ITA_FEE"));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_COLLECTED, item.getDouble("TOTAL_FEE_COLLECTED"));
            temp.put(TemplateUtils.MFR_TOTAL_FEE_RECEIVABLE, item.getDouble("TOTAL_FEE_RECEIVABLE"));
            temp.put(TemplateUtils.MFR_ADV_AMOUNT, item.getDouble("ADV_AMOUNT"));
            temp.put(TemplateUtils.MFR_ADV_AMOUNT_PART1, Double.parseDouble("0")); // TODO
            temp.put(TemplateUtils.MFR_ADV_AMOUNT_PART2, Double.parseDouble("0")); // TODO
            temp.put(TemplateUtils.MFR_DAY_CHECK, item.getString("D_CHECK"));
            temp.put(TemplateUtils.MFR_CMF_AMOUNT, item.getDouble("N_CMF_AMOUNT"));
            temp.put("N_PAYMENT_AMOUNT", item.getDouble("N_PAYMENT_AMOUNT"));
            temp.put("N_ORIGINAL_FEE", item.getDouble("N_ORIGINAL_FEE"));

            // process time by cutoff config
            Date fromDate = null, toDate = null;
            String receiptType = monthlyFeeReport.getString("receiptType");
            Long fromDateTime = item.getLong("D_ADVANCE_FROM");
            Long toDateTime = item.getLong("D_ADVANCE_TO");
            Long toDateTimeView = item.getLong("D_ADVANCE_TO_VIEW");
            if (receiptType.contains("by_advance_date")) {
                fromDate = new Date(fromDateTime);
                toDate = new Date(toDateTimeView);
            }
            if (receiptType.contains("by_transaction_date")) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(fromDateTime);
                int hoursFromDate = calendar.get(Calendar.HOUR_OF_DAY);
                // cutoff 24h
                if (hoursFromDate == 0) {
                    fromDate = new Date(fromDateTime);
                    toDate = new Date(toDateTimeView);
                }
                // cutoff 17h
                if (hoursFromDate == 17) {
                    long plusHours = 7 * 60 * 60 * 1000; // 7 hours
                    fromDate = new Date(fromDateTime + plusHours);
                    toDate = new Date(toDateTime);
                }
                // cutoff 8,14h
                if (hoursFromDate == 8 ||hoursFromDate == 14 ) {
                    fromDate = new Date(fromDateTime);
                    toDate = new Date(toDateTimeView);
                }

            }

            String transPeriods = sdfTransPeriods.format(fromDate) + " - " + sdfTransPeriods.format(toDate);
            // String transPeriods = sdfTransPeriods.format(mIn.get(MFR_FROM_DATE_TIME)) +
            // "-" + sdfTransPeriods.format(mIn.get(MFR_TO_DATE_TIME));
            temp.put(TemplateUtils.MFR_TRANS_PERIOD, transPeriods);

            String merchantId = item.getString("MERCHANT_ID");
            String specialMerchantIds = Config.getString("payment2.special_merchant_ids", "");
            if (specialMerchantIds.contains(merchantId)) {
                temp.put(TemplateUtils.MFR_ROW_NUM, ++rowNumParent);
                listMapParent.add(temp);
                specialMerchantId = merchantId;
            } else {
                temp.put(TemplateUtils.MFR_ROW_NUM, ++rowNumChilds);
                listMapChilds.add(temp);
            }

            JsonObject jData = new JsonObject(item.getString("S_DATA"));
            // PAYCOLLECT
            String virtualAccount = jData.getString("virtual_account");
            String virtualAccountName = jData.getString("virtual_account_name");
            String bankTransRef = jData.getString("bank_trans_ref");
            String bankId = jData.getString("bank_id");
            String remark = jData.getString("remark");
            temp.put("S_VIRTUAL_ACCOUNT", virtualAccount);
            temp.put("S_VIRTUAL_ACCOUNT_NAME", virtualAccountName);
            temp.put("S_BANK_TRANS_REF", bankTransRef);
            temp.put("S_BANK_ID", bankId);
            temp.put("S_REMARK", remark);

            //OBJECT FEE
            JsonObject customerFeeJ = jData.getJsonObject("customer_fee");
            if (customerFeeJ != null) {
                String objectFee = customerFeeJ.getString("object_fee");
                if ("CUSTOMER".equals(objectFee))
                    isObjectFee = true;
            }
        }
        Map<String, Object> listDataParent = new HashMap<>();
        listDataParent.put("listMapParent", listMapParent);
        Map<String, Object> listDataChilds = new HashMap<>();
        listDataChilds.put("listMapChilds", listMapChilds);
        Map<String, Object> externalData = new HashMap<>();
        externalData.put("specialMerchantId", specialMerchantId);
        externalData.put("isObjectFee", isObjectFee);
        listMap.add(listDataParent);
        listMap.add(listDataChilds);
        listMap.add(externalData);
        return listMap;
    }

    public static String filterPayChannel(String payChannel) {
        switch (payChannel) {
            case "PC":
                return "Paycollect";
            case "DD":
                return "Direct Debit";
            default:
                return payChannel;
        }
    }

}
