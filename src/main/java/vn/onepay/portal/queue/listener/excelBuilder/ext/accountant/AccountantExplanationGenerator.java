package vn.onepay.portal.queue.listener.excelBuilder.ext.accountant;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.accountantManagement.report.ReportDAO;
import vn.onepay.portal.resources.accountantManagement.report.dto.ExplanationDetailDTO;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.document_tracking.dao.DocumentTrackingDao;
import vn.onepay.portal.resources.document_tracking.dto.DocumentTrackingDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class AccountantExplanationGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ExplanationDetailDTO> baseListExplanation = ReportDAO.getStatementExplanation(mIn, "DOWNLOAD_EXPLANATION");
            if (baseListExplanation.get(0).getIdExplanation() != 0) {
                generateExplanation(baseListExplanation,listMap, mIn);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE STATEMENT EXPLANATION ERROR", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateExplanation(List<ExplanationDetailDTO> list, List<Map> listData, Map mIn) throws Exception {
        Map<Object, List<ExplanationDetailDTO>> explanationGroupedByBank = list.stream()
                .filter(event -> event.getBank() != null)
                .collect(Collectors.groupingBy(w -> w.getBank()));
        Map item = new HashMap();
        double total = (double) mIn.get("vcbTotal") + (double) mIn.get("vtbTotal") + (double) mIn.get("tcbTotal");

        List<Map> explanationConverted = new ArrayList<>();
        explanationGroupedByBank.forEach((keyReport, valueReport) -> {
            Map explanationConvertedMap = new HashMap();
            double amountTotal = 0;
            double amountTotalByBank = 0;
            String bank = "";
            List<Map> listExplanationByName = new ArrayList<>();
            for (int j = 0; j < valueReport.size(); j++) {
                Map m = new HashMap();
                bank = valueReport.get(j).getBank();
                amountTotal = amountTotal + valueReport.get(j).getAmount();
                if (listExplanationByName.size() == 0) {
                    m.put("amount", valueReport.get(j).getAmount());
                    m.put("reason", valueReport.get(j).getDesc());
                    m.put("count", valueReport.get(j).getCount());
                    listExplanationByName.add(m);
                } else if (valueReport.get(j).getAmount() != 0 || valueReport.get(j).getDesc() != null || valueReport.get(j).getCount() != 0) {
                    m.put("amount", valueReport.get(j).getAmount());
                    m.put("reason", valueReport.get(j).getDesc());
                    m.put("count", valueReport.get(j).getCount());
                    listExplanationByName.add(m);
                }
            }
            if (bank.equals("VCB")) {
                amountTotalByBank = (double) mIn.get("vcbTotal");
            } else if (bank.equals("VTB")) {
                amountTotalByBank = (double) mIn.get("vtbTotal");
            } else if (bank.equals("TCB")) {
                amountTotalByBank = (double) mIn.get("tcbTotal");
            }
            if (bank.equals("VCB") || bank.equals("VTB") || bank.equals("TCB")) {
                explanationConvertedMap.put("listExplanationByName", listExplanationByName);
                explanationConvertedMap.put("amountTotal", amountTotal);
                explanationConvertedMap.put("amountTotalByBank", amountTotalByBank);
                explanationConvertedMap.put("bank", bank);
                explanationConvertedMap.put("stt", explanationConverted.size() + 1);
                explanationConverted.add(explanationConvertedMap);
            }
        });

        item.put("explanationConverted", explanationConverted);
        item.put("total", total);
        listData.add(item);
    }

    private static String handleData(Timestamp inputData) {
        String outputData = "";
        if (inputData != null) {
            try {
                outputData = Util.formatDate(new Date(inputData.getTime()), "dd-MM-yyyy");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Convert Date error", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }    
        }
        return outputData;
    }
    
    private static final Logger LOGGER = Logger.getLogger(AccountantExplanationGenerator.class.getName());
}
