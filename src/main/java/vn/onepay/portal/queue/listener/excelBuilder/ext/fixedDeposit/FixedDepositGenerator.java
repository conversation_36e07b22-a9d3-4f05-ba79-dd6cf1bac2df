package vn.onepay.portal.queue.listener.excelBuilder.ext.fixedDeposit;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fixedDeposit.FixedDepositDao;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FixedDepositGenerator implements BaseGenerator<FixedDepositQueryDto> {

    @Override
    public List<Map> generate(FixedDepositQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<FixedDepositDto> list = FixedDepositDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FixedDepositDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        LocalDate openDate = null;
        LocalDate maturityDate = null;
        Date dateSign = null;
        Date dateOpen = null;
        Date dateMaturity = null;
        for (FixedDepositDto itemReport : list) {
            long days = 0;
            if (itemReport.getOpenDate() != null && !itemReport.getOpenDate().equals("") && itemReport.getMaturityDate() != null && !itemReport.getMaturityDate().equals("")) {
                openDate = new Timestamp(df.parse(itemReport.getOpenDate()).getTime()).toLocalDateTime().toLocalDate();
                maturityDate = new Timestamp(df.parse(itemReport.getMaturityDate()).getTime()).toLocalDateTime().toLocalDate();
                days = Duration.between(openDate.atStartOfDay(), maturityDate.atStartOfDay()).toDays();
                dateOpen = df.parse(itemReport.getOpenDate());
                dateMaturity = df.parse(itemReport.getMaturityDate());
            }
            if(itemReport.getSignedDate() != null && !"".equals(itemReport.getSignedDate())) 
            dateSign = df.parse(itemReport.getSignedDate());

            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("N_ID", itemReport.getId());
            item.put("S_CONTRACT_CODE", itemReport.getContractCode());
            item.put("S_FD", itemReport.getFd());
            item.put("S_BANK", itemReport.getBank());
            item.put("S_CURRENCY", itemReport.getCurrency());
            item.put("N_BALANCE", itemReport.getBalance());
            item.put("D_OPEN", dateOpen == null ? "" : dateOpen);
            item.put("D_SIGNED", dateSign == null ? "" : dateSign);
            item.put("D_MATURITY", dateMaturity == null ? "" : dateMaturity);
            item.put("N_MATURITY_PERIOD", itemReport.getMaturityPeriod());
            item.put("N_INTEREST_RATE", itemReport.getInterestRate());
            item.put("N_PARTNER_ID", itemReport.getPartnerId());
            item.put("S_BUSINESS_REG_NAME", itemReport.getBusinessRegName());
            item.put("S_MERCHANT_IDS", itemReport.getMerchantIds());
            item.put("S_MERCHANT_NAME", itemReport.getMerchantName());
            item.put("S_ADV_ACCOUNTS", itemReport.getAdvAccounts());
            item.put("N_BALANCE_AFTER_MATURITY", itemReport.getBalance() + itemReport.getBalance() * itemReport.getInterestRate() * days / (365 * 100));
            item.put("S_NOTE", itemReport.getNote());
            item.put("S_STATE", itemReport.getState());
            item.put("S_SOURCE", itemReport.getSource());
            item.put("S_ROLL_PRINCIPAL", convertRollPrincipal(itemReport.getRollPrincipal()));
            item.put("S_TAXNUMBER", itemReport.getTaxNumber());
            item.put("S_ACC_SAVING_NUM", itemReport.getAccSavingNum());
            item.put("S_ACC_SAVING_NAME", itemReport.getAccSavingName());
            item.put("S_ACC_SAVING_ADDRESS", itemReport.getAccSavingAddress());
            item.put("S_ACC_SAVING_BANK", itemReport.getAccSavingBank());
            item.put("S_ACC_SAVING_BRANCH", itemReport.getAccSavingBranch());
            item.put("S_ACC_REF_NUM", itemReport.getAccRefNum());
            item.put("S_ACC_REF_NAME", itemReport.getAccRefName());
            item.put("S_ACC_REF_ADDRESS", itemReport.getAccRefAddress());
            item.put("S_ACC_REF_BANK", itemReport.getAccRefBank());
            item.put("S_ACC_REF_BRANCH", itemReport.getAccRefBranch());
            item.put("N_ID_FD", itemReport.getIdFd());
            // put into list
            listData.add(item);
        }
    }

    private String convertRollPrincipal(String rollPrincipal) {
        if(rollPrincipal.equals("BOTH")){
            return "Quay vòng cả gốc và lãi";
        }else if(rollPrincipal.equals("PRINCIPAL")){
            return "Quay vòng gốc";
        }else if(rollPrincipal.equals("NONE")){
            return "Không quay vòng";
        }else{
            return "";
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FixedDepositGenerator.class.getName());
}
