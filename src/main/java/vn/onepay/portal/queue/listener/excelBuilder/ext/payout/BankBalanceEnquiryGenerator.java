package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.bank_balance_enquiry.BankBalanceEnquiryDao;
import vn.onepay.portal.resources.payout.bank_balance_enquiry.dto.BankBalanceDto;
import vn.onepay.portal.resources.payout.bank_balance_enquiry.dto.BankBalanceQueryDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BankBalanceEnquiryGenerator implements BaseGenerator<BankBalanceQueryDto> {

    @Override
    public List<Map> generate(BankBalanceQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<BankBalanceDto> list = BankBalanceEnquiryDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate BANK BALANCE ENQUIRY error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<BankBalanceDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (BankBalanceDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.BANK_NAME_COLUMN, itemReport.getBankName());
                item.put(TemplateUtils.BANK_ACCOUNT_COLUMN, itemReport.getBankAcc());
                item.put(TemplateUtils.SWIFT_CODE, itemReport.getSwiftCode());
                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());
                item.put(TemplateUtils.BALANCE_TEXT_COLUMN, "VND " + Util.formatCurrencyDouble(itemReport.getBalance()));
                item.put(TemplateUtils.DATE_COLUMN, itemReport.getDate());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(BankBalanceEnquiryGenerator.class.getName());
}
