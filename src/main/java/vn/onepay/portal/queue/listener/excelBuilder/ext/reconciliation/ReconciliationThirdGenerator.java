package vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation;

import com.onepay.commons.util.Convert;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.reconciliation.cdr.dto.ReconciliationLineDto;
import vn.onepay.portal.resources.reconciliation.cdr_third_source.CDRThirdSourceDAO;
import vn.onepay.portal.resources.reconciliation.cdr_third_source.dto.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ReconciliationThirdGenerator implements BaseGenerator<ReconQueryThirdDto> {

    @Override
    public List<Map> generate(ReconQueryThirdDto reconQueryThirdDto) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReconLineThirdDto> baseListExtend = CDRThirdSourceDAO.downloadListReconciliationLine(reconQueryThirdDto);
            this.generateRs(baseListExtend, listMap, reconQueryThirdDto);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation Third error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReconLineThirdDto> baseListExtend, List<Map> listData, ReconQueryThirdDto queryDto) throws Exception {
        int rowNumber = 0;
        for (ReconLineThirdDto itemReport : baseListExtend) {
            Map<String, Object> rowData = new HashMap<String, Object>();
            rowNumber++;

            rowData.put("index", rowNumber);

            String strTransStatus1 = itemReport.getS_trans_status_1();
            if (strTransStatus1 == null)
                strTransStatus1 = "";
            rowData.put("statusLeft", strTransStatus1);

            String sourceService1 = itemReport.getSource_service_1();
            if (sourceService1 == null)
                sourceService1 = "";
            rowData.put("sourceServiceLeft", sourceService1);

            String midPaycollect1 = itemReport.getMid_paycollect_1();
            if (midPaycollect1 == null)
                midPaycollect1 = "";
            rowData.put("midPaycollectLeft", midPaycollect1);

            String bbaLeft = itemReport.getBenefi_bank_acc_1();
            if (bbaLeft == null)
                bbaLeft = "";
            rowData.put("bbaLeft", bbaLeft);            

            String strTransType1 = itemReport.getS_trans_type_1();
            if (strTransType1 == null)
                strTransType1 = "";
            rowData.put("transTypeLeft", strTransType1);

            String strTransID1 = itemReport.getS_trans_id_1();
            if (strTransID1 == null)
                strTransID1 = "";
            rowData.put("transIDLeft", strTransID1);

            Date strD1 = itemReport.getD_trans_date_1();
            if (strD1 != null) {
                rowData.put("dateLeft", Convert.toString(strD1, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("dateLeft", strD1);
            }

            Date strUD1 = itemReport.getD_update_1();
            if (strUD1 != null) {
                rowData.put("updateDateLeft", Convert.toString(strUD1, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("updateDateLeft", strUD1);
            }

            String strAmount1 = itemReport.getN_amount_1();
            double dbAmount1 = 0;
            if (strAmount1 != null) {
                dbAmount1 = Double.parseDouble(strAmount1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("transAmountLeft", currencyCode1 + Convert.toString(dbAmount1, "###,##0.##"));
            } else {
                rowData.put("transAmountLeft", "");
            }

            String strTransInfo1 = itemReport.getS_trans_info_1();
            if (strTransInfo1 == null)
                strTransInfo1 = "";
            rowData.put("panLeft", strTransInfo1);
            rowData.put("bankIdLeft", itemReport.getS_bank_trans_id_1() != null ? itemReport.getS_bank_trans_id_1() : "");

            String cdrResult_ob = Util.nvl(itemReport.getS_result_ob_1(), "") + "|" + Util.nvl(itemReport.getS_result_ob_2(), "");
            String cdrResult_os = Util.nvl(itemReport.getS_result_os_1(), "") + "|" + Util.nvl(itemReport.getS_result_os_2(), "");
            String cdrResult_bs = Util.nvl(itemReport.getS_result_bs_1(), "") + "|" + Util.nvl(itemReport.getS_result_bs_2(), "");
            
            rowData.put("result_ob", cdrResult_ob);
            rowData.put("result_os", cdrResult_os);
            rowData.put("result_bs", cdrResult_bs);

            rowData.put("bankIdRight", itemReport.getS_bank_trans_id_2() != null ? itemReport.getS_bank_trans_id_2() : "");
            String strTransInfo2 = itemReport.getS_trans_info_2();
            if (strTransInfo2 == null)
                strTransInfo2 = "";
            rowData.put("panRight", strTransInfo2);

            String strAmount2 = itemReport.getN_amount_2();
            double dbAmount2 = 0;
            if (strAmount2 != null) {
                dbAmount2 = Double.parseDouble(strAmount2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("transAmountRight", currencyCode2 + Convert.toString(dbAmount2, "###,##0.##"));
            } else {
                strAmount2 = "";
                rowData.put("transAmountRight", strAmount2);
            }

            Date strD2 = itemReport.getD_trans_date_2();
            if (strD2 != null) {
                rowData.put("dateRight", Convert.toString(strD2, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("dateRight", strD2);
            }

            Date strUD2 = itemReport.getD_update_2();
            if (strUD2 != null) {
                rowData.put("updateDateRight", Convert.toString(strUD2, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("updateDateRight", strUD2);
            }

            String strTransID2 = itemReport.getS_trans_id_2();
            if (strTransID2 == null)
                strTransID2 = "";
            rowData.put("transIDRight", strTransID2);

            String strTransType2 = itemReport.getS_trans_type_2();
            if (strTransType2 == null)
                strTransType2 = "";
            rowData.put("transTypeRight", strTransType2);

            String strTransStatus2 = itemReport.getS_trans_status_2();
            if (strTransStatus2 == null)
                strTransStatus2 = "";
            rowData.put("statusRight", strTransStatus2);

            String sourceService2 = itemReport.getSource_service_2();
            if (sourceService2 == null)
                sourceService2 = "";
            rowData.put("sourceServiceRight", sourceService2);

            String midPaycollect2 = itemReport.getMid_paycollect_2();
            if (midPaycollect2 == null)
                midPaycollect2 = "";
            rowData.put("midPaycollectRight", midPaycollect2);

            String bbaRight = itemReport.getBenefi_bank_acc_2();
            if (bbaRight == null)
            bbaRight = "";
            rowData.put("bbaRight", bbaRight);

            if ("BIDV-PAYCOLLECT".equals(queryDto.getP_service())) {
                String line = itemReport.getP_line_1() == null ? itemReport.getP_line_2() : itemReport.getP_line_1();
                rowData.put("s_line", line == null? "" : line);
            }

            // Todo: thay doi file download cho Agribank Direct ST ( 01/07/2020)
            if ("AGRIBANK-DIRECT-ST".equals(queryDto.getP_service())) {
                String strReference1 = itemReport.getReference_1();
                if (strReference1 == null)
                    strReference1 = "";
                rowData.put("referenceLeft", strReference1);
                String strReference2 = itemReport.getReference_2();
                if (strReference2 == null)
                    strReference2 = "";
                rowData.put("referenceRight", strReference2);
                String strMaskCard1 = itemReport.getMaskCard_1();
                if (strMaskCard1 == null)
                    strMaskCard1 = "";
                rowData.put("maskCardLeft", strMaskCard1);
                String strMaskCard2 = itemReport.getMaskCard_2();
                if (strMaskCard2 == null)
                    strMaskCard2 = "";
                rowData.put("maskCardRight", strMaskCard2);
            }
            // Todo: thay doi file download cho Cup All ( 29/09/2021)
            if ("CUP-ALL".equals(queryDto.getP_service())) {
                String strSettlemenAmount1 = itemReport.getS_settlement_amount1();
                double dbSettlemenAmount1 = 0;
                if (strSettlemenAmount1 != null) {
                    dbSettlemenAmount1 = Double.parseDouble(strSettlemenAmount1);
                    String currencyCode2 = itemReport.getS_settlement_currency1();
                    if (currencyCode2 != null) {
                        if ("USD".equalsIgnoreCase(currencyCode2)) {
                            double dbSetAmount = (dbSettlemenAmount1 / 100);
                            rowData.put("settlementAmountLeft", currencyCode2 + " " + Convert.toString(dbSetAmount, "###,##0.##"));
                        } else {
                            rowData.put("settlementAmountLeft", currencyCode2 + " " + Convert.toString(dbSettlemenAmount1, "###,##0.##"));
                        }
                    } else {
                        rowData.put("settlementAmountLeft", Convert.toString(dbSettlemenAmount1, "###,##0.##"));
                    }

                } else {
                    strSettlemenAmount1 = "";
                    rowData.put("settlementAmountLeft", strSettlemenAmount1);
                }

                String strCardNo1 = itemReport.getS_card_no1();
                if (strCardNo1 == null)
                    strCardNo1 = "";
                rowData.put("cardNoLeft", strCardNo1);
                String strMerchantName1 = itemReport.getS_merchant_name_1();
                if (strMerchantName1 == null)
                    strMerchantName1 = "";
                rowData.put("merchantNameLeft", strMerchantName1);
                String strMerchantNo1 = itemReport.getS_merchant_no_1();
                if (strMerchantNo1 == null)
                    strMerchantNo1 = "";
                rowData.put("merchantNoLeft", strMerchantNo1);
                String strMcc1 = itemReport.getS_mcc_1();
                if (strMcc1 == null)
                    strMcc1 = "";
                rowData.put("mccLeft", strMcc1);
                String service_download = itemReport.getService_download();
                if (service_download == null)
                    service_download = "";
                rowData.put("serviceDownload", service_download);
            }

            listData.add(rowData);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ReconciliationThirdGenerator.class.getName());
}
