package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.FixDepositPaymentDao;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.dto.FDDetailPaymentDto;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FDPaymentTransactionDetailGenerator implements BaseGenerator<Long> {
    private static final Logger LOGGER = Logger.getLogger(FDPaymentTransactionDetailGenerator.class.getName());
    @Override
    public List<Map> generate(Long id) {
        List<Map> listMap = new ArrayList<>();
        try {
            listMap = FixDepositPaymentDao.fixedDepositAdvTransDetail(0, Integer.MAX_VALUE, id);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit payment Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }


}
