package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.guaranteeManage.GuaranteeManageDao;
import vn.onepay.portal.resources.payment2.guaranteeManage.dto.GuaranteeManageDto;
import vn.onepay.portal.resources.payment2.guaranteeManage.dto.GuaranteeManageQueryDto;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class GuaranteeGenerator implements BaseGenerator<GuaranteeManageQueryDto> {

    @Override
    public List<Map> generate(GuaranteeManageQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<GuaranteeManageDto> list = GuaranteeManageDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    public List<Map> generateSelected(GuaranteeManageQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<GuaranteeManageDto> list = GuaranteeManageDao.getByids(query).getList();
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    /**
     * Convert dữ liệu từ database, format lại để hiển thị trên file excel download guarantee management.
     * 
     * @param list
     *            List of GuaranteeManageDto
     * @param listData
     *            List of Map for export
     * @throws Exception
     */
    private void generateRs(List<GuaranteeManageDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        for (GuaranteeManageDto itemReport : list) {
            long days = 0;
            Date dateRefund = null;
            Date dateOriginalOpen = null;
            LocalDate openDate = null;
            LocalDate maturityDate = null;
            Date dateSign = null;
            Date dateOpen = null;
            Date dateMaturity = null;
            if (itemReport.getOpen_date() != null && !itemReport.getOpen_date().equals("") && itemReport.getMaturity_date() != null && !itemReport.getMaturity_date().equals("")) {
                openDate = new Timestamp(df.parse(itemReport.getOpen_date()).getTime()).toLocalDateTime().toLocalDate();
                maturityDate = new Timestamp(df.parse(itemReport.getMaturity_date()).getTime()).toLocalDateTime().toLocalDate();
                days = Duration.between(openDate.atStartOfDay(), maturityDate.atStartOfDay()).toDays();
                dateOpen = df.parse(itemReport.getOpen_date());
                dateMaturity = df.parse(itemReport.getMaturity_date());
            }
            if (itemReport.getSigned_date() != null && !"".equals(itemReport.getSigned_date()))
                dateSign = df.parse(itemReport.getSigned_date());
            if (itemReport.getRefundDate() != null && !"".equals(itemReport.getRefundDate())) {
                dateRefund = df.parse(itemReport.getRefundDate());
            }
            // Format trường ngày mở khoản đảm bảo ban đầu theo định dạng dd/MM/yyyy trong trường hợp trường này có giá trịtrị
            if (itemReport.getOriginalOpenDate() != null && !"".equals(itemReport.getOriginalOpenDate())) {
                dateOriginalOpen = df.parse(itemReport.getOriginalOpenDate());
            }

            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("N_ID", itemReport.getId());
            item.put("S_CONTRACT_CODE", itemReport.getContract_code());
            item.put("S_FD", itemReport.getFd());
            item.put("S_BANK", itemReport.getBank());
            item.put("S_CURRENCY", itemReport.getCurrency());
            item.put("N_BALANCE", itemReport.getBalance());
            item.put("D_OPEN", dateOpen == null ? "" : dateOpen);
            item.put("D_SIGNED", dateSign == null ? "" : dateSign);
            item.put("D_MATURITY", dateMaturity == null ? "" : dateMaturity);
            item.put("N_MATURITY_PERIOD", itemReport.getMaturity_period());
            item.put("N_INTEREST_RATE", itemReport.getInterest_rate());
            item.put("N_PARTNER_ID", itemReport.getPartner_id());
            item.put("S_BUSINESS_REG_NAME", itemReport.getBusiness_reg_name());
            item.put("S_MERCHANT_IDS", itemReport.getMerchant_ids());
            item.put("S_MERCHANT_NAME", itemReport.getMerchant_name());
            item.put("S_ADV_ACCOUNTS", itemReport.getAdv_accounts());
            item.put("N_BALANCE_AFTER_MATURITY", itemReport.getBalance() + itemReport.getBalance() * itemReport.getInterest_rate() * days / (365 * 100));
            item.put("S_NOTE", itemReport.getNote());
            item.put("S_STATE", itemReport.getState());
            item.put("S_SOURCE", itemReport.getSource());
            item.put("S_ROLL_PRINCIPAL", convertRollPrincipal(itemReport.getRoll_principal()));
            item.put("S_TAXNUMBER", itemReport.getTax_number());
            item.put("S_ACC_SAVING_NUM", itemReport.getAcc_saving_num());
            item.put("S_ACC_SAVING_NAME", itemReport.getAcc_saving_name());
            item.put("S_ACC_SAVING_ADDRESS", itemReport.getAcc_saving_address());
            item.put("S_ACC_SAVING_BANK", itemReport.getAcc_saving_bank());
            item.put("S_ACC_SAVING_BRANCH", itemReport.getAcc_saving_branch());
            item.put("S_ACC_REF_NUM", itemReport.getAcc_ref_num());
            item.put("S_ACC_REF_NAME", itemReport.getAcc_ref_name());
            item.put("S_ACC_REF_ADDRESS", itemReport.getAcc_ref_address());
            item.put("S_ACC_REF_BANK", itemReport.getAcc_ref_bank());
            item.put("S_ACC_REF_BRANCH", itemReport.getAcc_ref_branch());
            item.put("N_ID_FD", itemReport.getId_fd());
            item.put("N_REFUND_AMOUNT", itemReport.getRefundAmount().doubleValue());
            item.put("D_REFUND_DATE", itemReport.getRefundDate() == null ? "" : dateRefund);
            item.put("N_ORIGINAL_AMOUNT", itemReport.getOriginalAmount().doubleValue());
            item.put("D_ORIGINAL_OPEN_DATE", itemReport.getOriginalOpenDate() == null ? "" : dateOriginalOpen);
            // put into list
            listData.add(item);
        }
    }

    private String convertRollPrincipal(String rollPrincipal) {
        if (rollPrincipal.equals("BOTH")) {
            return "Quay vòng cả gốc và lãi";
        } else if (rollPrincipal.equals("PRINCIPAL")) {
            return "Quay vòng gốc";
        } else if (rollPrincipal.equals("NONE")) {
            return "Không quay vòng";
        } else {
            return "";
        }
    }

    private static final Logger LOGGER = Logger.getLogger(GuaranteeGenerator.class.getName());
}
