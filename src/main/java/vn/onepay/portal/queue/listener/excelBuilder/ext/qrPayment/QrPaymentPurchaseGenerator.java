package vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.qr.purchase.QrPurchaseDao;
import vn.onepay.portal.resources.qr.purchase.dto.QrPurchase;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class QrPaymentPurchaseGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {

            BaseList<QrPurchase> qrPurchase = QrPurchaseDao.search(mIn);
            generateRs(qrPurchase.getList(),listMap);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment Purchase error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<QrPurchase> list, List<Map> listData ) throws Exception {

        int index = 0;
        for(QrPurchase dto: list)
        {
            Map item = new HashMap();
            index++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, index);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, dto.getMerchantId());

            // merchant name
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, dto.getMerchantName());

            // onecomMerchantId
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, dto.getOnecomMerchantId());

            // bankTransId
            item.put(TemplateUtils.BANK_TRANS_ID, dto.getBankTransId());

            // merchantTransId
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, dto.getTransactionId());

            // Merchant Trans ID
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, dto.getCustomerTransId());

            // QR ID
            item.put("S_QR_ID", dto.getQrId());

            // Trans ID
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, dto.getMerchantTxnRef());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, dto.getOrderInfo());

            // Acq Code
            item.put(TemplateUtils.ACQ_CODE_COLUMN, dto.getAcqCode());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, dto.getInstrument().getType());

            // Channel
            item.put(TemplateUtils.CHANNEL_COLUMN, dto.getChannel());

            // App NAme
            item.put("S_APP_NAME", dto.getAppName());

            // Masking
            item.put("S_MASKING", dto.getMasking());

            // Date
            Date transactionTime = dto.getCreateTime();
            item.put("S_PURCHASE_DATE", transactionTime);

            // Card Number
            item.put(TemplateUtils.CARD_NUMBER_COLUMN, dto.getInstrument().getNumber());

            item.put(TemplateUtils.CURRENCY,dto.getCurrency());

            // Original Amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, dto.getOriginalAmount());

            // Card Name
            item.put(TemplateUtils.CARD_NAME_COLUMN, dto.getInstrument().getName());

            // Brand Id
            item.put(TemplateUtils.BRAND_ID_COLUMN, dto.getInstrument().getBrandId() == "" ? "" : dto.getInstrument().getBrandId().toUpperCase());
            
            // Status
            item.put(TemplateUtils.STATUS_COLUMN, dto.getStatus());

            // Bank Terminal Id
            item.put(TemplateUtils.TID_COLUMN, dto.getBankTerminalId());

            // Bank Merchant Id
            item.put(TemplateUtils.MID_COLUMN, dto.getBankMerchantId());

            // Platform
            item.put(TemplateUtils.PLATFORM_COLUMN, dto.getPlatform());

            // Merchant Discount
            item.put("N_MERCHANT_DISCOUNT", dto.getMerchantDiscountAmount());

            // Partner Discount
            item.put("N_PARTNER_DISCOUNT", dto.getPartnerDiscountAmount());

            // Payment Amount
            item.put("N_PAYMENT_AMOUNT", dto.getPaymentAmount());

            // Qr Type
            item.put("S_QR_TYPE", "static".equalsIgnoreCase(dto.getQrType()) ? "Static" : "Dynamic");
            // Merchant Channel
            item.put("S_MERCHANT_CHANNEL", "upos".equalsIgnoreCase(dto.getMerchantChannel())  ? "Upos" : "Ecom");

            // Source
            item.put(TemplateUtils.S_SOURCE, dto.getSource());

            // put into list
            listData.add(item);
        }

    }
    private static final Logger LOGGER = Logger.getLogger(QrPaymentPurchaseGenerator.class.getName());
}
