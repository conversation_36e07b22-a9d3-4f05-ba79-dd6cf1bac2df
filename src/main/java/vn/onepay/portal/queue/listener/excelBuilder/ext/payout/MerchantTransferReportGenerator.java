package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.merchant_config.PayoutMerchantConfigDao;
import vn.onepay.portal.resources.payout.merchant_config.dto.PartnerDto;
import vn.onepay.portal.resources.payout.merchant_transfer_report.MerchantTransferReportDao;
import vn.onepay.portal.resources.payout.merchant_transfer_report.dto.MerchantTransferReportDto;
import vn.onepay.portal.resources.payout.merchant_transfer_report.dto.MerchantTransferReportRes;
import vn.onepay.portal.utils.TemplateUtils;

public class MerchantTransferReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            MerchantTransferReportRes fundsTransDtos = MerchantTransferReportDao.searchMerchantTransferReport(mIn);

            List<String> merchantIds = fundsTransDtos.getList()
                                .stream()
                                .map(MerchantTransferReportDto::getMerchant_id)
                                .distinct()
                                .collect(Collectors.toList());
            //get partner by merchant id
            Map<String, PartnerDto> pMap = PayoutMerchantConfigDao.listPartnerByMerchant(String.join( ",", merchantIds))
                                .stream()
                                .collect(Collectors.toMap(PartnerDto::getMerchantId, Function.identity()));
            fundsTransDtos.getList().forEach(a -> a.setPartner_name(pMap.get(a.getMerchant_id()).getShotName()));


            this.generateRs(fundsTransDtos.getList(), listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Tranfer Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<MerchantTransferReportDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (MerchantTransferReportDto data : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("S_DATE_TYPE", data.getDate_type());
                item.put("S_PARTNER_NAME", data.getPartner_name());
                item.put("S_MERCHANT_ID", data.getMerchant_id());
                item.put("S_MERCHANT_NAME", data.getMerchant_name());
                item.put("S_MERCHANT_ACCOUNT", data.getMerchant_account());
                item.put("N_OPEN_BALANCE", data.getMerchant_opening_balance());
                item.put("N_CLOSE_BALANCE", data.getMerchant_closing_balance());
                item.put("N_INCREASE_TOTAL", data.getTotal_increase_transaction());
                item.put("N_INCREASE_AMOUNT", data.getTotal_increase_amount());
                item.put("N_DECREASE_TOTAL", data.getTotal_decrease_transaction());
                item.put("N_DECREASE_AMOUNT", data.getTotal_decrease_amount());
                item.put("N_TOPUP_TOTAL", data.getTopup_number());
                item.put("N_TOPUP_AMOUNT", data.getTopup_amount());
                item.put("N_REVERSAL_TOTAL", data.getReversal_number());
                item.put("N_REVERSAL_AMOUNT", data.getReversal_amount());
                item.put("N_APPROVED_TOTAL", data.getApproved_number());
                item.put("N_APPROVED_AMOUNT", data.getApproved_amount());
                item.put("N_PENDING_TOTAL", data.getPending_number());
                item.put("N_PENDING_AMOUNT", data.getPending_amount());
                item.put("N_FAILED_TOTAL", data.getFailed_number());
                item.put("N_FAILED_AMOUNT", data.getFailed_amount());
                item.put("N_WAIT_REVERSAL_TOTAL", data.getWfr_number());
                item.put("N_WAIT_REVERSAL_AMOUNT", data.getWfr_amount());
                item.put("N_REVERTED_TOTAL", data.getReverted_number());
                item.put("N_REVERTED_AMOUNT", data.getReverted_amount());
                item.put("N_FEE_TOTAL", data.getFee_number());
                item.put("N_FEE_AMOUNT", data.getFee_amount());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantTransferReportGenerator.class.getName());
}
