package vn.onepay.portal.queue.listener.excelBuilder.ext.International.report;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.report.InterReportDto;
import vn.onepay.portal.resources.international.report.InterReportResponse;
import vn.onepay.portal.resources.international.report.TransactionReportDao;
import vn.onepay.portal.utils.OneCreditUtil;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InternationalReportGenerator implements BaseGenerator<Map<String, String>>, IConstants {
    private static final Logger LOGGER = Logger.getLogger(InternationalReportGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            InterReportResponse data = TransactionReportDao.list(mIn);
            generateRs(listMap, data);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[INTER REPORT] ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, InterReportResponse dataResponse) throws Exception {
        int count = 1;
        Map item;
        for (InterReportDto data : dataResponse.getData()) {
            item = new HashMap();
            item.put("NO", count);
            count++;
            item.put("S_DATE", data.getDateType());
            item.put("S_CONTRACT_TYPE", data.getContractType());

            item.put("N_ACQUIRER", data.getAcquirerName());
            item.put("S_BANK_MERCHANT_ID", data.getBankMerchantId());

            item.put("S_MERCHANT_ID", data.getMerchantId());

            item.put("S_MERCHANT_NAME", data.getMerchantName());
            // Partner Id
            item.put("N_PARTNER_ID", data.getPartnerId());

            item.put("S_BIN_COUNTRY", data.getBinCountry());

            item.put("S_CARD_TYPE", data.getCardType());
            item.put("S_ITA_TIME", data.getItaTime());
            item.put("S_ITA_BANK", data.getItaBank());
            item.put("S_CURRENCY", data.getCurrency());
            item.put("S_TRANSACTION_TYPE", data.getTransType());
            // todo
            item.put("S_RESPONSE_CODE", data.getResponseCode());
            // Order Reference
            item.put("totalTxn", data.getTotalTxn());
            // Merchant Transaction Reference
            item.put("totalTxnAmount", data.getTotalTxnAmount());
            // Org Ref
            item.put("totalItaApprovalAmount", data.getTotalItaApprovalAmount());
            // Authorization Code
            item.put("S_TYPE_ADVANCE", data.getAdvType());
            // Reference
            item.put("S_PLATFORM", data.getPlatForm());
            // Card Number
            item.put("S_SOURCE", data.getSource());

            // put into list
            list.add(item);
        }
        for (Map.Entry<String, Double> entry : dataResponse.getTotalAmountByCurrency().entrySet()) {
            item = new HashMap();
            String currency = entry.getKey();
            Double total = entry.getValue();
            item.put("NO", "");
            item.put("S_DATE", "");
            item.put("S_CONTRACT_TYPE", "");

            item.put("N_ACQUIRER", "");
            item.put("S_BANK_MERCHANT_ID", "");

            item.put("S_MERCHANT_ID", "");

            item.put("S_MERCHANT_NAME", "");
            item.put("N_PARTNER_ID", "");

            item.put("S_BIN_COUNTRY", "");

            item.put("S_CARD_TYPE", "");
            item.put("S_ITA_TIME", "");
            item.put("S_ITA_BANK", "");
            item.put("S_CURRENCY",  "TOTAL " + entry.getKey());
            item.put("S_TRANSACTION_TYPE", "");
            item.put("S_RESPONSE_CODE", "");
            item.put("totalTxn", dataResponse.getTotalByCurrency().get(entry.getKey()));
            item.put("totalTxnAmount", total);
            item.put("totalItaApprovalAmount", dataResponse.getTotalApproAmountByCurrency().get(entry.getKey()));
            item.put("S_TYPE_ADVANCE", "");
            item.put("S_PLATFORM", "");
            item.put("S_SOURCE", "");
            list.add(item);
        }

    }

    private String formatNumber(Double amount, String currency){
        DecimalFormat formatter = new DecimalFormat("###,###,###");
        String[] stringArray = {"USD", "SGD", "MYR", "TWD", "CNY", "THB"};
        List<String> stringList = Arrays.asList(stringArray);
        if(stringList.contains(currency)){
            formatter = new DecimalFormat("###,###,###0.00");
        }
        return formatter.format(amount);
    }
}
