package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.account_fee.AccountFeeDao;
import vn.onepay.portal.resources.payout.account_fee.dto.AccountFeeDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantFeeGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<AccountFeeDto> merchantFeeDtos = AccountFeeDao.search(mIn).getList();
            this.generateRs(merchantFeeDtos, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Bank Fee error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<AccountFeeDto> list, List<Map> listData) {
        int rowNumber = listData.size();
        try {
            for (AccountFeeDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put("partner_name", itemReport.getPartnerName());
                item.put("merchant_acc_name", itemReport.getMerchantName());
                item.put("merchant_account", itemReport.getMerchantAccount());
                item.put("merchant_id", itemReport.getMerchantId());
                item.put("trans_id", itemReport.getId());
                item.put("type", itemReport.getType().substring(0, 1).toUpperCase() + itemReport.getType().substring(1));
                item.put("opening_balance", ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceBefore()));
                item.put("amount", formatCurrencyDouble(itemReport.getAmount()));
                item.put("currency", "VND");
                item.put("closing_balance", ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceAfter()));
                item.put("state", itemReport.getState().substring(0, 1).toUpperCase() + itemReport.getState().substring(1));
                item.put("desc", itemReport.getDescription());
                item.put(TemplateUtils.CREATE, itemReport.getCreatedDate() == null ? "" : formatDate(new Date(itemReport.getCreatedDate().getTime())));
                item.put(TemplateUtils.UPDATE, itemReport.getUpdatedDate() == null ? "" : formatDate(new Date(itemReport.getUpdatedDate().getTime())));
                item.put("D_FUND", itemReport.getFundDate() == null ? "" : formatDate(new Date(itemReport.getFundDate().getTime())));
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
    }

    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("###,###,###");
        return df.format(value);
    }

    public static String formatDate(Date dates) throws ParseException {
        String myDate = null;
        if (!dates.equals("")) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy hh:mm a");
            try {
                myDate = sdf.format(dates);
            } catch (Exception ex) {
                throw ex;
            }
        } else {
            myDate = "";
        }
        return myDate;
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantFeeGenerator.class.getName());
}
