package vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation;

import com.onepay.commons.util.Convert;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.reconciliation.cdr.dto.ReconciliationQueryDto;
import vn.onepay.portal.resources.reconciliation.cdr_bnpl.CDRBNPLDAO;
import vn.onepay.portal.resources.reconciliation.cdr_bnpl.dto.ReconciliationBNPLLineDto;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ReconciliationBNPLGenerator implements BaseGenerator<ReconciliationQueryDto> {

    @Override
    public List<Map> generate(ReconciliationQueryDto reconciliationQueryDto) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReconciliationBNPLLineDto> baseListExtend = CDRBNPLDAO.downloadListReconciliationLine(reconciliationQueryDto);
            this.generateRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReconciliationBNPLLineDto> baseListExtend, List<Map> listData) throws Exception {
        int rowNumber = 0;
        for (ReconciliationBNPLLineDto itemReport : baseListExtend) {
            Map rowData = new HashMap();
            rowNumber++;

            rowData.put("index", rowNumber);

            String strTransStatus1 = itemReport.getS_trans_status_1();
            if (strTransStatus1 == null)
                strTransStatus1 = "";
            rowData.put("statusLeft", strTransStatus1);

            String strTransType1 = itemReport.getS_trans_type_1();
            if (strTransType1 == null)
                strTransType1 = "";
            rowData.put("transTypeLeft", strTransType1);

            String strTransID1 = itemReport.getS_trans_id_1();
            if (strTransID1 == null)
                strTransID1 = "";
            rowData.put("transIDLeft", strTransID1);

            Date strD1 = itemReport.getD_trans_date_1();
            if (strD1 != null) {
                rowData.put("dateLeft", Convert.toString(strD1, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("dateLeft", strD1);
            }

            String strTransInfo1 = itemReport.getS_trans_info_1();
            if (strTransInfo1 == null)
                strTransInfo1 = "";
            rowData.put("providerTransLeft", strTransInfo1);

            String strOrgAmount1 = itemReport.getOriginalAmountLeft();
            double dbOrgAmount1 = 0;
            if (strOrgAmount1 != null) {
                dbOrgAmount1 = Double.parseDouble(strOrgAmount1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("originalAmountLeft", currencyCode1 + Convert.toString(dbOrgAmount1, "###,##0.##"));
            } else {
                rowData.put("originalAmountLeft", "");
            }

            String strFirstPayment1 = itemReport.getFirstPaymentLeft();
            double dbFirstPayment1 = 0;
            if (strFirstPayment1 != null) {
                dbFirstPayment1 = Double.parseDouble(strFirstPayment1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("firstPaymentLeft", currencyCode1 + Convert.toString(dbFirstPayment1, "###,##0.##"));
            } else {
                rowData.put("firstPaymentLeft", "");
            }

            String strPayLater1 = itemReport.getPayLaterLeft();
            double dbPayLater1 = 0;
            if (strPayLater1 != null) {
                dbPayLater1 = Double.parseDouble(strPayLater1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("payLaterLeft", currencyCode1 + Convert.toString(dbPayLater1, "###,##0.##"));
            } else {
                rowData.put("payLaterLeft", "");
            }

            String strDisbursedAmount1 = itemReport.getDisbursedAmountLeft();
            double dbDisbursedAmount1 = 0;
            if (strDisbursedAmount1 != null) {
                dbDisbursedAmount1 = Double.parseDouble(strDisbursedAmount1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("disbursedLeft", currencyCode1 + Convert.toString(dbDisbursedAmount1, "###,##0.##"));
            } else {
                rowData.put("disbursedLeft", "");
            }

            String strRefundAmount1 = itemReport.getRefundAmountLeft();
            double dbRefundAmount1 = 0;
            if (strRefundAmount1 != null) {
                dbRefundAmount1 = Double.parseDouble(strRefundAmount1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("refundLeft", currencyCode1 + Convert.toString(dbRefundAmount1, "###,##0.##"));
            } else {
                rowData.put("refundLeft", "");
            }

            String strProviderMerchFee1 = itemReport.getProviderMerchantFeeLeft();
            double dbProviderMerchFee1 = 0;
            if (strProviderMerchFee1 != null) {
                dbProviderMerchFee1 = Double.parseDouble(strProviderMerchFee1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("providerMerchFeeLeft", currencyCode1 + Convert.toString(dbProviderMerchFee1, "###,##0.##"));
            } else {
                rowData.put("providerMerchFeeLeft", "");
            }

            String strCustomerName1 = itemReport.getCustomerNameLeft();
            if (strCustomerName1 == null)
                strCustomerName1 = "";
            rowData.put("customerNameLeft", strCustomerName1);

            String strCustomerMobile1 = itemReport.getCustomerMobileLeft();
            if (strCustomerMobile1 == null)
                strCustomerMobile1 = "";
            rowData.put("customerPhoneLeft", strCustomerMobile1);

            String strModel1 = itemReport.getModelLeft();
            if (strModel1 == null)
                strModel1 = "";
            rowData.put("modelLeft", strModel1);

            String cdrResult1 = itemReport.getS_result_code_1();
            String cdrResult2 = itemReport.getS_result_code_2();
            String cdrResult = "";
            if (cdrResult1 != null) {
                cdrResult = cdrResult1;
            }
            cdrResult = cdrResult + "|";
            if (cdrResult2 != null) {
                cdrResult = cdrResult + cdrResult2;
            }
            rowData.put("result", cdrResult);

            String strTransInfo2 = itemReport.getS_trans_info_2();
            if (strTransInfo2 == null)
                strTransInfo2 = "";
            rowData.put("providerTransRight", strTransInfo2);

            Date strD2 = itemReport.getD_trans_date_2();
            if (strD2 != null) {
                rowData.put("dateRight", Convert.toString(strD2, "dd/MM/yyyy HH:mm:ss"));
            } else {
                rowData.put("dateRight", strD2);
            }

            String strTransID2 = itemReport.getS_trans_id_2();
            if (strTransID2 == null)
                strTransID2 = "";
            rowData.put("transIDRight", strTransID2);

            String strTransType2 = itemReport.getS_trans_type_2();
            if (strTransType2 == null)
                strTransType2 = "";
            rowData.put("transTypeRight", strTransType2);

            String strTransStatus2 = itemReport.getS_trans_status_2();
            if (strTransStatus2 == null)
                strTransStatus2 = "";
            rowData.put("statusRight", strTransStatus2);

        
            String strOrgAmount2 = itemReport.getOriginalAmountRight();
            double dbOrgAmount2 = 0;
            if (strOrgAmount2 != null) {
                dbOrgAmount2 = Double.parseDouble(strOrgAmount2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("originalAmountRight", currencyCode2 + Convert.toString(dbOrgAmount2, "###,##0.##"));
            } else {
                rowData.put("originalAmountRight", "");
            }

            String strFirstPayment2 = itemReport.getFirstPaymentRight();
            double dbFirstPayment2 = 0;
            if (strFirstPayment2 != null) {
                dbFirstPayment2 = Double.parseDouble(strFirstPayment2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("firstPaymentRight", currencyCode2 + Convert.toString(dbFirstPayment2, "###,##0.##"));
            } else {
                rowData.put("firstPaymentRight", "");
            }

            String strPayLater2 = itemReport.getPayLaterRight();
            double dbPayLater2 = 0;
            if (strPayLater2 != null) {
                dbPayLater2 = Double.parseDouble(strPayLater2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("payLaterRight", currencyCode2 + Convert.toString(dbPayLater2, "###,##0.##"));
            } else {
                rowData.put("payLaterRight", "");
            }

            String strDisbursedAmount2 = itemReport.getDisbursedAmountRight();
            double dbDisbursedAmount2 = 0;
            if (strDisbursedAmount2 != null) {
                dbDisbursedAmount2 = Double.parseDouble(strDisbursedAmount2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("disbursedRight", currencyCode2 + Convert.toString(dbDisbursedAmount2, "###,##0.##"));
            } else {
                rowData.put("disbursedRight", "");
            }

            String strRefundAmount2 = itemReport.getRefundAmountRight();
            double dbRefundAmount2 = 0;
            if (strRefundAmount2 != null) {
                dbRefundAmount2 = Double.parseDouble(strRefundAmount2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("refundRight", currencyCode2 + Convert.toString(dbRefundAmount2, "###,##0.##"));
            } else {
                rowData.put("refundRight", "");
            }

            String strProviderMerchFee2 = itemReport.getProviderMerchantFeeRight();
            double dbProviderMerchFee2 = 0;
            if (strProviderMerchFee2 != null) {
                dbProviderMerchFee2 = Double.parseDouble(strProviderMerchFee2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("providerMerchFeeRight", currencyCode2 + Convert.toString(dbProviderMerchFee2, "###,##0.##"));
            } else {
                rowData.put("providerMerchFeeRight", "");
            }

            String strCustomerName2 = itemReport.getCustomerNameRight();
            if (strCustomerName2 == null)
                strCustomerName2 = "";
            rowData.put("customerNameRight", strCustomerName2);

            String strCustomerMobile2 = itemReport.getCustomerMobileRight();
            if (strCustomerMobile2 == null)
                strCustomerMobile2 = "";
            rowData.put("customerPhoneRight", strCustomerMobile2);

            String strModel2 = itemReport.getModelRight();
            if (strModel2 == null)
                strModel2 = "";
            rowData.put("modelRight", strModel2);

            listData.add(rowData);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ReconciliationBNPLGenerator.class.getName());
}
