package vn.onepay.portal.queue.listener.excelBuilder.ext.invoice_individual;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.invoice_individual.invoice_transaction.InvoiceTransaction;
import vn.onepay.portal.resources.invoice_individual.invoice_transaction.InvoiceTransactionDao;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InvoiceTransactionGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger LOGGER = Logger.getLogger(InvoiceSummaryGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            JsonObject inputSearch = new JsonObject();
            inputSearch.put("invoiceId", Integer.parseInt(mIn.get("id").toString()));
            List<InvoiceTransaction> invoiceTransactions = InvoiceTransactionDao.getListInvTransaction(inputSearch);
            this.generateRs(invoiceTransactions, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Invoice Individual Generator error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<InvoiceTransaction> list, List<Map> listData) throws Exception {
        try {
            for (InvoiceTransaction data : list) {
                Map item = new HashMap();
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, data.getRowNum());
                item.put(TemplateUtils.PARTNER_NAME_COLUMN, data.getPartnerName());
                item.put(TemplateUtils.TEN_DKKD_COLUMN, data.getTenDKKD());
                item.put(TemplateUtils.TEN_DV, data.getTenDV());
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchantId());
                item.put(TemplateUtils.CONTRACT_CODE_COLUMN, data.getContractCode());
                item.put(TemplateUtils.MFR_ADDENDUM, data.getAddendum());
                item.put(TemplateUtils.MST, "");
                item.put(TemplateUtils.TRANSACTION_ID_COLUMN, data.getTransactionId());
                item.put(TemplateUtils.ORDER_INFO_COLUMN, data.getOrderInfo());
                item.put(TemplateUtils.TRANSACTION_DATE_COLUMN, data.getDateTransaction());
                item.put(TemplateUtils.TRANSACTION_TYPE_COLUMN, data.getTransactionType());
                item.put(TemplateUtils.AMOUNT_VND, data.getAmountVND());
                if ("VND".equalsIgnoreCase(data.getCurrency())) {
                    item.put(TemplateUtils.AMOUNT_USD_EXCHANGE_COLUMN, 0);
                    item.put(TemplateUtils.AMOUNT_VND_EXCHANGE_COLUMN, data.getAmount());
                }
                if ("USD".equalsIgnoreCase(data.getCurrency())) {
                    item.put(TemplateUtils.AMOUNT_USD_EXCHANGE_COLUMN, data.getAmount());
                    item.put(TemplateUtils.AMOUNT_VND_EXCHANGE_COLUMN, 0);
                }
                item.put(TemplateUtils.FEE, data.getTotalFeeADV());
                item.put(TemplateUtils.SETTLEMENT_COLUMN, data.getSettlement());


                item.put(TemplateUtils.RECEIPT_NUMBER, data.getReceiptNumber());


                /**
                 * Tong phi bao gom VAT
                 */
                item.put(TemplateUtils.TOTAL_FEE_ADV_COLUMN, data.getTotalFeeADV());

                /**
                 * Phi Ecom
                 */
                item.put(TemplateUtils.MFR_ECOM_FEE, data.getFeeEcomADV());

                /**
                 * Thue VAT
                 */
                item.put(TemplateUtils.FEE_VAT_COLUMN, data.getTotalFeeVAT());

                /**
                 * Phi tra gop
                 */
                item.put(TemplateUtils.FEE_ITA_COLUMN, data.getFeeItaADV());

                /**
                 * Dich vu
                 */
                item.put(TemplateUtils.SERVICE, data.getPayChannel());

                /**
                 * Ma so thue
                 */
                item.put(TemplateUtils.MST, data.getMst());

                /**
                 * Trang thai giao dich
                 */
                item.put(TemplateUtils.STATE_COLUMN, data.getState());

                /**
                 * Phi xu ly giao dich that bai
                 */
                item.put(TemplateUtils.MFR_FEE_FAILED, data.getFeeFailedADV());

                /**
                 * Phi xu ly giao dich thanh cong
                 */
                item.put(TemplateUtils.FEE_SUCCESS_COLUMN, data.getFeeSuccessADV());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }
}
