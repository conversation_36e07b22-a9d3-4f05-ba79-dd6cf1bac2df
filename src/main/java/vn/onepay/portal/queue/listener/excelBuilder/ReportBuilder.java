package vn.onepay.portal.queue.listener.excelBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by anhkh on 05-Jun-17.
 */
public class ReportBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReportBuilder.class);

    private static ExcelReportGenerator reportGenerator;

    public ReportBuilder exportFileName(String fileName) {
        ReportBuilder.reportGenerator.setExportFileName(fileName);
        return this;
    }

    public ReportBuilder template(String templatePath) {
        ReportBuilder.reportGenerator.setTemplateFilePath(templatePath);
        return this;
    }

    public ReportBuilder setBeanValue(String key, Object value) {
        if (ReportBuilder.reportGenerator.getBean() == null) {
            ReportBuilder.reportGenerator.setBean(new HashMap<>());
        }
        if (value != null) {
            ReportBuilder.reportGenerator.getBean().put(key, value);
        }
        return this;
    }

    public ReportBuilder setListBeanValue(List<Map<String,Object>> maps) {
        ReportBuilder.reportGenerator.setListBean(maps);
        return this;
    }

    public ReportBuilder setBean(HashMap map) {
        if (map != null) {
            ReportBuilder.reportGenerator.setBean(map);
        }
        return this;
    }

    public ReportBuilder setParameters(Map<String, Object> parameters) {
        ReportBuilder.reportGenerator.setParameters(parameters);
        return this;
    }

    public ReportBuilder setTemplateSheetNameList(List<String> templateSheetNameList) {
        ReportBuilder.reportGenerator.setTemplateSheetNameList(templateSheetNameList);
        return this;
    }

    public ReportBuilder setSheetNameList(List<String> sheetNameList) {
        ReportBuilder.reportGenerator.setSheetNameList(sheetNameList);
        return this;
    }

    public ReportBuilder setListData(List<Object> listData) {
        ReportBuilder.reportGenerator.setListData(listData);
        return this;
    }

    public ReportBuilder setListMap(Object listMap) {
        try {
            ReportBuilder.reportGenerator.setListMap((List<Map<String, Object>>) (List<?>) listMap);
        } catch (ClassCastException e) {
            LOGGER.error("Error while parsing export", e);
        }
        return this;
    }

    public ReportBuilder setListHiddenColumn(List<String> lstColumn) {
        if (lstColumn == null) {
            return this;
        }
        this.reportGenerator.setHiddenColumn(lstColumn);
        return this;
    }

    /**
     * Set start-th row of list map on template file (count from 0)
     */
    public ReportBuilder setStart(int start) {
        ReportBuilder.reportGenerator.setStart(start);
        return this;
    }

    public ReportBuilder setRestyleNumber(boolean value) {
        ReportBuilder.reportGenerator.setRestyleNumber(value);
        return this;
    }

    public ReportBuilder setMaxCols(int maxCols) {
        ReportBuilder.reportGenerator.setMaxCols(maxCols);
        return this;
    }

    public ReportBuilder setImage(byte[] bytes) {
        ReportBuilder.reportGenerator.setImage(bytes);
        return this;
    }

    public static ReportBuilder newInstance() {
        reportGenerator = new ExcelReportGenerator();

        return new ReportBuilder();
    }

    public ExcelReportGenerator build() {
        return ReportBuilder.reportGenerator;
    }
}
