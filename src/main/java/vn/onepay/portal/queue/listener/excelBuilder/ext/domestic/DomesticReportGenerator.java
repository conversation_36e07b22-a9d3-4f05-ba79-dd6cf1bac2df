package vn.onepay.portal.queue.listener.excelBuilder.ext.domestic;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.report.DomesticReportDao;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReportConvert;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DomesticReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        // List<DomesticReport> result = null;
        try {
            // result = DomesticReportDao.getListDomesticReports(mIn);
            // result.addAll(DomesticRefundApprovalDao.getDomesticReportPending(mIn));
            // List<DomesticReportConvert> listFinalReports = DomesticReportDao.convertReport(result);
            // Collections.sort(listFinalReports, Comparator.comparing(DomesticReportConvert::getTransaction_date)
            //         .thenComparing(DomesticReportConvert::getMerchant_id)
            //         .thenComparing(DomesticReportConvert::getAcquirer_name)
            //         .thenComparing(DomesticReportConvert::getAcquirer_bank)
            //         .thenComparing(DomesticReportConvert::getCaic));

            // String version = mIn.get("version").toString();
            // listFinalReports = listFinalReports.stream().filter(domesticReportConvert -> {
            //     if(version.equalsIgnoreCase("successful")) {
            //         return domesticReportConvert.getTransaction_count() != 0 || domesticReportConvert.getRefund_count() != 0;
            //     }
            //     if(version.equalsIgnoreCase("fail")) {
            //         return domesticReportConvert.getTransaction_fail_count() != 0 || domesticReportConvert.getRefund_fail_count() != 0;
            //     }
            //     if(version.equalsIgnoreCase("pending")) {
            //         return domesticReportConvert.getRefund_pending_count() != 0;
            //     }
            //     return  true;
            // }).collect(Collectors.toList());
            List<DomesticReportConvert> listFinalReports = DomesticReportDao.getListDomesticReports(mIn);
            this.generateRs(listFinalReports, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<DomesticReportConvert> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();

        for (DomesticReportConvert itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getTransaction_date());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchant_id());

            // bank Id
            item.put(TemplateUtils.BANK_COLUMN, itemReport.getAcquirer_name());

            item.put("S_CAIC", itemReport.getCaic());
            
            item.put("S_CONTRACT_TYPE", itemReport.getContract_type());
            
            item.put("S_CONTRACT_TYPE", itemReport.getContract_type());
            
            item.put("S_ACQUIRER", itemReport.getAcquirer_bank());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");

            // platform
            item.put(TemplateUtils.PLATFORM_COLUMN, itemReport.getPlatform());

            // Transaction Count
            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getTransaction_count());
            item.put("N_PURCHASE_FAIL", itemReport.getTransaction_fail_count());

            // Transaction total Count
            item.put(TemplateUtils.PURCHASE_TOTAL_COUNT_COLUMN, itemReport.getTransaction_total());
            item.put("N_TOTAL_PURCHASE_FAIL", itemReport.getTransaction_fail_total());
            item.put("N_TOTAL_ORIGINAL_AMOUNT", itemReport.getTransaction_original_total());

            // Refund Count
            item.put(TemplateUtils.REFUND_COUNT_COLUMN, itemReport.getRefund_count());
            item.put("N_REFUND_FAIL", itemReport.getRefund_fail_count());
            item.put("N_REFUND_PENDING", itemReport.getRefund_pending_count());

            // Refund total Count
            item.put(TemplateUtils.REFUND_TOTAL_COUNT_COLUMN, itemReport.getRefund_total());
            item.put("N_TOTAL_REFUND_FAIL", itemReport.getRefund_fail_total());
            item.put("N_TOTAL_REFUND_PENDING", itemReport.getRefund_pending_total());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticReportGenerator.class.getName());
}
