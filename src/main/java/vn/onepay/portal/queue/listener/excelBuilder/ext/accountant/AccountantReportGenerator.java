package vn.onepay.portal.queue.listener.excelBuilder.ext.accountant;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.accountantManagement.report.ReportDAO;
import vn.onepay.portal.resources.accountantManagement.statement.StatementHandler;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.time.LocalDate;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;

public class AccountantReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<Map<String, Object>> baseListReport = ReportDAO
                    .listReportById(Long.parseLong(mIn.get("id").toString()));
            String fromDateString = mIn.get("from_date").toString();
            String toDateString = mIn.get("to_date").toString();
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(sdf3.format(sdf.parse(fromDateString))),
                    end = LocalDate.parse(sdf3.format(sdf.parse(toDateString)));
            List<Map> summaryMapList = new ArrayList<>();
            for (LocalDate date = start; date.isBefore(end.plusDays(1)); date = date.plusDays(1)) {
                Map<String, Object> summaryMap = new HashMap<>();
                summaryMap = StatementHandler.getSummaryStm(sdf2.format(sdf3.parse(date.toString())));
                summaryMap.put("date", date.toString());
                summaryMapList.add(summaryMap);
            }
            generateReport2(summaryMapList, listMap, baseListReport.get(0).get("N_IB_VCB").toString(),
                    baseListReport.get(0).get("N_IB_VTB").toString(), baseListReport.get(0).get("N_IB_TCB").toString());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE STATEMENT REPORT ERROR", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateReport2(List<Map> list, List<Map> listData, String ibVcb, String ibVtb, String ibTcb)
            throws Exception {
        // Map<Timestamp, List<ReportDetailDTO>> reportGroupedByDate = list.stream()
        // .collect(Collectors.groupingBy(w -> w.getReportDate()));
        // SortedMap<Timestamp, List<ReportDetailDTO>> sortedMap = new
        // TreeMap<Timestamp, List<ReportDetailDTO>>();
        // sortedMap.putAll(reportGroupedByDate);

        Map item = new HashMap();
        double psTotal = 0;
        double vcbAmount = 0;
        double vcbQuantity = 0;
        double vcbFee = 0;
        double vcbTotal = 0;
        double vtbTotal = 0;
        double tcbTotal = 0;
        double inTcbTotal = 0;
        double outTcbTotal = 0;
        double total = 0;
        double differenceTotal = 0;
        double vcbIb = Double.parseDouble(ibVcb);
        double vtbIb = Double.parseDouble(ibVtb);
        double tcbIb = Double.parseDouble(ibTcb);
        // double feeTotal = 0;
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> totalList = new HashMap<>(), totalVcbList = new HashMap<>(),
                    totalTcbList = new HashMap<>();
            totalList = (Map<String, Object>) list.get(i).get("total");
            totalVcbList = (Map<String, Object>) totalList.get("VCB");
            totalTcbList = (Map<String, Object>) totalList.get("TCB");
            psTotal = psTotal + Double.parseDouble(totalList.get("advance_amount").toString());
            vcbAmount = vcbAmount + Double.parseDouble(totalVcbList.get("amount").toString());
            vcbQuantity = vcbQuantity + Double.parseDouble(totalVcbList.get("quantity").toString());
            vcbFee = vcbFee + Double.parseDouble(totalVcbList.get("fee").toString());
            vcbTotal = vcbTotal + Double.parseDouble(totalVcbList.get("total").toString());
            vtbTotal = vtbTotal + Double.parseDouble(totalList.get("VTB").toString());
            tcbTotal = tcbTotal + Double.parseDouble(totalTcbList.get("total").toString());
            inTcbTotal = inTcbTotal + Double.parseDouble(totalTcbList.get("in_amount").toString());
            outTcbTotal = outTcbTotal + Double.parseDouble(totalTcbList.get("out_amount").toString());
            total = total + Double.parseDouble(totalList.get("statement_amount").toString());
            differenceTotal = differenceTotal + Double.parseDouble(totalList.get("discrepancy").toString());
            // vcbIb = list.get(i).getIbVCB();
            // vtbIb = list.get(i).getIbVTB();
            // tcbIb = list.get(i).getIbTCB();
            // feeTotal = feeTotal + list.get(i).getReportFee();
        }

        List<Map> reportConverted = new ArrayList<>();
        list.forEach(valueReport -> {
            if (((List) valueReport.get("detail")).size() > 0) {
                Map<String, Object> totalList = new HashMap<>(), totalVcbList = new HashMap<>(),
                        totalTcbList = new HashMap<>();
                List<Map> detailList = new ArrayList<>();
                totalList = (Map<String, Object>) valueReport.get("total");
                totalVcbList = (Map<String, Object>) totalList.get("VCB");
                totalTcbList = (Map<String, Object>) totalList.get("TCB");
                detailList = (List) valueReport.get("detail");
                List<Map> listReportByDate = new ArrayList<>();

                for (int j = 0; j < detailList.size(); j++) {
                    Map<String, Object> detailVcbList = new HashMap<>(), detailTcbList = new HashMap<>();
                    detailVcbList = (Map<String, Object>) detailList.get(j).get("VCB");
                    detailTcbList = (Map<String, Object>) detailList.get(j).get("TCB");
                    Map m = new HashMap();

                    m.put("reportName", detailList.get(j).get("name"));
                    m.put("ps", detailList.get(j).get("advance_amount"));
                    m.put("vcbAmount", detailVcbList.get("amount"));
                    m.put("vcbQuantity", detailVcbList.get("quantity"));
                    m.put("vcbFee", detailVcbList.get("fee"));
                    m.put("vcbTotal", detailVcbList.get("total"));
                    m.put("vtb", detailList.get(j).get("VTB"));
                    m.put("tcb", detailTcbList.get("total"));
                    m.put("inTcb", detailTcbList.get("in_amount"));
                    m.put("outTcb", detailTcbList.get("out_amount"));
                    m.put("total", detailList.get(j).get("statement_amount"));
                    m.put("differenceBankTotalPsTotalItem", detailList.get(j).get("discrepancy"));
                    listReportByDate.add(m);
                }

                Map reportDateMap = new HashMap();
                reportDateMap.put("listReportByDate", listReportByDate);
                reportDateMap.put("reportDatePsTotal", totalList.get("advance_amount"));
                reportDateMap.put("reportDateVcbAmount", totalVcbList.get("amount"));
                reportDateMap.put("reportDateVcbQuantity", totalVcbList.get("quantity"));
                reportDateMap.put("reportDateVcbFee", totalVcbList.get("fee"));
                reportDateMap.put("reportDateVcbTotal", totalVcbList.get("total"));
                reportDateMap.put("reportDateVtbTotal", totalList.get("VTB"));
                reportDateMap.put("reportDateTcbTotal", totalTcbList.get("total"));
                reportDateMap.put("reportDateInTcb", totalTcbList.get("in_amount"));
                reportDateMap.put("reportDateOutTcb", totalTcbList.get("out_amount"));
                reportDateMap.put("reportDateTotal", totalList.get("statement_amount"));
                reportDateMap.put("reportDateDifferenceTotal", totalList.get("discrepancy"));
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String date = valueReport.get("date").toString();
                Timestamp filterDateFromTs;
                try {
                    filterDateFromTs = new Timestamp((dateFormat.parse(date)).getTime());
                    reportDateMap.put("day", convertToDay(filterDateFromTs));
                    reportDateMap.put("date", handleData(filterDateFromTs));
                } catch (ParseException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

                Double reportDateBankTotal = Double.parseDouble(totalVcbList.get("total").toString())
                        + Double.parseDouble(totalList.get("VTB").toString())
                        + Double.parseDouble(totalTcbList.get("total").toString());
                reportDateMap.put("reportDateBankTotal", reportDateBankTotal);
                reportDateMap.put("reportDateDifferenceBankTotalPsTotal", Double.parseDouble(totalList.get("discrepancy").toString()));
                reportConverted.add(reportDateMap);
            }
        });

        item.put("reportConverted", reportConverted);
        item.put("psTotal", psTotal);
        item.put("vcbAmount", vcbAmount);
        item.put("vcbQuantity", vcbQuantity);
        item.put("vcbFee", vcbFee);
        item.put("vcbTotal", vcbTotal);
        item.put("vtbTotal", vtbTotal);
        item.put("tcbTotal", tcbTotal);
        item.put("inTcbTotal", inTcbTotal);
        item.put("outTcbTotal", outTcbTotal);
        item.put("total", total);
        item.put("differenceTotal", differenceTotal);
        item.put("vcbIb", vcbIb);
        item.put("vtbIb", vtbIb);
        item.put("tcbIb", tcbIb);
        item.put("vcbDifference", vcbIb - vcbTotal);
        item.put("vtbDifference", vtbIb - vtbTotal);
        item.put("tcbDifference", tcbIb - tcbTotal);
        item.put("bankTotal", total);
        item.put("differenceBankTotalPsTotal", psTotal - total);
        // item.put("feeTotal", feeTotal);
        // item.put("note", feeTotal == vcbTotal + vtbTotal + tcbTotal - psTotal ? "Phí"
        // : "Khác");
        listData.add(item);
    }

    private static String handleData(Timestamp inputData) {
        String outputData = "";
        if (inputData != null) {
            try {
                outputData = Util.formatDate(new Date(inputData.getTime()), "dd-MMM-yyyy");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Convert Date error", e);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }
        return outputData;
    }

    public static String convertToDay(Timestamp inputData) {
        Calendar c = Calendar.getInstance();
        c.setTime(inputData);
        int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);
        switch (dayOfWeek) {
        case 1:
            return "Chủ nhật";
        case 2:
            return "Hai";
        case 3:
            return "Ba";
        case 4:
            return "Tư";
        case 5:
            return "Năm";
        case 6:
            return "Sáu";
        case 7:
            return "Bảy";
        default:
            System.out.println("");
        }
        return null;
    }

    public static String convertBank(int exportType) {
        switch (exportType) {
        case 1:
            return "VCB";
        case 2:
            return "TCB";
        case 3:
            return "VCB";
        case 4:
            return "TCB";
        case 5:
            return "VCB";
        case 6:
            return "TCB";
        case 7:
            return "VTB";
        case 8:
            return "Khác";
        default:
            System.out.println("");
        }
        return null;
    }

    private static final Logger LOGGER = Logger.getLogger(AccountantReportGenerator.class.getName());
}
