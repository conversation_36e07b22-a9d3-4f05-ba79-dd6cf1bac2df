package vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_account;

import vn.onepay.portal.Util;
import vn.onepay.portal.client.MerchantAccountClient;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.refund.DomesticRefundDao;
import vn.onepay.portal.resources.domestic.refund.dto.DomesticRefund;
import vn.onepay.portal.resources.merchant_account.dao.MerchantAccountDao;
import vn.onepay.portal.resources.merchant_account.dto.MerchantAccountParam;
import vn.onepay.portal.resources.merchant_account.dto.MerchantIdService;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.google.gson.Gson;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

public class MerchantAccountGenerator implements BaseGenerator<Map<String, Object>> {

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            JsonObject json = MerchantAccountClient.searchMerchantAccount(mIn);
            //System.out.println("ppppp"+ json);
            //List<Map> listMerchMaps = (List<Map<String,Object>>) json.getMap().get("list");
           // System.out.println("xxxx"+json.getJsonArray("list"));
            MerchantAccountParam[] merchantAccountParam = new Gson().fromJson(json.getJsonArray("list").toString(), MerchantAccountParam[].class);
           
            // for (int i = 0; i < merchantAccountParam.length; i++) {
            //     System.out.println("xxxxx" + merchantAccountParam[i]);
            //    }
            this.generateRs(merchantAccountParam, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Account error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(MerchantAccountParam [] list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();

        for (MerchantAccountParam itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant account name
            item.put("ID", itemReport.getId());

            // merchant account name
            item.put(TemplateUtils.MERCHANT_ACCOUNT_NAME, itemReport.getMerchantAccountName());

            // ma doi tac
            item.put(TemplateUtils.PARTNER_ID, itemReport.getPartnerId());

            // ten doi tac
            item.put(TemplateUtils.PARTNER_NAME, itemReport.getPartnerName());

            // ten DKKD
            item.put(TemplateUtils.BUSINESS_NAME, itemReport.getBusinessName());

            // Merchant Id
            item.put(TemplateUtils.MERCHANT_IDS, itemReport.getMerchantIdService().stream().map(f -> f.getMerchantId()).distinct().collect(Collectors.joining(",")));

            // Loai dich vu
            item.put(TemplateUtils.SERVICE_TYPES, itemReport.getMerchantIdService().stream().map(f -> f.getServiceType()).distinct().collect(Collectors.joining(",")));

            // MCC
            item.put(TemplateUtils.MCC, itemReport.getMerchantIdService().stream().filter(a -> a.getMcc() != null && !a.getMcc().isEmpty()).map(f ->  f.getMcc().trim()).distinct().collect(Collectors.joining(",")));

            // ma hop dong
            item.put(TemplateUtils.CONTRACT_CODE, itemReport.getContractCode());

            // subcontract code
            item.put(TemplateUtils.SUBCONTRACT_CODE, itemReport.getSubcontractCode());

            // ma so thue
            item.put(TemplateUtils.TAX_CODE, itemReport.getTaxCode());

            //activated date 
            item.put(TemplateUtils.ACTIVATED_DATE, Util.formatDate(new Date(itemReport.getActivatedDate()), "dd/MM/yyyy hh:mm:ss aaa"));

            // nhom nganh nghe
            item.put(TemplateUtils.CATEGORY, itemReport.getCategory());

            // tinh thanh
            item.put(TemplateUtils.PROVINCE, itemReport.getProvince());

            // put into list
            listData.add(item);
        }
    }

    private static String mapToForm(MerchantAccountParam map) {
        // if (!map.isEmpty()) {
            return map.getMerchantIdService().stream().map(x -> x.getActivatedDate()).collect(Collectors.joining(","));
        // } else {
        //     return "";
        // }
    }


    private static final Logger LOGGER = Logger.getLogger(MerchantAccountGenerator.class.getName());
}
