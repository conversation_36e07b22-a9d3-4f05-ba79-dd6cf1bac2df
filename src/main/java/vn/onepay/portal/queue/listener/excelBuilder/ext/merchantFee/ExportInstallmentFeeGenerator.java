package vn.onepay.portal.queue.listener.excelBuilder.ext.merchantFee;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_reconciliation.merchant_fee.dto.FeeDataInstallmentDTO;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportInstallmentFeeGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(ExportInstallmentFeeGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<FeeDataInstallmentDTO> feeDataInstallmentDTOs = (List<FeeDataInstallmentDTO>) mIn.get("data");
            logger.log(Level.INFO, "feeDataInstallmentDTOs: " + feeDataInstallmentDTOs.size());
//            feeDataInstallmentDTOs.sort((o1,o2) -> o1.getName().compareTo(o2.getName()));
            generateRs(feeDataInstallmentDTOs, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate On Off Bank Approval History Error ", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<FeeDataInstallmentDTO> feeDataInstallmentDTOs, List<Map> listMap) {
        Map mapData;
        int index = 1;
        for (FeeDataInstallmentDTO item : feeDataInstallmentDTOs) {
            logger.log(Level.INFO, "item: " + item.getName() + " - " + item.getState());
            if(item.getState().equals("active") || item.getState().equals("approved")){
                logger.log(Level.INFO, "item: " + item.getName() + " - in " + item.getState());
                mapData = new HashMap();
                mapData.put("INDEX", index++);
                mapData.put("BANK_NAME", item.getName());
                mapData.put("BANK_3_MONTHS_FEE", convertPercent(item.getBank3monthsFee()));
                mapData.put("BANK_6_MONTHS_FEE", convertPercent(item.getBank6monthsFee()));
                mapData.put("BANK_9_MONTHS_FEE", convertPercent(item.getBank9monthsFee()));
                mapData.put("BANK_12_MONTHS_FEE", convertPercent(item.getBank12monthsFee()));
                mapData.put("BANK_15_MONTHS_FEE", convertPercent(item.getBank15monthsFee()));
                mapData.put("BANK_18_MONTHS_FEE", convertPercent(item.getBank18monthsFee()));
                mapData.put("BANK_24_MONTHS_FEE", convertPercent(item.getBank24monthsFee()));
                mapData.put("BANK_36_MONTHS_FEE", convertPercent(item.getBank36monthsFee()));
                listMap.add(mapData);
            }
        }
    }

    private String convertPercent(Double a){
        if(a == null){
            return "x";
        }else if (a == 0){
            return "0%";
        }else
            return a + "%";
    }

}
