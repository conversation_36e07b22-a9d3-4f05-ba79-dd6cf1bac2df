package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_reconciliation.end_user;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_reconciliation.end_user.dao.EndUserFeeConfigDao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Payment2EndUserFeeGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(Payment2EndUserFeeGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put("type", "DOWNLOAD");
            Map<String,Object> data = EndUserFeeConfigDao.getListMerchantFee(mIn);
            listMap = (List<Map>) data.get("list");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportQrPaygateGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }
}
