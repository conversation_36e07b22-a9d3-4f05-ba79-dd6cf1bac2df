package vn.onepay.portal.queue.listener.excelBuilder.ext.fixedDeposit;

import vn.onepay.portal.Config;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fixedDeposit.FixedDepositDao;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositDto;
import vn.onepay.portal.resources.fixedDeposit.dto.FixedDepositQueryDto;
import vn.onepay.portal.utils.Common;
import vn.onepay.portal.utils.ConvertMoneyNumberToString;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UNCPdfGenerator implements BaseGenerator<FixedDepositQueryDto> {

    @Override
    public List<Map> generate(FixedDepositQueryDto query) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<FixedDepositDto> list = FixedDepositDao.getByids(query).getList();
            this.generateRs(list, listMap, query.getType());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FixedDepositDto> list, List<Map> listData, String type) throws Exception {
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        DateFormat df2 = new SimpleDateFormat("dd-MM-yy");
        String onepay_acc_num = Config.getString("unc.onepay_acc_num", "0011 003 100399");
        String onepay_acc_name = Config.getString("unc.onepay_acc_name", "Công ty CP TM và DV Trực Tuyến OnePay");
        String onepay_acc_address = Config.getString("unc.onepay_acc_address", "194 Trần Quang Khải,HN");
        String onepay_acc_bank = Config.getString("unc.onepay_acc_bank", "Vietcombank - Sở giao dịch");
        String accounterName = Config.getString("unc.accounter_name", "Lê Thị Hải Phương");

        for (FixedDepositDto itemReport : list) {
            for (int i = 1; i <= 3; i++) {
                Map item = new HashMap();
                long days = 0;
                if (itemReport.getOpenDate() != null && !itemReport.getOpenDate().equals("") && itemReport.getMaturityDate() != null && !itemReport.getMaturityDate().equals("")) {
                    LocalDate openDate = new Timestamp(df.parse(itemReport.getOpenDate()).getTime()).toLocalDateTime().toLocalDate();
                    LocalDate maturityDate = new Timestamp(df.parse(itemReport.getMaturityDate()).getTime()).toLocalDateTime().toLocalDate();
                    days = Duration.between(openDate.atStartOfDay(), maturityDate.atStartOfDay()).toDays();
                }
                if (type.equals("open")) {
                    item.put("s_date", "");
                    item.put("s_acc_debit_number", "VND  "+onepay_acc_num);
                    item.put("s_acc_debit_name", onepay_acc_name);
                    item.put("s_acc_debit_address", onepay_acc_address);
                    item.put("s_acc_debit_bank", onepay_acc_bank);
                    item.put("s_acc_credit_number", "");
                    item.put("s_acc_credit_name", "");
                    item.put("s_acc_credit_address", "");
                    item.put("s_acc_credit_bank", "");
                } else if (type.equals("refund")) {
                    item.put("s_date", "");
                    item.put("s_acc_debit_number", "VND  " + onepay_acc_num);
                    item.put("s_acc_debit_name", onepay_acc_name);
                    item.put("s_acc_debit_address", onepay_acc_address);
                    item.put("s_acc_debit_bank", onepay_acc_bank);
                    item.put("s_acc_credit_number", itemReport.getAccRefNum());
                    item.put("s_acc_credit_name", itemReport.getAccRefName());
                    item.put("s_acc_credit_address", itemReport.getAccRefAddress());
                    if (itemReport.getAccRefBranch() != null && itemReport.getAccRefBranch().length() > 0) {
                        item.put("s_acc_credit_bank", itemReport.getAccRefBank() + " - " + itemReport.getAccRefBranch());
                    } else {
                        item.put("s_acc_credit_bank", itemReport.getAccRefBank());
                    }

                } else if (type.equals("break")) {
                    LOGGER.log(Level.INFO, "getAccSavingBank" + itemReport.getAccSavingBank());
                    LOGGER.log(Level.INFO, "getAccSavingBranch" + itemReport.getAccSavingBranch());
                    item.put("s_date", df2.format(new Date()));
                    item.put("s_acc_debit_number", itemReport.getFd());
                    item.put("s_acc_debit_name", itemReport.getAccSavingName());
                    item.put("s_acc_debit_address", itemReport.getAccSavingAddress());
                    if (itemReport.getAccSavingBranch() != null && itemReport.getAccSavingBranch().length() > 0) {
                        item.put("s_acc_debit_bank", itemReport.getAccSavingBank() + " - " + itemReport.getAccRefBranch());
                    } else if (itemReport.getAccSavingBank() != null && itemReport.getAccSavingBank().length() > 0) {
                        item.put("s_acc_debit_bank", itemReport.getAccSavingBank());
                    } else {
                        item.put("s_acc_debit_bank", "");
                    }
                    item.put("s_acc_credit_number", "VND  " + onepay_acc_num);
                    item.put("s_acc_credit_name", onepay_acc_name);
                    item.put("s_acc_credit_address", onepay_acc_address);
                    item.put("s_acc_credit_bank", onepay_acc_bank);
                } else {
                    item.put("s_acc_debit_number", "");
                    item.put("s_acc_debit_name", "");
                    item.put("s_acc_debit_address", "");
                    item.put("s_acc_debit_bank", "");
                    item.put("s_acc_credit_number", "");
                    item.put("s_acc_credit_name", "");
                    item.put("s_acc_credit_address", "");
                    item.put("s_acc_credit_bank", "");
                }
                item.put("s_accounter_name", accounterName);
                item.put("n_lien", Integer.toString(i));
                item.put("s_content", convertContent(itemReport, type, onepay_acc_num));
                item.put("s_amount", Common.formatCurrencyDouble(itemReport.getBalance()));
                
                String balance_amount= Long.toString((long) Math.round(itemReport.getBalance()));
                LOGGER.log(Level.INFO, "N_BALANCE: " + itemReport.getBalance());
                LOGGER.log(Level.INFO, "N_BALANCE AFTER: " + balance_amount);
                item.put("s_amount_text", ConvertMoneyNumberToString.getVND(balance_amount));     
                listData.add(item);
            }
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FixedDepositPdfGenerator.class.getName());

    private static String convertContent(FixedDepositDto itemReport, String type, String onepay_acc_num) {
        StringBuilder sb = new StringBuilder();
        if (type.equals("open")) {
            sb.append("Mở tiết kiệm tiền gửi có kỳ hạn ");
            if (itemReport.getMaturityPeriod() < 10) {
                sb.append("0" + itemReport.getMaturityPeriod());
            } else {
                sb.append(itemReport.getMaturityPeriod());
            }

            sb.append(" tháng");
            if (itemReport.getMerchantName() != null && itemReport.getMerchantName().length() > 0) {
                sb.append(" - " + itemReport.getMerchantName());
            }
            if (itemReport.getTaxNumber() != null && itemReport.getTaxNumber().length() > 0) {
                sb.append(" - MST: " + itemReport.getTaxNumber());
            }
            sb.append(" - " + convertRollPrincipal(itemReport.getRollPrincipal(), onepay_acc_num) + " (HĐ: " + itemReport.getContractCode() + ")");
        } else if (type.equals("refund")) {
            if(itemReport.getRollPrincipal().equals("PRINCIPAL")) {
                sb.append("OP hoàn trả lãi FD " + itemReport.getFd());
            }
            else {
                sb.append("OP hoàn trả khoản ĐBTT + lãi FD " + itemReport.getFd());
            }
            if (itemReport.getMerchantName() != null && itemReport.getMerchantName().length() > 0) {
                sb.append(" - " + itemReport.getMerchantName());
            }
            if (itemReport.getTaxNumber() != null && itemReport.getTaxNumber().length() > 0) {
                sb.append(" - MST: " + itemReport.getTaxNumber());
            }
            sb.append(" - HĐ: " + itemReport.getContractCode());
        } else {
            sb.append("Tất toán FD " + itemReport.getFd());
            if (itemReport.getMerchantName() != null && itemReport.getMerchantName().length() > 0) {
                sb.append(" - " + itemReport.getMerchantName());
            }
            if (itemReport.getTaxNumber() != null && itemReport.getTaxNumber().length() > 0) {
                sb.append(" - MST: " + itemReport.getTaxNumber());
            }
            sb.append(" - HĐ: " + itemReport.getContractCode());
        }
        return sb.toString();
    }

    private static String convertRollPrincipal(String rollPrincipal, String onepay_acc_num) {
        if (rollPrincipal.equals("BOTH")) {
            return "Lãi nhập gốc";
        } else if (rollPrincipal.equals("PRINCIPAL")) {
            return "Lãi chuyển về TK " + onepay_acc_num.replaceAll(" ", "");
        } else if (rollPrincipal.equals("NONE")) {
            return "Gốc và lãi chuyển về TK " + onepay_acc_num.replaceAll(" ", "");
        } else {
            return "";
        }
    }
}
