package vn.onepay.portal.queue.listener.excelBuilder.ext.transaction_accountant;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.Status;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2_transaction_accounting.report18.TransactionAccountingDao;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class TransactionAccoutingGenerator18 implements BaseGenerator<Map<String, Object>>, IConstants {
    private static final Logger LOGGER = Logger.getLogger(TransactionAccoutingGenerator18.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) {
        List<Map> listMap = new ArrayList<>();
        List<Map<String, Object>> listData = new ArrayList<>();
        try {
            mIn.put("page_size", Integer.MAX_VALUE);
            mIn.put("page_active", 0);
            listData = (List<Map<String, Object>>) TransactionAccountingDao.searchTransaction(mIn);
            generateRs(listMap, listData, mIn.get(DOWNLOADTYPE).toString());
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[REPORT FULL LINK] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map> list, List<Map<String, Object>> listData, String downloadType) throws Exception {
        int count = 1;
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        List transTypes = new ArrayList<String>();
        transTypes.add("VOID_AUTHORIZE");
        transTypes.add("REFUND_CAPTURE");
        transTypes.add("REFUND");
        transTypes.add("VOID_PURCHASE");
        transTypes.add("VOID_CAPTURE");
        transTypes.add("VOID_REFUND_CAPTURE");
        String transType = "";
        for (Map map : listData) {
            transType = map.get("transactionType").toString();
            map.put("count", count++);
            if (transTypes.contains(transType)) {
                map.put("parentId", map.get("parentId").toString());
            } else if ("VOID_REFUND".equalsIgnoreCase(transType)) {
                map.put("parentId", map.get("originalId").toString());
            }
            map.put("acquirer", map.get("acquirerName"));
            if ("csv".equalsIgnoreCase(downloadType)) {
                map.put("amount", formatNumber(Double.parseDouble(map.get("amount").toString()), map.get("currency").toString()));
            } else {
                map.put("amount", Double.parseDouble(map.get("amount").toString()));
            }
            
            if ("csv".equalsIgnoreCase(downloadType)) {
                map.put("date", map.get("dateDownload").toString());
            } else {
                Date date = dateFormat.parse(map.get("dateDownload").toString());
                map.put("date", date);
            }
            if (null != map.get("itaState") && !map.get("itaState").toString().isBlank()) {
                map.put("itaState", Status.getStatus("installment", map.get("itaState").toString()));
            }
            if (null != map.get("itaAmount") && !map.get("itaAmount").toString().isBlank()) {
                if ("csv".equalsIgnoreCase(downloadType)) {
                    map.put("itaAmount", map.get("itaAmountStr").toString());
                } else {
                    map.put("itaAmount", Double.parseDouble(map.get("itaAmount").toString()));
                }
            }
            list.add(map);
        }

    }

    private String formatNumber(Double amount, String currency){
        DecimalFormat formatter = new DecimalFormat("###,###,###");
        String[] stringArray = {"USD", "SGD", "MYR", "TWD", "CNY", "THB"};
        List<String> stringList = Arrays.asList(stringArray);
        if(stringList.contains(currency)){
            formatter = new DecimalFormat("###,###,###0.00");
        }
        return formatter.format(amount);
    }
}
