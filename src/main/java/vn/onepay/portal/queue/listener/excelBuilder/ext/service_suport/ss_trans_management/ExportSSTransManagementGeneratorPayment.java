package vn.onepay.portal.queue.listener.excelBuilder.ext.service_suport.ss_trans_management;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.service_suport.trans_management.dao.SSTransManagementDao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportSSTransManagementGeneratorPayment implements BaseGenerator<Map<String, Object>>, IConstants {
    private static Logger logger = Logger.getLogger(ExportSSTransManagementGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            Map dt = new HashMap<>();
            Map<String,Object> data = SSTransManagementDao.searchGeneral(mIn);
            List<Map<String,Object>> list = (List<Map<String,Object>>) data.get("list");
            generateRs(list, listMap, columnList);
            listMap.add(dt);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportSSTransManagementGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map<String,Object>> list, List<Map> listMap, List<String> columns) {
        Map mapData;
        for (Map<String,Object> item : list) {
            mapData = new HashMap();
            // No
            mapData.put("no", item.get("no"));
            // Merchant Id
            mapData.put("merchantId", item.get("merchantId"));
            // Gate
            mapData.put("gate", item.get("gate"));
            // Promotion Partner
            mapData.put("promotionPartner", item.get("promotionPartner"));
            // Transaction Id
            mapData.put("transId", item.get("transId"));
            // Oder Reference
            mapData.put("orderRef", item.get("orderRef"));
             // Promotion Id
            mapData.put("promotionId", item.get("promotionId"));
            // Promotion Name
            mapData.put("promotionName", item.get("promotionName"));
            // Transaction Type
            mapData.put("transType", item.get("transType"));
            // Date
            mapData.put("transDate", item.get("transDate"));
            // Card Number
            mapData.put("cardNumber", item.get("cardNumber"));
            // Source
            mapData.put("source", item.get("source"));
            // Customer Mobile Number
            mapData.put("customerMobileNumber", item.get("customerMobileNumber"));
            // Card Type
            mapData.put("cardType", item.get("cardType"));
            // Original Amount
            mapData.put("originalAmount", item.get("originalAmount"));
            // Amount
            mapData.put("transAmount", item.get("transAmount"));
            // Currency
            mapData.put("currency", item.get("currency"));
            // Response Code
            mapData.put("responseCode", item.get("responseCode"));
            // Status
            mapData.put("transState", item.get("transState"));

            // put into list
            listMap.add(mapData);
        }
    }

}
