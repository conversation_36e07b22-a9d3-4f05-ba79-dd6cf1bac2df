package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.topupbank.TopUpBankDao;
import vn.onepay.portal.resources.payout.topupbank.dto.TopUpBankDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BankTopUpGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<TopUpBankDto> topUpBanks = TopUpBankDao.search(mIn).getList();
            this.generateRs(topUpBanks, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Bank TopUp error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<TopUpBankDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (TopUpBankDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

                // Id
                item.put(TemplateUtils.ID, itemReport.getId());

                // swift code
                item.put(TemplateUtils.SWIFT_CODE, itemReport.getSwiftCode());

                // bank Name
                item.put(TemplateUtils.NAME, itemReport.getBankName());

                // bank trans id
                item.put(TemplateUtils.BANK_TXN_REF, itemReport.getBankTransId());

                // balance before
                item.put(TemplateUtils.BEFORE_BALANCE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceBefore()));

                // balance After
                item.put(TemplateUtils.AFTER_BALANCE, ("created".equalsIgnoreCase(itemReport.getState()) || "rejected".equalsIgnoreCase(itemReport.getState()))?"-": Util.formatCurrencyDouble(itemReport.getBalanceAfter()));

                // amount
                item.put(TemplateUtils.AMOUNT, Util.formatCurrencyDouble(itemReport.getAmount()));

                // currency
                item.put(TemplateUtils.CURRENCY, itemReport.getCurrency());

                // state
                String status = itemReport.getState().substring(0, 1).toUpperCase() + itemReport.getState().substring(1);
                item.put(TemplateUtils.STATE, status);

                // type
                String type = itemReport.getType().substring(0, 1).toUpperCase() + itemReport.getType().substring(1);
                item.put(TemplateUtils.STYPE, type);

                // created date
                item.put(TemplateUtils.CREATE, itemReport.getCreatedDate() == null ? "" : Util.formatDate(new Date(itemReport.getCreatedDate().getTime()), "dd-MM-yyyy hh:mm a"));

                item.put(TemplateUtils.UPDATE, itemReport.getUpdatedDate() == null ? "" : Util.formatDate(new Date(itemReport.getUpdatedDate().getTime()), "dd-MM-yyyy hh:mm a"));

                // bank topup date
                item.put(TemplateUtils.BANK_TOPUP, itemReport.getBankTopUpDate() == null ? "" : Util.formatDate(new Date(itemReport.getBankTopUpDate().getTime()), "dd-MM-yyyy hh:mm a"));

                // bank D_FUND date
                item.put("D_FUND", itemReport.getFundDate() == null ? "" : Util.formatDate(new Date(itemReport.getFundDate().getTime()), "dd-MM-yyyy hh:mm a"));

                // Desc
                item.put(TemplateUtils.DESC, itemReport.getDesc());

                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(BankTopUpGenerator.class.getName());
}
