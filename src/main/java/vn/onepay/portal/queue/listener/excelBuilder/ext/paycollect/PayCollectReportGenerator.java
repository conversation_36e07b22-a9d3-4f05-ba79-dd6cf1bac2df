package vn.onepay.portal.queue.listener.excelBuilder.ext.paycollect;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.pay_collect.dao.*;
import vn.onepay.portal.resources.pay_collect.dto.ReportDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PayCollectReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);

            List<ReportDto> list = PayCollectDao.getPayCollectReport(mIn);
            generateRs(list, listMap, mIn.get("interval").toString());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE PAY COLLECT error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReportDto> list, List<Map> listData, String interval) throws Exception {

        int rowNumber = 0;
        Integer totalCount = 0;
        Double totalAmount = 0D;
        for (int i = 0; i < list.size(); i++) {
            rowNumber++;
            Map<String, Object> item = new HashMap<>();
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put("bankDate", list.get(i).getBankDate() == null ? "" : formatDate(new Date(list.get(i).getBankDate().getTime()), interval));
            item.put("updateDate", list.get(i).getUpdateDate() == null ? "" : formatDate(new Date(list.get(i).getUpdateDate().getTime()), interval));
            item.put("createDate", list.get(i).getCreateDate() == null ? "" : formatDate(new Date(list.get(i).getCreateDate().getTime()), interval));
            item.put("merchantId", list.get(i).getMerchantId());
            item.put("accountNumber", list.get(i).getAccount_number());
            totalCount += list.get(i).getNoTrans();
            totalAmount += list.get(i).getTotalTrans();
            item.put("accountName", list.get(i).getAccount_name());
            item.put("bankName", list.get(i).getBank_name());
            item.put("noTrans", list.get(i).getNoTrans());
            // item.put("totalTrans", formatCurrencyDouble(list.get(i).getTotalTrans()));
            item.put("totalTrans", list.get(i).getTotalTrans());
            item.put("referalProgram", list.get(i).getReferalProgram());
            item.put("beneficiaryBankAccount", list.get(i).getBeneficiaryBankAccount());
            listData.add(item);
        }
        HashMap<String, Object> mapCount = new HashMap<>();
        mapCount.put("totalCount", totalCount);
        listData.add(mapCount);
        HashMap<String, Object> mapAmount = new HashMap<>();
        mapAmount.put("totalAmount", formatCurrencyDouble(totalAmount));
        listData.add(mapAmount);

    }

    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("###,###,###");
        return df.format(value);
    }

    public static String formatDate(Date dates, String interval) throws ParseException {
        String myDate = null;
        String partern = null;
        if (dates != null) {
            if (interval.equals("DD")) {
                partern = "dd-MM-yyyy";
            } else if (interval.equals("MM")) {
                partern = "MM-yyyy";
            } else if (interval.equals("Q")) {
                SimpleDateFormat sfq = new SimpleDateFormat("yyyy");
                String year = sfq.format(dates);
                return "Q" + ((dates.getMonth() / 3) + 1) + "," + year;
            } else {
                partern = "yyyy";
            }
            SimpleDateFormat sdf = new SimpleDateFormat(partern);
            try {
                myDate = sdf.format(dates);
            } catch (Exception ex) {
                throw ex;
            }
        } else {
            myDate = "";
        }
        return myDate;
    }

    private static final Logger LOGGER = Logger.getLogger(PayCollectReportGenerator.class.getName());
}
