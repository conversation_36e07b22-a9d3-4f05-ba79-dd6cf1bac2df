package vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_history.constant;

import vn.onepay.portal.resources.system_management.cyber_mpgs_management.constant.AcquirerRuleGroupSwitchStatus;

public enum MerchantDownloadTypeEnum {

    SWITCH("SWITCH", "SWITCH"), CONFIG("CONFIG", "CONFIG");

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    private MerchantDownloadTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(String key) {
        try {
            MerchantDownloadTypeEnum result = null;
            for (MerchantDownloadTypeEnum type : values()) {
                if (type.getKey().equalsIgnoreCase(key)) {
                    result = type;
                    break;
                }
            }
            return result.getValue();
        } catch (Exception ex) {
            System.out.println("MerchantDownloadTypeEnum Not Found");
        }
        return "";
    } 
}