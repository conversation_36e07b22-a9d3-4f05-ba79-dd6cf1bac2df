package vn.onepay.portal.queue.listener.excelBuilder.ext.advance_payment;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.fee.indebconfig.dto.ConfigDto;
import vn.onepay.portal.resources.payment_advance.advance_transaction.AdvanceTransactionDao;
import vn.onepay.portal.resources.payment_advance.advance_transaction.dto.AdvanceTransactionDto;
import vn.onepay.portal.resources.payment_reconciliation.merchant_fee.dao.PaymentMerchantFeeDao;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentAdvanceTransactionGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(PaymentAdvanceTransactionGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<AdvanceTransactionDto> baseList = AdvanceTransactionDao.list(mIn);
            List<AdvanceTransactionDto> list = baseList.getList();
            generateRs(list,listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportQrPaygateGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<AdvanceTransactionDto> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(AdvanceTransactionDto data: list) {
            Map item = new HashMap();
            rowNumber++;

            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            item.put("ID", data.getId());
            item.put("MERCHANT_ID", data.getMerchantId());
            item.put("TRAN_ID", data.getTranId());
            item.put("TRANS_REF", data.getTranRef());
            item.put("DATE", data.getDate());
            item.put("AMOUNT", data.getAmount());
            item.put("CURRENCY", data.getCurrency());
            item.put("CHANNEL", data.getChannel());
            item.put("BANK_CARD", data.getBankCard());
            item.put("STATE", data.getState());
            item.put("FIX_FEE", data.getFixFee());
            item.put("PERCENT_FEE", data.getPercentFee());
            item.put("ITA_FEE", data.getItaFee());
            item.put("TOTAL_FEE", data.getTotalFee());
            item.put("TRANS_TYPE", data.getTranType());
            item.put("STATE_ADV", data.getStateAdvance());
            item.put("PARENT_ID", data.getParentId());
            item.put("CARD_NO", data.getCardNo());
            item.put("AUTH_CODE", data.getAuthCode());
            item.put("IN_ADV", data.getInIdv());
            item.put("DATE_IMPORT", data.getDateImport());
            item.put("DATE_UPDATE", data.getDateUpdate());
            item.put("DESC", data.getDesc());
            listData.add(item);
        }
    }
}
