package vn.onepay.portal.queue.listener.excelBuilder;

import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.engine.xml.JRXmlLoader;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFRegionUtil;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.util.IOUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.checkerframework.checker.units.qual.K;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.slf4j.LoggerFactory;

import com.caucho.services.server.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.qrcode.QRCodeWriter;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.client.OneClient.Bank;
import vn.onepay.portal.resources.payment_analysis.bank_fee.bll.openSearchUtil;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dao.PaymentBankFeeAnalysisDao;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EExchangeRate;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartner;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPartnerMerchantActiveDate;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EPermisson;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EReportItem;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EReportView;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.ETranBankShare;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.ETransaction;
import vn.onepay.portal.resources.payment_analysis.bank_fee.dto.EUposReportItem;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.User;

import java.net.URL;
import javax.sql.RowSet;

import static vn.onepay.portal.Util.failureResponse;

import java.io.*;
import java.lang.reflect.Array;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by anhkh on 05-Jun-17.
 */
public class ExcelReportGenerator {

    private String templateFilePath;

    private Map<String, Object> bean;

    private String exportFileName;

    private Map<String, Object> parameters;

    private List<Object> listData;

    private static List<Map<String, Object>> listMap;

    private Integer start;

    private boolean restyleNumber;

    private int maxCols;

    private List<String> hiddenColumn;

    private List<Map<String, Object>> listBean;

    private List<String> templateSheetNameList;

    private List<String> sheetNameList;

    private byte[] image;

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    /**
     * Getter for property 'templateFilePath'.
     *
     * @return Value for property 'templateFilePath'.
     */

    public List<String> getTemplateSheetNameList() {
        return templateSheetNameList;
    }

    public void setTemplateSheetNameList(List<String> templateSheetNameList) {
        this.templateSheetNameList = templateSheetNameList;
    }

    public List<String> getSheetNameList() {
        return sheetNameList;
    }

    public void setSheetNameList(List<String> sheetNameList) {
        this.sheetNameList = sheetNameList;
    }

    public List<Map<String, Object>> getListBean() {
        return listBean;
    }

    public void setListBean(List<Map<String, Object>> listBean) {
        this.listBean = listBean;
    }

    public List<String> getHiddenColumn() {
        return hiddenColumn;
    }

    public void setHiddenColumn(List<String> hiddenColumn) {
        this.hiddenColumn = hiddenColumn;
    }

    public void setHiddenColumnItem(String column) {
        this.hiddenColumn.add(column);
    }

    public String getTemplateFilePath() {
        return templateFilePath;
    }

    /**
     * Setter for property 'templateFilePath'.
     *
     * @param templateFilePath Value to set for property 'templateFilePath'.
     */
    public void setTemplateFilePath(String templateFilePath) {
        this.templateFilePath = templateFilePath;
    }

    /**
     * Getter for property 'bean'.
     *
     * @return Value for property 'bean'.
     */
    public Map<String, Object> getBean() {
        return bean;
    }

    /**
     * Setter for property 'bean'.
     *
     * @param bean Value to set for property 'bean'.
     */
    public void setBean(Map<String, Object> bean) {
        this.bean = bean;
    }

    /**
     * Getter for property 'exportFileName'.
     *
     * @return Value for property 'exportFileName'.
     */
    public String getExportFileName() {
        return exportFileName;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public List<Object> getListData() {
        return listData;
    }

    public void setListData(List<Object> listData) {
        this.listData = listData;
    }

    public List<Map<String, Object>> getListMap() {
        return listMap;
    }

    public void setListMap(List<Map<String, Object>> listMap) {
        this.listMap = listMap;
    }

    public static Logger getLOGGER() {
        return LOGGER;
    }

    public Integer getStart() {
        return this.start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public void setRestyleNumber(boolean value) {
        this.restyleNumber = value;
    }

    public void setMaxCols(int maxCols) {
        this.maxCols = maxCols;
    }

    /**
     * Setter for property 'exportFileName'.
     *
     * @param exportFileName Value to set for property 'exportFileName'.
     */
    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }

    public void exportExcel() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            LOGGER.log(Level.INFO, "DEBUG: Created XLSTransformer instance");
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            LOGGER.log(Level.INFO, "DEBUG: setSpreadsheetToRename");
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                transformer
                        .setColumnPropertyNamesToHide(this.hiddenColumn.toArray(new String[this.hiddenColumn.size()]));
            }
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, this.bean);
            LOGGER.log(Level.INFO, "DEBUG: transformXLS");
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }

    public void exportExcelMultilSheet() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            LOGGER.log(Level.INFO, "DEBUG: Created XLSTransformer instance");
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            LOGGER.log(Level.INFO, "DEBUG: setSpreadsheetToRename");
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                transformer
                        .setColumnPropertyNamesToHide(this.hiddenColumn.toArray(new String[this.hiddenColumn.size()]));
            }
            // Picture p;
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, templateSheetNameList,
                    sheetNameList, this.listBean);
            LOGGER.log(Level.INFO, "DEBUG: transformXLS");
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }

    public void exportCustomizeColumsExcel() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE exportCustomizeColumsExcel-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            Map<String, Object> listMap = (Map<String, Object>) this.bean.get("listMap");
            Map<String, Object> requestBody = (Map<String, Object>) listMap.get("requestBody");
            List<Map> listDataMap = (List<Map>) listMap.get("listData");
            List<String> listHeader = new ArrayList<String>(
                    Arrays.asList(String.valueOf(requestBody.get("headers")).split(",")));
            List<String> listColumnName = new ArrayList<String>(
                    Arrays.asList(String.valueOf(requestBody.get("columns")).split(",")));
            String fromDate = String.valueOf(requestBody.get("from_date"));
            String toDate = String.valueOf(requestBody.get("to_date"));

            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            // FileInputStream templateFileStream = new FileInputStream(templateFile);
            XLSTransformer transformer = new XLSTransformer();
            transformer.setSpreadsheetToRename("sheetName", "Transaction Management");
            HSSFWorkbook workbook = new HSSFWorkbook(templateFileStream);
            HSSFSheet wSheet = workbook.getSheetAt(0);

            // SET STYLE FOR HEADER
            HSSFCellStyle headerRowStyle = workbook.createCellStyle();
            // headerRowStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
            // headerRowStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            // headerRowStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            // headerRowStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            // headerRowStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            // headerRowStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            headerRowStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            headerRowStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            headerRowStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            headerRowStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            HSSFFont headerFont = workbook.createFont();
            // headerFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
            headerFont.setFontHeight((short) 250);
            headerRowStyle.setFont(headerFont);

            HSSFFont dataFont = workbook.createFont();
            headerFont.setFontHeight((short) 200);

            // SET STYLE FOR DATA CELL
            HSSFCellStyle dataRowCenterStyle = workbook.createCellStyle();
            // dataRowCenterStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
            // dataRowCenterStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            // dataRowCenterStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            // dataRowCenterStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            // dataRowCenterStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            // dataRowCenterStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            dataRowCenterStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            dataRowCenterStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            dataRowCenterStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            dataRowCenterStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            dataRowCenterStyle.setFont(dataFont);

            HSSFCellStyle dataRowRightStyle = workbook.createCellStyle();
            // dataRowRightStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
            // dataRowRightStyle.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
            // dataRowRightStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            // dataRowRightStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            // dataRowRightStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            // dataRowRightStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            dataRowRightStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            dataRowRightStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            dataRowRightStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            dataRowRightStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            dataRowRightStyle.setFont(dataFont);

            HSSFCellStyle dataRowLeftStyle = workbook.createCellStyle();
            // dataRowLeftStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
            // dataRowLeftStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
            // dataRowLeftStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            // dataRowLeftStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            // dataRowLeftStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            // dataRowLeftStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            dataRowLeftStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            dataRowLeftStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            dataRowLeftStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            dataRowLeftStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            dataRowLeftStyle.setFont(dataFont);

            // create header row, header cell, bind list column
            HSSFRow timeRow = wSheet.createRow(6);
            timeRow.setHeight((short) 300);
            wSheet.setColumnWidth(5, 5500);
            HSSFCell cellTime = timeRow.createCell(5);
            cellTime.setCellValue("From: " + fromDate + "   To: " + toDate);

            HSSFRow headerRow = wSheet.createRow(8);
            headerRow.setHeight((short) 400);
            int startHeaderCell = 1;
            for (int i = 0; i < listHeader.size(); i++) {
                if (startHeaderCell > 1)
                    wSheet.setColumnWidth(startHeaderCell, 5500);
                HSSFCell cell = headerRow.createCell(startHeaderCell);
                cell.setCellValue(listHeader.get(i));
                cell.setCellStyle(headerRowStyle);
                startHeaderCell++;
            }

            // create data row, data cell, bind data
            int startDataRow = 9, startDataCell = 1;
            for (int i = 0; i < listDataMap.size(); i++) {
                Map<String, Object> dataMap = listDataMap.get(i);
                HSSFRow dataRow = wSheet.createRow(startDataRow);
                dataRow.setHeight((short) 300);
                for (int j = 0; j < listColumnName.size(); j++) {
                    HSSFCell cell = dataRow.createCell(startDataCell);
                    String dataCell = dataMap.get(listColumnName.get(j)) == null ? ""
                            : String.valueOf(dataMap.get(listColumnName.get(j)));
                    cell.setCellValue(dataCell);
                    if ("no".equals(listColumnName.get(j)) || "responseCode".equals(listColumnName.get(j))
                            || "invoiceState".equals(listColumnName.get(j))
                            || "transState".equals(listColumnName.get(j))) {
                        cell.setCellStyle(dataRowCenterStyle);
                    } else if ("originalAmount".equals(listColumnName.get(j))
                            || "transAmount".equals(listColumnName.get(j))) {
                        cell.setCellStyle(dataRowRightStyle);
                    } else {
                        cell.setCellStyle(dataRowLeftStyle);
                    }
                    startDataCell++;
                }
                startDataRow++;
                startDataCell = 1;
            }
            outputStream = new FileOutputStream(exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }

    /**
     * This new export mechanism only support 1 list
     *
     * @throws Exception
     */
    public void exportExcel2() throws Exception {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        XSSFWorkbook templateWorkbook = null;
        SXSSFWorkbook wb = null;
        FileOutputStream out = null;
        Exception e = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.info("TEMPLATE FILE: {}" + this.templateFilePath);
            templateWorkbook = new XSSFWorkbook(templateFileStream);
            XSSFSheet templateSheet = templateWorkbook.getSheetAt(0);
            wb = new SXSSFWorkbook(templateWorkbook, 100, true);

            boolean exportedList = false;

            for (Entry<String, Object> entry : this.bean.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    LOGGER.info("Create Excel Detail map "+entry.getKey());
                    CreateHugeXLSFile.createExcelDetail(templateSheet, (Map<String, Object>) entry.getValue(),
                            entry.getKey());
                } else if (entry.getValue() instanceof List && !exportedList) {
                    LOGGER.info("Create Excel Data list" + entry.getKey());
                    List<Map<String, Object>> listMapTemp = (List<Map<String, Object>>) entry.getValue();
                    SXSSFSheet sh = wb.getSheetAt(0);
                    sh.setDefaultColumnWidth(25);
                    sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk

                    // cell delete
                    if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                        CreateHugeXLSFile.deleteExcelRow(templateSheet, this.hiddenColumn, start);
                    }

                    CreateHugeXLSFile.createExcelData(wb, sh, templateSheet, listMapTemp, start);
                    exportedList = true;
                } else {
                    LOGGER.info("Create Excel Detail entry"+entry.getKey());
                    CreateHugeXLSFile.createExcelDetail(templateSheet, entry);
                }
            }
            // tinh lai formula khi bat file excel
            wb.setForceFormulaRecalculation(true);
            LOGGER.info("EXPORT FILE: {}" + this.exportFileName);
            out = new FileOutputStream(exportFileName);
            if (this.restyleNumber) {
                this.doRestyleNumber(wb, this.maxCols);
            }
            wb.write(out);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
            e = ex;
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            Util.closeResourse(LOGGER, templateFileStream, templateWorkbook, wb, out);
        }
        if (e != null)
            throw e;
    }

    public void exportExcelFormatAmount(List<String> columnFormat, List<String> currencyFormat, String currencyColumn)
            throws Exception {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE WITH FORMAT AMOUNT -----------------------");
        InputStream templateFileStream = null;
        XSSFWorkbook templateWorkbook = null;
        SXSSFWorkbook wb = null;
        FileOutputStream out = null;
        Exception e = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.info("TEMPLATE FILE WITH FORMAT AMOUNT : {}" + this.templateFilePath);
            templateWorkbook = new XSSFWorkbook(templateFileStream);
            XSSFSheet templateSheet = templateWorkbook.getSheetAt(0);
            wb = new SXSSFWorkbook(templateWorkbook, 100, true);

            boolean exportedList = false;

            for (Entry<String, Object> entry : this.bean.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    CreateHugeXLSFile.createExcelDetail(templateSheet, (Map<String, Object>) entry.getValue(),
                            entry.getKey());
                } else if (entry.getValue() instanceof List && !exportedList) {
                    List<Map<String, Object>> listMapTemp = (List<Map<String, Object>>) entry.getValue();
                    SXSSFSheet sh = wb.getSheetAt(0);
                    sh.setDefaultColumnWidth(25);
                    sh.setRandomAccessWindowSize(100);// keep 100 rows in memory, exceeding rows will be flushed to disk

                    // cell delete
                    if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                        CreateHugeXLSFile.deleteExcelRow(templateSheet, this.hiddenColumn, start);
                    }

                    CreateHugeXLSFile.createExcelDataFomatCurrency(wb, sh, templateSheet, listMapTemp, start,
                            columnFormat, currencyFormat, currencyColumn);
                    exportedList = true;
                } else {
                    CreateHugeXLSFile.createExcelDetail(templateSheet, entry);
                }
            }
            wb.setForceFormulaRecalculation(true);
            LOGGER.info("EXPORT FILE WITH FORMAT AMOUNT : {}" + this.exportFileName);
            out = new FileOutputStream(exportFileName);
            if (this.restyleNumber) {
                this.doRestyleNumber(wb, this.maxCols);
            }
            wb.write(out);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file WITH FORMAT AMOUNT ", ex);
            e = ex;
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE WITH FORMAT AMOUNT -------------------");
            Util.closeResourse(LOGGER, templateFileStream, templateWorkbook, wb, out);
        }
        if (e != null)
            throw e;
    }

    // format lai cell currency de hien thi du sau dau thap phan
    public void doRestyleNumber(SXSSFWorkbook wb, int maxCols) {
        DataFormat dataFormat = wb.createDataFormat();
        CellStyle cachedStyle = null;
        SXSSFSheet sh = wb.getSheetAt(0);
        int rowId = this.start;
        while (rowId <= sh.getLastRowNum()) {
            SXSSFRow row = sh.getRow(rowId);
            rowId++;
            if (row == null)
                continue;
            for (int colId = 0; colId < maxCols; colId++) {
                SXSSFCell cell = row.getCell(colId);
                if (cell == null)
                    continue;
                if (!cell.getCellTypeEnum().equals(CellType.NUMERIC))
                    continue;

                Double number = cell.getNumericCellValue();
                // LOGGER.info("row " + String.valueOf(rowId) + " col " + String.valueOf(colId)
                // + " amount " + String.valueOf(number) + " isInteger " +
                // Util.isInteger(number));
                if (!Util.isInteger(number)) {
                    // LOGGER.info(() -> "doReformatNumber " + number.toString());
                    if (cachedStyle == null) {
                        cachedStyle = wb.createCellStyle();
                        cachedStyle.cloneStyleFrom(cell.getCellStyle());
                        cachedStyle.setDataFormat(dataFormat.getFormat("#,##0.00"));
                    }

                    cell.setCellStyle(cachedStyle);
                }
                ;
            }
        }
    }

    public void exportExcelWithPassword(boolean isPassword, String password) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            LOGGER.log(Level.INFO, "DEBUG: Created XLSTransformer instance");
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            LOGGER.log(Level.INFO, "DEBUG: setSpreadsheetToRename");
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, this.bean);
            // Biff8EncryptionKey.setCurrentUserPassword("123456");
            LOGGER.log(Level.INFO, "DEBUG: transformXLS");
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            // workbook.writeProtectWorkbook(Biff8EncryptionKey.getCurrentUserPassword(),
            // "");
            if (isPassword) {
                HSSFSheet sheet = workbook.getSheetAt(0);
                sheet.protectSheet(password);
            }
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }

    public void exportByJasperReport(String typeFile) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE PDF BY JASPER-----------------------");
        InputStream templateFileStream = Thread.currentThread().getContextClassLoader()
                .getResourceAsStream("templates/jasper/temp_statistics_report_XLSX.jasper");
        try {
            JRBeanCollectionDataSource beanColDataSource = new JRBeanCollectionDataSource(listData);
            JasperReport jasperReport = (JasperReport) JRLoader.loadObject(templateFileStream);
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, beanColDataSource);
            // export PDF
            if (typeFile.equalsIgnoreCase("PDF")) {
                JasperExportManager.exportReportToPdfFile(jasperPrint, exportFileName);
            } else if (typeFile.equalsIgnoreCase("XLSX")) {
                JRXlsxExporter exporterXLS = new JRXlsxExporter();
                exporterXLS.setExporterInput(new SimpleExporterInput(jasperPrint));
                exporterXLS.setExporterOutput(new SimpleOutputStreamExporterOutput(exportFileName));
                SimpleXlsxReportConfiguration configuration = new SimpleXlsxReportConfiguration();
                configuration.setOnePagePerSheet(true);
                configuration.setDetectCellType(true);
                configuration.setCollapseRowSpan(false);
                exporterXLS.setConfiguration(configuration);
                exporterXLS.exportReport();
            }

        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export file by Jasper", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE BY JASPER-----------------------");

        }

    }

    public void exportPdf() {
        System.out.println("Start ....");
        try {
            LOGGER.log(Level.INFO, "File Name: " + exportFileName);
            URL sourceFileName = Thread.currentThread().getContextClassLoader().getResource(this.templateFilePath);
            LOGGER.log(Level.INFO, "sourceFileName: " + sourceFileName);
            List<JasperPrint> jasperPrintList = new ArrayList<JasperPrint>();

            for (Map<String, Object> map : listMap) {
                LOGGER.log(Level.INFO, "Map: " + map);
                JasperDesign design = JRXmlLoader.load(sourceFileName.getPath());
                JasperReport jasperReport = JasperCompileManager.compileReport(design);
                JasperPrint jprint = JasperFillManager.fillReport(jasperReport, map, getDataSource());
                jasperPrintList.add(jprint);
            }
            FileOutputStream fos = new FileOutputStream(exportFileName);

            JRPdfExporter exporter = new JRPdfExporter();
            exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, jasperPrintList);
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, fos);
            exporter.exportReport();

            fos.flush();
            fos.close();
            System.out.println("Done exporting reports to pdf");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void exportPdf2() {
        LOGGER.log(Level.INFO, "-------------START EXPORT PDF FILE-----------------------");
        FileOutputStream fos = null;
        try {
            LOGGER.log(Level.INFO, "File Name: " + exportFileName);
            URL sourceFileName = Thread.currentThread().getContextClassLoader().getResource(this.templateFilePath);
            LOGGER.log(Level.INFO, "Source File Name: " + sourceFileName);
            List<JasperPrint> jasperPrintList = new ArrayList<JasperPrint>();

            LOGGER.log(Level.INFO, "Map: " + parameters);
            JasperDesign design = JRXmlLoader.load(sourceFileName.getPath());
            LOGGER.log(Level.INFO, "Jasper design: " + design);
            JasperReport jasperReport = JasperCompileManager.compileReport(design);
            LOGGER.log(Level.INFO, "Jasper report: " + jasperReport);
            JasperPrint jprint = JasperFillManager.fillReport(jasperReport, parameters, getDataSource2());
            LOGGER.log(Level.INFO, "Jasper jprint: " + jasperReport);
            jasperPrintList.add(jprint);
            fos = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "File Output Stream: " + fos);

            // JasperExportManager.exportReportToPdfFile(jprint, exportFileName);

            JRPdfExporter exporter = new JRPdfExporter();
            exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, jasperPrintList);
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, fos);
            exporter.exportReport();
            LOGGER.log(Level.INFO, "JRPdfExporter: " + exporter);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error Export Pdf File: ", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT PDF FILE-----------------------");
            try {
                if (fos != null) {
                    fos.flush();
                    fos.close();
                }
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection Pdf File: ", e);
            }
        }
    }

    private static JRDataSource getDataSource2() {
        // Collection<Map> coll = new ArrayList<>();
        // Map params = new HashMap();
        // params.put("test", "test fucn ngon");
        // coll.add(params);

        return new JRBeanCollectionDataSource(listMap);
    }

    private static JRDataSource getDataSource() {
        Collection<Map> coll = new ArrayList<>();
        Map params = new HashMap();
        params.put("test", "test fucn ngon");
        coll.add(params);

        return new JRBeanCollectionDataSource(coll);
    }

    public void exportCsv(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader,
            List<Map> listMap, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(IConstants.NEW_LINE);
            // Write the CSV file header
            fileWriter.append(fileHeader);

            // Add a new line separator after the header
            fileWriter.append(IConstants.NEW_LINE);
            int rowNumber = 0;
            for (Map m : listMap) {
                rowNumber++;
                fileWriter.append(String.valueOf(rowNumber));
                fileWriter.append(IConstants.COMMA);
                for (String str : listHeader) {
                    String dataField = IConstants.BLANK;

                    Object data = m.get(str);
                    if (Timestamp.class.isInstance(data)) {
                        Timestamp timestamp = (Timestamp) data;
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                        dataField = dataField == null ? IConstants.BLANK
                                : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));
                    } else if (Double.class.isInstance(data)) {
                        dataField = String.valueOf((Double) data);
                    } else if (Integer.class.isInstance(data)) {
                        dataField = String.valueOf((Integer) data);
                    } else if (Long.class.isInstance(data)) {
                        dataField = String.valueOf((Long) data);
                    } else {

                        if (data == null || "null".equals(data.toString()) || data.toString().isEmpty()) {
                            dataField = IConstants.BLANK;
                        } else {
                            String dataObj = data.toString().replace("\"", "");
                            dataField = ("=\"" + dataObj + "\"");
                        }
                    }
                    // if (str.startsWith("D_")) {
                    // Timestamp timestamp = (Timestamp)m.get(str);
                    // SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                    // dataField = dateFormat.format(timestamp);
                    // dataField = dataField == null ? IConstants.BLANK
                    // : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));

                    // } else if (str.startsWith("N_")) {
                    // dataField = String.valueOf((Double)m.getOrDefault(str,""));
                    // } else {
                    // dataField = m.getOrDefault(str,"").toString();
                    // dataField = dataField == null ? IConstants.BLANK
                    // : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));
                    // }
                    fileWriter.append(dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : StringEscapeUtils.escapeCsv(dataField)));
                    fileWriter.append(IConstants.COMMA);
                }
                fileWriter.append(IConstants.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }

        }

    }

    public void exportCsvRn(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader,
        List<Map> listMap, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(IConstants.NEW_LINE);
            // Write the CSV file header
            fileWriter.append(fileHeader);

            // Add a new line separator after the header
            fileWriter.append(IConstants.NEW_LINE);
            //int rowNumber = 0;
            for (Map m : listMap) {
                // rowNumber++;
                // fileWriter.append(String.valueOf(rowNumber));
                // fileWriter.append(IConstants.COMMA);
                for (String str : listHeader) {
                    String dataField = IConstants.BLANK;

                    Object data = m.get(str);
                    if (Timestamp.class.isInstance(data)) {
                        Timestamp timestamp = (Timestamp) data;
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                        dataField = dataField == null ? IConstants.BLANK
                                : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));
                    } else if (Double.class.isInstance(data)) {
                        dataField = String.valueOf((Double) data);
                    } else if (Integer.class.isInstance(data)) {
                        dataField = String.valueOf((Integer) data);
                    } else if (Long.class.isInstance(data)) {
                        dataField = String.valueOf((Long) data);
                    } else {

                        if (data == null || "null".equals(data.toString()) || data.toString().isEmpty()) {
                            dataField = IConstants.BLANK;
                        } else {
                            String dataObj = data.toString().replace("\"", "");
                            dataField = ("=\"" + dataObj + "\"");
                        }
                    }
                    
                    fileWriter.append(dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : StringEscapeUtils.escapeCsv(dataField)));
                    fileWriter.append(IConstants.COMMA);
                }
                fileWriter.append(IConstants.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }

        }
    }


    /*
     * 
     */
    public void exportCsvSpecsColumns(String fileTitle, String fileDate, ArrayList<String> listHeader,
            List<Map> listMap, Map<String, String> mapDate, Map<String, String> mapColumnHeader,
            List<String> listColumn) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV WITH SPECIFICED COLUMNS-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(IConstants.NEW_LINE);

            // Write the CSV file header
            StringBuilder fileHeader = new StringBuilder();
            fileHeader.append("No,");
            for (String col : listColumn) {
                fileHeader.append(mapColumnHeader.get(col));
                fileHeader.append(",");
                listHeader.add(col);
            }
            fileWriter.append(fileHeader.toString());

            // Add a new line separator after the header
            fileWriter.append(IConstants.NEW_LINE);
            int rowNumber = 0;
            for (Map m : listMap) {
                rowNumber++;
                fileWriter.append(String.valueOf(rowNumber));
                fileWriter.append(IConstants.COMMA);
                for (String str : listHeader) {
                    String dataField = IConstants.BLANK;

                    Object data = m.get(str);
                    if (Timestamp.class.isInstance(data)) {
                        Timestamp timestamp = (Timestamp) data;
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                        dataField = dataField == null ? IConstants.BLANK
                                : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));
                    } else if (Double.class.isInstance(data)) {
                        dataField = String.valueOf((Double) data);
                    } else if (Integer.class.isInstance(data)) {
                        dataField = String.valueOf((Integer) data);
                    } else if (Long.class.isInstance(data)) {
                        dataField = String.valueOf((Long) data);
                    } else {

                        if (data == null || "null".equals(data.toString()) || data.toString().isEmpty()) {
                            dataField = IConstants.BLANK;
                        } else {
                            String dataObj = data.toString().replace("\"", "");
                            dataField = ("=\"" + dataObj + "\"");
                        }
                    }
                    fileWriter.append(dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : StringEscapeUtils.escapeCsv(dataField)));
                    fileWriter.append(IConstants.COMMA);
                }
                fileWriter.append(IConstants.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }

        }

    }

    public void exportCsv(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader,
            RowSet resultSet, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(IConstants.NEW_LINE);
            // Write the CSV file header
            fileWriter.append(fileHeader);

            // Add a new line separator after the header
            fileWriter.append(IConstants.NEW_LINE);
            int rowNumber = 0;
            while (resultSet.next()) {
                rowNumber++;
                fileWriter.append(String.valueOf(rowNumber));
                fileWriter.append(IConstants.COMMA);
                for (String str : listHeader) {
                    String dataField = IConstants.BLANK;
                    if (str.startsWith("D_")) {
                        LOGGER.info("str := " + str);
                        // LOGGER.info("str value:= " +strresultSet.getObject(str).toString());
                        Timestamp timestamp = resultSet.getTimestamp(str);
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                        dataField = dataField == null ? IConstants.BLANK
                                : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));

                    } else if (str.startsWith("N_")) {
                        dataField = String.valueOf(resultSet.getDouble(str));
                    } else {
                        dataField = resultSet.getString(str);
                        dataField = dataField == null ? IConstants.BLANK
                                : ("null".equals(dataField) ? IConstants.BLANK : ("=\"" + dataField + "\""));
                    }
                    fileWriter.append(dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : StringEscapeUtils.escapeCsv(dataField)));
                    fileWriter.append(IConstants.COMMA);
                }
                fileWriter.append(IConstants.NEW_LINE);
            }

        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }

        }

    }

    public void exportCsvFile(String fileTitle, String fileDate, String fileHeader, ArrayList<String> listHeader,
            List<Map> listData, Map<String, String> mapDate) throws IOException, SQLException {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE CSV-----------------------");
        // FileWriter fileWriter = null;
        Writer fileWriter = null;
        try {
            fileWriter = new OutputStreamWriter(new FileOutputStream(exportFileName), StandardCharsets.UTF_8);
            fileWriter.append("sep=,"); // FOR EXCEL 2010
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileTitle);
            fileWriter.append(IConstants.NEW_LINE);
            fileWriter.append(fileDate);
            fileWriter.append(IConstants.NEW_LINE);
            // Write the CSV file header
            fileWriter.append(fileHeader);

            // Add a new line separator after the header
            fileWriter.append(IConstants.NEW_LINE);
            int rowNumber = 0;
            for (Map data : listData) {
                rowNumber++;
                String row = String.valueOf(rowNumber);
                row = ("\"" + row + "\"");
                fileWriter.append(row);
                fileWriter.append(IConstants.COMMA);
                for (String str : listHeader) {
                    String dataField = IConstants.BLANK;
                    // dataField = ;
                    if (data == null) {
                        dataField = "";
                    } else if (String.class.isInstance(data.get(str))) {
                        dataField = "" + data.get(str).toString();
                    } else if (Integer.class.isInstance(data.get(str))) {
                        dataField = "" + data.get(str);
                    } else if (Double.class.isInstance(data.get(str))) {
                        dataField = "" + data.get(str);
                    } else if (Long.class.isInstance(data.get(str))) {
                        dataField = "" + data.get(str);
                    } else if (Timestamp.class.isInstance(data.get(str))) {
                        Timestamp timestamp = (Timestamp) data;
                        SimpleDateFormat dateFormat = new SimpleDateFormat(mapDate.get(str));
                        dataField = dateFormat.format(timestamp);
                    }
                    dataField = dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : ("\"" + dataField + "\""));
                    fileWriter.append(dataField == null ? IConstants.BLANK
                            : ("null".equals(dataField) ? IConstants.BLANK : dataField));
                    fileWriter.append(IConstants.COMMA);
                }
                fileWriter.append(IConstants.NEW_LINE);
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            try {
                fileWriter.flush();
                fileWriter.close();
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "Error Close File Writer ", e);
            }
        }
    }

    public void exportWord(String templateFileName, Map data) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE WORD-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(templateFileName);
            LOGGER.log(Level.INFO, "TEMPLATE FILE WORD : " + templateFileName);
            XWPFDocument document = new XWPFDocument(templateFileStream);
            document = replaceTextInWord(document, data);
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE WORD : " + exportFileName);
            document.write(outputStream);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export word file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------END EXPORT FILE WORD-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
        }

    }

    public static XWPFDocument replaceTextInWord(XWPFDocument document, Map data) {
        try {
            document.getParagraphs().forEach(p -> {
                p.getRuns().forEach(run -> {
                    String text = run.getText(0);
                    data.forEach((k, v) -> {
                        if (text.contains(k.toString())) {
                            run.setText(text.replace(k.toString(), v.toString()), 0);
                        }
                    });

                });
            });
            replaceTextInTable(document, data);
        } catch (Exception e) {
            e.getMessage();
        }
        return document;
    }

    public static XWPFDocument replaceTextInTable(XWPFDocument document, Map data) {

        for (XWPFTable tbl : document.getTables()) {
            for (XWPFTableRow row : tbl.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph p : cell.getParagraphs()) {
                        for (XWPFRun r : p.getRuns()) {
                            data.forEach((k, v) -> {
                                String text = r.getText(0);
                                // logger.info(text);
                                if (text != null && text.contains(k.toString())) {
                                    text = text.replace(k.toString(), v.toString());
                                    r.setText(text, 0);
                                }
                            });
                        }
                    }
                }
            }
        }

        return document;
    }

    public void exportWord(Map contractData) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XWPFDocument document = new XWPFDocument(templateFileStream);

            String contractCode = contractData.get("contractCode").toString();

            List<Map> subDataMerchant = (List<Map>) contractData.get("subDataMerchant");
            List<Map> subDataNoMerchant = (List<Map>) contractData.get("subDataNoMerchant");
            List<Map> subDataParentTable = (List<Map>) contractData.get("subDataParentTable");
            List<Map> subDataFeeTable = (List<Map>) contractData.get("subDataFeeTable");
            List<Map> subDataPauseTable = (List<Map>) contractData.get("subDataPauseTable");
            List<Map> subDataShopifyTable = (List<Map>) contractData.get("subShopifyData");
            List<Map> subDataNonShopifyTable = (List<Map>) contractData.get("subNonShopifyData");

            document = replaceTextInWord(document, "merchantTable", "", subDataMerchant, subDataNoMerchant,
                    subDataParentTable, subDataFeeTable, subDataPauseTable, 1, contractCode, contractData);
            document = replaceTextInWord(document, "parentMerchantTable", "", subDataMerchant, subDataNoMerchant,
                    subDataParentTable, subDataFeeTable, subDataPauseTable, 1, contractCode, contractData);
            document = replaceTextInWord(document, "merchanttable", "", subDataMerchant, subDataNoMerchant,
                    subDataParentTable, subDataFeeTable, subDataPauseTable, 1, contractCode, contractData);
            document = replaceTextInWord(document, "feeTableVCB", "", subDataMerchant, subDataNoMerchant,
                    subDataParentTable, subDataFeeTable, subDataPauseTable, 1, contractCode, contractData);
            document = replaceTextInWord(document, "", "", subDataMerchant, subDataNoMerchant, subDataParentTable,
                    subDataFeeTable, subDataPauseTable, 0, contractCode, contractData);

            if (contractCode.equals("PL16")) {
                document = replaceTextInWordShopify(document, "subDataShopifyTable", "", subDataShopifyTable, 0, "PL16",
                        contractData);
                document = replaceTextInWordShopify(document, "subDataNonShopifyTable", "", subDataNonShopifyTable, 0,
                        "PL16", contractData);
            }
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + exportFileName);
            document.write(outputStream);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export word file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
        }
    }

    private static XWPFDocument replaceTextInWord(XWPFDocument document, String findTxt, String replaceTxt,
            List<Map> subDataMerchant, List<Map> subDataNoMerchant, List<Map> subDataParentTable,
            List<Map> subDataFeeTable, List<Map> subDataPauseTable, int typeFillter, String contractCode,
            Map contractData) {

        if (typeFillter == 0) {
            contractData.forEach((key, value) -> {
                String findText = key != null ? key.toString() : "";
                String replaceText = value != null ? value.toString() : "";
                document.getParagraphs().forEach(p -> {
                    p.getRuns().forEach(run -> {
                        String text = run.text();
                        if (text.contains(findText)) {
                            run.setText(text.replace(findText, replaceText), 0);
                        }
                    });
                });

                replaceTextInTable(document, findText, replaceText, contractCode);
            });
            if (contractCode.startsWith("HD13_TH") || contractCode.startsWith("HD13_Shopify")) {
                document.getFooterList().forEach(f -> {
                    f.getParagraphs().forEach(p -> {
                        p.getRuns().forEach(r -> {
                            String text = r.text();
                            if (text.contains("contractIdTxt")) {
                                r.setText(text.replace("contractIdTxt",
                                        String.valueOf(contractData.get("contractIdTxt"))), 0);
                            }
                        });
                    });
                });
            }
        } else {
            document.getParagraphs().forEach(p -> {
                p.getRuns().forEach(run -> {
                    String text = run.text();
                    if (text.contains(findTxt)) {
                        run.setText(text.replace(findTxt, replaceTxt), 0);
                        // create a new table
                        addTableMerchant(p, subDataMerchant, subDataNoMerchant, subDataParentTable, subDataPauseTable,
                                contractCode, findTxt);
                    }
                });
            });
            // replace Text in table fee
            if (contractCode.equals("HD09") || contractCode.equals("PL09") || contractCode.startsWith("HD13_TH")
                    || contractCode.startsWith("HD13_Shopify")) {
                subDataFeeTable.forEach(data -> {
                    data.forEach((keyFee, valueFee) -> {
                        String keyText = keyFee != null ? keyFee.toString() : "";
                        String valueText = valueFee != null ? valueFee.toString() : "";
                        replaceTextInTable(document, keyText, valueText, contractCode);
                    });
                });
            }
        }

        return document;
    }

    private static XWPFDocument replaceTextInWordShopify(XWPFDocument document, String findTxt, String replaceTxt,
            List<Map> subDataShopifyMerchant, int typeFillter, String contractCode, Map contractData) {

        if (typeFillter == 0) {
            contractData.forEach((key, value) -> {
                String findText = key != null ? key.toString() : "";
                String replaceText = value != null ? value.toString() : "";
                document.getParagraphs().forEach(p -> {
                    p.getRuns().forEach(run -> {
                        String text = run.text();
                        if (text.contains(findText)) {
                            run.setText(text.replace(findText, replaceText), 0);
                        }
                    });
                });

                replaceTextInTable(document, findText, replaceText, contractCode);
            });

            document.getParagraphs().forEach(p -> {
                p.getRuns().forEach(run -> {
                    String text = run.text();
                    if (text.contains(findTxt)) {
                        run.setText(text.replace(findTxt, replaceTxt), 0);
                        // create a new table
                        addTableShopifyMerchant(p, subDataShopifyMerchant, findTxt);
                    }
                });
            });
        }

        return document;
    }

    private static void addTableMerchant(XWPFParagraph p, List<Map> subDataMerchant, List<Map> subDataNoMerchant,
            List<Map> subDataParentTable, List<Map> subDataPauseTable, String contractCode, String findText) {
        List<Map> subData = null;
        boolean fourColumns = false;

        if (!contractCode.equals("PL11-2BEN") && !contractCode.equals("PL11-3BEN") && !contractCode.equals("PL15-2BEN")
                && !contractCode.equals("PL15-3BEN") && !contractCode.equals("BBTL-2BEN")
                && !contractCode.equals("BBTL-3BEN") && !contractCode.equals("BBTL-CUP") && !contractCode.equals("HD05")
                && !contractCode.equals("BBNT-HD03-01") && !contractCode.equals("BBNT-HD04")
                && !contractCode.equals("BBNT-HD05") && !contractCode.equals("BBNT-HD06")
                && !contractCode.equals("BBNT-HD08") && !contractCode.equals("PL06")) {
            fourColumns = true;
        }

        // init Map Date
        if (!contractCode.equals("HD05") && !contractCode.equals("PL11-2BEN") && !contractCode.equals("PL11-3BEN")
                && !contractCode.equals("PL15-2BEN") && !contractCode.equals("PL15-3BEN")
                && !contractCode.equals("BBTL-2BEN") && !contractCode.equals("BBTL-3BEN")
                && !contractCode.equals("BBNT-HD03-01") && !contractCode.equals("BBNT-HD04")
                && !contractCode.equals("BBNT-HD05") && !contractCode.equals("BBNT-HD06")
                && !contractCode.equals("BBNT-HD08") && !contractCode.equals("PL06") && !contractCode.equals("BBTL-CUP")
                && !findText.equals("parentMerchantTable")) {
            subData = subDataMerchant;
        } else if (!contractCode.equals("HD05") && !contractCode.equals("PL11-2BEN")
                && !contractCode.equals("PL11-3BEN") && !contractCode.equals("PL15-2BEN")
                && !contractCode.equals("PL15-3BEN") && !contractCode.equals("BBTL-HD04")
                && !contractCode.equals("BBTL-3BEN") && !contractCode.equals("BBNT-HD03-01")
                && !contractCode.equals("BBNT-HD04") && !contractCode.equals("BBNT-HD05")
                && !contractCode.equals("BBNT-HD06") && !contractCode.equals("BBNT-HD08")
                && !contractCode.equals("PL06") && !contractCode.equals("BBTL-CUP")
                && findText.equals("parentMerchantTable")) {
            subData = subDataParentTable;
        } else if (contractCode.equals("PL11-2BEN") || contractCode.equals("PL11-3BEN")
                || contractCode.equals("PL15-2BEN") || contractCode.equals("PL15-3BEN")
                || contractCode.equals("BBTL-2BEN") || contractCode.equals("BBTL-3BEN")
                || contractCode.equals("BBTL-CUP") || contractCode.equals("BBNT-HD03-01")
                || contractCode.equals("BBNT-HD04") || contractCode.equals("BBNT-HD05")
                || contractCode.equals("BBNT-HD06") || contractCode.equals("BBNT-HD08")) {
            subData = subDataPauseTable;
        } else {
            subData = subDataNoMerchant;
        }
        XmlCursor cursor = p.getCTP().newCursor();
        XWPFTable table = p.getBody().insertNewTbl(cursor);
        if (fourColumns == true) {
            table.setCellMargins(100, 200, 100, 200);
        } else {
            table.setCellMargins(50, 50, 50, 400);
        }

        // /create first row
        XWPFTableRow tableRowOne = table.getRow(0);
        tableRowOne.addNewTableCell();
        tableRowOne.addNewTableCell();
        tableRowOne.addNewTableCell();
        if (fourColumns == true) {
            tableRowOne.addNewTableCell();
        }

        XWPFParagraph p0 = tableRowOne.getCell(0).getParagraphs().get(0);
        p0.setAlignment(ParagraphAlignment.LEFT);
        // p0.setSpacingBefore(1);
        // p0.setSpacingAfter(1);
        XWPFParagraph p1 = tableRowOne.getCell(1).getParagraphs().get(0);
        p1.setAlignment(ParagraphAlignment.LEFT);
        // p1.setSpacingBefore(1);
        // p1.setSpacingAfter(1);
        XWPFParagraph p2 = tableRowOne.getCell(2).getParagraphs().get(0);
        p2.setAlignment(ParagraphAlignment.LEFT);
        // p2.setSpacingBefore(1);
        // p2.setSpacingAfter(1);
        XWPFParagraph p3 = tableRowOne.getCell(3).getParagraphs().get(0);
        p3.setAlignment(ParagraphAlignment.LEFT);
        // p3.setSpacingBefore(1);
        // p3.setSpacingAfter(1);

        XWPFRun r0 = p0.createRun();
        XWPFRun r1 = p1.createRun();
        XWPFRun r2 = p2.createRun();
        XWPFRun r3 = p3.createRun();

        r0.setText("STT");
        r0.setFontSize(10);
        r1.setText(contractCode.equals("HD05") || contractCode.equals("PL06") ? "Số tài khoản"
                : contractCode.equals("PL11") ? "Merchant name" : "Merchant name");
        r1.setFontSize(10);
        r2.setText(contractCode.equals("HD05") || contractCode.equals("PL06") ? "Tên tài khoản"
                : contractCode.equals("PL11") || contractCode.equals("BBNT-HD05") || contractCode.equals("BBNT-HD03-01")
                        || contractCode.equals("BBTL-3BEN") || contractCode.equals("BBTL-2BEN") ? "Merchant ID"
                                : "Số tài khoản");
        r2.setFontSize(10);
        r3.setText(contractCode.equals("HD05") || contractCode.equals("PL06") ? "Ngân hàng - chi nhánh"
                : contractCode.equals("PL11-2BEN") || contractCode.equals("PL11-3BEN")
                        || contractCode.equals("BBTL-2BEN") || contractCode.equals("BBTL-3BEN")
                        || contractCode.equals("BBTL-CUP") || contractCode.equals("BBNT-HD03-01")
                        || contractCode.equals("BBNT-HD04") || contractCode.equals("BBNT-HD05")
                        || contractCode.equals("BBNT-HD06") || contractCode.equals("BBNT-HD08") ? "Số hiệu đơn vị"
                                : "Tên tài khoản");
        r3.setFontSize(10);

        r0.setBold(true);
        r1.setBold(true);
        r2.setBold(true);
        r3.setBold(true);

        if (fourColumns == true) {
            XWPFParagraph p4 = tableRowOne.getCell(4).getParagraphs().get(0);
            p4.setAlignment(ParagraphAlignment.CENTER);
            p4.setSpacingBefore(1);
            p4.setSpacingAfter(1);
            XWPFRun r4 = p4.createRun();
            r4.setText("Ngân hàng - chi nhánh");
            r4.setFontSize(10);
            r4.setBold(true);
        }

        for (int i = 0; i < subData.size(); i++) {
            XWPFTableRow tableRow = table.createRow();
            XWPFParagraph pp0 = tableRow.getCell(0).getParagraphs().get(0);
            pp0.setAlignment(ParagraphAlignment.CENTER);
            XWPFParagraph pp1 = tableRow.getCell(1).getParagraphs().get(0);
            XWPFParagraph pp2 = tableRow.getCell(2).getParagraphs().get(0);
            XWPFParagraph pp3 = tableRow.getCell(3).getParagraphs().get(0);
            if (fourColumns == true) {
                XWPFParagraph pp4 = tableRow.getCell(4).getParagraphs().get(0);
                XWPFRun rr4 = pp4.createRun();
                rr4.setText(subData.get(i).get("data04").toString());
                rr4.setFontSize(10);
            }

            XWPFRun rr0 = pp0.createRun();
            XWPFRun rr1 = pp1.createRun();
            XWPFRun rr2 = pp2.createRun();
            XWPFRun rr3 = pp3.createRun();

            rr0.setText("" + (i + 1));
            rr0.setFontSize(10);
            rr1.setText(subData.get(i).get("data01").toString());
            rr1.setFontSize(10);
            rr2.setText(subData.get(i).get("data02").toString());
            rr2.setFontSize(10);
            rr3.setText(subData.get(i).get("data03").toString());
            rr3.setFontSize(10);

        }
    }

    private static void addTableShopifyMerchant(XWPFParagraph p, List<Map> subDataShopify, String findText) {
        List<Map> subData = null;
        // List<Map> subDataShopify = null;

        // init Map Date
        subData = subDataShopify;
        XmlCursor cursor = p.getCTP().newCursor();
        XWPFTable table = p.getBody().insertNewTbl(cursor);

        table.setCellMargins(50, 100, 100, 800);

        // /create first row
        XWPFTableRow tableRowOne = table.getRow(0);
        tableRowOne.addNewTableCell();
        tableRowOne.addNewTableCell();
        // tableRowOne.addNewTableCell();

        XWPFParagraph p0 = tableRowOne.getCell(0).getParagraphs().get(0);
        p0.setAlignment(ParagraphAlignment.LEFT);
        // p0.setSpacingBefore(1);
        // p0.setSpacingAfter(1);
        XWPFParagraph p1 = tableRowOne.getCell(1).getParagraphs().get(0);
        p1.setAlignment(ParagraphAlignment.LEFT);
        // p1.setSpacingBefore(1);
        // p1.setSpacingAfter(1);
        XWPFParagraph p2 = tableRowOne.getCell(2).getParagraphs().get(0);
        p2.setAlignment(ParagraphAlignment.LEFT);
        // p2.setSpacingBefore(1);
        // p2.setSpacingAfter(1);

        XWPFRun r0 = p0.createRun();
        XWPFRun r1 = p1.createRun();
        XWPFRun r2 = p2.createRun();
        // XWPFRun r3 = p3.createRun();

        r0.setText("STT");
        r0.setFontSize(10);
        r1.setText("Merchant name");
        r1.setFontSize(10);
        r2.setText("Merchant ID");
        r2.setFontSize(10);

        r0.setBold(true);
        r1.setBold(true);
        r2.setBold(true);

        for (int i = 0; i < subData.size(); i++) {
            XWPFTableRow tableRow = table.createRow();
            XWPFParagraph pp0 = tableRow.getCell(0).getParagraphs().get(0);
            pp0.setAlignment(ParagraphAlignment.CENTER);
            XWPFParagraph pp1 = tableRow.getCell(1).getParagraphs().get(0);
            XWPFParagraph pp2 = tableRow.getCell(2).getParagraphs().get(0);

            XWPFRun rr0 = pp0.createRun();
            XWPFRun rr1 = pp1.createRun();
            XWPFRun rr2 = pp2.createRun();
            // XWPFRun rr3 = pp3.createRun();

            rr0.setText("" + (i + 1));
            rr0.setFontSize(10);
            rr1.setText(subData.get(i).get("data01").toString());
            rr1.setFontSize(10);
            rr2.setText(subData.get(i).get("data02").toString());
            rr2.setFontSize(10);

        }
    }

    private static void replaceTextInTable(XWPFDocument document, String findText, String replaceText,
            String contractCode) {
        for (int d = 0; d < document.getTables().size(); d++) {
            for (int i = 0; i < document.getTables().get(d).getRows().size(); i++) {
                for (int c = 0; c < document.getTables().get(d).getRows().get(i).getTableCells().size(); c++) {
                    for (int p = 0; p < document.getTables().get(d).getRows().get(i).getTableCells().get(c)
                            .getParagraphs().size(); p++) {
                        for (int r = 0; r < document.getTables().get(d).getRows().get(i).getTableCells().get(c)
                                .getParagraphs().get(p).getRuns().size(); r++) {
                            String text = document.getTables().get(d).getRows().get(i).getTableCells().get(c)
                                    .getParagraphs().get(p).getRuns().get(r).text();

                            if (text.contains(findText)) {
                                document.getTables().get(d).getRows().get(i).getTableCells().get(c).getParagraphs()
                                        .get(p).getRuns().get(r).setText(text.replace(findText, replaceText), 0);
                                if (contractCode.equals("HD09") || contractCode.equals("PL09")
                                        || contractCode.equals("PL16")
                                        || contractCode.equals("PL01") || contractCode.equals("HD03-01")
                                        || (contractCode.startsWith("HD13_TH") && findText.endsWith("Tbl"))
                                        || (contractCode.startsWith("HD13_Shopify") && findText.endsWith("Tbl"))) {
                                    document.getTables().get(d).getRows().get(i).getTableCells().get(c).getParagraphs()
                                            .get(p).getRuns().get(r).setFontSize(10);
                                } else {
                                    document.getTables().get(d).getRows().get(i).getTableCells().get(c).getParagraphs()
                                            .get(p).getRuns().get(r).setFontSize(12);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void exportExcelTbtu(String file) {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            LOGGER.log(Level.INFO, "DEBUG: Created XLSTransformer instance");
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            LOGGER.log(Level.INFO, "DEBUG: setSpreadsheetToRename");
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                transformer
                        .setColumnPropertyNamesToHide(this.hiddenColumn.toArray(new String[this.hiddenColumn.size()]));
            }
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, this.bean);
            if (file.equals("0")) {
                workbook.removeSheetAt(1);
            }
            LOGGER.log(Level.INFO, "DEBUG: transformXLS");
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }

    public void exportDataOnly(String fileName, List<Map> lines, String extension) {
        OutputStream os = null;
        try {
            os = new FileOutputStream(exportFileName);
            if ("xlsx".equalsIgnoreCase(extension) || "xls".equalsIgnoreCase(extension)) {
                LOGGER.log(Level.INFO, "------------- EXPORT DATA ONLY -------------------");
                Workbook wb = getWorkbook(fileName);
                try {
                    Sheet sheet = wb.createSheet(fileName);
                    if (!lines.isEmpty()) {
                        Map mapHeader = lines.get(0);
                        String header = String.valueOf(mapHeader.get("S_LINE"));
                        String[] metaHeader = header.split("\\||,");
                        Row rowHeader = sheet.createRow(0);
                        for (int rowHead = 0; rowHead < metaHeader.length; rowHead++) {
                            String metaData = metaHeader[rowHead].replaceAll("[\"]", "");
                            Cell cell = rowHeader.createCell(rowHead);
                            writeCell(cell, metaData);
                        }
                        for (int i = 1; i < lines.size(); i++) {
                            Map mapContent = lines.get(i);
                            String line = String.valueOf(mapContent.get("S_LINE"));
                            String[] dataContent = line.split("\\||,|\",\"");
                            Row row = sheet.createRow(i);
                            for (int j = 0; j < dataContent.length; j++) {
                                String metaData = dataContent[j].replaceAll("[\"]", "");
                                Cell cell = row.createCell(j);
                                writeCell(cell, metaData);
                            }
                        }
                        wb.write(os);
                    }
                } catch (Exception ex) {
                    LOGGER.log(Level.WARNING, "Error export excel file EXPORT DATA ONLY ", ex);
                } finally {
                    LOGGER.log(Level.INFO, "-------------DONE EXPORT DATA ONLY -------------------");
                    try {
                        if (os != null)
                            os.close();
                    } catch (Exception e) {
                        LOGGER.log(Level.WARNING, "Error Close Connection File EXPORT DATA ONLY ", e);
                    }
                }
            } else {
                LOGGER.log(Level.INFO, "------------- EXPORT DATA ONLY (OTHER) -------------------");
                for (int i = 0; i < lines.size(); i++) {
                    Map line = lines.get(i);
                    os.write(String.valueOf(line.get("S_LINE")).getBytes());
                    os.write("\n".getBytes());
                }
                os.close();
            }
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "", e);
        }
    }

    private static Workbook getWorkbook(String excelFilePath) throws Exception {
        Workbook workbook = null;

        if (excelFilePath.endsWith("xlsx")) {
            workbook = new XSSFWorkbook();
        } else if (excelFilePath.endsWith("xls")) {
            workbook = new HSSFWorkbook();
        } else {
            throw new IllegalArgumentException("The specified file is not Excel file");
        }

        return workbook;
    }

    private static void writeCell(Cell c, Object fieldValue) {
        if (fieldValue == null) {
            c.setCellValue("");
        } else if (fieldValue instanceof String) {
            c.setCellValue(fieldValue.toString());
        } else if (fieldValue instanceof Date) {
            c.setCellValue((Date) fieldValue);
        } else if (fieldValue instanceof Timestamp) {
            Timestamp d = (Timestamp) fieldValue;
            c.setCellValue(new Date(d.getTime()));
        } else if (fieldValue instanceof Integer) {
            c.setCellValue((Integer) fieldValue);
        } else if (fieldValue instanceof Boolean) {
            c.setCellValue((Boolean) fieldValue);
        } else if (fieldValue instanceof Long) {
            c.setCellValue((Long) fieldValue);
        } else if (fieldValue instanceof Double) {
            c.setCellValue((Double) fieldValue);
        } else {
            c.setCellValue(fieldValue.toString());
        }
    }

    public void exportExcelMultiSheetWithImage() {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XLSTransformer transformer = new XLSTransformer();
            LOGGER.log(Level.INFO, "DEBUG: Created XLSTransformer instance");
            transformer.setSpreadsheetToRename("sheetname", "CPR");
            LOGGER.log(Level.INFO, "DEBUG: setSpreadsheetToRename");
            if (this.hiddenColumn != null && !this.hiddenColumn.isEmpty()) {
                transformer
                        .setColumnPropertyNamesToHide(this.hiddenColumn.toArray(new String[this.hiddenColumn.size()]));
            }
            // Picture p;
            HSSFWorkbook workbook = (HSSFWorkbook) transformer.transformXLS(templateFileStream, templateSheetNameList,
                    sheetNameList, this.listBean);
            HSSFSheet my_sheet = workbook.getSheet("Biểu đồ");
            /* Add Picture to workbook and get a index for the picture */
            int my_picture_id = workbook.addPicture(image, Workbook.PICTURE_TYPE_JPEG);
            /* Close Input Stream */
            /* Create the drawing container */
            HSSFPatriarch drawing = my_sheet.createDrawingPatriarch();
            /* Create an anchor point */
            ClientAnchor my_anchor = new HSSFClientAnchor();
            /* Define top left corner, and we can resize picture suitable from there */
            my_anchor.setCol1(0);
            my_anchor.setRow1(4);
            /* Invoke createPicture and pass the anchor point and ID */
            HSSFPicture my_picture = drawing.createPicture(my_anchor, my_picture_id);
            /* Call resize method, which resizes the image */
            my_picture.resize();
            LOGGER.log(Level.INFO, "DEBUG: transformXLS");
            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + this.exportFileName);
            workbook.write(outputStream);

        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }

        }

    }
    
    public void excelUPOSAnalysis(String fromDate, String toDate) throws Exception {
        InputStream templateFileStream = null;
        XSSFWorkbook wb = null;
        FileOutputStream out = null;
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.info("TEMPLATE FILE: {}" + this.templateFilePath);
            wb = new XSSFWorkbook(templateFileStream);
            Sheet sheet = wb.getSheetAt(0);
            
           
            List<EUposReportItem> lstResult = (List<EUposReportItem>) this.bean.get("dataUPOS");
            Row rowfromDate = sheet.createRow(2);
            rowfromDate.createCell(4).setCellValue("From: "+fromDate);
            rowfromDate.createCell(5).setCellValue("To: "+toDate);
            int startRow = 6; 
            int currentRowIdx = startRow;
            int rowNum = 1;
            // Ghi từng dòng dữ liệu
            for (EUposReportItem item : lstResult) {
                Row row = sheet.createRow(currentRowIdx++);
                row.createCell(0).setCellValue(rowNum);
                row.createCell(1).setCellValue(getSafe(item.getMerchantName()));
                row.createCell(2).setCellValue(getSafe(item.getPartnerName()));
                row.createCell(3).setCellValue(getSafe(item.getAcceptanceDate()));
                row.createCell(4).setCellValue(getSafe(item.getAmountDoanhSoCard()));
                row.createCell(5).setCellValue(getSafe(item.getAmountDoanhSoITA()));
                row.createCell(6).setCellValue(getSafe(item.getAmountDoanhSoQR()));
                row.createCell(7).setCellValue(getSafe(item.getTotalAmountDoanhSo()));
                row.createCell(8).setCellValue(getSafe(item.getTotalFeeOPCard()));
                row.createCell(9).setCellValue(getSafe(item.getTotalFeeOPITA()));
                row.createCell(10).setCellValue(getSafe(item.getTotalFeeOPUPOS()));
                row.createCell(11).setCellValue(getSafe(item.getTotalFeeOP()));
                row.createCell(12).setCellValue(getSafe(item.getPercentageBenefit()));
                rowNum++;
            }

            // Tính tổng cộng
            EUposReportItem totalItem = calculateTotal(lstResult);

            // Ghi dòng tổng cộng
            Row totalRow = sheet.createRow(currentRowIdx);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIdx, currentRowIdx, 0, 3));

            Cell totalLabel = totalRow.createCell(0);
            totalLabel.setCellValue("Tổng cộng");

            totalRow.createCell(4).setCellValue(totalItem.getAmountDoanhSoCard());
            totalRow.createCell(5).setCellValue(totalItem.getAmountDoanhSoITA());
            totalRow.createCell(6).setCellValue(totalItem.getAmountDoanhSoQR());
            totalRow.createCell(7).setCellValue(totalItem.getTotalAmountDoanhSo());
            totalRow.createCell(8).setCellValue(totalItem.getTotalFeeOPCard());
            totalRow.createCell(9).setCellValue(totalItem.getTotalFeeOPITA());
            totalRow.createCell(10).setCellValue(totalItem.getTotalFeeOPUPOS());
            totalRow.createCell(11).setCellValue(totalItem.getTotalFeeOP());
            // Set màu cho dòng tổng cộng
            setTotalRowStyle(wb, totalRow, 11);

            // Ghi file
            LOGGER.info("EXPORT FILE: {}" + this.exportFileName);
            out = new FileOutputStream(exportFileName);
            wb.write(out);
        } catch (

        Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            Util.closeResourse(LOGGER, templateFileStream, wb, wb, out);
        }

    }

    private EUposReportItem calculateTotal(List<EUposReportItem> dataList) {
        EUposReportItem totalItem = new EUposReportItem("Tổng cộng", "", "", 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);

        for (EUposReportItem item : dataList) {
            totalItem.setAmountDoanhSoCard(totalItem.getAmountDoanhSoCard() + getSafe(item.getAmountDoanhSoCard()));
            totalItem.setAmountDoanhSoITA(totalItem.getAmountDoanhSoITA() + getSafe(item.getAmountDoanhSoITA()));
            totalItem.setAmountDoanhSoQR(totalItem.getAmountDoanhSoQR() + getSafe(item.getAmountDoanhSoQR()));
            totalItem.setTotalFeeOPCard(totalItem.getTotalFeeOPCard()+ getSafe(item.getTotalFeeOPCard()));
            totalItem.setTotalFeeOPITA(totalItem.getTotalFeeOPITA()+ getSafe(item.getTotalFeeOPITA()));
            totalItem.setTotalFeeOPUPOS(totalItem.getTotalFeeOPUPOS()+ getSafe(item.getTotalFeeOPUPOS()));
        }
        return totalItem;
    }

    private double getSafe(Double val) {
        return val != null ? val : 0.0;
    }

    private String getSafe(String val) {
        return val != null ? val : "";
    }

    private void setTotalRowStyle(Workbook workbook, Row row, int totalColumns) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);

        for (int i = 0; i < totalColumns; i++) {
            Cell cell = row.getCell(i);
            if (cell == null) cell = row.createCell(i);
            cell.setCellStyle(style);
        }
    }

    public void excelOPRevenue(String gridViewType, String displayColumn, String termsColumn,
            String fromDate, String toDate, String service, String bankPartnerName,
            String transactionType, String province, String contractRelation, String mcc,
            String categoryStan, String category, String cardType, String mpgsId, String binCountry, String currency,
            String sale, String saleProvince, String transactionDurationType, String period,
            String exchangeRate, String title, String vat) throws Exception {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        XSSFWorkbook wb = null;
        // SXSSFWorkbook wb = null;
        FileOutputStream out = null;
        Exception e = null;
        if (title == null || title.equals(""))
            title = "OnePay Fee Revenue";
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.info("TEMPLATE FILE: {}" + this.templateFilePath);
            wb = new XSSFWorkbook(templateFileStream);

            // wb = new SXSSFWorkbook(templateWorkbook, 100, true);
            // wb = new SXSSFWorkbook(templateWorkbook, -1, Boolean.FALSE, Boolean.TRUE);
            List<EReportItem> lstResult = (List<EReportItem>) this.bean.get("data");

            XSSFSheet sh = wb.getSheetAt(0);

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
            Date dFromDate = simpleDateFormat.parse(fromDate);
            Calendar cFromDate = Calendar.getInstance();
            cFromDate.setTime(dFromDate);

            Date dToDate = simpleDateFormat.parse(toDate);
            Calendar cToDate = Calendar.getInstance();
            cToDate.setTime(dToDate);
            cToDate.add(Calendar.MONTH, 1);
            cToDate.add(Calendar.DATE, -1);

            if (gridViewType.equals("column")) {
                // sh.setDefaultColumnWidth(22);
                int iRow = 1;
                int iColumn = 0;
                XSSFRow row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                XSSFCell cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cellInfo.setCellValue(title);

                iRow = 2;
                iColumn = 1;
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);

                cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                List<String> lstColumn = Arrays.asList(displayColumn.split(","));

                cellInfo.setCellValue(
                        "From Date" + ": " + fromDate + " - To Date" + ": "
                                + simpleDateFormat.format(cToDate.getTime()));

                if (service != null && !service.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    if (service.contains("QT"))
                        service = service.replace("QT", "International Gateway");
                    if (service.contains("ND"))
                        service = service.replace("ND", "Local Debit Gateway");
                    if (service.contains("QR") && !service.equals("VIETQR"))
                        service = service.replace("QR", "Mobile Banking / E-wallet");
                    if (service.contains("VIETQR"))
                        service = service.replace("VIETQR", "VietQR");
                    if (service.contains("BL"))
                        service = service.replace("BL", "Billing");
                    if (service.contains("PO"))
                        service = service.replace("PO", "PayOut");
                    if (service.contains("PC"))
                        service = service.replace("PC", "PayCollect");
                    if (service.contains("DD"))
                        service = service.replace("DD", "Direct Debit");

                    cellInfo.setCellValue("Service: " + service);
                }
                if (bankPartnerName != null && !bankPartnerName.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Bank/Partner Name: " + bankPartnerName);
                }
                if (transactionType != null && !transactionType.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Transaction Type: " + transactionType);
                }
                if (province != null && !province.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Partner Province: " + province);
                }
                if (contractRelation != null && !contractRelation.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Contract Relation: " + contractRelation);
                }
                if (mcc != null && !mcc.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("MCC: " + mcc);
                }
                if (categoryStan != null && !categoryStan.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Category(Stan): " + categoryStan);
                }
                if (category != null && !category.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Category: " + category);
                }

                if (cardType != null && !cardType.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Card Type: " + cardType);
                }
                if (binCountry != null && !binCountry.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Bin Country: " + binCountry);
                }
                if (mpgsId != null && !mpgsId.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("MPGS/CyberID/CIAC: " + mpgsId);
                }
                if (sale != null && !sale.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Sale: " + sale);
                }
                if (saleProvince != null && !saleProvince.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Sale Province: " + saleProvince);
                }
                if (transactionDurationType != null && !transactionDurationType.equals("")) {
                    iRow++;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Transaction Duration Type: " + transactionDurationType);
                }

                iRow++;
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                SimpleDateFormat simpleDateFormatMonth = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");
                Date dateNow = new Date();
                iColumn = 13;
                cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cellInfo.setCellValue("Created Date: " + simpleDateFormatMonth.format(dateNow));

                iRow++;
                iColumn = 1;

                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                String vatValue = " (No VAT)";
                if (Boolean.parseBoolean(vat))
                    vatValue = " (VAT included)";
                cellInfo.setCellValue("Currency: " + currency + vatValue);

                if (currency.equals("USD")) {
                    int columnTotal = 3;
                    if ((termsColumn.equals("partnerName.keyword") ||
                            termsColumn.equals("partnerShortName.keyword") ||
                            termsColumn.equals("merchantId.keyword"))) {
                        iColumn = 4;
                        columnTotal = 5;
                    } else
                        iColumn = 2;
                    cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cellInfo.setCellValue("Exchange Rate");

                    iColumn++;
                    int year = cFromDate.get(Calendar.YEAR);
                    List<EExchangeRate> lstExchangeRate = PaymentBankFeeAnalysisDao.getExchangeRate();
                    double exchangeRateNumberDefault = Double.parseDouble(exchangeRate);
                    double exchangeRateNumber = exchangeRateNumberDefault;
                    iColumn = iColumn + lstColumn.size();

                    for (int j = 0; j <= (cToDate.get(Calendar.YEAR) - cFromDate.get(Calendar.YEAR)); j++) {
                        year = year + j;
                        for (int i = 1; i <= 12; i++) {
                            exchangeRateNumber = exchangeRate(exchangeRateNumberDefault, lstExchangeRate, year, i);
                            cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                            cellInfo.setCellValue(exchangeRateNumber);
                            iColumn = iColumn + lstColumn.size();
                        }
                    }
                    // column Total
                    cellInfo = row.getCell(columnTotal) == null ? row.createCell(columnTotal)
                            : row.getCell(columnTotal);
                    cellInfo.setCellValue(exchangeRateNumber);
                }

                iRow++;
                iColumn = 0;
                int stt = 0;

                Font font = wb.createFont();
                CellStyle normalCellStyle = wb.createCellStyle();
                normalCellStyle.setBorderBottom(BorderStyle.THIN);
                normalCellStyle.setBorderRight(BorderStyle.THIN);
                normalCellStyle.setBorderTop(BorderStyle.THIN);
                normalCellStyle.setBorderLeft(BorderStyle.THIN);
                normalCellStyle.setAlignment(HorizontalAlignment.CENTER);
                normalCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                font.setBold(true);
                normalCellStyle.setWrapText(true);
                normalCellStyle.setFont(font);

                CellStyle numberCellStyle = wb.createCellStyle();
                numberCellStyle.setBorderBottom(BorderStyle.THIN);
                numberCellStyle.setBorderRight(BorderStyle.THIN);
                numberCellStyle.setBorderTop(BorderStyle.THIN);
                numberCellStyle.setBorderLeft(BorderStyle.THIN);
                DataFormat format = wb.createDataFormat();
                numberCellStyle.setDataFormat(format.getFormat("#,##0"));

                CellStyle percentageCellStyle = wb.createCellStyle();
                percentageCellStyle.setBorderBottom(BorderStyle.THIN);
                percentageCellStyle.setBorderRight(BorderStyle.THIN);
                percentageCellStyle.setBorderTop(BorderStyle.THIN);
                percentageCellStyle.setBorderLeft(BorderStyle.THIN);
                DataFormat formatPercentage = wb.createDataFormat();
                percentageCellStyle.setDataFormat(formatPercentage.getFormat("0.00%"));

                CellStyle dateTimeCellStyle = wb.createCellStyle();
                dateTimeCellStyle.setBorderBottom(BorderStyle.THIN);
                dateTimeCellStyle.setBorderRight(BorderStyle.THIN);
                dateTimeCellStyle.setBorderTop(BorderStyle.THIN);
                dateTimeCellStyle.setBorderLeft(BorderStyle.THIN);
                DataFormat formatDatetime = wb.createDataFormat();
                dateTimeCellStyle.setDataFormat(formatDatetime.getFormat("dd/MM/yyyy"));
                List<String> lstMonths = new ArrayList<>();

                if (cToDate.get(Calendar.YEAR) - cFromDate.get(Calendar.YEAR) == 0) // trong năm
                {
                    int year = cFromDate.get(Calendar.YEAR);
                    lstMonths = Arrays.asList("Total", "Jan-" + year, "Feb-" + year, "Mar-" + year, "Apr-" + year,
                            "May-" + year, "Jun-" + year, "Jul-" + year, "Aug-" + year, "Sep-" + year, "Oct-" + year,
                            "Nov-" + year, "Dec-" + year);
                } else {
                    int year = cFromDate.get(Calendar.YEAR);
                    int year2 = cToDate.get(Calendar.YEAR);
                    lstMonths = Arrays.asList("Total", "Jan-" + year, "Feb-" + year, "Mar-" + year, "Apr-" + year,
                            "May-" + year, "Jun-" + year, "Jul-" + year, "Aug-" + year, "Sep-" + year, "Oct-" + year,
                            "Nov-" + year, "Dec-" + year,
                            "Jan-" + year2, "Feb-" + year2, "Mar-" + year2, "Apr-" + year2,
                            "May-" + year2, "Jun-" + year2, "Jul-" + year2, "Aug-" + year2, "Sep-" + year2,
                            "Oct-" + year2, "Nov-" + year2, "Dec-" + year2);
                }

                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                XSSFCell cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                        : row.getCell(iColumn);
                cell.setCellValue("STT");
                cell.setCellStyle(normalCellStyle);
                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                        : row.getCell(iColumn);
                if (termsColumn != null)
                    cell.setCellValue(termsColumn.replace(".keyword", "").toUpperCase());
                cell.setCellStyle(normalCellStyle);
                if (termsColumn.equals("partnerName.keyword") ||
                        termsColumn.equals("partnerShortName.keyword") ||
                        termsColumn.equals("merchantId.keyword")) {
                    if (termsColumn.equals("partnerName.keyword") ||
                            termsColumn.equals("partnerShortName.keyword"))
                        cell.setCellValue("Partner");
                    else
                        cell.setCellValue("MerchantId");

                    iColumn++;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                            : row.getCell(iColumn);
                    cell.setCellValue("Active Date");
                    cell.setCellStyle(normalCellStyle);

                    iColumn++;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                            : row.getCell(iColumn);
                    cell.setCellValue("Sale");
                    cell.setCellStyle(normalCellStyle);

                    iColumn = 4;
                } else
                    iColumn = 2;
                for (String sMonth : lstMonths) {
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                            : row.getCell(iColumn);

                    cell.setCellValue(sMonth);
                    cell.setCellStyle(normalCellStyle);
                    if (lstColumn.size() > 1) {
                        int colSpan = iColumn + lstColumn.size() - 1;
                        if (sMonth.equals("Total")) {
                            if ((termsColumn.equals("partnerName.keyword") ||
                                    termsColumn.equals("partnerShortName.keyword") ||
                                    termsColumn.equals("merchantId.keyword")))
                                colSpan = colSpan + 2;
                            else
                                colSpan = colSpan + 1;
                        }
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(iRow, iRow, iColumn,
                                colSpan);
                        sh.addMergedRegionUnsafe(cellRangeAddress);
                        RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sh);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sh);
                        RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sh);
                        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sh);
                    }
                    iColumn = iColumn + lstColumn.size();
                    if (sMonth.equals("Total")) {
                        if ((termsColumn.equals("partnerName.keyword") ||
                                termsColumn.equals("partnerShortName.keyword") ||
                                termsColumn.equals("merchantId.keyword")))
                            iColumn = iColumn + 2;
                        else
                            iColumn = iColumn + 1;
                    }
                }
                iRow++;
                iColumn = 0;

                if ((termsColumn.equals("partnerName.keyword") ||
                        termsColumn.equals("partnerShortName.keyword") ||
                        termsColumn.equals("merchantId.keyword"))) {
                    iColumn = 4;
                } else
                    iColumn = 2;
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                row.setHeight((short) 600);
                for (int i = 0; i < iColumn; i++) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(iRow - 1, iRow, i, i);
                    sh.addMergedRegionUnsafe(cellRangeAddress);
                    RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sh);
                }
                for (String sMonth : lstMonths) {
                    for (String sColumn : lstColumn) {

                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);

                        if (sColumn.equals("NumberOfTransaction"))
                            cell.setCellValue("Number of Txn");
                        else if (sColumn.equals("SaleAmount"))
                            cell.setCellValue("Transaction Volume");
                        else if (sColumn.equals("OnePayFeeRevenue"))
                            cell.setCellValue("OnePay Fee Revenue ");
                        else if (sColumn.equals("MerchantFee"))
                            cell.setCellValue("Merchant Fee");
                        else if (sColumn.equals("PercentNumberOfTransaction"))
                            cell.setCellValue("Percentage of Total Txn");
                        else if (sColumn.equals("PercentSaleAmount"))
                            cell.setCellValue("Percentage of Total Txn Volume");
                        cell.setCellStyle(normalCellStyle);
                        iColumn++;

                        if (sMonth.equals("Total") && sColumn.equals("SaleAmount")) {
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            cell.setCellValue("Txn Volume Average");
                            cell.setCellStyle(normalCellStyle);
                            iColumn++;

                            if ((termsColumn.equals("partnerName.keyword") ||
                                    termsColumn.equals("partnerShortName.keyword") ||
                                    termsColumn.equals("merchantId.keyword"))) {
                                cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                        : row.getCell(iColumn);
                                cell.setCellValue("Level");
                                cell.setCellStyle(normalCellStyle);
                                iColumn++;
                            }
                        }
                    }
                }

                boolean existAllData = false;
                if (lstResult.size() > 1)
                    existAllData = true;
                for (EReportItem reportItem : lstResult) {
                    iRow++;
                    stt++;
                    iColumn = 0;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    if (existAllData && stt == 1) {
                        existAllData = false;
                        stt--;
                    } else
                        cell.setCellValue(stt);
                    cell.setCellStyle(numberCellStyle);

                    iColumn++;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue(reportItem.getName());
                    cell.setCellStyle(numberCellStyle);

                    if ((termsColumn.equals("partnerName.keyword") ||
                            termsColumn.equals("partnerShortName.keyword") ||
                            termsColumn.equals("merchantId.keyword"))) {

                        iColumn++;
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellStyle(dateTimeCellStyle);
                        if (reportItem.getSummary().getActiveDate() != null) {
                            cell.setCellValue(reportItem.getSummary().getActiveDate());
                        }

                        iColumn++;
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellValue(reportItem.getSummary().getSale());
                        cell.setCellStyle(numberCellStyle);
                        iColumn = 4;
                    } else
                        iColumn = 2;

                    for (String sColumn : lstColumn) {
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);

                        if (sColumn.equals("NumberOfTransaction")) {
                            cell.setCellValue(reportItem.getSummary().getNumberOfTransaction());
                            cell.setCellStyle(numberCellStyle);
                        } else if (sColumn.equals("SaleAmount")) {
                            cell.setCellValue(reportItem.getSummary().getTotalVolume());
                            cell.setCellStyle(numberCellStyle);
                        } else if (sColumn.equals("OnePayFeeRevenue")) {
                            cell.setCellValue(reportItem.getSummary().getTotalFeeOP());
                            cell.setCellStyle(numberCellStyle);
                        } else if (sColumn.equals("MerchantFee")) {
                            cell.setCellValue(reportItem.getSummary().getTotalFeeMerchant());
                            cell.setCellStyle(numberCellStyle);
                        } else if (sColumn.equals("PercentNumberOfTransaction")) {
                            cell.setCellValue(reportItem.getSummary().getPercentNumberOfTransaction());
                            cell.setCellStyle(percentageCellStyle);
                        } else if (sColumn.equals("PercentSaleAmount")) {
                            cell.setCellValue(reportItem.getSummary().getPercentTotalVolume());
                            cell.setCellStyle(percentageCellStyle);
                        }
                        iColumn++;

                        if (sColumn.equals("SaleAmount")) {
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            cell.setCellValue(reportItem.getSummary().getAvgVolume());
                            cell.setCellStyle(numberCellStyle);
                            iColumn++;

                            if ((termsColumn.equals("partnerName.keyword") ||
                                    termsColumn.equals("partnerShortName.keyword") ||
                                    termsColumn.equals("merchantId.keyword"))) {
                                cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                        : row.getCell(iColumn);
                                cell.setCellValue(reportItem.getSummary().getLevel());
                                cell.setCellStyle(numberCellStyle);
                                iColumn++;
                            }
                        }
                    }

                    for (int i = 0; i < reportItem.getMonths().size(); i++) {
                        for (String sColumn : lstColumn) {
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);

                            if (sColumn.equals("NumberOfTransaction")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getNumberOfTransaction());
                                cell.setCellStyle(numberCellStyle);
                            } else if (sColumn.equals("SaleAmount")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getTotalVolume());
                                cell.setCellStyle(numberCellStyle);
                            } else if (sColumn.equals("OnePayFeeRevenue")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getTotalFeeOP());
                                cell.setCellStyle(numberCellStyle);
                            } else if (sColumn.equals("MerchantFee")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getTotalFeeMerchant());
                                cell.setCellStyle(numberCellStyle);
                            } else if (sColumn.equals("PercentNumberOfTransaction")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getPercentNumberOfTransaction());
                                cell.setCellStyle(percentageCellStyle);
                            } else if (sColumn.equals("PercentSaleAmount")) {
                                cell.setCellValue(reportItem.getMonths().get(i).getPercentTotalVolume());
                                cell.setCellStyle(percentageCellStyle);
                            }
                            iColumn++;
                        }
                    }
                }

                iColumn = 6 + lstColumn.size();
                for (int i = 0; i < lstResult.get(0).getMonths().size(); i++) {
                    for (String sColumn : lstColumn) {
                        if (lstResult.get(0).getMonths().get(i).getActive() == 0) {
                            sh.setColumnHidden(iColumn, true);
                        }
                        iColumn++;
                    }
                }

                for (int i = 0; i < iColumn; i++)
                    sh.autoSizeColumn(i);

                if ((termsColumn.equals("partnerName.keyword") ||
                        termsColumn.equals("partnerShortName.keyword") ||
                        termsColumn.equals("merchantId.keyword"))) {
                    sh.setColumnWidth(4, 5000);
                    sh.setColumnWidth(5, 3000);
                }

            } else {
                sh.setDefaultColumnWidth(25);
                int iRow = 1;
                int iColumn = 0;
                XSSFRow row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                XSSFCell cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(title);

                iRow = 2;
                iColumn = 1;
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(
                        "From Date" + ": " + fromDate + " - To Date" + ": "
                                + simpleDateFormat.format(cToDate.getTime()));

                if (service != null && !service.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    if (service.contains("QT"))
                        service = service.replace("QT", "International Gateway");
                    if (service.contains("ND"))
                        service = service.replace("ND", "Local Debit Gateway");                   
                    if (service.contains("VIETQR"))
                        service = service.replace("VIETQR", "VietQR");
                    else if (service.contains("QR"))
                        service = service.replace("QR", "Mobile Banking / E-wallet");
                    if (service.contains("BL"))
                        service = service.replace("BL", "Billing");
                    if (service.contains("PO"))
                        service = service.replace("PO", "PayOut");
                    if (service.contains("PC"))
                        service = service.replace("PC", "PayCollect");
                    if (service.contains("DD"))
                        service = service.replace("DD", "Direct Debit");
                    cell.setCellValue("Service: " + service);
                }
                if (bankPartnerName != null && !bankPartnerName.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Bank/Partner Name: " + bankPartnerName);
                }
                if (transactionType != null && !transactionType.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Transaction Type: " + transactionType);
                }
                if (province != null && !province.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Partner Province: " + province);
                }
                if (contractRelation != null && !contractRelation.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Contract Relation: " + contractRelation);
                }
                if (mcc != null && !mcc.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("MCC: " + mcc);
                }
                if (categoryStan != null && !categoryStan.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Category(Stan): " + categoryStan);
                }
                if (category != null && !category.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Category: " + category);
                }

                if (cardType != null && !cardType.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Card Type: " + cardType);
                }
                if (binCountry != null && !binCountry.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Bin Country: " + binCountry);
                }
                if (mpgsId != null && !mpgsId.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("MPGS/CyberID/CIAC: " + mpgsId);
                }

                if (sale != null && !sale.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Sale: " + sale);
                }
                if (saleProvince != null && !saleProvince.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Sale Province: " + saleProvince);
                }
                if (transactionDurationType != null && !transactionDurationType.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Transaction Duration Type: " + transactionDurationType);
                }
                if (period != null && !period.equals("")) {
                    iColumn = iColumn + 2;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue("Sale MerchantID Period: " + period);
                }

                iRow++;
                iColumn = 1;
                SimpleDateFormat simpleDateFormatMonth = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");
                Date dateNow = new Date();
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                String vatValue = " (No VAT)";
                if (Boolean.parseBoolean(vat))
                    vatValue = " (VAT included)";
                cell.setCellValue("Currency: " + currency + vatValue);

                // if (currency.equals("USD")) {
                // iColumn++;
                // cell = row.getCell(iColumn) == null ? row.createCell(iColumn) :
                // row.getCell(iColumn);
                // cell.setCellValue("Exchange Rate: " + exchangeRate);
                // }

                // if (currency.equals("USD")) {
                // iColumn++;
                // cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) :
                // row.getCell(iColumn);
                // cellInfo.setCellValue("Exchange Rate");

                // int year = cToDate.get(Calendar.YEAR);
                // List<EExchangeRate> lstExchangeRate =
                // PaymentBankFeeAnalysisDao.getExchangeRate();
                // double exchangeRateNumberDefault = Double.parseDouble(exchangeRate);
                // double exchangeRateNumber = exchangeRateNumberDefault;
                // iColumn = 4;
                // for (int i = 1; i <= 12; i++) {
                // exchangeRateNumber = exchangeRate(exchangeRateNumberDefault, lstExchangeRate,
                // year, i);
                // cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) :
                // row.getCell(iColumn);
                // cellInfo.setCellValue(exchangeRateNumber);
                // iColumn++;
                // }
                // iColumn = 3; // column Total
                // cellInfo = row.getCell(iColumn) == null ? row.createCell(iColumn) :
                // row.getCell(iColumn);
                // cellInfo.setCellValue(exchangeRateNumber);
                // }

                iColumn = 3;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue("Created Date: " + simpleDateFormatMonth.format(dateNow));

                iRow = 4;
                iColumn = 0;
                int stt = 0;

                CellStyle numberCellStyle = wb.createCellStyle();
                numberCellStyle.setBorderBottom(BorderStyle.THIN);
                numberCellStyle.setBorderRight(BorderStyle.THIN);
                numberCellStyle.setBorderTop(BorderStyle.THIN);
                numberCellStyle.setBorderLeft(BorderStyle.THIN);
                DataFormat format = wb.createDataFormat();
                numberCellStyle.setDataFormat(format.getFormat("#,##0"));

                CellStyle percentageCellStyle = wb.createCellStyle();
                percentageCellStyle.setBorderBottom(BorderStyle.THIN);
                percentageCellStyle.setBorderRight(BorderStyle.THIN);
                percentageCellStyle.setBorderTop(BorderStyle.THIN);
                percentageCellStyle.setBorderLeft(BorderStyle.THIN);
                DataFormat formatPercentage = wb.createDataFormat();
                percentageCellStyle.setDataFormat(formatPercentage.getFormat("0.00%"));

                boolean existAllData = false;
                if (lstResult.size() > 1)
                    existAllData = true;

                Font font = wb.createFont();
                font.setBold(true);
                List<String> lstMonths = new ArrayList<>();
                if (cToDate.get(Calendar.YEAR) - cFromDate.get(Calendar.YEAR) == 0) // trong năm
                {
                    int year = cFromDate.get(Calendar.YEAR);
                    lstMonths = Arrays.asList("Jan-" + year, "Feb-" + year, "Mar-" + year,
                            "Apr-" + year,
                            "May-" + year, "Jun-" + year, "Jul-" + year, "Aug-" + year, "Sep-" + year, "Oct-" + year,
                            "Nov-" + year, "Dec-" + year);
                } else {
                    int year = cFromDate.get(Calendar.YEAR);
                    int year2 = cToDate.get(Calendar.YEAR);
                    lstMonths = Arrays.asList("Jan-" + year, "Feb-" + year, "Mar-" + year,
                            "Apr-" + year,
                            "May-" + year, "Jun-" + year, "Jul-" + year, "Aug-" + year, "Sep-" + year, "Oct-" + year,
                            "Nov-" + year, "Dec-" + year,
                            "Jan-" + year2, "Feb-" + year2, "Mar-" + year2, "Apr-" + year2,
                            "May-" + year2, "Jun-" + year2, "Jul-" + year2, "Aug-" + year2, "Sep-" + year2,
                            "Oct-" + year2, "Nov-" + year2, "Dec-" + year2);
                }

                CellStyle nameCellStyle = wb.createCellStyle();
                nameCellStyle.setBorderBottom(BorderStyle.THIN);
                nameCellStyle.setBorderRight(BorderStyle.THIN);
                nameCellStyle.setBorderTop(BorderStyle.THIN);
                nameCellStyle.setBorderLeft(BorderStyle.THIN);
                nameCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                nameCellStyle.setFont(font);

                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                iColumn = 3;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue("Total");
                cell.setCellStyle(nameCellStyle);
                iColumn++;

                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue("Average");
                cell.setCellStyle(nameCellStyle);
                iColumn++;

                int totalMonths = 0;
                if (cToDate.get(Calendar.YEAR) - cFromDate.get(Calendar.YEAR) == 0) {
                    for (int i = 0; i < 12; i++) {
                        if (checkValidateMonth(i, 1, cFromDate, cToDate)) {
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);

                            cell.setCellValue(lstMonths.get(i));
                            cell.setCellStyle(nameCellStyle);
                            iColumn++;
                            totalMonths++;
                        }
                    }
                } else {
                    for (int j = 0; j <= 1; j++) {
                        for (int i = 0; i < 12; i++) {
                            if (checkValidateMonth(i, j, cFromDate, cToDate)) {
                                cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                        : row.getCell(iColumn);

                                cell.setCellValue(lstMonths.get(j == 0 ? i : (12 + i)));
                                cell.setCellStyle(nameCellStyle);
                                iColumn++;
                                totalMonths++;
                            }
                        }
                    }
                }

                for (EReportItem reportItem : lstResult) {
                    iRow++;
                    stt++;
                    iColumn = 0;
                    row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    if (existAllData && stt == 1) {
                        existAllData = false;
                        stt--;
                    } else
                        cell.setCellValue(stt);
                    cell.setCellStyle(numberCellStyle);

                    iColumn++;
                    cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                    cell.setCellValue(reportItem.getName());
                    cell.setCellStyle(nameCellStyle);
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(iRow, iRow, iColumn,
                            iColumn + 3 + totalMonths);
                    sh.addMergedRegionUnsafe(cellRangeAddress);

                    RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sh);
                    RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sh);

                    for (EReportView reportView : reportItem.getViews()) {
                        if (!checkReferralITA(reportView.getDisplayName(), reportView.getTotal())) {
                            continue;
                        }
                        iRow++;
                        iColumn = 0;
                        row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellValue(reportView.getDisplayName());
                        CellRangeAddress cellRangeAddressView = new CellRangeAddress(iRow, iRow, iColumn, iColumn + 1);
                        sh.addMergedRegion(cellRangeAddressView);

                        RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddressView, sh);
                        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddressView, sh);
                        RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddressView, sh);
                        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddressView, sh);

                        iColumn = iColumn + 2;
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellStyle(percentageCellStyle);
                        if (!reportView.getDisplayName().equals("Number of Txn")
                                && !reportView.getDisplayName().equals("Growth of Txn Volume")
                                && !reportView.getDisplayName().equals("Transaction Volume")) {
                            cell.setCellValue(reportView.getPercentToSaleVolume());
                        }

                        iColumn++;
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellValue(reportView.getTotal());
                        cell.setCellStyle(numberCellStyle);

                        iColumn++;
                        cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                : row.getCell(iColumn);
                        cell.setCellValue(reportView.getAvg());
                        cell.setCellStyle(numberCellStyle);

                        if (checkValidateMonth(0, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang1Old())
                                    cell.setCellValue(reportView.getThang1Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang1Old())
                                    cell.setCellValue(reportView.getThang1Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(1, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang2Old())
                                    cell.setCellValue(reportView.getThang2Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang2Old())
                                    cell.setCellValue(reportView.getThang2Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(2, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang3Old())
                                    cell.setCellValue(reportView.getThang3Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang3Old())
                                    cell.setCellValue(reportView.getThang3Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(3, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang4Old())
                                    cell.setCellValue(reportView.getThang4Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang4Old())
                                    cell.setCellValue(reportView.getThang4Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(4, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang5Old())
                                    cell.setCellValue(reportView.getThang5Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang5Old())
                                    cell.setCellValue(reportView.getThang5Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(5, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang6Old())
                                    cell.setCellValue(reportView.getThang6Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang6Old())
                                    cell.setCellValue(reportView.getThang6Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(6, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang7Old())
                                    cell.setCellValue(reportView.getThang7Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang7Old())
                                    cell.setCellValue(reportView.getThang7Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(7, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang8Old())
                                    cell.setCellValue(reportView.getThang8Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang8Old())
                                    cell.setCellValue(reportView.getThang8Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(8, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang9Old())
                                    cell.setCellValue(reportView.getThang9Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang9Old())
                                    cell.setCellValue(reportView.getThang9Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(9, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang10Old())
                                    cell.setCellValue(reportView.getThang10Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang10Old())
                                    cell.setCellValue(reportView.getThang10Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(10, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang11Old())
                                    cell.setCellValue(reportView.getThang11Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang11Old())
                                    cell.setCellValue(reportView.getThang11Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(11, 0, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang12Old())
                                    cell.setCellValue(reportView.getThang12Old() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang12Old())
                                    cell.setCellValue(reportView.getThang12Old());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(0, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang1())
                                    cell.setCellValue(reportView.getThang1() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang1())
                                    cell.setCellValue(reportView.getThang1());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(1, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang2())
                                    cell.setCellValue(reportView.getThang2() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang2())
                                    cell.setCellValue(reportView.getThang2());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(2, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang3())
                                    cell.setCellValue(reportView.getThang3() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang3())
                                    cell.setCellValue(reportView.getThang3());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(3, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang4())
                                    cell.setCellValue(reportView.getThang4() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang4())
                                    cell.setCellValue(reportView.getThang4());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(4, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang5())
                                    cell.setCellValue(reportView.getThang5() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang5())
                                    cell.setCellValue(reportView.getThang5());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(5, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang6())
                                    cell.setCellValue(reportView.getThang6() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang6())
                                    cell.setCellValue(reportView.getThang6());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(6, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang7())
                                    cell.setCellValue(reportView.getThang7() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang7())
                                    cell.setCellValue(reportView.getThang7());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(7, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang8())
                                    cell.setCellValue(reportView.getThang8() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang8())
                                    cell.setCellValue(reportView.getThang8());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(8, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang9())
                                    cell.setCellValue(reportView.getThang9() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang9())
                                    cell.setCellValue(reportView.getThang9());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(9, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang10())
                                    cell.setCellValue(reportView.getThang10() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang10())
                                    cell.setCellValue(reportView.getThang10());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(10, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang11())
                                    cell.setCellValue(reportView.getThang11() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang11())
                                    cell.setCellValue(reportView.getThang11());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                        if (checkValidateMonth(11, 1, cFromDate, cToDate)) {
                            iColumn++;
                            cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                                    : row.getCell(iColumn);
                            if (reportView.getType() == "%") {
                                if (reportView.getStatusThang12())
                                    cell.setCellValue(reportView.getThang12() / 100);
                                cell.setCellStyle(percentageCellStyle);
                            } else {
                                if (reportView.getStatusThang12())
                                    cell.setCellValue(reportView.getThang12());
                                cell.setCellStyle(numberCellStyle);
                            }
                        }
                    }
                }
            }

            LOGGER.info("EXPORT FILE: {}" + this.exportFileName);
            out = new FileOutputStream(exportFileName);
            wb.write(out);
        } catch (

        Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
            e = ex;
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            Util.closeResourse(LOGGER, templateFileStream, wb, wb, out);
        }
        if (e != null)
            throw e;
    }

    private static boolean checkValidateMonth(int value, int isOld, Calendar fromDate, Calendar toDate) {
        var kq = false;
        if (fromDate.get(Calendar.YEAR) < toDate.get(Calendar.YEAR)) {
            // fromdate: 06/2022
            // todate: 09/2023
            if (isOld == 0) // old
            {
                if (value >= fromDate.get(Calendar.MONTH))
                    kq = true;
            } else {
                if (value <= toDate.get(Calendar.MONTH))
                    kq = true;
            }
        } else {
            if (isOld == 0)
                kq = false;
            else {
                if (fromDate.get(Calendar.MONTH) <= value && value <= toDate.get(Calendar.MONTH))
                    kq = true;
            }
        }
        return kq;
    }

    private static int getLevel(double avg) {
        if (avg >= 100000)
            return 1;
        if (avg >= 30000)
            return 2;
        if (avg >= 10000)
            return 3;
        if (avg >= 2000)
            return 4;
        return 5;
    }

    private boolean checkReferralITA(String name, double total) {
        if (name != null) {
            if (name.toLowerCase().contains("referral partner fee") || name.toLowerCase().contains("ita fee")) {
                if (total > 0)
                    return true;
                else
                    return false;
            } else
                return true;
        }
        return false;
    }

    private double exchangeRate(double staticExchangeRate, List<EExchangeRate> lstExchangeRate, int year,
            int month) {
        EExchangeRate rate = lstExchangeRate.stream()
                .filter(t -> t.getMonth() == month
                        && t.getYear() == year)
                .findAny()
                .orElse(null);
        if (rate != null)
            return rate.getExchangeRate();
        else
            return staticExchangeRate;
    }

    public void excelOPTransactionReportNew(List<String> currencyFormat) throws Exception {
        LOGGER.log(Level.INFO, "-------------START EXPORT FILE-----------------------");
        InputStream templateFileStream = null;
        XSSFWorkbook templateWorkbook = null;
        SXSSFWorkbook wb = null;
        FileOutputStream out = null;
        Exception e = null;
        try {
            templateFileStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(this.templateFilePath);
            LOGGER.info("TEMPLATE FILE: {}" + this.templateFilePath);
            templateWorkbook = new XSSFWorkbook(templateFileStream);

            wb = new SXSSFWorkbook(templateWorkbook, 100, true);

            Map mIn = (Map) this.bean.get("data");

            String service = mIn.get("service").toString();
            String bankCard = mIn.get("bankCard").toString();
            String acquirer = mIn.get("acquirer").toString();
            String transactionType = mIn.get("transactionType").toString();
            String province = mIn.get("province").toString();

            String merchantId = mIn.get("merchantId").toString();
            String merchantName = mIn.get("merchantName").toString();
            String partnerName = mIn.get("partnerName").toString();
            String fromDate = mIn.get("fromDate").toString();
            String toDate = mIn.get("toDate").toString();
            String keyword = mIn.get("keyword").toString();
            String transactionState = mIn.get("transactionState").toString();
            String transactionCurrency = mIn.get("transactionCurrency").toString();
            String mcc = mIn.get("mcc").toString();
            String category = mIn.get("category").toString();
            String categoryStan = mIn.get("categoryStan").toString();
            String contractRelation = mIn.get("contractRelation").toString();
            String openSearchScrollId = mIn.get("openSearchScrollId").toString();
            String sortField = mIn.get("sortField").toString();
            String sortOrder = mIn.get("sortOrder").toString();

            String cardType = mIn.get("cardType").toString();
            String mpgsId = mIn.get("mpgsId").toString();
            String binCountry = mIn.get("binCountry").toString();
            String binBank = mIn.get("binBank").toString();
            String viewTestData = mIn.get("viewTestData").toString();
            String moreSearch = mIn.get("moreSearch").toString();
            String isFeeMonth = mIn.get("isFeeMonth").toString();
            String userId = mIn.get("userId").toString();
            String fromAmount = mIn.get("fromAmount").toString();
            String toAmount = mIn.get("toAmount").toString();

            User user = UserDao.get(userId);
            String email = "";
            if (user != null)
                email = user.getEmail();
            List<EPermisson> listAllPermissons = PaymentBankFeeAnalysisDao.getPermissonData(email);
            List<EPermisson> listPermissons = listAllPermissons.stream().filter(a -> a.getAdd() == 1)
                    .collect(Collectors.toList());
            List<EPermisson> listNotPermissons = listAllPermissons.stream().filter(a -> a.getAdd() == 0)
                    .collect(Collectors.toList());

            String currency = " (VND)";
            List<ETranBankShare> lstResult = new ArrayList<ETranBankShare>();
            ETransaction objTransaction = openSearchUtil.getTransaction(service, bankCard, acquirer,
                    transactionType, province,
                    merchantId, merchantName, partnerName, contractRelation,
                    mcc, category, categoryStan, cardType, mpgsId, binCountry, binBank, viewTestData,
                    fromDate, toDate, keyword,
                    transactionState, transactionCurrency, fromAmount, toAmount, 0, 10000,
                    true,
                    openSearchScrollId, sortField, sortOrder, "DOWNLOAD", moreSearch, isFeeMonth, listPermissons,
                    listNotPermissons);

            lstResult.addAll(objTransaction.getList());

            boolean flag = true;
            if (objTransaction.getList().size() > 0) {
                openSearchScrollId = objTransaction.getScrollId();
                while (flag) {
                    objTransaction = openSearchUtil.getTransaction(service, bankCard, acquirer,
                            transactionType, province,
                            merchantId, merchantName, partnerName, contractRelation,
                            mcc, category, categoryStan, cardType, mpgsId, binCountry, binBank, viewTestData,
                            fromDate, toDate, keyword,
                            transactionState, transactionCurrency, fromAmount, toAmount, 1,
                            10000, true,
                            openSearchScrollId, sortField, sortOrder, "DOWNLOAD", moreSearch, isFeeMonth,
                            listPermissons, listNotPermissons);
                    lstResult.addAll(objTransaction.getList());
                    if (objTransaction.getList().size() == 0)
                        flag = false;
                }
            } else
                flag = false;

            if (flag == false && !openSearchScrollId.isEmpty()) {
                openSearchUtil.deleteScrollOpenSearch(openSearchScrollId);
            }

            SXSSFSheet sh = wb.getSheetAt(0);

            sh.setDefaultColumnWidth(19);
            int iRow = 3;
            int iColumn = 0;
            int stt = 0;

            Font font = wb.createFont();
            font.setBold(true);

            CellStyle cellStyle = wb.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);
            cellStyle.setFont(font);

            CellStyle titleCellStyle = wb.createCellStyle();
            titleCellStyle.setBorderBottom(BorderStyle.THIN);
            titleCellStyle.setBorderRight(BorderStyle.THIN);
            titleCellStyle.setBorderTop(BorderStyle.THIN);
            titleCellStyle.setBorderLeft(BorderStyle.THIN);
            titleCellStyle.setAlignment(HorizontalAlignment.CENTER);
            titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleCellStyle.setWrapText(true);

            CellStyle titleBoldCellStyle = wb.createCellStyle();
            titleBoldCellStyle.setBorderBottom(BorderStyle.THIN);
            titleBoldCellStyle.setBorderRight(BorderStyle.THIN);
            titleBoldCellStyle.setBorderTop(BorderStyle.THIN);
            titleBoldCellStyle.setBorderLeft(BorderStyle.THIN);
            titleBoldCellStyle.setAlignment(HorizontalAlignment.CENTER);
            titleBoldCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleBoldCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleBoldCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleBoldCellStyle.setFont(font);
            titleBoldCellStyle.setWrapText(true);

            CellStyle titleBoldCellStyleOP = wb.createCellStyle();
            titleBoldCellStyleOP.setBorderBottom(BorderStyle.THIN);
            titleBoldCellStyleOP.setBorderRight(BorderStyle.THIN);
            titleBoldCellStyleOP.setBorderTop(BorderStyle.THIN);
            titleBoldCellStyleOP.setBorderLeft(BorderStyle.THIN);
            titleBoldCellStyleOP.setAlignment(HorizontalAlignment.CENTER);
            titleBoldCellStyleOP.setVerticalAlignment(VerticalAlignment.CENTER);
            titleBoldCellStyleOP.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            titleBoldCellStyleOP.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleBoldCellStyleOP.setFont(font);
            titleBoldCellStyleOP.setWrapText(true);

            CellStyle titleBoldCellStyleITA = wb.createCellStyle();
            titleBoldCellStyleITA.setBorderBottom(BorderStyle.THIN);
            titleBoldCellStyleITA.setBorderRight(BorderStyle.THIN);
            titleBoldCellStyleITA.setBorderTop(BorderStyle.THIN);
            titleBoldCellStyleITA.setBorderLeft(BorderStyle.THIN);
            titleBoldCellStyleITA.setAlignment(HorizontalAlignment.CENTER);
            titleBoldCellStyleITA.setVerticalAlignment(VerticalAlignment.CENTER);
            titleBoldCellStyleITA.setFillForegroundColor(IndexedColors.GREEN.getIndex());
            titleBoldCellStyleITA.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleBoldCellStyleITA.setFont(font);
            titleBoldCellStyleITA.setWrapText(true);

            CellStyle numberCellStyle = wb.createCellStyle();
            numberCellStyle.setBorderBottom(BorderStyle.THIN);
            numberCellStyle.setBorderRight(BorderStyle.THIN);
            numberCellStyle.setBorderTop(BorderStyle.THIN);
            numberCellStyle.setBorderLeft(BorderStyle.THIN);
            DataFormat format = wb.createDataFormat();
            numberCellStyle.setDataFormat(format.getFormat("#,##0"));

            CellStyle stylePercentage = wb.createCellStyle();
            stylePercentage.setDataFormat(wb.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(10)));
            stylePercentage.setBorderBottom(BorderStyle.THIN);
            stylePercentage.setBorderRight(BorderStyle.THIN);
            stylePercentage.setBorderTop(BorderStyle.THIN);
            stylePercentage.setBorderLeft(BorderStyle.THIN);

            CellStyle numberBoldCellStyle = wb.createCellStyle();
            numberBoldCellStyle.setBorderBottom(BorderStyle.THIN);
            numberBoldCellStyle.setBorderRight(BorderStyle.THIN);
            numberBoldCellStyle.setBorderTop(BorderStyle.THIN);
            numberBoldCellStyle.setBorderLeft(BorderStyle.THIN);
            DataFormat formatBold = wb.createDataFormat();
            numberBoldCellStyle.setDataFormat(formatBold.getFormat("#,##0"));
            numberBoldCellStyle.setFont(font);

            DataFormat formatAmountVnd = wb.createDataFormat();
            CellStyle amountStyleVnd = wb.createCellStyle();
            amountStyleVnd.setBorderBottom(BorderStyle.THIN);
            amountStyleVnd.setBorderRight(BorderStyle.THIN);
            amountStyleVnd.setBorderTop(BorderStyle.THIN);
            amountStyleVnd.setBorderLeft(BorderStyle.THIN);
            amountStyleVnd.setDataFormat(formatAmountVnd.getFormat("#,##"));
            amountStyleVnd.setFont(font);

            DataFormat formatAmount = wb.createDataFormat();
            CellStyle amountStyle = wb.createCellStyle();
            amountStyle.setBorderBottom(BorderStyle.THIN);
            amountStyle.setBorderRight(BorderStyle.THIN);
            amountStyle.setBorderTop(BorderStyle.THIN);
            amountStyle.setBorderLeft(BorderStyle.THIN);
            amountStyle.setDataFormat(formatAmountVnd.getFormat("#,##0.00"));
            amountStyle.setFont(font);

            CellStyle dateTimeCellStyle = wb.createCellStyle();
            dateTimeCellStyle.setBorderBottom(BorderStyle.THIN);
            dateTimeCellStyle.setBorderRight(BorderStyle.THIN);
            dateTimeCellStyle.setBorderTop(BorderStyle.THIN);
            dateTimeCellStyle.setBorderLeft(BorderStyle.THIN);
            DataFormat formatDatetime = wb.createDataFormat();
            dateTimeCellStyle.setDataFormat(formatDatetime.getFormat("dd/MM/yyyy HH:mm:ss"));

            CellStyle percentageCellStyle = wb.createCellStyle();
            percentageCellStyle.setBorderBottom(BorderStyle.THIN);
            percentageCellStyle.setBorderRight(BorderStyle.THIN);
            percentageCellStyle.setBorderTop(BorderStyle.THIN);
            percentageCellStyle.setBorderLeft(BorderStyle.THIN);
            DataFormat formatPercentage = wb.createDataFormat();
            percentageCellStyle.setDataFormat(formatPercentage.getFormat("0.00%"));

            String[] arrrayColumn = { "Service", "Contract Type", "Merchant ID", "Merchant name", "Partner ID",
                    "Partner Name", "MCC", "Category", "Category (Stan)", "Acquirer",
                    "MPGS/CBS ID/CIAC", "Bank MID",
                    "Transaction ID",
                    "Transaction Date", "Card No", "Card Type",
                    "Order Reference", "Merchant Transaction Reference", "Transaction Type", "Transaction Currency",
                    "Transaction Amount", "Exchange Rate", "Transaction Amount" + currency, "Transaction State",
                    "Response Code", "Response Description", "Bin Country", "Bin Bank", "Installment Bank",
                    "Installment Period",
                    "Referral Partner", "Bank/Partner", "Source", "Province",
                    "OnePay Collected Fee" + currency,
                    "Installment Merchant Fee" + currency,
                    "Total Merchant Fee" + currency,
                    "Config - Merchant Fixed Fee" + currency, 
                    "Config - Merchant Fixed Failed Fee" + currency,
                    "Config - Merchant Percent Fee (%)", 
                    "Config - Shopify Percent Fee (%)",
                    "Config - Merchant Installment Fee (%)",
                    "OnePay Received from Acquirer Fee (3B BIDV ND)" + currency,
                    "Bank Cost" + currency,
                    "Installment Share Fee" + currency,
                    "Config - Bank Fix Fee" + currency,
                    "Config - Bank Percent Fee (%)", "Config - Bank Min Fee" + currency,
                    "Config - Bank Share Rate (%)",
                    "Config - ITA Percent Fee (%)",
                    "OnePay Sent to Issuer Fee (3B BIDV ND)" + currency,
                    "Referral Partner Share Fee" + currency,
                    "OnePay Revenue Fee" + currency,
                    "Installment OnePay Revenue Fee" + currency,
                    "OP Revenue Fixed Fee" + currency,
                    "OnePay Revenue Fixed Failed Fee" + currency,
                    "OnePay Revenue Percent Fee" + currency,
                    "Import Last Time" };

            SXSSFRow row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
            row.setHeight((short) 600);
            SXSSFCell cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                    : row.getCell(iColumn);
            cell.setCellValue("STT");
            cell.setCellStyle(titleCellStyle);
            for (String sColumn : arrrayColumn) {
                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn)
                        : row.getCell(iColumn);
                cell.setCellValue(sColumn);
                if (sColumn.contains("OnePay Collected Fee") || sColumn.equals("OnePay Revenue Fee" + currency)
                        || sColumn.contains("Bank Cost"))
                    cell.setCellStyle(titleBoldCellStyleOP);
                else if (sColumn.contains("Installment Merchant Fee" + currency)
                        || sColumn.contains("Installment Share Fee")
                        || sColumn.contains("Installment OnePay Revenue Fee"))
                    cell.setCellStyle(titleBoldCellStyleITA);
                else if (sColumn.contains("Referral Partner Share Fee"))
                    cell.setCellStyle(titleBoldCellStyle);
                else
                    cell.setCellStyle(titleCellStyle);
            }
            sh.setAutoFilter(new CellRangeAddress(iRow, iRow, 0, iColumn));
            for (ETranBankShare reportItem : lstResult) {
                iRow++;
                stt++;
                iColumn = 0;
                row = sh.getRow(iRow) == null ? sh.createRow(iRow) : sh.getRow(iRow);
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(stt);
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getService());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getContractRelation());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getMerchantId());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getMerchantName());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getPartnerId());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getPartnerName());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getMCC());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getCategory());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getCategoryStan());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getAcquirer());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getBankMerchantId());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getBankMID());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getTransactionId());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getDateTransaction());
                cell.setCellStyle(dateTimeCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getCardNo());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getBankCard());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getOrderInfo());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getTransactionRef());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getTransactionType());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getCurrency());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getAmountHienThi() != null)
                    cell.setCellValue(reportItem.getAmountHienThi());
                String currencyData = reportItem.getCurrency();
                if (!currencyData.isEmpty() && currencyFormat.contains(currencyData)) {
                    cell.setCellStyle(amountStyle);
                } else {
                    cell.setCellStyle(amountStyleVnd);
                }

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getExchangeRate() != null)
                    cell.setCellValue(reportItem.getExchangeRate());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getAmountHienThiVND() != null)
                    cell.setCellValue(reportItem.getAmountHienThiVND());
                cell.setCellStyle(numberBoldCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getState());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getResponseCode());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getResponseCodeDescription());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getBinCountry());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getBinBank());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getITABank());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getITATerm());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getPartnerFeeConfigOtherName());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getPartnerFeeConfigName());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getSource());
                // source
               

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                cell.setCellValue(reportItem.getProvince());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalAmountIn() != null)
                    cell.setCellValue(reportItem.getTotalAmountIn());
                cell.setCellStyle(numberBoldCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getITAFee() != null)
                    cell.setCellValue(reportItem.getITAFee());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalFeeMerchant() != null) {
                    if (reportItem.getITAFee() != null)
                        cell.setCellValue(reportItem.getTotalFeeMerchant() + reportItem.getITAFee());
                    else
                        cell.setCellValue(reportItem.getTotalFeeMerchant());
                }
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigMerchantFixFee() != null)
                    cell.setCellValue(reportItem.getConfigMerchantFixFee());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigMerchantFixFailedFee() != null)
                    cell.setCellValue(reportItem.getConfigMerchantFixFailedFee());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigMerchantPercentFee() != null)
                    cell.setCellValue(reportItem.getConfigMerchantPercentFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigShopifyMerchantFee() != null)
                    cell.setCellValue(reportItem.getConfigShopifyMerchantFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigMerchantITAPercentFee() != null)
                    cell.setCellValue(reportItem.getConfigMerchantITAPercentFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getAmountAcqReturnOnePay() != null)
                    cell.setCellValue(reportItem.getAmountAcqReturnOnePay());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalBankCost() != null)
                    cell.setCellValue(reportItem.getTotalBankCost());
                cell.setCellStyle(numberBoldCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalFeeShareITA() != null)
                    cell.setCellValue(reportItem.getTotalFeeShareITA());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigBankFixFee() != null)
                    cell.setCellValue(reportItem.getConfigBankFixFee());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigBankPercentFee() != null)
                    cell.setCellValue(reportItem.getConfigBankPercentFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigBankMinFixFee() != null)
                    cell.setCellValue(reportItem.getConfigBankMinFixFee());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigBankShareRateFromMerchantFee() != null)
                    cell.setCellValue(reportItem.getConfigBankShareRateFromMerchantFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getConfigBankITAPercentFee() != null)
                    cell.setCellValue(reportItem.getConfigBankITAPercentFee() / 100);
                cell.setCellStyle(percentageCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getAmountOnePayReturnIssuer() != null)
                    cell.setCellValue(reportItem.getAmountOnePayReturnIssuer());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalFeeShareOther() != null)
                    cell.setCellValue(reportItem.getTotalFeeShareOther());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalFeeOP() != null)
                    cell.setCellValue(reportItem.getTotalFeeOP());
                cell.setCellStyle(numberBoldCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getTotalFeeOPITA() != null)
                    cell.setCellValue(reportItem.getTotalFeeOPITA());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getOnePayFixFeeVnd() != null)
                    cell.setCellValue(reportItem.getOnePayFixFeeVnd());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getOnePayFixFailedFeeVnd() != null)
                    cell.setCellValue(reportItem.getOnePayFixFailedFeeVnd());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getOnePayPercentFeeVnd() != null)
                    cell.setCellValue(reportItem.getOnePayPercentFeeVnd());
                cell.setCellStyle(numberCellStyle);

                iColumn++;
                cell = row.getCell(iColumn) == null ? row.createCell(iColumn) : row.getCell(iColumn);
                if (reportItem.getModifyDate() != null)
                    cell.setCellValue(reportItem.getModifyDate());
                cell.setCellStyle(dateTimeCellStyle);
            }
            sh.createFreezePane(0, 4, iColumn, 4);

            SXSSFSheet sheet2 = wb.createSheet("Month Fee");
            if (sheet2 != null) {
                sheet2.setDefaultColumnWidth(19);
                List<ETranBankShare> lstResultMonth = new ArrayList<ETranBankShare>();
                ETransaction objTransactionMonth = openSearchUtil.getTransaction(service, "", "",
                        "", "",
                        "", "", "", "",
                        "", "", "", "", "", "", "", "",
                        fromDate, toDate, "",
                        "", "", "", "", 0, 10000,
                        true,
                        openSearchScrollId, sortField, sortOrder, "DOWNLOAD", moreSearch, "1", listPermissons,
                        listNotPermissons);
                lstResultMonth.addAll(objTransactionMonth.getList());

                int iRowSheet2 = 1;
                int iColumnSheet2 = 3;

                SXSSFRow rowSheet2 = sheet2.getRow(iRowSheet2) == null ? sheet2.createRow(iRowSheet2)
                        : sheet2.getRow(iRowSheet2);

                SXSSFCell cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                        : rowSheet2.getCell(iColumnSheet2);
                cellSheet2.setCellValue("MONTH FEE");
                cellSheet2.setCellStyle(cellStyle);

                int sttSheet2 = 0;
                iRowSheet2 = 3;
                iColumnSheet2 = 0;
                String[] arrrayColumnSheet2 = { "Service", "Contract Type", "Merchant ID", "Merchant name",
                        "Partner ID",
                        "Partner Name", "Acquirer",
                        "MPGS/CBS ID/CIAC", "Bank MID",
                        "Transaction Date",
                        "Transaction Currency",
                        "Transaction Amount", "Exchange Rate", "Transaction Amount" + currency, "Transaction State",
                        "Province",
                        "OnePay Collected Fee" + currency,
                        "Total Merchant Fee" + currency,
                        "Bank Cost" + currency,
                        "OnePay Revenue Fee" + currency,
                        "Import Last Time" };

                rowSheet2 = sheet2.getRow(iRowSheet2) == null ? sheet2.createRow(iRowSheet2)
                        : sheet2.getRow(iRowSheet2);
                rowSheet2.setHeight((short) 600);
                cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                        : rowSheet2.getCell(iColumnSheet2);
                cellSheet2.setCellValue("STT");
                cellSheet2.setCellStyle(titleCellStyle);
                for (String sColumn : arrrayColumnSheet2) {
                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumn);
                    cellSheet2.setCellValue(sColumn);
                    if (sColumn.contains("OnePay Collected Fee") || sColumn.equals("OnePay Revenue Fee" + currency)
                            || sColumn.contains("Bank Cost"))
                        cellSheet2.setCellStyle(titleBoldCellStyleOP);
                    else
                        cellSheet2.setCellStyle(titleCellStyle);
                }
                sheet2.setAutoFilter(new CellRangeAddress(iRowSheet2, iRowSheet2, 0, iColumnSheet2));

                for (ETranBankShare reportItem : lstResultMonth) {
                    iRowSheet2++;
                    sttSheet2++;
                    iColumnSheet2 = 0;
                    rowSheet2 = sheet2.getRow(iRowSheet2) == null ? sheet2.createRow(iRowSheet2)
                            : sheet2.getRow(iRowSheet2);
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(sttSheet2);
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getService());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getContractRelation());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getMerchantId());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getMerchantName());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getPartnerId());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getPartnerName());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getAcquirer());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getBankMerchantId());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getBankMID());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getDateTransaction());
                    cellSheet2.setCellStyle(dateTimeCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getCurrency());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getAmountHienThi() != null)
                        cellSheet2.setCellValue(reportItem.getAmountHienThi());
                    cellSheet2.setCellStyle(numberBoldCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getExchangeRate() != null)
                        cellSheet2.setCellValue(reportItem.getExchangeRate());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getAmountHienThiVND() != null)
                        cellSheet2.setCellValue(reportItem.getAmountHienThiVND());
                    cellSheet2.setCellStyle(numberBoldCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getState());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    cellSheet2.setCellValue(reportItem.getProvince());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getTotalAmountIn() != null)
                        cellSheet2.setCellValue(reportItem.getTotalAmountIn());
                    cellSheet2.setCellStyle(numberBoldCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getTotalFeeMerchant() != null)
                        cellSheet2.setCellValue(reportItem.getTotalFeeMerchant());
                    cellSheet2.setCellStyle(numberCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getTotalBankCost() != null)
                        cellSheet2.setCellValue(reportItem.getTotalBankCost());
                    cellSheet2.setCellStyle(numberBoldCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getTotalFeeOP() != null)
                        cellSheet2.setCellValue(reportItem.getTotalFeeOP());
                    cellSheet2.setCellStyle(numberBoldCellStyle);

                    iColumnSheet2++;
                    cellSheet2 = rowSheet2.getCell(iColumnSheet2) == null ? rowSheet2.createCell(iColumnSheet2)
                            : rowSheet2.getCell(iColumnSheet2);
                    if (reportItem.getModifyDate() != null)
                        cellSheet2.setCellValue(reportItem.getModifyDate());
                    cellSheet2.setCellStyle(dateTimeCellStyle);
                }
            }

            LOGGER.info("EXPORT FILE: {}" + this.exportFileName);
            out = new FileOutputStream(exportFileName);
            wb.write(out);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export excel file", ex);
            e = ex;
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            Util.closeResourse(LOGGER, templateFileStream, templateWorkbook, wb, out);
        }
        if (e != null)
            throw e;
    }


    public void exportContractDocx(Map contractData) {
        LOGGER.log(Level.INFO, "-------------START EXPORT CONTRACT DOCX FILE-----------------------");
        InputStream templateFileStream = null;
        OutputStream outputStream = null;
        try {
            // templateFileStream = Thread.currentThread().getContextClassLoader()
            //         .getResourceAsStream(this.templateFilePath);
            templateFileStream = new FileInputStream(this.templateFilePath);
            LOGGER.log(Level.INFO, "TEMPLATE FILE: " + this.templateFilePath);
            XWPFDocument document = new XWPFDocument(templateFileStream);
            Map<String, String> mDataReplace = contractData.get("mDataReplace") != null
                    ? (Map<String, String>) contractData.get("mDataReplace")
                    : new HashMap<String, String>();
            List<Map<String, String>> listHideData = contractData.get("hide") != null
                    ? (List<Map<String, String>>) contractData.get("hide")
                    : new ArrayList<Map<String, String>>();
            //hide row in word
            for (Map<String, String> hideData : listHideData) {
                // System.out.println(hideData);
                if (hideData.get("type").equals("table")) {
                    document = removeTableRowWithText(document, hideData.get("key"));
                } else if (hideData.get("type").equals("row")) {
                    document = removeParagraphWithText(document, hideData.get("key"));
                } else if (hideData.get("type").equals("tag")) {
                    document = removeParagraphsBetweenTags(document, hideData.get("start"), hideData.get("end"));
                } else if (hideData.get("type").equals("line_in_table")) {
                    document = removeLineWithTextInTable(document, hideData.get("key"));
                } else if (hideData.get("type").equals("full_table")) {
                    document = removeTableWithText(document, hideData.get("key"));
                }
            }
            
            //replace text in word
            document = replaceTextInDocument(document, mDataReplace);
            // System.out.println(mDataReplace);
            
            //generate qr code and replace in word
            String qrCodeText = contractData.get("qrCodeText") != null ? (String) contractData.get("N_ID_CONTRACT_VERSION") : "";
            // qrCodeText = "https://dev.onepay.vn/iportal/merchant-management/"+contractData.get("N_ID_PARTNER")+"?tabIndex=1&contractId="+contractData.get("N_ID_CONTRACT")+"&v="+contractData.get("N_ID_CONTRACT_VERSION");
            String bilingual = (contractData.get("bilingual") != null && Boolean.TRUE.equals(contractData.get("bilingual"))) ? "y" : "n";
            qrCodeText = contractData.get("N_ID_PARTNER")+"?c="+contractData.get("N_ID_CONTRACT")+"&v="+contractData.get("N_ID_CONTRACT_VERSION")+"&b="+bilingual;
            byte[] qrCodeImage = generateQRCodeImage(qrCodeText,50,50);
            // byte[] qrCodeImage = generateBarcodeImageByte(qrCodeText);
            // replaceTextWithQRCodeInHeader(document, "qrcode", qrCodeImage);
            replaceTextWithBarcodeInFooter(document, "qrcode", qrCodeImage);

            outputStream = new FileOutputStream(exportFileName);
            LOGGER.log(Level.INFO, "EXPORT FILE: " + exportFileName);
            document.write(outputStream);
        } catch (Exception ex) {
            LOGGER.log(Level.WARNING, "Error export word file", ex);
        } finally {
            LOGGER.log(Level.INFO, "-------------DONE EXPORT FILE-----------------------");
            try {
                if (templateFileStream != null)
                    templateFileStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
            try {
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "Error Close Connection File", e);
            }
        }
    }


    public static byte[] generateQRCodeImage(String text, int width, int height) throws WriterException, IOException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
        hints.put(EncodeHintType.MARGIN, 0); // Bo vien toi thieu
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height,hints);

        ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
        return pngOutputStream.toByteArray();
    }

    public static byte[] generateBarcodeImageByte(String text) throws WriterException, IOException {
        Code128Writer barcodeWriter = new Code128Writer();
        BitMatrix bitMatrix = barcodeWriter.encode(text, BarcodeFormat.CODE_128, 100, 30);

        ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
        return pngOutputStream.toByteArray();
    }

    public static void replaceTextWithQRCodeInBody(XWPFDocument document, String searchText, byte[] qrCodeImage) throws Exception {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        replaceTextWithQRCodeInParagraphs(paragraphs, searchText, qrCodeImage);
    }

    public static void replaceTextWithQRCodeInHeader(XWPFDocument document, String searchText, byte[] qrCodeImage) throws Exception {
        XWPFHeaderFooterPolicy headerFooterPolicy = document.getHeaderFooterPolicy();
        if (headerFooterPolicy != null) {
            XWPFHeader header = headerFooterPolicy.getDefaultHeader();
            if (header != null) {
                replaceTextWithQRCodeInParagraphs(header.getParagraphs(), searchText, qrCodeImage);
            }
        }
    }

    public static void replaceTextWithQRCodeInParagraphs(List<XWPFParagraph> paragraphs, String searchText, byte[] qrCodeImage) throws Exception {
        for (XWPFParagraph paragraph : paragraphs) {
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.contains(searchText)) {
                    text = text.replace(searchText, "");
                    run.setText(text, 0);

                    // Chèn hình ảnh mã QR vào vị trí của văn bản "qrcode"
                    run.addPicture(new ByteArrayInputStream(qrCodeImage), XWPFDocument.PICTURE_TYPE_PNG, "qrcode.png", 200, 200);
                }
            }
        }
    }



//dạng byte[]
public static void replaceTextWithBarcodeInFooter(XWPFDocument document, String searchText, byte[] barcodeImage) throws Exception {
    XWPFHeaderFooterPolicy headerFooterPolicy = document.getHeaderFooterPolicy();
    if (headerFooterPolicy != null) {
        // Kiểm tra và thay thế văn bản trong footer mặc định
        XWPFFooter defaultFooter = headerFooterPolicy.getDefaultFooter();
        if (defaultFooter != null) {
            replaceTextWithBarcodeInParagraphs(defaultFooter.getParagraphs(), searchText, barcodeImage);
        }

        // Kiểm tra và thay thế văn bản trong footer trang đầu tiên
        XWPFFooter firstPageFooter = headerFooterPolicy.getFirstPageFooter();
        if (firstPageFooter != null) {
            replaceTextWithBarcodeInParagraphs(firstPageFooter.getParagraphs(), searchText, barcodeImage);
        }

        // Kiểm tra và thay thế văn bản trong footer trang chẵn
        XWPFFooter evenPageFooter = headerFooterPolicy.getEvenPageFooter();
        if (evenPageFooter != null) {
            replaceTextWithBarcodeInParagraphs(evenPageFooter.getParagraphs(), searchText, barcodeImage);
        }
    }
}
private static void replaceTextWithBarcodeInParagraphs(List<XWPFParagraph> paragraphs, String searchText, byte[] barcodeImage) throws Exception {
    for (XWPFParagraph paragraph : paragraphs) {
        List<XWPFRun> runs = paragraph.getRuns();
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String text = run.getText(0);
            if (text != null && text.contains(searchText)) {
                text = text.replace(searchText, "");
                run.setText(text, 0);

                // Chèn hình ảnh mã vạch vào vị trí của văn bản "qrcode"
                run.addPicture(new ByteArrayInputStream(barcodeImage), XWPFDocument.PICTURE_TYPE_PNG, "barcode.png", Units.toEMU(50), Units.toEMU(50));
                // run.addPicture(new ByteArrayInputStream(barcodeImage), XWPFDocument.PICTURE_TYPE_PNG, "barcode.png", Units.toEMU(100), Units.toEMU(30));
            }
        }
    }
}


    // Thay thế văn bản trong file Word dựa map dữ liệu thay thế
    public static XWPFDocument replaceTextInDocument(XWPFDocument document, Map<String, String> replacements) throws IOException {
        int elementIndex = 0;
        // Duyệt qua tất cả các phần tử của tài liệu
        for (IBodyElement element : document.getBodyElements()) {
            if (element.getElementType() == BodyElementType.PARAGRAPH) {
                // Xử lý đoạn văn độc lập
                XWPFParagraph paragraph = (XWPFParagraph) element;
                replaceTextInParagraph(paragraph, replacements);
            } else if (element.getElementType() == BodyElementType.TABLE) {
                // Xử lý bảng
                XWPFTable table = (XWPFTable) element;
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            System.out.println("Table paragraph text: " + paragraph.getText());
                            replaceTextInParagraph(paragraph, replacements);
                        }
                    }
                }
            }
        }
        
        
        
        // // System.out.println("replace:"+replacements);
        // // Thay thế văn bản trong các đoạn văn
        // for (XWPFParagraph paragraph : document.getParagraphs()) {
        //     replaceTextInParagraph(paragraph, replacements);
        // }

        // // Thay thế văn bản trong các bảng
        // for (IBodyElement element : document.getBodyElements()) {
        //     if (element.getElementType() == BodyElementType.TABLE) {
        //         XWPFTable table = (XWPFTable) element;
        //         for (XWPFTableRow row : table.getRows()) {
        //             for (XWPFTableCell cell : row.getTableCells()) {
        //                 for (XWPFParagraph paragraph : cell.getParagraphs()) {
        //                     replaceTextInParagraph(paragraph, replacements);
        //                 }
        //             }
        //         }
        //     }
        // }
        return document;
    }

    // Thay thế văn bản trong đoạn văn
    private static void replaceTextInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        //kiểu thay thế nhưng ko dùng được với text nằm trên nhiều run
        for (XWPFRun run : paragraph.getRuns()) {
            String text = run.getText(0);
            if (text != null) {
                for (Map.Entry<String, String> entry : replacements.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    if (key != null && text.contains(key)) {
                        try {
                            text = text.replace(key, value != null ? value : "");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                run.setText(text, 0);
            }
        }


        //kiểu thay thế text nằm trên nhiều run và fix case replace chuỗi null vào văn bản, nhưng làm mất format
        /*
        List<XWPFRun> runs = paragraph.getRuns();
        StringBuilder paragraphText = new StringBuilder();
        for (XWPFRun run : runs) {
            String t = run.getText(0);
            paragraphText.append(t == null?"\t":t);
        }

        String combinedText = paragraphText.toString();
        if (combinedText != null) {
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                if (combinedText.contains(entry.getKey())) {
                    System.out.println("replace:"+entry.getKey()+"->"+entry.getValue()+" in:"+combinedText);
                    combinedText = combinedText.replace(entry.getKey(), entry.getValue());
                    // System.out.println("replace:"+entry.getKey()+"->"+entry.getValue()+" in:"+combinedText);
                    // Xóa tất cả các phần tử run hiện tại
                    // Sao chép định dạng từ run cuối cùng
                    XWPFRun lastRun = runs.get(runs.size() - 1);
                    CTRPr format = null;
                    if (lastRun != null) {
                        CTRPr lastRPr = lastRun.getCTR().getRPr();
                        if (lastRPr != null) {
                            format = (CTRPr) lastRPr.copy(); // Thực hiện sao chép sâu
                        }
                    }
                    for (int i = runs.size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);

                    }
                    // Thêm phần tử run mới với văn bản đã thay thế
                    XWPFRun newRun = paragraph.createRun();
                    newRun.setText(combinedText);
                    if(format!=null) newRun.getCTR().setRPr(format);

                    
                    // break;
                }
            }
        }
        */
        
        // kiểu thay thế text nằm trên nhiều run và fix case replace chuỗi null vào văn bản, và không làm mất format
        //kiểu này đang gây lỗi vị trí \t bị dịch vào giữa văn bản khi thay thế đầu dòng
        /*
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        StringBuilder paragraphText = new StringBuilder();
        Map<Integer, String> posToText = new LinkedHashMap<>();
        Map<Integer, XWPFRun> posToRun = new LinkedHashMap<>();

        // Gộp văn bản và lưu trữ vị trí từng run
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String text = run.getText(0);
            if (text != null) {
                posToText.put(paragraphText.length(), text);
                posToRun.put(paragraphText.length(), run);
                paragraphText.append(text);
            }
        }

        String updatedText = paragraphText.toString();
        boolean needReplacement = false;

        // Kiểm tra xem có cần thay thế không
        for (String key : replacements.keySet()) {
            if (updatedText.contains(key)) {
                needReplacement = true;
                break;
            }
        }

        if (!needReplacement) {
            return;
        }

        // Thay thế văn bản trong đoạn
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            updatedText = updatedText.replace(entry.getKey(), entry.getValue());
        }

        // Phân phối lại văn bản đã cập nhật vào các runs, giữ nguyên định dạng
        int currentCharIndex = 0;
        for (Map.Entry<Integer, String> entry : posToText.entrySet()) {
            XWPFRun run = posToRun.get(entry.getKey());
            String originalText = entry.getValue();
            int length = originalText.length();

            if (currentCharIndex >= updatedText.length()) {
                run.setText("", 0); // Xóa văn bản dư thừa
                continue;
            }

            int remainingLength = updatedText.length() - currentCharIndex;
            int runTextLength = Math.min(length, remainingLength);

            String newText = updatedText.substring(currentCharIndex, currentCharIndex + runTextLength);

            run.setText(newText, 0);

            currentCharIndex += runTextLength;
        }

        // Xóa văn bản còn lại trong các runs dư thừa
        if (currentCharIndex < updatedText.length()) {
            String remainingText = updatedText.substring(currentCharIndex);
            XWPFRun lastRun = runs.get(runs.size() - 1);
            XWPFRun newRun = paragraph.createRun();
            newRun.setText(remainingText);

            // Sao chép định dạng từ run cuối cùng
            if (lastRun != null) {
                newRun.getCTR().setRPr(lastRun.getCTR().getRPr());
            }
        } else {
            for (int i = currentCharIndex; i < runs.size(); i++) {
                runs.get(i).setText("", 0);
            }
        }
     */
    /* kiểu này gây lỗi rất lạ nhưng fix đc nội dung lỗi bên trên
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String text = run.getText(0);

            if (text != null) {
                boolean textReplaced = false;

                for (Map.Entry<String, String> entry : replacements.entrySet()) {
                    String searchText = entry.getKey();
                    String replacement = entry.getValue();

                    if (text.contains(searchText)) {
                        text = text.replace(searchText, replacement);
                        run.setText(text, 0);
                        textReplaced = true;
                    }
                }

                if (!textReplaced) {
                    // Kiểm tra xem chuỗi cần thay thế có nằm trong nhiều run hay không
                    StringBuilder combinedText = new StringBuilder(text);
                    int k = i + 1;
                    List<Integer> runPositions = new ArrayList<>();
                    runPositions.add(i);

                    while (k < runs.size() && !replacements.keySet().stream().anyMatch(combinedText.toString()::contains)) {
                        XWPFRun nextRun = runs.get(k);
                        String nextText = nextRun.getText(0);
                        if (nextText != null) {
                            combinedText.append(nextText);
                            runPositions.add(k);
                        }
                        k++;
                    }

                    String combinedString = combinedText.toString();

                    boolean found = false;
                    for (Map.Entry<String, String> entry : replacements.entrySet()) {
                        String searchText = entry.getKey();
                        String replacement = entry.getValue();

                        if (combinedString.contains(searchText)) {
                            found = true;
                            combinedString = combinedString.replace(searchText, replacement);

                            // Xóa văn bản trong các runs liên quan
                            for (int idx : runPositions) {
                                runs.get(idx).setText("", 0);
                            }

                            // Phân phối lại văn bản đã thay thế vào các runs
                            int pos = 0;
                            for (int idx : runPositions) {
                                XWPFRun currentRun = runs.get(idx);
                                String originalText = currentRun.getText(0);
                                if (originalText != null) {
                                    int len = originalText.length();
                                    if (pos + len > combinedString.length()) {
                                        len = combinedString.length() - pos;
                                    }
                                    String substr = combinedString.substring(pos, pos + len);
                                    currentRun.setText(substr, 0);
                                    pos += len;
                                }
                            }
                            break;
                        }
                    }

                    if (found) {
                        i = k - 1; // Cập nhật chỉ số i để tiếp tục vòng lặp
                    }
                }
            }
        }
             */
    }
    //xóa cả table trong file Word
    public static XWPFDocument removeTableWithText(XWPFDocument document, String searchText) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        for (int i = bodyElements.size() - 1; i >= 0; i--) {
            IBodyElement element = bodyElements.get(i);
            if (element.getElementType() == BodyElementType.TABLE) {
                XWPFTable table = (XWPFTable) element;
                boolean removeTable = false;
                outerloop:
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        if (cell.getText().contains(searchText)) {
                            removeTable = true;
                            break outerloop;
                        }
                    }
                }
                if (removeTable) {
                    document.removeBodyElement(i);
                }
            }
        }
        return document;
    }

    //xóa text trong dòng trong cell của bảng
    public static XWPFDocument removeLineWithTextInTable(XWPFDocument document, String textToRemove) {
        Iterator<IBodyElement> iterator = document.getBodyElementsIterator();
        while (iterator.hasNext()) {
            IBodyElement element = iterator.next();
            if (element.getElementType() == BodyElementType.TABLE) {
                XWPFTable table = (XWPFTable) element;
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        // Tạo danh sách các paragraph cần xóa
                        List<Integer> paragraphsToRemove = new ArrayList<>();
                        List<XWPFParagraph> paragraphs = cell.getParagraphs();
                        
                        // Tìm các paragraph chứa text cần xóa
                        for (int i = 0; i < paragraphs.size(); i++) {
                            XWPFParagraph para = paragraphs.get(i);
                            String paragraphText = para.getText();
                            if (paragraphText != null && paragraphText.contains(textToRemove)) {
                                paragraphsToRemove.add(i);
                            }
                        }
                        
                        // Xóa các paragraph từ cuối lên để tránh lỗi index
                        for (int i = paragraphsToRemove.size() - 1; i >= 0; i--) {
                            int indexToRemove = paragraphsToRemove.get(i);
                            cell.removeParagraph(indexToRemove);
                        }
                        
                        // Nếu cell không còn paragraph nào, thêm một paragraph trống để duy trì cấu trúc
                        if (cell.getParagraphs().isEmpty()) {
                            cell.addParagraph();
                        }
                    }
                }
            }
        }
        return document;
    }

    // Xóa hàng chứa văn bản trong bảng của file Word
    public static XWPFDocument removeTableRowWithText(XWPFDocument document, String text) throws IOException {
        Iterator<IBodyElement> iterator = document.getBodyElementsIterator();

        while (iterator.hasNext()) {
            IBodyElement element = iterator.next();
            if (element.getElementType() == BodyElementType.TABLE) {
                XWPFTable table = (XWPFTable) element;
                for (int i = 0; i < table.getNumberOfRows(); i++) {
                    XWPFTableRow row = table.getRow(i);
                    boolean isRowToRemove = false;
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            // System.out.println(paragraph.getText());
                            if (paragraph.getText().contains(text)) {
                                isRowToRemove = true;
                            }
                        }
                    }
                    if (isRowToRemove) {
                        table.removeRow(i);
                        break; // Xóa hàng đầu tiên tìm thấy và thoát khỏi vòng lặp
                    }
                }
            }
        }
        return document;
    }

    // Xóa dòng trên văn bản có chứa text truyền vào trong file Word
    public static XWPFDocument removeParagraphWithText(XWPFDocument document, String text) throws IOException {
        // Lấy danh sách các đoạn văn bản trong tài liệu
        List<XWPFParagraph> paragraphs = document.getParagraphs();

        // Duyệt qua các đoạn văn bản và xóa đoạn văn bản chứa text truyền vào
        for (XWPFParagraph paragraph : paragraphs) {
            if (paragraph.getText().contains(text)) {
                document.removeBodyElement(document.getPosOfParagraph(paragraph));
                break; // Xóa đoạn văn bản đầu tiên tìm thấy và thoát khỏi vòng lặp
            }
        }
        return document;
    }

    // Xóa tất cả các đoạn văn bản giữa các thẻ trong file Word
    public static XWPFDocument removeParagraphsBetweenTags(XWPFDocument document, String startTag, String endTag) throws IOException {
        Iterator<IBodyElement> iterator = document.getBodyElementsIterator();
        boolean isWithinTags = false;
        List<IBodyElement> elementsToRemove = new ArrayList<>();

        while (iterator.hasNext()) {
            IBodyElement element = iterator.next();
            if (element.getElementType() == BodyElementType.PARAGRAPH) {
                XWPFParagraph paragraph = (XWPFParagraph) element;
                String paragraphText = paragraph.getText();
                if (paragraphText.contains(startTag)) {
                    isWithinTags = true;
                }
                if (isWithinTags) {
                    elementsToRemove.add(element);
                }
                if (paragraphText.contains(endTag)) {
                    isWithinTags = false;
                }
            }
            if (element.getElementType() == BodyElementType.TABLE && isWithinTags) {
                elementsToRemove.add(element);
            }
        }

        // Xóa các phần tử từ danh sách tạm thời
        for (IBodyElement element : elementsToRemove) {
            if (element.getElementType() == BodyElementType.PARAGRAPH) {
                document.removeBodyElement(document.getPosOfParagraph((XWPFParagraph) element));
            } else if (element.getElementType() == BodyElementType.TABLE) {
                document.removeBodyElement(document.getPosOfTable((XWPFTable) element));
            }
        }
        return document;
    }

    private static final Logger LOGGER = Logger.getLogger(ExcelReportGenerator.class.getName());
}
