package vn.onepay.portal.queue.listener;

import com.google.gson.Gson;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.WebClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Server;
import vn.onepay.portal.resources.File.FileDownloadDao;
import vn.onepay.portal.resources.File.dto.FileDownloadDto;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by HungDX on 22-Jan-16.
 */
@Component
public class DownloadQueueSlowOutListener implements MessageListener, IConstants {

    @Override
    public void onMessage(Message message) {
        vn.onepay.portal.queue.message.Message messageData = null;

        try {
            messageData = (vn.onepay.portal.queue.message.Message) ((ObjectMessage) message).getObject();
        } catch (JMSException e) {
            LOGGER.log(Level.SEVERE, "Error Generate File", e);
            return;
        }
        int code = messageData.getResultCode();
        Map requestData = messageData.getRequestData();
        Map resultNoti = new HashMap<>();
        JsonObject json = new JsonObject();
        json.put("notification_id", "notification." + requestData.get(X_USER_ID));
        try {
            String status;
            LOGGER.log(Level.INFO, "Generate File Code " + code);
            if (code == 200) {
                resultNoti.put(HANDLER_DATA_CODE, "200");
                resultNoti.put(FILE_HASH_NAME, requestData.get(FILE_HASH_NAME));
                resultNoti.put(FILE_NAME, requestData.get(FILE_NAME));
                status = "success";
            } else {
                resultNoti.put(HANDLER_DATA_CODE, "500");
                resultNoti.put(FILE_HASH_NAME, requestData.get(FILE_HASH_NAME));
                resultNoti.put(FILE_NAME, requestData.get(FILE_NAME));
                status = "failed";
            }
            FileDownloadDto fileDownloadDto = new FileDownloadDto();
            fileDownloadDto.setFile_hash_name(String.valueOf(requestData.get(FILE_HASH_NAME)));
            fileDownloadDto.setStatus(status);
            fileDownloadDto.setFile_size(
                    Long.valueOf(requestData.get(FILE_SIZE) == null ? "0" : requestData.get(FILE_SIZE).toString()));
            FileDownloadDao.update(fileDownloadDto);

            LOGGER.log(Level.INFO, "Generate File With Info " + gson.toJson(resultNoti));
            json.put("notification_detail", resultNoti);
        } catch (Exception e) {
            resultNoti.put(HANDLER_DATA_CODE, "500");
            resultNoti.put(FILE_HASH_NAME, requestData.get(FILE_HASH_NAME));
            resultNoti.put(FILE_NAME, requestData.get(FILE_NAME));
            json.put("notification_detail", resultNoti);
            LOGGER.log(Level.SEVERE, "Error Generate File", e);
        } finally {
            LOGGER.log(Level.INFO, "Send Notification Content : " + json);
            sendToNotification(Server._vertx, Json.encode(json));
        }

    }

    private void sendToNotification(Vertx vertx, String param) {
        Buffer buffer = Buffer.buffer(param);
        final WebClient webClient = WebClient.create(vertx);
        webClient.postAbs(Config.getQueueNotificationUri() + "/push")
                .putHeader(String.valueOf(HttpHeaders.CONTENT_LENGTH), String.valueOf(buffer.length()))
                .sendBuffer(buffer, res -> {
                    if (res.result() != null) {
                        if (res.result().statusCode() == 200 || res.result().statusCode() == 201) {
                            LOGGER.log(Level.INFO, "Send Notification OK.");
                        } else {
                            LOGGER.log(Level.SEVERE, "Send Notification Error.");
                        }
                    }
                    else
                    {
                        LOGGER.log(Level.SEVERE, "Send Notification NULL.");
                    }
                });
    }

    private final static Gson gson = new Gson();

    private static final Logger LOGGER = Logger.getLogger(DownloadQueueSlowOutListener.class.getName());
}
