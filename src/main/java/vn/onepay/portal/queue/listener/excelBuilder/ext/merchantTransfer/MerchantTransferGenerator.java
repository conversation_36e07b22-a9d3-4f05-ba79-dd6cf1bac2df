package vn.onepay.portal.queue.listener.excelBuilder.ext.merchantTransfer;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.merchantTransfer.MerchantTransferDao;
import vn.onepay.portal.resources.merchantTransfer.dto.MerchantTransferDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MerchantTransferGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<MerchantTransferDto> list = MerchantTransferDao.search(mIn).getList();
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Transfer Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<MerchantTransferDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        Map<String, String> sourctTypeMap = new HashMap();
        sourctTypeMap.put("transfer", "Transfer");
        sourctTypeMap.put("payment_guarantee", "Payment Guarantee");

        for (MerchantTransferDto itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put(TemplateUtils.S_MERCHANT_ACCOUNT, itemReport.getMerchantAccount());
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchantId());
            item.put(TemplateUtils.BUSINESS_REG_NAME_COLUMN, itemReport.getBusinessRegName());
            item.put(TemplateUtils.PAY_CHANNEL_COLUMN, itemReport.getPayChannel());
            item.put(TemplateUtils.SOURCE_TYPE_COLUMN, sourctTypeMap.get(itemReport.getSourceType()));
            item.put(TemplateUtils.N_AMOUNT, itemReport.getAmount());
            item.put(TemplateUtils.N_EXPECTED_AMOUNT, itemReport.getExpectedAmount());
            item.put(TemplateUtils.S_REFERENCE, itemReport.getReference());
            item.put(TemplateUtils.ONEPAY_ACCOUNT_COLUMN, itemReport.getOnepayAccount() + " - " + itemReport.getOnepayBank());
            item.put(TemplateUtils.STATUS_COLUMN, itemReport.getState());
            item.put(TemplateUtils.DATE_COLUMN, df.format(itemReport.getDate()));
            item.put(TemplateUtils.S_DESCRIPTION, itemReport.getDesc());
            item.put(TemplateUtils.S_NOTE, itemReport.getNote());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantTransferGenerator.class.getName());
}
