package vn.onepay.portal.queue.listener.excelBuilder.ext.billing;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.paycollect.PayCollectReportGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.base.dto.QueryMethod;
import vn.onepay.portal.resources.debtClearance.dao.DebtClearanceDao;
import vn.onepay.portal.resources.debtClearance.dto.DebtClearanceAprovalDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 6/7/2021
 * Time: 5:49 PM
 * To change this IPORTAL_SERVICE.
 */

public class GenerateBillingDebtClearingFile implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            mIn.put(IConstants.OFFSET, 0);
            List<DebtClearanceAprovalDto> list = DebtClearanceDao.getDebtClearanceSearch(mIn, QueryMethod.DOWNLOAD);
            generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GenerateBillingDebtClearingFile error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<DebtClearanceAprovalDto> list, List<Map> listData) throws Exception {

        int rowNumber = 0;
        for (DebtClearanceAprovalDto dto:  list) {
            rowNumber++;
            Map<String, Object> item = new HashMap<>();
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            item.put(TemplateUtils.TRANSACTION_ID, dto.getTransactionId());
            item.put("S_BILL_CODE", dto.getBillCode());
            item.put("S_SERVICE", dto.getService());
            item.put("S_SERVICE_BILLING", dto.getServiceBilling());
            item.put("S_CLIENT", dto.getClient());
            item.put("N_AMOUNT", formatCurrencyDouble(dto.getAmount()));
            item.put("D_CREATE", Util.formatDate(new Date(dto.getCreateDate().getTime()), "dd/MM/yyyy hh:mm a"));
            item.put("D_UPDATE", dto.getUpdateDate() != null ?Util.formatDate(new Date(dto.getUpdateDate().getTime()), "dd/MM/yyyy hh:mm a"):"");
            item.put("S_USER_REQUEST", dto.getCreateBy());
            item.put("S_USER_APROVAL", dto.getUpdateBy());
            item.put("S_REQUEST_STATUS", dto.getStatus());
            item.put("S_DEBT_CLEARING_STATUS", dto.getDebtClearanceStatus());
            item.put("S_RESPONSE", dto.getResponce());
            listData.add(item);
        }
    }

    public static String formatCurrencyDouble(double value) {
        DecimalFormat df = new DecimalFormat("###,###,###");
        return df.format(value);
    }
    private static final Logger LOGGER = Logger.getLogger(PayCollectReportGenerator.class.getName());
}
