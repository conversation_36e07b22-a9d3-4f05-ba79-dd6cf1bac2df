package vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation;

import com.onepay.commons.util.Convert;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.reconciliation.cdr_international.CDRInternationalDAO;
import vn.onepay.portal.resources.reconciliation.cdr_international.dto.ReconciliationLineInterDto;
import vn.onepay.portal.resources.reconciliation.cdr_international.dto.ReconciliationQueryInterDto;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ReconciliationInterGenerator implements BaseGenerator<ReconciliationQueryInterDto> {

    @Override
    public List<Map> generate(ReconciliationQueryInterDto reconciliationQueryInterDto) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<ReconciliationLineInterDto> baseListExtend = CDRInternationalDAO.downloadListReconciliationLine(reconciliationQueryInterDto);
            this.generateRs(baseListExtend, listMap, reconciliationQueryInterDto);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<ReconciliationLineInterDto> baseListExtend, List<Map> listData, ReconciliationQueryInterDto queryDto) throws Exception {
        int rowNumber = 0;
        for (ReconciliationLineInterDto itemReport : baseListExtend) {
            Map<String, String> rowData = new HashMap<>();
            rowNumber++;

            rowData.put("index", String.valueOf(rowNumber));

            String strTransStatus1 = itemReport.getS_trans_status_1();
            rowData.put("status1", strTransStatus1 == null? "" : strTransStatus1);

            String sourceService1 = itemReport.getSource_service_1();
            rowData.put("sourceService1", sourceService1 == null? "" : sourceService1 );         

            String strTransType1 = itemReport.getS_trans_type_1();
            rowData.put("transType1", strTransType1 == null? "" : strTransType1);

            Date strD1 = itemReport.getD_trans_date_1();
            rowData.put("date1", strD1 == null? "" : Convert.toString(strD1, "dd/MM/yyyy HH:mm:ss"));
            
            String strAmount1 = itemReport.getN_amount_1();
            double dbAmount1 = 0;
            if (strAmount1 != null) {
                dbAmount1 = Double.parseDouble(strAmount1);
                String currencyCode1 = itemReport.getS_currency_code_1();
                rowData.put("transAmount1", currencyCode1 + Convert.toString(dbAmount1, "###,##0.##"));
            } else {
                rowData.put("transAmount1", "");
            }
            String orderRef1 = itemReport.getS_order_ref_1();
            rowData.put("orderRef1", orderRef1 == null ? "" : orderRef1);

            String authCode1 = itemReport.getS_auth_code_1();
            rowData.put("authCode1", authCode1 == null ? "" : authCode1);

            String mdr1 = itemReport.getN_mdr_amount_1();
            rowData.put("mdr1", mdr1 == null ? "" : mdr1);

            String fixFee1 = itemReport.getN_fix_fee_1();
            rowData.put("fixFee1", fixFee1 == null ? "" : fixFee1);

            String cardType1 = itemReport.getS_card_type_1();
            rowData.put("cardType1", cardType1 == null ? "" : cardType1);

            String mcc1 = itemReport.getS_mcc_1();
            rowData.put("mcc1", mcc1 == null ? "" : mcc1);

            String binCountry1 = itemReport.getS_bin_country_1();
            rowData.put("binCountry1", binCountry1 == null ? "" : binCountry1);

            String binBank1 = itemReport.getS_bin_bank_1();
            rowData.put("binBank1", binBank1 == null ? "" : binBank1);

            String cdrResult1 = itemReport.getS_result_code_1();
            String cdrResult2 = itemReport.getS_result_code_2();
            String cdrResult = "";
            if (cdrResult1 != null) {
                cdrResult = cdrResult1;
            }
            cdrResult = cdrResult + "|";
            if (cdrResult2 != null) {
                cdrResult = cdrResult + cdrResult2;
            }
            rowData.put("result", cdrResult);

            String strAmount2 = itemReport.getN_amount_2();
            double dbAmount2 = 0;
            if (strAmount2 != null) {
                dbAmount2 = Double.parseDouble(strAmount2);
                String currencyCode2 = itemReport.getS_currency_code_2();
                rowData.put("transAmount2", currencyCode2 + Convert.toString(dbAmount2, "###,##0.##"));
            } else {
                strAmount2 = "";
                rowData.put("transAmount2", strAmount2);
            }

            Date strD2 = itemReport.getD_trans_date_2();
            rowData.put("date2", strD2 == null? "" : Convert.toString(strD2, "dd/MM/yyyy HH:mm:ss"));

            String strTransType2 = itemReport.getS_trans_type_2();
            rowData.put("transType2", strTransType2 == null ? "": strTransType2);

            String strTransStatus2 = itemReport.getS_trans_status_2();
            rowData.put("status2", strTransStatus2 == null? "" : strTransStatus2);

            String sourceService2 = itemReport.getSource_service_2();
            rowData.put("sourceService2", sourceService2 == null ? "" : sourceService2);

            String orderRef2 = itemReport.getS_order_ref_2();
            rowData.put("orderRef2", orderRef2 == null ? "" : orderRef2);

            String authCode2 = itemReport.getS_auth_code_2();
            rowData.put("authCode2", authCode2 == null ? "" : authCode2);

            String mdr2 = itemReport.getN_mdr_amount_2();
            rowData.put("mdr2", mdr2 == null ? "" : mdr2);

            String fixFee2 = itemReport.getN_fix_fee_2();
            rowData.put("fixFee2", fixFee2 == null ? "" : fixFee2);

            String cardType2 = itemReport.getS_card_type_2();
            rowData.put("cardType2", cardType2 == null ? "" : cardType2);

            String mcc2 = itemReport.getS_mcc_2();
            rowData.put("mcc2", mcc2 == null ? "" : mcc2);

            String binCountry2 = itemReport.getS_bin_country_2();
            rowData.put("binCountry2", binCountry2 == null ? "" : binCountry2);

            String binBank2 = itemReport.getS_bin_bank_2();
            rowData.put("binBank2", binBank2 == null ? "" : binBank2);

            listData.add(rowData);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ReconciliationGenerator.class.getName());
}
