package vn.onepay.portal.queue.listener.excelBuilder.ext.reconciliation;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.reconciliation.cdrFileManagement.CDRFileManageDao;
import vn.onepay.portal.resources.reconciliation.cdrFileManagement.dto.CDRFileLineDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CDRFileGenerator implements BaseGenerator<String> {

    @Override
    public List<Map> generate(String cdr_file_line) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<CDRFileLineDto> baseListExtend = CDRFileManageDao.getCDRFileLineFromFileID(cdr_file_line);
            this.generateRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<CDRFileLineDto> baseListExtend, List<Map> listData) throws Exception {
        for (CDRFileLineDto itemReport: baseListExtend) {
            Map item = new HashMap();
            item.put("NO", itemReport.getLine_no());
            item.put("S_LINE", itemReport.getLine());
            item.put("S_LINE_STATUS", itemReport.getLine_status());
            listData.add(item);
        }
    }

    public List<Map> generate2(String cdr_file_line) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<CDRFileLineDto> baseListExtend = CDRFileManageDao.getCDRFileLineFromFileID2(cdr_file_line);
            this.generateRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Reconciliation error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private static final Logger LOGGER = Logger.getLogger(CDRFileGenerator.class.getName());
}
