package vn.onepay.portal.queue.listener.excelBuilder.ext.domestic;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.refund.DomesticRefundDao;
import vn.onepay.portal.resources.domestic.refund.dto.DomesticRefund;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DomesticRefundGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<DomesticRefund> domesticRefund = DomesticRefundDao.search(mIn).getList();
            this.generateRs(domesticRefund, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<DomesticRefund> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();

        for (DomesticRefund itemReport: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, itemReport.getAcquirer().getAcquirer_name());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchant_id());

            // CAIC
            item.put("S_CAIC", itemReport.getCaic());
            
            item.put("S_CONTRACT_TYPE", itemReport.getContract_type());
            
            item.put("S_ACQUIRER", itemReport.getAcquirer_bank());

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, itemReport.getOriginal_id().toString());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, itemReport.getOrder_info());

            // trans ref
            item.put(TemplateUtils.TRANS_REF_COLUMN, itemReport.getMerchant_transaction_ref());

            // purchase amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, itemReport.getAmount().getPurchase_total());

            // purchase date
            item.put(TemplateUtils.PURCHASE_DATE_COLUMN, itemReport.getTransaction_purchase_time());

            // refund amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, itemReport.getAmount().getTotal());

            // refund date
            item.put(TemplateUtils.REFUND_DATE_COLUMN, itemReport.getTransaction_time());

            // card numebr
            item.put(TemplateUtils.CARD_NO_COLUMN, itemReport.getCard().getCard_number());
            // Card name
            item.put("S_CARD_HOLDER_NAME",itemReport.getCard().getCard_holder_name());
            item.put("S_REFUND_BANK_TRANSACTION_ID", itemReport.getS_refund_bank_transaction_id() != null ? itemReport.getS_refund_bank_transaction_id().equals("null") ? "" : itemReport.getS_refund_bank_transaction_id() : "");
            item.put("S_BANK_TRANSACTION_ID",  itemReport.getS_bank_transaction_id());


            // status
            Integer statusData = itemReport.getStatus();
            String status = null;
            if(statusData != null) {
                switch (statusData) {
                    case 400: status = "Successful"; break;
                    case 300: status = "Waiting for OnePay's Approval"; break;
                    case 210: status = "Waiting for BIDV's Approval"; break;
                    case 401: status = "Waiting for Approval"; break;
                    case 402: status = "Approval Rejected"; break;
                    case 403: status = "Approved"; break;
                    case 404: status = "Rejected"; break;
                    case 310: status = "OnePay Approved"; break;
                    case 100: status = "Pending"; break;
                    default: status = "Failed"; break;
                }
            }
            item.put(TemplateUtils.STATUS_COLUMN, status);
            item.put(TemplateUtils.PLATFORM_COLUMN, itemReport.getPlatform());
            item.put("S_REFUND_TYPE",  itemReport.getActionType());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticRefundGenerator.class.getName());
}
