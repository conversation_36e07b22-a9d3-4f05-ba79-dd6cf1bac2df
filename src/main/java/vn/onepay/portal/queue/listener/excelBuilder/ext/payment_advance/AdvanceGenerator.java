package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import com.onepay.commons.util.Convert;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDao;
import vn.onepay.portal.resources.payment_advance.search_advance.dto.AdvanceDto;

public class AdvanceGenerator implements BaseGenerator<Map> {
    private static final Logger LOGGER = Logger.getLogger(AdvanceGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            Map<String, Object> mOut = PaymentAdvanceDao.getAdvanceList(mIn);

            // sắp xếp lại
            List<AdvanceDto> advs = (List<AdvanceDto>) mOut.get("advanceList");
            // list bút toán theo từng nhóm
            List<AdvanceDto> listAdvanceVcb = new ArrayList<>();
            List<AdvanceDto> listAdvanceTcbToTcb = new ArrayList<>();
            List<AdvanceDto> listAdvanceTcbToOther = new ArrayList<>();
            List<AdvanceDto> listAdvanceVtb = new ArrayList<>();
            List<AdvanceDto> listAdvanceVpb = new ArrayList<>();
            List<AdvanceDto> listPayout = new ArrayList<>();

            // total advance amount
            double totalAdvanceVcb = 0;
            double totalAdvanceTcbToTcb = 0;
            double totalAdvanceTcbToOther = 0;
            double totalAdvanceVtb = 0;
            double totalAdvanceVpb = 0;
            double totalPayout = 0;
            double tmpAmount = 0;
            double totalAdvance = 0;
            // stt
            int vcbIndex = 0;
            int tcbToTcbIndex = 0;
            int tcbToOtherIndex = 0;
            int vtbIndex = 0;
            int vpbIndex = 0;
            int payoutIndex = 0;

            HashMap<String, Object> map = new HashMap<>();
            map.put("fromDate", Convert.toString(formatter.parse(mIn.get("from_date").toString()), "dd/MM/yyyy", ""));
            map.put("toDate", Convert.toString(formatter.parse(mIn.get("to_date").toString()), "dd/MM/yyyy", ""));
            for (AdvanceDto adv : advs) {
                String merchantIdsTrans = adv.getMerchantIds();
                List<String> distinctMerchantId = merchantIdsTrans != null
                        ? Arrays.asList(merchantIdsTrans.split(";,")).stream().distinct().collect(Collectors.toList())
                        : new ArrayList<>();
                String merchantIds = String.join(";",
                        distinctMerchantId.toArray(new String[distinctMerchantId.size()]));
                adv.setMerchantIds(merchantIds);
                tmpAmount = adv.getAdvanceAmountReal();// co the là N_ADVANCE_TOTAL
                adv.setAdvanceDate(adv.getAdvanceDate() != null
                        ? (Convert.toString(formatter.parse(adv.getAdvanceDate()), "dd-MM-yyyy", ""))
                        : "-");

                String strDesc = (adv.getDesc() != null ? adv.getDesc() + "" : "");
                adv.setAdvanceDescription(strDesc);

                Map<String, Object> payoutMap = adv.getPayout();

                if (!(null == payoutMap || payoutMap.isEmpty())){
                    // Cập nhật thông tin khối Payout
                    AdvanceDto advPayout = new AdvanceDto(adv);
                    advPayout.setIndex(++payoutIndex);
                    advPayout.setAccountNumber(payoutMap.getOrDefault("payout_merchant_account", "").toString());
                    advPayout.setBankName(payoutMap.getOrDefault("bank_name", "").toString());
                    advPayout.setAdvanceAmountReal((int)payoutMap.getOrDefault("payout_amount",0));
                    double payoutAdvAmount = advPayout.getAdvanceAmountReal();
                    LOGGER.log(Level.INFO, "Payout adv amount: {0}", payoutAdvAmount);
                    totalPayout += payoutAdvAmount;
                    LOGGER.log(Level.INFO, "Total payout: {0}", totalPayout);
                    listPayout.add(advPayout);
                    
                    // Cập nhật lại khoản tiền của các khối còn lại (VCB,..)
                    tmpAmount -= payoutAdvAmount;
                    LOGGER.log(Level.INFO, "Adv amount remain: {0}", tmpAmount);
                    adv.setAdvanceAmountReal(tmpAmount);
                }
                if ("VIETCOMBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vcbIndex);
                    listAdvanceVcb.add(adv);
                    totalAdvanceVcb += tmpAmount;
                } else if ("TECHCOMBANK".equals(adv.getOnepayBankName()) && ("TECHCOMBANK".equals(adv.getBankName()))) {
                    adv.setIndex(++tcbToTcbIndex);
                    listAdvanceTcbToTcb.add(adv);
                    totalAdvanceTcbToTcb += tmpAmount;
                } else if ("TECHCOMBANK".equals(adv.getOnepayBankName()) && (!"TECHCOMBANK".equals(adv.getBankName()))) {
                    adv.setIndex(++tcbToOtherIndex);
                    listAdvanceTcbToOther.add(adv);
                    totalAdvanceTcbToOther += tmpAmount;
                } else if ("VIETINBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vtbIndex);
                    listAdvanceVtb.add(adv);
                    totalAdvanceVtb += tmpAmount;
                } else if ("VPBANK".equals(adv.getOnepayBankName())) {
                    adv.setIndex(++vpbIndex);
                    listAdvanceVpb.add(adv);
                    totalAdvanceVpb += tmpAmount;
                }
            }
            totalAdvance = totalAdvanceVcb + totalAdvanceTcbToTcb + totalAdvanceTcbToOther + totalAdvanceVtb
                    + totalAdvanceVpb + totalPayout;
            // export
            Map<String, Object> beans = new HashMap<>();

            beans.put("listAdvanceVcb", listAdvanceVcb);
            beans.put("listAdvanceTcbToTcb", listAdvanceTcbToTcb);
            beans.put("listAdvanceTcbToOther", listAdvanceTcbToOther);
            beans.put("listAdvanceVtb", listAdvanceVtb);
            beans.put("listAdvanceVpb", listAdvanceVpb);
            beans.put("listPayout", listPayout);

            map.put("totalAdvanceVcb", Math.round(totalAdvanceVcb));
            map.put("totalAdvanceTcbToTcb", Math.round(totalAdvanceTcbToTcb));
            map.put("totalAdvanceTcbToOther", Math.round(totalAdvanceTcbToOther));
            map.put("totalAdvanceTcb", Math.round(totalAdvanceTcbToTcb) + Math.round(totalAdvanceTcbToOther));
            map.put("totalAdvanceVtb", Math.round(totalAdvanceVtb));
            map.put("totalAdvanceVpb", Math.round(totalAdvanceVpb));
            map.put("totalPayout", Math.round(totalPayout));
            map.put("totalAdvance", Math.round(totalAdvance));

            Calendar calOut = Calendar.getInstance();
            map.put("date_export", "Ngày " + calOut.get(Calendar.DATE) + " tháng " + (calOut.get(Calendar.MONTH) + 1)
                    + " năm " + calOut.get(Calendar.YEAR));
            map.put("date_from_to",
                    "Từ ngày " + Convert.toString(formatter.parse(mIn.get("from_date").toString()), "dd/MM/yyyy", "")
                            + " đến ngày "
                            + Convert.toString(formatter.parse(mIn.get("to_date").toString()), "dd/MM/yyyy", ""));
            beans.put("map", map);
            listMap.add(beans);

            LOGGER.log(Level.INFO, "Beans: {0}", beans);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate ADVANCE error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }
}
