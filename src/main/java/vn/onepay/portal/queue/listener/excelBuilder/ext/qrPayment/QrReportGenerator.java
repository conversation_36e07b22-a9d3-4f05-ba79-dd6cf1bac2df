package vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.refund.DomesticRefundApprovalDao;
import vn.onepay.portal.resources.domestic.report.DomesticReportDao;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReport;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReportConvert;
import vn.onepay.portal.resources.qr.report.QrReportDao;
import vn.onepay.portal.resources.qr.report.dto.QrReport;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.StringUtils;

public class QrReportGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        List<QrReport> result = null;
        try {
            result = QrReportDao.searchQrReport(mIn, QrReportDao.total(mIn));
            this.generateRs(result, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<QrReport> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();

        for (QrReport itemReport : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getReportDate());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchantId());

            // bank Id
            item.put(TemplateUtils.BANK_COLUMN, itemReport.getAcqCode());

            item.put(TemplateUtils.TID_COLUMN, itemReport.getBankTerminalId());

            item.put(TemplateUtils.MID_COLUMN, itemReport.getBankMerchantId());

            item.put(TemplateUtils.PLATFORM_COLUMN, itemReport.getPlatform());

            item.put(TemplateUtils.ACQ_CODE_COLUMN, itemReport.getAcqCode());

            item.put(TemplateUtils.APP_NAME_COLUMN, itemReport.getAppName());

            item.put(TemplateUtils.CLIENT_ID_COLUMN, itemReport.getChannel());
            item.put("S_SOURCE", itemReport.getSource());

            item.put(TemplateUtils.MASKING_COLUMN, itemReport.getMasking());

            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, itemReport.getMerchantName());

            item.put(TemplateUtils.CARD_TYPE_COLUMN, itemReport.getCardType());

            item.put(TemplateUtils.PURCHASE_COUNT_COLUMN, itemReport.getPurchaseCount());

            item.put(TemplateUtils.REFUND_COUNT_COLUMN, itemReport.getRefundCount());

            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, itemReport.getOriginalPurchaseAmount());

            item.put(TemplateUtils.MERCHANT_DISOUNT_COLUMN, itemReport.getMerchantDiscountAmount());

            item.put(TemplateUtils.PARTNER_DISOUNT_COLUMN, itemReport.getPartnerDiscountAmount());

            item.put(TemplateUtils.PAYMENT_AMOUNT_COLUMN, itemReport.getPaymentAmount());


            item.put(TemplateUtils.ORIGINAL_REFUND_AMOUNT_COLUMN, itemReport.getOriginalRefundAmount());

            item.put(TemplateUtils.MERCHANT_DISOUNT_REFUND_COLUMN, itemReport.getMerchantDiscountRefundAmount());

            item.put(TemplateUtils.PARTNER_DISOUNT_REFUND_COLUMN, itemReport.getPartnerDiscountRefundAmount());

            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, itemReport.getRefundAmount());

            // QR Type
            item.put(TemplateUtils.S_QR_TYPE, StringUtils.capitalize(itemReport.getQrType()));

            // Merchant Channel
            item.put(TemplateUtils.S_MERCHANT_CHANNEL, StringUtils.capitalize(itemReport.getMerchantChannel()));

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(QrReportGenerator.class.getName());
}
