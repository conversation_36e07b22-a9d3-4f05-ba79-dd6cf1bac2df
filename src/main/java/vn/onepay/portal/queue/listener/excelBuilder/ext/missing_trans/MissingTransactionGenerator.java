package vn.onepay.portal.queue.listener.excelBuilder.ext.missing_trans;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.advance_transaction.dto.AdvanceTransactionDto;
import vn.onepay.portal.resources.payment_advance.missing_transaction.MissingTransactionDao;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.fasterxml.jackson.databind.ObjectMapper;

public class MissingTransactionGenerator implements BaseGenerator<Map<String, Object>> {
    private static Logger logger = Logger.getLogger(MissingTransactionGenerator.class.getName());
    private static String formatDate = "dd/MM/yyyy HH:mm";

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            Map<String, Object> baseList = MissingTransactionDao.list(mIn);
            List<AdvanceTransactionDto> list = (List<AdvanceTransactionDto>) baseList.get("list");
            generateRs(list, listMap);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportMissingTransaction error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<AdvanceTransactionDto> list, List<Map> listData) throws Exception {
        DateFormat df = new SimpleDateFormat(formatDate);
        int rowNumber = listData.size();
        ObjectMapper objectMapper = new ObjectMapper();
        for (AdvanceTransactionDto data : list) {
            data.setNo(++rowNumber);

            Map<String, Object> obj = objectMapper.convertValue(data, Map.class);
            obj.put("date", df.parse(obj.get("date").toString()));
            listData.add(obj);
        }
    }
}
