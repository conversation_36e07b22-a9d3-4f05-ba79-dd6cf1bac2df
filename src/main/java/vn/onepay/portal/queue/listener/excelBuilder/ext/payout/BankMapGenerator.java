package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.bank_map.BankMapDao;
import vn.onepay.portal.resources.payout.bank_map.dto.BankMapDto;
import vn.onepay.portal.resources.payout.bank_map.dto.BankMapQueryDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BankMapGenerator implements BaseGenerator<BankMapQueryDto> {

    @Override
    public List<Map> generate(BankMapQueryDto query) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<BankMapDto> list = BankMapDao.search(query);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate BANK MAP error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<BankMapDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (BankMapDto itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
                item.put(TemplateUtils.FROM_SWIFT_CODE, itemReport.getFromSwiftCode());
                item.put(TemplateUtils.TO_SWIFT_CODE, itemReport.getToSwiftCode());
                item.put(TemplateUtils.DEST_BANK_ID, itemReport.getDestBankId());
                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());
                item.put(TemplateUtils.DATE_TEXT_COLUMN, itemReport.getDateCreate());
                item.put(TemplateUtils.DESC, itemReport.getDesc());
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(BankMapGenerator.class.getName());
}
