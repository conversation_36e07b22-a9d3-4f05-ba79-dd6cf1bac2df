package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment_advance.search_advance.dao.PaymentAdvanceDetailDao;

public class AdvanceBigMerchantDetailGenerator implements BaseGenerator<Map>{
    private static final Logger LOGGER = Logger.getLogger(AdvanceDetailGenerator.class.getName());
    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            Map<String, Object> parram = new HashMap<>();
            String txnType = mIn.get("transtype") == null ? null: mIn.get("transtype").toString();
            String state = "";
            String transtype = "";
            if (txnType != null) {
                if (txnType.equals("101") || txnType.equals("201") || txnType.equals("301")) {
                    transtype = "PURCHASE,CAPTURE,VOID_REFUND";
                    state = "SUCCESS";
                } else if (txnType.equals("102") || txnType.equals("202") || txnType.equals("302")) {
                    transtype = "REFUND,REFUND_CAPTURE,VOID_CAPTURE";
                    state = "SUCCESS";
                } else if (txnType.equals("103")) {
                    transtype = "PURCHASE,CAPTURE, REFUND, REFUND_CAPTURE, VOID_CAPTURE";
                    state = "FAILED";
                }
            }
            parram.put("id", Long.parseLong(mIn.get("id").toString()));
            parram.put("payChannel",mIn.get("payChannel"));
            parram.put("fromDate",mIn.get("fromDate"));
            parram.put("toDate",mIn.get("toDate"));
            parram.put("merchantId","");
            parram.put("page",0);
            parram.put("pageSize",Integer.MAX_VALUE);  
            parram.put("transactionType",transtype);
            parram.put("state",state);
            LOGGER.info("===========MAP==========" + parram);
            Map<String,Object> list = PaymentAdvanceDetailDao.getAdvanceBigMerchantDetail(parram);
            listMap.add(list);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "GENERATE TRANSACTION HISTORY error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }
}
