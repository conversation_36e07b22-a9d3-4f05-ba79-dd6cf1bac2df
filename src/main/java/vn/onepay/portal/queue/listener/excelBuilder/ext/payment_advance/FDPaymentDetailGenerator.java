package vn.onepay.portal.queue.listener.excelBuilder.ext.payment_advance;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.FixDepositPaymentDao;
import vn.onepay.portal.resources.payment2.fix_deposit_payment.dto.FDDetailPaymentDto;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FDPaymentDetailGenerator implements BaseGenerator<Long> {

    @Override
    public List<Map> generate(Long id) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<FDDetailPaymentDto> list = FixDepositPaymentDao.fixedDepositDetail(0, Integer.MAX_VALUE, id);
            this.generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Fixed Deposit payment Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<FDDetailPaymentDto> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        for (FDDetailPaymentDto itemReport : list) {

            Map item = new HashMap();
            rowNumber++;
            item.put("NO", rowNumber);
            item.put("advance_id", itemReport.getAdvance_id());
            item.put("pay_channels", itemReport.getPay_channels());
            item.put("adv_config_id", itemReport.getAdv_config_id());
            item.put("state", itemReport.getState());
            item.put("merchant_ids", itemReport.getMerchant_ids());
            item.put("bank_account_adv", itemReport.getBank_account_adv());
            item.put("d_create", itemReport.getD_create() != null ? df.format(itemReport.getD_create()) : null);
            item.put("amount", itemReport.getAmount());
            item.put("d_advance",itemReport.getD_advance() != null ? df.format(itemReport.getD_advance()): null);
            item.put("total_txn", itemReport.getTotal_txn());
            item.put("amount_change_to_vnd", itemReport.getAmount_change_to_vnd());
            item.put("amount_est", itemReport.getAmount_est());
            item.put("amount_adv", itemReport.getAmount_adv());
            item.put("fee_total", itemReport.getFee_total());
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(FDPaymentGenerator.class.getName());
}
