package vn.onepay.portal.queue.listener.excelBuilder.ext.qrPayment;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.qr.refund_search.QrRefundSearchDao;
import vn.onepay.portal.resources.qr.refund_search.dto.QrRefund;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.StringUtils;

public class QrPaymentRefundGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<QrRefund> qrRefund = QrRefundSearchDao.search(mIn);
            generateRs(qrRefund.getList(),listMap);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate qr payment Purchase error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<QrRefund> list, List<Map> listData ) throws Exception {

        int index = 0;
        for(QrRefund dto: list)
        {
            Map item = new HashMap();
            index++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, index);

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, dto.getMerchantId());

            // merchant name
            item.put(TemplateUtils.MERCHANT_NAME_COLUMN, dto.getMerchantName());

            // onecomMerchantId
            item.put(TemplateUtils.ONECOM_MERCHANT_ID_COLUMN, dto.getOnecomMerchantId());

            // App NAme
            item.put("S_APP_NAME", dto.getAppName());

            // Card Type
            item.put(TemplateUtils.CARD_TYPE_COLUMN, dto.getInstrument().getType());

            // Channel
            item.put(TemplateUtils.CHANNEL_COLUMN, dto.getChannel());

            // Refund Trans Id
            item.put(TemplateUtils.REFUND_TRANSACTION_ID_COLUMN, dto.getId());

            // bankTransId
            item.put(TemplateUtils.BANK_TRANS_ID, dto.getBankTransId());

            // merchantTransId
            item.put(TemplateUtils.TRANSACTION_ID_COLUMN, dto.getTransactionId());

            // Customer Trans ID
            item.put(TemplateUtils.CUSTOMER_TRANSACTION_ID_COLUMN, dto.getCustomerTransId());

            // QR ID
            item.put("S_QR_ID", dto.getQrId());

            item.put(TemplateUtils.TID_COLUMN,dto.getBankTerminalId());

            item.put(TemplateUtils.MID_COLUMN,dto.getBankMerchantId());

            // Merchant Trans Ref
            item.put(TemplateUtils.MERCHANT_TRANSACTION_REF_COLUMN, dto.getMerchantTxnRef());

            // Masking
            item.put("S_MASKING", dto.getMasking());

            item.put(TemplateUtils.CURRENCY,dto.getCurrency());
            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, dto.getOrderInfo());

            // Acq Code
            item.put(TemplateUtils.ACQ_CODE_COLUMN, dto.getAcqCode());

            // Card Number
            item.put(TemplateUtils.CARD_NUMBER_COLUMN, dto.getInstrument().getNumber());

            // Purchase Date
            item.put("S_PURCHASE_DATE", dto.getCreateTime());

            // Payment Amount
            item.put(TemplateUtils.PURCHASE_AMOUNT_COLUMN, dto.getPaymentAmount());

            // Refund Date
            item.put("S_REFUND_DATE", dto.getRefundTime());

            // Original Refund Amount
            item.put(TemplateUtils.REFUND_AMOUNT_COLUMN, dto.getOriginalRefundAmount());

            // Card Name
            item.put(TemplateUtils.CARD_NAME_COLUMN, dto.getInstrument().getName());

            // Brand Id
            item.put(TemplateUtils.BRAND_ID_COLUMN, dto.getInstrument().getBrandId());

            // Status
            item.put(TemplateUtils.STATUS_COLUMN, dto.getStatus());

            // Platform
            item.put(TemplateUtils.PLATFORM_COLUMN, dto.getPlatform());

            // Merchant Discount Refund
            item.put("N_MERCHANT_DISCOUNT_REFUND", dto.getMerchantDiscountRefund());

            // Partner Discount Refund
            item.put("N_PARTNER_DISCOUNT_REFUND", dto.getPartnerDiscountRefund());

            // Refund Amount
            item.put("N_REFUND_AMOUNT", dto.getRefundAmount());

            // QR Type
            item.put(TemplateUtils.S_QR_TYPE, StringUtils.capitalize(dto.getQrType()));

            // Merchant Channel
            item.put(TemplateUtils.S_MERCHANT_CHANNEL, StringUtils.capitalize(dto.getMerchantChannel()));

            // Refund type
            item.put("S_REFUND_TYPE", dto.getRefundType());
            // Source
            item.put("S_SOURCE", dto.getSource());

            // put into list
            listData.add(item);
        }

    }
    private static final Logger LOGGER = Logger.getLogger(QrPaymentRefundGenerator.class.getName());
}
