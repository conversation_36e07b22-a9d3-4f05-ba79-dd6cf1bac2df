package vn.onepay.portal.queue.listener.excelBuilder.ext.International;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.international.transaction.InternationalTransactionDao;
import vn.onepay.portal.resources.international.transaction.dto.InstallmentMerchantDto;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class InternationalInstallmentMerchantGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<InstallmentMerchantDto> baseList = InternationalTransactionDao.searchInstallmentMerchantReportCommon2(mIn);
            generateRs(baseList.getList(),listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<InstallmentMerchantDto> list, List<Map> listData ) throws Exception {

        int index = 0;
        for(InstallmentMerchantDto dto: list)
        {
            Map item = new HashMap();
            index++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, index);

            // date
            item.put(TemplateUtils.DATE_BANK, dto.getDate_bank());

            // merchant_id
            item.put(TemplateUtils.MERCHANT_ID, dto.getMerchant_id());

            // bank
            item.put(TemplateUtils.INSTALLMENT_BANK_REPORT,dto.getInstallment_bank() );

            // referral partner
            item.put(TemplateUtils.REFERRAL_PARTNER,dto.getReferral_partner() );

            // term
            item.put(TemplateUtils.TERM_BANK, dto.getTerm_bank());

            // purchase_quantity_wait_for
            item.put(TemplateUtils.PURCHASE_QUANTITY_WAIT_FOR,dto.getPurchase_quantity_wait_for_trans() );

            // purchase_total_wait_for
            item.put(TemplateUtils.PURCHASE_TOTAL_WAIT_FOR,dto.getPurchase_total_wait_for_trans());

            // purchase_quantity_approved
            item.put(TemplateUtils.PURCHASE_QUANTITY_APPROVED,dto.getPurchase_quantity_approved_trans());

            //purchase_total_approved
            item.put(TemplateUtils.PURCHASE_TOTAL_APPROVED,dto.getPurchase_total_approved_trans());

            // purchase_quantity_failed
            item.put(TemplateUtils.PURCHASE_QUANTITY_FAILED,dto.getPurchase_quantity_failed_trans());

            // purchase_total_failed
            item.put(TemplateUtils.PURCHASE_TOTAL_FAILED,dto.getPurchase_total_failed_trans());

            // purchase_quantity_void
            item.put(TemplateUtils.PURCHASE_QUANTITY_VOID,dto.getPurchase_quantity_void_trans());

            // total amount reject
            item.put(TemplateUtils.PURCHASE_TOTAL_VOID,dto.getPurchase_total_void_trans());

            // quantity approved
            item.put(TemplateUtils.REFUND_QUANTITY_WAIT_FOR,dto.getRefund_quantity_wait_for_trans());

            // total amount approved
            item.put(TemplateUtils.REFUND_TOTAL_WAIT_FOR,dto.getRefund_total_wait_for_trans());

            // quantity reject
            item.put(TemplateUtils.REFUND_QUANTITY_SUCCESS,dto.getRefund_quantity_success_trans());

            // total amount reject
            item.put(TemplateUtils.REFUND_TOTAL_SUCCESS,dto.getRefund_total_success_trans());

            // put into list
            listData.add(item);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(InternationalInstallmentMerchantGenerator.class.getName());
}
