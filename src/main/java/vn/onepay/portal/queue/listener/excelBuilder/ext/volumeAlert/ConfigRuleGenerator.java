package vn.onepay.portal.queue.listener.excelBuilder.ext.volumeAlert;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.volume_alert.config_rule.ConfigRuleDao;
import vn.onepay.portal.resources.volume_alert.dto.ConfigRuleDto;
import vn.onepay.portal.utils.TemplateUtils;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.commons.util.Convert;

public class ConfigRuleGenerator implements BaseGenerator<Map>, IConstants {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        try {
            String partnerName = mIn.get("partnerName") == null ? "" : mIn.get("partnerName").toString();
            String merchantName = ""; //cancel filter merchant
            String mcc = mIn.get("mcc") == null ? "" : mIn.get("mcc").toString();
            String status = mIn.get("status") == null ? "" : mIn.get("status").toString();
            String groupIdStr = mIn.get("groupId") == null ? "" : mIn.get("groupId").toString();
            int userId = Convert.parseInt(mIn.get(USER_ID).toString(), 0);
            int groupId = Integer.parseInt(groupIdStr);
            int page = 0;
            int pageSize = Integer.MAX_VALUE;
            map = ConfigRuleDao.getListConfigRule(partnerName, mcc, groupId, status, page, pageSize, userId);
            this.generateRs(map, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "ConfigRuleGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(Map<String, Object> map, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        ArrayList<ConfigRuleDto> arrayConfig = (ArrayList<ConfigRuleDto>) map.get("configRule");
        for (ConfigRuleDto itemReport : arrayConfig) {
            Map<String, Object> item = new HashMap<>();
            rowNumber++;
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);
            // item.put(TemplateUtils.S_MERCHANT_ACCOUNT, itemReport.getMerchantName());//cancel merchant column
            item.put(TemplateUtils.S_PARTNER_NAME, itemReport.getPartnerName());
            item.put(TemplateUtils.S_MCC, itemReport.getMcc());
            item.put(TemplateUtils.S_RULE_GROUP, itemReport.getRuleGroupName());
            item.put(TemplateUtils.S_STATUS, convertStatus(itemReport.getStatus()));
            listData.add(item);
        }
    }

    private String convertStatus(String status) {
        if (status == null || status.toLowerCase().contains("non")) {
            return "Non active";
        } else if (status.equalsIgnoreCase("active")) {
            return "Active";
        } else {
            return "Ignor";
        }
    }

    private static final Logger LOGGER = Logger.getLogger(ConfigRuleGenerator.class.getName());

}
