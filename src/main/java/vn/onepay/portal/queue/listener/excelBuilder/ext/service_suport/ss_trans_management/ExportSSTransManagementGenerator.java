package vn.onepay.portal.queue.listener.excelBuilder.ext.service_suport.ss_trans_management;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.service_suport.trans_management.dao.SSTransManagementDao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportSSTransManagementGenerator implements BaseGenerator<Map<String, Object>>, IConstants {
    private static Logger logger = Logger.getLogger(ExportSSTransManagementGenerator.class.getName());

    @Override
    public List<Map> generate(Map<String, Object> mIn) throws Exception {
        List<Map> listMap = new ArrayList<>();
        try {
            List<String> columnList = new ArrayList<String>(Arrays.asList(mIn.get(COLUMN_LIST).toString().split(",")));
            Map dt = new HashMap<>();
            Map<String,Object> data = SSTransManagementDao.searchGeneral(mIn);
            List<Map<String,Object>> list = (List<Map<String,Object>>) data.get("list");
            generateRs(list, listMap, columnList);
            // listMap.add(dt);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ExportSSTransManagementGenerator error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<Map<String,Object>> list, List<Map> listMap, List<String> columns) {
        Map mapData;
        for (Map<String,Object> item : list) {
            mapData = new HashMap();
            for(String column: columns){

                if ("merchantChannel".equalsIgnoreCase(column) && item.get(column) != null){
                    mapData.put(column, "ecom".equalsIgnoreCase(item.get(column).toString()) ? "Ecom" : "Upos");
                } else {
                    mapData.put(column, item.get(column));
                }
            }
            listMap.add(mapData);
        }
    }

}
