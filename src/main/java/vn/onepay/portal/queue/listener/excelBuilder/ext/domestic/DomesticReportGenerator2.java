package vn.onepay.portal.queue.listener.excelBuilder.ext.domestic;

import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.report.DomesticReportDao2;
import vn.onepay.portal.resources.domestic.report.dto.DomesticReport;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DomesticReportGenerator2 implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        List<DomesticReport> result = null;
        try {
            result = DomesticReportDao2.getListDomesticReports(mIn);
            String downloadType = mIn.get(IConstants.DOWNLOADTYPE) == null ? IConstants.EXCEL : mIn.get(IConstants.DOWNLOADTYPE).toString();

            this.generateRs(result, listMap, downloadType);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Domestic Report error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<DomesticReport> list, List<Map> listData, String downloadType) throws Exception {
        int rowNumber = listData.size();
        long totalPurchaseSuccessAmount = 0;
        long totalPurchaseFailedAmount = 0;
        long totalRefundSuccessAmount = 0;
        long totalRefundFailAmount = 0;
        long totalRefundWaitForBankAmount = 0;
        long totalRefundWaitForOnePayAmount = 0;

        int totalPurchaseSuccessCount = 0;
        int totalPurchaseFailedCount = 0;
        int totalRefundSuccessCount = 0;
        int totalRefundFailCount = 0;
        int totalRefundWaitForBankCount = 0;
        int totalRefundWaitForOnepayCount = 0;

        for (DomesticReport itemReport : list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // date
            item.put(TemplateUtils.DATE_COLUMN, itemReport.getReport_date());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getMerchant_id());

            // bank Id
            item.put(TemplateUtils.BANK_COLUMN, itemReport.getAcquirer_name());

            item.put("S_CAIC", itemReport.getCaic());

            item.put("S_CONTRACT_TYPE", itemReport.getContract_type());

            item.put("S_CONTRACT_TYPE", itemReport.getContract_type());

            item.put("S_ACQUIRER", itemReport.getAcquirer_bank());

            // currency
            item.put(TemplateUtils.CURRENCY_COLUMN, "VND");
            item.put("N_PURCHASE", itemReport.getPurchase_success_total());
            item.put("N_PURCHASE_FAIL", itemReport.getPurchase_failed_total());
            item.put("N_REFUND", itemReport.getRefund_success_total());
            item.put("N_REFUND_FAIL", itemReport.getRefund_failed_total());
            item.put("N_REFUND_WFB", itemReport.getRefund_wfb_approval_total());
            item.put("N_REFUND_WFO", itemReport.getRefund_wfo_approval_total());

            item.put("N_TOTAL_PURCHASE", itemReport.getPurchase_success_amount());
            item.put("N_TOTAL_PURCHASE_FAIL", itemReport.getPurchase_failed_amount());
            item.put("N_TOTAL_REFUND", itemReport.getRefund_success_amount());
            item.put("N_TOTAL_REFUND_FAIL", itemReport.getRefund_failed_amount());
            item.put("N_TOTAL_REFUND_WFB", itemReport.getRefund_wfb_approval_amount());
            item.put("N_TOTAL_REFUND_WFO", itemReport.getRefund_wfo_approval_amount());
            // put into list
            totalPurchaseSuccessCount += itemReport.getPurchase_success_total();
            totalPurchaseFailedCount += itemReport.getPurchase_failed_total();
            totalRefundSuccessCount += itemReport.getRefund_success_total();
            totalRefundFailCount += itemReport.getRefund_failed_total();
            totalRefundWaitForBankCount += itemReport.getRefund_wfb_approval_total();
            totalRefundWaitForOnepayCount += itemReport.getRefund_wfo_approval_total();

            totalPurchaseSuccessAmount += itemReport.getPurchase_success_amount();
            totalPurchaseFailedAmount += itemReport.getPurchase_failed_amount();
            totalRefundSuccessAmount += itemReport.getRefund_success_amount();
            totalRefundFailAmount += itemReport.getRefund_failed_amount();
            totalRefundWaitForBankAmount += itemReport.getRefund_wfb_approval_amount();
            totalRefundWaitForOnePayAmount += itemReport.getRefund_wfo_approval_amount();
            listData.add(item);
        }
        if (IConstants.CSV.equals(downloadType)) {
            Map itemTotal = new HashMap();
            itemTotal.put(TemplateUtils.ROW_NUMBER_COLUMN, "");

            // date
            itemTotal.put(TemplateUtils.DATE_COLUMN, "TOTAL");
            // merchant Id
            itemTotal.put(TemplateUtils.MERCHANT_ID_COLUMN, "");

            // bank Id
            itemTotal.put(TemplateUtils.BANK_COLUMN, "");

            itemTotal.put("S_CAIC", "");

            itemTotal.put("S_CONTRACT_TYPE", "");

            itemTotal.put("S_CONTRACT_TYPE", "");

            itemTotal.put("S_ACQUIRER", "");

            // currency
            itemTotal.put(TemplateUtils.CURRENCY_COLUMN, "");

            itemTotal.put("N_PURCHASE", totalPurchaseSuccessCount);
            itemTotal.put("N_PURCHASE_FAIL", totalPurchaseFailedCount);
            itemTotal.put("N_REFUND", totalRefundSuccessCount);
            itemTotal.put("N_REFUND_FAIL", totalRefundFailCount);
            itemTotal.put("N_REFUND_WFB", totalRefundWaitForBankCount);
            itemTotal.put("N_REFUND_WFO", totalRefundWaitForOnepayCount);

            itemTotal.put("N_TOTAL_PURCHASE", totalPurchaseSuccessAmount);
            itemTotal.put("N_TOTAL_PURCHASE_FAIL", totalPurchaseFailedAmount);
            itemTotal.put("N_TOTAL_REFUND", totalRefundSuccessAmount);
            itemTotal.put("N_TOTAL_REFUND_FAIL", totalRefundFailAmount);
            itemTotal.put("N_TOTAL_REFUND_WFB", totalRefundWaitForBankAmount);
            itemTotal.put("N_TOTAL_REFUND_WFO", totalRefundWaitForOnePayAmount);
            listData.add(itemTotal);
        }
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticReportGenerator.class.getName());
}
