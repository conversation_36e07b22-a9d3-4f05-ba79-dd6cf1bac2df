package vn.onepay.portal.queue.listener.excelBuilder.ext.blackListMerchant;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant.BlackListMerchantDao;
import vn.onepay.portal.resources.blacklistmanagement.blacklistmerchant.dto.BlackListMerchantDto;
import vn.onepay.portal.utils.TemplateUtils;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BlackListMerchantGenerator implements BaseGenerator<Map> {

    @Override
    public List<Map> generate(Map mIn) {
        List<Map> listMap = new ArrayList<>();
        try {
            List<BlackListMerchantDto> list = BlackListMerchantDao.getBlackListMerchant(mIn).getList();
            generateRs(list, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate black list merchant error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<BlackListMerchantDto> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(BlackListMerchantDto data:list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // merchant Id
            item.put("S_BUSINESS_REGISTRATION_NAME", data.getBusiness_registration_name());

            // trans Id
            item.put("S_BUSINESS_REGISTRATION_NUMBER", data.getBusiness_registration_number());

            // trans Id
            item.put("S_LEGAL_REPRESENTATIVE", data.getLegal_representative());

            // trans Id
            item.put("S_INDUSTRY", data.getIndustry());


            // Order Info
            item.put("S_ADDRESS", data.getAddress());

            // trans ref
            item.put("S_WEBSITE", data.getWebsite());

            // Card no
            item.put("S_NOTE", data.getNote());

            // currency
            item.put("D_UPDATE", data.getUpdate());

            // amount
            item.put("S_USER", data.getUser());
            // put into list
            listData.add(item);
        }

    }

    private static final Logger LOGGER = Logger.getLogger(BlackListMerchantGenerator.class.getName());
}
