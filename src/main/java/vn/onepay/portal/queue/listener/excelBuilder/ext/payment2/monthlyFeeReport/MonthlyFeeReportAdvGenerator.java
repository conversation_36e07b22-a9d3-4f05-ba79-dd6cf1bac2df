package vn.onepay.portal.queue.listener.excelBuilder.ext.payment2.monthlyFeeReport;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.onepay.commons.util.Convert;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.monthly_fee_report.MonthlyFeeReportDao;
import vn.onepay.portal.resources.payment2.dto.MonthlyReportDetail;

public class MonthlyFeeReportAdvGenerator implements BaseGenerator<Map>, IConstants {
    private static Logger LOGGER = Logger.getLogger(MonthlyFeeReportAdvGenerator.class.getName());

    @Override
    public List<Map> generate(Map mIn) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<Map> resultMap = new ArrayList<>();
        try {
            Integer reportId = (Integer) mIn.get(MFR_ID_MONTHLY_REPORT);
            String exportType = (String) mIn.get("exportType");
            JsonObject monthlyFeeReport = MonthlyFeeReportDao.getMonthlyFeeReport(reportId.toString()).get(0);

            Map<String, Object> monthlyReportDetails = MonthlyFeeReportDao.getAdvanceMonthlyFeeReport(mIn, monthlyFeeReport);
            List<Map> listMap = generateList(monthlyReportDetails, exportType);

            map.put("template", monthlyFeeReport.getString("feeMonthTemplate"));
            if ("excel".equals(exportType)) {
                map.put("trans", listMap);
                map.put("header", monthlyReportDetails.get("header"));
                map.put("total", monthlyReportDetails.get("total"));
                map.put("footer", monthlyReportDetails.get("footer"));
                map.put("fm", monthlyReportDetails.get("fm"));
            }
            if ("pdf".equals(exportType)) {
                map.put("fields", listMap);
                Map params = new HashMap();
                params.putAll((Map) monthlyReportDetails.get("params"));
                map.put("params", params);
            }

            resultMap.add(map);
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "[MONTHLY REPORT DETAIL] ", ex);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return resultMap;
    }

    private List<Map> generateList(Map mapData, String exportType) throws Exception {
        List<Map> listData = new ArrayList<>();
        List<MonthlyReportDetail> monthlyReportDetails = (List<MonthlyReportDetail>) mapData.get(DATA_EXPORT);
        HashMap map = null;
        int count = 1;
        if ("excel".equals(exportType)) {
            for (MonthlyReportDetail item : monthlyReportDetails) {
                map = new HashMap();
                map.put("NO", count++);
                String periodDetail = MonthlyFeeReportDao.processPeriodDetailClone1(item);
                String periodDetailTime = MonthlyFeeReportDao.processPeriodDetailClone2(item);
                map.put("S_DATE", periodDetail);
                map.put("S_DATE_TIME", periodDetailTime);
                map.put("S_DATE_ADVANCE", item.getAdvDate());
                map.put("S_PAYMENT_VOUCHER", item.getPaymentVoucher());
                map.put("S_PAY_CHANNELS", MonthlyFeeReportDao.filterPayChannel(item.getPayChannels().replaceAll(",", "/")));
                map.put("N_TOTAL_TRANS", item.getTotalTrans());
                map.put("N_TOTAL_TRANS_SUCCESS", item.getTotalTransSuccess());
                map.put("N_TOTAL_TRANS_FAIL", item.getTotalTransFailed());
                map.put("N_TOTAL_USD", item.getTotalUsd());
                map.put("N_TOTAL_VND", item.getTotalVnd());
                map.put("N_TOTAL_ORIGINAL_AMOUNT", item.getTotalOriginalAmount());
                map.put("N_TOTAL_MERCHANT_DISCOUNT_AMOUNT", item.getTotalMerchantDiscountAmount());
                map.put("N_TOTAL_PARTNER_DISCOUNT_AMOUNT", item.getTotalPartnerDiscountAmount());
                map.put("N_TOTAL_CHANGE_TO_VND", item.getTotalChangeToVnd());
                map.put("N_TOTAL_ADVANCE", item.getTotalAdvance());
                map.put("N_TOTAL_ADVANCE_P2", item.getTotalAdvanceP2());
                map.put("N_GUARANTEE_AMOUNT", item.getTotalGuaranteeAmount());
                map.put("N_TOTAL_FEE", item.getTotalFee());
                map.put("N_TOTAL_PAYLATER_AMOUNT", item.getTotalPaylaterAmount());
                map.put("N_TOTAL_DISBURSED_AMOUNT", item.getTotalDisbursedAmount());
                map.put("N_TOTAL_FEE_COLLECTED", item.getTotalFeeCollected());
                map.put("N_TOTAL_FEE_RECEIVABLE", item.getTotalFeeReceivable());
                map.put("N_TOTAL_REFUND_HOLD_AMOUNT", item.getTotalRefundHoldAmount());
                map.put("N_TOTAL_REFUND_UNHOLD_AMOUNT", item.getTotalRefundUnholdAmount());
                map.put("N_TOTAL_PAYOUT_AMOUNT", item.getTotalPayoutAmount());
                map.put("N_TOTAL_BANK_TRANSFER_AMOUNT", item.getTotalBankTransferAmount());
                map.put("N_TOTAL_TOPUP_REFUND_AMOUNT", item.getTotalTopupRefundAmount());
                map.put("N_TOTAL_TOTAL_ADJUST_ADV_AMOUNT", item.getTotalTotalAdjustAdvAmount());
                map.put("S_TYPE_ADVANCE_AMOUNT", item.getTypeAdvanceAmount().equals("Full amount") ? "Chưa thu" : "Đã thu cùng bút toán tạm ứng");
                listData.add(map);
            }
        }
        if ("pdf".equals(exportType)) {
            for (MonthlyReportDetail item : monthlyReportDetails) {
                map = new HashMap();
                map.put("NO", count++);
                String periodDetail = MonthlyFeeReportDao.processPeriodDetailClone1(item);
                String periodDetailTime = MonthlyFeeReportDao.processPeriodDetailClone2(item);
                map.put("S_DATE", periodDetail == null ? "" : periodDetail);
                map.put("S_DATE_TIME", periodDetailTime == null ? "" : periodDetailTime);
                map.put("S_DATE_ADVANCE", item.getAdvDate() == null ? "" : item.getAdvDate());
                map.put("S_PAYMENT_VOUCHER", item.getPaymentVoucher() == null ? "" : item.getPaymentVoucher());
                map.put("S_PAY_CHANNELS", MonthlyFeeReportDao.filterPayChannel(item.getPayChannels().replaceAll(",", "/")));
                map.put("N_TOTAL_TRANS", Convert.toString(item.getTotalTrans(), "###,###"));
                map.put("N_TOTAL_TRANS_SUCCESS", Convert.toString(item.getTotalTransSuccess(), "###,###"));
                map.put("N_TOTAL_TRANS_FAIL", Convert.toString(item.getTotalTransFailed(), "###,###"));
                map.put("N_TOTAL_USD", Convert.toString(item.getTotalUsd(), "###,###"));
                map.put("N_TOTAL_VND", Convert.toString(item.getTotalVnd(), "###,###"));
                map.put("N_TOTAL_ORIGINAL_AMOUNT", Convert.toString(item.getTotalOriginalAmount(), "###,###"));
                map.put("N_TOTAL_MERCHANT_DISCOUNT_AMOUNT", Convert.toString(item.getTotalMerchantDiscountAmount(), "###,###"));
                map.put("N_TOTAL_PARTNER_DISCOUNT_AMOUNT", Convert.toString(item.getTotalPartnerDiscountAmount(), "###,###"));
                map.put("N_TOTAL_CHANGE_TO_VND", Convert.toString(item.getTotalChangeToVnd(), "###,###"));
                map.put("N_TOTAL_ADVANCE", Convert.toString(item.getTotalAdvance(), "###,###"));
                map.put("N_TOTAL_ADVANCE_P2", Convert.toString(item.getTotalAdvanceP2(), "###,###"));
                map.put("N_GUARANTEE_AMOUNT", Convert.toString(item.getTotalGuaranteeAmount(), "###,###"));
                map.put("N_TOTAL_FEE", Convert.toString(item.getTotalFee(), "###,###"));
                map.put("N_TOTAL_PAYLATER_AMOUNT", Convert.toString(item.getTotalPaylaterAmount(), "###,###"));
                map.put("N_TOTAL_DISBURSED_AMOUNT", Convert.toString(item.getTotalDisbursedAmount(), "###,###"));
                map.put("N_TOTAL_FEE_COLLECTED", Convert.toString(item.getTotalFeeCollected(), "###,###"));
                map.put("N_TOTAL_FEE_RECEIVABLE", Convert.toString(item.getTotalFeeReceivable(), "###,###"));
                map.put("N_TOTAL_REFUND_HOLD_AMOUNT", Convert.toString(item.getTotalRefundHoldAmount(), "###,###"));
                map.put("N_TOTAL_REFUND_UNHOLD_AMOUNT", Convert.toString(item.getTotalRefundUnholdAmount(), "###,###"));
                map.put("N_TOTAL_PAYOUT_AMOUNT", Convert.toString(item.getTotalPayoutAmount(), "###,###"));
                map.put("N_TOTAL_BANK_TRANSFER_AMOUNT", Convert.toString(item.getTotalBankTransferAmount(), "###,###"));
                map.put("N_TOTAL_TOPUP_REFUND_AMOUNT", Convert.toString(item.getTotalTopupRefundAmount(), "###,###"));
                map.put("N_TOTAL_TOTAL_ADJUST_ADV_AMOUNT", Convert.toString(item.getTotalTotalAdjustAdvAmount(), "###,###"));
                map.put("S_TYPE_ADVANCE_AMOUNT", item.getTypeAdvanceAmount().equals("Full amount") ? "Chưa thu" : "Đã thu cùng bút toán tạm ứng");
                listData.add(map);
            }
        }
        return listData;
    }
}
