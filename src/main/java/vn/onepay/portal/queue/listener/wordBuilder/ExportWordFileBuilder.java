package vn.onepay.portal.queue.listener.wordBuilder;


import vn.onepay.portal.IConstants;
import vn.onepay.portal.queue.listener.excelBuilder.ReportBuilder;

import vn.onepay.portal.queue.message.Message;
import vn.onepay.portal.resources.base.dto.IErrors;

import vn.onepay.portal.utils.TemplateUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExportWordFileBuilder {

    public static void generateContractExportFile(Message<Map> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CONTRACT EXPORT FILE--------------------");
            Map contractData = messageData.getRequestBody();
            String template = TemplateUtils.CONTRACT_PATH + "/" + contractData.get("contractCode") + "/" + contractData.get("contractCode") + ".docx";
            if(contractData.get("mFileTemplate") != null){
                @SuppressWarnings("unchecked")
                Map<String, String> mDataReplace = (Map<String, String>) contractData.get("mDataReplace");
                if(mDataReplace != null && mDataReplace.get("templatePath") != null){
                    template = mDataReplace.get("templatePath");
                    LOGGER.log(Level.INFO, "Template from db: "+template);
                }
            }
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("contractData", contractData)
                    .build()
                    .exportWord(contractData);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate GENERATE CONTRACT EXPORT FILE Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate GENERATE CONTRACT EXPORT FILE successfully: ");
    }

    public static void generateContractFromTemplateDocx(Message<Map<String,Object>> messageData) {
        try {
            LOGGER.log(Level.INFO, "--------------------START GENERATE CONTRACT FROM TEMPLATE DOCX--------------------");
            Map<String,Object> contractData = messageData.getRequestBody();
            // @SuppressWarnings("unchecked")
            String template = contractData.get("templatePath")+"";
            ReportBuilder.newInstance()
                    .template(template)
                    .exportFileName(messageData.getRequestData().get(IConstants.FILE_NAME_FINAL).toString())
                    .setBeanValue("contractData", contractData)
                    .build()
                    .exportContractDocx(contractData);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate GENERATE CONTRACT EXPORT FILE Error: ", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        LOGGER.log(Level.INFO, "Generate GENERATE CONTRACT EXPORT FILE successfully: ");
    }

    private static final Logger LOGGER = Logger.getLogger(ExportWordFileBuilder.class.getName());
}
