package vn.onepay.portal.queue.listener.excelBuilder.ext.domestic;

import vn.onepay.portal.Config;
import vn.onepay.portal.Status;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.domestic.transaction.DomesticTransactionDao;
import vn.onepay.portal.resources.domestic.transaction.dto.DomesticPurchase;
import vn.onepay.portal.utils.TemplateUtils;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DomesticTransactionalGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();
        List<DomesticPurchase> listData = new ArrayList<>();
        try {
            // List<DomesticPurchase> resultOnline = DomesticTransactionDao.searchTransaction(mIn, -1);
            // listData.addAll(resultOnline);

            List<DomesticPurchase> resultReport = DomesticTransactionDao.searchTransaction(mIn, listMap.size());
            listData.addAll(resultReport);
            generateRs(listData, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate international transactional error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private void generateRs(List<DomesticPurchase> list, List<Map> listData ) throws Exception {
        int rowNumber = listData.size();
        for(DomesticPurchase data: list) {
            Map item = new HashMap();
            rowNumber++;
            // row number
            item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

            // bank
            item.put(TemplateUtils.BANK_COLUMN, data.getAcquirer().getAcquirer_name());

            // merchant Id
            item.put(TemplateUtils.MERCHANT_ID_COLUMN, data.getMerchant_id());

            // CAIC
            item.put("S_CAIC", data.getCaic());
            // CAIC
            item.put("S_CONTRACT_TYPE", data.getContract_type());
            
            item.put("S_ACQUIRER", data.getAcquirer_bank());

            // trans Id
            item.put(TemplateUtils.N_TRANSACTION_ID_COLUMN, data.getTransaction_id());

            // Order Info
            item.put(TemplateUtils.ORDER_INFO_COLUMN, data.getOrder_info());

            // S_MERCHANT_TRANSACTION_REF
            item.put(TemplateUtils.TRANS_REF_COLUMN, data.getMerchant_transaction_ref());


            // Card no
            item.put(TemplateUtils.CARD_NO_COLUMN, data.getCard().getCard_number());
            // Card name
            item.put("S_CARD_HOLDER_NAME", data.getCard().getCard_holder_name());

            // payment amount
            item.put(TemplateUtils.AMOUNT_COLUMN, data.getAmount().getTotal());

            //original amount
            item.put(TemplateUtils.ORIGINAL_AMOUNT_COLUMN, data.getOriginalAmount().getTotal());

            String reg = Config.getString("domestic.bank_txn_id_reg", "");
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(data.getAuthorisation().getAuthorisation_info()== null ? "" : data.getAuthorisation().getAuthorisation_info());
            if (m.find()) {
                item.put("S_BANK_TRANS_ID", m.group(1));
            } else {
                item.put("S_BANK_TRANS_ID", "");
            }

            // date
            Date transactionTime = data.getTransaction_time();
            item.put(TemplateUtils.DATE_COLUMN, transactionTime);

            // response code
            item.put(TemplateUtils.RESPONSE_CODE_COLUMN, this.convertCardVertificationCode(data));

            // platform
            item.put(TemplateUtils.PLATFORM_COLUMN, data.getPlatform());

            // source
            item.put(TemplateUtils.S_SOURCE, data.getSource());

            // status
            item.put(TemplateUtils.STATUS_COLUMN, data.getAdvance_status());
            // item.put("S_BANK_TRANS_ID", data.getBank_transaction_id());

            // put into list
            listData.add(item);
        }
    }
    private String convertCardVertificationCode(DomesticPurchase data) throws SQLException {

        String status = String.valueOf(data.getStatus());
        String cardVerificationCode = String.valueOf(data.getCard().getCard_verification_code());
        String advanceStatus = data.getAdvance_status();
        String authTime = data.getAuth_time() != null ? data.getAuth_time().toString() : null;
        int acquirerId = data.getAcquirer().getAcquirer_id();

        // SPECIAL CASE : 17/04/2019
        if (acquirerId == 0 && status.equals("100") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100")) {
            return "No Select Bank";
        }
        if (advanceStatus.equals("Waiting for authentication")) {
            return "";
        }
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("99")) {
            return "Authentication cancelled";
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null
                && (acquirerId == 2 || acquirerId == 6 || acquirerId == 31 || acquirerId == 21)) {
            return "No Response from IB payment"; // case Techcombank, DongAbank, VIB, Viettelpay, CUP && onepay admin
        } else if (status.equals("200") && advanceStatus.equals("Failed") && authTime == null) {
            return "Authentication failed";
        }
        if (status.equals("100") && cardVerificationCode.equals("100")) {
            return "Not process";
        }
        if (status.equals("300") && advanceStatus.equals("Pending") ) {
            return "Not process";
        }
//
        if (status.equals("200") && advanceStatus.equals("Failed") && cardVerificationCode.equals("100") && authTime != null) {
            Integer authCode = data.getAuthorisation().getAuthorisation_code();
            String specialResponseCode = Status.getString("domestic_special.txncode_" + acquirerId +"_"+ authCode.toString(), "1"); // default is 1

            cardVerificationCode = specialResponseCode.length() == 0 ? "1" : specialResponseCode;
        }

        return cardVerificationCode + "-" + Status.getString("domestic_reponse_code." + cardVerificationCode+".text", "Not Response");
    }

    private static final Logger LOGGER = Logger.getLogger(DomesticTransactionalGenerator.class.getName());
}
