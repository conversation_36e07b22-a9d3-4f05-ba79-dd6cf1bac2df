package vn.onepay.portal.queue.listener.excelBuilder.ext.payout;

import org.springframework.stereotype.Component;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.payout.merchant_config.PayoutMerchantConfigDao;
import vn.onepay.portal.resources.payout.merchant_config.dto.MerchantConfigDTO;
import vn.onepay.portal.utils.TemplateUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Component
public class MerchantConfigGenerator implements BaseGenerator<Map<String, String>> {

    @Override
    public List<Map> generate(Map<String, String> mIn) {
        List<Map> listMap = new ArrayList<>();

        try {
            List<MerchantConfigDTO> merchantConfigDTOS = PayoutMerchantConfigDao.search(mIn);
            this.generateRs(merchantConfigDTOS, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate Merchant Config error", e);
            throw IErrors.QUERY_ERROR;
        }

        return listMap;
    }

    private void generateRs(List<MerchantConfigDTO> list, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        try {
            for (MerchantConfigDTO itemReport : list) {
                Map item = new HashMap();
                rowNumber++;
                // row number
                item.put(TemplateUtils.ROW_NUMBER_COLUMN, rowNumber);

                // Id
                item.put(TemplateUtils.PARTNER_ID_COLUMN, itemReport.getPartner_name());

                // swift code
                item.put(TemplateUtils.BUSINESS_REG_NAME_COLUMN, itemReport.getBusiness_reg_name());

                // bank Name
                item.put(TemplateUtils.MERCHANT_NAME_COLUMN, itemReport.getName());

                // bank trans id
                item.put(TemplateUtils.MERCHANT_ID_COLUMN, itemReport.getId());

                // balance before
                item.put(TemplateUtils.MERCHANT_ACCOUNT_COLUMN, itemReport.getAccount_number());

                item.put(TemplateUtils.BALANCE_TEXT_COLUMN, itemReport.getCurrency() + " " + String.format("%,.0f", itemReport.getBalance()));

                // balance After
                item.put(TemplateUtils.VOLUMN_PER_DAY_COLUMN, (itemReport.getVolumn_per_day() != null && itemReport.getVolumn_per_day() != 0L) ? (itemReport.getCurrency() + " " + String.format("%,d", itemReport.getVolumn_per_day())) : "UNLIMITED");

                // amount
                item.put(TemplateUtils.STATE_COLUMN, itemReport.getState());

                // currency
                item.put(TemplateUtils.CREATE, itemReport.getDate());

                // currency
                item.put(TemplateUtils.ACTIVE_DATE_COLUMN, itemReport.getActive_date());

                // currency
                item.put(TemplateUtils.DEACTIVE_DATE_COLUMN, itemReport.getDeactive_date());

                // MCC   
                item.put(TemplateUtils.MCC, itemReport.getMcc());
                // Desc
                item.put(TemplateUtils.DESC, itemReport.getDescription());

                // put into list
                listData.add(item);
            }
        } catch (Exception e) {
            throw IErrors.QUERY_ERROR;
        }
    }

    private static final Logger LOGGER = Logger.getLogger(MerchantConfigGenerator.class.getName());
}
