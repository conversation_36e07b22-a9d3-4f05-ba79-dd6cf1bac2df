package vn.onepay.portal.queue.listener.excelBuilder.ext.exchange_rate;

import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.exchange_rate.ExchangeRateDAO;
import vn.onepay.portal.resources.exchange_rate.dto.ExchangeRateQueryDto;
import vn.onepay.portal.resources.payment_reconciliation.merchant_fee.dto.FeeDataInstallmentDTO;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExchangeRateGenerator implements BaseGenerator<ExchangeRateQueryDto> {
    private static Logger logger = Logger.getLogger(ExchangeRateGenerator.class.getName());

    @Override
    public List<Map> generate(ExchangeRateQueryDto query) throws Exception {
        try {
            // List<Map> feeDataInstallmentDTOs = ExchangeRateDAO.download(mIn);
            List<Map> listMap = new ArrayList<>();
            Date from = new SimpleDateFormat("dd/MM/yyyy").parse(query.getFromDate());
            Date to = new SimpleDateFormat("dd/MM/yyyy").parse(query.getToDate());
            BaseList<Map<String, Object>> listB = ExchangeRateDAO.getListExchangeRate(from,to,query.getPage().toString(),query.getPageSize().toString(),query.getpartnerId().toString(),query.getType());
            List<Map<String, Object>> feeDataInstallmentDTOs = listB.getList();
            this.generateRs(feeDataInstallmentDTOs, listMap);
            return listMap;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Generate On Off Bank Approval History Error ", e);
            throw IErrors.QUERY_ERROR;
        }
    }

    private void generateRs(List<Map<String, Object>> list, List<Map> listData) throws Exception {

        for (Map<String, Object> itemReport : list) {
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            Map item = new HashMap<>();
            item.put("RNUM",itemReport.get("RNUM"));
            item.put("S_SHORT_NAME", itemReport.get("S_SHORT_NAME"));
            item.put("S_CURRENCY_CODE", itemReport.get("S_CURRENCY_CODE"));
            item.put("N_ADV_SELLING_RATE",Integer.parseInt(itemReport.get("N_ADV_SELLING_RATE").toString()));
            item.put("N_ADV_BUYING_RATE", Integer.parseInt(itemReport.get("N_ADV_BUYING_RATE").toString()));
            item.put("S_ADVANCE_TYPE", itemReport.get("S_ADVANCE_TYPE"));
            item.put("D_ADVANCE",formatter.format(itemReport.get("D_ADVANCE")));
            listData.add(item);
        }
    }
}
