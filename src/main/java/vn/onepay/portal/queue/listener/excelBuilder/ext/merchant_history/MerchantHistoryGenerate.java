package vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_history;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.StringUtils;
import vn.onepay.portal.queue.listener.excelBuilder.ext.BaseGenerator;
import vn.onepay.portal.queue.listener.excelBuilder.ext.merchant_history.constant.MerchantDownloadTypeEnum;
import vn.onepay.portal.queue.listener.excelBuilder.ext.refundApproval.RefundApprovalGenerator;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.system_management.cyber_mpgs_management.dao.AcquirerRuleConfigDao;
import vn.onepay.portal.resources.system_management.cyber_mpgs_management.dto.MerchantConfigHistoryDTO;
import vn.onepay.portal.resources.system_management.switch_cyber_mpgs.SwitchCyberMpgsDao;
import vn.onepay.portal.resources.system_management.switch_cyber_mpgs.dto.SwitchMerchantHistoryDTO;

public class MerchantHistoryGenerate implements BaseGenerator<Map<String, String>> {

    private static final Logger LOGGER = Logger.getLogger(RefundApprovalGenerator.class.getName());


    @Override
    public List<Map> generate(Map<String, String> query) {
        String type = query.get("type");
        if (MerchantDownloadTypeEnum.CONFIG.name().equals(type))
            return processDownloadMerchantConfig(query);
        if (MerchantDownloadTypeEnum.SWITCH.name().equals(type))
            return processDownloadMerchantSwitch(query);
        return List.of();
        
    }

    private static List<Map> processDownloadMerchantConfig(Map<String, String> query) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<MerchantConfigHistoryDTO> baseListExtend = AcquirerRuleConfigDao.getMerchantConfigHistory(query);
            generateMerchantConfigRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate MerchantHistoryGenerate Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private static List<Map> processDownloadMerchantSwitch(Map<String, String> query) {
        List<Map> listMap = new ArrayList<>();
        try {
            BaseList<SwitchMerchantHistoryDTO> baseListExtend = SwitchCyberMpgsDao.getSwitchGroupByMerchantHistory(query);
            generateMerchantSwitchRs(baseListExtend, listMap);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Generate MerchantHistoryGenerate Report error", e);
            throw IErrors.QUERY_ERROR;
        }
        return listMap;
    }

    private static void generateMerchantConfigRs(BaseList<MerchantConfigHistoryDTO> baseListExtend, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (MerchantConfigHistoryDTO itemReport : baseListExtend.getList()) {
            Map<String, String> item = new HashMap<>();
            rowNumber++;
            String merchantIDText = itemReport.getMerchantId() 
            + "( " + itemReport.getCategoryCode() + "_" + itemReport.getPayMethod() + "_" + itemReport.getCurrencyCode() + "_" + itemReport.getCvv() + ")";
            item.put("NO", String.valueOf(rowNumber));
            item.put("S_MERCHANT_ID", merchantIDText);
            item.put("S_ACQ_GROUP_BEFORE", itemReport.getGroupNameFrom());
            item.put("S_ACQ_GROUP_AFTER", itemReport.getGroupNameTo());
            item.put("S_FROM_DATE", itemReport.getFromDate());
            item.put("S_TO_DATE", itemReport.getToDate());
            listData.add(item);
            //
        }
    }

    private static void generateMerchantSwitchRs(BaseList<SwitchMerchantHistoryDTO> baseListExtend, List<Map> listData) throws Exception {
        int rowNumber = listData.size();
        for (SwitchMerchantHistoryDTO itemReport : baseListExtend.getList()) {
            Map<String, String> item = new HashMap<>();
            rowNumber++;
            String switchBy = "completed".equalsIgnoreCase(itemReport.getManualSwitchStatus()) ? "Manual" : "HotSwitch";
            String convertTextScenarioTo = StringUtils.isEmpty(itemReport.getScenarioNameTo()) ? "Default" : itemReport.getScenarioNameTo();
            String convertTextScenarioFrom = StringUtils.isEmpty(itemReport.getScenarioNameFrom()) ? "Default" : itemReport.getScenarioNameFrom();
            String runningConfigType = "";
            if ("SUCCESS".equalsIgnoreCase(itemReport.getSwitchStatus())) {
                runningConfigType = "Default".equalsIgnoreCase(convertTextScenarioTo) ? "Default" : "Backup";
            } else {
                runningConfigType = "Default".equalsIgnoreCase(convertTextScenarioTo) ? "Backup" : "Default";
            }
            // 
            String merchantIDText = itemReport.getMerchantId() 
            + "( " + itemReport.getCategoryCode() + "_" + itemReport.getPayMethod() + "_" + itemReport.getCurrencyCode() + "_" + itemReport.getCvv() + ")";
            item.put("NO", String.valueOf(rowNumber));
            item.put("S_MERCHANT_ID", merchantIDText);
            item.put("S_GROUP_NAME", itemReport.getGroupName());
            item.put("S_SOURCE_SCENARIO", convertTextScenarioFrom);
            item.put("S_DESTINATION_SCENARIO", convertTextScenarioTo);
            item.put("S_SWITCH_STATUS", itemReport.getSwitchStatus());
            item.put("S_RUNNING_CONFIG", itemReport.getGroupName());
            item.put("S_RUNNING_CONFIG_TYPE", runningConfigType);
            item.put("D_CREATE", itemReport.getSwitchedDate());
            item.put("S_SWITCH_BY", switchBy);
            item.put("S_ACTOR", itemReport.getActor());
            listData.add(item);
            //
        }
    }

}