package vn.onepay.portal.queue.server;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.broker.BrokerService;
import org.apache.activemq.command.ActiveMQQueue;
import vn.onepay.portal.Config;
import vn.onepay.portal.queue.listener.*;
import vn.onepay.portal.queue.producer.QueueProducer;

import javax.jms.*;
import java.net.URI;
import java.util.logging.Level;
import java.util.logging.Logger;

public class QueueServer {

    private static String queueUri;
    private static String queueDataDirectory;
    private static long queueTempLimit;
    private static long queueMemoryLimit;
    private static long queueStorageLimit;
    private static Queue downloadQueueFastIn;
    private static Queue downloadQueueFastOut;
    private static Queue downloadQueueSlowIn;
    private static Queue downloadQueueSlowOut;
    private static Queue serviceQueueIn;
    private static Queue serviceQueueOut;

    public static void init() {
        queueUri = Config.getQueueUri();
        queueDataDirectory = Config.getQueueDirectory();
        queueTempLimit = Config.getQueueTempLimit();
        queueMemoryLimit = Config.getQueueMemoryLimit();
        queueStorageLimit = Config.getQueueStorageLimit();

        Thread thread = new Thread(() -> run());
        thread.start();
    }

    public static void run() {
        try {
            LOGGER.log(Level.INFO, "------------------------------- STARTING DOWNLOAD QUEUE SERVER -----------------------------------");

            // Initial broker
            BrokerService broker = new BrokerService();
            broker.addConnector(new URI(queueUri));
            broker.setDataDirectory(queueDataDirectory);
            broker.getSystemUsage().getTempUsage().setLimit(1024 * 1024 * queueTempLimit);
            broker.getSystemUsage().getMemoryUsage().setLimit(1024 * 1024 * queueMemoryLimit);
            broker.getSystemUsage().getStoreUsage().setLimit(1024 * 1024 * queueStorageLimit);
            broker.setUseJmx(false);
            if (broker.isStarted()) {
                broker.stop();
                broker.waitUntilStopped();
                broker.start();
            } else {
                broker.start();
            }

            // Initial queue
            downloadQueueFastIn = new ActiveMQQueue("DOWNLOAD_QUEUE_FAST_IN");
            downloadQueueFastOut = new ActiveMQQueue("DOWNLOAD_QUEUE_FAST_OUT");
            downloadQueueSlowIn = new ActiveMQQueue("DOWNLOAD_QUEUE_SLOW_IN");
            downloadQueueSlowOut = new ActiveMQQueue("DOWNLOAD_QUEUE_SLOW_OUT");
            serviceQueueIn = new ActiveMQQueue("SERVICE_QUEUE_IN");
            serviceQueueOut = new ActiveMQQueue("SERVICE_QUEUE_OUT");
            ConnectionFactory connectionFactory = new ActiveMQConnectionFactory("failover:("+queueUri+")?randomize=false");
            Connection connection = connectionFactory.createConnection();
            Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);

            // Initial producer
            MessageProducer downloadQueueFastInProducer = session.createProducer(downloadQueueFastIn);
            MessageProducer downloadQueueFastOutProducer = session.createProducer(downloadQueueFastOut);
            MessageProducer downloadQueueSlowInProducer = session.createProducer(downloadQueueSlowIn);
            MessageProducer downloadQueueSlowOutProducer = session.createProducer(downloadQueueSlowOut);
            MessageProducer serviceQueueInProducer = session.createProducer(serviceQueueIn);
            MessageProducer serviceQueueOutProducer = session.createProducer(serviceQueueOut);
            QueueProducer.init(session, downloadQueueFastInProducer, downloadQueueFastOutProducer, downloadQueueSlowInProducer, downloadQueueSlowOutProducer, serviceQueueInProducer, serviceQueueOutProducer);

            // Initial consumer
            MessageConsumer downloadQueueFastInConsumer = session.createConsumer(downloadQueueFastIn);
            MessageConsumer downloadQueueFastOutConsumer = session.createConsumer(downloadQueueFastOut);
            MessageConsumer downloadQueueSlowInConsumer = session.createConsumer(downloadQueueSlowIn);
            MessageConsumer downloadQueueSlowOutConsumer = session.createConsumer(downloadQueueSlowOut);
            MessageConsumer serviceQueueInConsumer = session.createConsumer(serviceQueueIn);
            MessageConsumer serviceQueueOutConsumer = session.createConsumer(serviceQueueOut);


            // Map queue with consumer
            downloadQueueFastInConsumer.setMessageListener(new DownloadQueueFastInListener());
            downloadQueueFastOutConsumer.setMessageListener(new DownloadQueueFastOutListener());
            downloadQueueSlowInConsumer.setMessageListener(new DownloadQueueSlowInListener());
            downloadQueueSlowOutConsumer.setMessageListener(new DownloadQueueSlowOutListener());
            serviceQueueInConsumer.setMessageListener(new ServiceQueueInListener());
            serviceQueueOutConsumer.setMessageListener(new ServiceQueueOutListener());
            connection.start();

            LOGGER.log(Level.INFO, "-------------------------------  DOWNLOAD QUEUE SERVER START UP SUCCESSFULLY AT " + queueUri + " ------------------------------- ");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error", e);
        }
    }

    public static Queue getDownloadQueueFastIn() {
        return downloadQueueFastIn;
    }

    public static Queue getDownloadQueueFastOut() {
        return downloadQueueFastOut;
    }

    public static Queue getDownloadQueueSlowIn() {
        return downloadQueueSlowIn;
    }

    public static Queue getDownloadQueueSlowOut() {
        return downloadQueueSlowOut;
    }

    public static Queue getServiceQueueIn() {
        return serviceQueueIn;
    }

    public static Queue getServiceQueueOut() {
        return serviceQueueOut;
    }

    private static final Logger LOGGER = Logger.getLogger(QueueServer.class.getName());
}
