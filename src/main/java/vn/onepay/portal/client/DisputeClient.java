package vn.onepay.portal.client;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.dispute.dto.DisputeParam;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.google.gson.Gson;
import static java.nio.charset.StandardCharsets.UTF_8;

public class DisputeClient extends Db implements IConstants {
    private static final Gson gson = new Gson();
    private static final Logger LOGGER = Logger.getLogger(DisputeClient.class.getName());
    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static String DISPUTE_SERVICE_BASE_URL = Config.getString("dispute-service.url", "");
    private static final Integer DISPUTE_TIMEOUT = Config.getInteger("dispute-service.timeout", 60000);
    private static final String DISPUTE_SECURE = Config.getString("dispute-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static final String ONEPAY_DISPUTE_SERVICE_BASE_URL = Config.getString("dispute-service.onepay_dispute_service_base_url", "");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject searchDisputeRefundAmount(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/search-refund-amount";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(FROM_DATE, request.get(FROM_DATE));
        jbody.put(TO_DATE, request.get(TO_DATE));
        jbody.put(DISPUTE_STATUS, request.get(DISPUTE_STATUS));
        jbody.put(MERCHANT_ID, request.get(MERCHANT_ID));
        jbody.put(TRANSACTION_ID, request.get(TRANSACTION_ID));
        jbody.put(ORDER_REF, request.get(ORDER_REF));
        jbody.put(MERCHANT_TRANSACTION_REF, request.get(MERCHANT_TRANSACTION_REF));
        jbody.put(CHANNEL, request.get(CHANNEL));
        jbody.put(ACQUIRER_ID, request.get(ACQUIRER_ID));
        jbody.put(CARD_TYPE, request.get(CARD_TYPE));
        jbody.put(CARD_NUMBER, request.get(CARD_NUMBER));
        jbody.put(AUTHORISATION_CODE, request.get(AUTHORISATION_CODE));
        jbody.put(TRANS_AMOUNT, request.get(TRANS_AMOUNT));
        jbody.put(DISPUTE_AMOUNT, request.get(DISPUTE_AMOUNT));
        jbody.put(DISPUTE_REASON, request.get(DISPUTE_REASON));
        jbody.put(DISPUTE_CODE, request.get(DISPUTE_CODE));
        jbody.put(DISPUTE_STAGE, request.get(DISPUTE_STAGE));
        jbody.put(OUTCOME, request.get(OUTCOME));
        jbody.put(PAGE, request.get(PAGE));
        jbody.put(PAGE_SIZE, request.get(PAGE_SIZE));
        jbody.put(FILTER_TYPE, request.get(FILTER_TYPE));
        jbody.put(USER_ID, request.get(USER_ID));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH DISPUTE RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH DISPUTE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.DISPUTE_ALREADY_EXIST;
        }
        return jsonReturn;
    }

    public static JsonObject searchDispute(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/search";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(FROM_DATE, request.get(FROM_DATE));
        jbody.put(TO_DATE, request.get(TO_DATE));
        jbody.put(DISPUTE_STATUS, request.get(DISPUTE_STATUS));
        jbody.put(MERCHANT_ID, request.get(MERCHANT_ID));
        jbody.put(MERCHANT_ROLE, null != request.get(MERCHANT_ROLE)? request.get(MERCHANT_ROLE):"");
        jbody.put(TRANSACTION_ID, request.get(TRANSACTION_ID));
        jbody.put(ORDER_REF, request.get(ORDER_REF));
        jbody.put(MERCHANT_TRANSACTION_REF, request.get(MERCHANT_TRANSACTION_REF));
        jbody.put(CHANNEL, request.get(CHANNEL));
        jbody.put(ACQUIRER_ID, request.get(ACQUIRER_ID));
        jbody.put(ONEPAY_PIC, request.get(ONEPAY_PIC));
        jbody.put(CARD_TYPE, request.get(CARD_TYPE));
        jbody.put(CARD_NUMBER, request.get(CARD_NUMBER));
        jbody.put(AUTHORISATION_CODE, request.get(AUTHORISATION_CODE));
        jbody.put(DISPUTE_TRANS_CURRENCY, request.get(DISPUTE_TRANS_CURRENCY));
        jbody.put(DISPUTE_CURRENCY, request.get(DISPUTE_CURRENCY));
        jbody.put(DISPUTE_REASON, request.get(DISPUTE_REASON));
        jbody.put(DISPUTE_CODE, request.get(DISPUTE_CODE));
        jbody.put(DISPUTE_STAGE, request.get(DISPUTE_STAGE));
        jbody.put(OUTCOME, request.get(OUTCOME));
        jbody.put(PAGE, request.get(PAGE));
        jbody.put(PAGE_SIZE, request.get(PAGE_SIZE));
        jbody.put(FILTER_TYPE, request.get(FILTER_TYPE));
        jbody.put(USER_ID, request.get(USER_ID));
        jbody.put(DEPARTMENT, request.get(DEPARTMENT));
        jbody.put(MERCHANT_CHANNEL, request.get(MERCHANT_CHANNEL));
        jbody.put(PARTNER_NAME, request.get(PARTNER_NAME));
        jbody.put(TRANSACTION_STATE, request.get(TRANSACTION_STATE));
        jbody.put(TRANSACTION_TYPE, request.get(TRANSACTION_TYPE));
        jbody.put(FRAUD_INVES, request.get(FRAUD_INVES));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH DISPUTE totalItems: " + jsonReturn.getInteger("totalItems", 0));
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH DISPUTE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.DISPUTE_ALREADY_EXIST;
        }
        return jsonReturn;
    }

    public static JsonObject getAcqList() throws Exception {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/acquirers";

        try {
            //set headers
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json");
            headers.put("Content-Type", "application/json");
            // call request
            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, headers, null, 60000, 60000);

            // response
            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");
            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("list", resContent);
            }

        } catch (Exception e) {
            StringBuilder messageErr = new StringBuilder();
            messageErr.append("Error when get all acquirers. Detail: ").append(e.getMessage());
            LOGGER.log(Level.SEVERE, messageErr.toString(), e);
        }

        return jsonReturn;
    }


    public static JsonObject searchDisputeCase(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/search-dispute-case";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(FROM_DATE, request.get(FROM_DATE));
        jbody.put(TO_DATE, request.get(TO_DATE));
        jbody.put(ACQUIRER_ID, request.get(ACQUIRER_ID));
        jbody.put(USER_ID, request.get(USER_ID));
        jbody.put(FILE_NAME, request.get(FILE_NAME));
        jbody.put(PAGE, request.get(PAGE));
        jbody.put(PAGE_SIZE, request.get(PAGE_SIZE));
        jbody.put(FILTER_TYPE, request.get(FILTER_TYPE));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH DISPUTE CASE RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH DISPUTE CASE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.DISPUTE_CASE_ALREADY_EXIST;
        }
        return jsonReturn;
    }

    public static JsonObject searchTransactionHistory(String transId) {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/trans-history/" + transId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Dispute Transaction History by Transaction Id Status Response:" + resStatus);
            LOGGER.info(() -> "Dispute Transaction History by Transaction Id Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject searchTransactionHistoryV2(String transId, String paygate) {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/trans-history/" + transId + "/" + paygate;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Dispute Transaction History by Transaction Id Status Response:" + resStatus);
            LOGGER.info(() -> "Dispute Transaction History by Transaction Id Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject searchDisputeRefundByTrans(String transId,String paygate) {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/get-refund-amount/" + transId + "/" + paygate;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Dispute refund  by Transaction Id Status Response:" + resStatus);
            LOGGER.info(() -> "Dispute refundby Transaction Id Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    // public static JsonObject searchDisputeRefundByRef(String ref,String type) {
    //     JsonObject jsonReturn = null;
    //     String requestUri = "/dispute-backend/dispute/get-refund-amount-by-ref/" + ref+"/"+type;
    //     try {
    //         Map<String, String> signedHeaders = new HashMap<>();
    //         signedHeaders.put("Accept", "application/json");
    //         signedHeaders.put("Content-Type", "application/json");

    //         Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

    //         int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

    //         String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
    //         if (resStatus == 200) {
    //             jsonReturn = new JsonObject(resContent);
    //         }
    //         LOGGER.info(() -> "Dispute refund  by ref Status Response:" + resStatus);
    //         LOGGER.info(() -> "Dispute refundby ref Resopnse:" + resContent);

    //     } catch (Exception e) {
    //         LOGGER.log(Level.SEVERE, e.getMessage(), e);
    //     }
    //     return jsonReturn;
    // }

    public static JsonObject getBusinessCategory() throws Exception {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/business-category";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("business_category", resContent);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject getDisputeCode() throws Exception {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/dispute-code";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("dispute_code", resContent);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject getOperator() throws Exception {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/operators";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("operators", resContent);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getAllOnePayPics(String role){
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/onepay-pics?role=" + role;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("onepay_pics", resContent);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject searchHistoryByTransIds(String transIds) throws Exception {
        JsonObject jsonReturn = null;
        String endCodeStr = URLEncoder.encode(transIds, StandardCharsets.UTF_8.name());
        String requestUri = "/dispute-backend/dispute/trans-history/" + endCodeStr;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("history", resContent);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject searchHistoryByTransIdsV2(String transIds, String paygate) throws Exception {
        JsonObject jsonReturn = null;
        String endCodeStr = URLEncoder.encode(transIds, StandardCharsets.UTF_8.name());
        String requestUri = "/dispute-backend/dispute/trans-history/" + endCodeStr + "/" + paygate;
        logger.log(Level.INFO, () -> "===Search history by trans ids v2: " + transIds + paygate);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200 && !"[]".equals(resContent)) {
                jsonReturn = new JsonObject(resContent);
            } else {
                jsonReturn = new JsonObject();
                jsonReturn.put("history", resContent);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        String data = gson.toJson(jsonReturn); 
        logger.log(Level.INFO, () -> "===Search history SUCCESS: " + data);
        return jsonReturn;
    }

    public static Integer createDispute(DisputeParam disputeParam) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/create";
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("transactionList", disputeParam.getTransactionList());

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "DISPUTE CREATE RESPONSE: " + jsonReturn.toString());
        } else {
            LOGGER.log(Level.SEVERE,
                    "DISPUTE CREATE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.DISPUTE_ALREADY_EXIST;
        }

        return result;
    }

    public static Integer updateDispute(JsonObject body) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/update-dispute/" + body.getValue("id");
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody = body;
        LOGGER.log(Level.INFO, "PUT TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "DISPUTE UPDATE RESPONSE: " + jsonReturn.toString());
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.DISPUTE_NOTE_EXISTED;
        }

        return result;

    }

    public static Integer deleteDispute(JsonObject body) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/delete-dispute/";
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody = body;
        LOGGER.log(Level.INFO, "PUT TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "DISPUTE DELETE RESPONSE: " + jsonReturn.toString());
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "DISPUTE DELETE EXCEPTION: " + jsonReturn.toString());
            result = 0;
            if (responseCode != HttpURLConnection.HTTP_BAD_REQUEST) {
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        }

        return result;
    }

    public static Integer sendDispute(JsonObject body) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/send-dispute";
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody = body;
        LOGGER.log(Level.INFO, "PUT TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "DISPUTE SEND RESPONSE: " + jsonReturn.toString());
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.DISPUTE_NOTE_EXISTED;
        }

        return result;

    }

    public static Integer updateDisputeStatus(JsonObject body) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/update-dispute-status/" + body.getValue("ids");
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody = body;
        LOGGER.log(Level.INFO, "PUT TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "DISPUTE_STATUS UPDATE RESPONSE: " + jsonReturn.toString());
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.DISPUTE_NOTE_EXISTED;
        }

        return result;

    }

    public static Map<String, Object> request(String method, String url, Map<String, String> headers, byte[] content, int connectTimeout, int readTimeout) {
        Map<String, Object> result = null;
        try {
            LOGGER.info("call to dispute url: " + url + ", method: " + method + ", headers: " + headers + ", content: " + (content != null ? new String(content, UTF_8) : "null"));
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod(method);
            if (headers != null)
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    conn.setRequestProperty(header.getKey(), header.getValue());
                }
            if (method.matches("^(POST|PUT)$")) {
                conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length : 0));
                if (content != null && content.length > 0)
                    conn.setDoOutput(true);
            }
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setInstanceFollowRedirects(false);
            conn.connect();
            // Send request content
            if (method.matches("^(POST|PUT)$") && content != null && content.length > 0) {
                DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
                wr.write(content);
                wr.flush();
                wr.close();
            }
            int statusCode = conn.getResponseCode();
            result = new LinkedHashMap<>();
            result.put(HTTP_STATUS_CODE, statusCode);
            result.put(HTTP_STATUS_MESS, conn.getResponseMessage());
            // Get response header
            Map<String, List<String>> resHeaders = conn.getHeaderFields();
            result.put(HTTP_HEADERS, resHeaders);

            // Get response content
            InputStream is = statusCode < 400 ? conn.getInputStream() : conn.getErrorStream();
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1)
                bout.write(buf, 0, length);
            is.close();
            bout.flush();
            byte[] resContent = bout.toByteArray();
            result.put(HTTP_CONTENT, resContent);
            bout.close();
            String logContent = new String(resContent, UTF_8);
            if (logContent.length() > 100)
                logContent = logContent.substring(0, 100) + "...";
            LOGGER.info("status_code: " + statusCode + ", headers: " + resHeaders + ", content: " + logContent);
            // Keepalive
            // conn.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return result;
    }


    public static JsonObject getTotalDispute(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/total-items";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        try {
            // Milliseconds
            int requestTimeOut = DISPUTE_TIMEOUT;
            JsonObject jbody = new JsonObject();
            jbody.put(FROM_DATE, request.get(FROM_DATE));
            jbody.put(TO_DATE, request.get(TO_DATE));
            jbody.put(DISPUTE_STATUS, request.get(DISPUTE_STATUS));
            jbody.put(MERCHANT_ID, request.get(MERCHANT_ID));
            jbody.put(TRANSACTION_ID, request.get(TRANSACTION_ID));
            jbody.put(ORDER_REF, request.get(ORDER_REF));
            jbody.put(MERCHANT_TRANSACTION_REF, request.get(MERCHANT_TRANSACTION_REF));
            jbody.put(CHANNEL, request.get(CHANNEL));
            jbody.put(ACQUIRER_ID, request.get(ACQUIRER_ID));
            jbody.put(CARD_TYPE, request.get(CARD_TYPE));
            jbody.put(CARD_NUMBER, request.get(CARD_NUMBER));
            jbody.put(AUTHORISATION_CODE, request.get(AUTHORISATION_CODE));
            jbody.put(TRANS_AMOUNT, request.get(TRANS_AMOUNT));
            jbody.put(DISPUTE_AMOUNT, request.get(DISPUTE_AMOUNT));
            jbody.put(DISPUTE_REASON, request.get(DISPUTE_REASON));
            jbody.put(DISPUTE_CODE, request.get(DISPUTE_CODE));
            jbody.put(DISPUTE_STAGE, request.get(DISPUTE_STAGE));
            jbody.put(OUTCOME, request.get(OUTCOME));
            jbody.put(PAGE, request.get(PAGE));
            jbody.put(PAGE_SIZE, request.get(PAGE_SIZE));
            jbody.put(FILTER_TYPE, request.get(FILTER_TYPE));
            jbody.put(ONEPAY_PIC, request.get(ONEPAY_PIC));
            jbody.put(DEPARTMENT, request.get(DEPARTMENT));
            jbody.put(MERCHANT_CHANNEL, request.get(MERCHANT_CHANNEL));
            jbody.put(PARTNER_NAME, request.get(PARTNER_NAME));
            jbody.put(TRANSACTION_STATE, request.get(TRANSACTION_STATE));
            jbody.put(TRANSACTION_TYPE, request.get(TRANSACTION_TYPE));
            jbody.put(FRAUD_INVES, request.get(FRAUD_INVES));

            LOGGER.log(Level.INFO, "getTotalDispute POST TO DISPUTE-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

            URL url = new URL(disputeUrl);

            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "POST");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "TOTAL ITEMS DISPUTE RESPONSE: " + jsonReturn);
            } else {
                LOGGER.log(Level.SEVERE,
                        "TOTAL ITEMS DISPUTE: " + responseCode + ", DESC: " + responseMsg);
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                throw IErrors.DISPUTE_ALREADY_EXIST;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject downloadDispute(Map<String, Object> request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/download";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(FROM_DATE, request.get(FROM_DATE));
        jbody.put(TO_DATE, request.get(TO_DATE));
        jbody.put(DISPUTE_STATUS, request.get(DISPUTE_STATUS));
        jbody.put(MERCHANT_ID, request.get(MERCHANT_ID));
        jbody.put(TRANSACTION_ID, request.get(TRANSACTION_ID));
        jbody.put(ORDER_REF, request.get(ORDER_REF));
        jbody.put(MERCHANT_TRANSACTION_REF, request.get(MERCHANT_TRANSACTION_REF));
        jbody.put(CHANNEL, request.get(CHANNEL));
        jbody.put(ACQUIRER_ID, request.get(ACQUIRER_ID));
        jbody.put(CARD_TYPE, request.get(CARD_TYPE));
        jbody.put(CARD_NUMBER, request.get(CARD_NUMBER));
        jbody.put(AUTHORISATION_CODE, request.get(AUTHORISATION_CODE));
        jbody.put(TRANS_AMOUNT, request.get(TRANS_AMOUNT));
        jbody.put(DISPUTE_AMOUNT, request.get(DISPUTE_AMOUNT));
        jbody.put(DISPUTE_REASON, request.get(DISPUTE_REASON));
        jbody.put(DISPUTE_CODE, request.get(DISPUTE_CODE));
        jbody.put(DISPUTE_STAGE, request.get(DISPUTE_STAGE));
        jbody.put(OUTCOME, request.get(OUTCOME));
        jbody.put(PAGE, request.get(PAGE));
        jbody.put(PAGE_SIZE, request.get(PAGE_SIZE));
        jbody.put(FILTER_TYPE, request.get(FILTER_TYPE));
        jbody.put(USER_ID, request.get(USER_ID));
        jbody.put(ONEPAY_PIC, request.get(ONEPAY_PIC));
        jbody.put(DEPARTMENT, request.get(DEPARTMENT));
        jbody.put(MERCHANT_CHANNEL, request.get(MERCHANT_CHANNEL));
        jbody.put(PARTNER_NAME, request.get(PARTNER_NAME));
        jbody.put(TRANSACTION_STATE, request.get(TRANSACTION_STATE));
        jbody.put(TRANSACTION_TYPE, request.get(TRANSACTION_TYPE));
        jbody.put(TRANS_IDS, request.get(TRANS_IDS));
        jbody.put(DISPUTE_IDS, request.get(DISPUTE_IDS));
        jbody.put(FRAUD_INVES, request.get(FRAUD_INVES));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "DOWLOAD DISPUTE RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "DOWLOAD DISPUTE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject getEmailContent(Map mIn) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/email";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(MERCHANT_NAME, mIn.get(MERCHANT_NAME));
        jbody.put(TRANSACTION_ID, mIn.get(TRANSACTION_ID));
        jbody.put(DUE_DATE, mIn.get(DUE_DATE));
        jbody.put(DISPUTE_STAGE, mIn.get(DISPUTE_STAGE));
        jbody.put(DISPUTE_CODE, mIn.get(DISPUTE_CODE));
        jbody.put(ORDER_REF, mIn.get(ORDER_REF));
        jbody.put(AUTHORISATION_CODE, mIn.get(AUTHORISATION_CODE));
        jbody.put(PAYGATE, mIn.get(PAYGATE));
        jbody.put(MERCHANT_ID, mIn.get(MERCHANT_ID));
        jbody.put(DISPUTE_DATE, mIn.get(DISPUTE_DATE));
        jbody.put(DISPUTE_TRANS_AMOUNT, mIn.get(DISPUTE_TRANS_AMOUNT));
        jbody.put(DISPUTE_AMOUNT, mIn.get(DISPUTE_AMOUNT));
        jbody.put(MERCHANT_TRANSACTION_REF, mIn.get(MERCHANT_TRANSACTION_REF));
        jbody.put(DISPUTE_TRANS_DATE,mIn.get(DISPUTE_TRANS_DATE));
        jbody.put(CARD_NUMBER, mIn.get(CARD_NUMBER));
        jbody.put(DISPUTE_IDS, mIn.get(DISPUTE_IDS));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "DOWLOAD DISPUTE RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "DOWLOAD DISPUTE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject getDisputesByKeys(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/dispute-backend/dispute/keys";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("keys", request.get("keys"));

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH DISPUTE BY KEYS RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH DISPUTE BY KEYS CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw IErrors.DISPUTE_ALREADY_EXIST;
        }
        return jsonReturn;
    }
    public static JsonObject searchDisputeById(String disputeId) {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/search-v2/" + disputeId;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            // signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Dispute by ID Status Response:" + resStatus);
            LOGGER.info(() -> "Dispute by ID Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject searchDisputeByIds(JsonObject jBody) throws IOException {
        String requestUri = "/dispute-backend/dispute/search-by-ids/";
        JsonObject jsonReturn = disputePostMethod(requestUri, jBody);
        LOGGER.log(Level.SEVERE, "SEARCH DISPUTE BY IDS RESPONSE: " + jsonReturn);
        return jsonReturn;
    }

    public static JsonObject updateDisputeByBatch(JsonObject jbody) throws IOException {
        String requestUri = "/dispute-backend/dispute/update-by-batch";
        JsonObject jsonReturn = disputePostMethod(requestUri, jbody);
        LOGGER.log(Level.SEVERE, "UPDATE DISPUTE BY BATCH RESPONSE: " + jsonReturn);
        return jsonReturn;
    }


    public static JsonObject getAppleDisputeMerchants() {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/apple-merchants/";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");
            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "All apple merchant Status Response:" + resStatus);
            LOGGER.info(() -> "All apple merchant Response:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getCurrentCaseNo() {
        JsonObject jsonReturn = null;
        String requestUri = "/dispute-backend/dispute/current-case-no/";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            Map<String, Object> resMap = request("GET", DISPUTE_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");
            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Current CaseNo Status Response:" + resStatus);
            LOGGER.info(() -> "Current CaseNo Response:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject createCSVFile(JsonObject body) throws IOException {
        String requestUri = "/dispute-backend/dispute/create-csv-file/";
        JsonObject jsonReturn = disputePostMethod(requestUri, body);
        LOGGER.log(Level.SEVERE, "SEARCH DISPUTE BY IDS RESPONSE: " + jsonReturn);
        return jsonReturn;
    }

    private static JsonObject disputePostMethod(String requestURI, JsonObject jbody) throws IOException {
        JsonObject jsonReturn;
        int expires = 300;
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = DISPUTE_TIMEOUT;

        LOGGER.log(Level.INFO, "POST TO DISPUTE-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String disputeUrl = DISPUTE_SERVICE_BASE_URL + requestURI;

        URL url = new URL(disputeUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        Util.setRequestMethod(connection, POST);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + jbody.toString().getBytes().length);

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }
}
