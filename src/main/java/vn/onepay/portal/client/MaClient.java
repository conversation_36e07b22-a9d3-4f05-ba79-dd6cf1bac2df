package vn.onepay.portal.client;

import com.onepay.commons.util.Convert;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.domestic.refund.dto.refund.RefundConfirmReq;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;
import static vn.onepay.portal.Util.gson;

public class MaClient extends Db {


    private static final Logger LOGGER = Logger.getLogger(MaClient.class.getName());
    private static String ONEPAY_MA_SERVICE_BASE_URL = Config.getString("ma-service.url", "");
    //    private static String ONEPAY_PCS_SERVICE_PREFIX = PropsUtil.get("pcs.service.prefix", "");
//    private static String ONEPAY_PCS_SERVICE_NAME = PropsUtil.get("pcs.service.name", "");
//    private static String ONEPAY_PCS_SERVICE_REGION = PropsUtil.get("pcs.service.region", "");
//    private static String ONEPAY_PCS_SERVICE_CLIENT_ID = PropsUtil.get("pcs.service.client.id", "");
//    private static String ONEPAY_PCS_SERVICE_AUTH_TYPE = PropsUtil.get("pcs.service.ows.request", "");
//    private static String ONEPAY_PCS_SERVICE_AUTH_ALGORITHM = PropsUtil.get("pcs.service.ows.algorithm", "");
//    private static String ONEPAY_PSP_SERVICE_CLIENT_KEY = PropsUtil.get("pcs.service.client.key", "");
    private static final String ONEPAY_ONECREDIT_SERVICE_URL = Config.getString("onecredit-service.url", "");
    private static final Integer ONEPAY_ONECREDIT_SERVICE_TIMEOUT = Config.getInteger("onecredit-service.timeout", 60000);
    private static final String ONEPAY_ONECREDIT_SERVICE_SECURE = Config.getString("onecredit-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }
    public static JsonObject manualRefundApporval(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundApporval("/manual", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }
    public static JsonObject reverseDueRefundApporval(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundReverseDue("/reverseDue", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }
    public static JsonObject approveRefundApporval(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundApporval("/approve", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }
    public static JsonObject approveRefundDomestic(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundDomestic("/approve", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }

    public static JsonObject approveRefundApporvalQr(String approvalId, String userId, String xRequestId) {
        return patchRefundQr("/onepay-approve", approvalId, userId, xRequestId);
    }

    public static JsonObject approveRefundCup(String approvalId, String merchantId, String accessCode, String hashCode, String merchantTransRef, String orgMerchantTransRef, Double amount, String operatorId) throws Exception {
        Map<String, Object> req = new HashMap<>();
        req.put("command_id", "CONFIRM_REFUND");
        Map<String, String> vpc = new HashMap<>();
        vpc.put("vpc_Command", "refund");
        vpc.put("vpc_Merchant", merchantId);
        vpc.put("vpc_AccessCode", accessCode);
        vpc.put("vpc_MerchTxnRef", merchantTransRef);
        vpc.put("vpc_OrgMerchTxnRef", orgMerchantTransRef);
        vpc.put("vpc_Amount", Convert.toString(amount * 100, "0"));
        vpc.put("vpc_Operator", operatorId);
        vpc.put("vpc_Version", "2");
        vpc.put("vpc_SecureHash", Util.createMerchantHash(vpc, hashCode, "2"));
        req.put("vpc", vpc);

        JsonObject jsonResult = postRefundCup(req);
        LOGGER.info("[REFUND CUP RES] res=" + Util.mask(jsonResult.encode()));
        return jsonResult;
    }

    public static JsonObject rejectRefundApporval(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundApporval("/reject", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }

    public static JsonObject rejectAprovalDomestic(String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        return patchRefundDomestic("/reject", approvalId, userId, xRequestId, merchant_id, transaction_reference, currency);
    }

    public static JsonObject rejectRefundApporvalQr(String approvalId, String userId, String xRequestId) {
        return patchRefundQr("/onepay-reject", approvalId, userId, xRequestId);
    }

    public static JsonObject rejectRefundCup(Integer approvalId, String operatorId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ONECREDIT.cancel_refund(?,?,?,?)}");
            cs.setInt(1, approvalId);
            cs.setString(2, operatorId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String sResult = cs.getString(4);
            int nResult = cs.getInt(3);
            if (nResult == 0) {
                logger.severe("DB load Acceptance error: " + sResult);
                throw new Exception("" + sResult);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return new JsonObject();
    }

    public static JsonObject manualRefundCup(Integer approvalId, String operatorId) throws Exception {
        Exception exception = null;
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection112();
            cs = con.prepareCall("{ call ONEPORTAL.PKG_ONECREDIT.confirm_refund_cup(?,?,?,?)}");
            cs.setInt(1, approvalId);
            cs.setString(2, operatorId);
            cs.registerOutParameter(3, OracleTypes.NUMBER);
            cs.registerOutParameter(4, OracleTypes.VARCHAR);
            cs.execute();
            String sResult = cs.getString(4);
            int nResult = cs.getInt(3);
            if (nResult == 0) {
                logger.severe("DB load Acceptance error: " + sResult);
                throw new Exception("" + sResult);
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "", e);
            exception = e;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        if (exception != null)
            throw exception;
        return new JsonObject();
    }

    private static JsonObject patchRefundQr(String action , String approvalId, String userId, String xRequestId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/refund-approval/mpay/" + approvalId;
        String requestMethod = "PATCH";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;
        JsonObject jbody = new JsonObject();
        jbody.put("id", approvalId);
        jbody.put("op", "replace");
        jbody.put("path", action);
        try {
            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));
            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");

            connection.setRequestProperty(X_USER_ID, userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    private static JsonObject patchRefundDomestic(String action , String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/refund/domestic/" + approvalId;
        String requestMethod = "PATCH";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = 60000;
        JsonObject value = new JsonObject();
        value.put("merchant_id", merchant_id);
        value.put("transaction_reference", transaction_reference);
        value.put("currency", currency);
        JsonObject jbody = new JsonObject();
        jbody.put("id", approvalId);
        jbody.put("op", "replace");
        jbody.put("path", action);
        jbody.put("value", value);
        try {
            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));
            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");

            connection.setRequestProperty(X_USER_ID, userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }
    private static JsonObject patchRefundApporval(String action , String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/refund/international/" + approvalId;

        String requestMethod = "PATCH";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        JsonObject value = new JsonObject();
        value.put("merchant_id", merchant_id);
        value.put("transaction_reference", transaction_reference);
        value.put("currency", currency);

        JsonObject jbody = new JsonObject();
//        jbody.put("partner_name", Utils.deAccent(partnerName));

        jbody.put("id", approvalId);
        jbody.put("op", "replace");
        jbody.put("path", action);
        jbody.put("value", value);
        /* 2022-02-09 . hungld add
         * ma-service end point handles request approve/ reject refund
         * from ma or/and iportal. Because of delay problem, so we add this parameter for
         * skipping call synchronize when send request from iportal
         */
        jbody.put("skipCallSynchronize", true);

        try {

            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));

            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;

            URL url = new URL(aspOrderStateQueryURL);
//            Util.allowMethods("PATCH");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

//            connection.setRequestMethod(requestMethod);
//            Util.
//            connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty(X_USER_ID, userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    private static JsonObject patchRefundReverseDue(String action , String approvalId, String userId, String xRequestId, String merchant_id, String transaction_reference, String currency) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/refund/international/" + approvalId;

        String requestMethod = "PATCH";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        JsonObject value = new JsonObject();
        value.put("merchant_id", merchant_id);
        value.put("transaction_reference", transaction_reference);
        value.put("currency", currency);

        JsonObject jbody = new JsonObject();
//        jbody.put("partner_name", Utils.deAccent(partnerName));

        jbody.put("id", approvalId);
        jbody.put("op", "replace");
        jbody.put("path", action);
        jbody.put("value", value);

        try {

            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));


            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;

            URL url = new URL(aspOrderStateQueryURL);
//            Util.allowMethods("PATCH");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

//            connection.setRequestMethod(requestMethod);
//            Util.
//            connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty(X_USER_ID, userId);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + jsonReturn.toString());
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + Util.mask(jsonObject.encodePrettily()));
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject patchQueryDomesticTransaction(String merchantId, String currency, String transactionId, String xRequestId, String userId, String ipAddress) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/transaction/domestic/" + transactionId;

        String requestMethod = "PATCH";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        JsonObject value = new JsonObject();
        value.put("merchant_id", merchantId);
        value.put("currency", currency);

        JsonObject jbody = new JsonObject();
//        jbody.put("partner_name", Utils.deAccent(partnerName));

        jbody.put("id", transactionId);
        jbody.put("op", "replace");
        jbody.put("path", "/query");
        jbody.put("value", value);


        try {


            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));


            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;

            URL url = new URL(aspOrderStateQueryURL);
//            Util.allowMethods("PATCH");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

//            connection.setRequestMethod(requestMethod);
//            Util.
//            connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty(X_USER_ID, userId);
            connection.setRequestProperty("X-Real-IP", ipAddress);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject patchUpdateDomesticRefund(RefundConfirmReq req, String transactionId, String xRequestId, String userId, String ipAddress) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/refund/domestic/" + transactionId + "/update";

        String requestMethod = "PATCH";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        JsonObject value = new JsonObject();
        value.put("transactionId", req.getTransactionId());
        value.put("merchantId", req.getMerchantId());
        value.put("currencyCode", req.getCurrencyCode());
        value.put("amount", req.getAmount());
        value.put("operatorId", req.getOperatorId());
        value.put("clientIp", req.getClientIp());
        value.put("refund_transactionId", req.getRefund_transactionId());
        value.put("commandCode", req.getCommandCode());
        value.put("status", req.getStatus());
        value.put("additionInfo", req.getAdditionInfo());

        JsonObject jbody = new JsonObject();
//        jbody.put("partner_name", Utils.deAccent(partnerName));

        jbody.put("id", transactionId);
        jbody.put("op", "replace");
        jbody.put("path", "update");
        jbody.put("value", value);


        try {


            LOGGER.log(Level.INFO, "PATCH TO MA-SERVICE");
            LOGGER.log(Level.INFO, "url: " + ONEPAY_MA_SERVICE_BASE_URL + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(jbody.encode()));


            String aspOrderStateQueryURL = ONEPAY_MA_SERVICE_BASE_URL + requestURI;

            URL url = new URL(aspOrderStateQueryURL);
//            Util.allowMethods("PATCH");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

//            connection.setRequestMethod(requestMethod);
//            Util.
//            connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty(X_USER_ID, userId);
            connection.setRequestProperty("X-Real-IP", ipAddress);
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.encode().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "MA-SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject postRefundCup(Map<String, Object> map) throws Exception {
        LOGGER.info("Client request map : " + map);
        byte[] params = gson.toJson(map).getBytes("UTF-8");

        HttpURLConnection conn = (HttpURLConnection) (new URL(ONEPAY_ONECREDIT_SERVICE_URL)).openConnection();
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(ONEPAY_ONECREDIT_SERVICE_TIMEOUT);

        conn.setRequestMethod("POST");
        conn.setRequestProperty("User-Agent", "Onecredit HTTP Client");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Content-Length", params != null ? String.valueOf(params.length) : "0");
        conn.setRequestProperty(X_SECURE_HASH, genHMACSHA256(params, ONEPAY_ONECREDIT_SERVICE_SECURE));

        conn.setUseCaches(false);
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setInstanceFollowRedirects(false);

        // Send request params
        DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
        wr.write(params);
        wr.flush();
        wr.close();

        int responseCode = conn.getResponseCode();
        LOGGER.fine("response_code=" + responseCode);
        if (responseCode != 200) {
            LOGGER.log(Level.SEVERE, "Request fail: http responseCode=" + responseCode);
            return null;
        }
        //Get response header
        Map<String, List<String>> headers = conn.getHeaderFields();
        String localHash = headers.get(X_SECURE_HASH) != null ? headers.get(X_SECURE_HASH).get(0) : "";

        //Get response content
        InputStream is = null;
        if (responseCode == 200) {
            is = conn.getInputStream();
        } else {
            is = conn.getErrorStream();
        }
        byte[] content = null;
        if (is != null) {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1) {
                bout.write(buf, 0, length);
            }
            bout.flush();
            content = bout.toByteArray();
            bout.close();
        }
        conn.disconnect();
        //logger.fine("content=" + (new String(content, "UTF-8")));
        if (localHash.equalsIgnoreCase(genHMACSHA256(content, ONEPAY_ONECREDIT_SERVICE_SECURE))) {
            if (content != null && content.length > 0 && responseCode == 200) {
                Map mapRes = gson.fromJson(new String(content, "UTF-8"), HashMap.class);
                JsonObject jsonRes =  new JsonObject(mapRes);
                LOGGER.info("Client response map : " + jsonRes.encode());
                return jsonRes;
            } else {
                return null;
            }
        } else {
            LOGGER.log(Level.SEVERE, "Invalid Hash");
            return null;
        }
    }

    private static String genHMACSHA256(byte[] data, String secretKey) throws Exception {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(Convert.decodeHexa(secretKey.getBytes()), "HMACSHA256");
            Mac mac = Mac.getInstance("HMACSHA256");
            mac.init(signingKey);
            return Convert.toHexString(mac.doFinal(data));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            return "";
        }
    }

    public static JsonObject manualRefundApporvalQr(String approvalId, String userId, String xRequestId) {
        return patchRefundQr("/onepay-manual", approvalId, userId, xRequestId);
    }

}
