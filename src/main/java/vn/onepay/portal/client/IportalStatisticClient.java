package vn.onepay.portal.client;

import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpClient;
import io.vertx.core.http.HttpClientRequest;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.*;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.dispute.dto.DisputeParam;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;
import static vn.onepay.portal.Util.sendResponse;

public class IportalStatisticClient extends Db implements IConstants {
    private static final Logger LOGGER = Logger.getLogger(IportalStatisticClient.class.getName());
    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static String IPORTAL_STATISTIC_SERVICE_BASE_URL = Config.getString("iportal-statistic-service.url", "http://localhost:9089") ;
    private static final Integer IPORTAL_STATISTIC_TIMEOUT = Config.getInteger("iportal-statistic-service.timeout", 600000);
    private static final String IPORTAL_STATISTIC_SECURE = Config.getString("iportal-statistic-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    public static final String CONTENT_TYPE = "Content-Type";
    public static final String ACCEPT_LANGUAGE = "Accept-Language";
    public static final String USER_AGENT = "User-Agent";
    public static final String AUTHORIZATION = "Authorization";

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject internationalDownloadClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/international/file";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("target", request.get("target"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));
        jbody.put("userId", request.get("userId"));
        jbody.put("acquirerId", request.get("acquirerId"));
        jbody.put("source", request.get("source"));

        LOGGER.log(Level.INFO, "POST TO DOWNLOAD IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);
        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
//
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            // LOGGER.log(Level.SEVERE, "DOWNLOAD STATISTIC RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "DOWNLOAD STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }

    public static JsonObject domesticDownloadClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/domestic/file";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("target", request.get("target"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));


        LOGGER.log(Level.INFO, "POST TO DOWNLOAD IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);

        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            // LOGGER.log(Level.SEVERE, "DOWNLOAD STATISTIC RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "DOWNLOAD STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }

    public static JsonObject domesticSearchClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/domestic";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("target", request.get("target"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));


        LOGGER.log(Level.INFO, "POST TO SEARCH IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);

        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH STATISTIC RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }

    public static JsonObject domesticDetailClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/domestic-detail";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("target", request.get("target"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));
        jbody.put("dateTo", request.get("dateTo"));
        jbody.put("acquirerId", request.get("acquirerId"));


        LOGGER.log(Level.INFO, "POST TO SEARCH IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);

        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "DETAIL STATISTIC DOMESTIC RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }

    public static JsonObject internationalDetailClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/international-detail";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        // jbody.put("target", request.get("target"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));
        jbody.put("dateTo", request.get("dateTo"));
        jbody.put("acquirerId", request.get("acquirerId"));
        jbody.put("source", request.get("source"));

        LOGGER.log(Level.INFO, "POST TO SEARCH IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);

        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "DETAIL STATISTIC INTERNATIONAL RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }

    public static JsonObject internationalSearchClient(Map request) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/statistic-service/statistic-chart/international";
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = IPORTAL_STATISTIC_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("target", request.get("target"));
        jbody.put("userId", request.get("userId"));
        jbody.put("acquirerId", request.get("acquirerId"));
        jbody.put("merchantId", request.get("merchantId"));
        jbody.put("cardType", request.get("cardType"));
        jbody.put("dateFrom", request.get("dateFrom"));
        jbody.put("source", request.get("source"));

        LOGGER.log(Level.INFO, "POST TO SEARCH IPORTAL_STATISTIC-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String statisticUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + requestURI;
        logger.info("URL-STATISTIC" + statisticUrl);

        URL url = new URL(statisticUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");

        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            LOGGER.log(Level.SEVERE, "SEARCH STATISTIC RESPONSE: " + jsonReturn);
        } else {
            LOGGER.log(Level.SEVERE,
                    "SEARCH STATISTIC CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            throw new Exception("STATISTIC EXCEPTION");
        }
        return jsonReturn;
    }


    public static void getPartnersByUserId(Integer userId, String paygate, RoutingContext ctx) {
        JsonObject result = new JsonObject();
        String requestUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + "/statistic-service/partners";
        JsonObject body = new JsonObjectBuilder()
                .put(USER_ID, userId)
                .put(PAYGATE, paygate)
                .build();
        Future<JsonObject> p = Future.future();
        sendHttpRequest(requestUrl, body, p);
        p.setHandler(res -> {
            if (res.succeeded()) {
                int statusCode = res.result().getInteger("status_code");
                if (statusCode == 200) {
                    logger.info("Call API successful: " + requestUrl);
                    result.put("status_code", statusCode);
                    result.put("data", res.result().getJsonArray("data"));
                }
                sendResponse(ctx, statusCode, result.getJsonArray("data"));
            } else {
                logger.log(Level.SEVERE, "An error has occurred when call API : " + requestUrl);
                sendResponse(ctx, 500, new JsonObject());
            }
        });
     }


    public static void getMerchantsByUserId(Integer userId, String paygate, RoutingContext ctx) {
        JsonObject result = new JsonObject();
        String requestUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + "/statistic-service/merchants";
        JsonObject body = new JsonObjectBuilder()
                .put(USER_ID, userId)
                .put(PAYGATE, paygate)
                .build();
        Future<JsonObject> p = Future.future();
        sendHttpRequest(requestUrl, body, p);
        p.setHandler(res -> {
            if (res.succeeded()) {
                int statusCode = res.result().getInteger("status_code");
                if (statusCode == 200) {
                    logger.info("Call API successful: " + requestUrl);
                    result.put("status_code", statusCode);
                    result.put("data", res.result().getJsonArray("data"));
                }
                sendResponse(ctx, statusCode, result.getJsonArray("data"));
            } else {
                logger.log(Level.SEVERE, "An error has occurred when call API : " + requestUrl);
                sendResponse(ctx, 500, new JsonObject());
            }
        });
    }

    public static void getAcquirers(RoutingContext ctx) {
        JsonObject result = new JsonObject();
        String requestUrl = IPORTAL_STATISTIC_SERVICE_BASE_URL + "/statistic-service/acquirers";
        JsonObject body = new JsonObjectBuilder()
                .build();
        Future<JsonObject> p = Future.future();
        sendHttpRequest(requestUrl, body, p);
        p.setHandler(res -> {
            if (res.succeeded()) {
                int statusCode = res.result().getInteger("status_code");
                if (statusCode == 200) {
                    logger.info("Call API successful: " + requestUrl);
                    result.put("status_code", statusCode);
                    result.put("data", res.result().getJsonArray("data"));
                }
                sendResponse(ctx, statusCode, result.getJsonArray("data"));
            } else {
                logger.log(Level.SEVERE, "An error has occurred when call API : " + requestUrl);
                sendResponse(ctx, 500, new JsonObject());
            }
        });
    }

    public static void sendHttpRequest(String requestURL, JsonObject body, Handler<AsyncResult<JsonObject>> handler) {
        JsonObject response = new JsonObject();
        response.put("status_code", 500);
        HttpClientRequest clientRequest = Server.httpClient.requestAbs(HttpMethod.POST, requestURL);
        try {
            Buffer buffer = Buffer.buffer(body.encode(), "UTF-8");
            logger.fine("send iportal-statistic-service: " + clientRequest.absoluteURI() + ", content: " + buffer);
            clientRequest.putHeader(HttpHeaders.ACCEPT.toString(), IConstants.APPLICATION_JSON)
                    .putHeader(HttpHeaders.CONTENT_TYPE.toString(), IConstants.APPLICATION_JSON)
                    .putHeader(HttpHeaders.CONTENT_LENGTH + "", buffer.length() + "")
                    .handler(res -> res.bodyHandler(resData -> {
                        int statusCode = res.statusCode();
                        logger.fine("send iportal-statistic-service response code: " + statusCode + ", content:" + new String(resData.getBytes()));
                        response.put("status_code", statusCode);
                        response.put("data", new JsonArray(new String(resData.getBytes())));
                        handler.handle(Future.succeededFuture(response));
                    }))
                    .end(buffer);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Error call: " + requestURL, ex.getMessage());
            handler.handle(Future.failedFuture(new Exception("AN INTERNAL SERVER")));
        }
    }
}
