package vn.onepay.portal.client;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;

public class MpPermissionClient extends Db {
	private static final Logger logger = Logger.getLogger(MpPermissionClient.class.getName());

    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static final String CLIENT_ID = "iportal";
    private static final String MP_PERMISSION_SERVICE_BASE_URL = Config.getString("mp-permission-service.url", "");
    private static final Integer MP_PERMISSION_TIMEOUT = Config.getInteger("mp-permission-service.timeout", 60000);
    private static final DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject sendRequest(String reqUrl, String method, JsonObject body) throws Exception {
        JsonObject jsonReturn = null;
        
        try {
            String permissionUrl = MP_PERMISSION_SERVICE_BASE_URL + reqUrl;

            URL url = new URL(permissionUrl);
            Date dateTime = new Date();

            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();

            Util.setRequestMethod(httpConn, method);
            httpConn.setRequestProperty("Accept", "application/json");
            httpConn.setRequestProperty("Content-Type", "application/json");
            httpConn.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            httpConn.setRequestProperty("X-OP-Expires", String.valueOf(MP_PERMISSION_TIMEOUT));
            httpConn.setRequestProperty(S_CLIENT_ID, CLIENT_ID);

            httpConn.setUseCaches(false);
            httpConn.setDoInput(true);
            httpConn.setDoOutput(true);

            if(body != null){
                String contentLength = Integer.toString(body.toString().getBytes().length);
                httpConn.addRequestProperty("Content-Length", "" + contentLength);
                
                DataOutputStream outputStream = new DataOutputStream(httpConn.getOutputStream());
                outputStream.write(body.encode().getBytes(UTF_8));
                outputStream.flush();
                outputStream.close();
            }

            int respCode = httpConn.getResponseCode();
            String respMsg = httpConn.getResponseMessage();

            logger.log(Level.INFO, "Permission Url: ", url);
            logger.log(Level.INFO, "Response Code: ", respCode);
            logger.log(Level.INFO, "Response Msg: ", respMsg);
            
            InputStream inputStream = null;
            if (respCode == HttpURLConnection.HTTP_CREATED || respCode == HttpURLConnection.HTTP_OK) {
                inputStream = httpConn.getInputStream();
            } else {
                inputStream = httpConn.getErrorStream();
            }

            BufferedReader buffReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = buffReader.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            buffReader.close();
            inputStream.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;
    }
}
