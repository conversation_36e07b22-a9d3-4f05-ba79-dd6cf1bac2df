package vn.onepay.portal.client;

import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.merchant_account.dto.MerchantAccountParam;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;

public class AdvanceServiceClient extends Db implements IConstants {
    private static final Logger LOGGER = Logger.getLogger(AdvanceServiceClient.class.getName());
    private static String ADVANCE_SERVICE_BASE_URL = Config.getString("advance-service.url", "http://localhost/advance/api/v1/");
    private static final Integer ADVANCE_SERVICE_TIMEOUT = Config.getInteger("advance-service.timeout", 60000);
    private static final String ADVANCE_SERVICE_SECURE = Config.getString("advance-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";

    public static Map<String, Object> request(String method, String url, Map<String, String> headers, byte[] content,
                                              int connectTimeout, int readTimeout) {
        Map<String, Object> result = null;
        try {

            LOGGER.info("call to advance_payment_service url: " + url + ", method: " + method + ", headers: " + headers + ", content: "
                    + (content != null ? new String(content, UTF_8) : "null"));
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod(method);
            if (headers != null)
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    conn.setRequestProperty(header.getKey(), header.getValue());
                }
            if (method.matches("^(POST|PUT|DELETE)$")) {
                conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length : 0));
                if (content != null && content.length > 0)
                    conn.setDoOutput(true);
            }
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setInstanceFollowRedirects(false);
            conn.connect();
            // Send request content
            if (method.matches("^(POST|PUT|DELETE)$") && content != null && content.length > 0) {
                DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
                wr.write(content);
                wr.flush();
                wr.close();
            }
            int statusCode = conn.getResponseCode();
            result = new LinkedHashMap<>();
            result.put(HTTP_STATUS_CODE, statusCode);
            result.put(HTTP_STATUS_MESS, conn.getResponseMessage());
            // Get response header
            Map<String, List<String>> resHeaders = conn.getHeaderFields();
            result.put(HTTP_HEADERS, resHeaders);

            // Get response content
            InputStream is = statusCode < 400 ? conn.getInputStream() : conn.getErrorStream();
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1)
                bout.write(buf, 0, length);
            is.close();
            bout.flush();
            byte[] resContent = bout.toByteArray();
            result.put(HTTP_CONTENT, resContent);
            bout.close();
            String logContent = new String(resContent, UTF_8);
            if (logContent.length() > 100)
                logContent = logContent.substring(0, 100) + "...";
            LOGGER.info("status_code: " + statusCode + ", headers: " + resHeaders + ", content: " + logContent);
            // Keepalive
            // conn.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return result;
    }

    public static JsonObject recalculateWhenApprove(String merchantId, String service)throws Exception{
        JsonObject result =  new JsonObject();
        String url = "/prefix_advance/recalculate_fee";
        try {
            Map<String, Object> params = new HashMap<>();
            // String strFrom = "";
            // String strTo = "";
            // Calendar cal = Calendar.getInstance();
            // strTo = yyyyMMddTHHmmssZ.format(cal.getTime());
            // cal.add(Calendar.DATE, -30);
            // strFrom = yyyyMMddTHHmmssZ.format(cal.getTime());
            params.put("merchant_id",merchantId);
            params.put("service", service);
            Gson gson = new Gson();
            String bodyJson = gson.toJson(params);
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("POST", ADVANCE_SERVICE_BASE_URL + url, signedHeaders,
                    bodyJson.getBytes(), 60000, 60000);
            result.put("status", Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE))));
            result.put("message",new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "recaculateWhenApprove: ", e);
            throw e;
        }
        return result;
    }

    public static JsonObject updateExchangeRate()throws Exception{
        JsonObject result =  new JsonObject();
        String url = "/prefix_advance/update_exchange_rate";
        try {
            Map params = new HashMap<>();
            Gson gson = new Gson();
            String bodyJson = gson.toJson(params);
            Map<String, String> signedHeaders = new HashMap();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("POST", ADVANCE_SERVICE_BASE_URL + url, signedHeaders,
                    bodyJson.getBytes(), 60000, 60000);
            result.put("status", Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE))));
            result.put("message",new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "updateExchangeRate: ", e);
            throw e;
        }
        return result;
    }
}
