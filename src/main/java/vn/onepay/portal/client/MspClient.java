package vn.onepay.portal.client;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import com.onepay.commons.util.Convert;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.utils.OneCreditUtil;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import static vn.onepay.portal.Util.gson;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

public class MspClient implements IConstants{
    
    private static final Logger LOGGER = Logger.getLogger(MspClient.class.getName());
    private static String ONEPAY_MSP_SERVICE_NAME = Config.getString("msp-service.service-name", "");
    private static String ONEPAY_MSP_SERVICE_REGION = Config.getString("msp-service.region", "");
    private static String ONEPAY_MSP_SERVICE_BASE_URL = Config.getString("msp-service.url", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_ID = Config.getString("msp-service.client-id", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_ID_2 = Config.getString("msp-service.client-id-extend", "");
    //private static String ONEPAY_MSP_SERVICE_AUTH_TYPE = "ows1_request";
    //private static String ONEPAY_MSP_SERVICE_AUTH_ALGORITHM = "OWS1-HMAC-SHA256";
    private static String ONEPAY_MSP_SERVICE_CLIENT_KEY = Config.getString("msp-service.client-key", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_KEY_2 = Config.getString("msp-service.client-key-extend", "");
    private static String REFUND_DISPUTE_URL = Config.getString("refund-dispute.url", "");
    private static String REFUND_DISPUTE_BIG_MERCHANT_URL = Config.getString("refund-dispute.big-merchant-url", "");
    private static String REFUND_DISPUTE_CAPTURE_URL = Config.getString("refund-dispute.capture-url", "");
    private static String REFUND_DISPUTE_CAPTURE_BIG_MERCHANT_URL = Config.getString("refund-dispute.big-merchant-capture-url", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    private static final String ONEPAY_ONECREDIT_SERVICE_SECURE = Config.getString("refund.one_credit-secure_code", "");
    private static String ONEPAY_MSP_SERVICE_EXTEND_PREFIX = Config.getString("msp-service.extend-prefix", "/msp-extend/api/v1");
    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject approveRefundRequest(String refundId){
        return refund(refundId, "approved");
    }
    public static JsonObject rejectRefundRequest(String refundId){
        return refund(refundId, "rejected");
    }


    private static JsonObject refund(String refundId, String state) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/refunds/"+refundId;

        String requestMethod = "PATCH";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();
//        queryParamMap.put("onecomm_txn_id", transId);

        JsonObject jOrder = new JsonObject();
        jOrder.put("state", state);


        try {

            LOGGER.log(Level.INFO, "PATCH TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + jOrder.encode());
            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI, queryParamMap, new LinkedHashMap<>(),
                    jOrder.encode().getBytes(UTF_8), requestDate, requestTimeOut);

//            String queryString = "?";

//            for (Map.Entry<String, String> queryParam : queryParamMap.entrySet()) {
//                queryString += queryParam.getKey() + "=" + URLEncoder.encode(queryParam.getValue(), "UTF-8") + "&";
//            }

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jOrder.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jOrder.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);

                LOGGER.log(Level.SEVERE, "MSP_CLIENT REFUND RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject refundPurchaseBNPL(String merchantId, String purchaseRef, String refundRef, Double amount, String accessCode, String hashCode, String userId, String note, String cancleBy) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        Map<String, String> data = null;
        requestURI = new StringBuilder(ONEPAY_MSP_SERVICE_EXTEND_PREFIX).append("/vpc/refunds");
            data = Util.refundMspData("refundPurchase", merchantId,
                    refundRef, purchaseRef, amount, userId, hashCode, "2", note, cancleBy);
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "POST";

        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info("url refundQT: " + requestURI.toString());
            LOGGER.info(" info refundQT: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "QT responseCode: " + responseCode);
            LOGGER.info(() -> "QT responseMsg: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
            }
            rd.close();
            is.close();
            jsonReturn = Util.decodeVpcResponse(response.toString());
            LOGGER.severe("MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[QT REFUND ERROR]", e);
        }
        return jsonReturn;
    }


    public static String queryQtRefundTxn(Map<String, Object> map, Map<String, String> input, String merchantId, String txnRef) throws Exception {
        LOGGER.info("Client request map : " + map);
        byte[] params = gson.toJson(map).getBytes("UTF-8");
        
        String requestURI = "/msp/api/v1/merchants/"+merchantId+"/refunds/"+txnRef + "?" +paramGet(input);
        String urlApi = ONEPAY_MSP_SERVICE_BASE_URL+requestURI;
        HttpURLConnection conn = (HttpURLConnection) (new URL(urlApi)).openConnection();
        conn.setConnectTimeout(5000);

        conn.setRequestMethod("GET");
        conn.setRequestProperty("User-Agent", "MSP HTTP Client");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Content-Length", params != null ? String.valueOf(params.length) : "0");
        conn.setRequestProperty(X_SECURE_HASH, genHMACSHA256(params, ONEPAY_ONECREDIT_SERVICE_SECURE));

        conn.setUseCaches(false);
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setInstanceFollowRedirects(false);

        // Send request params
        // DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
        // wr.write(params);
        // wr.flush();
        // wr.close();

        int responseCode = conn.getResponseCode();
        LOGGER.fine("response_code=" + responseCode);
        if (responseCode != 200) {
            LOGGER.log(Level.SEVERE, "Request fail: http responseCode=" + responseCode);
            return null;
        }
        //Get response header
        Map<String, List<String>> headers = conn.getHeaderFields();
        String localHash = headers.get(X_SECURE_HASH) != null ? headers.get(X_SECURE_HASH).get(0) : "";

        //Get response content
        InputStream is = null;
        if (responseCode == 200) {
            is = conn.getInputStream();
        } else {
            is = conn.getErrorStream();
        }
        byte[] content = null;
        if (is != null) {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1) {
                bout.write(buf, 0, length);
            }
            bout.flush();
            content = bout.toByteArray();
            bout.close();
        }
        conn.disconnect();
        //logger.fine("content=" + (new String(content, "UTF-8")));
        if (localHash.equalsIgnoreCase(genHMACSHA256(content, ""))) {
            if (content != null && content.length > 0 && responseCode == 200) {
                String stringRes = new String(content, "UTF-8");
                LOGGER.info("Client response map : " + stringRes);
                return stringRes;
            } else {
                return null;
            }
        } else {
            LOGGER.log(Level.SEVERE, "Invalid Hash");
            return null;
        }
    }

    private static String genHMACSHA256(byte[] data, String secretKey) throws Exception {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(Convert.decodeHexa(secretKey.getBytes()), "HMACSHA256");
            Mac mac = Mac.getInstance("HMACSHA256");
            mac.init(signingKey);
            return Convert.toHexString(mac.doFinal(data));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            return "";
        }
    }

    private static String paramGet(Map<String, String> map) throws Exception {
        StringBuilder buf = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (buf.length() > 0)
                buf.append("&");
            buf.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), "UTF-8"));
        }
        return buf.toString();
    }

    public static JsonObject refundDispute(Map<String, String> mIn) throws Exception {
        String command = mIn.get("vpc_Command");

        if ("refundCapture".equals(command)) {
            return refundDisputeCapture(mIn);
        } else {
            return refundDisputePurchase(mIn);
        }
    }

    private static JsonObject refundDisputeCapture(Map<String, String> mIn) throws Exception {
        JsonObject jsonReturn = new JsonObject();
        
        // Request uri base
        String requestURICapture;

        if (Util.isBigMerchant(mIn.get("vpc_Merchant"))) {
            LOGGER.log(Level.INFO, "Call refund api for BIGMERCHANT.");
            requestURICapture = REFUND_DISPUTE_CAPTURE_BIG_MERCHANT_URL;
        } else {
            requestURICapture = REFUND_DISPUTE_CAPTURE_URL;
        }

        String requestMethod = "PUT";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        String body = "";
        for (Map.Entry entry : mIn.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);

        try {
            requestURICapture = requestURICapture.replace(":merchant_id", mIn.get("vpc_Merchant"));
            requestURICapture = requestURICapture.replace(":capture_refund_ref", mIn.get("vpc_MerchTxnRef"));
            requestURICapture = requestURICapture.replace(":capture_ref", mIn.get("vpc_OrgMerchTxnRef"));

            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURICapture, queryParamMap, new LinkedHashMap<>(),
                    body.toString().getBytes(UTF_8), requestDate, requestTimeOut);

            // Request URL with query parameters
            String mspPutRefundDisputeURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURICapture;

            URL url = new URL(mspPutRefundDisputeURL);

            LOGGER.log(Level.INFO, requestMethod + " to url: " + Util.mask(mspPutRefundDisputeURL));
            LOGGER.log(Level.INFO, " info: " + Util.mask(body));

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "POST");
            connection.setRequestProperty("Accept", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = Util.decodeVpcResponse(response.toString());

                LOGGER.log(Level.SEVERE, "MSP_CLIENT DISPUTE REFUND RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT DISPUTE REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                jsonReturn.put("vpc_TxnResponseCode", Integer.toString(responseCode));
                jsonReturn.put("vpc_Message", responseMsg);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    private static JsonObject refundDisputePurchase(Map<String, String> mIn) throws Exception {
        JsonObject jsonReturn = new JsonObject();
        
        // Request uri base
        String requestURIPurchase;

        if (Util.isBigMerchant(mIn.get("vpc_Merchant"))) {
            LOGGER.log(Level.INFO, "Call refund api for BIGMERCHANT.");
            requestURIPurchase = REFUND_DISPUTE_BIG_MERCHANT_URL;
        } else {
            requestURIPurchase = REFUND_DISPUTE_URL;
        }

        String requestMethod = "POST";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        String body = "";
        for (Map.Entry entry : mIn.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);

        try {

            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURIPurchase, queryParamMap, new LinkedHashMap<>(),
                    body.toString().getBytes(UTF_8), requestDate, requestTimeOut);

            // Request URL with query parameters
            String mspPutRefundDisputeURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURIPurchase;

            URL url = new URL(mspPutRefundDisputeURL);

            LOGGER.log(Level.INFO, requestMethod + " to url: " + Util.mask(mspPutRefundDisputeURL));
            LOGGER.log(Level.INFO, " info: " + Util.mask(body));

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "POST");
            connection.setRequestProperty("Accept", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = Util.decodeVpcResponse(response.toString());

                LOGGER.log(Level.SEVERE, "MSP_CLIENT SS DISPUTE REFUND MANUAL RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT SS DISPUTE REFUND MANUAL [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                jsonReturn.put("vpc_TxnResponseCode", Integer.toString(responseCode));
                jsonReturn.put("vpc_Message", responseMsg);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject refundManualQtPurchaseSs(Map<String, String> mIn) throws Exception {
        JsonObject jsonReturn = new JsonObject();

        // Request uri base
        String requestURIPurchase;

        // if (Util.isBigMerchant(mIn.get("vpc_Merchant"))) {
        //     LOGGER.log(Level.INFO, "Call refund api for BIGMERCHANT.");
        //     requestURIPurchase = REFUND_DISPUTE_BIG_MERCHANT_URL;
        // } else {
            requestURIPurchase = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/"+ mIn.get("vpc_Merchant")+"/purchases/" + mIn.get("vpc_OrgMerchTxnRef") 
                +"/refunds_international/" + mIn.get("vpc_MerchTxnRef") + "/manual";
        // }

        String requestMethod = "POST";

        Date requestDate = new Date();

        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        JsonObject body = new JsonObject();
        body.put("merchant", mIn.get("vpc_Merchant"));
        body.put("merch_txn_ref", mIn.get("vpc_MerchTxnRef"));
        body.put("org_merch_txn_ref", mIn.get("vpc_OrgMerchTxnRef"));
        body.put("amount", mIn.get("vpc_Amount"));
        body.put("operator", mIn.get("vpc_Operator"));
        body.put("command", mIn.get("vpc_Command"));
        body.put("version", mIn.get("vpc_Version"));
        body.put("note", mIn.getOrDefault("vpc_Note", ""));

        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
            signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));

            // Create service signature with OnePAY Web Service Signature algorithm
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURIPurchase,
                    queryParamMap, new LinkedHashMap<>(),
                    body.encode().getBytes(UTF_8), requestDate, requestTimeOut);
            // Request URL with query parameters
            String mspManualRefundInternationalURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURIPurchase;

            URL url = new URL(mspManualRefundInternationalURL);

            LOGGER.log(Level.INFO, requestMethod + " to url: " + Util.mask(mspManualRefundInternationalURL));
            LOGGER.log(Level.INFO, " info: " + Util.mask(body));

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());

                LOGGER.log(Level.SEVERE, "MSP_CLIENT QT MANUAL REFUND RESPONSE: " + Util.mask(jsonReturn.toString()));
                
            } else {
                LOGGER.log(Level.SEVERE,
                        "MSP_CLIENT QT MANUAL REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                jsonReturn.put("status_code", Integer.toString(responseCode));
                jsonReturn.put("message", responseMsg);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject refundPurchaseQT(Map<String, String> mIn) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "/msp/api/v1/vpc/refunds2";

        String requestMethod = "POST";
        String body = "";
        for (Map.Entry<String, String> entry : mIn.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.log(Level.INFO, "POST TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + Util.mask(body));

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            jsonReturn = Util.decodeVpcResponse(response.toString());
            LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + Util.mask(jsonReturn.encodePrettily()));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    // private static JsonObject refundDisputeCapture(Map<String, String> mIn) throws Exception {
    //     JsonObject jsonReturn = new JsonObject();
    //     String merchantId = mIn.get("vpc_Merchant");
    //     String orgMerchTxnRef = mIn.get("vpc_OrgMerchTxnRef");
    //     String merchTxnRef = mIn.get("vpc_MerchTxnRef");

    //     // Request uri base
    //     //     /msp/api/v1/vpc/merchants/:merchant_id/captures/:capture_ref/capture_refunds/:capture_refund_ref
    //     String requestURICapture = "/msp/api/v1/vpc/merchants/" + merchantId + "/captures/" + orgMerchTxnRef + "/capture_refunds/" + merchTxnRef;

    //     String requestMethod = "PUT";

    //     Date requestDate = new Date();

    //     // Milliseconds
    //     int requestTimeOut = 60000;

    //     Map<String, String> queryParamMap = new LinkedHashMap<>();

    //     String body = "";
    //     for (Map.Entry entry : mIn.entrySet()) {
    //         body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
    //     }
    //     body = body.substring(0, body.length() - 1);

    //     try {

    //         LOGGER.log(Level.INFO, requestMethod + " TO MSP-SERVICE");
    //         LOGGER.log(Level.INFO, "url: " + requestURICapture);
    //         LOGGER.log(Level.INFO, " info: " + body);
    //         // Create service signature with OnePAY Web Service Signature algorithm
    //         Authorization onePAYServiceAuthorization = new Authorization(
    //                 ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
    //                 ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURICapture, queryParamMap, new LinkedHashMap<>(),
    //                 body.toString().getBytes(UTF_8), requestDate, requestTimeOut);

    //         // Request URL with query parameters
    //         String mspPutRefundDisputeURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURICapture;
    //         LOGGER.log(Level.INFO, " mspPutRefundDisputeURL: " + mspPutRefundDisputeURL);

    //         URL url = new URL(mspPutRefundDisputeURL);

    //         HttpURLConnection connection = (HttpURLConnection) url.openConnection();

    //         connection.setRequestMethod(requestMethod);
    //         connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
    //         connection.setRequestProperty("Accept", "application/x-www-form-urlencoded");
    //         connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
    //         connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
    //         connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
    //         connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
    //         connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));

    //         connection.setUseCaches(false);
    //         connection.setDoInput(true);
    //         connection.setDoOutput(true);

    //         DataOutputStream wr = new DataOutputStream(
    //                 connection.getOutputStream());
    //         wr.write(body.getBytes(UTF_8));
    //         wr.flush();
    //         wr.close();
    //         int responseCode = connection.getResponseCode();
    //         String responseMsg = connection.getResponseMessage();
    //         LOGGER.info("responseCode:" + responseCode);
    //         LOGGER.info("responseMsg:" + responseMsg);
    //         if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
    //             //Get Response
    //             InputStream is = connection.getInputStream();
    //             BufferedReader rd = new BufferedReader(new InputStreamReader(is));
    //             String line;
    //             StringBuilder response = new StringBuilder();
    //             while ((line = rd.readLine()) != null) {
    //                 response.append(line);
    //                 response.append('\r');
    //             }
    //             rd.close();
    //             is.close();
    //             jsonReturn = Util.decodeVpcResponse(response.toString());

    //             LOGGER.log(Level.SEVERE, "MSP_CLIENT DISPUTE REFUND RESPONSE: " + jsonReturn.toString());
    //         } else {
    //             LOGGER.log(Level.SEVERE, "MSP_CLIENT DISPUTE REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
    //             jsonReturn.put("vpc_TxnResponseCode", Integer.toString(responseCode));
    //             jsonReturn.put("vpc_Message", responseMsg);
    //         }
    //     } catch (Exception e) {
    //         LOGGER.log(Level.SEVERE, "[ERROR]", e);
    //     }
    //     return jsonReturn;
    // }

    public static JsonObject reverseDue(String merchantId, String transactionId, String tranRef, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId + "/approve/" + tranRef;

        String requestMethod = "PUT";

        JsonObject body = new JsonObject();
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        body.put("dispute", "y");
        body.put("dispute_reason", "DPR-00");

        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE reverseDue");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND reverseDue [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject approveRefund(String merchantId, String transactionId, String tranRef, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId + "/approve/" + tranRef;

        String requestMethod = "PUT";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

  

    public static JsonObject refundCDR(String merchantId, String transactionId, Long amount, String operator, String currency) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI =ONEPAY_MSP_SERVICE_EXTEND_PREFIX+ "/merchant/" + merchantId + "/refunds-cdr/" + transactionId;

        String requestMethod = "POST";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("amount", amount);
        body.put("currency", currency);


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }


    public static JsonObject UpdateCDRPendingRefState(String merchantId, String transactionId, String state, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI =ONEPAY_MSP_SERVICE_EXTEND_PREFIX+ "/merchant/" + merchantId + "/refunds-cdr/" + transactionId;

        String requestMethod = "PUT";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        body.put("state", state);


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "PUT TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject rejectRefund(String merchantId, String transactionId, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId + "/reject";

        String requestMethod = "PATCH";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject updateStateToApproved(String merchantId, String transactionId, String note, String operator) {
        return updateState(merchantId, transactionId, "approved", note, operator);
    }

    public static JsonObject updateStateToFailed(String merchantId, String transactionId, String note, String operator) {
        return updateState(merchantId, transactionId, "failed", note, operator);
    }

    public static JsonObject updateState(String merchantId, String transactionId, String state, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId;

        String requestMethod = "PATCH";
        JsonObject body = new JsonObject();
        body.put("state", state);
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.log(Level.INFO, "POST TO MSP-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "PATCH");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    // response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject manual(String merchantId, String transactionId, String tranRef, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId + "/manual/" + tranRef;

        String requestMethod = "PUT";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.severe(() -> "[ERROR]" + e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject auto(String merchantId, String transactionId, String tranRef, String note, String operator) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId + "/auto/" + tranRef;

        String requestMethod = "PUT";
        JsonObject body = new JsonObject();
        body.put("operator", operator);
        if (note != null) {
            body.put("note", note);
        }
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.severe(() -> "[ERROR]" + e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject checkAutoManual(String merchantId, String transactionId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/merchant/" + merchantId + "/refunds/" + transactionId;
        String requestMethod = "GET";
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                new byte[0], requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "GET FROM MSP-SERVICE");
            LOGGER.info(() -> "URL: " + requestURI);

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject refundPurchaseQT(String merchantId, String purchaseRef, String refundRef, Double amount,
            String accessCode, String hashCode, String userId, String note) throws Exception {
        return refundPurchaseQT(merchantId, purchaseRef, refundRef, amount, accessCode, hashCode, userId, note, null);
    }

    public static JsonObject refundPurchaseQT(String merchantId, String purchaseRef, String refundRef, Double amount,
            String accessCode, String hashCode, String userId, String note, String hisHash) throws Exception {
        JsonObject jsonReturn = null;
        // Request uri base
        StringBuilder requestURI = null;
        StringBuilder mspOrderStateQueryURL = null;
        Map<String, String> data = null;
        // if (Util.isBigMerchant(merchantId)) {
            // requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/refunds2");
            // data = OneCreditUtil.mspData("refundPurchase", merchantId,
            //         accessCode, refundRef, purchaseRef,
            //         amount, userId, hashCode, "2", note);
        // } else {
            requestURI = new StringBuilder(ONEPAY_MSP_SERVICE_EXTEND_PREFIX).append("/merchant/").append(merchantId).append("/refunds/").append(purchaseRef);
            data = OneCreditUtil.refundMspData("refundPurchase", merchantId,
                    refundRef, purchaseRef, amount, userId, hashCode, "2", note, hisHash);
        // }
        mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
        String requestMethod = "POST";

        String body = "";
        for (Map.Entry<String, String> entry : data.entrySet()) {
            body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
        }
        body = body.substring(0, body.length() - 1);
        // Milliseconds
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "text/html");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
                queryParamMap, signedHeaders,
                body.getBytes(UTF_8), requestDate, requestTimeOut);
        LOGGER.info("onePAYServiceAuthorization: " + onePAYServiceAuthorization.toString());
        LOGGER.info("ONEPAY_MSP_SERVICE_CLIENT_ID_2: " + ONEPAY_MSP_SERVICE_CLIENT_ID_2);
        LOGGER.info("ONEPAY_MSP_SERVICE_CLIENT_KEY_2: " + ONEPAY_MSP_SERVICE_CLIENT_KEY_2);
        LOGGER.info("ONEPAY_MSP_SERVICE_REGION: " + ONEPAY_MSP_SERVICE_REGION);
        LOGGER.info("ONEPAY_MSP_SERVICE_NAME: " + ONEPAY_MSP_SERVICE_NAME);
        LOGGER.info("requestMethod: " + requestMethod);
        LOGGER.info("requestURI.toString(): " + requestURI.toString());
        LOGGER.info("queryParamMap: " + queryParamMap);
        LOGGER.info("signedHeaders: " + signedHeaders);
        LOGGER.info("body.getBytes(UTF_8): " + body.getBytes(UTF_8));
        LOGGER.info("requestDate: " + requestDate);
        LOGGER.info("requestTimeOut: " + requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info("url refundQT: " + requestURI.toString());
            LOGGER.info(" info refundQT: " + body);

            URL url = new URL(mspOrderStateQueryURL.toString());

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER,
                    signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER,
                    signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER,
                    onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "QT responseCode: " + responseCode);
            LOGGER.info(() -> "QT responseMsg: " + responseMsg);

            InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
            // Get Response
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
            }
            rd.close();
            is.close();
            jsonReturn = Util.decodeVpcResponse(response.toString());
            LOGGER.severe("MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[QT REFUND ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject createBankMerchantSecretKey(JsonObject body) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/";

        String requestMethod = "POST";


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "POST TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT REFUND [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.REFUND_FAILED;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;  
    }

    public static JsonObject enableBankMerchantSecretKey(String acquirerBankId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/" + acquirerBankId + "/enable";
        
        String requestMethod = "PATCH";


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                new byte[0], requestDate, requestTimeOut);

        try {
            LOGGER.info(() -> "PATCH TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: ");

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            // Sử dụng Apache HttpClient thay vì HttpURLConnection
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPatch httpPatch = new HttpPatch(mspOrderStateQueryURL);
                
                // Set headers
                httpPatch.setHeader("Content-Type", "application/json");
                httpPatch.setHeader("Accept", signedHeaders.get("Accept"));
                httpPatch.setHeader(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
                httpPatch.setHeader(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
                httpPatch.setHeader(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());
                
                // Set request body
                StringEntity entity = new StringEntity("", "UTF-8");
                httpPatch.setEntity(entity);
                
                // Execute request
                try (CloseableHttpResponse response = httpClient.execute(httpPatch)) {
                    int responseCode = response.getStatusLine().getStatusCode();
                    String responseMsg = response.getStatusLine().getReasonPhrase();
                    
                    LOGGER.info(() -> "responseCode:" + responseCode);
                    LOGGER.info(() -> "responseMsg:" + responseMsg);
                    
                    if (responseCode == 200 || responseCode == 201) {
                        // Get response body
                        String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                        jsonReturn = new JsonObject(responseBody);
                        LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
                    } else {
                        LOGGER.severe(() -> "MSP_CLIENT ENABLE BANK MERCHANT SECRET KEY [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }
    
    /**
     * Gọi API gán merchant vào acquirer bank của msp-extend service
     * @param body
     * @param acquirerBankId
     * @return JsonArray response từ msp-extend service
     */
    public static JsonArray assignMerchantToAcquirerBank(JsonObject body, String acquirerBankId) {
        JsonArray jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/" + acquirerBankId+ "/merchants";
        
        String requestMethod = "POST";


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "PATCH TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Content-Length", "" + Integer.toString(body.toString().getBytes().length));
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(body.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                }
                rd.close();
                is.close();
                jsonReturn = new JsonArray(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT ASSIGN MERCHANT TO ACQUIRER BANK [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    /**
     * Gọi API hủy gán merchant vào acquirer bank của msp-extend service
     * @param acquirerBankId
     * @param merchantId
     * @return JsonObject response từ msp-extend service
     */
    public static JsonObject unassignMerchantToAcquirerBank(String acquirerBankId, String merchantId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/" + acquirerBankId + "/merchants/" + merchantId;
        
        String requestMethod = "DELETE";


        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                new byte[0], requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "DELETE TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: ");

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

              
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT UNASSIGN MERCHANT TO ACQUIRER BANK [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject updateStateMerchantToAcquirerBank(JsonObject body, String acquirerBankId, String merchantId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/" + acquirerBankId + "/merchants/" + merchantId + "/state";
        
        String requestMethod = "PATCH";

        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // signedHeaders.put(X_REQUEST_ID ,  "REFCDR-" + UUID.randomUUID().toString());

        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
            ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                body.encode().getBytes(UTF_8), requestDate, requestTimeOut);

        try {
            LOGGER.info(() -> "PATCH TO MSP-SERVICE");
            LOGGER.info(() -> "url: " + requestURI);
            LOGGER.info(() -> " info: " + body.encode());

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            // Sử dụng Apache HttpClient thay vì HttpURLConnection
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpPatch httpPatch = new HttpPatch(mspOrderStateQueryURL);
                
                // Set headers
                httpPatch.setHeader("Content-Type", "application/json");
                httpPatch.setHeader("Accept", signedHeaders.get("Accept"));
                httpPatch.setHeader(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
                httpPatch.setHeader(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
                httpPatch.setHeader(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());
                
                // Set request body
                StringEntity entity = new StringEntity(body.encode(), "UTF-8");
                httpPatch.setEntity(entity);
                
                // Execute request
                try (CloseableHttpResponse response = httpClient.execute(httpPatch)) {
                    int responseCode = response.getStatusLine().getStatusCode();
                    String responseMsg = response.getStatusLine().getReasonPhrase();
                    
                    LOGGER.info(() -> "responseCode:" + responseCode);
                    LOGGER.info(() -> "responseMsg:" + responseMsg);
                    
                    if (responseCode == 200 || responseCode == 201) {
                        // Get response body
                        String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                        jsonReturn = new JsonObject(responseBody);
                        LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
                    } else {
                        LOGGER.severe(() -> "MSP_CLIENT UPDATE STATE ACQUIRER BANK KEY TO MERCHANT [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                        throw IErrors.INTERNAL_SERVER_ERROR;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    public static JsonObject getAcquirerBankMerchantKeyById(String acquirerBankId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = ONEPAY_MSP_SERVICE_EXTEND_PREFIX + "/acquirer-bank-key/" + acquirerBankId;
        String requestMethod = "GET";
        int requestTimeOut = 60000;

        Map<String, String> queryParamMap = new LinkedHashMap<>();

        Date requestDate = new Date();
        Map<String, String> signedHeaders = new HashMap<>();
        signedHeaders.put("Accept", "application/json");
        signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
        signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
        // Create service signature with OnePAY Web Service Signature algorithm
        Authorization onePAYServiceAuthorization = new Authorization(
                ONEPAY_MSP_SERVICE_CLIENT_ID_2, ONEPAY_MSP_SERVICE_CLIENT_KEY_2,
                ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                queryParamMap, signedHeaders,
                new byte[0], requestDate, requestTimeOut);

        try {

            LOGGER.info(() -> "GET FROM MSP-SERVICE");
            LOGGER.info(() -> "URL: " + requestURI);

            // Request URL with query parameters
            String mspOrderStateQueryURL = ONEPAY_MSP_SERVICE_BASE_URL + requestURI;

            URL url = new URL(mspOrderStateQueryURL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
            connection.setRequestProperty(Authorization.X_OP_DATE_HEADER, signedHeaders.get(Authorization.X_OP_DATE_HEADER));
            connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER, signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
            connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER, onePAYServiceAuthorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info(() -> "responseCode:" + responseCode);
            LOGGER.info(() -> "responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
                // Get Response
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
            } else {
                LOGGER.severe(() -> "MSP_CLIENT GET ACQUIRER BANK MERCHANT KEY BY ID [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw IErrors.INTERNAL_SERVER_ERROR;
        }
        return jsonReturn;
    }

    // public static JsonObject refundCapture(String merchantId, String captureRef, String refundRef, Double amount,
    //         String accessCode, String hashCode, String userId, String note, String hisHash) throws Exception {
    //     JsonObject jsonReturn = null;
    //     StringBuilder requestURI = new StringBuilder(ONEPAY_MSP_SERVICE_EXTEND_PREFIX)
    //             .append("/vpc/merchant/")
    //             .append(merchantId)
    //             .append("/captures/")
    //             .append(captureRef)
    //             .append("/capture_refunds/")
    //             .append(refundRef);
    //     StringBuilder mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
    //     String requestMethod = "PUT";

    //     Map<String, String> data = OneCreditUtil.refundMspData("refundCapture", merchantId,
    //             refundRef, captureRef, amount, userId, hashCode, "2", note, hisHash);

    //     String body = "";
    //     for (Map.Entry<String, String> entry : data.entrySet()) {
    //         body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
    //     }
    //     body = body.substring(0, body.length() - 1);
    //     // Milliseconds
    //     int requestTimeOut = 60000;

    //     Map<String, String> queryParamMap = new LinkedHashMap<>();

    //     Date requestDate = new Date();
    //     Map<String, String> signedHeaders = new HashMap<>();
    //     signedHeaders.put("Accept", "text/html");
    //     signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
    //     signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
    //     // Create service signature with OnePAY Web Service Signature algorithm
    //     Authorization onePAYServiceAuthorization = new Authorization(
    //             ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
    //             ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
    //             queryParamMap, signedHeaders,
    //             body.getBytes(UTF_8), requestDate, requestTimeOut);
    //     try {

    //         LOGGER.info(() -> "POST TO MSP-SERVICE");
    //         LOGGER.info("url refund capture: " + mspOrderStateQueryURL.toString());
    //         LOGGER.info("info refund capture: " + body);

    //         URL url = new URL(mspOrderStateQueryURL.toString());

    //         HttpURLConnection connection = (HttpURLConnection) url.openConnection();

    //         connection.setRequestMethod(requestMethod);
    //         connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
    //         connection.addRequestProperty("Content-Length", "" + Integer.toString(body.getBytes().length));
    //         connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
    //         connection.setRequestProperty(Authorization.X_OP_DATE_HEADER,
    //                 signedHeaders.get(Authorization.X_OP_DATE_HEADER));
    //         connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER,
    //                 signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
    //         connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER,
    //                 onePAYServiceAuthorization.toString());

    //         connection.setUseCaches(false);
    //         connection.setDoInput(true);
    //         connection.setDoOutput(true);

    //         DataOutputStream wr = new DataOutputStream(
    //                 connection.getOutputStream());
    //         wr.write(body.getBytes(UTF_8));
    //         wr.flush();
    //         wr.close();
    //         int responseCode = connection.getResponseCode();
    //         LOGGER.info(() -> "responseCode:" + responseCode);

    //         InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
    //         // Get Response
    //         BufferedReader rd = new BufferedReader(new InputStreamReader(is));
    //         String line;
    //         StringBuilder response = new StringBuilder();
    //         while ((line = rd.readLine()) != null) {
    //             response.append(line);
    //         }
    //         rd.close();
    //         is.close();
    //         jsonReturn = Util.decodeVpcResponse(response.toString());
    //         LOGGER.severe("MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
    //     } catch (Exception e) {
    //         LOGGER.log(Level.SEVERE, "[ERROR]", e);
    //     }
    //     return jsonReturn;
    // }

    // public static JsonObject refundCaptureBigMerchant(String merchantId, String captureRef, String refundRef,
    //         Double amount, String accessCode, String hashCode, String userId, String note) throws Exception {
    //     JsonObject jsonReturn = null;
    //     StringBuilder requestURI = new StringBuilder("/msp-pci-apple/api/v1/vpc/merchants/").append(merchantId)
    //             .append("/captures/").append(captureRef)
    //             .append("/capture_refunds/").append(refundRef);

    //     StringBuilder mspOrderStateQueryURL = new StringBuilder(ONEPAY_MSP_SERVICE_BASE_URL).append(requestURI);
    //     String requestMethod = "PUT";

    //     Map<String, String> data = OneCreditUtil.mspData("capture_refund", merchantId,
    //             accessCode, refundRef, captureRef, amount, userId, hashCode, "2", note);

    //     Date requestDate = new Date();
    //     String body = "";
    //     for (Map.Entry<String, String> entry : data.entrySet()) {
    //         body += entry.getKey() + "=" + URLEncoder.encode(entry.getValue() + "", "UTF-8") + "&";
    //     }
    //     body = body.substring(0, body.length() - 1);

    //     // Milliseconds
    //     int requestTimeOut = 60000;

    //     Map<String, String> queryParamMap = new LinkedHashMap<>();

    //     Map<String, String> signedHeaders = new HashMap<>();
    //     signedHeaders.put("Accept", "text/html");
    //     signedHeaders.put(Authorization.X_OP_DATE_HEADER, yyyyMMddTHHmmssZ.format(requestDate));
    //     signedHeaders.put(Authorization.X_OP_EXPIRES_HEADER, String.valueOf(requestTimeOut));
    //     // Create service signature with OnePAY Web Service Signature algorithm
    //     Authorization onePAYServiceAuthorization = new Authorization(
    //             ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
    //             ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI.toString(),
    //             queryParamMap, signedHeaders,
    //             body.getBytes(UTF_8), requestDate, requestTimeOut);
    //     try {

    //         LOGGER.log(Level.INFO, "PUT TO MSP-SERVICE");
    //         LOGGER.log(Level.INFO, "url refund capture: " + mspOrderStateQueryURL.toString());
    //         LOGGER.log(Level.INFO, " info refund capture: " + body);

    //         URL url = new URL(mspOrderStateQueryURL.toString());

    //         HttpURLConnection connection = (HttpURLConnection) url.openConnection();

    //         connection.setRequestMethod(requestMethod);
    //         connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
    //         connection.addRequestProperty("Content-Length", "" + Integer.toString(body.getBytes().length));
    //         connection.setRequestProperty("Accept", signedHeaders.get("Accept"));
    //         connection.setRequestProperty(Authorization.X_OP_DATE_HEADER,
    //                 signedHeaders.get(Authorization.X_OP_DATE_HEADER));
    //         connection.setRequestProperty(Authorization.X_OP_EXPIRES_HEADER,
    //                 signedHeaders.get(Authorization.X_OP_EXPIRES_HEADER));
    //         connection.setRequestProperty(Authorization.X_OP_AUTHORIZATION_HEADER,
    //                 onePAYServiceAuthorization.toString());

    //         connection.setUseCaches(false);
    //         connection.setDoInput(true);
    //         connection.setDoOutput(true);

    //         DataOutputStream wr = new DataOutputStream(
    //                 connection.getOutputStream());
    //         wr.write(body.getBytes(UTF_8));
    //         wr.flush();
    //         wr.close();
    //         int responseCode = connection.getResponseCode();
    //         String responseMsg = connection.getResponseMessage();
    //         LOGGER.info(() -> "responseCode:" + responseCode);
    //         LOGGER.info(() -> "responseMsg:" + responseMsg);

    //         InputStream is = responseCode < 400 ? connection.getInputStream() : connection.getErrorStream();
    //         // Get Response
    //         BufferedReader rd = new BufferedReader(new InputStreamReader(is));
    //         String line;
    //         StringBuilder response = new StringBuilder();
    //         while ((line = rd.readLine()) != null) {
    //             response.append(line);
    //         }
    //         rd.close();
    //         is.close();
    //         jsonReturn = Util.decodeVpcResponse(response.toString());
    //         LOGGER.log(Level.SEVERE, "MSP_CLIENT RESPONSE: " + jsonReturn.encodePrettily());
    //     } catch (Exception e) {
    //         LOGGER.log(Level.SEVERE, "[ERROR]", e);
    //     }
    //     return jsonReturn;
    // }
}
