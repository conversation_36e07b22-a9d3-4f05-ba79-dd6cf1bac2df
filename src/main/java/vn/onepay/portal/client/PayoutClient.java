package vn.onepay.portal.client;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Logger;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;
import vn.onepay.portal.Config;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;

public class PayoutClient extends Db {

    private static final Logger LOGGER = Logger.getLogger(PayoutClient.class.getName());
    // private static String ONEPAY_MA_PAYOUT_SERVICE_BASE_URL =
    // Config.getString("onepayout-service.url", "");
    private static final String ONEPAY_MA_PAYOUT_SERVICE_BASE_URL = "https://dev.onepay.vn";

    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static String ONEPAY_MSP_SERVICE_CLIENT_ID = Config.getString("onepayout-service.client_id", "");
    private static String ONEPAY_MSP_SERVICE_CLIENT_KEY = Config.getString("onepayout-service.client_key", "");
    private static String ONEPAY_MSP_SERVICE_REGION = Config.getString("onepayout-service.region", "");
    private static String ONEPAY_MSP_SERVICE_NAME = Config.getString("onepayout-service.name", "");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static void notiMerchantAndOp(JsonObject jbody) {
        Util.execBlocking(() -> {
            JsonObject jsonReturn = null;
            // Request uri base
            String requestURI = "/onepayout/api/v1/noti-approved-trans";
            String requestMethod = "POST";
            Date requestDate = new Date();
            // Milliseconds
            int requestTimeOut = 60000;
            Map<String, String> queryParamMap = new LinkedHashMap<>();

            Map<String, String> signedHeaders = new LinkedHashMap<>();
            Authorization onePAYServiceAuthorization = new Authorization(
                    ONEPAY_MSP_SERVICE_CLIENT_ID, ONEPAY_MSP_SERVICE_CLIENT_KEY,
                    ONEPAY_MSP_SERVICE_REGION, ONEPAY_MSP_SERVICE_NAME, requestMethod, requestURI,
                    queryParamMap, signedHeaders,
                    jbody.encode().getBytes(), requestDate, requestTimeOut);

            String payoutOrderStateQueryURL = ONEPAY_MA_PAYOUT_SERVICE_BASE_URL + requestURI;
            URL url = new URL(payoutOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(6000);
            connection.setReadTimeout(6000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", requestTimeOut + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("X-OP-Authorization", onePAYServiceAuthorization.toString());
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }

                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.info("notiMerchantAndOp success: " + jsonReturn.encode());
            } else {
                LOGGER.warning("notiMerchantAndOp failed: ");
            }
        });
    }
}
