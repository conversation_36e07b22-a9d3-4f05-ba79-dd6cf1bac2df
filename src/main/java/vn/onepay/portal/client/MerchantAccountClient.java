package vn.onepay.portal.client;

import static java.nio.charset.StandardCharsets.UTF_8;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.google.gson.Gson;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.BaseList;
import vn.onepay.portal.resources.merchant_account.dto.MerchantAccountParam;
import vn.onepay.portal.resources.merchant_account.dto.MerchantIdService;

public class MerchantAccountClient extends Db implements IConstants {
    private static final Logger LOGGER = Logger.getLogger(MerchantAccountClient.class.getName());
    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static String MERCHANT_ACCOUNT_SERVICE_BASE_URL = Config.getString("merchant-account-service.url", "");
    //=private static String MERCHANT_ACCOUNT_SERVICE_BASE_URL = "http://localhost:9055";
    private static final Integer MERCHANT_ACCOUNT_TIMEOUT = Config.getInteger("merchant-account-service.timeout",
            60000);
    private static final String MERCHANT_ACCOUNT_SECURE = Config.getString("merchant-account-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static final String ONEPAY_MERCHANT_ACCOUNT_SERVICE_BASE_URL = Config
            .getString("merchant-account-service.onepay_merchant_account_service_base_url", "");
    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }
    public static JsonObject getpartnerList() throws Exception{
        JsonObject jsonReturn = null;
        String requestUri = "/merchant-account-backend/merchant-account/get-partner/search-partner-name";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestUri, signedHeaders,
                    null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Partner Status Response:" + resStatus);
            LOGGER.info(() -> "Get Partner Link Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
            throw e;
        }
        return jsonReturn;
    }

    public static JsonObject getPartnerById(String idString) throws Exception{
        JsonObject merchantJsonObject = new JsonObject();
        String requestUri = "/merchant-account-backend/merchant-account/get-partner-id/" + idString;
        try {
            
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("GET", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestUri, signedHeaders,
                    null, 60000, 60000);
            int resStatus = Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE)));
            if (resStatus == 200) {
                String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
                merchantJsonObject = new JsonObject(resContent);
            }
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "merchantAccountGetById: ", e);
            throw e;
        }
        return merchantJsonObject;
    }

    public static JsonObject getMerchantByPartner(Map<String,Object> mIn) throws Exception{
        JsonObject merchantJsonObject = new JsonObject();
        try {
            String param =  Convert.mapToParamHttp(mIn);
            String requestURI = "/merchant-account-backend/merchant-account/get-merchant-by-partner?"+param;
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("GET", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestURI, signedHeaders,
                    null, 60000, 60000);
            int resStatus = Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE)));
            if (resStatus == 200) {
                String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
                merchantJsonObject = new JsonObject(resContent);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "merchantAccountGetById: ", e);
            throw e;
        }
        return merchantJsonObject;
    }

    public static JsonObject merchantAccountGetById(String idString) throws Exception{
        JsonObject merchantJsonObject = new JsonObject();
        String requestUri = "/merchant-account-backend/merchant-account/get-merchant-account-by-id/" + idString;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("GET", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestUri, signedHeaders,
                    null, 60000, 60000);
            int resStatus = Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE)));
            if (resStatus == 200) {
                String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
                merchantJsonObject = bind(new JsonObject(resContent));
            }
        
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "merchantAccountGetById: ", e);
            throw e;
        }
        return merchantJsonObject;
    }

    public static Integer deleteMerchantAccount(String idString) throws Exception{
        int result = 0;
        String requestUri = "/merchant-account-backend//merchant-account/delete-merchant-account/" + idString;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("DELETE", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestUri, signedHeaders,
                    null, 60000, 60000);
            int resStatus = Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE)));
            if (resStatus == 200) {
                result = 1;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "deleteMerchantAccount: ", e);
            throw e;
        }
        return result;
    }

    public static JsonObject createMerchantAccount(MerchantAccountParam merchantAccountParam)throws Exception{
        JsonObject result =  new JsonObject();
        try {
           
            String requestURI = "/merchant-account-backend/merchant-account/create";
            Gson gson = new Gson();
            String body = gson.toJson(merchantAccountParam);
            Map<String, String> signedHeaders = new HashMap();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("POST", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestURI, signedHeaders,
                    body.getBytes(), 60000, 60000);
            result.put("status", Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE))));
            result.put("message",new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "createMerchantAccount: ", e);
            throw e;
        }
        return result;
    }
    public static JsonObject updateMerchantAccount(MerchantAccountParam merchantAccountParam)throws Exception{
        JsonObject result =  new JsonObject();
        try {
            String requestURI = "/merchant-account-backend/merchant-account/update";
            Gson gson = new Gson();
            String body = gson.toJson(merchantAccountParam);
            Map<String, String> signedHeaders = new HashMap();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("PUT", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestURI, signedHeaders,
                    body.getBytes(), 60000, 60000);
            result.put("status", Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE))));
            result.put("message", new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "createMerchantAccount: ", e);
            throw e;
        }
        return result;
    }
    public static JsonObject searchMerchantAccount(Map<String,Object> mIn) throws Exception{
        JsonObject merchantJsonObject = new JsonObject();
        try {
            String param =  Convert.mapToParamHttp(mIn);
            String requestURI = "/merchant-account-backend/merchant-account/search?"+param;
            Map<String, String> signedHeaders = new HashMap();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");
            Map<String, Object> resMap = request("GET", MERCHANT_ACCOUNT_SERVICE_BASE_URL + requestURI, signedHeaders,
            null ,60000, 60000);
            int resStatus = Integer.parseInt(String.valueOf(resMap.get(HTTP_STATUS_CODE)));
            if (resStatus == 200) {
                BaseList  baseList = new BaseList<>();
                String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
                merchantJsonObject = new JsonObject(resContent);
                List<Map<String,Object>> listMerchMaps = (List<Map<String,Object>>) merchantJsonObject.getMap().get("list");
                List listMerchMapsConvert = new  ArrayList<>();
                for (Map<String,Object> map : listMerchMaps) {
                   listMerchMapsConvert.add(bind(new JsonObject(map)));
                }
                baseList.setList(listMerchMapsConvert);
                baseList.setTotalItems(merchantJsonObject.getInteger("totalItems"));
                merchantJsonObject = new JsonObject().mapFrom(baseList);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "createMerchantAccount: ", e);
            throw e;
        }
        return merchantJsonObject;
    }
    public static JsonObject bind(JsonObject ojb){
        Gson gson = new Gson();
        MerchantIdService[] merchantDatas = gson.fromJson(ojb.getString("data").trim(), MerchantIdService[].class);
        MerchantAccountParam merchantAccountParam = new MerchantAccountParam();
        merchantAccountParam.setId(ojb.getInteger("id"));
        merchantAccountParam.setPartnerId(ojb.getInteger("partnerId"));
        merchantAccountParam.setPartnerName(ojb.getString("partnerName"));
        merchantAccountParam.setBusinessName(ojb.getString("businessName"));
        merchantAccountParam.setCreateDate(ojb.getString("createDate"));
        merchantAccountParam.setMerchantAccountName(ojb.getString("merchantAccountName"));
        merchantAccountParam.setContractCode(ojb.getString("contractCode"));
        merchantAccountParam.setSubcontractCode(ojb.getString("subcontractCode"));
        merchantAccountParam.setTaxCode(ojb.getString("taxCode"));
        merchantAccountParam.setActivatedDate(ojb.getString("createDate"));
        merchantAccountParam.setMerchantIdService(new ArrayList<>(Arrays.asList(merchantDatas)));
        merchantAccountParam.setCategory(ojb.getString("category"));
        merchantAccountParam.setDescribeCategory(ojb.getString("describeCategory"));
        merchantAccountParam.setProvince(ojb.getString("province"));
        return new JsonObject(gson.toJson(merchantAccountParam));
    }
    public static Map<String, Object> request(String method, String url, Map<String, String> headers, byte[] content,
            int connectTimeout, int readTimeout) {
        Map<String, Object> result = null;
        try {

            LOGGER.info("call to quiclink url: " + url + ", method: " + method + ", headers: " + headers + ", content: "
                    + (content != null ? new String(content, UTF_8) : "null"));
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod(method);
            if (headers != null)
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    conn.setRequestProperty(header.getKey(), header.getValue());
                }
            if (method.matches("^(POST|PUT|DELETE)$")) {
                conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length : 0));
                if (content != null && content.length > 0)
                    conn.setDoOutput(true);
            }
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setInstanceFollowRedirects(false);
            conn.connect();
            // Send request content
            if (method.matches("^(POST|PUT|DELETE)$") && content != null && content.length > 0) {
                DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
                wr.write(content);
                wr.flush();
                wr.close();
            }
            int statusCode = conn.getResponseCode();
            result = new LinkedHashMap<>();
            result.put(HTTP_STATUS_CODE, statusCode);
            result.put(HTTP_STATUS_MESS, conn.getResponseMessage());
            // Get response header
            Map<String, List<String>> resHeaders = conn.getHeaderFields();
            result.put(HTTP_HEADERS, resHeaders);

            // Get response content
            InputStream is = statusCode < 400 ? conn.getInputStream() : conn.getErrorStream();
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1)
                bout.write(buf, 0, length);
            is.close();
            bout.flush();
            byte[] resContent = bout.toByteArray();
            result.put(HTTP_CONTENT, resContent);
            bout.close();
            String logContent = new String(resContent, UTF_8);
            if (logContent.length() > 100)
                logContent = logContent.substring(0, 100) + "...";
            LOGGER.info("status_code: " + statusCode + ", headers: " + resHeaders + ", content: " + logContent);
            // Keepalive
            // conn.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return result;
    }
}
