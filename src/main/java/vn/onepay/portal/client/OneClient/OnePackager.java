package vn.onepay.portal.client.OneClient;
import org.jpos.iso.ISOComponent;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOPackager;
import org.jpos.util.Logger;
import vn.onepay.portal.client.OneClient.OneMsg;

import java.io.*;

/**
 * Created by IntelliJ IDEA.
 * User: NhatNV
 * Date: Apr 14, 2010
 * Time: 10:35:31 AM
 * To change this template use File | Settings | File Templates.
 */
public class OnePackager implements ISOPackager {
    @Override
    public byte[] pack(ISOComponent isoComponent) throws ISOException {
        byte[] result = null;
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(isoComponent.getValue());
            oos.flush();
            result = baos.toByteArray();
        } catch (Exception ex) {
            throw new ISOException(ex);
        }
        return result;
    }

    @Override
    public int unpack(ISOComponent isoComponent, byte[] bytes) throws ISOException {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            unpack(isoComponent, bais);
        } catch (Exception ex) {
            throw new ISOException(ex);
        }
        return 0;
    }

    @Override
    public void unpack(ISOComponent isoComponent, InputStream inputStream) throws IOException, ISOException {
        try {
            ObjectInputStream ois = new ObjectInputStream(inputStream);
            isoComponent.setValue(ois.readObject());
        }catch (IOException ioe) {
            throw ioe;
        }catch (Exception ex) {
            throw new ISOException(ex);
        }
    }

    @Override
    public String getFieldDescription(ISOComponent isoComponent, int i) {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public ISOMsg createISOMsg() {
        return new OneMsg();
    }

    @Override
    public void setLogger(Logger logger, String s) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public String getRealm() {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public Logger getLogger() {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public String getDescription() {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
