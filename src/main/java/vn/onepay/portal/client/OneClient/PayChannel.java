package vn.onepay.portal.client.OneClient;

/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 6/7/2021
 * Time: 2:44 PM
 * To change this IPORTAL_SERVICE.
 */

public enum PayChannel {
    ATM,
    WEB,
    POS,
    COUNTER,
    INTERNET_BANKING,
    MOBILE_BANKING,
    ADL;
    public static String getValue(PayChannel pc){
        switch(pc){
            case ATM:
                return "6011";
            case WEB:
                return "6014";
            case POS:
                return "6012";
            case COUNTER:
                return "6013";
            case ADL:
                return "6015";
            case INTERNET_BANKING:
                return "6016";
            case MOBILE_BANKING:
                return "6017";
            default:
                return "0000";
        }
    }

    public static String getLabel(PayChannel pc){
        switch (pc){
            case ATM:
                return "ATM";
            case WEB:
                return "WEB";
            case POS:
                return "POS";
            case COUNTER:
                return "COUNTER";
            case ADL:
                return "Automatically deduct loan";
            case INTERNET_BANKING:
                return "Internet banking";
            case MOBILE_BANKING:
                return "Mobile banking";
            default:
                return "UNKNOWN";
        }
    }
}

