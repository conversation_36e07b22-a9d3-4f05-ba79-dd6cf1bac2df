package vn.onepay.portal.client.OneClient;
import com.onepay.commons.util.Convert;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOPackager;
import org.jpos.iso.channel.ASCIIChannel;
import org.jpos.iso.channel.HEXChannel;

import java.util.*;
import java.util.logging.Logger;
/**
 * Created by IntelliJ IDEA.
 * User: GiangFu
 * Date: 6/7/2021
 * Time: 2:36 PM
 * To change this IPORTAL_SERVICE.
 */
public class OneClient {
    private String secureCode = "98BD17AAB630143906F7D22D8E51FD58";

    public void setSecureCode(String secureCode) {
        this.secureCode = secureCode;
    }

    private String serverHost;

    public void setServerHost(String _serverHost) {
        this.serverHost = _serverHost;
    }

    private int serverPort;

    public void setServerPort(int _serverPort) {
        this.serverPort = _serverPort;
    }

    public OneClient(String serverHost, int serverPort) {
        this.serverHost = serverHost;
        this.serverPort = serverPort;
    }

    public Map request(Map m, int timeout) throws Exception {
        setHash(m);
        printMap(m);
        ISOMsg req = new OneMsg();
        req.setPackager(packager);
        req.setValue(m);
//        req.setHeader("ISO8583".getBytes());
        ASCIIChannel channel = new ASCIIChannel(serverHost, serverPort, packager);
//        HEXChannel channel = new HEXChannel (serverHost, serverPort, packager, null);
        channel.setTimeout(timeout);
        channel.connect();
        channel.send(req);
        ISOMsg res = channel.receive();
        channel.disconnect();
        if (res != null) {
            return (Map) res.getValue();
        } else {
            return null;
        }
    }

    public Map enquiry(String accountId, Services services, PayChannel payChannel, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "ENQUIRY");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map enquiry(String accountId, Services services, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "ENQUIRY");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map enquiry(String accountId, String serviceId, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "ENQUIRY");
        map.put("account_id", accountId);
        map.put("service_id", serviceId);
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, double settlementAmount, Services services,
                          PayChannel payChannel, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, long settlementAmount, Services services,
                          PayChannel payChannel, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, double settlementAmount, Services services,
                          PayChannel payChannel, Bank bank, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("request_timeout", timeout);
        map.put("bank_id", bank.name().toLowerCase());

        return request(map, timeout);
    }

    public Map settlement(String accountId, long settlementAmount, Services services,
                          PayChannel payChannel, Bank bank, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("request_timeout", timeout);
        map.put("bank_id", bank.name().toLowerCase());

        return request(map, timeout);
    }

    public Map settlement(String accountId, long settlementAmount, String serviceId,
                          String payChannel, String bankId, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", serviceId);
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", payChannel);
        map.put("request_timeout", timeout);
        map.put("bank_id", bankId.toLowerCase());

        return request(map, timeout);
    }

    public Map settlement(String accountId, double settlementAmount, Services services,
                          PayChannel payChannel, String onepayCode, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("onepay_code", onepayCode);
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, long settlementAmount, Services services,
                          PayChannel payChannel, String onepayCode, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("onepay_code", onepayCode);
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, double settlementAmount, Services services,
                          PayChannel payChannel, Bank bank, String onepayCode, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("onepay_code", onepayCode);
        map.put("bank_id", bank.name().toLowerCase());
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public Map settlement(String accountId, long settlementAmount, Services services,
                          PayChannel payChannel, Bank bank, String onepayCode, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("account_id", accountId);
        map.put("service_id", services.name());
        map.put("settlement_amount", settlementAmount);
        map.put("pay_channel", PayChannel.getValue(payChannel));
        map.put("onepay_code", onepayCode);
        map.put("bank_id", bank.name().toLowerCase());
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    private void setHash(Map m) throws Exception {
        Iterator it = m.keySet().iterator();
        List lst = new Vector();
        while (it.hasNext()) {
            lst.add(it.next());
        }
        Collections.sort(lst);
        it = lst.iterator();
        String src = "";
        while (it.hasNext()) {
            String k = (String) it.next();
            if (k.equals(hashField)) {
                continue;
            }
            if (!src.equals("")) src += "|";
            src += encodeString(k) + "=";
            Object v = m.get(k);
            if (v == null) {
                continue;
            }
            if (v instanceof String) {
                src += encodeString((String) v);
            } else {
                src += encodeString(v.getClass().getName());
            }
        }
        src += "|" + secureCode;
        String hash = Convert.toHexString(Convert.md5(src.getBytes()));
        m.put(hashField, hash);
    }

    private String encodeString(String s) {
        if (s == null) return null;
        return s.replaceAll("\\|", "<PIPE>").replaceAll("=", "<EQUAL>");
    }

    public void printMap(Map map) {
        String line = "";
        Iterator it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            line += key + " : " + map.get(key) + "\n";
        }
        logger.info(line);
    }

    public void soutMap(Map map) {
        String line = "";
        Iterator it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            line += key + " : " + map.get(key) + "\n";
        }
        logger.fine(line);
    }

    private Logger logger = Logger.getLogger(OneClient.class.getName());
    private ISOPackager packager = new OnePackager();
    private static final String hashField = "local_hash";

    public Map enqMegastar(String transactionid) throws Exception {
        Map map = new HashMap();
        map.put("vpc_TransactionNo", transactionid);
        map.put("service_id", "MEGASTAR_TICKET_BILLING");
        map.put("request_type", "ENQUIRY");
        map.put("account_id", transactionid);
        map.put("pay_channel", PayChannel.WEB.name());
        map.put("request_timeout", 60000);

        return request(map, 60000);
    }

    public Map stmMegastar(String vpc_OrderInfo, String vpc_MerchTxnRef,
                           double vpc_Amount, String vpc_Merchant, int timeout) throws Exception {
        Map map = new HashMap();
        map.put("request_type", "SETTLEMENT");
        map.put("vpc_OrderInfo", vpc_OrderInfo);
        map.put("vpc_MerchTxnRef", vpc_MerchTxnRef);
        map.put("service_id", "MEGASTAR_TICKET_BILLING");
        map.put("vpc_Amount", vpc_Amount);
        map.put("vpc_Merchant", vpc_Merchant);
        map.put("pay_channel", PayChannel.WEB.name());
        map.put("request_timeout", timeout);

        return request(map, timeout);
    }

    public static void main(String[] args) throws Exception {
        OneClient oc = new OneClient("10.36.71.92", 10238);
        Map m = null;                                                              //0958868320
//                                                             //0955042688  //**********-765   Apocalypitca
//    m = oc.enquiry("0438343019",Services.VTHN_HOMEFONE_BILLING,PayChannel.WEB,60000);
//    m = oc.settlement("0436740981",1000,Services.VTHN_HOMEFONE_BILLING,PayChannel.WEB,60000);
        m = oc.enquiry("G3SLRS", Services.MEKONGAIR_BILLING, PayChannel.WEB, 60000);
//    m = oc.settlement("O3Y2KI",511500)
//    Map m = oc.settlement("**********",100,Services.SFONE_MOBILE_BILLING,PayChannel.WEB,60000);

//    Map m = oc.enquiry("0958868320",Services.SFONE,PayChannel.WEB,60000);
//    Map m = oc.settlement("0955042688",(float) 273,Services.SFONE,PayChannel.WEB,60000);
//    Map m = oc.stmMegastar("116|Date Night|1447628","MSCHVP-5915,2010/07/2911:08",
//        80000,"TESTMEGASTAR",60000);

//    m = oc.enquiry("giang3282",Services.VIETTEL_ADSL_BILLING,60000);   //A01579170
//     Map m = oc.enquiry("**********",Services.SFONE_MOBILE_BILLING,60000);
//    Map m = oc.settlement("0*********",1000,Services.VIETTEL_PSTN_BILLING,PayChannel.WEB,60000);

//    m = oc.enquiry("C4UEYS",Services.JETSTAR_BILLING,60000);
//    m = oc.settlement("C4UEYS",817000,Services.JETSTAR_BILLING,PayChannel.WEB,Bank.BIDV,60000);
//    m = oc.settlement("*********",1000,Services.VIETTEL_HOMEFONE_BILLING,PayChannel.ATM,60000);
//    m = oc.enquiry("phuong1983",Services.VIETTEL_ADSL_BILLING,60000);
//    m = oc.settlement("giang3282",1000,Services.VIETTEL_ADSL_BILLING,PayChannel.ATM,60000);
//    m = oc.enquiry("*********",Services.VIETTEL_PSTN_BILLING,60000);
//    m = oc.settlement("*********",1000,Services.VIETTEL_PSTN_BILLING,PayChannel.ATM,60000);
//    m = oc.enquiry("dhvhntqd_l3ll_hni",Services.VIETTEL_LEASEDLINE_BILLING,60000);
//    m = oc.settlement("dhvhntqd_l3ll_hni",1000,Services.VIETTEL_LEASEDLINE_BILLING,PayChannel.ATM,60000);
//    m = oc.enquiry("S95TJW",Services.JETSTAR_BILLING,60000);
        oc.soutMap(m);
    }
}
