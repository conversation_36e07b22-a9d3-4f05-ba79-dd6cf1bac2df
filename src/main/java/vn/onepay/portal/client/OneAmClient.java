package vn.onepay.portal.client;

import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.onepay.commons.util.Convert;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomStringUtils;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.ResourcesResult;
import vn.onepay.portal.resources.domestic.refund.dto.refund.RefundConfirmReq;
import vn.onepay.portal.resources.user.UserDao;
import vn.onepay.portal.resources.user.dto.OneAmUser;
import vn.onepay.portal.resources.user.dto.User;
import io.vertx.core.http.HttpClientRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;

public class OneAmClient implements IConstants {
    private static final Logger LOGGER = Logger.getLogger(OneAmClient.class.getName());
    private static String ONEPAY_ONE_AM_SERVICE_BASE_URL = Config.getString("one_am_bk.service_uri", "");

    // Step 1
    public static JsonObject postAuth(RoutingContext context,String authUri, String language, String password, String xRequestId,
            String username) {
        JsonObject jsonReturn = null;
        // Request uri base

        String requestMethod = "POST";
        // Milliseconds
        int requestTimeOut = 60000;

        JsonObject jbody = new JsonObject();
        jbody.put("continue", authUri);
        // JsonObject jbody = new JsonObject();

        // jbody.put("id", "");
        // jbody.put("op", "replace");
        // jbody.put("path", "post");
        // jbody.put("value", value);
        try {
            String authQueryURL = ONEPAY_ONE_AM_SERVICE_BASE_URL + "auth";

            LOGGER.info("AUTH STEP 1 URI: " + authQueryURL);
            LOGGER.info("AUTH STEP 1 BODY: " + jbody.encode());

            URL url = new URL(authQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Language", language);
            connection.setRequestProperty("X-Request-Id", xRequestId);
            connection.setRequestProperty("X-Username", username);
            connection.setRequestProperty("X-Password", password);

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {

                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                
                LOGGER.info("AUTH STEP 1 RESPONSE BODY: " + jsonReturn.encode());

                Map<String, List<String>> headers = connection.getHeaderFields();
                List<String> values = headers.get("Set-Cookie");
                String cookie = "NG_TRANSLATE_LANG_KEY=%22vi%22; " + StringUtils.substringBefore(values.get(0), ";")
                + "; JSESSIONID=dummy";
                return getAuthorize(context, language, xRequestId, cookie, jsonReturn.getInteger("clientRequestId").toString());
            } else {
                // Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                // ObjectMapper objectMapper = new ObjectMapper();
                // jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "ONEAM-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("responseCode"),
                        jsonObject.getString("responseDescription"), jsonObject.getString("information_link"),
                        jsonObject.getString("responseDescription"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    private static JsonObject getAuthorize(RoutingContext context,String language, String requestId, String cookieString, String clientRequestId) {
        JsonObject jsonReturn = null;
        // Request uri base
        String requestURI = "authorize?confirm=true";
        String requestMethod = "GET";
        // Milliseconds
        int requestTimeOut = 60000;
        try {
            String aspOrderStateQueryURL = ONEPAY_ONE_AM_SERVICE_BASE_URL + requestURI;

            
            LOGGER.info("AUTH STEP 2 URI: " + aspOrderStateQueryURL);

            LOGGER.info("STEP 2: Set-Cookie" + " => " + cookieString);
            URL url = new URL(aspOrderStateQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
            connection.setRequestProperty("Accept-Language", "vi,en;q=0.9");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Real-IP", "127.0.0.1");
            connection.setRequestProperty("X-Language", language);
            connection.setRequestProperty("X-Request-Id", RandomStringUtils.randomAlphabetic(32));
            connection.setRequestProperty("X-Client-Request-Id", clientRequestId);
            connection.setRequestProperty("Cookie", cookieString);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                LOGGER.log(Level.INFO, "123 " + jsonReturn.getString("redirect"));
                Map<String, String> map = getQueryMap(jsonReturn.getString("redirect"));
                // Step 3
                step3(context,map.get("code"));
            } else {
                // Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                // ObjectMapper objectMapper = new ObjectMapper();
                // jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "MA-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("responseCode"), jsonObject.getString("responseDescription"),
                        jsonObject.getString("information_link"), jsonObject.getString("responseDescription"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }

    public static void step3(RoutingContext context,String code) {
        JsonObject jsonReturn = null;
        // Request uri base

        String requestMethod = "POST";
        // Milliseconds
        int requestTimeOut = 60000;

        try {
            String authQueryURL = Config.getString("one_am_bk.token_uri", "");

            
            LOGGER.info("AUTH STEP 3 URI: " + authQueryURL);

            URL url = new URL(authQueryURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            Util.setRequestMethod(connection, requestMethod);
            String userOneAm = Config.getString("one_am_bk.user", "") + ":" + Config.getString("one_am_bk.password", "");
            String headerAuthorization = "Basic "
                    + Convert.toBase64String(userOneAm.getBytes()).replaceAll("[\r\n]", "");
            connection.setRequestProperty("Authorization", headerAuthorization);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            JsonObject tokenParam = new JsonObject();
            tokenParam.put(CODE, code);
            tokenParam.put(GRANT_TYPE, "authorization_code");
            Buffer buffer = Buffer.buffer(tokenParam.toString(), "UTF-8");

            connection.setRequestProperty("Content-Length", buffer.length() + "");

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.write(tokenParam.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                step4(context,strReturn, tokenParam, headerAuthorization);
            } else {
                // Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                // ObjectMapper objectMapper = new ObjectMapper();
                // jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "ONEAM-SERVICE ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("responseCode"),
                        jsonObject.getString("responseDescription"), jsonObject.getString("information_link"),
                        jsonObject.getString("responseDescription"));
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
    }

    public static void step4(RoutingContext context, String data, JsonObject tokenOneAmParameter, String headerAuthorization) {
        JsonObject token = new JsonObject(data);
        String uriRes = String.format(Config.getString("one_am_bk.resources_uri", ""),
                token.getString(ACCESS_TOKEN).toString()) + "?" + CODE + "="
                + tokenOneAmParameter.getString(CODE).toString() + "&" + GRANT_TYPE + "="
                + tokenOneAmParameter.getString(GRANT_TYPE).toString();
        String headerRes = headerAuthorization + " " + "Bearer" + " " + token.getString(ACCESS_TOKEN).toString();

        JsonObject jsonReturn = null;
        // Request uri base

        String requestMethod = "GET";
        try {

            
            LOGGER.info("AUTH STEP 4 URI: " + uriRes);
            URL url = new URL(uriRes);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("Authorization", headerRes);
            connection.setRequestProperty("Accept", APPLICATION_JSON);
            connection.setRequestProperty("Content-Length", 0 + "");
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                try {
                    ResourcesResult resourcesResult = Util.gson.fromJson(response.toString(), ResourcesResult.class);
                    User user = UserDao.get(resourcesResult.getResources().getProfile().getId());
                        LOGGER.info("[ USER SESSION GET FROM ONEPORTAL ] => " + user);
                    OneAmUser userAM = resourcesResult.getResources().getProfile();
                        LOGGER.info("[ USER SESSION GET FROM ONEAM ] => " + userAM);

                    if (user != null) {

                        String databaseUser = user.getName() + user.getEmail() + user.getAddress() + user.getPhone() + user.getS_id();
                        String oneAmUser = userAM.getLastName() + " " + userAM.getFirstName() + user.getEmail() + userAM.getAddress() + userAM.getMobile() + userAM.getId();

                        String hashUser = "";
                        String hashOneAmUser = "";

                        try {
                            hashUser = Util.hash(databaseUser);
                            hashOneAmUser = Util.hash(oneAmUser);
                        } catch (NoSuchAlgorithmException e) {
                            LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                            try {
                                throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");
                            } catch (Exception e1) {
                                context.fail(e);
                            }
                        } catch (UnsupportedEncodingException e) {
                            LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED ] => HASH ERROR", e);
                                throw new Exception("[ USER SESSION POST LOGIN FAILED ] => HASH ERROR");

                        }

                        if (!hashUser.equals(hashOneAmUser)) {
                            User userData = new User();
                            // userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                            String name = userAM.getLastName();
                            if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                                name += " " + userAM.getFirstName();
                            }
                            userData.setName(name);
                            userData.setEmail(userAM.getEmail());
                            userData.setAddress(userAM.getAddress());
                            userData.setPhone(userAM.getMobile());
                            userData.setS_id(userAM.getId());
                            userData.setN_id(user.getN_id());

                            UserDao.update(userData);

                            // context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            // context.response().putHeader("Pragma", "no-cache");
                            // context.response().putHeader("Expires", "0");
                            context.put(HANDLER_USER_DATA_RESULT, userData);
                            Util.sendResponse(context, 200, userData);
                        } else {


                            // context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                            // context.response().putHeader("Pragma", "no-cache");
                            // context.response().putHeader("Expires", "0");
                            context.put(HANDLER_USER_DATA_RESULT, user);
                            Util.sendResponse(context, 200, user);
                        }
                    } else {
                        User userData = new User();
                        // userData.setName(userAM.getLastName() + StringPool.SPACE + userAM.getFirstName());
                        String name = userAM.getLastName();
                        if (userAM.getFirstName() != null && !userAM.getFirstName().trim().equals("")) {
                            name += " " + userAM.getFirstName();
                        }
                        userData.setName(name);
                        userData.setEmail(userAM.getEmail());
                        userData.setAddress(userAM.getAddress());
                        userData.setPhone(userAM.getMobile());
                        userData.setS_id(userAM.getId());

                        userData = UserDao.insert(userData);



                        context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                        context.response().putHeader("Pragma", "no-cache");
                        context.response().putHeader("Expires", "0");
                        context.put(HANDLER_USER_DATA_RESULT, userData);
                        context.put(HANDLER_DATA_TYPE, TEMPLATE_TYPE);
                        context.put(TEMPLATE_FILE_NAME, Config.getString("server.templatePath", ""));
                        context.response().putHeader("Cache-Control", "no-cache,no-store,private,must-revalidate,max-stale=0,post-check=0,pre-check=0");
                        context.response().putHeader("Pragma", "no-cache");
                        context.response().putHeader("Expires", "0");
                        Util.sendResponse(context, 200, userData);
                    }
                    } catch (Exception e1) {
                        context.fail(e1);
                    }
            } else {
                LOGGER.log(Level.SEVERE, "[ USER SESSION POST LOGIN FAILED 2 ] => RESULT :" + responseCode + " DESC : " + responseMsg);
                try {
                    throw new Exception("[ USER SESSION POST LOGIN FAILED 2 ] => RESULT :" + responseCode + " DESC : " + responseMsg);
                } catch (Exception e) {
                    context.fail(e);
                }
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
    }

    public static Map<String, String> getQueryMap(String query) {
        String[] params = query.split("&");
        Map<String, String> map = new HashMap<String, String>();

        for (String param : params) {
            String name = param.split("=")[0];
            String value = param.split("=")[1];
            map.put(name, value);
        }
        return map;
    }
}
