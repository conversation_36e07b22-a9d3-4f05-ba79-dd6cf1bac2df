package vn.onepay.portal.client;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import vn.onepay.ows.Authorization;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import vn.onepay.portal.Util;


import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.codec.binary.Hex;
import com.onepay.commons.util.Convert;
import static java.nio.charset.StandardCharsets.UTF_8;

public class PayCollectClient extends Db implements IConstants {
    private static final Logger LOGGER = Logger.getLogger(PayCollectClient.class.getName());
    private static String PAYCOLLECT_SERVICE_BASE_URL = Config.getString("paycollect-service.url", "");
    private static final Integer PAYCOLLECT_TIMEOUT = Config.getInteger("paycollect-service.timeout", 60000);
    private static final String PAYCOLLECT_SECURE = Config.getString("paycollect-service.secure", "");
    private static final String PAYCOLLECT_VCB_SECRET = Config.getString("paycollect-service.vcb_secret", "OnepayVcb@159");
    private static final String PAYCOLLECT_VCB_PRI_KEY = Config.getString("paycollect-service.vcb_pri_key", "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCfRr9e2JmfWT/gDTljvTW7eGcz0jYg/r/fyeUlgOxfrCCKsAD86qQn3JFYVvtQNhBJcLShmcAh00GkZ3xl9eSmmmAfE+3b/llRdBixzssEHqS9WNuwWJ8wvhE8GIY6CL/4ZeKGkP980qycZV9XGloMb0kvW9uyzWrCt+i6cHmnunqwZQZwnzIijF6Y5/uwUw37l5FCxC2b6kXBKkn7sCBWMaPyJUksZzXAr05dOtdROTzVHM5k47ca/YRSzuVpWfS4YmGgD9C0PtryQEborFrW8czkwEjRnUrVn6Xoisbp5OwowZ21NMyp3CutY7k6vc0+5+SrxbbgeDBkzlq3cyndAgMBAAECggEAHHMMl6pktjvA1wpFp/UR9expsq3nkTkvmI99aJeAoQ4jueH6KzLWI3CYXYkb1pXxjsyc1wiZcvFDFlSNucCUGNYWOTbcE6F43r2383qWdKPslQ2cIAGUXaZ1eaB/yTnq4DmCnrqjh+4xerX4s5w3XWU7U0Q7F9LtMN6cqUCR2k2CYMaFjD1zI+/owBHfoCPKnCTLPs89s5dAnBaFXL3JhaxxKcuJbU7E5bQIXi9Y0gkGKjxzrEoAmokxYpzjMKSgAT8bdSadBoVkkhb12VSQXqGGOvVj82P4aQahHG4D7qUwZcv/bvtiMF7R/k1yxCFNoDOXFmTSIZapKr30pNz2GQKBgQDbA6UJny4B1BBY0geXnWeUjsPAMjZJiHZlYO6sqXELxx1UO8jWHjHXCJZQcCiiYJaLZB1cLE2MOZkO4oT5wHmLdz9Jw8tg4PVX2U45EOVVeY4ZtKHvj4Dz1WsJgJ4b9Fuj/UBzzYHOeb/uqobBvtC6iQXzgxV9OYnZYR2u/o2ljwKBgQC6LIiQawdxcj1JcL4E8WKlCJelbCzeSMreEnw6W5TNMJsBw/395+7nN0SkIG0SqabYCOMmRnVuiCCI8zww6FhIlbPZ9xPLyDZUs9U7XjhEDzAR6hoWDyhI5TI/AGpGslRsXRhXBYLEi9nhIuRKmJK3FvR1d66CB6mngYAif1h70wKBgE5UVF9Hlf3DaYFKxLpd6mDAWpRPoQjVjHqfm10XUL4EnaG1rBOgQvUENjZYlr/USaNa0AFaOvXu16XyL9rfEtllcbYhF4jzi+u6ZXaVa2ARlFfvOYdjTJxodNq2LFYVI/1XowiHtGgzNQjX5lA+vYopXX6FIFdkD8A2MpaWpfRTAoGBAJelCp/H7koj6XPWl5DjJZyTwSIHWQy+cjAtEm9ehXgVbU0k3dBhiSFgzVJ67x1SfwpdQti8b47W5tlmdtLHjpGhDtwt+zv0Gv0ndjpjZqVZUahl7VWmhwPJXQIgTnDDdSzHL7pCTN/XomNVzmpMmMT1t62vDL/xielc/Igc/m/tAoGADRx00bgusk1D1OQ4v96ubemh/CX0H4DXL5YjQHir2sm6sdQJCWJuipBfitySt1wUcaic22gJClX462K1b3Me4XlcTNMVBvQBpSTxcNh7gOVS1EoRkme4UOxtuE6kjSMcZbeXzoiYWVURoi+hGfTzZbXuICAthyMv9sqpUNoi3No=");
    private static final String PAYCOLLECT_MSB_ACCESS_CODE = Config.getString("paycollect-service.msb_access_code", "123456");

    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    public static JsonObject addVPBTransaction(String amount, String create_time, String funds_transfer_id,
            String receiver_account_number, String receiver_bank_id, String receiver_holder_name,
            String receiver_master_number,
            String remark, String trans_time, String userId) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/paycollect/api/v1/cdr/banks/" + receiver_bank_id + "/funds_transfers/" + funds_transfer_id;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = PAYCOLLECT_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("request_time", yyyyMMddTHHmmssZ.format(requestDate));
        jbody.put("request_id", yyyyMMddTHHmmssZ.format(requestDate));
        jbody.put("amount", amount);

        jbody.put("create_time", create_time);
        jbody.put("currency", "VND");
        jbody.put("funds_transfer_id", funds_transfer_id);
        jbody.put("receiver_account_number", receiver_account_number);
        jbody.put("receiver_bank_id", receiver_bank_id);
        jbody.put("receiver_holder_name", receiver_holder_name);
        jbody.put("receiver_master_number", receiver_master_number);
        jbody.put("remark", remark);
        jbody.put("trans_time", trans_time);
        
        String appId = "VPB";
        String appKey = "A57525892B2E40A8845E201BF0114BBF";
        String region = "onepay";
        String service = "paycollect";
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());

            String uriSign = "/paycollect/api/v1/cdr/banks/" + receiver_bank_id + "/funds_transfers/" + funds_transfer_id;

            Map queryParamsMap = new LinkedHashMap();
            Authorization authorization = new Authorization(appId, appKey, region, service, "PUT", uriSign, queryParamsMap, signedHeaders, jbody.encode().getBytes(), dateTime, expires);

            String createAccountCollectURL = PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            LOGGER.info("API request:" + createAccountCollectURL);
            URL url = new URL(createAccountCollectURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(6000);
            connection.setReadTimeout(6000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            connection.setRequestProperty("X-OP-Expires", expires + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("X-OP-Authorization", authorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
                jsonReturn = new JsonObject(strReturn);
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "", jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        return jsonReturn;
    }

    public static JsonObject addBIDVTransaction(String trans_id, String trans_date, String customer_id,
        String service_id, String bill_id, String amount, String receiver_bank_id, String userId, String remark, String bankTransId) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/paycollect/api/v1/cdr/banks/" + receiver_bank_id + "/paybill";
        String requestMethod = "POST";
        // Remove identify Acount - Bo 6 ky tu nhan dang tai khoan
        String shortCustomerId = ( customer_id.length() == 16 ) ? customer_id.substring(6) : customer_id;
        
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = PAYCOLLECT_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("trans_id", trans_id);
        jbody.put("trans_date", trans_date);
        jbody.put("customer_id", shortCustomerId);
        jbody.put("service_id", "006003");
        jbody.put("bill_id", bill_id);
        jbody.put("amount", amount);
        jbody.put("remark", remark);
        jbody.put("bidv_id", bankTransId);
        String secretCode = PAYCOLLECT_SECURE;
        String raw = secretCode + "|" + trans_id + "|" + bill_id + "|" + amount;
        // MD5
        String hash = Convert.toHexString(Convert.md5(raw.getBytes())).toLowerCase();
        // MessageDigest md = MessageDigest.getInstance("MD5");
        // md.update(raw.getBytes());
        // byte[] digest = md.disget();
        // String hash = Hex.encodeHexString(digest).toLowerCase();



        jbody.put("checksum", hash);
        // jbody.put("currency", "VND");
        // jbody.put("funds_transfer_id", funds_transfer_id);
        // jbody.put("receiver_account_number", receiver_account_number);
        // jbody.put("receiver_bank_id", receiver_bank_id);
        // jbody.put("receiver_holder_name", receiver_holder_name);
        // jbody.put("receiver_master_number", receiver_master_number);
        // jbody.put("remark", remark);
        // jbody.put("trans_date", trans_time);
        
        String appId = "BIDV";
        String appKey = "A57525892B2E40A8845E201BF0114BAA";
        String region = "onepay";
        String service = "paycollect";
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());

            String uriSign = "/paycollect/cdr/api/v1/banks/" + receiver_bank_id + "/paybill";

            Map queryParamsMap = new LinkedHashMap();
            Authorization authorization = new Authorization(appId, appKey, region, service, "POST", uriSign, queryParamsMap, signedHeaders, jbody.encode().getBytes(), dateTime, expires);

            String createAccountCollectURL = PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            LOGGER.info("API request:" + createAccountCollectURL);
            URL url = new URL(createAccountCollectURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(6000);
            connection.setReadTimeout(6000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            connection.setRequestProperty("X-OP-Expires", expires + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("X-OP-Authorization", authorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
                jsonReturn = new JsonObject(strReturn);
                if (jsonReturn.getString("result_code") != null && !"000".equals(jsonReturn.getString("result_code"))){
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("result_desc"), "", jsonReturn.getString("result_desc"));
                }
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "", jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        return jsonReturn;
    }
    
    public static JsonObject addVCBTransaction(String channelId,  String billId, String dCreateStr, String billAmount, String internalTrans) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        String key = PAYCOLLECT_VCB_SECRET;
        String cdrPrikey = PAYCOLLECT_VCB_PRI_KEY;

        // Request uri base
        String requestURI = "/paycollect/api/v1/cdr/banks/BFTVVNVX/paybill";
        String requestMethod = "POST";

        String sbody = genRequestAddVcbTransContent(channelId,billId,billId,dCreateStr,billAmount,key, cdrPrikey, internalTrans);


        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + sbody);

            Map queryParamsMap = new LinkedHashMap();

            String addTransVcbURL = PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            LOGGER.info("API request:" + addTransVcbURL);
            URL url = new URL(addTransVcbURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(60000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            connection.setRequestProperty("X-OP-Expires", expires + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            // connection.setRequestProperty("X-OP-Authorization", authorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(sbody.getBytes(UTF_8));
            wr.flush();
            wr.close();


            LOGGER.log(Level.INFO, "BODY bdJson:" + sbody);


            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
                jsonReturn = new JsonObject(strReturn);
                JsonObject jsonReturnCtx = jsonReturn.getJsonObject("context");
                if (jsonReturnCtx == null || (jsonReturnCtx != null && "FAILURE".equalsIgnoreCase(jsonReturnCtx.getString("status")))) {
                    String errMessage = jsonReturnCtx == null ? "INTERNAL_SERVER_ERROR" : jsonReturnCtx.getString("errorMessage");
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", errMessage, "", errMessage);
                }
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "", jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        return jsonReturn;
    }

    public static byte[] encodeHex(byte[] value) {
        return Hex.encodeHexString(value).getBytes();
    }

    public static String getSignature(String value) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        byte[] data = value.getBytes();
        byte[] result;
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(value.getBytes("UTF-8")); // Change this to "UTF-16" if needed
        result = md.digest();
        return new String(encodeHex(result)).toUpperCase();
    }


    public static JsonObject addMSBTransaction(String tranSeq,String vaCode, String vaName,String fromAccountName,String fromAccountNumber,String toAccountName,String toAccountNumber,String tranRemark,String bankDate,String vaNumber, String tranAmount) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        String accessCode = PAYCOLLECT_MSB_ACCESS_CODE;

        // Request uri base
        String requestURI = "/paycollect/api/v1/banks/MCOBVNVX/paybill";
        String requestMethod = "POST";


        // build request data
        String sigData = accessCode + ""+tranSeq + "" +bankDate +""+vaNumber + ""+tranAmount + "" + fromAccountNumber+ "" + toAccountNumber;
        String signature = getSignature(sigData);
        JsonObject jContext = new JsonObject()
        .put("tranSeq", tranSeq)
            .put("vaCode", vaCode)
            .put("vaNumber", vaNumber)
            .put("vaName", vaName)
            .put("fromAccountName", fromAccountName)
            .put("fromAccountNumber", fromAccountNumber)
            .put("toAccountName", toAccountName)
            .put("tranAmount", tranAmount)
            .put("toAccountNumber", toAccountNumber)
            .put("tranRemark", tranRemark)
            .put("tranDate", bankDate)
            .put("signature",signature );


        String sbody =jContext.encode();


        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + sbody);

            Map queryParamsMap = new LinkedHashMap();

            String addTransMsbURL = PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            LOGGER.info("API request:" + addTransMsbURL);
            URL url = new URL(addTransMsbURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(60000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            connection.setRequestProperty("X-OP-Expires", expires + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            // connection.setRequestProperty("X-OP-Authorization", authorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(sbody.getBytes(UTF_8));
            wr.flush();
            wr.close();




            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
                jsonReturn = new JsonObject(strReturn);
                if (jsonReturn == null || (jsonReturn != null && !"SUCCESS".equalsIgnoreCase(jsonReturn.getString("name")))) {
                    String errMessage = jsonReturn == null ? "INTERNAL_SERVER_ERROR" : jsonReturn.getString("errorMessage");
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", errMessage, "", errMessage);
                }
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "", jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }
        return jsonReturn;
    }



    public static String genRequestAddVcbTransContent(String channelId, String billId, String customerId, String dCreateStr, String billAmount, String key, String cdrPrikey, String internalTrans) throws Exception {
        String channelRef, signature;
        channelRef = billId;
        JsonObject jBill = new JsonObject().put("billId", billId).put(AMOUNT, billAmount);
        JsonArray jBills = new JsonArray().add(jBill);
        String dataSign = channelId + "|" + channelRef + "|" + key;
        signature = Util.signRSASHA256(dataSign, Util.getPrivateKeyFromString(cdrPrikey));
        // signature="";
        JsonObject jContext = new JsonObject().put("channelId", channelId).put("channelRefNumber", channelRef).put("requestDateTime", dCreateStr);
        JsonObject jPayload = new JsonObject().put("bills", jBills).put("customerCode", customerId).put("internalTransactionRefNo", internalTrans);
        return new JsonObject().put("payload", jPayload).put("context", jContext).put("signature", signature).encode();
    }

    public static Map retryNotify(String transId) throws Exception {
        Integer responseCode = 200;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/paycollect/api/v1/id/" + transId + "/notify";
        String requestMethod = "PUT";
        Date requestDate = new Date();

        JsonObject jbody = new JsonObject();
        jbody.put("request_time", yyyyMMddTHHmmssZ.format(requestDate));
        jbody.put("request_id", yyyyMMddTHHmmssZ.format(requestDate));

        String appId = Config.getString("retry_notify.appId", "");
        String appKey = Config.getString("retry_notify.appKey", "");
        String region = Config.getString("retry_notify.region", "");
        String service = Config.getString("retry_notify.service", "");
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());

            Map queryParamsMap = new LinkedHashMap();
            Authorization authorization = new Authorization(appId, appKey, region, service, "PUT", requestURI, queryParamsMap, signedHeaders, jbody.encode().getBytes(), dateTime, expires);

            String createAccountCollectURL = PAYCOLLECT_SERVICE_BASE_URL + requestURI;
            LOGGER.info("API request:" + createAccountCollectURL);
            URL url = new URL(createAccountCollectURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(requestMethod);
            connection.setConnectTimeout(60000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            connection.setRequestProperty("X-OP-Expires", expires + "");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("X-OP-Authorization", authorization.toString());

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                LOGGER.log(Level.SEVERE, "Return Bođy: " + strReturn);
                jsonReturn = new JsonObject(strReturn);
            } else {
                LOGGER.log(Level.SEVERE, "MSP_CLIENT POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "", jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "", "Resend the request at another time.");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
            throw e;
        }

        // success
        Map<String, Object> mIn = new HashMap<>();
        mIn.put("n_result", responseCode);
        return mIn;
    }
}
