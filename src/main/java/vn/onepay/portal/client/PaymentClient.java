package vn.onepay.portal.client;

import com.onepay.commons.util.Convert;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PaymentClient extends Db {

    private static final Logger LOGGER = Logger.getLogger(MaClient.class.getName());
    private static String PAYMENT_SERVICE_BASE_URL = Config.getString("advance-service.url", "");
    private static Integer PAYMENT_SERVICE_TIMEOUT = Config.getInteger("advance-service.timeout", 60000);
    private static String PAYMENT_SERVICE_SECURE = Config.getString("advance-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");


    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }


    public static JsonObject postAdjustManual(JsonObject jData) throws Exception {
        JsonObject jsonReturn = null;
        LOGGER.info("Client adjust manual advance service map : " + Util.mask(jData));
        String url = PAYMENT_SERVICE_BASE_URL + "/prefix_advance/add_manual_adjust";
        LOGGER.info("Client adjust manual advance service url : " + Util.mask(url));
        try {
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(PAYMENT_SERVICE_TIMEOUT);

            conn.setRequestMethod("POST");
            conn.setRequestProperty("User-Agent", "Onecredit HTTP Client");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.addRequestProperty("Content-Length", "" + Integer.toString(jData.encode().getBytes("UTF-8").length));
            conn.setRequestProperty(X_SECURE_HASH, genHMACSHA256(jData.encode().getBytes("UTF-8"), PAYMENT_SERVICE_SECURE));

            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setInstanceFollowRedirects(false);

            // Send request params
            DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
            wr.write(jData.encode().getBytes("UTF-8"));
            wr.flush();
            wr.close();

            int responseCode = conn.getResponseCode();
            LOGGER.fine("response_code=" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {

                InputStream is = conn.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.INFO, "ADVANCE SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                // Get Response
                InputStream is = conn.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT TRANSFER ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
            conn.disconnect();
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ADVANCE SERVICE ERROR]", e);
        }
        return jsonReturn;
    }

    public static JsonObject postAdjustManualBigMerchant(JsonObject jData) throws Exception {
        JsonObject jsonReturn = null;
        LOGGER.info("Client adjust manual advance service map : " + Util.mask(jData));
        String url = PAYMENT_SERVICE_BASE_URL + "/prefix_advance/add_manual_adjust_big_merchant";
        LOGGER.info("Client adjust manual advance service url : " + Util.mask(url));
        try {
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(PAYMENT_SERVICE_TIMEOUT);

            conn.setRequestMethod("POST");
            conn.setRequestProperty("User-Agent", "Onecredit HTTP Client");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.addRequestProperty("Content-Length", "" + Integer.toString(jData.encode().getBytes("UTF-8").length));
            conn.setRequestProperty(X_SECURE_HASH, genHMACSHA256(jData.encode().getBytes("UTF-8"), PAYMENT_SERVICE_SECURE));

            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setInstanceFollowRedirects(false);

            // Send request params
            DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
            wr.write(jData.encode().getBytes("UTF-8"));
            wr.flush();
            wr.close();

            int responseCode = conn.getResponseCode();
            LOGGER.fine("response_code=" + responseCode);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {

                InputStream is = conn.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                jsonReturn = new JsonObject(response.toString());
                LOGGER.log(Level.INFO, "ADVANCE SERVICE RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                // Get Response
                InputStream is = conn.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                JsonObject jsonObject = new JsonObject(strReturn);
                LOGGER.log(Level.SEVERE, "PAYOUT TRANSFER ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
            conn.disconnect();
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "[ADVANCE SERVICE ERROR]", e);
        }
        return jsonReturn;
    }

    private static String genHMACSHA256(byte[] data, String secretKey) throws Exception {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(Convert.decodeHexa(secretKey.getBytes()), "HMACSHA256");
            Mac mac = Mac.getInstance("HMACSHA256");
            mac.init(signingKey);
            return Convert.toHexString(mac.doFinal(data));
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "", e);
            return "";
        }
    }

}
