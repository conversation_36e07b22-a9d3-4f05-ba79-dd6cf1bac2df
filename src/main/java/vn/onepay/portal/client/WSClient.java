package vn.onepay.portal.client;

import com.caucho.hessian.client.HessianProxyFactory;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.domestic.refund.DomesticRefundHandler;
import vn.onepay.portal.utils.Common;
import vn.onepay.portal.utils.WebContext;

import java.util.logging.Level;
import java.util.logging.Logger;

public class WSClient {
    private static Logger logger = Logger.getLogger(WSClient.class.getName());
    private static final HessianProxyFactory factory = new HessianProxyFactory();
    private static IService ws = null;
    private static final String serviceUrl = Config.getString("refund.one_comm_url", "");
    public static Object execute(Object request) {
        logger.log(Level.INFO, "Domestic Update Refund Input: " + request);
        Object respone = null;
        try {
            //HessianProxyFactory factory = new HessianProxyFactory();
            factory.setChunkedPost(false);
            if (ws == null)
                ws = (IService) factory.create(IService.class, serviceUrl);
            byte[] brequest = Common.serialize(request);
            logger.info("brequest=" + new String(brequest));
            respone = Common.deserialize((byte[]) ws.execute(brequest));
        }
        catch (Exception ex) {
            logger.log(Level.SEVERE, "execute error :", ex);
        }
        return respone;
    }
}
