package vn.onepay.portal.client;

import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.Convert;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.utils.OneCreditUtil;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.nio.charset.StandardCharsets.UTF_8;

public class OneCreditClient implements IConstants {


    private static final Logger logger = Logger.getLogger(OneCreditClient.class.getName());
    private static String ONEPAY_ONECREDIT_SERVICE_URL = Config.getString("refund.one_credit-url", "");
    private static String ONEPAY_ONECREDIT_SERVICE_ACCESSCODE = Config.getString("refund.one_credit-secure_code", "");
    private static String ONEPAY_ONECREDIT_SERVICE_TIMEOUT = Config.getString("refund.one_credit-time_out", "60000");
//    private static String ONEPAY_PCS_SERVICE_NAME = PropsUtil.get("pcs.service.name", "");
//    private static String ONEPAY_PCS_SERVICE_REGION = PropsUtil.get("pcs.service.region", "");
//    private static String ONEPAY_PCS_SERVICE_CLIENT_ID = PropsUtil.get("pcs.service.client.id", "");
//    private static String ONEPAY_PCS_SERVICE_AUTH_TYPE = PropsUtil.get("pcs.service.ows.request", "");
//    private static String ONEPAY_PCS_SERVICE_AUTH_ALGORITHM = PropsUtil.get("pcs.service.ows.algorithm", "");
//    private static String ONEPAY_PSP_SERVICE_CLIENT_KEY = PropsUtil.get("pcs.service.client.key", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }


    public static JsonObject refund(Map data) {
        JsonObject jsonReturn = null;

        String requestMethod = "POST";

        Date dt = new Date();
        data.put("timeout", String.valueOf(ONEPAY_ONECREDIT_SERVICE_TIMEOUT));
        data.put("start_date", Convert.toString(dt, "yyyyMMddHHmmss", "19802210041200"));


        JsonObject jbody = new JsonObject(data);
        logger.log(Level.INFO, "CLIENT REFUND REQUEST DATA: " + jbody.encodePrettily());


        try {

            logger.log(Level.INFO, "POST TO ONECREDIT");
            logger.log(Level.INFO, "url: " + ONEPAY_ONECREDIT_SERVICE_URL);
            logger.log(Level.INFO, "refund info: " + Util.mask(jbody.toString()));


            URL url = new URL(ONEPAY_ONECREDIT_SERVICE_URL);

            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(requestMethod);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty(X_SECURE_HASH, OneCreditUtil.genHMACSHA256(jbody.toString().getBytes(), ONEPAY_ONECREDIT_SERVICE_ACCESSCODE));
            connection.setRequestProperty(HttpHeaders.USER_AGENT + "", "Onecredit HTTP Client");
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(
                    connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();

            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            logger.info("responseCode:" + responseCode);
            logger.info("responseMsg:" + responseMsg);

            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                logger.log(Level.INFO, "ONECREDIT RESPONSE: " + Util.mask(jsonReturn.toString()));
            } else {
                //Get Response
                InputStream is = connection.getErrorStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
//                ObjectMapper objectMapper = new ObjectMapper();
//                jsonReturn = objectMapper.readTree(strReturn);
                JsonObject jsonObject = new JsonObject(strReturn);
                logger.log(Level.SEVERE, "ONECREDIT ERROR RESPONSE: " + jsonObject.encodePrettily());
                throw new ErrorException(500, jsonObject.getString("name"), jsonObject.getString("message"), jsonObject.getString("information_link"), jsonObject.getString("details"));
            }
        } catch (IOException e) {
            logger.log(Level.SEVERE, "[ERROR]", e);
        }
        return jsonReturn;
    }
}
