package vn.onepay.portal.client;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.json.JsonObject;
import oracle.jdbc.OracleTypes;
import vn.onepay.portal.Config;
import vn.onepay.portal.IConstants;
import vn.onepay.portal.Util;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;
import vn.onepay.portal.resources.base.dto.IErrors;
import vn.onepay.portal.resources.quicklink.dto.MerchantProfileParam;
import vn.onepay.portal.resources.quicklink.handler.UploadFileDTO;

public class QuicklinkClient extends Db {
    private static final Logger LOGGER = Logger.getLogger(QuicklinkClient.class.getName());
    private static final ObjectMapper objectMapper = new ObjectMapper();
    public static final String HTTP_STATUS_CODE = "status_code";
    public static final String HTTP_STATUS_MESS = "status_mess";
    public static final String HTTP_HEADERS = "headers";
    public static final String HTTP_CONTENT = "content";
    private static String QUICKLINK_SERVICE_BASE_URL = Config.getString("quicklink-service.url", "");
    private static final Integer QUICKLINK_TIMEOUT = Config.getInteger("quicklink-service.timeout", 60000);
    private static final String QUICKLINK_SECURE = Config.getString("quicklink-service.secure", "");
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
    private static final String ONEPAY_QUICKLINK_SERVICE_BASE_URL = Config.getString("quicklink-service.onepay_quicklink_service_base_url", "");
    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public static JsonObject getMerchantProfile(String keyword, int page, int pageSize) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        String params = keyword;
        String endCodeStr = URLEncoder.encode(params, StandardCharsets.UTF_8.name());
        // Request uri base
        String requestURI = "/quicklink-backend/quicklink/merchant-profile?keyword=" + endCodeStr + "&page=" + page
                + "&pageSize=" + pageSize;
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        // JsonObject jbody = new JsonObject();
        // jbody.put("keyword", keyword);
        // jbody.put("page", page);
        // jbody.put("pageSize", pageSize);

        try {

            LOGGER.log(Level.INFO, "GET TO QUICKLINK-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            // LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

            URL url = new URL(quicklinkUrl);

            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            // LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            // connection.addRequestProperty("Content-Length", "" +
            // Integer.toString(jbody.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            // wr.write(jbody.encode().getBytes(UTF_8));
            // wr.flush();
            // wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);

                LOGGER.log(Level.SEVERE, "QUICK LINK GET MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
            } else {
                LOGGER.log(Level.SEVERE,
                        "QUICK LINK GET MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;
    }

    public static JsonObject getMerchantProfileById(Integer id) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/quicklink-backend/quicklink/merchant-profile/" + id;
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        try {
            LOGGER.log(Level.INFO, "GET TO QUICKLINK-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

            URL url = new URL(quicklinkUrl);

            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            // wr.write(jbody.encode().getBytes(UTF_8));
            // wr.flush();
            // wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);

                LOGGER.log(Level.SEVERE, "QUICK LINK GET MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
            } else {
                LOGGER.log(Level.SEVERE,
                        "QUICK LINK GET MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;
    }

    public static JsonObject getMerchantProfileByName(String name) throws Exception {
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String params = name;
        String endCodeStr = URLEncoder.encode(params, StandardCharsets.UTF_8.name());
        String requestURI = "/quicklink-backend/quicklink/merchant-profile-name/" + endCodeStr;
        String requestMethod = "GET";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        try {
            LOGGER.log(Level.INFO, "GET TO QUICKLINK-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            LOGGER.log(Level.INFO, " info: " + jbody.encode());
            String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

            URL url = new URL(quicklinkUrl);

            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            // wr.write(jbody.encode().getBytes(UTF_8));
            // wr.flush();
            // wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);

                LOGGER.log(Level.SEVERE, "QUICK LINK GET MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
            } else {
                LOGGER.log(Level.SEVERE,
                        "QUICK LINK GET MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return jsonReturn;
    }

    public static Integer createMerchantProfile(MerchantProfileParam merchantProfileParam) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/quicklink-backend/quicklink/merchant-profile";
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("name", merchantProfileParam.getName());
        jbody.put("logo", merchantProfileParam.getLogo());
        jbody.put("merchantName", merchantProfileParam.getMerchantName());
        jbody.put("address", merchantProfileParam.getAddress());
        jbody.put("website", merchantProfileParam.getWebsite());
        jbody.put("email", merchantProfileParam.getEmail());
        jbody.put("merchantMobile", merchantProfileParam.getMerchantMobile());
        jbody.put("fields", merchantProfileParam.getFields());
        jbody.put("partnerId", merchantProfileParam.getPartnerId());
        jbody.put("paymentMethod", merchantProfileParam.getPaymentMethod());
        jbody.put("operator", merchantProfileParam.getOperator());
        jbody.put("language", merchantProfileParam.getLanguage());
        jbody.put("notifyEmail", merchantProfileParam.getNotifyEmail());
        jbody.put("requiredFields", merchantProfileParam.getRequiredFields());
        jbody.put("termConditionFile", merchantProfileParam.getTermConditionFile());
        jbody.put("termJson", objectMapper.readValue(merchantProfileParam.getTermJson(), new TypeReference<Map<String, String>>() {}) );
        jbody.put("enableCaptcha", Objects.nonNull(merchantProfileParam.getEnableCaptcha()) ? merchantProfileParam.getEnableCaptcha() : false);
        jbody.put("enableOnlyForeignCurrency", Objects.nonNull(merchantProfileParam.getEnableOnlyForeignCurrency()) ? merchantProfileParam.getEnableOnlyForeignCurrency() : false);

        LOGGER.log(Level.INFO, "POST TO QUICKLINK-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

        URL url = new URL(quicklinkUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "QUICK LINK CREATE MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
        } else {
            LOGGER.log(Level.SEVERE,
                    "QUICK LINK CREATE MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.MERCHANT_PROFILE_EXISTED;
        }

        return result;
    }

    public static Integer updateMerchantProfile(JsonObject newValue) throws Exception {
        Integer result = 0;
        JsonObject jsonReturn = null;
        int expires = 300;
        // Request uri base
        String requestURI = "/quicklink-backend/quicklink/merchant-profile/" + newValue.getValue("id");
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody = newValue;
        LOGGER.log(Level.INFO, "PUT TO QUICKLINK-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

        URL url = new URL(quicklinkUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 1;

            LOGGER.log(Level.SEVERE, "QUICK LINK UPDATE MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);
            result = 0;
            throw IErrors.MERCHANT_PROFILE_EXISTED;
        }

        return result;

    }

    public static Integer deleteMerchantProfile(Integer id, String operator) throws Exception {
        Integer result = 0;
        int expires = 300;
        // Request uri base
        String requestURI = "/quicklink-backend/quicklink/merchant-profile/" + id;
        String requestMethod = "DELETE";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put("operator", operator);

        try {

            LOGGER.log(Level.INFO, "DELETE TO QUICKLINK-SERVICE");
            LOGGER.log(Level.INFO, "url: " + requestURI);
            String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestURI;

            URL url = new URL(quicklinkUrl);

            Date dateTime = new Date();
            Map<String, Object> signedHeaders = new HashMap();
            signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
            signedHeaders.put("X-OP-Expires", expires + "");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // connection.setRequestMethod(requestMethod);
            Util.setRequestMethod(connection, requestMethod);
            connection.setRequestProperty("X-HTTP-Method-Override", "DELETE");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
            connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
            connection.addRequestProperty("Content-Length", "");

            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.write(jbody.encode().getBytes(UTF_8));
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            LOGGER.info("responseCode:" + responseCode);
            LOGGER.info("responseMsg:" + responseMsg);
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                // Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                result = 1;

                LOGGER.log(Level.SEVERE, "QUICK LINK DELETE MERCHANT PROFILE RESPONSE: ");
            } else {
                LOGGER.log(Level.SEVERE,
                        "QUICK LINK DELETE MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
                throw IErrors.INTERNAL_SERVER_ERROR;
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }

        return result;
    }
    public static JsonObject getAllLink(Map<String,Object> param) throws Exception{

        JsonObject jsonReturn = null;
        // Request uri base
        String requestUri = "/quicklink-backend/quicklink/all-link";
        int expires = 6000;
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(PAGE, param.getOrDefault(PAGE, ""));
        jbody.put(PAGESIZE, param.getOrDefault(PAGESIZE, ""));
        jbody.put(KEYWORD, param.getOrDefault(KEYWORD, ""));
        jbody.put(STATE, param.getOrDefault(STATE, ""));
        jbody.put(STATUS_LINK, param.getOrDefault(STATUS_LINK, ""));
        jbody.put(INSTALLMENT_MERCHANT_ID, param.getOrDefault(INSTALLMENT_MERCHANT_ID, ""));
        jbody.put(PAYNOW_MERCHANT_ID, param.getOrDefault(PAYNOW_MERCHANT_ID, ""));
        jbody.put(BNPL_MERCHANT_ID, param.getOrDefault(BNPL_MERCHANT_ID, ""));
        jbody.put(EMAIL, param.getOrDefault(EMAIL, ""));
        jbody.put(FROMDATE, param.getOrDefault(FROMDATE, ""));
        jbody.put(TODATE, param.getOrDefault(TODATE, ""));
        jbody.put(PARTNER, param.getOrDefault(PARTNER, ""));

        LOGGER.log(Level.INFO, "POST TO QUICKLINK-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestUri);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestUri;

        URL url = new URL(quicklinkUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + jbody.toString().getBytes().length);

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("quicklink service responseCode:" + responseCode);
        LOGGER.info("quicklink service  responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);

            LOGGER.log(Level.SEVERE, "QUICK LINK CREATE MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
        } else {
            LOGGER.log(Level.SEVERE,
                    "QUICK LINK CREATE MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);

            throw IErrors.MERCHANT_PROFILE_EXISTED;
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }
    public static JsonObject getAllLinkDetail(String linkId) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/quicklink-backend/quicklink/all-link/" + linkId;
        LOGGER.info(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Detail Payment Link Status Response:" + resStatus);
            LOGGER.info(() -> "Detail Payment Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }
    public static JsonObject getQuicklinkDetail(String linkId, String statusLink) {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = new String();
        if (statusLink.equals("payment-link")){
            requestUri = "/quicklink-backend/quicklink/payment-link-detail/" + linkId;
        } else {
            requestUri = "/quicklink-backend/quicklink/static-link-detail/" + linkId;
        }
        
        // LOGGER.info(() -> "requestUri:" + requestUri);
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri,
                    signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Detail Payment Link Status Response:" + resStatus);
            LOGGER.info(() -> "Detail Payment Link Body Resopnse:" + resContent);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR]", e);
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }
    public static Map<String, Object> request(String method, String url, Map<String, String> headers, byte[] content, int connectTimeout, int readTimeout) {
        Map<String, Object> result = null;
        try {
            LOGGER.info("call to quiclink url: " + url + ", method: " + method + ", headers: " + headers + ", content: " + (content != null ? new String(content, UTF_8) : "null"));
            HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod(method);
            if (headers != null)
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    conn.setRequestProperty(header.getKey(), header.getValue());
                }
            if (method.matches("^(POST|PUT)$")) {
                conn.setRequestProperty("Content-Length", String.valueOf(content != null ? content.length : 0));
                if (content != null && content.length > 0)
                    conn.setDoOutput(true);
            }
            conn.setUseCaches(false);
            conn.setDoInput(true);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setInstanceFollowRedirects(false);
            conn.connect();
            // Send request content
            if (method.matches("^(POST|PUT)$") && content != null && content.length > 0) {
                DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
                wr.write(content);
                wr.flush();
                wr.close();
            }
            int statusCode = conn.getResponseCode();
            result = new LinkedHashMap<>();
            result.put(HTTP_STATUS_CODE, statusCode);
            result.put(HTTP_STATUS_MESS, conn.getResponseMessage());
            // Get response header
            Map<String, List<String>> resHeaders = conn.getHeaderFields();
            result.put(HTTP_HEADERS, resHeaders);

            // Get response content
            InputStream is = statusCode < 400 ? conn.getInputStream() : conn.getErrorStream();
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] buf = new byte[1000];
            int length;
            while ((length = is.read(buf)) != -1)
                bout.write(buf, 0, length);
            is.close();
            bout.flush();
            byte[] resContent = bout.toByteArray();
            result.put(HTTP_CONTENT, resContent);
            bout.close();
            String logContent = new String(resContent, UTF_8);
            if (logContent.length() > 100)
                logContent = logContent.substring(0, 100) + "...";
            LOGGER.info("status_code: " + statusCode + ", headers: " + resHeaders + ", content: " + logContent);
            // Keepalive
            // conn.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return result;
    }
    public static JsonObject download(Map param) throws IOException {
        JsonObject jsonReturn = new JsonObject();
        String requestUri = "/quicklink-backend/quicklink/all-link";
        int expires = 6000;
        // Request uri base
        String requestMethod = "POST";
        Date requestDate = new Date();
        // Milliseconds
        int requestTimeOut = QUICKLINK_TIMEOUT;
        JsonObject jbody = new JsonObject();
        jbody.put(PAGE, param.getOrDefault(PAGE, ""));
        jbody.put(PAGESIZE, param.getOrDefault(PAGESIZE, ""));
        jbody.put(KEYWORD, param.getOrDefault(KEYWORD, ""));
        jbody.put(STATE, param.getOrDefault(STATE, ""));
        jbody.put(INSTALLMENT_MERCHANT_ID, param.getOrDefault(INSTALLMENT_MERCHANT_ID, ""));
        jbody.put(PAYNOW_MERCHANT_ID, param.getOrDefault(PAYNOW_MERCHANT_ID, ""));
        jbody.put(EMAIL, param.getOrDefault(EMAIL, ""));
        jbody.put(FROMDATE, param.getOrDefault(FROMDATE, ""));
        jbody.put(TODATE, param.getOrDefault(TODATE, ""));
        jbody.put(PARTNER, param.getOrDefault(PARTNER, ""));

        LOGGER.log(Level.INFO, "POST TO QUICKLINK-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestUri);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String quicklinkUrl = QUICKLINK_SERVICE_BASE_URL + requestUri;

        URL url = new URL(quicklinkUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // connection.setRequestMethod(requestMethod);
        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "POST");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + jbody.toString().getBytes().length);

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("quicklink service download responseCode:" + responseCode);
        LOGGER.info("quicklink service download responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);

            LOGGER.log(Level.SEVERE, "QUICK LINK CREATE MERCHANT PROFILE RESPONSE: " + jsonReturn.toString());
        } else {
            LOGGER.log(Level.SEVERE,
                    "QUICK LINK CREATE MERCHANT PROFILE CODE: " + responseCode + ", DESC: " + responseMsg);
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            jsonReturn = new JsonObject(strReturn);

            throw IErrors.MERCHANT_PROFILE_EXISTED;
        }
        jsonReturn.put("state", 200);
        return jsonReturn;
    }
    public static JsonObject getMerchantProfileById() {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/list-all-mer-profile";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Merchant Profile by profileIds Status Response:" + resStatus);
            LOGGER.info(() -> "Get Merchant Profile by profileIds Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject getMerchantIdByProfile(String merchantIds, String payMethod) {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/all-merchant-by-profile?payMethod=" + payMethod;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Merchant Id by profileIds Status Response:" + resStatus);
            LOGGER.info(() -> "Get Merchant Id by profileIds Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject getAllMerchantProfile() {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/merchant-profile?keywords=&page=0&pageSize=999999";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Merchant Profile Payment Link Status Response:" + resStatus);
            LOGGER.info(() -> "Get Merchant Profile Payment Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject getpartner() {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/partner";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("GET", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, null, 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Partner Status Response:" + resStatus);
            LOGGER.info(() -> "Get Partner Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static UploadFileDTO insertUploadFile(UploadFileDTO upload, String userId) throws Exception {
        Exception exception = null;
        List<UploadFileDTO> list = new ArrayList<>();
        UploadFileDTO ulf = new UploadFileDTO();
        Connection con = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try {
            con = getConnection114();
            cs = con.prepareCall("{ call MERCHANTPORTAL.PKG_UPLOAD.insert_upload(?,?,?,?,?,?,?)}");
            cs.setString(1, upload.getName());
            cs.setString(2, upload.getOriginalName());
            cs.setString(3, upload.getExt());
            cs.setString(4, userId);
            cs.registerOutParameter(5, OracleTypes.CURSOR);
            cs.registerOutParameter(6, OracleTypes.NUMBER);
            cs.registerOutParameter(7, OracleTypes.VARCHAR);
            cs.execute();
            rs = (ResultSet) cs.getObject(5);
            Integer nResult = cs.getInt(6);
            String sResult = cs.getString(7);

            if (nResult != 200)
                throw new Exception("DB get upload data error: " + sResult);
            while (rs != null && rs.next()) {
                return bindingData(rs, upload);
            }
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Error in get upload data", ex);
            exception = ex;
            throw exception ;
        } finally {
            closeConnectionDB(rs, null, cs, con);
        }
        return ulf;
    }

    private static UploadFileDTO bindingData(ResultSet rs, UploadFileDTO uploadData) {
        UploadFileDTO upload = new UploadFileDTO();
        try {
            upload.setId(Util.getColumnInteger(rs, "N_ID"));
            upload.setName(Util.getColumnString(rs, "S_NAME"));
            upload.setOriginalName(Util.getColumnString(rs, "S_ORIGINAL_NAME"));
            upload.setExt(Util.getColumnString(rs, "S_EXT"));
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            upload.setCreatedDate(Util.getColumnString(rs, "D_CREATE"));
            upload.setUserId(Util.getColumnString(rs, "S_CREATE"));
            JsonObject data = new JsonObject(Util.getColumnString(rs, "S_DATA"));
            upload.setData(data.isEmpty() ? new JsonObject(): data);
            upload.setType(uploadData.getType());
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR BINDING UPLOAD DATA] ", e);
        }
        return upload;
    }
    
    public static JsonObject postPaymentLink(JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/payment-link";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("POST", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            } else if (resStatus == 409) {
                throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
            }
            LOGGER.fine(() -> "Create Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Create Payment Link Resopnse:" + resContent);

        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    public static JsonObject putPaymentLinkDetail(String id, JsonObject body) {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/payment-link/" + id ;
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("PUT", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            } else if (resStatus == 409) {
                throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
            }
            LOGGER.fine(() -> "Update Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Update Payment Link Resopnse:" + resContent);

        } catch (ErrorException e) {
            throw new ErrorException(409, "Duplicate", "Link name existed", "", "");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
    
    public static JsonObject postMerchantProfileById(String id) {
        JsonObject jsonReturn = null;
        String requestUri = "/quicklink-backend/quicklink/list-merchant-profile-by-partner";
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("POST", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, id.getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.info(() -> "Get Merchant Profile by profileIds Status Response:" + resStatus);
            LOGGER.info(() -> "Get Merchant Profile by profileIds Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }

    public static JsonObject putPaymentState(String linkId, JsonObject body, String paymentState) {
        JsonObject jsonReturn = null;
        // String requestUri = "/payment-link-state/" + linkId;
        String requestUri = new String();
        if (paymentState.equals("payment-link")){
            requestUri = "/quicklink-backend/quicklink/payment-link-state/" + linkId;
        }else { //static link
            requestUri = "/quicklink-backend/quicklink/static-link-state/" + linkId;
        }
        try {
            Map<String, String> signedHeaders = new HashMap<>();
            signedHeaders.put("Accept", "application/json");
            signedHeaders.put("Content-Type", "application/json");

            Map<String, Object> resMap = request("PUT", QUICKLINK_SERVICE_BASE_URL + requestUri, signedHeaders, body.toString().getBytes(UTF_8), 60000, 60000);

            int resStatus = Integer.parseInt(resMap.get(HTTP_STATUS_CODE) + "");

            String resContent = new String((byte[]) resMap.get(HTTP_CONTENT), UTF_8);
            if (resStatus == 200) {
                jsonReturn = new JsonObject(resContent);
            }
            LOGGER.fine(() -> "Change State Payment Link Status Response:" + resStatus);
            LOGGER.fine(() -> "Change State Payment Link Resopnse:" + resContent);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, e.getMessage(), e);
        }
        return jsonReturn;
    }
}
