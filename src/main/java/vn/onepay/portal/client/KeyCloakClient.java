package vn.onepay.portal.client;

import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;
import vn.onepay.portal.resources.Db;
import vn.onepay.portal.resources.base.dto.ErrorException;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.logging.Level;
import java.util.logging.Logger;
import com.onepay.commons.util.Convert;

public class KeyCloakClient extends Db {
    private static final Logger LOGGER = Logger.getLogger(KeyCloakClient.class.getName());
    private static String KEY_CLOAK_LOG_OUT_URL = Config.getString("one_am.uri_logout", "");
    private static String KEY_CLOAK_TOKEN_URL = Config.getString("one_am.token_uri", "");
    public static JsonObject logOutKeyCloak(String content) throws Exception {
        JsonObject jsonReturn = null;
        String userOneAm = Config.getString("one_am.user", "") + ":" + Config.getString("one_am.password", "");
        String headerAuthorization = "Basic " + Convert.toBase64String(userOneAm.getBytes()).replaceAll("[\r\n]", "");
        try {
            URL url = new URL(KEY_CLOAK_LOG_OUT_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Content-Length", String.valueOf(content.length()));
            connection.setRequestProperty("Authorization", headerAuthorization);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setInstanceFollowRedirects(false);
            connection.connect();
            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.writeBytes(content);
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            logger.info("responseCode keycloack :" + responseCode);
            logger.info("responseMsg keycloack :" + responseMsg);
            
            if (responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {
                jsonReturn = new JsonObject();
                String location = connection.getHeaderField("Location");
                jsonReturn.put("return_url", location);
            } else {
                LOGGER.log(Level.SEVERE, "KEY_CLOAK POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "",
                            jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "",
                        "Resend the request at another time.");
            }
            connection.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR KEYCLOAK ]", e);
            throw e;
        }
        return jsonReturn;
    }
    public static JsonObject getToken(String content) throws Exception {
        JsonObject jsonReturn = null;
        String userOneAm = Config.getString("one_am.user", "") + ":" + Config.getString("one_am.password", "");
        String headerAuthorization = "Basic " + Convert.toBase64String(userOneAm.getBytes()).replaceAll("[\r\n]", "");
        try {
            URL url = new URL(KEY_CLOAK_TOKEN_URL);
            LOGGER.log(Level.SEVERE, "KEY_CLOAK_TOKEN_URL KEY CLOAK: " + KEY_CLOAK_TOKEN_URL);
            LOGGER.log(Level.SEVERE, "ONE AM KEY CLOAK: " + userOneAm);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Content-Length", String.valueOf(content.length()));
            connection.setRequestProperty("Authorization", headerAuthorization);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setInstanceFollowRedirects(false);
            connection.connect();
            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.writeBytes(content);
            wr.flush();
            wr.close();
            int responseCode = connection.getResponseCode();
            String responseMsg = connection.getResponseMessage();
            logger.info("responseCode keycloak: " + responseCode);
            logger.info("responseMsg keycloak : " + responseMsg);
            
            if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                //Get Response
                InputStream is = connection.getInputStream();
                BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = rd.readLine()) != null) {
                    response.append(line);
                    response.append('\r');
                }
                rd.close();
                is.close();
                String strReturn = response.toString();
                jsonReturn = new JsonObject(strReturn);
                logger.log(Level.INFO, "TOKEN KEYCLOAK RESPONSE: " + jsonReturn.toString());
            } else {
                LOGGER.log(Level.SEVERE, "KEY_CLOAK POST [ERROR] CODE: " + responseCode + ", DESC: " + responseMsg);
                if (responseCode == HttpURLConnection.HTTP_INTERNAL_ERROR) {
                    InputStream is = connection.getErrorStream();
                    BufferedReader rd = new BufferedReader(new InputStreamReader(is));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = rd.readLine()) != null) {
                        response.append(line);
                        response.append('\r');
                    }
                    rd.close();
                    is.close();
                    String strReturn = response.toString();
                    jsonReturn = new JsonObject(strReturn);
                    throw new ErrorException(500, "INTERNAL_SERVER_ERROR", jsonReturn.getString("message"), "",
                            jsonReturn.getString("message"));
                }
                throw new ErrorException(500, "INTERNAL_SERVER_ERROR", "An internal service error has occurred", "",
                        "Resend the request at another time.");
            }
            connection.disconnect();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "[ERROR KEYCLOAK]", e);
            throw e;
        }
        return jsonReturn;
    }
}