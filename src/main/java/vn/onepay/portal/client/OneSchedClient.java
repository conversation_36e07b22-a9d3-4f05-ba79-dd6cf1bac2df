package vn.onepay.portal.client;

import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpClient;
import io.vertx.core.json.JsonObject;
import vn.onepay.portal.Config;

import vn.onepay.portal.resources.Db;
import vn.onepay.portal.utils.HttpRequestTask;
import vn.onepay.portal.Util;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import static java.nio.charset.StandardCharsets.UTF_8;

public class OneSchedClient extends Db {

    private static final Logger LOGGER = Logger.getLogger(OneSchedClient.class.getName());
    private static String ONESCHED_SERVICE_BASE_URL = Config.getString("onesched-service.url", "");
    private static Integer ONESCHED_SERVICE_TIMEOUT = Config.getInteger("onesched-service.timeout", 60000);
    private static Integer ONESCHED_SERVICE_TIMEOUT_LONG = Config.getInteger("onesched-service.timeout_long", 100000);
    private static DateFormat yyyyMMddTHHmmssZ = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    static {
        yyyyMMddTHHmmssZ.setTimeZone(TimeZone.getTimeZone("UTC"));
    }


    // public static JsonObject syncBIDVPayCollectTransaction(String xRequestId, String userId) throws
    // Exception {
    // JsonObject jsonReturn = new JsonObject();
    // if(ONESCHED_SERVICE_ID == null || ONESCHED_SERVICE_ID.equals("0")){
    // jsonReturn.put("mess", "paycollect_auto_add_missing_id is not exist");
    // jsonReturn.put("status", "BAD");
    // return jsonReturn;
    // }
    // Random random = new Random();
    // String reference = "exe_" + Math.abs(random.nextInt());
    // String requestURI = ONESCHED_SERVICE_BASE_URL + "/scheduler/api/v1/jobs/" + ONESCHED_SERVICE_ID +
    // "/executes/" + reference;
    // String requestMethod = "PUT";
    // JsonObject request = new JsonObject();
    // request.put("time_skip", ONESCHED_SERVICE_TIME_SKIP);
    // Date requestDate = new Date();
    // int requestTimeOut = ONESCHED_SERVICE_TIMEOUT;
    // try {
    // logger.info("==============START CALL API ONESCHED-SERVICE=================");
    // logger.info(requestURI);
    // URL url = new URL(requestURI);
    // HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    // Util.setRequestMethod(connection, requestMethod);
    // connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
    // connection.setRequestProperty("Accept", "application/json");
    // connection.setRequestProperty("Content-Type", "application/json");
    // connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
    // connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
    // connection.setRequestProperty("X-Request-Id", xRequestId);
    // connection.setRequestProperty("X-Real-IP", "127.0.0.1");
    // connection.setRequestProperty(X_USER_ID, userId);
    // connection.addRequestProperty("Content-Length", "" +
    // Integer.toString(request.encode().getBytes().length));
    // connection.setUseCaches(false);
    // connection.setDoInput(true);
    // connection.setDoOutput(true);
    // DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
    // wr.write(request.encode().getBytes(UTF_8));
    // wr.flush();
    // wr.close();
    // int responseCode = connection.getResponseCode();
    // logger.fine("response_code=" + responseCode);
    // if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK)
    // {

    // java.io.InputStream is = connection.getInputStream();
    // BufferedReader rd = new BufferedReader(new InputStreamReader(is));
    // String line;
    // StringBuilder response = new StringBuilder();
    // while ((line = rd.readLine()) != null) {
    // response.append(line);
    // response.append('\r');
    // }
    // rd.close();
    // is.close();
    // jsonReturn = new JsonObject(response.toString());
    // logger.log(Level.INFO, "ONESCHED-SERVICE RESPONSE: " + jsonReturn.toString());
    // }
    // } catch (Exception e) {
    // logger.log(Level.SEVERE, "Error API ONESCHED-SERVICE: " + e);
    // }
    // return jsonReturn;
    // }
    public static JsonObject synchronizeLong(String jobId, String timeSkip) throws Exception {
        return synchronize(jobId, timeSkip, ONESCHED_SERVICE_TIMEOUT_LONG);
    }

    public static JsonObject synchronize(String jobId, String timeSkip) throws Exception {
        return synchronize(jobId, timeSkip, ONESCHED_SERVICE_TIMEOUT);
    }

    public static JsonObject synchronize2(String jobId, String timeSkip) throws Exception {
        return synchronize2(jobId, timeSkip, ONESCHED_SERVICE_TIMEOUT);
    }


    public static void synchronizeBatchAWait(List<String> jobIds, String timeSkip) throws Exception {

        ArrayList<CompletableFuture<JsonObject>> tasks = new ArrayList<>();
        // Future<List<JsonObject>> result = pool.sub(new HttpRequestTask("", timeSkip));
        for (String jobId : jobIds) {
            // tasks.add(new HttpRequestTask(url, timeSkip));
            tasks.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return synchronize(jobId, timeSkip);
                } catch (Exception e) {
                    LOGGER.log(Level.SEVERE, "Error on sycn batch 1", e);
                }
                return null;
            }));
        }

        CompletableFuture<Void> results = CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0]));
        try {
            results.get(); // Wait for all futures to complete
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error on sycn batch 2", e);

        }


    }

    public static JsonObject synchronize2(String jobId, String timeSkip, int requestTimeOut) throws Exception {
        int expires = 300;
        // Request uri base
        String requestReference = jobId + new Date().getTime();
        String requestURI = "/scheduler/api/v2/jobs/" + jobId + "/executes/exe" + requestReference;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        JsonObject jbody = new JsonObject();
        jbody.put("time_skip", timeSkip);
        LOGGER.log(Level.INFO, "PUT TO SCHEDULER-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String schedulerUrl = ONESCHED_SERVICE_BASE_URL + requestURI;

        URL url = new URL(schedulerUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));
        connection.setConnectTimeout(requestTimeOut);
        connection.setReadTimeout(requestTimeOut);

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            LOGGER.log(Level.SEVERE, "SCHEDULER SYNCHRONIZE: " + strReturn);
            return new JsonObject(strReturn);

        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            return new JsonObject();
        }
    }
    public static JsonObject synchronize(String jobId, String timeSkip, int requestTimeOut) throws Exception {
        int expires = 300;
        // Request uri base
        String requestReference = jobId + new Date().getTime();
        String requestURI = "/scheduler/api/v1/jobs/" + jobId + "/executes/exe" + requestReference;
        String requestMethod = "PUT";
        Date requestDate = new Date();
        // Milliseconds
        JsonObject jbody = new JsonObject();
        jbody.put("time_skip", timeSkip);
        LOGGER.log(Level.INFO, "PUT TO SCHEDULER-SERVICE");
        LOGGER.log(Level.INFO, "url: " + requestURI);
        LOGGER.log(Level.INFO, " info: " + jbody.encode());
        String schedulerUrl = ONESCHED_SERVICE_BASE_URL + requestURI;

        URL url = new URL(schedulerUrl);

        Date dateTime = new Date();
        Map<String, Object> signedHeaders = new HashMap();
        signedHeaders.put("X-OP-Date", yyyyMMddTHHmmssZ.format(dateTime));
        signedHeaders.put("X-OP-Expires", expires + "");
        LOGGER.log(Level.INFO, "BODY bdJson:" + jbody.encode());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        Util.setRequestMethod(connection, requestMethod);
        connection.setRequestProperty("X-HTTP-Method-Override", "PUT");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-OP-Date", yyyyMMddTHHmmssZ.format(requestDate));
        connection.setRequestProperty("X-OP-Expires", String.valueOf(requestTimeOut));
        connection.addRequestProperty("Content-Length", "" + Integer.toString(jbody.toString().getBytes().length));
        connection.setConnectTimeout(requestTimeOut);
        connection.setReadTimeout(requestTimeOut);

        connection.setUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);

        DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
        wr.write(jbody.encode().getBytes(UTF_8));
        wr.flush();
        wr.close();
        int responseCode = connection.getResponseCode();
        String responseMsg = connection.getResponseMessage();
        LOGGER.info("responseCode:" + responseCode);
        LOGGER.info("responseMsg:" + responseMsg);
        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
            // Get Response
            InputStream is = connection.getInputStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            String strReturn = response.toString();
            LOGGER.log(Level.SEVERE, "SCHEDULER SYNCHRONIZE: " + strReturn);
            return new JsonObject(strReturn);

        } else {
            InputStream is = connection.getErrorStream();
            BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\r');
            }
            rd.close();
            is.close();
            return new JsonObject();
        }
    }

    public static JsonObject synchronizeTransAccount18FullSync() throws Exception {
        String jobId = Config.getString("onesched-service.trans_account_refund_18_synx_id", "");
        String timeSkip = "0s";
        return synchronize2(jobId, timeSkip);
    }

}
